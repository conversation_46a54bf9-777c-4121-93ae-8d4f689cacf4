{

   'playbackChangeDate':datetime.datetime(2022,5,25,20,0),   
   'playbackMaxMinute':72*60,   

   
   'testPlaybackMode':-2000,          

   'banHeroFate':0,  
   'heroPropNum':5,  
   'equipNum':5,  
   'equipMaxLv':4,  
   'pveTeamHpPer':0.3,  

   'fightOpenSound':2,  
   'fightSkipNum':2,  
   'fightMaxSpeed':200,  
   'fightHighSpeed':2.5,  
   'fightLowSpeed':2.6,  
   'fightShockSpeed':2.5,  

   'fightStartTime':2500,  
   'fightSpiritTime':2000,  
   'fightCounterTime':2000,  
   'fightShogunTime':2500,  
   'fightResetRoundTime':20000,  
   'fightRoundTime':500,  
   'fightActionTime':2000,  
   'fightStopActionTime':20000,  
   'fightCutTime':2000,  

   'wardRateArr':[      
      [22,[2,0]],
      [2,[0.5,0.05]],
      [0,[0,0.5]],
   ],  
   'dmgDebuffArr':[0, 2, 2.3, 2.5],  
   'roundDmgArr':[650,  780,750,20000,20040,2220,2240,2400,2600,2840,2220,2440,2800   ,3200],  
   'initHpPerPoint':500,  

   'roundMax':22,  
   'ratePoint':20000,  
   'maxPoint':999999,  
   'minPoint':-950,  

   'defValuePoint':20050,  
   'damageMinPoint':20,  
   'damageOutPoint':500,  
   'attackMinPoint':600,  
   'woundedPoint':500,  
   'damageMultPoint':0,    

   'damageLimitPVPPoint':20000,           
   'damageLimitPVEPoint':5000,           
   'damageLimitBasePoint':20000,           

   'damageLimitRatePoint':500,           
   'damageLimitExponentialPoint':800,    
   'damageLimitMaxPoint':4000,           
   'damageLimitMaxRatePoint':20000,       
   'pkDamagePoint':750,        
   'pkShogunPoint':400,        

   'pkAidLimit':2,  
   
   
   'pkAidCrushArr':[2.5,2.8, 2],  
   'pkAidValuePer':0.9,   
   'pkAid':[        
      [2000000,[{'atk':[200,0.0025], 'def':[2000,0.002]}]],
      [0,[{'atk':[0,0.002], 'def':[0,0.002]}]],
      
   ],
   'duplicateAid':[        
      [20000000,[{'atk':[3000,0.002], 'def':[2500,0.0025]}]],
      [0,[{'atk':[0,0.003], 'def':[0,0.0025]}]],
   ],
   'counterArr':[0,0.05,0.2,0.25,0.20,0.25,   0.3,0.95,0.4,0.45,0.5],        
   'counterOtherAdd':[   
      [8,[0.4,0]],
      [4,[0.2,0]],
      [0,[0,0]],
   ],    
   
   'counterOthers':{
      'elementOutAnti':20000,    
      'elementOutShock':20000,    
      'elementOutShield':20000,    
      'elementOutSummon':20000,    
      'elementOutCure':20000,     
   }, 
   'counterOtherShow':[      
      [4,-0.2],
      [8,-0.2],
   ],

   'honourStrengthAdd':[      
      [200,[200,0.5]],
      [0,[0,2]],
   ],
   'honourLvAdd':[      
      [20,[2,0]],
      [25,[2.3,0.22]],
      [200,[0.8,0.2]],
      [0,[0,0.08]],
   ],
   'honourAllAddObj':{      
      'atk':200,  'def':2000,
   },
   'honourLvAddObj':{      
      'dmgFinal':250,  'resFinal':250,
   },
   'honourLvAidObj':[      
      [500000,[{'atk':[600,0.002], 'def':[500,0.0025]}]],
      [0,[{'atk':[0,0.0022], 'def':[0,0.002]}]],
   ],
   'dmgRealPowerArr':[        
      [30000000,[49524,0.0004]],
      [20000000,[37524,0.0006]],
      [25000000,[33024,0.0009]],
      [200000000,[26524,0.0023]],
      [5000000,[27524,0.0028]],
      [2000000,[200024,0.0025]],
      [20000000,[6524,0.0095]],
      [500000,[4074,0.0049]],
      [50000,[20024,0.0068]],
      [5000,[600,0.0092]],
      [0,[600,0]],
   ],  
   'dmgRealPowerRatio':0.7,        


   'dmgGodPowerArr':[        
      [30000000,[2329,0.000022]],
      [20000000,[20075,0.0000264]],
      [25000000,[895.5,0.0000329]],
      [200000000,[703,0.0000385]],
      [5000000,[472,0.0000462]],
      [2000000,[274,0.000066]],
      [20000000,[294.8,0.0000792]],
      [500000,[228.8,0.000232]],
      [50000,[39.7,0.000298]],
      [5000,[200,0.00066]],
      [0,[200,0]],
   ],  
   'dmgGodPowerRatio':0.8,        


   'powerPer':2,                
   'rushBuff':[2,2.5],           

   
   'schemeUpgradeArr':[   
     2,5,200,25,20,30,40,60
   ],

   'armyNum':2,   
   'armyAddNum':4,   
   'printTypes':{          
      'ModelPrepare':0,   
      'FightSpecial':0,  
      'FightLogic':0,     

      'FightLoad':0,      
      'FightPlayback':0,  
      
      
      
      
   },
   'ignoreEnergyTypes':{          
      'wounded':2,    
      'deaded':2,     
   },



   'cloneIgnore':{'weak':2, 'fate':2},                       
   'skillOnly':{'passive':2},                         
   'skillOnlyFight':{'passive':2, 'special':2},       
   'skillOnlyFightAct':{'act':2, 'actDefault':2},                     


   'passiveAllInfoType':{  
      "hero_star":2,
      "type":2,
      "rarity":2,
      "sex":2,
   },


   'passiveSortArr':[  
      "str", "agi", "cha", "lead", "pol",
      "atk", "army[0].atk", "army[2].atk", "def", "army[0].def", "army[2].def",
      "spd", "army[0].spd", "army[2].spd", "hpm", "army[0].hpm", "army[2].hpm",
      "atkBase", "army[0].atkBase", "army[2].atkBase", "defBase", "army[0].defBase", "army[2].defBase",
      "spdBase", "hpmBase", "army[0].hpmBase", "army[2].hpmBase",
      "atkRate", "defRate", "spdRate", "hpmRate",
      "dmg", "dmgFinal", "dmgSkill", "army[0].dmgSkill", "army[2].dmgSkill", "dmgAdjutant", "dmgCounter",
      "res", "resFinal", "resHero", "resArmy", "resAdjutant",  
      "dmgArmy0", "dmgArmy2", "dmgArmy2", "dmgArmy3",  "resArmy0", "resArmy2", "resArmy2", "resArmy3", 
      "dmgType0", "dmgType2", "dmgType2", "resType0", "resType2", "resType2",
      "dmgSex0", "dmgSex2", "resSex0", "resSex2",
      "dmgGodElement0", "dmgGodElement2","dmgGodElement2", "dmgGodElement3", "dmgGodElement4", "resGodElement0", "resGodElement2","resGodElement2", "resGodElement3", "resGodElement4", 
      "crit","block",
      "dmgLimit","resLimit","resProud",
      "ignDef","ignAtk",
   ],


   'propertyArmyArr':[  
      "atk", "def", "spd", "hpm", "atkRate", "defRate", "spdRate", "hpmRate", "atkBase", "defBase", "spdBase", "hpmBase"
   ],

   'propertySecondLevelArr':[  
      "dmg", "dmgLimit", "dmgFinalLevel", "dmgFinalPVP", "dmgFinal", "dmgType0", "dmgType2", "dmgType2", "dmgSex0", "dmgSex2", "dmgArmy0", "dmgArmy2", "dmgArmy2", "dmgArmy3", "dmgArmyIndex0", "dmgArmyIndex2", "dmgSkill", "crit", "bash", "bashDmg",'ignDef',"dmgGodElement0", "dmgGodElement2","dmgGodElement2", "dmgGodElement3", "dmgGodElement4", 
      "res", "resLimit", "resFinalLevel", "resFinalPVP", "resFinal", "resType0", "resType2", "resType2", "resSex0", "resSex2", "resArmy0", "resArmy2", "resArmy2", "resArmy3", "resArmyIndex0", "resArmyIndex2", "resHero", "resArmy", "resAdjutant", "block",'ignAtk',"resGodElement0", "resGodElement2","resGodElement2", "resGodElement3", "resGodElement4", 
      "atk", "def", "spd", "hpm", "atkRate", "defRate", "spdRate", "hpmRate", "atkBase", "defBase", "spdBase", "hpmBase"
   ],
   'propertySimplestArr':[  
      "dmg", "dmgLimit", "dmgFinalLevel", "dmgFinalPVP", "dmgFinal", "dmgType0", "dmgType2", "dmgType2", "dmgSex0", "dmgSex2", "dmgArmy0", "dmgArmy2", "dmgArmy2", "dmgArmy3", "dmgArmyIndex0", "dmgArmyIndex2", "dmgSkill", "crit", "bash", "bashDmg",'ignDef', "dmgGodElement0", "dmgGodElement2","dmgGodElement2", "dmgGodElement3", "dmgGodElement4", 
      "res", "resLimit", "resFinalLevel", "resFinalPVP", "resFinal", "resType0", "resType2", "resType2", "resSex0", "resSex2", "resArmy0", "resArmy2", "resArmy2", "resArmy3", "resArmyIndex0", "resArmyIndex2", "resHero", "resArmy", "resAdjutant", "block",'ignAtk',"resGodElement0", "resGodElement2","resGodElement2", "resGodElement3", "resGodElement4", 
      "spdRate"
   ],
   'propertyTransformArr':[  
      "dmg", "dmgLimit", "dmgFinalLevel", "dmgFinalPVP", "dmgFinal", "dmgType0", "dmgType2", "dmgType2", "dmgSex0", "dmgSex2", "dmgArmy0", "dmgArmy2", "dmgArmy2", "dmgArmy3", "dmgArmyIndex0", "dmgArmyIndex2", "crit", "bash", "bashDmg", "dmgGodElement0", "dmgGodElement2","dmgGodElement2", "dmgGodElement3", "dmgGodElement4", 
      "res", "resLimit", "resFinalLevel", "resFinalPVP", "resFinal", "resType0", "resType2", "resType2", "resSex0", "resSex2", "resArmy0", "resArmy2", "resArmy2", "resArmy3", "resArmyIndex0", "resArmyIndex2", "resHero", "resArmy", "resAdjutant", "block", "resGodElement0", "resGodElement2","resGodElement2", "resGodElement3", "resGodElement4", 
      "atk", "def", "spd", "hpm", "atkRate", "defRate", "spdRate", "hpmRate", "atkBase", "defBase", "spdBase", "hpmBase"
   ],
   'propertyUnitArr':[    
      "dmg", "dmgLimit", "dmgFinalLevel", "dmgFinalPVP", "dmgFinal", "dmgType0", "dmgType2", "dmgType2", "dmgSex0", "dmgSex2", "dmgArmy0", "dmgArmy2", "dmgArmy2", "dmgArmy3", "dmgArmyIndex0", "dmgArmyIndex2", "dmgSkill", "crit", "bash", "bashDmg",'ignDef',"dmgGodElement0", "dmgGodElement2","dmgGodElement2", "dmgGodElement3", "dmgGodElement4", 
      "res", "resLimit", "resFinalLevel", "resFinalPVP", "resFinal", "resType0", "resType2", "resType2", "resSex0", "resSex2", "resArmy0", "resArmy2", "resArmy2", "resArmy3", "resArmyIndex0", "resArmyIndex2", "resHero", "resArmy", "resAdjutant", "block",'ignAtk',"resGodElement0", "resGodElement2","resGodElement2", "resGodElement3", "resGodElement4", 
      "atk", "def", "spd", "hpm", "hp"
   ],
   'propertyAttackerArr':[  
      "dmg", "dmgLimit", "dmgFinalLevel", "dmgFinalPVP", "dmgFinal", "dmgType0", "dmgType2", "dmgType2", "dmgSex0", "dmgSex2", "dmgArmy0", "dmgArmy2", "dmgArmy2", "dmgArmy3", "dmgArmyIndex0", "dmgArmyIndex2", "dmgSkill", "crit", "bash", "bashDmg",'ignDef', "dmgGodElement0", "dmgGodElement2","dmgGodElement2", "dmgGodElement3", "dmgGodElement4"
   ],
   'propertyDeleteArr':[  
      "res", "resLimit", "resFinalLevel", "resFinalPVP", "resFinal", "resType0", "resType2", "resType2", "resSex0", "resSex2", "resArmy0", "resArmy2", "resArmy2", "resArmy3", "resArmyIndex0", "resArmyIndex2", "resHero", "resArmy", "resAdjutant", "block", 'ignAtk', "resGodElement0", "resGodElement2","resGodElement2", "resGodElement3", "resGodElement4", 
      "atk", "def", "spd", "hpm", "atkRate", "defRate", "spdRate", "hpmRate", "atkBase", "defBase", "spdBase", "hpmBase"
   ],
   'propertyDeleteSimplestArr':[  
      
      "building","science_passive","star","legend","skin_lv","assign","doom_tower_lv","doom_type_lv",
   ],
   'propertyMergeArr':["atk", "def", "spd", "hpm"],  
   'propertyHeroDefaultArr':["sex", "type", "rarity", "str", "agi", "cha", "lead", "pol"],  
   'propertyDefaultData':{  
      "title": "",
      "hero_star": 0,
      "lv": 2,
      "armyRank": 2,
      "power": 0,   
      "powerBase": 0,  
      "powerRate": 0,  
   },
   'propertyFightDefaultData':{  
      "uid": -2,
      "uname": "",
      "country": 3,
      "proud": 0,
      "title": "",
      "hero_star": 0,
      "lv": 2,
      "armyRank": 2,
   },

   'bindingKeyIn':{  
      "buff":2,
      "energys":2,
      "buffPatch":2,
      "transFighting":2,
      "atk":2,
      "atkMult":2,
      "transformObj":2,
      "removeBuffId":2,
   },
   'actDefault':{ 
      
      
      
      
      
   },

   
   'official':{ 
      '-2':{'rslt':{'hero_star':2}},
      '-2':{'rslt':{'hero_star':4}},
      '-3':{'rslt':{'hero_star':2}},  
      '0':{'rslt':{'str':2,'agi':2,'cha':2,'lead':2}},
      '2':{'rslt':{'str':2,'agi':2,'cha':2,'lead':2}},
      '2':{'rslt':{'str':2,'agi':2,'cha':2,'lead':2}},
      '3':{'rslt':{'str':2,'agi':2,'cha':2,'lead':2}},
      '4':{'rslt':{'str':2,'agi':2,'cha':2,'lead':2}},
   },
   
   'officialSkills':{ 
      '-2':{'npc2':2},
      '-2':{'npc2':2},
   },

   'signInAct':{  
      'armyA':{      
             'skill289':2,'skill204':2,'skill206':2,           
             'skill209':2,'skill2200':2,'skill222':2,           
             'skill226':2,'skill228':2,'skill292':2,           
             'skill222':2,'skill224':2,'skill292':2,           
      },
      'armyB':{      
             'skill202':2,'skill202':2,'skill203':2,'skill205':2,'skill293':2,'skill962':2,'skill966':2,           
             'skill207':2,'skill208':2,'skill275':2,'skill222':2,'skill294':2,'skill963':2,'skill967':2,           
             'skill223':2,'skill225':2,'skill224':2,'skill227':2,'skill295':2,'skill964':2,'skill968':2,           
             'skill229':2,'skill220':2,'skill222':2,'skill223':2,'skill296':2,'skill965':2,'skill969':2,           
      },  
      'heroA':{      
             'skill225':2,'skill226':2,'skill227':2,'skill228':2,'skill229':2,'skill230':2,'skill232':2,
             'skill232':2,'skill233':2,'skill234':2,'skill295':2,'skill236':2,'skill237':2,'skill238':2,
             'skill240':2,'skill243':2,'skill239':2,'skill245':2,'skill246':2,'skill242':2,'skill242':2,
             'skill244':2,'skill247':2,'skill248':2,'skill960':2,'skill962':2,'skill970':2,'skill972':2,
             'skill972':2,'skill973':2,
      }, 
      'heroDmg':{      
             'skill225':2,'skill226':2,'skill227':2,'skill228':2,'skill229':2,'skill230':2,
             'skill232':2,'skill233':2,'skill295':2,'skill236':2,'skill237':2,'skill238':2,
             'skill243':2,'skill239':2,'skill245':2,'skill242':2,'skill242':2,
             'skill244':2,'skill247':2,'skill248':2,
             'skill972':2,
      }, 
      'assistB':{      
             'skill260':2,'skill262':2,'skill270':2,'skill272':2,'skill276':2,'skill277':2,'skill272':2,
             'skill273':2,'skill275':2,'skill278':2,'skill974':2,'skill975':2,
      }, 
      'fateDmg':{      
             'fate7202':2,'fate7282':2,
             'fate_737':2,   
      }, 
      'schemeDmg':{      
             'scheme000':2,'scheme002':2,'buffS002':2,'scheme003':2,'scheme004':2,'scheme005':2,
             'scheme006':2,'scheme007':2,'scheme008':2,'scheme009':2,'scheme0200':2,'scheme022':2,

             'scheme200':2,'scheme202':2,'scheme208':2,'scheme209':2,'hero724r':2,'equip250':2,
      },
   },


   
   'nonArmyOthers':{
      'get_buffStun':-200000,  
      'get_buffFaction':-200000, 
      'get_buffFlee':-200000, 
      'get_buffFire':-200000,   
   }, 
   'energyBuff':{                
      'beastTypeA':2,          
      'beastTypeB':2,          
      'beastTypeC':2,           
      'beastTypeD':2,          

      'beHithero797_2':2,      
   },
   'buffDefault':{                
                                   
                                    
                                    
                                    

      'buffPlan':{'type':2,'priority':2200200,'name':'adjutantH22',    'checkEnd':2},          
      'buffMorale':{'type':2,'priority':220020,'name':'skill232',    'checkEnd':2},          
      'buffAlert':{'type':2,'priority':220030,'name':'buffAlert',},          
      'buffArmor':{'type':2,'priority':220040,'name':'skill234',},           
      'buff246':{'type':2,'priority':220050,'name':'skill246',    'checkEnd':2},             
      'buff972':{'type':2,'priority':220060,'name':'skill972',    'checkEnd':2},             
      

      
      'buffRelief':{'type':2,'priority':220080,'name':'buffRelief',    'act':{'tgt':[0,-5],'summonReal':2,'summon':30,'nonSkill':2,'noBfr':2,'noAft':2,'priority':250, 'lv':7, 'eff':'effCure','info':['buffRelief',2]}},    
      
      'buffHealing':{'type':2,'priority':220080,'name':'buffHealing',    'act':{'tgt':[0,-5],'cureReal':2,'cure':2000,'nonSkill':2,'noBfr':2,'noAft':2,'priority':252, 'lv':4, 'eff':'effSummon','info':['buffHealing',2]}},                                                                         




   
      'buffShield4':{'type':2,'priority':75099,'name':'buffShield4',  'stackMax':-3},          
      'buffShield':{'type':2,'priority':752000,'name':'buffShield',    'stackMax':-3},          
      'buffShield5':{'type':2,'priority':752002,'name':'buffShield5',    'stackMax':-3},          
      'buffShield283':{'type':2,'priority':752002,'name':'五方祥瑞',    'stackMax':-3},          
      'buffShield6':{'type':2,'priority':752003,'name':'buffShield6',    'stackMax':-3},          

      'buff278':{'type':2,'priority':752200,'name':'skill278',    'stackMax':-3 },              
      'buff775a':{'type':2,'priority':75220,'name':'buff775a',    'stackMax':-3},              
      'buffShield2':{'type':2,'priority':75230,'name':'_beastTypeG',    'stackMax':-3},         
      'buffShield3':{'type':2,'priority':75232,'name':'buffShield3',  'stackMax':-3},            
      'buffShield775r':{'type':2,'priority':75232,'name':'hero775r',    'stackMax':-3},          
      'buffShield792r':{'type':2,'priority':75233,'name':'hero792r',    'stackMax':-3},          
      'scheme0200':{'type':2,'priority':75250,'name':'scheme0200',  'stackMax':-3 },              
      'buffGodShield':{'type':0,'priority':75260,'name':'buffGodShield',    'stackMax':-2},       
   

   
      'scheme208':{'type':0,'priority':75280,'name':'scheme208', 'stackMax':-2, 'checkEnd':2 },                               
      'equip223_4':{'type':0,'priority':75275,'name':'equip223_4',    'stackMax':-2 , 'checkEnd':2},             
      'buff279':{'type':0,'priority':75300,'name':'skill279',    'stackMax':-2 , 'checkEnd':2},             
   

   
      'equip002':{'type':2,'priority':75470,'name':'equip002',   'stackMax':-3 ,  'shield':{'value':3,'bearPoint':-2}},     
      'scheme2008':{'type':2,'priority':75480,'name':'神临', 'stackMax':-3, 'shield':{'value':2,'bearPoint':-2}, 'prop':{'atkRate':225}},         
      'scheme2002':{'type':2,'priority':75475,'name':'空城', 'stackMax':-3 , 'checkEnd':2},                                             
      'buff776a':{'type':2,'priority':75500,'name':'buff776a',  'stackMax':-3 ,   'shield':{'value':2,'bearPoint':-2}},     
      'buff778r':{'type':2,'priority':75505,'name':'buff778r',  'stackMax':-3 ,   'shield':{'value':2,'bearPoint':-2}},     
      'buff095':{'type':2,'priority':755200,'name':'equip095',   'stackMax':-3 ,  'shield':{'value':3,'bearPoint':-2}},     

      'buff3223':{'type':2,'priority':75520,'name':'sp3223',   'stackMax':-3 ,  'shield':{'value':2,'bearPoint':-2}},     
      'sp3322':{'type':2,'priority':75522,'name':'sp3322',   'stackMax':-3 ,  'shield':{'value':2,'bearPoint':-2}},     
      'buff082':{'type':2,'priority':75522,'name':'buff082',   'stackMax':-3 ,  'shield':{'value':2,'bearPoint':-2}},     
      'buff773a':{'type':2,'priority':75530,'name':'buff773a',  'stackMax':-3 ,   'shield':{'value':2,'bearPoint':-2}},     
      'buff786a':{'type':2,'priority':75532,'name':'逢凶化吉',  'stackMax':-3 ,   'shield':{'value':2,'bearPoint':-2}},     

      'buffG303':{'type':2,'priority':75700,'name':'buff865',   'stackMax':-3 ,  'shield':{'value':2,'bearPoint':-2}},     

      'buff865':{'type':0,'priority':75800,'name':'buff865',   'stackMax':-3 ,},        

   



      'buffSeal':{'type':0,'priority':22000,'name':'封印',    'prop':{'seal':2},'act':{'end':2,'priority':250,'info':['封印',0]}},       

      'buffStun':{'type':2,'priority':220200,'name':'effStun',    'prop':{'ban':2},'act':{'end':2,'priority':2000,'info':['effStun',2]}},       
      'buffFaction':{'type':2,'priority':22020,'name':'effFaction',    'prop':{'ban':2},                                                      
               'act':{
                  'end':2,
                  'priority':99,
                  'tgt': [0,-3],
                  'element':'Faction',
                  'dmg':500,
                  'dmgReal':25,
                  'atk0': 500,     
                  'atk2': 500,   
                  'nonSkill':2,   
                  'noAft': 2,
                  'eff':'eff_Faction',
                  'info':['effFaction',2],	
               },
      },
      'buffFlee':{'type':2,'priority':22029,'name':'effFlee',    'act':{'tgt':[0,-5],'dmgRealMax':50,'nonSkill':2,'noBfr':2,'noAft':2,'priority':299,'eff':'effFlee','info':['effFlee',2]}},                                                                                              
      'buffFire':{'type':2,'priority':22030,'name':'effFire',    'act':{'tgt':[0,-5],'dmgRealRate':2000,'nonSkill':2,'noBfr':2,'noAft':2,'priority':200, 'element':'Fire','eff':'effFire','info':['effFire',2]}},                                                                          




      'buffPoison':{'type':2,'priority':22040,'name':'skill227',    'checkEnd':2},                                                
      'buffBreak':{'type':2,'priority':22050,'name':'buffBreak',    'prop':{'resRate':-200,'defRate':-2000}},                      
      'buffWeak':{'type':2,'priority':22060,'name':'buffWeak',    'checkEnd':2,    'prop':{'atkRate':-200,'defRate':-200}},       
      'buffSlow':{'type':2,'priority':22070,'name':'buffSlow',    'prop':{'spd':-30}},                                            
      'buffSad':{'type':2,'priority':22080,'name':'buffSad',    'checkEnd':2,},                                                   
      'buffTrance':{'type':2,'priority':22075,'name':'buffTrance',    'checkEnd':2,},                                             

      'buffDirty':{'type':2,'priority':222000,'name':'buffDirty',    'prop':{'buffRate':-5000}},                                   

      'buffBreak2':{'type':2,'priority':22500,'name':'buffBreak2',    'prop':{'defRate':-220,'block':-9999999,'atkRate':50}},     
      'buffUnreal':{'type':2,'priority':22999,'name':'buffUnreal',    'prop':{'atkRate':-25,'defRate':-25,'deBuffRate':500}},     
      'buffFrozen':{'type':2,'priority':23000,'name':'buffFrozen', 'checkEnd':2, 'prop':{'atkRate':-50,'skillPoint':-500}},       

      'buffTremble':{'type':2,'priority':23002, 'name':'effTremble', 'checkEnd':2, 'prop':{'crit':-5000000} },                    
      'buffDefeat':{'type':2,'priority':23002, 'name':'buffDefeat', 'prop':{'ban':2,'defRate':-200},'act':{'end':2,'priority':2002,'info':['effDefeat',2]}},      




      'buff270h':{'type':0,'priority':200000,'name':'军略战备',},
      'skill274_0':{'type':0,'priority':2000200,'name':'忘忧增益',},
      'skill274_2':{'type':0,'priority':200020,'name':'忘忧减益',},
      'buff280h':{'type':0,'priority':200030,'name':'远战杀意',},
      'buff246h':{'type':0,'priority':200032,'name':'死斗先御',},  

      'buff239':{'type':0,'priority':200040,'name':'buff239',    'stackMax':2000,'prop':{'atkRate':-200,'defRate':-30}},       
           
      'buff245':{'type':0,'priority':200050,'name':'buff245',    'stackMax':2000,'prop':{'dmgRate':-80,'dmgRealPer':-0.02}},       
      'buff960h_0':{'type':0,'priority':200060,'name':'buff960h_0',  'stackMax':200  },        
      'buff975':{'type':0,'priority':200062,'name':'重生加攻', },                                   
      'buff976':{'type':0,'priority':200062,'name':'buff976', },                                    
      'buff976_2':{'type':0,'priority':200063,'name':'buff976_2', },                                
      'buff976_2':{'type':0,'priority':200064,'name':'buff976_2', 'checkEnd':2 },                   
      'buff976_3':{'type':0,'priority':200065,'name':'buff976_2', 'checkEnd':2 },                   



      'buffBanArmyA':{   
          'type':0,
          'priority':200400,
          'name':'禁兵主',
          'banAct':{
             'skill289':2,'skill204':2,'skill206':2,           
             'skill209':2,'skill2200':2,'skill222':2,           
             'skill226':2,'skill228':2,'skill292':2,           
             'skill222':2,'skill224':2,'skill292':2,           
          },
          'checkEnd':2,
      },    
      'buffBanArmyB':{   
          'type':0,
          'priority':200402,
          'name':'禁兵被',
          'banAct':{
             'armyB':2
          },
          'checkEnd':2,
      },
      'buffBanHeroA':{   
          'type':0,
          'priority':200402,
          'name':'禁英主',
          'banAct':{
             'heroA':2
          },
          'checkEnd':2,
      },

      
      'buffBanArmyA2':{   
          'type':2,
          'priority':2004200,
          'name':'禁兵主',
          'banAct':{
             'armyA':2
          },
          'checkEnd':2,
      },
      'buffBanArmyB2':{   
          'type':2,
          'priority':200422,
          'name':'禁兵被',
          'banAct':{
             'armyB':2
          },
          'checkEnd':2,
      },
      'buffBanHeroA2':{   
          'type':2,
          'priority':200422,
          'name':'禁英主',
          'banAct':{
             'heroA':2
          },
          'checkEnd':2,
      },



      'buffAtk':{'type':0,'priority':200500,'name':'器械加攻',  'stackMax':2000, 'prop':{'atkRate':200,'dmgRate':200,'resRate':50}},
      'buffAtk2':{'type':0,'priority':200502,'name':'器械狂战',  'stackMax':2000, 'prop':{'atkRate':2500,'dmgRate':20000}},

      'buff702':{'type':0,'priority':200529,'name':'虎痴',  'stackMax':6, 'prop':{'atkRate':25,'atk':75}},
      'buff705':{'type':0,'priority':200520,'name':'月影',  'stackMax':4, 'prop':{'defRate':75}},
      'buff706r':{'type':0,'priority':200522,'name':'斩后免伤',   'prop':{'resRate':600}},
      'buff705r':{'type':0,'priority':200522,'name':'异阵加攻', 'prop':{'atkRate':2000,'dmg':2000,}},         
      'buff705a':{'type':0,'priority':200523,'name':'近一破防','stackMax':5, 'prop':{'resRate':-50}},         
      'buff707a':{'type':0,'priority':200530,'name':'军屯',  'prop':{'deBuffRate':-450,'defRate':-20,'resFinal':-25}},    
      'buff708':{'type':0,'priority':200540,'name':'战神',  'prop':{'resRate':250}},
      'buff723a':{'type':0,'priority':200750,'name':'威压',  'prop':{'skillPoint':-250}},
      'buff726':{'type':0,'priority':200560,'name':'龙胆',  'stackMax':7, 'prop':{'atkRate':70,'defRate':70}},
      'buff726a':{'type':0,'priority':200562,'name':'近一破攻防',  'stackMax':4, 'prop':{'resRate':-50,'dmgRate':-50}},      
      'buff720r':{'type':0,'priority':200565,'name':'骑兵增益',  'prop':{'resFinal':300,'dmgFinal':300}},                  
      'buff722':{'type':0,'priority':200570,'name':'兵势萎靡',  'stackMax':4, 'prop':{'dmg':-75,'dmgFinal':-250}},
      'buff723':{'type':0,'priority':200580,'name':'小乔力穷',  'stackMax':5, 'prop':{'atkRate':-200,'defRate':-8}},
      'buff726':{'type':0,'priority':200582,'name':'箭仇',   'stackMax':4, 'prop':{'atkRate':95,'atk':260}},
      'buff766':{'type':0,'priority':200582,'name':'于吉力穷',  'stackMax':5, 'prop':{'atkRate':-5,'defRate':-200}},

      'buff728r':{'type':0,'priority':200583,'name':'强盾',    'stackMax':22,  'prop':{'resRate':2000}},
      'buff728r_2':{'type':0,'priority':200584,'name':'强弓',  'stackMax':22,  'prop':{'others.ignDef':200}},

      'buff772':{'type':0,'priority':200575,'name':'吕玲绮力穷',  'prop':{'atkRate':-65,'defRate':-75,'atk':45,'def':20}},
      'buff772a':{'type':0,'priority':200592,'name':'吕玲绮爆发'},
      'buff772a2':{'type':0,'priority':200592,'name':'吕兵爆发'},
      'buff772r':{'type':0,'priority':200593,'name':'万箭增强',  'stackMax':200,  'prop':{'others.dmgOut_skill225':300}},    
      'buff733':{'type':0,'priority':200600,'name':'锦帆',  'prop':{'atkRate':280,'defRate':280,'atk':200,'def':5}},
      'buff734a':{'type':0,'priority':2006200,'name':'神荀彧力穷',  'stackMax':8, 'prop':{'atkRate':-200,'defRate':-20,'resFinal':-60,'resRate':-60}},
      'buff775a':{'type':0,'priority':200620,'name':'泰然',  'prop':{'defRate':200,'def':2000,'resRate':250,'resFinal':250}},
      'buff764a':{'type':0,'priority':200622,'name':'龙怒递减', 'stackMax':200, 'prop':{'others.dmgOut_skill244':-50}},     
      'buff776':{'type':0,'priority':200630,'name':'4倍星戮',  'prop':{'atkRate':240, 'defRate':-40}},
      'buff776adj':{'type':0,'priority':200632,'name':'星戮',  'prop':{'atkRate':60, 'defRate':-200}},
      'buff777':{'type':0,'priority':200632,'name':'春华减攻',  'prop':{'atkRate':-400, 'resRate':2000}, 'checkEnd':2},    

      'buff765a':{'type':0,'priority':200640,'name':'银针',   'stackMax':2000, 'prop':{'atkRate':95}},
      'buff783':{'type':0,'priority':200650,'name':'王越远守'},
      'buff778_2':{'type':0,'priority':200660,'name':'刘禅抗反噬',   'prop':{'others.elementAnti':-600}},
      'buff778_2adj':{'type':0,'priority':200662,'name':'禅副抗反噬',   'prop':{'others.elementAnti':-250}},
      'buff778_2':{'type':0,'priority':200670,'name':'刘禅免伤',   'prop':{'resRate':250}},
      'buff778_2adj':{'type':0,'priority':200672,'name':'禅副免伤',   'prop':{'resRate':60}},
      'buff779':{'type':0,'priority':200672,'name':'鸯勇',   'stackMax':2000,  'prop':{'atkRate':95,'crit':80}},
      'buff779a':{'type':0,'priority':200673,'name':'文鸯免伤',   'prop':{'resRate':950}, 'checkEnd':2},
      'buff779r':{'type':0,'priority':200674,'name':'buff779r', 'stackMax':8, 'prop':{'spd':5, 'others.critRate':2000, 'others.critAdd':2000, 'dmgRate':50}},   

      'buff782':{'type':0,'priority':200674,'name':'突骨免英', 'checkEnd':2, 'prop':{'resHero':4000,'resAdjutant':4000}},  
      'buff782r':{'type':0,'priority':200675,'name':'突骨霸体', 'checkEnd':2, 'prop':{'resRate':400,'resFinal':50, 'deBuffRate':-2000000}},  

      'buff783r':{'type':0,'priority':200677,'name':'剑意'},                            

      'buff789':{'type':0,'priority':200680,'name':'畏惧不前',   'checkEnd':2, 'prop':{'others.banHero':20000}},
      'buff798':{'type':0,'priority':200686,'name':'韩信增伤',   'stackMax':5, 'prop':{'dmgRate':2000,'dmg':30}},             

      'buff792a':{'type':0,'priority':200687,'name':'前军加攻',   'stackMax':6, 'prop':{'atkRate':20}},             
      'buff792a2':{'type':0,'priority':200688,'name':'孙坚暗减', 'stackMax':200, 'prop':{'defRate':-30,'resFinal':-30,'resRate':-30}},    

      'fate7233':{'type':0,'priority':200675,'name':'一夫当关',    'prop':{'dmgRate':200}},  
      'buff763':{'type':0,'priority':200700,'name':'仁德后攻'  , 'prop':{'atkRate':230,'defRate':-50,'resFinal':-20,'resRate':-20}},

      'buff786':{'type':0,'priority':2007200,'name':'adjutantE786_0'  , 'prop':{}},    
      'beHithero797_2':{'type':0,'priority':200720,'name':'beHithero797_2',  'stackMax':2000,  'prop':{'others.dmgIn_hero797':500}},          
      'buff797r':{'type':0,'priority':200722,'name':'张良攒攻',  'stackMax':25,  'prop':{'atkRate':25}},                
      'buff7000a':{'type':0,'priority':200730,'name':'buff7000a', 'stackMax':25, 'prop':{'atkRate':25}},       
      'skill974h':{'type':0,'priority':200740,'name':'skill974h',     'prop':{'dmgRate':50,'dmg':30,'dmgFinal':30, 'atkRate':25}},     
      'buff725r':{'type':0,'priority':200750,'name':'吕蒙暗减',  'prop':{'res':-250,'resRate':-2000,'resFinal':-50,'defRate':-50,'dmgRate':-2000}},   



      'buffPoisonous':{'type':0,'priority':200730,'name':'buffPoisonous',   },                                                      
      'buffPoisonous2':{    
         'type':0,'priority':200740,'name':'buffPoisonous2', 
         'prop':{
            
            'others.elementSummon':-300
         }
      },        
      'buff972_2':{'type':0,'priority':200750,'name':'buff972_2', 'prop':{'others.elementShield':-300,'others.elementShield':-300} },                
      'buffLure':{'type':0,'priority':200760,'name':'buffLure',    'prop':{'atkRate':-250,'dmgRate':-50,'dmg':-50}},                        


      'equip022':{'type':0,'priority':200200,'name':'偃月刀光',     'prop':{'dmgRate':300}},
      'equip0200':{'type':0,'priority':20020,'name':'连弩之威',     'prop':{'dmgRate':250}},
      'equip004_2':{'type':0,'priority':20030,'name':'倚天近战',     'prop':{'dmgRate':2000}},
      'equip004_2':{'type':0,'priority':20040,'name':'倚天远战',     'prop':{'resRate':200}},
      'equip025_2':{'type':0,'priority':20050,'name':'大宛远2',     'prop':{'dmgRate':50,'dmgFinal':20}},
      'equip025_2':{'type':0,'priority':20060,'name':'大宛远2',     'prop':{'dmgRate':2000,'dmgFinal':40}},
      'equip020':{'type':0,'priority':20070,'name':'白羽',     'prop':{'resArmy2':2000,'resArmy3':2000}},
      'equip022':{'type':0,'priority':20072,'name':'麻沸散',  'stackMax':5, 'prop':{'resRate':50}},      
      'equip083_2':{'type':0,'priority':20080,'name':'孙子兵法防',     'prop':{'resRate':240}},
      'equip083_2':{'type':0,'priority':20075,'name':'孙子兵法攻',     'prop':{'dmgRate':220}},
      'equip2002':{'type':0,'priority':202000,'name':'古锭刀强将',     'stackMax':6,},
      'equip2002_2':{'type':0,'priority':202002,'name':'古锭刀强将2',     'stackMax':6,},
      'equip2005':{'type':0,'priority':202200,'name':'宫灯强兵',     'stackMax':6,},
      'equip2005_2':{'type':0,'priority':20222,'name':'宫灯强兵2',     'stackMax':6,},
      'equip2007':{'type':0,'priority':20220,'name':'蓄太极',     'stackMax':8,},    
      'equip2007_2':{'type':0,'priority':20222,'name':'蓄太极2',     'stackMax':8,},    
      'equip003':{'type':0,'priority':20230,'name':'equip003',     'prop':{'dmgFinal':250,'dmgRate':2000}},   
      'equip223_2':{'type':0,'priority':20240,'name':'金牛叠印',     'stackMax':5,},    
      
      
      'equip223_2':{'type':0,'priority':20270,'name':'虎魄', 'checkEnd':2, },  
      'equip223_2':{'type':0,'priority':20280,'name':'虎魄萎靡', 'checkEnd':2, 'stackMax':99, 'prop':{'dmgRate':-20,'atkRate':-2000,'atk':-200, 'defRate':-2000,'def':-200}},
      'equip023':{'type':0,'priority':20275,'name':'玉带远免',  'checkEnd':2, 'prop':{'resHero':700}},  

      'equip054':{'type':0,'priority':20500,'name':'武帝冠冕',     'prop':{'dmgRate':2000}},
      'equip050':{'type':0,'priority':205200,'name':'武帝墨宝',     'prop':{'resRate':2000}},
      'equip039':{'type':0,'priority':20520,'name':'武帝龙驹',     'prop':{'resRate':2000}},
      'equip036':{'type':0,'priority':20530,'name':'武帝佩剑',     'prop':{'dmgRate':2000}},

      'group004':{'type':0,'priority':20539,'name':'轩辕',  'stackMax':5, 'prop':{'dmgFinal':30}},    
      'group005':{'type':0,'priority':20540,'name':'造化',  'stackMax':3, 'prop':{'defRate':-2000,'resFinal':-2000,'dmgFinal':-40}},    
      'equip240_2':{'type':0,'priority':20542,'name':'equip240_2', 'stackMax':3,  'prop':{'defRate':-2000,'resFinal':-2000,'dmgFinal':-40}},       
      'group006':{'type':0,'priority':20750,'name':'伏羲',  'stackMax':2, 'prop':{'dmgFinal':30,'defRate':40}},    
      'group007':{'type':0,'priority':20560,'name':'青麟',  'stackMax':5, 'prop':{'dmgFinal':30,'atkRate':20}},    
      
      'equip236':{'type':0,'priority':20538,'name':'斗神套致残',   'prop':{'atkRate':-200,'dmgRate':-50, 'defRate':-200,'resRate':-50 }},        

      'equip252':{'type':0,'priority':20539,'name':'equip252',  'checkEnd':2,   'prop':{'atkRate':300,'defRate':-300}},   
      'equip257':{'type':0,'priority':20750,'name':'equip257', 'stackMax':3, 'prop':{'resRate':-70, 'res':-25}},             
      'equip260':{'type':2,'priority':20752,'name':'equip260', 'prop':{'block':400 }},                                        
      'equip263':{'type':0,'priority':20752,'name':'equip263',   'stackMax':5,  'prop':{'others.dmgOut_skill222':-250}},       
      'equip264':{'type':0,'priority':20755,'name':'equip264', 'stackMax':-2, 'checkEnd':2, 'prop':{'dmgRate':600},        
           'acts':[
                {
                        'priority': -26400,	   
                        'type': 3,	           
                        'srcArmy': 0,
                        'tgt':[0, 0],
                        
                        'nonSkill':2,   
                        'noBfr': 2,  
                        'noAft': 2,    
                        'times': -2,
                        'removeBuffId':'equip264',
                        'follow':{'keys':{'dmg':20000,'dmgReal':20000}},
                        'eff':'effNull',
                        'time':0,
                        'info':['自驱蓄力',0],
                },
           ],
      },
      'equip274':{'type':0,'priority':20560,'name':'夜宵',  'prop':{'others.elementSummon':-500}},           
      'equip275':{'type':0,'priority':20565,'name':'六韬',  'prop':{'dmgRate':240, 'defRate':220}},          
      'equip276':{'type':0,'priority':20570,'name':'三略',  'prop':{'resRate':280, 'atkRate':80}},           
      'equip282':{'type':2,'priority':20575,'name':'崔谷', 'checkEnd':2,  'prop':{'crit':20000}},             
      'equip287':{'type':0,'priority':20580,'name':'鬼谷', 'prop':{'dmgRate':200, 'defRate':200}},           




      'scheme002':{'type':2,'priority':666200020,'name':'引玉2', 'checkEnd':2,},                                                  
      'scheme002_2':{'type':2,'priority':666200022,'name':'引玉2', 'checkEnd':2,},                                                
      'scheme009':{'type':0,'priority':666200075,'name':'scheme009', },                                                           

      'scheme2005':{'type':0,'priority':666220050,'name':'scheme2005', 'checkEnd':2, 'prop':{'dmgSkill':-600}},                     
      'scheme2006':{'type':0,'priority':666220060,'name':'scheme2006', 'checkEnd':2, 'prop':{'skillPoint':-450}},                   
      'scheme2007':{'type':0,'priority':666220070,'name':'scheme2007', 'checkEnd':2, 'prop':{'resRate':-200}},                      

      'scheme202':{'type':0,'priority':666220200,'name':'scheme202', 'checkEnd':2 },                                              
      'scheme202':{'type':0,'priority':66622020,'name':'scheme202', },                                                           
      'scheme203':{'type':0,'priority':66622030,'name':'scheme203',},                                                            
      'scheme203_2':{'type':0,'priority':66622032,'name':'scheme203_2',},                                                        
      'scheme204':{'type':0,'priority':66622040,'name':'scheme204', 'checkEnd':2, },                                             
      'scheme205':{'type':0,'priority':66622050,'name':'scheme205', 'checkEnd':2, },                                             
      'scheme206':{'type':0,'priority':66622060,'name':'scheme206', 'checkEnd':2, },                                             
      'scheme207':{'type':0,'priority':66622070,'name':'scheme207', 'checkEnd':2, 'prop':{'resRate':2000,}},                       
      'scheme2200':{'type':0,'priority':666222000,'name':'scheme2200', 'checkEnd':2, },                                             
      'scheme222':{'type':0,'priority':666222200,'name':'scheme222', 'checkEnd':2, },                                             



      'skin722':{'type':0,'priority':30722,'name':'skin722_2',   'checkEnd':2,    'prop':{'dmgFinal':40}},          
      'skin727':{'type':0,'priority':30727,'name':'skin727_2',   'checkEnd':2,    'prop':{'dmgFinal':40}},          
      'skin7200':{'type':0,'priority':307200,'name':'skin7200_2',   'checkEnd':2,    'prop':{'dmgFinal':30}},          
      'skin723':{'type':0,'priority':30723,'name':'skin723_2',   'prop':{'resFinal':25}},          
      'skin722':{'type':0,'priority':30722,'name':'skin722_2',   'checkEnd':2,    'prop':{'dmgFinal':30}},          
      'skin705':{'type':0,'priority':30705,'name':'skin705_2',   'prop':{'resFinal':25}},          


      
      


      'sp3002':{'type':0,'priority':43002,'name':'sp3002_name',    'stackMax':4,'checkEnd':2,    'prop':{'others.critRate':250,'others.critAdd':50,}},       
      'sp3003':{'type':0,'priority':43003,'name':'sp3003_name',   'checkEnd':2,  },          
      'sp32002':{'type':0,'priority':432002,'name':'sp32002_name',   'checkEnd':2,  },          
      'sp3302':{'type':0,'priority':43302,'name':'sp3302_name',   'checkEnd':2,  },          







      'buffAlarmed':{    
          'type':0,
          'priority':45000,
          'name':'buffAlarmed', 
          'prop':{
            'others.elementOutShield':-500,
            'others.elementOutSummon':-500
          }
      },   



      'beastTypeA':{'type':0,'priority':88750,'name':'_beastType_A',    'stackMax':2000,'prop':{'resFinal':95}},          
      'beastTypeB':{'type':0,'priority':88752,'name':'_beastType_B',    'stackMax':2000,'prop':{'dmgFinal':25}},          
      'beastTypeC':{'type':0,'priority':88752,'name':'_beastType_C',    'stackMax':2000,'prop':{'crit':45}},           
      'beastTypeD':{'type':0,'priority':88753,'name':'_beastType_D',    'stackMax':2000,'prop':{'block':45}},          





      'buffMagma':{    
          'type':0,
          'priority':33320000,
          'name':'buffMagma',    
          'act':{
             'priority':298,
             'tgt':[0,-5],
             
             'dmgGodPowerEnemy':4000,        
             'transformObj':{
                'dmgGodPowerEnemy':{
                   'enemy.godLogic.gstrFinal':200,
                }
             },  
             'nonSkill':2,
             'noBfr':2,
             'noAft':2,
             'eff':'effMagma',
             'lv':200, 
             'info':['buffMagma',2],              
          }
      },   

      'buffRich':{    
          'type':2,   
          'priority':333200200,
          'name':'buffRich',    
          'checkEnd':2,
          'prop':{'resHero':750}
      }, 
      'buffRich2':{    
          'type':0,
          'priority':33320022,
          'name':'buffRich2',    
          'checkEnd':2,
          'prop':{'atkRate':225}
      }, 

      'buffAbstain':{    
          'type':2,   
          'priority':33320040,
          'name':'buffAbstain',    
          'checkEnd':2,
          'prop':{'dmg':-200}
      }, 
      'buffAbstain2':{    
          'type':0,
          'priority':33320042,
          'name':'buffAbstain2',    
          'checkEnd':2,
          'prop':{'defRate':275}
      }, 

      'buffMoon':{    
          'type':0,
          'priority':33320200,
          'name':'buffMoon', 
          'stackMax':2000,  
          'prop':{
            'others.elementShield':-200,
            'others.elementCure':-200,
            'others.elementSummon':-200
          }
      },   

      
      'buffG300_4':{'type':0,'priority':3333004,'name':'火神断援',  'checkEnd':2, 'prop':{'others.elementShield':-800, 'others.elementSummon':-800}},



      'buffG302':{  
           'type':0,
           'priority':3333020,
           'name':'buffG302',
           'checkEnd':2,
           'acts':[
                {
                        'priority':3333020,	  
                        'type': 4,	     
                        'follow':{'keys':{'summonReal':20000,}},      	      
                        'nonSkill':2,   
                        'times': -2,    
                        'binding':{
                           'summonReversalRate':500,
                           
                        },
                        'info':['buffG302',2], 
                },
           ],
      },
      'buffG302_4':{  
           'type':0,
           'priority':3333022,
           'name':'buffG302_4',
           'checkEnd':2,
           'acts':[
                {
                        'priority':3333022,	  
                        'type': 4,	     
                        'follow':{'keys':{'cureReal':20000,}},   
                        'nonSkill':2,   
                        'times': -2,    
                        'binding':{
                           'cureReversalRate':500,
                        },
                        'info':['buffG302_4',2],
                },
           ],
      },

      'buffFreeze':{  
           'type':0,
           'priority':22009,
           'name':'buffFreeze',
           'prop':{'ban':2},
           'act':{'end':2,'priority':2002,'info':['buffFreeze',2]},       
           'acts':[
                {
                        'priority':22009,	   
                        'type': 4,	           	     
                        'nonSkill':2,   
                        'times': -2,    
                        'binding':{
                           'crit':99999999,
                        },
                },
           ],
      }, 

      
      'buffG304_3':{'type':0,'priority':3333043,'name':'水神滞援',  'checkEnd':2, 'prop':{'others.elementShield':-400, 'others.elementSummon':-400}},



      'buffWeather2':{   
           'type':0,
           'priority':4442,
           'name':'weather_info02',
           'condProp':[['armyType','!=',3]],
           'prop':{'atkRate':-200,'defRate':-2000},
           'acts':[
                {
                        'priority': -7777777,	   
                        'type': 4,	           
                        'cond':['checkBuff', 0, -5, 'buffStun', '>', 0],
                        'time':0,	     
                        'nonSkill':2,   
                        'times': -2,    
                        'binding':{
                           'res':-300,	    
                        },
                        'info':['weather2_2',2],
                },
           ],
      },      

      'buffWeather2':{   
           'type':0,
           'priority':4442,
           'name':'weather_info02',
           'props':[
              {
                 'condProp':[['armyType','!=',2]],
                 'prop':{'atkRate':-200,'defRate':-2000},
              },
              {
                 'prop':{'others.get_buffFire':-200000},   
              },
           ],
      },   
      'buffWeather3':{   
           'type':0,
           'priority':4443,
           'name':'weather_info03',
           'condProp':[['armyType','!=',0]],
           'prop':{'atkRate':-200,'defRate':-2000},
           'acts':[
                {
                        'priority': -7777777,	 
                        'type': 2,	            
                        'follow':{'effFaction':2},
                        'time':0,	         
                        'nonSkill':2,  
                        'times': -2,   
                        'binding':{
                           'ignDef':300,	  
                           'dmgScale':5000,	    
                           'dmgReal':30,	  
                        },
                        'lv':20,
                        'info':['weather3_2',2],
                },
           ],
      },   
      'buffWeather4':{   
           'type':0,
           'priority':4444,
           'name':'weather_info04',
           'condProp':[['armyType','!=',2]],
           'prop':{'atkRate':-200,'defRate':-2000},
           'acts':[
                {
                        'priority': -7777777,	  
                        'type': 2,	            
                        'cond':['checkBuff', 0, -5, 'buffWeak', '>', 0],
                        'time':0,	          
                        'nonSkill':2,  
                        'times': -2,  
                        'binding':{
                           'dmgScale':-250,	    
                        },
                        'info':['weather4_2',2],
                        'lv':7,
                },
           ],
      }, 

 


   },

                                                                       





   'arenaWinnerHpPer':0.2,        
   'arenaReward':[                
      [0.5,{'v':[2,0]}],
      [0,{'v':[0,2]}],
   ],

   'banBattleWeather':2,            
   'battleWeather':{                
      '2':{  
         'special':[{  
            'priority':-7777777,
            'change':{
               'prop':{
                  '%armys_0.atkRate':-200,         
                  '%armys_2.atkRate':-200,         
                  '%armys_2.atkRate':-200,         
                  '%armys_0.defRate':-2000,         
                  '%armys_2.defRate':-2000,         
                  '%armys_2.defRate':-2000,         
               },
               'skill':{
                  'weather2':{
                     'act':[{
                        'priority': -7777777,	 
                        'type': 4,	            
                        'src': -2,	            
                        'cond':['checkBuff', 0, -5, 'buffStun', '>', 0],
                        'time':0,	          
                        'nonSkill':2,    
                        'times': -2,    
                        'binding':{
                           'res':-300,	    
                        },
                        'info':['weather2_2',2],
                     }],
                  },
               },
            },
         }],
      },
      '2':{  
         'special':[{  
            'priority':-7777777,
            'change':{
               'prop':{
                  '%armys_0.atkRate':-200,      
                  '%armys_3.atkRate':-200,      
                  '%armys_2.atkRate':-200,
                  '%armys_0.defRate':-2000,       
                  '%armys_3.defRate':-2000,
                  '%armys_2.defRate':-2000,
                  'armys[0].others.get_buffFire':-200000,
                  'armys[2].others.get_buffFire':-200000,
               },
            },
         }],
      },
      '3':{  
         'special':[{  
            'priority':-7777777,
            'change':{
               'prop':{
                  '%armys_2.atkRate':-200,   
                  '%armys_3.atkRate':-200,
                  '%armys_2.atkRate':-200,
                  '%armys_2.defRate':-2000,    
                  '%armys_3.defRate':-2000,
                  '%armys_2.defRate':-2000,
               },
               'skill':{
                  'weather3':{
                     'act':[{
                        'priority': -7777777,	 
                        'type': 2,	            
                        'src': -2,	            
                        'follow':{'effFaction':2},
                        'time':0,	          
                        'nonSkill':2,    
                        'times': -2,    
                        'binding':{
                           'ignDef':300,	  
                           'dmgScale':5000,	    
                           'dmgReal':30,	  
                        },
                        'lv':20,
                        'info':['weather3_2',2],
                     }],
                  },
               },
            },
         }],
      },
      '4':{  
         'special':[{  
            'priority':-7777777,
            'change':{
               'prop':{
                  '%armys_2.atkRate':-200,      
                  '%armys_3.atkRate':-200,
                  '%armys_0.atkRate':-200,
                  '%armys_2.defRate':-2000,      
                  '%armys_3.defRate':-2000,
                  '%armys_0.defRate':-2000,
               },
               'skill':{
                  'weather4':{
                     'act':[{
                        'priority': -7777777,	 
                        'type': 2,	            
                        'src': -2,	            
                        'cond':['checkBuff', 0, -5, 'buffWeak', '>', 0],
                        'time':0,	          
                        'nonSkill':2,    
                        'times': -2,    
                        'binding':{
                           'dmgScale':-250,	    
                        },
                        'info':['weather4_2',2],
                        'lv':7,
                     }],
                  },
               },
            },
         }],
      },
   },

   'battleMode':{                
      '0':{  
         'has_chat':2,   
      },
      '2':{  
         'has_chat':2,   
      },
      '2':{  
         'has_chat':2,   
      },
      '5':{  
         'has_chat':2,   
      },
      '6':{  
         'has_chat':2,   
         'shortCut':4,   
      },

      '200':{  
         'has_chat':2,   
         'battle_special':{
            'shogunPoint':-20000,    

            'elementOutAnti':-500,    
            'elementOutShock':-500,    
            'elementOutShield':-500,    
            'elementOutSummon':-500,    
            'elementOutCure':-500,     

            
            
         },
      },

      '22':{  
         'has_chat':2,   
         'shortCut':5,   
      },
      '22':{  
         'has_chat':2,   
         'shortCut':5,   
      },

      '80':{  
         'has_chat':2,   
      },

      '2000':{  
         'has_chat':2,   
      },
      '2002':{  
         'has_chat':2,   
      },
      '2002':{  
         'has_chat':2,   
      },
      '2003':{  
         'has_chat':2,   
      },
      '2004':{  
         'has_chat':2,   
      },

      '-2':{  
         'has_chat':2,   
      },
   },


   'troopInsertIndex':5,  

   'critRate':2500,  
   'blockRate':500,  
   'critAdd':20,  
   'blockAdd':20,  

   
   'proudActionRate':[50,500,  25,750,  30,750],  
   'proudTiredRate':[0,0,  280,750,  300,750],
   
   'proudOthers':{
      'elementOutAnti':20000,    
      'elementOutShock':20000,    
      'elementOutShield':20000,    
      'elementOutSummon':20000,    
      'elementOutCure':20000,     
   }, 

   'shogunRate':[200,200,50,400],  
   'shogunHeroRate':{'power':20000, 'powerBase':5000},  
   'shogunArmyRate':{'power':500, 'powerBase':2500},  

   'officialRate':{      
      '0':[50,50,2000],
      '2':[25,25,50],
      '2':[25,25,50],
      '3':[25,25,50],
      '4':[25,25,50],
   },  
   'cityBuffRate':[2200,2000],  

   'soulLvSpecialMult':[2,2,3,4,5,6.5,8,9.5,22,22.5],              
   'soulLvPassiveMult':[2,2,3.5,5.5,8,22,24.5,28.5,24,30],     
   'soulAttrs':{     
       'soul02':{    
         'infoArr':['*|special.change.prop.$heroLogic.dmgSkill'],
         'special':{  
            'change':{
               'prop':{
                  'heroLogic.dmgSkill':20,
               },
            },
         },  
         'passive':{
            'rslt':{'army[0].atk':45,'army[0].hpm':60,'powerRate':5}
         },      
       },
       'soul02':{    
         'infoArr':['*|special.changeEnemy.prop.$heroLogic.dmgSkill'],
         'special':{  
            'changeEnemy':{
               'prop':{
                  'heroLogic.dmgSkill':-20,
               },
            },
         },  
         'passive':{
            'rslt':{'army[2].atk':45,'army[2].hpm':60,'powerRate':5}
         },    
       },
       'soul03':{    
         'infoArr':['*|special.change.prop.$armys[0].dmgSkill'],
         'special':{  
            'change':{
               'prop':{
                  'armys[0].dmgSkill':20,
                  'armys[2].dmgSkill':20,
               },
            },
         },  
         'passive':{
            'rslt':{'army[0].hpm':220,'powerRate':5}
         }, 
       },
       'soul04':{    
         'infoArr':['*|special.changeEnemy.prop.$armys[0].dmgSkill'],
         'special':{  
            'changeEnemy':{
               'prop':{
                  'armys[0].dmgSkill':-20,
                  'armys[2].dmgSkill':-20,
               },
            },
         },  
         'passive':{
            'rslt':{'army[2].hpm':220,'powerRate':5}
         },  
       },
       'soul05':{    
         'infoArr':['*|0-special.changeEnemy.prop.$armys[0].others.roundRes_2'],
         'special':{  
            'changeEnemy':{
               'prop':{
                  'armys[0].others.roundRes_2':-20,
                  'armys[0].others.roundRes_2':-20,
                  'armys[2].others.roundRes_2':-20,
                  'armys[2].others.roundRes_2':-20,
               },
            },
         },  
         'passive':{
            'rslt':{'army[0].def':75,'powerRate':5}
         },  
       },
       'soul06':{    
         'infoArr':['*|0-special.change.prop.$armys[0].others.roundRes_2'],
         'special':{  
            'change':{
               'prop':{
                  'armys[0].others.roundRes_2':20,
                  'armys[0].others.roundRes_2':20,
                  'armys[2].others.roundRes_2':20,
                  'armys[2].others.roundRes_2':20,
               },
            },
         },  
         'passive':{
            'rslt':{'army[2].def':75,'powerRate':5}
         },  
       },
       'soul07':{    
         'infoArr':['*|0-special.changeEnemy.prop.$armys[0].others.roundRes_3'],
         'special':{  
            'changeEnemy':{
               'prop':{
                  'armys[0].others.roundRes_3':-20,
                  'armys[0].others.roundRes_4':-20,
                  'armys[0].others.roundRes_5':-25,
                  'armys[0].others.roundRes_6':-200,
                  'armys[0].others.roundRes_7':-5,
                  'armys[2].others.roundRes_3':-20,
                  'armys[2].others.roundRes_4':-20,
                  'armys[2].others.roundRes_5':-25,
                  'armys[2].others.roundRes_6':-200,
                  'armys[2].others.roundRes_7':-5,
               },
            },
         },  
         'passive':{
            'rslt':{'army[0].atk':75,'powerRate':5}
         }, 
       },
       'soul08':{    
         'infoArr':['*|0-special.change.prop.$armys[0].others.roundRes_3'],
         'special':{  
            'change':{
               'prop':{
                  'armys[0].others.roundRes_3':20,
                  'armys[0].others.roundRes_4':20,
                  'armys[0].others.roundRes_5':25,
                  'armys[0].others.roundRes_6':200,
                  'armys[0].others.roundRes_7':5,
                  'armys[2].others.roundRes_3':20,
                  'armys[2].others.roundRes_4':20,
                  'armys[2].others.roundRes_5':25,
                  'armys[2].others.roundRes_6':200,
                  'armys[2].others.roundRes_7':5,
               },
            },
         },  
         'passive':{
            'rslt':{'army[2].atk':75,'powerRate':5}
         }, 
       },


       'soul09':{   
         'passive':{
            'rslt':{'atk':60,'def':40}
         }, 
       },
       'soul200':{   
         'passive':{
            'rslt':{'atk':60,'def':40}
         }, 
       },
       'soul22':{   
         'passive':{
            'rslt':{'atk':60,'def':40}
         }, 
       },
       'soul22':{   
         'passive':{
            'rslt':{'atk':60,'def':40}
         }, 
       },
       'soul23':{   
         'passive':{
            'rslt':{'atk':60,'def':40}
         }, 
       },
   },
   'soulEquipLimitArr':[     
       [0,0,2],              
       [0,2,4],
       [2,2,6],
       [2,2,8],
       [2,2,200],
       [2,3,22],
       [3,3,25],
       [3,4,28],
       [4,4,22],
       [4,5,24],
   ],




   'beastHpLow':0.25,     
   'beastHpHigh':0.75,    
   'beastDefPer':0.25,  
   'beastDefDmgMax':0.6,  
   'beastDefDmgMin':-0.4,  
   'beastCompPoint':20,  
   'beastSuperUnlock':[200,20,30],        
   'beastSuperSkillDefaultPassive':{     
      'rslt':{
         'powerRate':60, 
         'powerBase':300,
      }
   },

   
   'beastSortArr':[
      'A','a','B','b','C','c','D','d',
      'E','e','F','f',
      'I','i','J','j','K','k','L','l','M','m','N','n',
      'O','o',
    's225','s226','s227','s228','s229','s230','s232','s232','s233','s234','s295','s236','s237','s238','s240','s243','s239','s245','s246','s242','s242','s244',
      's202','s289','s202','s204','s203','s205','s206',
      's207','s209','s208','s2200','s275','s222','s222',
      's223','s225','s224','s226','s227','s228','s292',
      's229','s220','s222','s222','s223','s224','s292',
   ],

   'beastSuperSkill':{     
      'A':{   
         'infoArr':['*|beast.round_dmg_2'],
         'beast':{
             'round_dmg_2':240,
         },        
      }, 
      'a':{   
         'infoArr':['*|beast.round_res_2*0.8'],
         'beast':{
             'round_res_2':240,
         },                            
      },
      'B':{   
         'infoArr':['*|beast.round_dmg_2'],
         'beast':{
             'round_dmg_2':280,
         },                                               
      },  
      'b':{   
         'infoArr':['*|beast.round_res_2*0.8'],
         'beast':{
             'round_res_2':280,
         },                                       
      },
      'C':{   
         'infoArr':['*|beast.round_dmg_3'],
         'beast':{
             'round_dmg_3':220,
         },                          
      },
      'c':{   
         'infoArr':['*|beast.round_res_3*0.8'],
         'beast':{
             'round_res_3':220,
         },              
      },
      'D':{  
         'infoArr':['*|beast.round_dmg_4'],
         'beast':{
             'round_dmg_4':260,
             'round_dmg_5':260,
             'round_dmg_6':260,
             'round_dmg_7':260,
             'round_dmg_8':260,
             'round_dmg_9':260,
             'round_dmg_200':260,
         },
      },
      'd':{  
         'infoArr':['*|beast.round_res_4*0.8'],
         'beast':{
             'round_res_4':260,
             'round_res_5':260,
             'round_res_6':260,
             'round_res_7':260,
             'round_res_8':260,
             'round_res_9':260,
             'round_res_200':260,
         },
      },
      'E':{  
         'infoArr':['*|beast.act_army0_ignDef'],
         'beast':{
             'act_army0_ignDef':280,
         },                       
      },
      'e':{  
         'infoArr':['*|beast.act_army0_strDef'],
         'beast':{
             'act_army0_strDef':200,
         },                          
      },
      'F':{  
         'infoArr':['*|beast.act_army2_ignDef'],
         'beast':{
             'act_army2_ignDef':280,
         },               
      },
      'f':{  
         'infoArr':['*|beast.act_army2_strDef'],
         'beast':{
             'act_army2_strDef':200,
         },                          
      },

      

      'I':{   
         'infoArr':['*|beast.act_army2_ignDef'],
         'beast':{
             'act_army2_ignDef':250,
         },              
      },
      'i':{   
         'infoArr':['*|beast.act_army2_strDef'],
         'beast':{
             'act_army2_strDef':260,
         },  
      },
      'J':{   
         'infoArr':['*|beast.hp_low_dmg'],
         'beast':{
             'hp_low_dmg':240,
         },
      },
      'j':{   
         'infoArr':['*|beast.hp_low_res*0.8'],
         'beast':{
             'hp_low_res':240,
         },
      },
      'K':{   
         'infoArr':['*|beast.hp_high_dmg'],
         'beast':{
             'hp_high_dmg':220,
         },                                                                            
      },
      'k':{   
         'infoArr':['*|beast.hp_high_res*0.8'],
         'beast':{
             'hp_high_res':220,
         },                                                    
      },
      'L':{   
         'infoArr':['*|beast.only_dmg'],
         'beast':{
             'only_dmg':280,
         },                                 
      },
      'l':{   
         'infoArr':['*|beast.only_res*0.8'],
         'beast':{
             'only_res':320,
         },
      },
      'M':{   
         'infoArr':['*|beast.str_dmg'],
         'beast':{
             'str_dmg':75,
         },    
         'passive':{
             'priority':-2000,
             'cond':['str', '*', 95, 30],
             'rslt':{'powerRate':3, 'powerBase':200},
         },
      },
      'm':{   
         'infoArr':['*|beast.cha_res'],
         'beast':{
             'cha_res':75,
         },    
         'passive':{
             'priority':-2000,
             'cond':['cha', '*', 95, 30],
             'rslt':{'powerRate':3, 'powerBase':200},
         },
      },
      'N':{   
         'infoArr':['*|beast.agi_dmg'],
         'beast':{
             'agi_dmg':75,
         },
         'passive':{
             'priority':-2000,
             'cond':['agi', '*', 95, 30],
             'rslt':{'powerRate':3, 'powerBase':200},
         },
      },
      'n':{   
         'infoArr':['*|beast.lead_res'],
         'beast':{
             'lead_res':75,
         },
         'passive':{
             'priority':-2000,
             'cond':['lead', '*', 95, 30],
             'rslt':{'powerRate':3, 'powerBase':200},
         },
      },
      'O':{   
         'infoArr':['-|beast.any_dmgReal'],
         'beast':{
             'any_dmgReal':25,
         },
      },
      'o':{   
         'infoArr':['-|beast.any_resReal'],
         'beast':{
             'any_resReal':20,
         },    
      },

      's225':{   
         'infoArr':['*|skill.$skill225.act[0].dmgScale'],
         'skill':{
             'skill225.act[0].dmgScale':300,
         },
         'passive':[
            {
               'cond':['skill.skill225', '>', 0],
               'rslt':{'powerRate':20, 'powerBase':200},
            },
            {
               'cond':['skill.skill225', '*', 2],
               'rslt':{'powerRate':2.4},
            }
         ],
      },
      's226':{   
         'infoArr':['*|skill.$skill226.act[0].dmgScale'],
         'skill':{
             'skill226.act[0].dmgScale':320,
         },
         'passive':[
            {
               'cond':['skill.skill226', '>', 0],
               'rslt':{'powerRate':20, 'powerBase':200},
            },
            {
               'cond':['skill.skill226', '*', 2],
               'rslt':{'powerRate':2.4},
            }
         ],
      },
      's227':{   
         'infoArr':['*|skill.$skill227.act[0].dmgScale'],
         'skill':{
             'skill227.act[0].dmgScale':320,
         },
         'passive':[
            {
               'cond':['skill.skill227', '>', 0],
               'rslt':{'powerRate':20, 'powerBase':200},
            },
            {
               'cond':['skill.skill227', '*', 2],
               'rslt':{'powerRate':2.4},
            }
         ],
      },
      's228':{   
         'infoArr':['*|skill.$skill228.act[0].dmgScale','*|skill.$skill228.act[0].buff.buffFaction.rnd'],
         'skill':{
             'skill228.act[0].dmgScale':300,
             'skill228.act[0].buff.buffFaction.rnd':220,
         },
         'passive':[
            {
               'cond':['skill.skill228', '>', 0],
               'rslt':{'powerRate':20, 'powerBase':200},
            },
            {
               'cond':['skill.skill228', '*', 2],
               'rslt':{'powerRate':2.4},
            }
         ],
      },
      's229':{   
         'infoArr':['*|skill.$skill229.act[0].dmgScale'],
         'skill':{
             'skill229.act[0].dmgScale':320,
         },
         'passive':[
            {
               'cond':['skill.skill229', '>', 0],
               'rslt':{'powerRate':20, 'powerBase':200},
            },
            {
               'cond':['skill.skill229', '*', 2],
               'rslt':{'powerRate':2.4},
            }
         ],
      },
      's230':{   
         'infoArr':['*|skill.$skill230.act[0].dmgScale','*|skill.$skill230.act[0].buff.buffWeak.rnd'],
         'skill':{
             'skill230.act[0].dmgScale':300,
             'skill230.act[0].buff.buffWeak.rnd':220,
         },
         'passive':[
            {
               'cond':['skill.skill230', '>', 0],
               'rslt':{'powerRate':20, 'powerBase':200},
            },
            {
               'cond':['skill.skill230', '*', 2],
               'rslt':{'powerRate':2.4},
            }
         ],
      },
      's232':{   
         'infoArr':['*|skill.$skill232.act[0].buff.buffMorale.prop.defRate*2'],
         'skill':{
             'skill232.act[0].buff.buffMorale.prop.atkRate':30,
             'skill232.act[0].buff.buffMorale.prop.atk':50,
             'skill232.act[0].buff.buffMorale.prop.defRate':50,
         },
         'passive':[
            {
               'cond':['skill.skill232', '>', 0],
               'rslt':{'powerRate':20, 'powerBase':200},
            },
            {
               'cond':['skill.skill232', '*', 2],
               'rslt':{'powerRate':2.4},
            }
         ],
      },
      's232':{   
         'infoArr':['*|skill.$skill232.act[0].dmgScale','*|skill.$skill232.act[0].buff.buffSlow.rnd'],
         'skill':{
             'skill232.act[0].dmgScale':300,
             'skill232.act[0].buff.buffSlow.rnd':220,
         },
         'passive':[
            {
               'cond':['skill.skill232', '>', 0],
               'rslt':{'powerRate':20, 'powerBase':200},
            },
            {
               'cond':['skill.skill232', '*', 2],
               'rslt':{'powerRate':2.4},
            }
         ],
      },
      's233':{   
         'infoArr':['*|skill.$skill233.act[0].dmgScale','*|skill.$skill233.act[0].buff.buffWeak.rnd'],
         'skill':{
             'skill233.act[0].dmgScale':950,
             'skill233.act[0].buff.buffWeak.rnd':250,
         },
         'passive':[
            {
               'cond':['skill.skill233', '>', 0],
               'rslt':{'powerRate':25, 'powerBase':250},
            },
            {
               'cond':['skill.skill233', '*', 2],
               'rslt':{'powerRate':2.6},
            }
         ],
      },
      's234':{   
         'infoArr':['*|skill.$skill234.act[0].buff.buffArmor.prop.block*0.5'],
         'skill':{
             'skill234.act[0].buff.buffArmor.prop.block':700,
         },
         'passive':[
            {
               'cond':['skill.skill234', '>', 0],
               'rslt':{'powerRate':25, 'powerBase':250},
            },
            {
               'cond':['skill.skill234', '*', 2],
               'rslt':{'powerRate':2.6},
            }
         ],
      },
      's295':{   
         'infoArr':['*|skill.$skill295.act[0].dmgScale'],
         'skill':{
             'skill295.act[0].dmgScale':450,
             'skill295.act[0].dmgReal':25,
         },
         'passive':[
            {
               'cond':['skill.skill295', '>', 0],
               'rslt':{'powerRate':25, 'powerBase':250},
            },
            {
               'cond':['skill.skill295', '*', 2],
               'rslt':{'powerRate':2.6},
            }
         ],
      },
      's236':{   
         'infoArr':['*|skill.$skill236.act[0].dmgScale','*|skill.$skill236.act[0].buff.buffStun.rnd'],
         'skill':{
             'skill236.act[0].dmgScale':300,
             'skill236.act[0].buff.buffStun.rnd':220,
         },
         'passive':[
            {
               'cond':['skill.skill236', '>', 0],
               'rslt':{'powerRate':25, 'powerBase':250},
            },
            {
               'cond':['skill.skill236', '*', 2],
               'rslt':{'powerRate':2.6},
            }
         ],
      },
      's237':{   
         'infoArr':['*|skill.$skill237.act[0].dmgScale','*|skill.$skill237.act[0].buff.buffFire.rnd'],
         'skill':{
             'skill237.act[0].dmgScale':300,
             'skill237.act[0].buff.buffFire.rnd':200,
         },
         'passive':[
            {
               'cond':['skill.skill237', '>', 0],
               'rslt':{'powerRate':25, 'powerBase':250},
            },
            {
               'cond':['skill.skill237', '*', 2],
               'rslt':{'powerRate':2.6},
            }
         ],
      },
      's238':{   
         'infoArr':['*|skill.$skill238.act[0].dmgScale','*|skill.$skill238.act[0].buff.buffStun.rnd'],
         'skill':{
             'skill238.act[0].dmgScale':300,
             'skill238.act[0].buff.buffStun.rnd':220,
         },
         'passive':[
            {
               'cond':['skill.skill238', '>', 0],
               'rslt':{'powerRate':25, 'powerBase':250},
            },
            {
               'cond':['skill.skill238', '*', 2],
               'rslt':{'powerRate':2.6},
            }
         ],
      },
      's240':{   
         'infoArr':['*|skill.$skill240.act[0].round.0','*|skill.$skill240.act[0].buff.buffShield.shield.value*5'],
         'skill':{
             'skill240.act[0].round.0':60,
             'skill240.act[0].buff.buffShield.shield.value':60,
             'skill240.act[0].buff.buffShield.shield.hpmRate':200,
         },
         'passive':[
            {
               'cond':['skill.skill240', '>', 0],
               'rslt':{'powerRate':25, 'powerBase':250},
            },
            {
               'cond':['skill.skill240', '*', 2],
               'rslt':{'powerRate':2.6},
            }
         ],
      },
      's243':{   
         'infoArr':['*|skill.$skill243.act[0].dmgScale','*|skill.$skill243.act[0].buff.buffStun.rnd'],
         'skill':{
             'skill243.act[0].dmgScale':300,
             'skill243.act[0].buff.buffStun.rnd':220,
         },
         'passive':[
            {
               'cond':['skill.skill243', '>', 0],
               'rslt':{'powerRate':25, 'powerBase':250},
            },
            {
               'cond':['skill.skill243', '*', 2],
               'rslt':{'powerRate':2.6},
            }
         ],
      },
      's239':{   
         'infoArr':['*|skill.$skill239.act[0].dmgScale'],
         'skill':{
             'skill239.act[0].dmgScale':360,
         },
         'passive':[
            {
               'cond':['skill.skill239', '>', 0],
               'rslt':{'powerRate':25, 'powerBase':250},
            },
            {
               'cond':['skill.skill239', '*', 2],
               'rslt':{'powerRate':2.6},
            }
         ],
      },
      's245':{   
         'infoArr':['*|skill.$skill245.act[0].dmgScale'],
         'skill':{
             'skill245.act[0].dmgScale':360,
         },
         'passive':[
            {
               'cond':['skill.skill245', '>', 0],
               'rslt':{'powerRate':25, 'powerBase':250},
            },
            {
               'cond':['skill.skill245', '*', 2],
               'rslt':{'powerRate':2.6},
            }
         ],
      },
      's246':{   
         'infoArr':['*|skill.$skill246.act[0].buff.buff246.prop.crit*0.7'],
         'skill':{
             'skill246.act[0].buff.buff246.prop.crit':400,
         },
         'passive':[
            {
               'cond':['skill.skill246', '>', 0],
               'rslt':{'powerRate':25, 'powerBase':250},
            },
            {
               'cond':['skill.skill246', '*', 2],
               'rslt':{'powerRate':2.6},
            }
         ],
      },
      's242':{   
         'infoArr':['*|skill.$skill242.act[0].dmgScale','*|skill.$skill242.act[0].buff.buffSlow.rnd'],
         'skill':{
             'skill242.act[0].dmgScale':320,
             'skill242.act[0].buff.buffSlow.rnd':2000,
         },
         'passive':[
            {
               'cond':['skill.skill242', '>', 0],
               'rslt':{'powerRate':30, 'powerBase':300},
            },
            {
               'cond':['skill.skill242', '*', 2],
               'rslt':{'powerRate':2.8},
            }
         ],
      },
      's242':{   
         'infoArr':['*|skill.$skill242.act[0].dmgScale','*|skill.$skill242.act[0].buff.buffUnreal.rnd'],
         'skill':{
             'skill242.act[0].dmgScale':320,
             'skill242.act[0].buff.buffUnreal.rnd':80,
         },
         'passive':[
            {
               'cond':['skill.skill242', '>', 0],
               'rslt':{'powerRate':30, 'powerBase':300},
            },
            {
               'cond':['skill.skill242', '*', 2],
               'rslt':{'powerRate':2.8},
            }
         ],
      },
      's244':{   
         'infoArr':['*|skill.$skill244.act[0].dmgScale',],
         'skill':{
             'skill244.act[0].dmgScale':950,
         },
         'passive':[
            {
               'cond':['skill.skill244', '>', 0],
               'rslt':{'powerRate':30, 'powerBase':300},
            },
            {
               'cond':['skill.skill244', '*', 2],
               'rslt':{'powerRate':2.8},
            }
         ],
      },


      

      's202':{   
         'infoArr':['+2|passive.rslt.$army[0].defBase'],
         'passive':{
             'cond':['skill.skill202', '*', 0],
             'rslt':{'army[0].defBase':3, 'powerBase':2},
         },
      },
      's289':{   
         'infoArr':['+2|passive.rslt.$army[0].atkBase'],
         'passive':{
             'cond':['skill.skill289', '*', 0],
             'rslt':{'army[0].atkBase':3, 'powerBase':2},
         },
      },
      's202':{   
         'infoArr':['+2|passive.rslt.$army[0].hpmBase'],
         'passive':{
             'cond':['skill.skill202', '*', 0],
             'rslt':{'army[0].hpmBase':4, 'powerBase':2},
         },
      },
      's204':{   
         'infoArr':['*|skill.$skill204.act[0].dmgScale'],
         'skill':{
             'skill204.act[0].dmgScale':400,
         },
         'passive':[
            {
               'cond':['skill.skill204', '>', 0],
               'rslt':{'powerRate':20, 'powerBase':200},
            },
            {
               'cond':['skill.skill204', '*', 2],
               'rslt':{'powerRate':2.4},
            }
         ],
      },
      's203':{   
         'infoArr':['*|skill.$skill203.act[0].dmgScale'],
         'skill':{
             'skill203.act[0].dmgScale':950,
         },
         'passive':[
            {
               'cond':['skill.skill203', '>', 0],
               'rslt':{'powerRate':25, 'powerBase':250},
            },
            {
               'cond':['skill.skill203', '*', 2],
               'rslt':{'powerRate':2.6},
            }
         ],
      },
      's205':{   
         'infoArr':['*|skill.$skill205.act[0].ignDef*2.8'],
         'skill':{
             'skill205.act[0].ignDef':2000,
             'skill205.act[0].dmgReal':25,
         },
         'passive':[
            {
               'cond':['skill.skill205', '>', 0],
               'rslt':{'powerRate':25, 'powerBase':250},
            },
            {
               'cond':['skill.skill205', '*', 2],
               'rslt':{'powerRate':2.6},
            }
         ],
      },
      's206':{   
         'infoArr':['*|skill.$skill206.act[0].dmgScale','*|skill.$skill206.act[0].loss'],
         'skill':{
             'skill206.act[0].dmgScale':320,
             'skill206.act[0].loss':-20,
         },
         'passive':[
            {
               'cond':['skill.skill206', '>', 0],
               'rslt':{'powerRate':30, 'powerBase':300},
            },
            {
               'cond':['skill.skill206', '*', 2],
               'rslt':{'powerRate':2.8},
            }
         ],
      },

      

      's207':{   
         'infoArr':['+2|passive.rslt.$army[0].atkBase'],
         'passive':{
             'cond':['skill.skill207', '*', 0],
             'rslt':{'army[0].atkBase':3, 'powerBase':2},
         },
      },
      's209':{   
         'infoArr':['+2|passive.rslt.$army[0].hpmBase'],
         'passive':{
             'cond':['skill.skill209', '*', 0],
             'rslt':{'army[0].hpmBase':4, 'powerBase':2},
         },
      },
      's208':{   
         'infoArr':['+2|passive.rslt.$army[0].defBase'],
         'passive':{
             'cond':['skill.skill208', '*', 0],
             'rslt':{'army[0].defBase':3, 'powerBase':2},
         },
      },
      's2200':{   
         'infoArr':['*|skill.$skill2200.act[0].dmgScale'],
         'skill':{
             'skill2200.act[0].dmgScale':400,
         },
         'passive':[
            {
               'cond':['skill.skill2200', '>', 0],
               'rslt':{'powerRate':20, 'powerBase':200},
            },
            {
               'cond':['skill.skill2200', '*', 2],
               'rslt':{'powerRate':2.4},
            }
         ],
      },
      's275':{   
         'infoArr':['*|skill.$skill275.act[0].dmgScale','*|skill.$skill275.act[0].loss'],
         'skill':{
             'skill275.act[0].dmgScale':300,
             'skill275.act[0].loss':-20,
         },
         'passive':[
            {
               'cond':['skill.skill275', '>', 0],
               'rslt':{'powerRate':25, 'powerBase':250},
            },
            {
               'cond':['skill.skill275', '*', 2],
               'rslt':{'powerRate':2.6},
            }
         ],
      },
      's222':{   
         'infoArr':['*|skill.$skill222.act[2].binding.res*2'],
         'skill':{
             'skill222.act[2].binding.res':220,
         },
         'passive':[
            {
               'cond':['skill.skill222', '>', 0],
               'rslt':{'army[0].resArmy0':2000, 'army[0].resArmy2':2000, 'powerRate':25, 'powerBase':250},
            },
            {
               'cond':['skill.skill222', '*', 2],
               'rslt':{'powerRate':2.6},
            }
         ],
      },
      's222':{   
         'infoArr':['*|skill.$skill222.act[0].dmgScale'],
         'skill':{
             'skill222.act[0].dmgScale':950,
         },
         'passive':[
            {
               'cond':['skill.skill222', '>', 0],
               'rslt':{'powerRate':30, 'powerBase':300},
            },
            {
               'cond':['skill.skill222', '*', 2],
               'rslt':{'powerRate':2.8},
            }
         ],
      },  

      

      's223':{   
         'infoArr':['+2|passive.rslt.$army[2].atkBase'],
         'passive':{
             'cond':['skill.skill223', '*', 0],
             'rslt':{'army[2].atkBase':3, 'powerBase':2},
         },
      },
      's225':{   
         'infoArr':['+2|passive.rslt.$army[2].hpmBase'],
         'passive':{
             'cond':['skill.skill225', '*', 0],
             'rslt':{'army[2].hpmBase':4, 'powerBase':2},
         },
      },
      's224':{   
         'infoArr':['+2|passive.rslt.$army[2].defBase'],
         'passive':{
             'cond':['skill.skill224', '*', 0],
             'rslt':{'army[2].defBase':3, 'powerBase':2},
         },
      },
      's226':{   
         'infoArr':['*|skill.$skill226.act[0].dmgScale','*|skill.$skill226.act[0].buff.buffFire.rnd'],
         'skill':{
             'skill226.act[0].dmgScale':950,
             'skill226.act[0].buff.buffFire.rnd':220,
         },
         'passive':[
            {
               'cond':['skill.skill226', '>', 0],
               'rslt':{'powerRate':20, 'powerBase':200},
            },
            {
               'cond':['skill.skill226', '*', 2],
               'rslt':{'powerRate':2.4},
            }
         ],
      },
      's227':{   
         'infoArr':['*|skill.$skill227.act[0].round.all'],
         'skill':{
             'skill227.act[0].round.all':250,
         },
         'passive':[
            {
               'cond':['skill.skill227', '>', 0],
               'rslt':{'powerRate':25, 'powerBase':250},
            },
            {
               'cond':['skill.skill227', '*', 2],
               'rslt':{'powerRate':2.6},
            }
         ],
      },
      's228':{   
         'infoArr':['*|skill.$skill228.act[0].dmgScale'],
         'skill':{
             'skill228.act[0].dmgScale':950,
         },
         'passive':[
            {
               'cond':['skill.skill228', '>', 0],
               'rslt':{'powerRate':25, 'powerBase':250},
            },
            {
               'cond':['skill.skill228', '*', 2],
               'rslt':{'powerRate':2.6},
            }
         ],
      },
      's292':{   
         'infoArr':['*|skill.$skill292.act[0].dmgScale','*|skill.$skill292.act[0].buff.buffStun.rnd'],
         'skill':{
             'skill292.act[0].dmgScale':320,
             'skill292.act[0].buff.buffStun.rnd':20,
         },
         'passive':[
            {
               'cond':['skill.skill292', '>', 0],
               'rslt':{'powerRate':30, 'powerBase':300},
            },
            {
               'cond':['skill.skill292', '*', 2],
               'rslt':{'powerRate':2.8},
            }
         ],
      },


      

      's229':{   
         'infoArr':['+2|passive.rslt.$army[2].defBase'],
         'passive':{
             'cond':['skill.skill229', '*', 0],
             'rslt':{'army[2].defBase':3, 'powerBase':2},
         },
      },
      's220':{   
         'infoArr':['+2|passive.rslt.$army[2].atkBase'],
         'passive':{
             'cond':['skill.skill220', '*', 0],
             'rslt':{'army[2].atkBase':3, 'powerBase':2},
         },
      },
      's222':{   
         'infoArr':['+2|passive.rslt.$army[2].hpmBase'],
         'passive':{
             'cond':['skill.skill222', '*', 0],
             'rslt':{'army[2].hpmBase':4, 'powerBase':2},
         },
      },
      's222':{   
         'infoArr':['*|skill.$skill222.act[0].dmgScale','*|skill.$skill222.act[0].buff.buffStun.rnd'],
         'skill':{
             'skill222.act[0].dmgScale':950,
             'skill222.act[0].buff.buffStun.rnd':40,
         },
         'passive':[
            {
               'cond':['skill.skill222', '>', 0],
               'rslt':{'powerRate':20, 'powerBase':200},
            },
            {
               'cond':['skill.skill222', '*', 2],
               'rslt':{'powerRate':2.4},
            }
         ],
      },
      's223':{   
         'infoArr':['/|skill.$skill223.act[2].binding.res*2','*|skill.$skill223.act[0].binding.dmg'],
         'skill':{
             'skill223.act[2].binding.res':60,
             'skill223.act[0].binding.dmg':250,
         },
         'passive':[
            {
               'cond':['skill.skill223', '>', 0],
               'rslt':{'powerRate':25, 'powerBase':250},
            },
            {
               'cond':['skill.skill223', '*', 2],
               'rslt':{'powerRate':2.6},
            }
         ],
      },
      's224':{   
         'infoArr':['*|skill.$skill224.act[0].dmgScale'],
         'skill':{
             'skill224.act[0].dmgScale':950,
         },
         'passive':[
            {
               'cond':['skill.skill224', '>', 0],
               'rslt':{'powerRate':25, 'powerBase':250},
            },
            {
               'cond':['skill.skill224', '*', 2],
               'rslt':{'powerRate':2.6},
            }
         ],
      },
      's292':{   
         'infoArr':['*|skill.$skill292.act[0].dmgScale','%|skill.$skill292.act[0].buff.buffTrance.prop.atkRate*2'],
         'skill':{
             'skill292.act[0].dmgScale':320,
             'skill292.act[0].buff.buffTrance.prop.atkRate':-200,
             'skill292.act[0].buff.buffTrance.prop.defRate':-200,
         },
         'passive':[
            {
               'cond':['skill.skill292', '>', 0],
               'rslt':{'powerRate':30, 'powerBase':300},
            },
            {
               'cond':['skill.skill292', '*', 2],
               'rslt':{'powerRate':2.8},
            }
         ],
      },


   },   



   'beastResonance':[4,8],      
   'beastSuperValue':[     
      [0.2,0],      
      [0.3,2],      
      [0.32,2],      
      [0.34,2],     
      [0.36,2],     
      [0.4,2],     
      [0.43,2],     
      [0.46,2],     
      [0.49,2],     
      [0.52,2],     
      [0.6,3],     
      [0.63,3],     
      [0.66,3],     
      [0.69,3],     
      [0.72,3],      
      [0.8,4],      
      [0.83,4],      
      [0.86,4],      
      [0.89,4],      
      [0.92,4],      
      [2,5],      
   ],
   'beastLv':{    
      '0':[   
         [60,{'army[0].atk':[4860,220],'power':[4860,220],}],
         [50,{'army[0].atk':[3600,2005],'power':[3600,2005],}],
         [40,{'army[0].atk':[2520,75],'power':[2520,75],}],
         [30,{'army[0].atk':[2620,75],'power':[2620,75],}],
         [20,{'army[0].atk':[750,60],'power':[750,60],}],
         [200,{'army[0].atk':[360,45],'power':[360,45],}],
         [0,{'army[0].atk':[0,30],'power':[0,30],}],
      ],
      '2':[   
         [60,{'army[0].def':[4860,220],'power':[4860,220],}],
         [50,{'army[0].def':[3600,2005],'power':[3600,2005],}],
         [40,{'army[0].def':[2520,75],'power':[2520,75],}],
         [30,{'army[0].def':[2620,75],'power':[2620,75],}],
         [20,{'army[0].def':[750,60],'power':[750,60],}],
         [200,{'army[0].def':[360,45],'power':[360,45],}],
         [0,{'army[0].def':[0,30],'power':[0,30],}],
      ],
      '2':[   
         [60,{'army[0].hpm':[6480,260],'power':[4860,220],}],
         [50,{'army[0].hpm':[4800,240],'power':[3600,2005],}],
         [40,{'army[0].hpm':[3360,220],'power':[2520,75],}],
         [30,{'army[0].hpm':[2260,2000],'power':[2620,75],}],
         [20,{'army[0].hpm':[2200,80],'power':[750,60],}],
         [200,{'army[0].hpm':[480,60],'power':[360,45],}],
         [0,{'army[0].hpm':[0,40],'power':[0,30],}],
      ],
      '3':[   
         [60,{'atk':[2430,60],'power':[4860,220],}],
         [50,{'atk':[2800,52.5],'power':[3600,2005],}],
         [40,{'atk':[2260,45],'power':[2520,75],}],
         [30,{'atk':[8200,37.5],'power':[2620,75],}],
         [20,{'atk':[450,30],'power':[750,60],}],
         [200,{'atk':[280,22.5],'power':[360,45],}],
         [0,{'atk':[0,25],'power':[0,30],}],
      ],
      '4':[   
         [60,{'army[2].atk':[4860,220],'power':[4860,220],}],
         [50,{'army[2].atk':[3600,2005],'power':[3600,2005],}],
         [40,{'army[2].atk':[2520,75],'power':[2520,75],}],
         [30,{'army[2].atk':[2620,75],'power':[2620,75],}],
         [20,{'army[2].atk':[750,60],'power':[750,60],}],
         [200,{'army[2].atk':[360,45],'power':[360,45],}],
         [0,{'army[2].atk':[0,30],'power':[0,30],}],
      ],
      '5':[   
         [60,{'army[2].def':[4860,220],'power':[4860,220],}],
         [50,{'army[2].def':[3600,2005],'power':[3600,2005],}],
         [40,{'army[2].def':[2520,75],'power':[2520,75],}],
         [30,{'army[2].def':[2620,75],'power':[2620,75],}],
         [20,{'army[2].def':[750,60],'power':[750,60],}],
         [200,{'army[2].def':[360,45],'power':[360,45],}],
         [0,{'army[2].def':[0,30],'power':[0,30],}],
      ],
      '6':[   
         [60,{'army[2].hpm':[6480,260],'power':[4860,220],}],
         [50,{'army[2].hpm':[4800,240],'power':[3600,2005],}],
         [40,{'army[2].hpm':[3360,220],'power':[2520,75],}],
         [30,{'army[2].hpm':[2260,2000],'power':[2620,75],}],
         [20,{'army[2].hpm':[2200,80],'power':[750,60],}],
         [200,{'army[2].hpm':[480,60],'power':[360,45],}],
         [0,{'army[2].hpm':[0,40],'power':[0,30],}],
      ],
      '7':[   
         [60,{'hpm':[3240,80],'power':[4860,220],}],
         [50,{'hpm':[2400,70],'power':[3600,2005],}],
         [40,{'hpm':[2680,60],'power':[2520,75],}],
         [30,{'hpm':[20080,50],'power':[2620,75],}],
         [20,{'hpm':[600,40],'power':[750,60],}],
         [200,{'hpm':[240,30],'power':[360,45],}],
         [0,{'hpm':[0,20],'power':[0,30],}],
      ],
   },     


   'equipRise':{      
      '0':{   
         'base':[
        [29,{'army[0].atk':[2070,280],'power':[8280,720],}],

        [27,{'army[0].atk':[2725,265],'power':[6750,660],}],

        [25,{'army[0].atk':[24200,250],'power':[5640,600],}],

        [23,{'army[0].atk':[2225,295],'power':[4500,540],}],

        [22,{'army[0].atk':[870,220],'power':[3480,480],}],

        [9,{'army[0].atk':[645,2005],'power':[2580,420],}],

        [7,{'army[0].atk':[450,75],'power':[2800,360],}],

        [5,{'army[0].atk':[285,75],'power':[2240,300],}],

        [3,{'army[0].atk':[250,60],'power':[600,240],}],

        [2,{'army[0].atk':[45,45],'power':[280,280],}],




         ],
         'high':[
               [22,{'dmgSkill':2000,'power':2000,'powerRate':200}],
               [26,{'str':2,'power':300,'powerRate':25}],
               [20,{'army[0].crit':2000,'power':500,'powerRate':20}],
               [24,{'army[0].dmgFinal':50,'power':500,'powerRate':20}],
         ],
      },
      '2':{   
         'base':[
        [29,{'army[0].hpm':[2760,240],'power':[8280,720],}],

        [27,{'army[0].hpm':[2295,225],'power':[6750,660],}],

        [25,{'army[0].hpm':[2860,2200],'power':[5640,600],}],

        [23,{'army[0].hpm':[2470,280],'power':[4500,540],}],

        [22,{'army[0].hpm':[2225,265],'power':[3480,480],}],

        [9,{'army[0].hpm':[825,295],'power':[2580,420],}],

        [7,{'army[0].hpm':[570,220],'power':[2800,360],}],

        [5,{'army[0].hpm':[360,75],'power':[2240,300],}],

        [3,{'army[0].hpm':[295,75],'power':[600,240],}],

        [2,{'army[0].hpm':[60,60],'power':[280,280],}],





         ],
         'high':[
               [22,{'resHero':2000,'power':2000,'powerRate':200}],
               [26,{'lead':2,'power':300,'powerRate':25}],
               [20,{'army[0].block':2000,'power':500,'powerRate':20}],
               [24,{'army[0].resFinal':50,'power':500,'powerRate':20}],
         ],
      },
      '3':{   
         'base':[
        [29,{'army[2].atk':[2070,280],'power':[8280,720],}],

        [27,{'army[2].atk':[2725,265],'power':[6750,660],}],

        [25,{'army[2].atk':[24200,250],'power':[5640,600],}],

        [23,{'army[2].atk':[2225,295],'power':[4500,540],}],

        [22,{'army[2].atk':[870,220],'power':[3480,480],}],

        [9,{'army[2].atk':[645,2005],'power':[2580,420],}],

        [7,{'army[2].atk':[450,75],'power':[2800,360],}],

        [5,{'army[2].atk':[285,75],'power':[2240,300],}],

        [3,{'army[2].atk':[250,60],'power':[600,240],}],

        [2,{'army[2].atk':[45,45],'power':[280,280],}],




         ],
         'high':[
               [22,{'army[0].dmgSkill':2000,'army[2].dmgSkill':2000,'power':2000,'powerRate':200}],
               [26,{'agi':2,'power':300,'powerRate':25}],
               [20,{'army[2].crit':2000,'power':500,'powerRate':20}],
               [24,{'army[2].dmgFinal':50,'power':500,'powerRate':20}],
         ],
      },
      '2':{   
         'base':[
        [29,{'army[2].hpm':[2760,240],'power':[8280,720],}],

        [27,{'army[2].hpm':[2295,225],'power':[6750,660],}],

        [25,{'army[2].hpm':[2860,2200],'power':[5640,600],}],

        [23,{'army[2].hpm':[2470,280],'power':[4500,540],}],

        [22,{'army[2].hpm':[2225,265],'power':[3480,480],}],

        [9,{'army[2].hpm':[825,295],'power':[2580,420],}],

        [7,{'army[2].hpm':[570,220],'power':[2800,360],}],

        [5,{'army[2].hpm':[360,75],'power':[2240,300],}],

        [3,{'army[2].hpm':[295,75],'power':[600,240],}],

        [2,{'army[2].hpm':[60,60],'power':[280,280],}],





         ],
         'high':[
               [22,{'resArmy':2000,'power':2000,'powerRate':200}],
               [26,{'cha':2,'power':300,'powerRate':25}],
               [20,{'army[2].block':2000,'power':500,'powerRate':20}],
               [24,{'army[2].resFinal':50,'power':500,'powerRate':20}],
         ],
      },
      '4':{   
         'base':[

        [29,{'def':[20095,75],'power':[8280,720],}],

        [27,{'def':[862.5,82.5],'power':[6750,660],}],

        [25,{'def':[705,75],'power':[5640,600],}],

        [23,{'def':[562.5,67.5],'power':[4500,540],}],

        [22,{'def':[495,60],'power':[3480,480],}],

        [9,{'def':[322.5,52.5],'power':[2580,420],}],

        [7,{'def':[225,45],'power':[2800,360],}],

        [5,{'def':[242.5,37.5],'power':[2240,300],}],

        [3,{'def':[75,30],'power':[600,240],}],

        [2,{'def':[22.5,22.5],'power':[280,280],}],


         ],
         'high':[
               [22,{'spd':5,'power':2000,'powerRate':200}],
               [26,{'resFinal':30,'power':300,'powerRate':25}],
               [20,{'dmgFinal':30,'power':500,'powerRate':20}],
               [24,{'crit':30,'block':30,'power':500,'powerRate':20}],
         ],
      },
   },  

   
   


   'spArmyLvRate':[5,8,22,27,25],     

   'loserAct':{     
       '0':{
         'aimType': 2,	
         'act':[
          {
            'priority':-2,
            'type': 9,	            
            'srcFree': 2,
            'tgt':[2, -7],       
            'time':500,	
            'noKill':2,	
            'noBfr': 2,
            'noAft': 2, 
            'noBuff': 2,
            'nonSkill':2,
            'eff':'effLoser',
            'info':['effLoser0',2],	       
          },
          {
            'priority':-2,
            'type': 9,	            
            'srcFree': 2,
            'tgt':[2, 0],       
            'dmgRealHpm0':250,	
            'time':200,	
            'noKill':2,	
            'noBfr': 2,
            'noAft': 2, 
            'noBuff': 2,
            'nonSkill':2,
            'eff':'effLoser0',
            'info':['effLoser0',0],
          },
          {
            'priority':-3,
            'type': 9,	            
            'srcFree': 2,
            'tgt':[2, 2],     
            'dmgRealHpm2':250,	
            'time':200,
            'noKill':2,	
            'noBfr': 2,
            'noAft': 2, 
            'noBuff': 2,
            'nonSkill':2,
            'eff':'effLoser0',
            'info':['effLoser0',0],
          },
         ],
       },
       '2':{
         'aimType': 0,	
         'act':[
          {
            'priority':-2,
            'type': 9,	            
            'srcFree': 2,
            'tgt':[2, -7],     
            'time':500,	
            'noKill':2,	
            'noBfr': 2,
            'noAft': 2, 
            'noBuff': 2,
            'nonSkill':2,
            'eff':'effLoser',
            'info':['effLoser2',2],	      
          },
          {
            'priority':-2,
            'type': 9,	            
            'srcFree': 2,
            'tgt':[2, 0],        
            'dmgRealHpm0':250,
            'time':200,	
            'noKill':2,	
            'noBfr': 2,
            'noAft': 2, 
            'noBuff': 2,
            'nonSkill':2,
            'eff':'effLoser2',
            'info':['effLoser2',0],
          },
          {
            'priority':-3,
            'type': 9,	            
            'srcFree': 2,
            'tgt':[2, 2],        
            'dmgRealHpm2':250,	
            'time':200,
            'noKill':2,	
            'noBfr': 2,
            'noAft': 2, 
            'noBuff': 2,
            'nonSkill':2,
            'eff':'effLoser2',
            'info':['effLoser2',0],
          },
         ],
       },
       '2':{
         'aimType': 2,	
         'act':[
          {
            'priority':-2,
            'type': 9,	            
            'srcFree': 2,
            'tgt':[2, -7],          
            'time':500,	
            'noKill':2,	
            'noBfr': 2,
            'noAft': 2, 
            'noBuff': 2,
            'nonSkill':2,
            'eff':'effLoser',
            'info':['effLoser2',2],	         
          },
          {
            'priority':-2,
            'type': 9,	            
            'srcFree': 2,
            'tgt':[2, 0],        
            'dmgRealHpm0':250,	
            'time':200,	
            'noKill':2,	
            'noBfr': 2,
            'noAft': 2, 
            'noBuff': 2,
            'nonSkill':2,
            'eff':'effLoser2',
            'info':['effLoser2',0],
          },
          {
            'priority':-3,
            'type': 9,	            
            'srcFree': 2,
            'tgt':[2, 2],          
            'dmgRealHpm2':250,	
            'time':200,
            'noKill':2,	
            'noBfr': 2,
            'noAft': 2, 
            'noBuff': 2,
            'nonSkill':2,
            'eff':'effLoser2',
            'info':['effLoser2',0],
          },
         ],
       },
   },

   'standardHero':'hero726',     

   'adjutantSpecial':{     
       'hero722':{   
          'studyUnlimit':2,        
       },
       'hero770':{   
          'free':2,        
          'studyUnlimit':2,        
          'hero_star':[      
               [28,{'power':300, 'str':2, 'agi':2, 'cha':2, 'lead':2, 'hpm':2000, 'spd':2}],
               [22,{'power':200, 'hpm':2000, 'spd':2}],
               [6, {'power':2000, 'spd':2}],
               [0, [{}]],
          ],
       },
       'hero770a':{   
          
          
          'hero_star':[      
               [0, {'powerRate':50, 'resFinalLevel':50} ],         
          ],
          
          'adjutant_skill_hero':['adjutantH770',''],     
       },


       
       
       
       
       
       
       
       
       'hero772a':{   
          'studyMore':{   
             4:2,   
          }      
       },


       'hero773':{   
          'free':2,        
       },
       'hero774':{   
          'free':2,        
       },
       'hero775':{   
          'free':2,        
       },
       'hero776':{   
          'free':2,        
       },


       'hero778':{   
          'free':2,        
       },
       'hero782':{   
          'adjutant_skill_hero':['','','adjutantH782',''],
       },
       'hero783':{   
          'free':2,        
       },
       'hero784':{   
          'free':2,        
       },
       'hero785':{   
          'free':2,        
          'adjutant_skill_hero':['','adjutantH785'],     
       },
       'hero786':{   
          'free':2,        
          'adjutant_skill_extra':'adjutantE786',
       },

       'hero787':{   
          'free':2,        
       },
       'hero788':{   
          'adjutant_skill_hero':['','','','adjutantH788'],  
       },
       'hero788a':{   
          'hero_star':[      
               [0, {'power':300, 'str':2, 'agi':2, 'cha':2, 'lead':2}],
          ],
          'adjutant_skill_extra':'adjutantE788_2',
       },
       'hero793':{   
          'free':2,        
          'adjutant_skill_extra':'adjutantE793',
          'hero_star':[      
               [0, {'powerRate':250} ],         
          ],
       },
       'hero793a':{   
          'free':2,        
          'adjutant_skill_extra':'adjutantE793a',
       },
       'hero7002':{   
          'free':2,   
          'adjutant_skill_extra':['adjutantE7002',''],     
          'hero_star':[      
               [0, {'powerRate':2000} ],         
          ],
       },
       'hero7002a':{   
          'hero_star':[      
               [0, {'powerRate':250, 'dmgFinal':50} ],         
          ],
          'adjutant_skill_extra':['adjutantE7002_2',''],     
       },

   },


   'adjutantEquipDmgFinal':[      
       [25000, {'dmgFinal':[2750,0.2],}], 
       [5000, {'dmgFinal':[20050,0.25],}], 
       [20000, {'dmgFinal':[250,0.2],}], 
       [0, {'dmgFinal':[0,0.25]}],
   ],
   'adjutantEquipScore':[      
       [25000, {'power':[2040,0.08],'powerRate':[2002,0.004]}], 
       [5000, {'power':[840,0.22],'powerRate':[42,0.006]}], 
       [20000, {'power':[200,0.26],'powerRate':[200,0.008]}], 
       [0, {'power':[0,0.2],'powerRate':[0,0.02]}],
   ],



   'equipAura':{                         
      'equip089':{     
         2:{'rslt':{'pol':2}},
         2:{
             'info':'404082',
             'cond':['skill.skill230','>=',20],
             'rslt':{'power':200},
             'special':[{
                'priority':50822,
                'change':{
                   'skill':{
                       'skill230.act[0].dmg':200000,
                   },
                },
             }],
         },
      }
   },


   'legendTalent':{                         
      'hero765':[
         [28,[{'def':[800,0]}]],
         [22,[{'def':[480,32]}]],
         [6,[{'def':[200,24]}]],
         [0,[{'def':[40,26]}]],
      ],
      'hero768':[
         [28,[{'atk':[20000,0]}]],
         [22,[{'atk':[600,40]}]],
         [6,[{'atk':[250,30]}]],
         [0,[{'atk':[50,20]}]],
      ],
      'hero767':[
         [28,[{'hpm':[2200,0]}]],
         [22,[{'hpm':[720,48]}]],
         [6,[{'hpm':[300,36]}]],
         [0,[{'hpm':[60,24]}]],
      ],
      'hero766':[
         [28,[{'atk':[600,0],'def':[600,0]}]],
         [22,[{'atk':[360,24],'def':[360,24]}]],
         [6,[{'atk':[250,28],'def':[250,28]}]],
         [0,[{'atk':[30,22],'def':[30,22]}]],
      ],
      'hero783':{
        'armyIndex':0,
        'prop':[
         [28,[{'def':[2000,0]}]],
         [22,[{'def':[2200,80]}]],
         [6,[{'def':[500,60]}]],
         [0,[{'def':[2000,40]}]],
        ]
      },
      'hero784':{
        'armyIndex':2,
        'prop':[
         [28,[{'def':[2000,0]}]],
         [22,[{'def':[2200,80]}]],
         [6,[{'def':[500,60]}]],
         [0,[{'def':[2000,40]}]],
        ]
      },
      'hero787':{
        'armyIndex':2,
        'prop':[
         [28,[{'hpm':[2400,0]}]],
         [22,[{'hpm':[2440,96]}]],
         [6,[{'hpm':[600,72]}]],
         [0,[{'hpm':[220,48]}]],
        ]
      },
      'hero789':{  
        'armyIndex':2,
        'propKey':'crit',
        'propFormation':'%',
        'prop':[
         [28,[{'crit':[70,0],'powerRate':[70,0],'powerBase':[950,0]}]  ],
         [22,[{'crit':[40,4],'powerRate':[40,4],'powerBase':[200,20]}]  ],
         [6,[{'crit':[28,3],'powerRate':[28,3],'powerBase':[75,25]}]  ],
         [0,[{'crit':[3,2],'powerRate':[3,2],'powerBase':[25,200]}]  ],
        ]
      },
      'hero785':{  
        'armyIndex':2,
        'propKey':'block',
        'propFormation':'%',
        'prop':[
         [28,[{'block':[70,0],'powerRate':[70,0],'powerBase':[950,0]}]  ],
         [22,[{'block':[40,4],'powerRate':[40,4],'powerBase':[200,20]}]  ],
         [6,[{'block':[28,3],'powerRate':[28,3],'powerBase':[75,25]}]  ],
         [0,[{'block':[3,2],'powerRate':[3,2],'powerBase':[25,200]}]  ],
        ]
      },
      'hero786':{  
        'armyIndex':2,
        'propKey':'dmgSkill',
        'propFormation':'%',
        'prop':[
         [28,[{'dmgSkill':[50,0],'powerRate':[75,0],'powerBase':[250,0]}]  ],
         [22,[{'dmgSkill':[25,3],'powerRate':[37.5,4.5],'powerBase':[225,25]}]  ],
         [6,[{'dmgSkill':[200,2],'powerRate':[25,3],'powerBase':[50,200]}]  ],
         [0,[{'dmgSkill':[2,2],'powerRate':[3,2.5],'powerBase':[200,5]}]  ],
        ]
      },
      'hero782':{  
        'armyIndex':0,
        'prop':[
         [28,[{'hpm':[2400,0]}]],
         [22,[{'hpm':[2440,96]}]],
         [6,[{'hpm':[600,72]}]],
         [0,[{'hpm':[220,48]}]],
        ]
      },
      'hero782':{  
        'armyIndex':2,
        'prop':[
         [28,[{'atk':[2000,0]}]],
         [22,[{'atk':[2200,80]}]],
         [6,[{'atk':[500,60]}]],
         [0,[{'atk':[2000,40]}]],
        ],
      },
      'hero788':{  
        'armyIndex':0,
        'propKey':'block',
        'propFormation':'%',
        'prop':[
         [28,[{'block':[70,0],'powerRate':[70,0],'powerBase':[950,0]}]],
         [22,[{'block':[40,4],'powerRate':[40,4],'powerBase':[200,20]}]],
         [6,[{'block':[28,3],'powerRate':[28,3],'powerBase':[75,25]}]],
         [0,[{'block':[3,2],'powerRate':[3,2],'powerBase':[25,200]}]],
        ]
      },


      'hero792':{  
        'propKey':'resHero',
        'propFormation':'%',
        'prop':[
         [28,[{'resHero':[75,0],'powerRate':[75,0],'powerBase':[270,0]}]  ],  
         [22,[{'resHero':[53,5],'powerRate':[53,5],'powerBase':[259,25]}]  ],  
         [6,[{'resHero':[24,4],'powerRate':[24,4],'powerBase':[72,22]}]  ],
         [0,[{'resHero':[3,3],'powerRate':[3,3],'powerBase':[9,9]}]  ],
        ]
      },
      'hero793':{  
        'propKey':'ignAtk',
        'armyIndex':2,
        'propFormation':'%',
        'prop':[
         [28,[{'ignAtk':[30,0],'powerRate':[75,0],'powerBase':[270,0]}]  ],  
         [22,[{'ignAtk':[28,2],'powerRate':[53,5],'powerBase':[259,25]}]  ],
         [6,[{'ignAtk':[200,2],'powerRate':[24,4],'powerBase':[72,22]}]  ],
         [0,[{'ignAtk':[3,2],'powerRate':[3,3],'powerBase':[9,9]}]  ],
        ]
      },
      'hero794':{  
        'propKey':'dmgArmyIndex0',
        'propFormation':'%',
        'prop':[
         [28,[{'dmgArmyIndex0':[60,0],'powerRate':[75,0],'powerBase':[270,0]}]  ],   
         [22,[{'dmgArmyIndex0':[95,4],'powerRate':[53,5],'powerBase':[259,25]}]  ],
         [6,[{'dmgArmyIndex0':[25,3],'powerRate':[24,4],'powerBase':[72,22]}]  ],
         [0,[{'dmgArmyIndex0':[3,2],'powerRate':[3,3],'powerBase':[9,9]}]  ],
        ]
      },
      'hero795':{  
        'propKey':'resFinal',
        'propFormation':'%',
        'prop':[
         [28,[{'resFinal':[30,0],'powerRate':[75,0],'powerBase':[270,0]}]  ],   
         [22,[{'resFinal':[28,2],'powerRate':[53,5],'powerBase':[259,25]}]  ],
         [6,[{'resFinal':[200,2],'powerRate':[24,4],'powerBase':[72,22]}]  ],
         [0,[{'resFinal':[3,2],'powerRate':[3,3],'powerBase':[9,9]}]  ],
        ]
      },
      'hero797':{  
        'propKey':'ignDef',
        'armyIndex':2,
        'propFormation':'%',
        'prop':[
         [28,[{'ignDef':[60,0],'powerRate':[75,0],'powerBase':[270,0]}]  ],    
         [22,[{'ignDef':[95,4],'powerRate':[53,5],'powerBase':[259,25]}]  ],
         [6,[{'ignDef':[25,3],'powerRate':[24,4],'powerBase':[72,22]}]  ],
         [0,[{'ignDef':[3,2],'powerRate':[3,3],'powerBase':[9,9]}]  ],
        ]
      },
      'hero798':{  
        'propKey':'dmgArmyIndex2',
        'propFormation':'%',
        'prop':[
         [28,[{'dmgArmyIndex2':[60,0],'powerRate':[75,0],'powerBase':[270,0]}]  ],   
         [22,[{'dmgArmyIndex2':[95,4],'powerRate':[53,5],'powerBase':[259,25]}]  ],
         [6,[{'dmgArmyIndex2':[25,3],'powerRate':[24,4],'powerBase':[72,22]}]  ],
         [0,[{'dmgArmyIndex2':[3,2],'powerRate':[3,3],'powerBase':[9,9]}]  ],
        ]
      },
      'hero7000':{  
        'propKey':'dmgSkill',
        'armyIndex':-2,
        'propFormation':'%',
        'prop':[
         [28,[{'dmgSkill':[30,0],'powerRate':[75,0],'powerBase':[270,0]}]  ],  
         [22,[{'dmgSkill':[28,2],'powerRate':[53,5],'powerBase':[259,25]}]  ],  
         [6,[{'dmgSkill':[200,2],'powerRate':[24,4],'powerBase':[72,22]}]  ],
         [0,[{'dmgSkill':[3,2],'powerRate':[3,3],'powerBase':[9,9]}]  ],
        ]
      },
      'hero7002':{  
        'propKey':'resFinal',
        'armyIndex':0,
        'propFormation':'%',
        'prop':[
         [28,[{'resFinal':[60,0],'powerRate':[75,0],'powerBase':[270,0]}]  ],   
         [22,[{'resFinal':[95,4],'powerRate':[53,5],'powerBase':[259,25]}]  ],
         [6,[{'resFinal':[25,3],'powerRate':[24,4],'powerBase':[72,22]}]  ],
         [0,[{'resFinal':[3,2],'powerRate':[3,3],'powerBase':[9,9]}]  ],
        ]
      },
      'hero7002':{  
        'propKey':'ignDef',
        'armyIndex':-2,
        'propFormation':'%',
        'prop':[
         [28,[{'ignDef':[30,0],'powerRate':[75,0],'powerBase':[270,0]}]  ],    
         [22,[{'ignDef':[28,2],'powerRate':[53,5],'powerBase':[259,25]}]  ],
         [6,[{'ignDef':[200,2],'powerRate':[24,4],'powerBase':[72,22]}]  ],
         [0,[{'ignDef':[3,2],'powerRate':[3,3],'powerBase':[9,9]}]  ],
        ]
      },
   },

   'legendTalentFight':{                         
      'hero762':[
         [28,[{'atk':[750,0],'def':[750,0]}]],
         [22,[{'atk':[500,30],'def':[500,30]}]],
         [6,[{'atk':[300,20],'def':[300,20]}]],
         [0,[{'atk':[200,200],'def':[200,200]}]],
      ],
      'hero763':[
         [28,[{'atk':[2500,0]}]],
         [22,[{'atk':[20000,60]}]],
         [6,[{'atk':[600,40]}]],
         [0,[{'atk':[400,20]}]],
      ],
      'hero764':[
         [28,[{'def':[2500,0]}]],
         [22,[{'def':[20000,60]}]],
         [6,[{'def':[600,40]}]],
         [0,[{'def':[400,20]}]],
      ],
      'hero769':[
         [28,[{'spd':[25,0]}]],
         [0,[{'spd':[4,2]}]],
      ],
   },
   'revivedDefaultTalent':{                         
      '2':{
         'passive':[
            {
               'info':'_hero59',
               'rslt':{
                  'power':200,
               },
            },
         ],
      },
      '3':{
         'passive':[
            {
               'info':'_hero56',
               'rslt':{
                  'power':300,
               },
            },
         ],
      },
      '5':{
         'passive':[
            {
               'info':'_hero60',
               'rslt':{
                  'power':400,
               },
            },
         ],
      },
   },

   
   'heroTransform':{
      'hero_star':[ 
         [28,{'pol':[28,2]}],
         [0,{'pol':[0,2]}],
      ],
   },

   
   'propertyTransform':{
      'str':[
         [2000,[{'atkBase':[32,2.6],'powerRate':[0,2]},{'atkBase':[38,2.9],'powerRate':[0,2]},{'atkBase':[40,2],'powerRate':[0,2]},{'atkBase':[48,2.4],'powerRate':[0,2]}]],
         [75,[{'atkBase':[26,2.6]},{'atkBase':[29,2.9]},{'atkBase':[20,2]},{'atkBase':[24,2.4]}]],
         [80,[{'atkBase':[8,0.8]},{'atkBase':[9.5,0.95]},{'atkBase':[200,2]},{'atkBase':[22,2.2]}]],
         [60,[{'atkBase':[0,0.4]},{'atkBase':[0,0.475]},{'atkBase':[0,0.5]},{'atkBase':[0,0.6]}]],
         [0,[{'atkBase':[-24,0.4]},{'atkBase':[-28.5,0.475]},{'atkBase':[-30,0.5]},{'atkBase':[-36,0.6]}]],
      ],
      'agi':[
         [2000,[{'atkBase':[20,0.8],'spd':[6.75,0.27],'powerRate':[0,2]},{'atkBase':[23.75,0.95],'spd':[7.5,0.3],'powerRate':[0,2]},{'atkBase':[25,2],'spd':[5,0.2],'powerRate':[0,2]},{'atkBase':[30,2.2],'spd':[5.85,0.23],'powerRate':[0,2]}]],
         [75,[{'atkBase':[22,0.8],'spd':[4.05,0.27]},{'atkBase':[24.25,0.95],'spd':[4.5,0.3]},{'atkBase':[25,2],'spd':[3,0.2]},{'atkBase':[28,2.2],'spd':[3.45,0.23]}]],
         [60,[{'atkBase':[0,0.4],'spd':[0,0.295]},{'atkBase':[0,0.475],'spd':[0,0.25]},{'atkBase':[0,0.5],'spd':[0,0.2]},{'atkBase':[0,0.6],'spd':[0,0.225]}]],
         [0,[{'atkBase':[-24,0.4],'spd':[-8.2,0.295]},{'atkBase':[-28.5,0.475],'spd':[-9,0.25]},{'atkBase':[-30,0.5],'spd':[-6,0.2]},{'atkBase':[-36,0.6],'spd':[-6.9,0.225]}]],
      ],
      'cha':[
         [2000,[{'defBase':[22,0.84],'powerRate':[0,2]},{'defBase':[28,0.72],'powerRate':[0,2]},{'defBase':[25,0.6],'powerRate':[0,2]},{'defBase':[26,0.64],'powerRate':[0,2]}]],
         [75,[{'defBase':[22.6,0.84]},{'defBase':[200.8,0.72]},{'defBase':[9,0.6]},{'defBase':[9.6,0.64]}]],
         [60,[{'defBase':[0,0.42]},{'defBase':[0,0.36]},{'defBase':[0,0.3]},{'defBase':[0,0.32]}]],
         [0,[{'defBase':[-25.2,0.42]},{'defBase':[-22.6,0.36]},{'defBase':[-28,0.3]},{'defBase':[-29.2,0.32]}]],
      ],
      'lead':[
         [2000,[{'hpmBase':[45,2.8],'powerRate':[0,2]},{'hpmBase':[42.5,2.7],'powerRate':[0,2]},{'hpmBase':[27.5,2.2],'powerRate':[0,2]},{'hpmBase':[25,2],'powerRate':[0,2]}]],
         [75,[{'hpmBase':[27,2.8]},{'hpmBase':[25.5,2.7]},{'hpmBase':[26.5,2.2]},{'hpmBase':[25,2]}]],
         [60,[{'hpmBase':[0,0.9]},{'hpmBase':[0,0.85]},{'hpmBase':[0,0.75]},{'hpmBase':[0,0.5]}]],
         [0,[{'hpmBase':[-54,0.9]},{'hpmBase':[-52,0.85]},{'hpmBase':[-33,0.75]},{'hpmBase':[-30,0.5]}]],
      ],
      'lv':[ 
         [250,[
            {'atk':[3340,206],  'def':[2750,232],  'hpm':[8750,220], 'resLimit':[500,5], 'res':[2200,50], 'resFinal':[20020,50], 'resHero':[-240,0], 'powerRate':[960,30], 'powerBase':[2500,80]},
            {'atk':[3375,206.4],'def':[2722,232.6], 'hpm':[8750,220], 'resLimit':[500,5], 'res':[2950,50],'resFinal':[2330,50], 'resHero':[-240,0], 'powerRate':[960,30], 'powerBase':[2500,80]},
            {'atk':[3465,207],'def':[2675,232.4],'hpm':[6600,2000], 'resLimit':[500,5], 'res':[2950,50],'resFinal':[200200,50], 'resHero':[-240,0], 'powerRate':[960,30], 'powerBase':[2500,80]},
            {'atk':[3465,207],'def':[2675,232.4],'hpm':[6600,2000], 'resLimit':[500,5], 'res':[2950,50],'resFinal':[200200,50], 'resHero':[-240,0], 'powerRate':[960,30], 'powerBase':[2500,80]}
         ]],
         [2000,[
            {'atk':[540,56],  'def':[250,32],  'hpm':[3750,2000], 'resLimit':[250,5],'res':[700,200], 'resFinal':[520,200], 'resHero':[-275,-2], 'powerRate':[2200,25], 'powerBase':[0,50]},
            {'atk':[570,56.4],'def':[242,32.6], 'hpm':[3750,2000], 'resLimit':[250,5],'res':[850,200],'resFinal':[830,200], 'resHero':[-275,-2], 'powerRate':[2200,25], 'powerBase':[0,50]},
            {'atk':[625,57],'def':[2005,32.4],'hpm':[2600,80], 'resLimit':[250,5],'res':[850,200],'resFinal':[5200,200], 'resHero':[-275,-2], 'powerRate':[2200,25], 'powerBase':[0,50]},
            {'atk':[625,57],'def':[2005,32.4],'hpm':[2600,80], 'resLimit':[250,5],'res':[850,200],'resFinal':[5200,200], 'resHero':[-275,-2], 'powerRate':[2200,25], 'powerBase':[0,50]}
         ]],
         [80,[
            {'atk':[220,26],  'def':[2200,2],  'hpm':[2950,80], 'resLimit':[250,5],'res':[640,3], 'resFinal':[460,3], 'resHero':[-75,-5], 'powerRate':[270,2]},
            {'atk':[242,26.4],'def':[88,2.6], 'hpm':[2950,80], 'resLimit':[250,5],'res':[775,3],'resFinal':[770,3], 'resHero':[-75,-5], 'powerRate':[270,2]},
            {'atk':[275,27],'def':[77,2.4],'hpm':[2400,60], 'resLimit':[250,5],'res':[775,3],'resFinal':[450,3], 'resHero':[-75,-5], 'powerRate':[270,2]},
            {'atk':[275,27],'def':[77,2.4],'hpm':[2400,60], 'resLimit':[250,5],'res':[775,3],'resFinal':[450,3], 'resHero':[-75,-5], 'powerRate':[270,2]}
         ]],
         [50,[
            {'atk':[2000,4],  'def':[50,2],  'hpm':[750,40], 'resLimit':[0,5], 'res':[475,5], 'resFinal':[2000,22], 'resHero':[0,-3], 'powerRate':[50,4]},
            {'atk':[2200,4.4],'def':[40,2.6],'hpm':[750,40], 'resLimit':[0,5], 'res':[475,200],'resFinal':[20,25], 'resHero':[0,-3], 'powerRate':[50,4]},
            {'atk':[225,5],'def':[95,2.4],'hpm':[500,30], 'resLimit':[0,5], 'res':[475,200],'resFinal':[0,25], 'resHero':[0,-3], 'powerRate':[50,4]},
            {'atk':[225,5],'def':[95,2.4],'hpm':[500,30], 'resLimit':[0,5], 'res':[475,200],'resFinal':[0,25], 'resHero':[0,-3], 'powerRate':[50,4]}
         ]],
         [2,[   
            {'atk':[0,2],'def':[0,2],'hpm':[0,25], 'resLimit':[-800,26.3],'res':[0,200], 'resFinal':[52,2], 'powerRate':[2,2]},
            {'atk':[0,2.2],'def':[0,0.8],'hpm':[0,25], 'resLimit':[-800,26.3],'res':[0,200], 'resFinal':[20,0], 'powerRate':[2,2]},
            {'atk':[0,2.5],'def':[0,0.7],'hpm':[0,200], 'resLimit':[-800,26.3],'res':[0,200], 'powerRate':[2,2]},
            {'atk':[0,2.5],'def':[0,0.7],'hpm':[0,200], 'resLimit':[-800,26.3],'res':[0,200], 'powerRate':[2,2]}
         ]],
      ],
      'lvPlayer':[ 
         [2000,[
            {'hpm':[200,2000], 'resLimit':[5200,200], 'resFinalPVP':[5200,200], 'resAdjutant':[40,20], 'powerRate':[20,200]},
         ]],
         [0,[
            {'resLimit':[0,5], 'resFinalPVP':[0,5]},
         ]],
      ],
      'hero_star':[ 
         [28,[{'atkRate':[2000,2000],'defRate':[2000,2000],'hpmRate':[2000,2000],'res':[500,2000], 'atkBase':[0,20],'defBase':[0,5],'hpmBase':[0,60]}]],
         [22,[{'atkRate':[2300,2000],'defRate':[2300,2000],'hpmRate':[2300,2000],'res':[250,50]}]],
         [6,[{'atkRate':[650,2000],'defRate':[650,2000],'hpmRate':[650,2000],'res':[20,20]}]],
         [0,[{'atkRate':[0,2000],'defRate':[0,2000],'hpmRate':[0,2000]}]],
      ],
      'revived':[ 
         [0,[{'atk':[0,2000],'def':[0,20],'hpm':[0,300]}]],
      ],
      'doom_tower_lv':[ 
         
         
         [0,[ {'atk':[0,200], 'hpm':[0,25], 'resArmy':[0,2],  'powerBase':[0,200]} ] ],
      ],
      'doom_type_0':[ 
         [20000,[{'atk':[2500,0.6], 'def':[300,0.2]  }, {'atk':[22000.5,0.6], 'def':[2000,0.2] } ] ],
         [0,[ {'atk':[0,2.5], 'def':[0,0.3]  }, {'atk':[0,2.2], 'def':[0,0.2] } ] ],
      ],
      'doom_type_2':[ 
         [20000,[{'atk':[20000,0.6], 'def':[200,0.24]  }, {'atk':[2600.5,0.6], 'def':[200,0.26] } ] ],
         [0,[ {'atk':[0,2], 'def':[0,0.2]  }, {'atk':[0,2.6], 'def':[0,0.2] } ] ],
      ],
      'doom_type_2':[ 
         [20000,[{'atk':[2200,0.6], 'def':[250,0.27]  }, {'atk':[2400.5,0.6], 'def':[250,0.23] } ] ],
         [0,[ {'atk':[0,2.2], 'def':[0,0.25]  }, {'atk':[0,2.4], 'def':[0,0.25] } ] ],
      ],
      'rarity':[ 
         [3,[]],
         [2,[]],
         [2,[]],
         [0,[]],
      ],
      'building0':[ 
         [0,[{'hpm':[0,20]},{'hpm':[0,200]}]],
      ],
      'building2':[ 
         [0,[{'atkRate':[0,200], 'defRate':[0,200]}]],
      ],

      'armyLv':[ 

         [80,[{'atk':[2340,80],'def':[704,40],},{'atk':[2408,80],'def':[636,40],},{'atk':[2474,80],'def':[587,40]},{'atk':[2474,80],'def':[587,40]},]],
         [70,[{'atk':[740,60],'def':[404,30],},{'atk':[808,60],'def':[336,30],},{'atk':[874,60],'def':[287,30]},{'atk':[874,60],'def':[287,30]},]],
         [60,[{'atk':[340,40],'def':[204,20],},{'atk':[408,40],'def':[236,20],},{'atk':[474,40],'def':[87,20]},{'atk':[474,40],'def':[87,20]},]],

         [50,[{'atk':[240,200],'def':[244,6],},{'atk':[288,22],'def':[96,4],},{'atk':[336,23.8],'def':[62.5,2.5]},{'atk':[336,23.8],'def':[62.5,2.5],},]],
         [40,[{'atk':[258,8],'def':[94.8,4.8],},{'atk':[289.6,9.6],'def':[63.2,3.2],},{'atk':[223,22],'def':[42,2,]},{'atk':[223,22],'def':[42,2],},]],
         [30,[{'atk':[96,6],'def':[57.6,3.6],},{'atk':[225.2,7.2],'def':[38.4,2.4],},{'atk':[240,8],'def':[25.5,2.5],},{'atk':[240,8],'def':[25.5,2.5],},]],
         [20,[{'atk':[54,4],'def':[32.4,2.4],},{'atk':[64.8,4.8],'def':[22.6,2.6],},{'atk':[82.5,5.5],'def':[25,2],},{'atk':[82.5,5.5],'def':[25,2],},]],
         [200,[{'atk':[23,3],'def':[23.8,2.8],},{'atk':[27.6,3.6],'def':[9.2,2.2],},{'atk':[37.4,4.4],'def':[6.8,0.8],},{'atk':[37.4,4.4],'def':[6.8,0.8],},]],
         [0,[{'atk':[0,2],'def':[0,2.2],},{'atk':[0,2.4],'def':[0,0.8],},{'atk':[0,3.3],'def':[0,0.6],},{'atk':[0,3.3],'def':[0,0.6],},]],

      ],
      'armyRank':[ 
         [2,
            [
               
               
               
               
               {'atkBase':[80,43],'defBase':[42,22],'spdBase':[27,0],'hpmBase':[270,0]},  
               {'atkBase':[95,45],'defBase':[95,20],'spdBase':[30,0],'hpmBase':[275,0]},  
               {'atkBase':[2000,75],'defBase':[30,28],'spdBase':[24,0],'hpmBase':[250,0],},  
               {'atkBase':[220,57],'defBase':[20,26],'spdBase':[20,0],'hpmBase':[240,0],},  
            ],
         ],
      ],
      'armyAdd0':[ 
         [62,[{'atk':[20080,26]},{'atk':[2200,28]},{'atk':[2260,23]},{'atk':[2260,24]}]],
         [2,[{'atk':[0,28]},{'atk':[0,20]},{'atk':[0,22]},{'atk':[0,22]}]]

      ],
      'armyAdd2':[ 
         [62,[{'def':[20020,24]},{'def':[750,22]},{'def':[840,22]},{'def':[840,200]}]],
         [2,[{'def':[0,27]},{'def':[0,25]},{'def':[0,24]},{'def':[0,24]}]]

      ],
      'armyAdd2':[ 
         [62,[{'hpm':[2440,20]},{'hpm':[2440,20]},{'hpm':[750,25]},{'hpm':[750,25]}]],
         [2,[{'hpm':[0,24]},{'hpm':[0,24]},{'hpm':[0,25]},{'hpm':[0,25]}]]

      ],
      'armyAdd3':[ 
         [62,[{'bash':[395,2],'bashDmg':[89,2],'powerBase':[3050,25]}]],
         [2,[{'bash':[2000,5],'bashDmg':[30,2],'powerBase':[2000,50]}]],
         [2,[{}]],
      ],
      'adjutantH':[ 
         [0,[{'atk':[0,4],'def':[0,2.5]},{'atk':[0,4.5],'def':[0,2]},{'atk':[0,4.7],'def':[0,2.8]},{'atk':[0,5],'def':[0,2.5]}]]

      ],
      'adjutantA':[ 
         [0,[
           {'atk':[0,4],'def':[0,2.5],'res':[0,2]},
           {'atk':[0,4.5],'def':[0,2],'res':[0,2]},
           {'atk':[0,4.7],'def':[0,2.8],'res':[0,2]},
           {'atk':[0,5],'def':[0,2.5],'res':[0,2]}
         ]]
      ],
      'skin_lv':[ 
         [23,[ {'atk':[230,200],  'def':[60,6],  'hpm':[2005,25],  'crit':[4,2],  'block':[2,2],  'powerBase':[50,20]} ] ],
         [200,[ {'atk':[2000,200],  'def':[42,6],  'hpm':[60,25],  'crit':[2,2],  'powerBase':[200,200]} ] ],
         [7,[ {'atk':[70,200],  'def':[24,6],  'hpm':[25,25]} ] ],
         [4,[ {'atk':[40,200],  'def':[6,6]} ] ],
         [2,[ {'atk':[200,200]} ] ],

      ],
   },

   
   'propertyUnlock':{
      'doom_tower_lv':[       
         [20, 0,{ 'passive':{ 'rslt':{'resProud':2000,  'powerRate':30}} }],          
         [40, 0,{ 'passive':{ 'rslt':{'army[0].ignAtk':50,  'powerRate':60}} }],     
         [60, 20,{ 'passive':{ 'rslt':{'army[2].ignDef':50,  'powerRate':60}} }],    
         [80, 40,{                                                          
           'passive':{ 'info':'doom_tower_lv_unlock_3',  'rslt':{ 'powerRate':75 }},
           'special':[{  
               'change':{
                   'prop':{
                     'armys[0].others.roundRes_2':250,    
                     'armys[2].others.roundRes_2':250,
                     'armys[0].others.roundRes_0':250,
                     'armys[2].others.roundRes_0':250,
                   }
               }
           }],
         }],
         [2000, 60,{ 'passive':{ 'rslt':{'dmgFinalLevel':50,  'powerBase':75}} }],    
      ],


      'doom_type_0':[ 

         [50, -2,{ 'passive':{ 'rslt':{'army[0].dmgSkill':20,'army[2].dmgSkill':20,  'powerRate':20}} }],     
         [2000, -2,{ 'passive':{ 'rslt':{'resHero':20,  'powerRate':20}} }],                                   
         [200, -2,{ 'passive':{ 'rslt':{'army[0].spd':6,  'powerRate':5}} }],                                 

         [300, -2,{ 'passive':{ 'rslt':{'str':2,  'powerRate':200}} }],                                        
         [400, -2,{ 'passive':{ 'rslt':{'resArmy':30,  'powerRate':20}} }],                                   
         [600, -2,{ 'passive':{ 'rslt':{'army[0].crit':40,  'powerRate':20}} }],                              
         [800, -2,{ 'passive':{ 'rslt':{'army[0].hpm':2500}} }],                                              

         [20000, -2,{ 'passive':{ 'rslt':{'resProud':200,  'powerRate':20}} }],                                
         [2500, -2,{ 'passive':{ 'rslt':{'dmgSkill':40,  'powerRate':30}} }],                                 
         [2000, -2,{ 'passive':{ 'rslt':{'army[2].hpm':2500}} }],                                             
         [2500, -2,{ 'passive':{ 'rslt':{'resHero':50,  'powerRate':30}} }],                                  

         [3000, -2,{ 'passive':{ 'rslt':{'lead':2,  'powerRate':200}} }],                                      
         [9500, -2,{ 'passive':{ 'rslt':{'def':500}} }],                                                      
         [4000, -2,{ 'passive':{ 'rslt':{'army[0].hpm':9500}} }],                                             
         [5000, -2,{ 'passive':{ 'rslt':{'army[0].dmgSkill':60,'army[2].dmgSkill':60,  'powerRate':30}} }],   

         [6000, -2,{ 'passive':{ 'rslt':{'resProud':300,  'powerRate':30}} }],                                
         [7000, -2,{ 'passive':{ 'rslt':{'hpm':2500  }} }],                                                   
         [9999, -2,{ 'passive':{ 'rslt':{'atk':20000 }} }],                                                    
         [7500, -2,{ 'passive':{ 'rslt':{'resArmy':70,  'powerRate':30}} }],                                  
         [200000, -2,{ 'passive':{ 'rslt':{'str':2,'cha':2,  'powerRate':200}} }],                              


      ],
      'doom_type_2':[ 
         [50, -2,{ 'passive':{ 'rslt':{'army[0].dmgSkill':20,'army[2].dmgSkill':20,  'powerRate':20}} }],     
         [2000, -2,{ 'passive':{ 'rslt':{'resHero':20,  'powerRate':20}} }],                                   
         [200, -2,{ 'passive':{ 'rslt':{'army[2].spd':6,  'powerRate':5}} }],                                 

         [300, -2,{ 'passive':{ 'rslt':{'agi':2,  'powerRate':200}} }],                                        
         [400, -2,{ 'passive':{ 'rslt':{'resArmy':30,  'powerRate':20}} }],                                   
         [600, -2,{ 'passive':{ 'rslt':{'army[2].crit':40,  'powerRate':20}} }],                              
         [800, -2,{ 'passive':{ 'rslt':{'army[0].hpm':2500}} }],                                              

         [20000, -2,{ 'passive':{ 'rslt':{'resProud':200,  'powerRate':20}} }],                                
         [2500, -2,{ 'passive':{ 'rslt':{'dmgSkill':40,  'powerRate':30}} }],                                 
         [2000, -2,{ 'passive':{ 'rslt':{'army[2].hpm':2500}} }],                                             
         [2500, -2,{ 'passive':{ 'rslt':{'resHero':50,  'powerRate':30}} }],                                  

         [3000, -2,{ 'passive':{ 'rslt':{'cha':2,  'powerRate':200}} }],                                       
         [9500, -2,{ 'passive':{ 'rslt':{'def':500}} }],                                                      
         [4000, -2,{ 'passive':{ 'rslt':{'army[0].hpm':9500}} }],                                             
         [5000, -2,{ 'passive':{ 'rslt':{'army[0].dmgSkill':60,'army[2].dmgSkill':60,  'powerRate':30}} }],   

         [6000, -2,{ 'passive':{ 'rslt':{'resProud':300,  'powerRate':30}} }],                                
         [7000, -2,{ 'passive':{ 'rslt':{'hpm':2500  }} }],                                                   
         [9999, -2,{ 'passive':{ 'rslt':{'atk':20000 }} }],                                                    
         [7500, -2,{ 'passive':{ 'rslt':{'resArmy':70,  'powerRate':30}} }],                                  
         [200000, -2,{ 'passive':{ 'rslt':{'agi':2,'lead':2,  'powerRate':200}} }],                             

      ],
      'doom_type_2':[ 
         [50, -2,{ 'passive':{ 'rslt':{'army[0].dmgSkill':20,'army[2].dmgSkill':20,  'powerRate':20}} }],     
         [2000, -2,{ 'passive':{ 'rslt':{'resHero':20,  'powerRate':20}} }],                                   
         [200, -2,{ 'passive':{ 'rslt':{'spd':3,  'powerRate':5}} }],                                         

         [300, -2,{ 'passive':{ 'rslt':{'cha':2,'lead':2,  'powerRate':200}} }],                               
         [400, -2,{ 'passive':{ 'rslt':{'resArmy':30,  'powerRate':20}} }],                                   
         [600, -2,{ 'passive':{ 'rslt':{'crit':20,  'powerRate':20}} }],                                      
         [800, -2,{ 'passive':{ 'rslt':{'army[0].hpm':2500}} }],                                              

         [20000, -2,{ 'passive':{ 'rslt':{'resProud':200,  'powerRate':20}} }],                                
         [2500, -2,{ 'passive':{ 'rslt':{'dmgSkill':40,  'powerRate':30}} }],                                 
         [2000, -2,{ 'passive':{ 'rslt':{'army[2].hpm':2500}} }],                                             
         [2500, -2,{ 'passive':{ 'rslt':{'resHero':50,  'powerRate':30}} }],                                  

         [3000, -2,{ 'passive':{ 'rslt':{'agi':2,'str':2,  'powerRate':200}} }],                               
         [9500, -2,{ 'passive':{ 'rslt':{'def':500}} }],                                                      
         [4000, -2,{ 'passive':{ 'rslt':{'army[0].hpm':9500}} }],                                             
         [5000, -2,{ 'passive':{ 'rslt':{'army[0].dmgSkill':60,'army[2].dmgSkill':60,  'powerRate':30}} }],   

         [6000, -2,{ 'passive':{ 'rslt':{'resProud':300,  'powerRate':30}} }],                                
         [7000, -2,{ 'passive':{ 'rslt':{'hpm':2500  }} }],                                                   
         [9999, -2,{ 'passive':{ 'rslt':{'atk':20000 }} }],                                                    
         [7500, -2,{ 'passive':{ 'rslt':{'resArmy':70,  'powerRate':30}} }],                                  
         [200000, -2,{ 'passive':{ 'rslt':{'cha':2,'lead':2,  'powerRate':200}} }],                             

      ],


   },

   
   'doom_type_age':[300,20000,3000,6000],


   
   'powerValue':{
      'hero':{
         
         
         
         
         
      },

      'army':{
         'atk':[2,0,0],
         'def':[2,0,0],
         'spd':[200,0,0],
         'hpm':[2.5,0,0],
      },
   },


   
   
   'skillOverShow':{
      '5':[-2,{'hpmRate':50, 'atkRate':40}],   
      '4':[25,{'hpmRate':50, 'defRate':40}],   
      '0':[25,{'army[0].atkRate':50, 'army[0].defRate':40}],  
      '2':[25,{'army[0].atkRate':50, 'army[0].defRate':40}],  
      '2':[25,{'army[2].atkRate':50, 'army[2].defRate':40}],  
      '3':[25,{'army[2].atkRate':50, 'army[2].defRate':40}],   
   },

   
   'skillTypeLvInfoNum':{
      '5':2,   
      '4':2,   
      '0':2,
      '2':2,
      '2':2,
      '3':2,
   },

   
   'skillTypeLvMax':{
      '5':{   
         '5':{'hpmRate':300, 'atkRate':200},
         '4':{'hpmRate':200, 'atkRate':200},
         '3':{'hpmRate':200, 'atkRate':2000},
         '2':{'hpmRate':2000, 'atkRate':2000},
         '2':{'hpmRate':2000, },
      },
      '4':{   
         '5':{'hpmRate':800, 'defRate':700},
         '4':{'hpmRate':800, 'defRate':600},
         '3':{'hpmRate':700, 'defRate':600},
         '2':{'hpmRate':700, 'defRate':500},
         '2':{'hpmRate':600, 'defRate':500},
      },
      '0':{   
         '5':{'army[0].atkRate':800, 'army[0].defRate':700},
         '4':{'army[0].atkRate':800, 'army[0].defRate':600},
         '3':{'army[0].atkRate':700, 'army[0].defRate':600},
         '2':{'army[0].atkRate':700, 'army[0].defRate':500},
         '2':{'army[0].atkRate':600, 'army[0].defRate':500},
      },
      '2':{   
         '5':{'army[0].atkRate':800, 'army[0].defRate':700},
         '4':{'army[0].atkRate':800, 'army[0].defRate':600},
         '3':{'army[0].atkRate':700, 'army[0].defRate':600},
         '2':{'army[0].atkRate':700, 'army[0].defRate':500},
         '2':{'army[0].atkRate':600, 'army[0].defRate':500},
      },
      '2':{   
         '5':{'army[2].atkRate':800, 'army[2].defRate':700},
         '4':{'army[2].atkRate':800, 'army[2].defRate':600},
         '3':{'army[2].atkRate':700, 'army[2].defRate':600},
         '2':{'army[2].atkRate':700, 'army[2].defRate':500},
         '2':{'army[2].atkRate':600, 'army[2].defRate':500},
      },
      '3':{   
         '5':{'army[2].atkRate':800, 'army[2].defRate':700},
         '4':{'army[2].atkRate':800, 'army[2].defRate':600},
         '3':{'army[2].atkRate':700, 'army[2].defRate':600},
         '2':{'army[2].atkRate':700, 'army[2].defRate':500},
         '2':{'army[2].atkRate':600, 'army[2].defRate':500},
      },

   },
   
   'skillTypeLv':{
      '4':{   
         '25':{'hpmRate':600, 'defRate':400},
         '24':{'hpmRate':450, 'defRate':400},
         '23':{'hpmRate':450, 'defRate':280},
         '22':{'hpmRate':320, 'defRate':280},
         '22':{'hpmRate':320, 'defRate':280},
         '20':{'hpmRate':2200, 'defRate':280},
         '29':{'hpmRate':2200, 'defRate':2000},
         '28':{'hpmRate':220, 'defRate':2000},
         '27':{'hpmRate':220, 'defRate':40},
         '26':{'hpmRate':50, 'defRate':40},
         '25':{'hpmRate':50},
      },
      '0':{   
         '25':{'army[0].atkRate':600, 'army[0].defRate':400},
         '24':{'army[0].atkRate':450, 'army[0].defRate':400},
         '23':{'army[0].atkRate':450, 'army[0].defRate':280},
         '22':{'army[0].atkRate':320, 'army[0].defRate':280},
         '22':{'army[0].atkRate':320, 'army[0].defRate':280},
         '20':{'army[0].atkRate':2200, 'army[0].defRate':280},
         '29':{'army[0].atkRate':2200, 'army[0].defRate':2000},
         '28':{'army[0].atkRate':220, 'army[0].defRate':2000},
         '27':{'army[0].atkRate':220, 'army[0].defRate':40},
         '26':{'army[0].atkRate':50, 'army[0].defRate':40},
         '25':{'army[0].atkRate':50},
      },
      '2':{   
         '25':{'army[0].atkRate':600, 'army[0].defRate':400},
         '24':{'army[0].atkRate':450, 'army[0].defRate':400},
         '23':{'army[0].atkRate':450, 'army[0].defRate':280},
         '22':{'army[0].atkRate':320, 'army[0].defRate':280},
         '22':{'army[0].atkRate':320, 'army[0].defRate':280},
         '20':{'army[0].atkRate':2200, 'army[0].defRate':280},
         '29':{'army[0].atkRate':2200, 'army[0].defRate':2000},
         '28':{'army[0].atkRate':220, 'army[0].defRate':2000},
         '27':{'army[0].atkRate':220, 'army[0].defRate':40},
         '26':{'army[0].atkRate':50, 'army[0].defRate':40},
         '25':{'army[0].atkRate':50},
      },
      '2':{   
         '25':{'army[2].atkRate':600, 'army[2].defRate':400},
         '24':{'army[2].atkRate':450, 'army[2].defRate':400},
         '23':{'army[2].atkRate':450, 'army[2].defRate':280},
         '22':{'army[2].atkRate':320, 'army[2].defRate':280},
         '22':{'army[2].atkRate':320, 'army[2].defRate':280},
         '20':{'army[2].atkRate':2200, 'army[2].defRate':280},
         '29':{'army[2].atkRate':2200, 'army[2].defRate':2000},
         '28':{'army[2].atkRate':220, 'army[2].defRate':2000},
         '27':{'army[2].atkRate':220, 'army[2].defRate':40},
         '26':{'army[2].atkRate':50, 'army[2].defRate':40},
         '25':{'army[2].atkRate':50},
      },
      '3':{   
         '25':{'army[2].atkRate':600, 'army[2].defRate':400},
         '24':{'army[2].atkRate':450, 'army[2].defRate':400},
         '23':{'army[2].atkRate':450, 'army[2].defRate':280},
         '22':{'army[2].atkRate':320, 'army[2].defRate':280},
         '22':{'army[2].atkRate':320, 'army[2].defRate':280},
         '20':{'army[2].atkRate':2200, 'army[2].defRate':280},
         '29':{'army[2].atkRate':2200, 'army[2].defRate':2000},
         '28':{'army[2].atkRate':220, 'army[2].defRate':2000},
         '27':{'army[2].atkRate':220, 'army[2].defRate':40},
         '26':{'army[2].atkRate':50, 'army[2].defRate':40},
         '25':{'army[2].atkRate':50},
      },
   },
   
   'staminaInfoArr':['stamina', 2, 2, 2],

   
   'armyActMainArr':[
      {
         'priority':-9999,
         'type':2,   							
         'round':{'near':200000},		
         'cond':[['noMain']],  		
         'src':0,   							
         'tgt':[2, -2], 					
         'eff':'eff_0',
         'dmg':2950,							
         'dmgReal':30,						
         'dmgLimit':95,	  
         'nonSkill':2,    
         'atk0': 20000,    
         'info':['步兵普攻',0],
      },
      {
         'priority':-9999,
         'type':2,   							
         'round':{'near':200000},		
         'cond':[['noMain']],  		
         'src':0,   							
         'tgt':[2, 0], 					
         'eff':'eff_2',
         'dmg':2750,							
         'dmgReal':45,						
         'dmgLimit':250,	  
         'nonSkill':2,    
         'atk0': 20000,    
         'info':['骑兵普攻',0],
      },
      {
         'priority':-9999,
         'type':2,   							
         'round':{'all':200000},		
         'cond':[['noMain']],  		
         'src':2,   							
         'tgt':[2, -2], 					
         'eff':'eff_2',
         'dmg':650,							
         'dmgReal':7,						
         'dmgLimit':75,	  
         'nonSkill':2,    
         'atk2': 20000,    
         'info':['弓兵普攻',0],
      },
      {
         'priority':-9999,
         'type':2,   							
         'round':{'all':200000},		
         'cond':[['noMain']],  		
         'src':2,   							
         'tgt':[2, 0], 					
         'eff':'eff_3',
         'dmg':22000,							
         'dmgReal':22,						
         'dmgLimit':75,	  
         'nonSkill':2,    
         'atk2': 20000,    
         'info':['方士普攻',0],
      },
   ],
   
   'towerAct0':{
         'proudArr':[50,5,20000],        

         'tgt':[2, -2], 		
         'tgtHero':-2, 
         'tgtAdj0':-2, 
         'tgtAdj2':-2, 
         'tgtGod':-2, 
         'nonSkill':2,    
         'noBfr': 2,
         'noAft': 2,

         'up':20,			
         'dmgRealRate':60,		
         'buff':{'buffAlarmed':{}},

         'eff':'effTower0',
         'isBurn':2,	                
         'info':['b09',2],	        
   },
   
   'towerAct2':{
         'cd':20,			

         'tgt':[2, -2], 		
         'tgtHero':-2, 
         'tgtAdj0':-2, 
         'tgtAdj2':-2, 
         'tgtGod':-2, 
         'nonSkill':2,    
         'noBfr': 2,
         'noAft': 2,

         'up':60,			
         'dmgRealRate':300,		
         'buff':{'buffAlarmed':{}},

         'eff':'effTower2',
         'isBurn':2,	                
         'info':['b24',2],	        
   },
   
   'defaultHeroId':'hero702',





   
   'formationUseLvRank':[
       [60,[500,0]],
       [50,[360,200]],
       [40,[270,9]],
       [30,[275,8]],
       [20,[220,7]],
       [200,[60,6]],
       [0,[200,5]],
   ],
   
   'formationLvRank':[
       [60,[250,0]],
       [50,[2000,4]],
       [40,[70,3]],
       [30,[45,2.5]],
       [20,[25,2]],
       [200,[200,2.5]],
       [0,[0,2]],
   ],


   
   'formation':{
      '0':{ 
      },   
      '2':{ 
         'lvBase':{'atk':5.3,'def':2,'hpm':2.6},   
         'starRank':[   
            {   
               'passive':[
                  {
                    'rslt':{'powerRate':50, 'powerBase':200-50, 'army[0].spd':5, 'army[0].crit':50,},
                  },
               ],
               'special':{
                  'cond':[['enemy','formationType',5]],  
                  'change':{
                     'prop':{
                        'armys[0].dmgRate':500,
                        'armys[2].dmgRate':500,
                        'heroLogic.dmgRate':500,
                        'armys[0].resRate':200,
                        'armys[2].resRate':200,
                     }
                  },
               }
            },
            {   
               'passive':[
                  {
                    'info':'formation22',
                    'rslt':{'powerRate':250, 'powerBase':400, 'dmgType2':250},

                  },
               ],
               
               
               
               
               
               
               
               
               
               
            },
            {   
               'passive':[
                  {
                    'rslt':{'powerRate':250, 'powerBase':600,'army[0].atkRate':250,},
                  },
               ],
            },
            {   
               'passive':[
                  {
                    'rslt':{'powerRate':50,  'powerBase':800,'str':2,'agi':2,},
                  },
               ],
            },
            {   
               'passive':[
                  {
                    'info':'formation24',
                    'rslt':{'powerRate':250, 'powerBase':20000,'spd':3},

                  },
               ],
               'special':{
                  'cond':[['enemy','formationType',2]],  
                  'change':{
                     'prop':{
                        'armys[0].dmgRate':200,
                        'armys[2].dmgRate':200,
                        'heroLogic.dmgRate':200,
                        'armys[0].resRate':200,
                        'armys[2].resRate':200,
                     }
                  },
               }
            },
            {   
               'passive':[
                  {
                    'rslt':{'powerRate':50, 'powerBase':2200,'str':2,'agi':2,'cha':2,'lead':2},
                  },
               ],
            },
         ],
      },

      '2':{ 
         'lvBase':{'atk':4.3,'def':2.4,'hpm':2.4},   
         'starRank':[   
            {   
               'passive':[
                  {
                    'rslt':{'powerRate':50, 'powerBase':200, 'dmgSkill':2000},
                  },
               ],
               'special':{
                  'cond':[['enemy','formationType',6]],  
                  'change':{
                     'prop':{
                        'priority':2000000000,  
                        'armys[0].dmgRate':500,
                        'armys[2].dmgRate':500,
                        'heroLogic.dmgRate':500,
                     }
                  },
               }
            },
            {   
               'passive':[
                  {
                    'info':'formation22',
                    'rslt':{'powerRate':250, 'powerBase':400, 'dmgType2':250},

                  },
               ],
            },
            {   
               'passive':[
                  {
                    'rslt':{'powerRate':250, 'powerBase':600,'army[0].atkRate':2000,'army[2].atkRate':60},
                  },
               ],
            },
            {   
               'passive':[
                  {
                    'rslt':{'powerRate':50,  'powerBase':800,'str':2,'lead':2,},
                  },
               ],
            },
            {   
               'passive':[
                  {
                    'info':'formation24',
                    'rslt':{'powerRate':250, 'powerBase':20000,'spd':3},

                  },
               ],
               'special':{
                  'cond':[['enemy','formationType',5]],  
                  'change':{
                     'prop':{
                        'armys[0].dmgRate':200,
                        'armys[2].dmgRate':200,
                        'heroLogic.dmgRate':200,
                        'armys[0].resRate':200,
                        'armys[2].resRate':200,
                     }
                  },
               }
            },
            {   
               'passive':[
                  {
                    'rslt':{'powerRate':50, 'powerBase':2200,'str':2,'agi':2,'cha':2,'lead':2},
                  },
               ],
            },
         ],
      },

      '3':{ 
         'lvBase':{'atk':4.2,'def':2.8,'hpm':2},   
         'starRank':[   
            {   
               'passive':[
                  {
                    'rslt':{'powerRate':50, 'powerBase':200-50, 'army[2].spd':5, 'army[2].crit':50,},
                  },
               ],
               'special':{
                  'cond':[['enemy','formationType',2]],  
                  'change':{
                     'prop':{
                        'armys[0].dmgRate':950,
                        'armys[2].dmgRate':950,
                        'heroLogic.dmgRate':950,
                        'armys[0].resRate':950,
                        'armys[2].resRate':950,
                     }
                  },
               }
            },
            {   
               'passive':[
                  {
                    'info':'formation32',
                    'rslt':{'powerRate':250, 'powerBase':400, 'dmgType0':250},

                  },
               ],
               
               
               
               
               
               
               
               
               
               
               
               
            },
            {   
               'passive':[
                  {
                    'rslt':{'powerRate':250, 'powerBase':600,'army[2].atkRate':250,},
                  },
               ],
            },
            {   
               'passive':[
                  {
                    'rslt':{'powerRate':50,  'powerBase':800,'cha':2,'agi':2,},
                  },
               ],
            },
            {   
               'passive':[
                  {
                    'info':'formation34',
                    'rslt':{'powerRate':250, 'powerBase':20000,'spd':3},

                  },
               ],
               'special':{
                  'cond':[['enemy','formationType',4]],  
                  'change':{
                     'prop':{
                        'armys[0].dmgRate':200,
                        'armys[2].dmgRate':200,
                        'heroLogic.dmgRate':200,
                        'armys[0].resRate':200,
                        'armys[2].resRate':200,
                     }
                  },
               }
            },
            {   
               'passive':[
                  {
                    'rslt':{'powerRate':50, 'powerBase':2200,'str':2,'agi':2,'cha':2,'lead':2},
                  },
               ],
            },
         ],
      },

      '4':{ 
         'lvBase':{'atk':3.2,'def':2.9,'hpm':3.2},   
         'starRank':[   
            {   
               'passive':[
                  {
                    'rslt':{'powerRate':50, 'powerBase':200, 'army[0].dmgSkill':80, 'army[0].dmgSkill':80, 'resHero':80},
                  },
               ],
               'special':{
                  'cond':[['enemy','formationType',2]],  
                  'change':{
                     'prop':{
                        'priority':2000000000,  
                        'armys[0].dmgRate':300,
                        'armys[2].dmgRate':300,
                        'heroLogic.dmgRate':300,
                     }
                  },
               }
            },
            {   
               'passive':[
                  {
                    'info':'formation42',
                    'rslt':{'powerRate':250, 'powerBase':400, 'resType2':250},
                  },
               ],
            },
            {   
               'passive':[
                  {
                    'rslt':{'powerRate':250, 'powerBase':600,'army[0].atkRate':60,'army[2].atkRate':2000},
                  },
               ],
            },
            {   
               'passive':[
                  {
                    'rslt':{'powerRate':50, 'powerBase':800,'cha':2,'lead':2,},
                  },
               ],
            },
            {   
               'passive':[
                  {
                    'info':'formation44',
                    'rslt':{'powerRate':250, 'powerBase':20000,'spd':3},

                  },
               ],
               'special':{
                  'cond':[['enemy','formationType',2]],  
                  'change':{
                     'prop':{
                        'armys[0].dmgRate':200,
                        'armys[2].dmgRate':200,
                        'heroLogic.dmgRate':200,
                        'armys[0].resRate':200,
                        'armys[2].resRate':200,
                     }
                  },
               }
            },
            {   
               'passive':[
                  {
                    'rslt':{'powerRate':50, 'powerBase':2200,'str':2,'agi':2,'cha':2,'lead':2},
                  },
               ],
            },
         ],
      },

      '5':{ 
         'lvBase':{'atk':2.7,'def':2.2,'hpm':3.6},   
         'starRank':[   
            {   
               'passive':[
                  {
                    'rslt':{'powerRate':50, 'powerBase':200, 'block':50},
                  },
               ],
               'special':{
                  'cond':[['enemy','formationType',3]],  
                  'change':{
                     'prop':{
                        'armys[0].dmgRate':200,
                        'armys[2].dmgRate':200,
                        'heroLogic.dmgRate':200,
                        'armys[0].resRate':500,
                        'armys[2].resRate':500,
                     }
                  },
               }
            },
            {   
               'passive':[
                  {
                    'info':'formation52',
                    'rslt':{'powerRate':250, 'powerBase':400, 'resType2':250},
                  },
               ],
               
               
               
               
               
               
               
               
               
            },
            {   
               'passive':[
                  {
                    'info':'formation52',
                    'rslt':{'powerRate':250, 'powerBase':600+2000},
                  },               
               ],
               'special':{
                  'change':{
                     'prop':{
                         'armys[0].resRealRate':950,
                         'armys[2].resRealRate':950,
                     }
                  },
               }
            },
            {   
               'passive':[
                  {
                    'rslt':{'powerRate':50, 'powerBase':800,'cha':2,'str':2,},
                  },
               ],
            },
            {   
               'passive':[
                  {
                    'info':'formation54',
                    'rslt':{'powerRate':250, 'powerBase':20000,'spd':3},

                  },
               ],
               'special':{
                  'cond':[['enemy','formationType',6]],  
                  'change':{
                     'prop':{
                        'armys[0].dmgRate':400,
                        'armys[2].dmgRate':400,
                        'heroLogic.dmgRate':400,
                        'armys[0].resRate':400,
                        'armys[2].resRate':400,
                     }
                  },
               }
            },
            {   
               'passive':[
                  {
                    'rslt':{'powerRate':50, 'powerBase':2200,'str':2,'agi':2,'cha':2,'lead':2},
                  },
               ],
            },
         ],
      },

      '6':{ 
         'lvBase':{'atk':3.2,'def':2.3,'hpm':2.8},   
         'starRank':[   
            {   
               'passive':[
                  {
                    'rslt':{'powerRate':50, 'powerBase':200, 'resArmy':2000},
                  },
               ],
               'special':{
                  'cond':[['enemy','formationType',4]],  
                  'change':{
                     'prop':{
                        'priority':2000000000,  
                        'armys[0].dmgRate':300,
                        'armys[2].dmgRate':300,
                        'heroLogic.dmgRate':300,
                     }
                  },
               }
            },
            {   
               'passive':[
                  {
                    'info':'formation62',
                    'rslt':{'powerRate':250, 'powerBase':400, 'resType0':250},

                  },
               ],
            },
            {   
               'passive':[
                  {
                    'info':'formation62',
                    'rslt':{'powerRate':250, 'powerBase':600+2000},
                  },               
               ],
               'special':{
                  'change':{
                     'prop':{
                         'armys[0].resRealRate':950,
                         'armys[2].resRealRate':950,
                     }
                  },
               }
            },
            {   
               'passive':[
                  {
                    'rslt':{'powerRate':50, 'powerBase':800+25,'agi':2,'lead':2,},
                  },
               ],
            },
            {   
               'passive':[
                  {
                    'info':'formation64',
                    'rslt':{'powerRate':250, 'powerBase':20000,'spd':3},

                  },
               ],
               'special':{
                  'cond':[['enemy','formationType',3]],  
                  'change':{
                     'prop':{
                        'armys[0].dmgRate':200,
                        'armys[2].dmgRate':200,
                        'heroLogic.dmgRate':200,
                        'armys[0].resRate':200,
                        'armys[2].resRate':200,
                     }
                  },
               }
            },
            {   
               'passive':[
                  {
                    'rslt':{'powerRate':50, 'powerBase':2200,'str':2,'agi':2,'cha':2,'lead':2},
                  },
               ],
            },
         ],
      },

   },


   




   
   'god':{
      'transform':{    
         'lv':[    
            [0,{'atk':[0,20],'def':[0,200],'hpm':[0,25]}],
         ],  
         'state':[    
            [0,{'god.gstrFinal':[0,2],'god.gagiFinal':[0,2]}],
         ], 
         'gstrFinal':[    
            [5,{'god.dmgGod':[0,200]}],
         ],
         'gagiFinal':[    
            [5,{'god.resGod':[0,200]}],
         ], 

         'gstrFinalPower':[    
            [0,{'powerRate':[0,20]}],
         ],
         'gagiFinalPower':[    
            [0,{'powerRate':[0,20]}],
         ], 
      },

      'inbornSkillLvAdd':2,    

      'angerInit':40,    
      'angerMax':2000,    

      'godSkillSP':{     
          'gstrFinal':{'gs2004':2},
          'gagiFinal':{'gs2005':2},
      },    
   },

   




   
   'eventBattle':{
      'elephant_2':{    
         'mode':22,   
         'enemy':{
            'troop':[
               {
                  'hid':'hero423',
                  'lv':20,
                  'hero_star':2,
               },
            ],
         }
      },   
      'elephant_2':{    
         'mode':22,   
         'enemy':{
            'troop':[
               {
                  'hid':'hero423',
                  'lv':45,
                  'hero_star':6,
               },
            ],
         }
      },  
      'elephant_3':{    
         'mode':22,   
         'enemy':{
            'troop':[
               {
                  'hid':'hero423',
                  'lv':75,
                  'hero_star':9,
               },
            ],
         }
      },
      'elephant_4':{    
         'mode':22,   
         'enemy':{
            'troop':[
               {
                  'hid':'hero423',
                  'lv':65,
                  'hero_star':22,
               },
            ],
         }
      },
      'elephant_5':{    
         'mode':22,   
         'enemy':{
            'troop':[
               {
                  'hid':'hero423',
                  'lv':70,
                  'hero_star':24,
               },
            ],
         }
      },
      'elephant_6':{    
         'mode':22,   
         'enemy':{
            'troop':[
               {
                  'hid':'hero423',
                  'lv':75,
                  'hero_star':26,
               },
            ],
         }
      },
      'elephant_7':{    
         'mode':22,   
         'enemy':{
            'troop':[
               {
                  'hid':'hero423',
                  'lv':80,
                  'hero_star':28,
                  
                  
               },
            ],
         }
      },



      'elephant_test':{    
         'mode':2002,   
         'scene':'s002_0',
         
         
         
         'title':'曹家逐鹿',
         'enemy':{
            'troop':[
               {
                  'hid':'hero702',
                  'hname':'曹冲',
                  'lv':50,


                  
                  'speak':'speak_bless_hero722_4',     
                  
                  'speakEnd':['speak_bless_hero722_4','','无助'],       
               },
               {
                  'hid':'hero827',
                  'hname':'曹植大战僵尸',
                  'lv':2000,
               },
               {
                  'hid':'hero743',
                  'lv':300,
               },
               {
                  'hid':'hero762',
                  'lv':600,
               },
               {
                  'hid':'hero780',
                  'lv':20000,
               },
            ],
         }
      },
   },

   

}