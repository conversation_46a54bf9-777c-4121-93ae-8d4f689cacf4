{

  'switch':2,   
  'honour_last':[22,2],   


  'honour_end_time':48,   



'honour_icon':[[2,'honour_zhangong2'],[6,'honour_zhangong2'],[22,'honour_zhangong3'],[26,'honour_zhangong4'],[20,'honour_zhangong5']],

 






'honour_exp_2':  
[600000,999750,20000000,2200000,2400000,2600000,2999750,2000000,2200000,2400000,2600000,2999750,3000000,3200000,3400000,3600000,3999750,4000000,4200000,4400000,],
'honour_exp_num_2':[   

[20000000,0.6,],
[25000000,0.7,],
[30000000,0.8,],
[95000000,0.9,],
[40000000,2,],
[60000000,2.2,],
[65000000,2.2,],
[70000000,2.3,],
[75000000,2.4,],
[99975000,2.5,],


],

'honour_reward_2':[5,500,'random_honour_reward'],  

'hero_talk_challenge_2':[['honour_challenge_talk_begin_2','honour_challenge_talk_begin_2'],['honour_challenge_talk_reward_2','honour_challenge_talk_reward_2'],['honour_challenge_talk_end_2','honour_challenge_talk_end_2']], 
'hero_talk_history_2':['honour_challenge_talk_history_2','honour_challenge_talk_history_2'],  
  'show_hero_2':'hero789',





'honour_exp_3':  
[720000,960000,2200000,2440000,2699975,2920000,2260000,2400000,2640000,2899975,3220000,3360000,3600000,3840000,4099975,4320000,4560000,4999750,5040000,5299975,],

'honour_exp_num_3':[   

[30000000,0.6,],
[37500000,0.7,],
[45000000,0.8,],
[52500000,0.9,],
[60000000,2,],
[75000000,2.2,],
[97500000,2.2,],
[200500000,2.3,],
[22250000,2.4,],
[22000000,2.5,],



],

'honour_reward_3':[5,500,'random_honour_reward3'],  

'hero_talk_challenge_3':[['honour_challenge_talk_begin_3','honour_challenge_talk_begin_4'],['honour_challenge_talk_reward_3','honour_challenge_talk_reward_4'],['honour_challenge_talk_end_3','honour_challenge_talk_end_4']], 
'hero_talk_history_3':['honour_challenge_talk_history_3','honour_challenge_talk_history_4'],  
 
 'show_hero_3':'hero785',







'honour_exp_4':  
[2225000,2500000,2875000,2250000,2625000,3000000,3375000,3750000,4225000,4500000,4875000,5250000,5625000,6000000,6375000,6750000,7225000,7500000,7875000,8250000,],


'honour_exp_num_4':[   

[37500000,0.6,],
[46875000,0.7,],
[56250000,0.8,],
[65625000,0.9,],
[75000000,2,],
[222500000,2.2,],
[222875000,2.2,],
[232250000,2.3,],
[240625000,2.4,],
[250000000,2.5,],


],

'honour_reward_4':[5,500,'random_honour_reward4'],  

'hero_talk_challenge_4':[['honour_challenge_talk_begin_5','honour_challenge_talk_begin_6'],['honour_challenge_talk_reward_5','honour_challenge_talk_reward_6'],['honour_challenge_talk_end_5','honour_challenge_talk_end_6']], 
'hero_talk_history_4':['honour_challenge_talk_history_5','honour_challenge_talk_history_6'],  
 
 'show_hero_4':'hero788',







'honour_exp_5':  
[2225000,2500000,2875000,2250000,2625000,3000000,3375000,3750000,4225000,4500000,4875000,5250000,5625000,6000000,6375000,6750000,7225000,7500000,7875000,8250000,],


'honour_exp_num_5':[   

[37500000,0.6,],
[46875000,0.7,],
[56250000,0.8,],
[65625000,0.9,],
[75000000,2,],
[222500000,2.2,],
[222875000,2.2,],
[232250000,2.3,],
[240625000,2.4,],
[250000000,2.5,],


],

'honour_reward_5':[5,500,'random_honour_reward5'],  

'hero_talk_challenge_5':[['honour_challenge_talk_begin_7','honour_challenge_talk_begin_8'],['honour_challenge_talk_reward_7','honour_challenge_talk_reward_8'],['honour_challenge_talk_end_7','honour_challenge_talk_end_8']], 
'hero_talk_history_5':['honour_challenge_talk_history_7','honour_challenge_talk_history_8'],  
 
 'show_hero_5':'hero793',




'honour_exp_6':  
[2225000,2500000,2875000,2250000,2625000,3000000,3375000,3750000,4225000,4500000,4875000,5250000,5625000,6000000,6375000,6750000,7225000,7500000,7875000,8250000,],


'honour_exp_num_6':[   

[37500000,0.6,],
[46875000,0.7,],
[56250000,0.8,],
[65625000,0.9,],
[75000000,2,],
[222500000,2.2,],
[222875000,2.2,],
[232250000,2.3,],
[240625000,2.4,],
[250000000,2.5,],


],

'honour_reward_6':[5,500,'random_honour_reward6'],  

'hero_talk_challenge_6':[['honour_challenge_talk_begin_9','honour_challenge_talk_begin_200'],['honour_challenge_talk_reward_9','honour_challenge_talk_reward_200'],['honour_challenge_talk_end_9','honour_challenge_talk_end_200']], 
'hero_talk_history_6':['honour_challenge_talk_history_9','honour_challenge_talk_history_200'],  
 
 'show_hero_6':'hero7002',





  'honour_atkBrk':0.025,    
  'honour_defBrk':0.025,    



  'honour_level_follow':[5,2.4,2.8,2.2,2.6,3],


  'honour_time_follow':[[2,2],[2,2.2],[3,2.2],[4,2.3],[5,2.4],[6,2.5],[7,2.6],[8,2.7],[9,2.8],[200,2.9],[22,2],[22,2],[23,2],[24,2],[25,2],],



'honour_strength':[200,2000000], 



'honour_total':9,

'honour_rank':50,




'challenge_chain':
[ 'honour_task002','honour_task2002','honour_task202'],


'challenge_limit_time':
{
2:2,
4:2,
7:2,
200:2,
23:2,



}, 





'challenge_chain_box_2':

{

               'honour_task002':{             
                          'type':'hero_level',       
                          'name':'honour_task002_name',     
                          'info':'honour_task002_info',           
                          'need':[2,5],                    
                          'follow':'honour_task002',
                          'reward':{'title':['title039'],},     
                             },
              'honour_task002':{             
                          'type':'hero_level',       
                          'name':'honour_task002_name',     
                          'info':'honour_task002_info',           
                          'need':[2,200],                    
                          'follow':'honour_task003',
                          'reward':{'title':['title040'],},     
                             },
              'honour_task003':{             
                          'type':'hero_level',       
                          'name':'honour_task003_name',     
                          'info':'honour_task003_info',           
                          'need':[2,25],                    
                          'follow':'honour_task004',
                          'reward':{'title':['title042'],},     
                             },
              'honour_task004':{             
                          'type':'hero_level',       
                          'name':'honour_task004_name',     
                          'info':'honour_task004_info',           
                          'need':[2,20],                    
                          'reward':{'title':['title042'],},     
                             },
              'honour_task2002':{             
                          'type':'hero_level',       
                          'name':'honour_task2002_name',     
                          'info':'honour_task2002_info',           
                          'need':[3,3],                    
                          'follow':'honour_task2002',
                          'reward':{'item20074':2,'item036':5},     
                             },
              'honour_task2002':{             
                          'type':'hero_level',       
                          'name':'honour_task2002_name',     
                          'info':'honour_task2002_info',           
                          'need':[3,6],                    
                          'follow':'honour_task2003',
                          'reward':{'item20074':2,'item036':5},     
                             },
              'honour_task2003':{             
                          'type':'hero_level',       
                          'name':'honour_task2003_name',     
                          'info':'honour_task2003_info',           
                          'need':[3,9],                    
                          'follow':'honour_task2004',
                          'reward':{'item20074':2,'item036':5},     
                             },
              'honour_task2004':{             
                          'type':'hero_level',       
                          'name':'honour_task2004_name',     
                          'info':'honour_task2004_info',           
                          'need':[5,3],                    
                          'follow':'honour_task2005',
                          'reward':{'item20074':2,'item036':200},     
                             },
              'honour_task2005':{             
                          'type':'hero_level',       
                          'name':'honour_task2005_name',     
                          'info':'honour_task2005_info',           
                          'need':[5,6],                    
                          'follow':'honour_task2006',
                          'reward':{'item20074':2,'item036':200},     
                             },
              'honour_task2006':{             
                          'type':'hero_level',       
                          'name':'honour_task2006_name',     
                          'info':'honour_task2006_info',           
                          'need':[5,9],                    
                          'follow':'honour_task2007',
                          'reward':{'item20074':2,'item036':200},     
                             },
              'honour_task2007':{             
                          'type':'hero_level',       
                          'name':'honour_task2007_name',     
                          'info':'honour_task2007_info',           
                          'need':[7,6],                    
                          'follow':'honour_task2008',
                          'reward':{'item20074':3,'item036':25},     
                             },
              'honour_task2008':{             
                          'type':'hero_level',       
                          'name':'honour_task2008_name',     
                          'info':'honour_task2008_info',           
                          'need':[7,9],                    
                          'follow':'honour_task2009',
                          'reward':{'item20074':3,'item036':25},     
                             },
              'honour_task2009':{             
                          'type':'hero_level',       
                          'name':'honour_task2009_name',     
                          'info':'honour_task2009_info',           
                          'need':[7,22],                    
                          'follow':'honour_task2200',
                          'reward':{'item20074':3,'item036':25},     
                             },
              'honour_task2200':{             
                          'type':'hero_level',       
                          'name':'honour_task2200_name',     
                          'info':'honour_task2200_info',           
                          'need':[9,22],                    
                          'follow':'honour_task222',
                          'reward':{'item20074':4,'item036':20},     
                             },
              'honour_task222':{             
                          'type':'hero_level',       
                          'name':'honour_task222_name',     
                          'info':'honour_task222_info',           
                          'need':[9,25],                    
                          'reward':{'item20074':4,'item036':20},     
                             },
              'honour_task202':{             
                          'type':'hero_total',       
                          'name':'honour_task202_name',     
                          'info':'honour_task202_info',           
                          'need':[40],                    
                          'follow':'honour_task202',
                          'reward':{'item20064':2,},     
                             },
              'honour_task202':{             
                          'type':'hero_total',       
                          'name':'honour_task202_name',     
                          'info':'honour_task202_info',           
                          'need':[60],                    
                          'reward':{'item789':50,},     
                             },









},



'challenge_limit_time_box_2':{ 


              'honour_task302':{             
                          'type':'hero_special',       
                          'name':'honour_task302_name',     
                          'info':'honour_task302_info',           
                          'need':[6,'type',0],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task302':{             
                          'type':'hero_special',       
                          'name':'honour_task302_name',     
                          'info':'honour_task302_info',           
                          'need':[6,'type',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task303':{             
                          'type':'hero_special',       
                          'name':'honour_task303_name',     
                          'info':'honour_task303_info',           
                          'need':[6,'type',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task304':{             
                          'type':'hero_special',       
                          'name':'honour_task304_name',     
                          'info':'honour_task304_info',           
                          'need':[6,'rarity',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task305':{             
                          'type':'hero_special',       
                          'name':'honour_task305_name',     
                          'info':'honour_task305_info',           
                          'need':[6,'rarity',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task306':{             
                          'type':'hero_special',       
                          'name':'honour_task306_name',     
                          'info':'honour_task306_info',           
                          'need':[6,'rarity',3],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task307':{             
                          'type':'hero_special',       
                          'name':'honour_task307_name',     
                          'info':'honour_task307_info',           
                          'need':[6,'rarity',4],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task308':{             
                          'type':'hero_special',       
                          'name':'honour_task308_name',     
                          'info':'honour_task308_info',           
                          'need':[6,'army',0],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task309':{             
                          'type':'hero_special',       
                          'name':'honour_task309_name',     
                          'info':'honour_task309_info',           
                          'need':[6,'army',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task3200':{             
                          'type':'hero_special',       
                          'name':'honour_task3200_name',     
                          'info':'honour_task3200_info',           
                          'need':[6,'army',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task322':{             
                          'type':'hero_special',       
                          'name':'honour_task322_name',     
                          'info':'honour_task322_info',           
                          'need':[6,'army',3],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },



},



'challenge_chain_box_3':

{

               'honour_task002':{             
                          'type':'hero_level',       
                          'name':'honour_task002_name',     
                          'info':'honour_task002_info',           
                          'need':[2,5],                    
                          'follow':'honour_task002',
                          'reward':{'title':['title039'],},     
                             },
              'honour_task002':{             
                          'type':'hero_level',       
                          'name':'honour_task002_name',     
                          'info':'honour_task002_info',           
                          'need':[2,200],                    
                          'follow':'honour_task003',
                          'reward':{'title':['title040'],},     
                             },
              'honour_task003':{             
                          'type':'hero_level',       
                          'name':'honour_task003_name',     
                          'info':'honour_task003_info',           
                          'need':[2,25],                    
                          'follow':'honour_task004',
                          'reward':{'title':['title042'],},     
                             },
              'honour_task004':{             
                          'type':'hero_level',       
                          'name':'honour_task004_name',     
                          'info':'honour_task004_info',           
                          'need':[2,20],                    
                          'reward':{'title':['title042'],},     
                             },
              'honour_task2002':{             
                          'type':'hero_level',       
                          'name':'honour_task2002_name',     
                          'info':'honour_task2002_info',           
                          'need':[3,3],                    
                          'follow':'honour_task2002',
                          'reward':{'item20074':2,'item036':5},     
                             },
              'honour_task2002':{             
                          'type':'hero_level',       
                          'name':'honour_task2002_name',     
                          'info':'honour_task2002_info',           
                          'need':[3,6],                    
                          'follow':'honour_task2003',
                          'reward':{'item20074':2,'item036':5},     
                             },
              'honour_task2003':{             
                          'type':'hero_level',       
                          'name':'honour_task2003_name',     
                          'info':'honour_task2003_info',           
                          'need':[3,9],                    
                          'follow':'honour_task2004',
                          'reward':{'item20074':2,'item036':5},     
                             },
              'honour_task2004':{             
                          'type':'hero_level',       
                          'name':'honour_task2004_name',     
                          'info':'honour_task2004_info',           
                          'need':[5,3],                    
                          'follow':'honour_task2005',
                          'reward':{'item20074':2,'item036':200},     
                             },
              'honour_task2005':{             
                          'type':'hero_level',       
                          'name':'honour_task2005_name',     
                          'info':'honour_task2005_info',           
                          'need':[5,6],                    
                          'follow':'honour_task2006',
                          'reward':{'item20074':2,'item036':200},     
                             },
              'honour_task2006':{             
                          'type':'hero_level',       
                          'name':'honour_task2006_name',     
                          'info':'honour_task2006_info',           
                          'need':[5,9],                    
                          'follow':'honour_task2007',
                          'reward':{'item20074':2,'item036':200},     
                             },
              'honour_task2007':{             
                          'type':'hero_level',       
                          'name':'honour_task2007_name',     
                          'info':'honour_task2007_info',           
                          'need':[7,6],                    
                          'follow':'honour_task2008',
                          'reward':{'item20074':3,'item036':25},     
                             },
              'honour_task2008':{             
                          'type':'hero_level',       
                          'name':'honour_task2008_name',     
                          'info':'honour_task2008_info',           
                          'need':[7,9],                    
                          'follow':'honour_task2009',
                          'reward':{'item20074':3,'item036':25},     
                             },
              'honour_task2009':{             
                          'type':'hero_level',       
                          'name':'honour_task2009_name',     
                          'info':'honour_task2009_info',           
                          'need':[7,22],                    
                          'follow':'honour_task2200',
                          'reward':{'item20074':3,'item036':25},     
                             },
              'honour_task2200':{             
                          'type':'hero_level',       
                          'name':'honour_task2200_name',     
                          'info':'honour_task2200_info',           
                          'need':[9,22],                    
                          'follow':'honour_task222',
                          'reward':{'item20074':4,'item036':20},     
                             },
              'honour_task222':{             
                          'type':'hero_level',       
                          'name':'honour_task222_name',     
                          'info':'honour_task222_info',           
                          'need':[9,25],                    
                          'reward':{'item20074':4,'item036':20},     
                             },
              'honour_task202':{             
                          'type':'hero_total',       
                          'name':'honour_task202_name',     
                          'info':'honour_task202_info',           
                          'need':[40],                    
                          'follow':'honour_task202',
                          'reward':{'item20064':2,},     
                             },
              'honour_task202':{             
                          'type':'hero_total',       
                          'name':'honour_task202_name',     
                          'info':'honour_task202_info',           
                          'need':[60],                    
                          'reward':{'item785':50,},     
                             },









},



'challenge_limit_time_box_3':{ 


              'honour_task302':{             
                          'type':'hero_special',       
                          'name':'honour_task302_name',     
                          'info':'honour_task302_info',           
                          'need':[6,'type',0],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task302':{             
                          'type':'hero_special',       
                          'name':'honour_task302_name',     
                          'info':'honour_task302_info',           
                          'need':[6,'type',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task303':{             
                          'type':'hero_special',       
                          'name':'honour_task303_name',     
                          'info':'honour_task303_info',           
                          'need':[6,'type',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task304':{             
                          'type':'hero_special',       
                          'name':'honour_task304_name',     
                          'info':'honour_task304_info',           
                          'need':[6,'rarity',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task305':{             
                          'type':'hero_special',       
                          'name':'honour_task305_name',     
                          'info':'honour_task305_info',           
                          'need':[6,'rarity',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task306':{             
                          'type':'hero_special',       
                          'name':'honour_task306_name',     
                          'info':'honour_task306_info',           
                          'need':[6,'rarity',3],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task307':{             
                          'type':'hero_special',       
                          'name':'honour_task307_name',     
                          'info':'honour_task307_info',           
                          'need':[6,'rarity',4],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task308':{             
                          'type':'hero_special',       
                          'name':'honour_task308_name',     
                          'info':'honour_task308_info',           
                          'need':[6,'army',0],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task309':{             
                          'type':'hero_special',       
                          'name':'honour_task309_name',     
                          'info':'honour_task309_info',           
                          'need':[6,'army',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task3200':{             
                          'type':'hero_special',       
                          'name':'honour_task3200_name',     
                          'info':'honour_task3200_info',           
                          'need':[6,'army',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task322':{             
                          'type':'hero_special',       
                          'name':'honour_task322_name',     
                          'info':'honour_task322_info',           
                          'need':[6,'army',3],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },



},






'challenge_chain_box_4':

{

              'honour_task002':{             
                          'type':'hero_level',       
                          'name':'honour_task002_name',     
                          'info':'honour_task002_info',           
                          'need':[2,5],                    
                          'follow':'honour_task002',
                          'reward':{'title':['title039'],'item046':2},     
                             },
              'honour_task002':{             
                          'type':'hero_level',       
                          'name':'honour_task002_name',     
                          'info':'honour_task002_info',           
                          'need':[2,200],                    
                          'follow':'honour_task003',
                          'reward':{'title':['title040'],'item046':2},     
                             },
              'honour_task003':{             
                          'type':'hero_level',       
                          'name':'honour_task003_name',     
                          'info':'honour_task003_info',           
                          'need':[2,25],                    
                          'follow':'honour_task004',
                          'reward':{'title':['title042'],'item046':2},     
                             },
              'honour_task004':{             
                          'type':'hero_level',       
                          'name':'honour_task004_name',     
                          'info':'honour_task004_info',           
                          'need':[2,20],                    
                          'reward':{'title':['title042'],'item046':2},     
                             },
              'honour_task2002':{             
                          'type':'hero_level',       
                          'name':'honour_task2002_name',     
                          'info':'honour_task2002_info',           
                          'need':[3,3],                    
                          'follow':'honour_task2002',
                          'reward':{'item20074':2,'item036':5},     
                             },
              'honour_task2002':{             
                          'type':'hero_level',       
                          'name':'honour_task2002_name',     
                          'info':'honour_task2002_info',           
                          'need':[3,6],                    
                          'follow':'honour_task2003',
                          'reward':{'item20074':2,'item036':5},     
                             },
              'honour_task2003':{             
                          'type':'hero_level',       
                          'name':'honour_task2003_name',     
                          'info':'honour_task2003_info',           
                          'need':[3,9],                    
                          'follow':'honour_task2004',
                          'reward':{'item20074':2,'item036':5},     
                             },
              'honour_task2004':{             
                          'type':'hero_level',       
                          'name':'honour_task2004_name',     
                          'info':'honour_task2004_info',           
                          'need':[5,3],                    
                          'follow':'honour_task2005',
                          'reward':{'item20074':2,'item036':200},     
                             },
              'honour_task2005':{             
                          'type':'hero_level',       
                          'name':'honour_task2005_name',     
                          'info':'honour_task2005_info',           
                          'need':[5,6],                    
                          'follow':'honour_task2006',
                          'reward':{'item20074':2,'item036':200},     
                             },
              'honour_task2006':{             
                          'type':'hero_level',       
                          'name':'honour_task2006_name',     
                          'info':'honour_task2006_info',           
                          'need':[5,9],                    
                          'follow':'honour_task2007',
                          'reward':{'item20074':2,'item036':200},     
                             },
              'honour_task2007':{             
                          'type':'hero_level',       
                          'name':'honour_task2007_name',     
                          'info':'honour_task2007_info',           
                          'need':[7,6],                    
                          'follow':'honour_task2008',
                          'reward':{'item20074':3,'item036':25},     
                             },
              'honour_task2008':{             
                          'type':'hero_level',       
                          'name':'honour_task2008_name',     
                          'info':'honour_task2008_info',           
                          'need':[7,9],                    
                          'follow':'honour_task2009',
                          'reward':{'item20074':3,'item036':25},     
                             },
              'honour_task2009':{             
                          'type':'hero_level',       
                          'name':'honour_task2009_name',     
                          'info':'honour_task2009_info',           
                          'need':[7,22],                    
                          'follow':'honour_task2200',
                          'reward':{'item20074':3,'item036':25},     
                             },
              'honour_task2200':{             
                          'type':'hero_level',       
                          'name':'honour_task2200_name',     
                          'info':'honour_task2200_info',           
                          'need':[9,22],                    
                          'follow':'honour_task222',
                          'reward':{'item20074':4,'item036':20},     
                             },
              'honour_task222':{             
                          'type':'hero_level',       
                          'name':'honour_task222_name',     
                          'info':'honour_task222_info',           
                          'need':[9,25],                    
                          'reward':{'item20074':4,'item036':20},     
                             },
              'honour_task202':{             
                          'type':'hero_total',       
                          'name':'honour_task202_name',     
                          'info':'honour_task202_info',           
                          'need':[40],                    
                          'follow':'honour_task202',
                          'reward':{'item20064':2,'item046':2},     
                             },
              'honour_task202':{             
                          'type':'hero_total',       
                          'name':'honour_task202_name',     
                          'info':'honour_task202_info',           
                          'need':[60],                    
                          'reward':{'item788':50,'item046':4},     
                             },









},



'challenge_limit_time_box_4':{ 


              'honour_task302':{             
                          'type':'hero_special',       
                          'name':'honour_task302_name',     
                          'info':'honour_task302_info',           
                          'need':[6,'type',0],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task302':{             
                          'type':'hero_special',       
                          'name':'honour_task302_name',     
                          'info':'honour_task302_info',           
                          'need':[6,'type',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task303':{             
                          'type':'hero_special',       
                          'name':'honour_task303_name',     
                          'info':'honour_task303_info',           
                          'need':[6,'type',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task304':{             
                          'type':'hero_special',       
                          'name':'honour_task304_name',     
                          'info':'honour_task304_info',           
                          'need':[6,'rarity',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task305':{             
                          'type':'hero_special',       
                          'name':'honour_task305_name',     
                          'info':'honour_task305_info',           
                          'need':[6,'rarity',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task306':{             
                          'type':'hero_special',       
                          'name':'honour_task306_name',     
                          'info':'honour_task306_info',           
                          'need':[6,'rarity',3],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task307':{             
                          'type':'hero_special',       
                          'name':'honour_task307_name',     
                          'info':'honour_task307_info',           
                          'need':[6,'rarity',4],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task308':{             
                          'type':'hero_special',       
                          'name':'honour_task308_name',     
                          'info':'honour_task308_info',           
                          'need':[6,'army',0],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task309':{             
                          'type':'hero_special',       
                          'name':'honour_task309_name',     
                          'info':'honour_task309_info',           
                          'need':[6,'army',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task3200':{             
                          'type':'hero_special',       
                          'name':'honour_task3200_name',     
                          'info':'honour_task3200_info',           
                          'need':[6,'army',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task322':{             
                          'type':'hero_special',       
                          'name':'honour_task322_name',     
                          'info':'honour_task322_info',           
                          'need':[6,'army',3],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },



},



'challenge_chain_box_5':

{

              'honour_task002':{             
                          'type':'hero_level',       
                          'name':'honour_task002_name',     
                          'info':'honour_task002_info',           
                          'need':[2,5],                    
                          'follow':'honour_task002',
                          'reward':{'title':['title039'],'item046':2},     
                             },
              'honour_task002':{             
                          'type':'hero_level',       
                          'name':'honour_task002_name',     
                          'info':'honour_task002_info',           
                          'need':[2,200],                    
                          'follow':'honour_task003',
                          'reward':{'title':['title040'],'item046':2},     
                             },
              'honour_task003':{             
                          'type':'hero_level',       
                          'name':'honour_task003_name',     
                          'info':'honour_task003_info',           
                          'need':[2,25],                    
                          'follow':'honour_task004',
                          'reward':{'title':['title042'],'item046':2},     
                             },
              'honour_task004':{             
                          'type':'hero_level',       
                          'name':'honour_task004_name',     
                          'info':'honour_task004_info',           
                          'need':[2,20],                    
                          'reward':{'title':['title042'],'item046':2},     
                             },
              'honour_task2002':{             
                          'type':'hero_level',       
                          'name':'honour_task2002_name',     
                          'info':'honour_task2002_info',           
                          'need':[3,3],                    
                          'follow':'honour_task2002',
                          'reward':{'item20074':2,'item036':5},     
                             },
              'honour_task2002':{             
                          'type':'hero_level',       
                          'name':'honour_task2002_name',     
                          'info':'honour_task2002_info',           
                          'need':[3,6],                    
                          'follow':'honour_task2003',
                          'reward':{'item20074':2,'item036':5},     
                             },
              'honour_task2003':{             
                          'type':'hero_level',       
                          'name':'honour_task2003_name',     
                          'info':'honour_task2003_info',           
                          'need':[3,9],                    
                          'follow':'honour_task2004',
                          'reward':{'item20074':2,'item036':5},     
                             },
              'honour_task2004':{             
                          'type':'hero_level',       
                          'name':'honour_task2004_name',     
                          'info':'honour_task2004_info',           
                          'need':[5,3],                    
                          'follow':'honour_task2005',
                          'reward':{'item20074':2,'item036':200},     
                             },
              'honour_task2005':{             
                          'type':'hero_level',       
                          'name':'honour_task2005_name',     
                          'info':'honour_task2005_info',           
                          'need':[5,6],                    
                          'follow':'honour_task2006',
                          'reward':{'item20074':2,'item036':200},     
                             },
              'honour_task2006':{             
                          'type':'hero_level',       
                          'name':'honour_task2006_name',     
                          'info':'honour_task2006_info',           
                          'need':[5,9],                    
                          'follow':'honour_task2007',
                          'reward':{'item20074':2,'item036':200},     
                             },
              'honour_task2007':{             
                          'type':'hero_level',       
                          'name':'honour_task2007_name',     
                          'info':'honour_task2007_info',           
                          'need':[7,6],                    
                          'follow':'honour_task2008',
                          'reward':{'item20074':3,'item036':25},     
                             },
              'honour_task2008':{             
                          'type':'hero_level',       
                          'name':'honour_task2008_name',     
                          'info':'honour_task2008_info',           
                          'need':[7,9],                    
                          'follow':'honour_task2009',
                          'reward':{'item20074':3,'item036':25},     
                             },
              'honour_task2009':{             
                          'type':'hero_level',       
                          'name':'honour_task2009_name',     
                          'info':'honour_task2009_info',           
                          'need':[7,22],                    
                          'follow':'honour_task2200',
                          'reward':{'item20074':3,'item036':25},     
                             },
              'honour_task2200':{             
                          'type':'hero_level',       
                          'name':'honour_task2200_name',     
                          'info':'honour_task2200_info',           
                          'need':[9,22],                    
                          'follow':'honour_task222',
                          'reward':{'item20074':4,'item036':20},     
                             },
              'honour_task222':{             
                          'type':'hero_level',       
                          'name':'honour_task222_name',     
                          'info':'honour_task222_info',           
                          'need':[9,25],                    
                          'reward':{'item20074':4,'item036':20},     
                             },
              'honour_task202':{             
                          'type':'hero_total',       
                          'name':'honour_task202_name',     
                          'info':'honour_task202_info',           
                          'need':[40],                    
                          'follow':'honour_task202',
                          'reward':{'item20064':2,'item046':2},     
                             },
              'honour_task202':{             
                          'type':'hero_total',       
                          'name':'honour_task202_name',     
                          'info':'honour_task202_info',           
                          'need':[60],                    
                          'reward':{'item788':50,'item046':4},     
                             },









},



'challenge_limit_time_box_5':{ 


              'honour_task302':{             
                          'type':'hero_special',       
                          'name':'honour_task302_name',     
                          'info':'honour_task302_info',           
                          'need':[6,'type',0],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task302':{             
                          'type':'hero_special',       
                          'name':'honour_task302_name',     
                          'info':'honour_task302_info',           
                          'need':[6,'type',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task303':{             
                          'type':'hero_special',       
                          'name':'honour_task303_name',     
                          'info':'honour_task303_info',           
                          'need':[6,'type',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task304':{             
                          'type':'hero_special',       
                          'name':'honour_task304_name',     
                          'info':'honour_task304_info',           
                          'need':[6,'rarity',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task305':{             
                          'type':'hero_special',       
                          'name':'honour_task305_name',     
                          'info':'honour_task305_info',           
                          'need':[6,'rarity',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task306':{             
                          'type':'hero_special',       
                          'name':'honour_task306_name',     
                          'info':'honour_task306_info',           
                          'need':[6,'rarity',3],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task307':{             
                          'type':'hero_special',       
                          'name':'honour_task307_name',     
                          'info':'honour_task307_info',           
                          'need':[6,'rarity',4],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task308':{             
                          'type':'hero_special',       
                          'name':'honour_task308_name',     
                          'info':'honour_task308_info',           
                          'need':[6,'army',0],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task309':{             
                          'type':'hero_special',       
                          'name':'honour_task309_name',     
                          'info':'honour_task309_info',           
                          'need':[6,'army',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task3200':{             
                          'type':'hero_special',       
                          'name':'honour_task3200_name',     
                          'info':'honour_task3200_info',           
                          'need':[6,'army',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task322':{             
                          'type':'hero_special',       
                          'name':'honour_task322_name',     
                          'info':'honour_task322_info',           
                          'need':[6,'army',3],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },



},


'challenge_chain_box_6':

{

              'honour_task002':{             
                          'type':'hero_level',       
                          'name':'honour_task002_name',     
                          'info':'honour_task002_info',           
                          'need':[2,5],                    
                          'follow':'honour_task002',
                          'reward':{'title':['title039'],'item046':2},     
                             },
              'honour_task002':{             
                          'type':'hero_level',       
                          'name':'honour_task002_name',     
                          'info':'honour_task002_info',           
                          'need':[2,200],                    
                          'follow':'honour_task003',
                          'reward':{'title':['title040'],'item046':2},     
                             },
              'honour_task003':{             
                          'type':'hero_level',       
                          'name':'honour_task003_name',     
                          'info':'honour_task003_info',           
                          'need':[2,25],                    
                          'follow':'honour_task004',
                          'reward':{'title':['title042'],'item046':2},     
                             },
              'honour_task004':{             
                          'type':'hero_level',       
                          'name':'honour_task004_name',     
                          'info':'honour_task004_info',           
                          'need':[2,20],                    
                          'reward':{'title':['title042'],'item046':2},     
                             },
              'honour_task2002':{             
                          'type':'hero_level',       
                          'name':'honour_task2002_name',     
                          'info':'honour_task2002_info',           
                          'need':[3,3],                    
                          'follow':'honour_task2002',
                          'reward':{'item20074':2,'item036':5},     
                             },
              'honour_task2002':{             
                          'type':'hero_level',       
                          'name':'honour_task2002_name',     
                          'info':'honour_task2002_info',           
                          'need':[3,6],                    
                          'follow':'honour_task2003',
                          'reward':{'item20074':2,'item036':5},     
                             },
              'honour_task2003':{             
                          'type':'hero_level',       
                          'name':'honour_task2003_name',     
                          'info':'honour_task2003_info',           
                          'need':[3,9],                    
                          'follow':'honour_task2004',
                          'reward':{'item20074':2,'item036':5},     
                             },
              'honour_task2004':{             
                          'type':'hero_level',       
                          'name':'honour_task2004_name',     
                          'info':'honour_task2004_info',           
                          'need':[5,3],                    
                          'follow':'honour_task2005',
                          'reward':{'item20074':2,'item036':200},     
                             },
              'honour_task2005':{             
                          'type':'hero_level',       
                          'name':'honour_task2005_name',     
                          'info':'honour_task2005_info',           
                          'need':[5,6],                    
                          'follow':'honour_task2006',
                          'reward':{'item20074':2,'item036':200},     
                             },
              'honour_task2006':{             
                          'type':'hero_level',       
                          'name':'honour_task2006_name',     
                          'info':'honour_task2006_info',           
                          'need':[5,9],                    
                          'follow':'honour_task2007',
                          'reward':{'item20074':2,'item036':200},     
                             },
              'honour_task2007':{             
                          'type':'hero_level',       
                          'name':'honour_task2007_name',     
                          'info':'honour_task2007_info',           
                          'need':[7,6],                    
                          'follow':'honour_task2008',
                          'reward':{'item20074':3,'item036':25},     
                             },
              'honour_task2008':{             
                          'type':'hero_level',       
                          'name':'honour_task2008_name',     
                          'info':'honour_task2008_info',           
                          'need':[7,9],                    
                          'follow':'honour_task2009',
                          'reward':{'item20074':3,'item036':25},     
                             },
              'honour_task2009':{             
                          'type':'hero_level',       
                          'name':'honour_task2009_name',     
                          'info':'honour_task2009_info',           
                          'need':[7,22],                    
                          'follow':'honour_task2200',
                          'reward':{'item20074':3,'item036':25},     
                             },
              'honour_task2200':{             
                          'type':'hero_level',       
                          'name':'honour_task2200_name',     
                          'info':'honour_task2200_info',           
                          'need':[9,22],                    
                          'follow':'honour_task222',
                          'reward':{'item20074':4,'item036':20},     
                             },
              'honour_task222':{             
                          'type':'hero_level',       
                          'name':'honour_task222_name',     
                          'info':'honour_task222_info',           
                          'need':[9,25],                    
                          'reward':{'item20074':4,'item036':20},     
                             },
              'honour_task202':{             
                          'type':'hero_total',       
                          'name':'honour_task202_name',     
                          'info':'honour_task202_info',           
                          'need':[40],                    
                          'follow':'honour_task202',
                          'reward':{'item20064':2,'item046':2},     
                             },
              'honour_task202':{             
                          'type':'hero_total',       
                          'name':'honour_task202_name',     
                          'info':'honour_task202_info',           
                          'need':[60],                    
                          'reward':{'item7002':50,'item046':4},     
                             },









},



'challenge_limit_time_box_6':{ 


              'honour_task302':{             
                          'type':'hero_special',       
                          'name':'honour_task302_name',     
                          'info':'honour_task302_info',           
                          'need':[6,'type',0],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task302':{             
                          'type':'hero_special',       
                          'name':'honour_task302_name',     
                          'info':'honour_task302_info',           
                          'need':[6,'type',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task303':{             
                          'type':'hero_special',       
                          'name':'honour_task303_name',     
                          'info':'honour_task303_info',           
                          'need':[6,'type',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task304':{             
                          'type':'hero_special',       
                          'name':'honour_task304_name',     
                          'info':'honour_task304_info',           
                          'need':[6,'rarity',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task305':{             
                          'type':'hero_special',       
                          'name':'honour_task305_name',     
                          'info':'honour_task305_info',           
                          'need':[6,'rarity',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task306':{             
                          'type':'hero_special',       
                          'name':'honour_task306_name',     
                          'info':'honour_task306_info',           
                          'need':[6,'rarity',3],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task307':{             
                          'type':'hero_special',       
                          'name':'honour_task307_name',     
                          'info':'honour_task307_info',           
                          'need':[6,'rarity',4],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task308':{             
                          'type':'hero_special',       
                          'name':'honour_task308_name',     
                          'info':'honour_task308_info',           
                          'need':[6,'army',0],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task309':{             
                          'type':'hero_special',       
                          'name':'honour_task309_name',     
                          'info':'honour_task309_info',           
                          'need':[6,'army',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task3200':{             
                          'type':'hero_special',       
                          'name':'honour_task3200_name',     
                          'info':'honour_task3200_info',           
                          'need':[6,'army',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task322':{             
                          'type':'hero_special',       
                          'name':'honour_task322_name',     
                          'info':'honour_task322_info',           
                          'need':[6,'army',3],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },



},







'honour_exp_2':  
[600000,999750,20000000,2200000,2400000,2600000,2999750,2000000,2200000,2400000,2600000,2999750,3000000,3200000,3400000,3600000,3999750,4000000,4200000,4400000,],

'honour_exp_num_2':[   

[200000000,0.4,],
[25000000,0.5,],
[20000000,0.6,],
[25000000,0.7,],
[30000000,0.8,],
[95000000,0.9,],
[75000000,2,],
[60000000,2.2,],
[65000000,2.2,],
[70000000,2.3,],
[75000000,2.4,],
[99975000,2.5,],


],


'honour_reward_2':[5,500,'random_honour_reward'],  

'honour_exp_0':  
[600000,999750,20000000,2200000,2400000,2600000,2999750,2000000,2200000,2400000,2600000,2999750,3000000,3200000,3400000,36000000,3999750,4000000,4200000,4400000,],


'honour_exp_num_0':[   

[200000000,0.4,],
[25000000,0.5,],
[20000000,0.6,],
[25000000,0.7,],
[30000000,0.8,],
[95000000,0.9,],
[75000000,2,],
[60000000,2.2,],
[65000000,2.2,],
[70000000,2.3,],
[75000000,2.4,],
[99975000,2.5,],


],


'honour_reward_0':[5,500,'random_honour_reward'],  







'challenge_chain_box_0':

{

               'honour_task002':{             
                          'type':'hero_level',       
                          'name':'honour_task002_name',     
                          'info':'honour_task002_info',           
                          'need':[2,5],                    
                          'follow':'honour_task002',
                          'reward':{'title':['title039'],},     
                             },
              'honour_task002':{             
                          'type':'hero_level',       
                          'name':'honour_task002_name',     
                          'info':'honour_task002_info',           
                          'need':[2,200],                    
                          'follow':'honour_task003',
                          'reward':{'title':['title040'],},     
                             },
              'honour_task003':{             
                          'type':'hero_level',       
                          'name':'honour_task003_name',     
                          'info':'honour_task003_info',           
                          'need':[2,25],                    
                          'follow':'honour_task004',
                          'reward':{'title':['title042'],},     
                             },
              'honour_task004':{             
                          'type':'hero_level',       
                          'name':'honour_task004_name',     
                          'info':'honour_task004_info',           
                          'need':[2,20],                    
                          'reward':{'title':['title042'],},     
                             },
              'honour_task2002':{             
                          'type':'hero_level',       
                          'name':'honour_task2002_name',     
                          'info':'honour_task2002_info',           
                          'need':[3,3],                    
                          'follow':'honour_task2002',
                          'reward':{'item20074':2,'item036':5},     
                             },
              'honour_task2002':{             
                          'type':'hero_level',       
                          'name':'honour_task2002_name',     
                          'info':'honour_task2002_info',           
                          'need':[3,6],                    
                          'follow':'honour_task2003',
                          'reward':{'item20074':2,'item036':5},     
                             },
              'honour_task2003':{             
                          'type':'hero_level',       
                          'name':'honour_task2003_name',     
                          'info':'honour_task2003_info',           
                          'need':[3,9],                    
                          'follow':'honour_task2004',
                          'reward':{'item20074':2,'item036':5},     
                             },
              'honour_task2004':{             
                          'type':'hero_level',       
                          'name':'honour_task2004_name',     
                          'info':'honour_task2004_info',           
                          'need':[5,3],                    
                          'follow':'honour_task2005',
                          'reward':{'item20074':2,'item036':200},     
                             },
              'honour_task2005':{             
                          'type':'hero_level',       
                          'name':'honour_task2005_name',     
                          'info':'honour_task2005_info',           
                          'need':[5,6],                    
                          'follow':'honour_task2006',
                          'reward':{'item20074':2,'item036':200},     
                             },
              'honour_task2006':{             
                          'type':'hero_level',       
                          'name':'honour_task2006_name',     
                          'info':'honour_task2006_info',           
                          'need':[5,9],                    
                          'follow':'honour_task2007',
                          'reward':{'item20074':2,'item036':200},     
                             },
              'honour_task2007':{             
                          'type':'hero_level',       
                          'name':'honour_task2007_name',     
                          'info':'honour_task2007_info',           
                          'need':[7,6],                    
                          'follow':'honour_task2008',
                          'reward':{'item20074':3,'item036':25},     
                             },
              'honour_task2008':{             
                          'type':'hero_level',       
                          'name':'honour_task2008_name',     
                          'info':'honour_task2008_info',           
                          'need':[7,9],                    
                          'follow':'honour_task2009',
                          'reward':{'item20074':3,'item036':25},     
                             },
              'honour_task2009':{             
                          'type':'hero_level',       
                          'name':'honour_task2009_name',     
                          'info':'honour_task2009_info',           
                          'need':[7,22],                    
                          'follow':'honour_task2200',
                          'reward':{'item20074':3,'item036':25},     
                             },
              'honour_task2200':{             
                          'type':'hero_level',       
                          'name':'honour_task2200_name',     
                          'info':'honour_task2200_info',           
                          'need':[9,22],                    
                          'follow':'honour_task222',
                          'reward':{'item20074':4,'item036':20},     
                             },
              'honour_task222':{             
                          'type':'hero_level',       
                          'name':'honour_task222_name',     
                          'info':'honour_task222_info',           
                          'need':[9,25],                    
                          'reward':{'item20074':4,'item036':20},     
                             },
              'honour_task202':{             
                          'type':'hero_total',       
                          'name':'honour_task202_name',     
                          'info':'honour_task202_info',           
                          'need':[40],                    
                          'follow':'honour_task202',
                          'reward':{'item20064':2,},     
                             },
              'honour_task202':{             
                          'type':'hero_total',       
                          'name':'honour_task202_name',     
                          'info':'honour_task202_info',           
                          'need':[60],                    
                          'reward':{'item789':50,},     
                             },









},



'challenge_limit_time_box_0':{ 


              'honour_task302':{             
                          'type':'hero_special',       
                          'name':'honour_task302_name',     
                          'info':'honour_task302_info',           
                          'need':[6,'type',0],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task302':{             
                          'type':'hero_special',       
                          'name':'honour_task302_name',     
                          'info':'honour_task302_info',           
                          'need':[6,'type',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task303':{             
                          'type':'hero_special',       
                          'name':'honour_task303_name',     
                          'info':'honour_task303_info',           
                          'need':[6,'type',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task304':{             
                          'type':'hero_special',       
                          'name':'honour_task304_name',     
                          'info':'honour_task304_info',           
                          'need':[6,'rarity',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task305':{             
                          'type':'hero_special',       
                          'name':'honour_task305_name',     
                          'info':'honour_task305_info',           
                          'need':[6,'rarity',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task306':{             
                          'type':'hero_special',       
                          'name':'honour_task306_name',     
                          'info':'honour_task306_info',           
                          'need':[6,'rarity',3],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task307':{             
                          'type':'hero_special',       
                          'name':'honour_task307_name',     
                          'info':'honour_task307_info',           
                          'need':[6,'rarity',4],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task308':{             
                          'type':'hero_special',       
                          'name':'honour_task308_name',     
                          'info':'honour_task308_info',           
                          'need':[6,'army',0],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task309':{             
                          'type':'hero_special',       
                          'name':'honour_task309_name',     
                          'info':'honour_task309_info',           
                          'need':[6,'army',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task3200':{             
                          'type':'hero_special',       
                          'name':'honour_task3200_name',     
                          'info':'honour_task3200_info',           
                          'need':[6,'army',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task322':{             
                          'type':'hero_special',       
                          'name':'honour_task322_name',     
                          'info':'honour_task322_info',           
                          'need':[6,'army',3],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },



},
'challenge_chain_box_2':

{

                 'honour_task002':{             
                          'type':'hero_level',       
                          'name':'honour_task002_name',     
                          'info':'honour_task002_info',           
                          'need':[2,5],                    
                          'follow':'honour_task002',
                          'reward':{'title':['title039'],'item046':2},     
                             },
              'honour_task002':{             
                          'type':'hero_level',       
                          'name':'honour_task002_name',     
                          'info':'honour_task002_info',           
                          'need':[2,200],                    
                          'follow':'honour_task003',
                          'reward':{'title':['title040'],'item046':2},     
                             },
              'honour_task003':{             
                          'type':'hero_level',       
                          'name':'honour_task003_name',     
                          'info':'honour_task003_info',           
                          'need':[2,25],                    
                          'follow':'honour_task004',
                          'reward':{'title':['title042'],'item046':2},     
                             },
              'honour_task004':{             
                          'type':'hero_level',       
                          'name':'honour_task004_name',     
                          'info':'honour_task004_info',           
                          'need':[2,20],                    
                          'reward':{'title':['title042'],'item046':2},     
                             },
              'honour_task2002':{             
                          'type':'hero_level',       
                          'name':'honour_task2002_name',     
                          'info':'honour_task2002_info',           
                          'need':[3,3],                    
                          'follow':'honour_task2002',
                          'reward':{'item20074':2,'item036':5},     
                             },
              'honour_task2002':{             
                          'type':'hero_level',       
                          'name':'honour_task2002_name',     
                          'info':'honour_task2002_info',           
                          'need':[3,6],                    
                          'follow':'honour_task2003',
                          'reward':{'item20074':2,'item036':5},     
                             },
              'honour_task2003':{             
                          'type':'hero_level',       
                          'name':'honour_task2003_name',     
                          'info':'honour_task2003_info',           
                          'need':[3,9],                    
                          'follow':'honour_task2004',
                          'reward':{'item20074':2,'item036':5},     
                             },
              'honour_task2004':{             
                          'type':'hero_level',       
                          'name':'honour_task2004_name',     
                          'info':'honour_task2004_info',           
                          'need':[5,3],                    
                          'follow':'honour_task2005',
                          'reward':{'item20074':2,'item036':200},     
                             },
              'honour_task2005':{             
                          'type':'hero_level',       
                          'name':'honour_task2005_name',     
                          'info':'honour_task2005_info',           
                          'need':[5,6],                    
                          'follow':'honour_task2006',
                          'reward':{'item20074':2,'item036':200},     
                             },
              'honour_task2006':{             
                          'type':'hero_level',       
                          'name':'honour_task2006_name',     
                          'info':'honour_task2006_info',           
                          'need':[5,9],                    
                          'follow':'honour_task2007',
                          'reward':{'item20074':2,'item036':200},     
                             },
              'honour_task2007':{             
                          'type':'hero_level',       
                          'name':'honour_task2007_name',     
                          'info':'honour_task2007_info',           
                          'need':[7,6],                    
                          'follow':'honour_task2008',
                          'reward':{'item20074':3,'item036':25},     
                             },
              'honour_task2008':{             
                          'type':'hero_level',       
                          'name':'honour_task2008_name',     
                          'info':'honour_task2008_info',           
                          'need':[7,9],                    
                          'follow':'honour_task2009',
                          'reward':{'item20074':3,'item036':25},     
                             },
              'honour_task2009':{             
                          'type':'hero_level',       
                          'name':'honour_task2009_name',     
                          'info':'honour_task2009_info',           
                          'need':[7,22],                    
                          'follow':'honour_task2200',
                          'reward':{'item20074':3,'item036':25},     
                             },
              'honour_task2200':{             
                          'type':'hero_level',       
                          'name':'honour_task2200_name',     
                          'info':'honour_task2200_info',           
                          'need':[9,22],                    
                          'follow':'honour_task222',
                          'reward':{'item20074':4,'item036':20},     
                             },
              'honour_task222':{             
                          'type':'hero_level',       
                          'name':'honour_task222_name',     
                          'info':'honour_task222_info',           
                          'need':[9,25],                    
                          'reward':{'item20074':4,'item036':20},     
                             },
              'honour_task202':{             
                          'type':'hero_total',       
                          'name':'honour_task202_name',     
                          'info':'honour_task202_info',           
                          'need':[40],                    
                          'follow':'honour_task202',
                          'reward':{'item20064':2,'item046':2},     
                             },
              'honour_task202':{             
                          'type':'hero_total',       
                          'name':'honour_task202_name',     
                          'info':'honour_task202_info',           
                          'need':[60],                    
                          'reward':{'item785':50,'item046':4},     
                             },










},



'challenge_limit_time_box_2':{ 


              'honour_task302':{             
                          'type':'hero_special',       
                          'name':'honour_task302_name',     
                          'info':'honour_task302_info',           
                          'need':[6,'type',0],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task302':{             
                          'type':'hero_special',       
                          'name':'honour_task302_name',     
                          'info':'honour_task302_info',           
                          'need':[6,'type',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task303':{             
                          'type':'hero_special',       
                          'name':'honour_task303_name',     
                          'info':'honour_task303_info',           
                          'need':[6,'type',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task304':{             
                          'type':'hero_special',       
                          'name':'honour_task304_name',     
                          'info':'honour_task304_info',           
                          'need':[6,'rarity',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task305':{             
                          'type':'hero_special',       
                          'name':'honour_task305_name',     
                          'info':'honour_task305_info',           
                          'need':[6,'rarity',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task306':{             
                          'type':'hero_special',       
                          'name':'honour_task306_name',     
                          'info':'honour_task306_info',           
                          'need':[6,'rarity',3],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task307':{             
                          'type':'hero_special',       
                          'name':'honour_task307_name',     
                          'info':'honour_task307_info',           
                          'need':[6,'rarity',4],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task308':{             
                          'type':'hero_special',       
                          'name':'honour_task308_name',     
                          'info':'honour_task308_info',           
                          'need':[6,'army',0],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task309':{             
                          'type':'hero_special',       
                          'name':'honour_task309_name',     
                          'info':'honour_task309_info',           
                          'need':[6,'army',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task3200':{             
                          'type':'hero_special',       
                          'name':'honour_task3200_name',     
                          'info':'honour_task3200_info',           
                          'need':[6,'army',2],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },
              'honour_task322':{             
                          'type':'hero_special',       
                          'name':'honour_task322_name',     
                          'info':'honour_task322_info',           
                          'need':[6,'army',3],                    
                          'reward':{'item20048':2,'item20047':200},     
                             },



},










































}