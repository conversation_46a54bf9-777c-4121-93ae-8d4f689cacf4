{
































































	'lv_guide_start':{
		'3':'office_guide',		
		'4':'catchhero_guide',    
		'5':'guild_guide',		
		'6':'pk_guide',		
		'8':'resolve_guide',	
		'9':'estate_guide',	
		'200':'equip_guide',	
		'22':'science_guide', 	
		'22':'shogun_guide', 
		'25':'lookstar_guide',	
		'37':'turtle_guide',	
		'38':'peach_guide',	
	},
    'new_install':{
	'g002':[
		{

			'image': {
				'url': 'sj2000',			
				'txt': 'guide002',		
				'next': 2,			
			},
			'sound':'guidetalk02',
		},
		{			
			'talks': [
				['hero702', 'guide_npc002', 'guide_talk002'],    
				['hero403', 'guide_npc002', 'guide_talk002'],
				['hero702', 'guide_npc002', 'guide_talk003'],
			]			
		},
		{
			'recruit_hero':'hero702',

		},
		{
			'delay':2500,
		},
		{
			'click':{},
		},
	],
	'g002': [		
		{
			'talks': [
				['hero702', 'guide_npc002', 'guide_talk004'],   
			]	
		},
		{
			'click':{
				'shade': 2,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'hero_list',
				'objName':'0',
			},			
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 0,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn_2',	
			},	
			'sound':'guidetalk02',
		},	
		{
			'save':{},
		},
		{
			'talks': [
				['hero702', 'guide_npc002', 'guide_talk005'], 
			]	
		},
	],
	'g003': [
		{
			'talks': [
				['hero403', 'guide_npc002', 'guide_talk006'],
				['hero702', 'guide_npc002', 'guide_talk007'],				
			]	
		},
		{
			'goto':{
				'type': 2,	
				'cityID':['246','32','953'],
			},
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'city',		
				'objName': '0',	             
			},
			'sound':'guidetalk03',	
		},
		{
			'click':{},    
		},
		{
			'click':{   
				'shade': 0,					
				'shield': 2,				
				'arrow': 0,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn2',	
			},	
		},
		{
			'save':{},
		},
	],
	'g004':[
		[
			{
				'goto':{
					'type': 2,	
					'cityID':['246','32','953'],
				},
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 0,					
					'rect': 2,					
					'type': 'city',		
					'objName': '0',	             
				},	
			},
		],
		
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 0,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn2',	
			},	
		},
	
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'city',		
				'objName': '3',	   
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn',	
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'list_0',	      
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn_send',	
			},	
		},
		{
			'save':{},
		},
		{
			'in_battle':{},	
		},
		{
			'click':{},	
		},
		{
			'click':{},	
		},
	],	
	'g005':[	
		{
			'goto':{
				'type': 2,	
				'cityID':['246','32','953'],
			},
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'city',		
				'objName': '0',	      
			},	
		},
		{
			'click':{},	
		},
		{
			'click':{ 
				'shade': 0,					
				'shield': 2,				
				'arrow': 0,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn2',	
			},	
		},
		{
			'save':{},
		},
		{
			'click':{	
			},	
		},
		{  
			'click':{	
			},	
			'sound':'guidetalk04',
		},
	],
	'g006': [
		{
			'talks': [
				['hero702', 'guide_npc002', 'guide_talk008'],				
			]	
		},
		{
			'click':{
				'shade': 2,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'ui',		
				'objName': 'btn_hero',	      
			},	
		},
		{
			'talks': [
				['hero702', 'guide_npc002', 'guide_talk009'],		
				['hero702', 'guide_npc003', 'guide_talk0200'],			
			]	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',		
				'objName': 'list_0',	      
			},	
		},
		{
			'save':{},
		},
		{
			'delay':2500,
		},
		{
			'click':{},	
		},
	],
	'g007': [
		[    
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'ui',		
					'objName': 'btn_hero',	      
				},	
			},
		],
		{
			'talks': [
				['hero702', 'guide_npc002', 'guide_talk022'],			
			]	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',		
				'objName': 'list_2',	      
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',		
				'objName': 'btn_lv',	      
			},	
		},
		{
			'delay':500,
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'list_0',	      
			},	
			'sound':'guidetalk05',
		},
		{
			'save':{},
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn_close',	      
			},	
		},
		{
			'talks': [
				['hero702', 'guide_npc002', 'guide_talk022'],			
			]	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',		
				'objName': 'btn_close',	      
			},	
		},
	],
	'g008': [              
		[    
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'ui',		
					'objName': 'btn_hero',	      
				},	
			},
		],
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',		
				'objName': 'list_0',	      
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',		
				'objName': 'btn_lv',	      
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'list_0',	      
			},	
		},
		{
			'save':{},
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn_close',	      
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',		
				'objName': 'btn_close',	      
			},	
		},		
	],
	'g009': [			
		[    
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'ui',		
					'objName': 'btn_hero',	      
				},	
			},
		],
		{
			'talks': [
				['hero702', 'guide_npc002', 'guide_talk023'],			
				['hero702', 'guide_npc003', 'guide_talk024'],	
			]	
		},	
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',		
				'objName': 'list_0',	      
			},		
		},	
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',		
				'objName': 'tab_2',	      
			},	
			'sound':'guidetalk06',
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',		
				'objName': 'hero0',	      
			},	
		},
		{
			'talks': [
				['hero702', 'guide_npc002', 'guide_talk025'],			
			]	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',		
				'objName': 'btn_ok',	      
			},	
		},
		{
			'save':{},
		},
		{
			'talks': [
				['hero702', 'guide_npc002', 'guide_talk026'],			
			]	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',		
				'objName': 'btn_close',	      
			},	
		},	
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',		
				'objName': 'btn_close',	      
			},	
		},	
	],
	'g0200':[			
		{
			'talks': [
				['hero403', 'guide_npc002', 'guide_talk045'],			
			],
			'sound':'guidetalk07',	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'ui',
				'objName':'taskPanel',
			},			
		},
		{
			'save':{},
		},
		{
			'click':{},			
		},
	],
	'g022': [ 		
		{
			'talks': [
				['hero403', 'guide_npc002', 'guide_talk027'],	
				['hero702', 'guide_npc003', 'guide_talk028'],	
			],	
			'sound':'guidetalk08',
		},
		{
			'click':{
				'shade': 2,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'hero_list',
				'objName':'2',
			},			
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 0,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn_2',	
			},	
		},
		{
			'save':{},
		},
	],
	'g022':[			
		{
			'talks': [
				['hero702', 'guide_npc002', 'guide_talk046'],			
			]	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'ui',
				'objName':'taskPanel',
			},			
		},
		{
			'save':{},
		},
		{
			'click':{},			
		},
	],	
	'g023':[
		{
			'talks': [
				['hero702', 'guide_npc002', 'guide_talk029'],	
			]	
		},
		{
			'click':{		
				'shade': 2,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'hero_list',
				'objName':'0',
			},	
					
		},
		{
			'delay':500,
		},
		{
			'click':{		
				'shade': 0,					
				'shield': 2,				
				'arrow': 0,					
				'rect': 2,					
				'type': 'panel',
				'objName':'btn_2',
			},	
			'sound':'guidetalk09',		
		},
		{
			'save':{},
		},
	],
	'g024': [			
		{
			'talks': [
				['hero702', 'guide_npc002', 'guide_talk020'],	
			]	
		},
		{
			'goto':{
				'type': 2,	
				'cityID':['244','62','375'],
				'state':2,
				'secondMenu':2,						
			},
		},		
		{
			'click':{		
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'city',
				'objName':['244','62','375'],
			},			
		},
		{
			'click':{		
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'city',
				'objName':'2',
			},			
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'list_0',	      
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'list_2',	      
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn_send',	
			},	
		},
		{
			'save':{},
		},
	],
	'g025':[				
		{
			'talks': [
				['hero403', 'guide_npc002', 'guide_talk022'],	
			],	
			'sound':'guidetalk200',
		},	
		{
			'click':{
				'shade': 2,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'ui',		
				'objName': 'btn_map2',	
			},	
		},	
		{
			'talks': [
				['hero403', 'guide_npc002', 'guide_talk022'],	
			]	
		},
		{
			'goto':{
				'type': 2,	
				'buildingID':'building002',
				'state':2,
			
			},
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'building',		
				'objName': 'building002',	
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'building',		
				'objName': '2',	
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn_cd',	
			},	
			'sound':'guidetalk22',
		},
		{
			'save':{},
		},
		{
			'click':{},	
		},
		{
			'click':{},	
		},
		{
			'click':{},		
		},		
	],
	'g026':[			
		[
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'ui',		
					'objName': 'btn_map2',	
				},	
			},
		],
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'ui',
				'objName':'taskPanel',
			},			
		},
		{
			'save':{},
		},
		{
			'click':{},			
		},
	],	
	'g027':[			
		[
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'ui',		
					'objName': 'btn_map2',	
				},	
			},
		],
		{
			'talks': [
				['hero403', 'guide_npc002', 'guide_talk023'],	
			]	
		},
		{
			'goto':{
				'type': 2,	
				'buildingID':'building009',
				'state':2,
				
			},
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'building',		
				'objName': 'building009',	
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 0,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn_free',	
				'close':2,
			},	
		},
		{
			'save':{},
		},
		{
			'click':{},
		},
	],
	'g028':[			
		[
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'ui',		
					'objName': 'btn_map2',	
				},	
			},
		],
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'ui',
				'objName':'taskPanel',
			},			
		},
		{
			'save':{},
		},
		{
			'click':{},			
		},
	],	
	'g029':[		
		[
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'ui',		
					'objName': 'btn_map2',	
				},
			},
		],
		{
			'talks': [
				['hero403', 'guide_npc002', 'guide_talk024'],	
			]	
		},
		{
			'goto':{
				'type': 2,	
				'buildingID':'building009',
				'state':2,
				
			},
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'building',		
				'objName': 'building009',	
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'building',		
				'objName': '2',						
			},	
			'sound':'guidetalk22',	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn_cd',						
			},	
		},
		{
			'save':{},
		},
	],
	'g020':[			
		[
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'ui',		
					'objName': 'btn_map2',	
				},	
			},
		],
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'ui',
				'objName':'taskPanel',
			},			
		},
		{
			'save':{},
		},
		{
			'click':{},			
		},
	],
	'g022':[			
		[
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'ui',		
					'objName': 'btn_map2',	
				},	
			},
		],
		{
			'talks': [
				['hero403', 'guide_npc002', 'guide_talk025'],	
			]	
		},
		{
			'goto':{
				'type': 2,	
				'buildingID':'building0200',
				'state':2,
				
			},
			'sound':'guidetalk23',
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'building',		
				'objName': 'building0200',	
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 0,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn_free',	
				'close':2,
			},	
		},
		{
			'save':{},
		},
		{
			'click':{},
		},
	],
	'g022':[			
		[
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'ui',		
					'objName': 'btn_map2',	
				},	
			},
		],
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'ui',
				'objName':'taskPanel',
			},			
		},
		{
			'save':{},
		},
		{
			'click':{},			
		},
	],
	'g023':[		
		[
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'ui',		
					'objName': 'btn_map2',	
				},	
			},
		],
		{
			'talks': [
				['hero403', 'guide_npc002', 'guide_talk026'],	
			]	
		},
		{
			'goto':{
				'type': 2,	
				'buildingID':'building0200',
				'state':2,
				
			},
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'building',		
				'objName': 'building0200',	
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'building',		
				'objName': '2',						
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn_cd',						
			},	
		},
		{
			'save':{},
		},
	],
	'g024':[			
		[
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'ui',		
					'objName': 'btn_map2',	
				},	
			},
		],
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'ui',
				'objName':'taskPanel',
			},			
		},
		{
			'save':{},
		},
		{
			'click':{},			
		},
	],
	'g025':[			
		[
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 0,					
					'rect': 2,					
					'type': 'ui',		
					'objName': 'btn_map2',	
				},	
			},
		],
		{
			'talks': [
				['hero403', 'guide_npc002', 'guide_talk027'],	
			]	
		},
		{
			'goto':{
				'type': 2,	
				'buildingID':'building005',
				'state':2,
				
			},
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'building',		
				'objName': 'building005',	
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 0,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn_free',	
			},	
		},	
		{
			'save':{},
		},
		{
			'click':{},	
		},	
	],
	'g026':[			
		[
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'ui',		
					'objName': 'btn_map2',	
				},	
			},
		],
		{
			'talks': [
				['hero403', 'guide_npc002', 'guide_talk028'],	
			]	
		},
		{
			'goto':{
				'type': 2,	
				'buildingID':'building005',
				'state':2,
				
			},
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'building',		
				'objName': 'building005',	
			},	
		},
		{
			'click':{				
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'building',		
				'objName': '200',	
			},	
		},
		{
			'click':{
				'shade': 2,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',		
				'objName': 'list_0_btnBuy',			
			},	
		},
		{
			'save':{},
		},
		{
			'sound':'guidetalk24',
			'delay':4000,						
		},
		{
			'click':{
				'shade': 2,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',		
				'objName': 'btn_close',	      
			},	
		},
		{
			'click':{
				'shade': 2,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',		
				'objName': 'btn_close',	      
			},	
		},
		
	],
	'g027':[			
		[
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'ui',		
					'objName': 'btn_map2',	
				},	
			},
		],
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'ui',
				'objName':'taskPanel',
			},			
		},
		{
			'save':{},
		},
		{
			'click':{},			
		},
	],
	'g028':[		
		[
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 0,					
					'rect': 2,					
					'type': 'ui',		
					'objName': 'btn_map2',	
				},	
			},
		],
		{
			'goto':{
				'type': 2,	
				'buildingID':'building023',
				'state':2,
				
			},
		},
		{
			'talks': [
				['hero403', 'guide_npc002', 'guide_talk029'],	
				['hero403', 'guide_npc002', 'guide_talk030'],	
			],	
			'sound':'guidetalk25',
		},
		{
			'click':{
				'shade': 2,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'ui',		
				'objName': 'btn_map2',	      
			},	
		},
	],
	'g029':[
		{
			'goto':{
				'type': 2,	
				'cityID':['244','62','375'],
			},
		},
		{
			'talks': [
				['hero702', 'guide_npc003', 'guide_talk032'],	
				['hero702', 'guide_npc002', 'guide_talk032'],	
			]	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'city',		
				'objName': '0',	             
			},	
		},
		{
			'click':{},    
		},
		{
			'click':{   
				'shade': 0,					
				'shield': 2,				
				'arrow': 0,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn2',	
			},	
		},
		{
			'save':{},
		},
		{
			'delay':20000,
		},
	],
	'g030':[			
		[
			{
				'goto':{
					'type': 2,	
					'cityID':['244','62','375'],
				},
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'city',		
					'objName': '0',	             
				},	
			},
		],
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 0,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn2',	
			},	
		},
	
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'city',		
				'objName': '2',	   
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn_sendTroop',	
			},	
		},
		{
			'save':{},
		},
		{
			'in_battle':{},	
		},
	],
	'g032':[				
		[
			{
				'goto':{
					'type': 2,	
					'cityID':['244','62','375'],
				},
			},
		],
		
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'city',		
				'objName': '0',	      
			},	
		},
		{
			'click':{},	
		},
		{
			'click':{ 
				'shade': 0,					
				'shield': 2,				
				'arrow': 0,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn2',	
			},	
		},
		{
			'save':{},
		},
		{
			'click':{},	
		},
	],
	'g032':[	
		[
			{
				'goto':{
					'type': 2,	
					'cityID':['244','62','375'],
				},
			},
		],
		{
			'talks': [
				['hero702', 'guide_npc003', 'guide_talk033'],	
				['hero702', 'guide_npc002', 'guide_talk034'],	
			]	
		},		
		{
			'click':{
				'shade': 2,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'ui',		
				'objName': 'btn_hero',	      
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',		
				'objName': 'list_0',	      
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',		
				'objName': 'tab_2',	      
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',		
				'objName': 'tab2_2',	      
			},	
			'sound':'guidetalk26',
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',		
				'objName': 'skilllist_0',	      
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn_gold',	      
			},	
		},
		{
			'save':{},
		},
	],
	'g033':[			
		[
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'ui',		
					'objName': 'btn_hero',	      
				},	
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'list_0',	      
				},	
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'tab_2',	      
				},	
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'tab2_2',	      
				},	
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'skilllist_0',	      
				},	
			},
		],
		{
			'talks': [
				['hero702', 'guide_npc003', 'guide_talk095'],	
				['hero702', 'guide_npc002', 'guide_talk036'],	
				['hero702', 'guide_npc002', 'guide_talk037'],
			]	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn_gold',	      
			},	
		},
		{
			'save':{},
		},
	],
	'g034':[			
		[
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'ui',		
					'objName': 'btn_hero',	      
				},	
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'list_0',	      
				},	
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'tab_2',	      
				},	
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'tab2_2',	      
				},	
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'skilllist_0',	      
				},	
			},
		],
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn_gold',	      
			},	
		},
		{
			'save':{},
		},
	],
	'g095':[			
		[
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'ui',		
					'objName': 'btn_hero',	      
				},	
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'list_0',	      
				},	
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'tab_2',	      
				},	
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'tab2_2',	      
				},	
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'skilllist_0',	      
				},	
			},
		],
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn_gold',	      
			},	
			'sound':'guidetalk27',
		},
		{
			'save':{},
		},
		{
			'talks': [
				['hero702', 'guide_npc002', 'guide_talk038'],
			]	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn_close',	      
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',		
				'objName': 'btn_close',	      
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',		
				'objName': 'btn_close',	      
			},	
		},
	],
	'g036':[			
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'ui',
				'objName':'taskPanel',
			},			
		},
		{
			'save':{},
		},
		{
			'click':{},			
		},
	],
	
	'g037': [			
		{
			'talks': [
				['hero702', 'guide_npc002', 'guide_talk039'],				
			]	
		},
		{
			'goto':{
				'type': 2,	
				'cityID':['244','62','375'],
			},
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'city',		
				'objName': '0',	             
			},	
		},
		{
			'click':{},    
		},
		{
			'click':{   
				'shade': 0,					
				'shield': 2,				
				'arrow': 0,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn2',	
			},	
		},
		{
			'save':{},
		},
	],
	'g038':[
		[
			{
				'goto':{
					'type': 2,	
					'cityID':['244','62','375'],
				},
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'city',		
					'objName': '0',	             
				},	
			},
		],
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 0,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn2',	
			},	
		},
	
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'city',		
				'objName': '2',	   
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn',	
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'list_0',	      
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn_send',	
			},	
		},
		{
			'save':{},
		},
		{
			'in_battle':{},	
		},
	],	
	'g039':[	
		{
			'goto':{
				'type': 2,	
				'cityID':['244','62','375'],
			},
		},
		{
			'talks':[
				['hero702', 'guide_npc002', 'guide_talk040'],
			],
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'city',		
				'objName': '0',	      
			},	
		},
		{
			'click':{},	
		},
		{
			'click':{ 
				'shade': 0,					
				'shield': 2,				
				'arrow': 0,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn2',	
			},	
		},
		{
			'save':{},
		},
		{
			'click':{},	
		},
		{  
			'click':{	
			},	
		},

	],
	'g040':[			
		[
			{
				'goto':{
					'type': 2,	
					'cityID':['244','62','375'],
				},
			},
		],
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'ui',
				'objName':'taskPanel',
			},			
		},
		{
			'save':{},
		},
		{
			'click':{},			
		},
	],
	'g042':[		
		{
			'alien_first':{},		
		},
		{
			'goto':{
				'type': 2,	
				'cityID':['244','62','375'],
			},
		},
		{
			'talks':[
				['hero702', 'guide_npc003', 'guide_talk042'],
				['hero702', 'guide_npc002', 'guide_talk042'],
				['hero702', 'guide_npc003', 'guide_talk043'],
			],
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'city',		
				'objName': '5',	   
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 0,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn',	
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'list_0',	      
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'list_2',	      
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn_send',	
			},	
		},
		{
			'save':{},
		},
		{
			'in_battle':{},	
		}
	],
	'g042':[
		{
			'talks':[
				['hero702', 'guide_npc002', 'guide_talk047'],	
			],
		},
		{
			'click':{
				'shade': 2,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'ui',		
				'objName':'btnCountry',	
			},	
		},
		{
			'talks':[
				['hero702', 'guide_npc002', 'guide_talk048'],	
				['hero702', 'guide_npc003', 'guide_talk049'],	
			],
			'sound':'guidetalk28',
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName':'btn_go',	
			},	
		},
		{
			'talks':[
				['hero702', 'guide_npc002', 'guide_talk050'],	
			],
			'sound':'guidetalk29',
		},
	],
	'g043':[
		{
			'goto':{
				'type': 2,	
				'cityID':['244','62','375'],
			},
		},
		{
			'talks':[
				['hero403', 'guide_npc002', 'guide_talk044'],	
			],
		},
		{
			'click':{
				'shade': 2,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'ui',		
				'objName':'taskPanel',	
			},	
		},
	],
},
	
    'new_install2':{       
	'g002':[
		{

			'image': {
				'url': 'text_painting2',			
				'next': 2,			
			},

		
		










		},
		{






			'sound':'guidetalk02',		
			'talks': [
				['hero702', 'guide_npc002', 'new_guide_talk002'],    
				['hero403', 'guide_npc002', 'new_guide_talk002'],
				['hero702', 'guide_npc002', 'new_guide_talk003'],
			]			
		},
		{
			'recruit_hero':'hero702',

		},
		{
			'delay':2500,
		},
		{
			'click':{},
		},

	],
	'g002': [		
		{
			'talks': [
				['hero702', 'guide_npc002', 'new_guide_talk004'],   
			]	
		},
		{
			'click':{
				'shade': 2,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'hero_list',
				'objName':'0',
			},			
		},

		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 0,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn_2',	
			},	
			'sound':'guidetalk02',
		},	
		{
			'save':{},
		},
		{
			'talks': [
				['hero702', 'guide_npc002', 'new_guide_talk005'], 
			]	
		},
	],
	'g003': [
		{
			'talks': [
				['hero403', 'guide_npc002', 'new_guide_talk006'],
				['hero702', 'guide_npc002', 'new_guide_talk007'],				
			]	
		},
		{
			'goto':{
				'type': 2,	
				'cityID':['246','32','953'],
			},
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'city',		
				'objName': '0',	             
			},
			'sound':'guidetalk03',	
		},
		{
			'click':{},    
		},
		{
			'click':{   
				'shade': 0,					
				'shield': 2,				
				'arrow': 0,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn2',	
			},	
		},
		{
			'save':{},
		},
	],
	'g004':[
		[
			{
				'goto':{
					'type': 2,	
					'cityID':['246','32','953'],
				},
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 0,					
					'rect': 2,					
					'type': 'city',		
					'objName': '0',	             
				},	
			},
		],
		
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 0,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn2',	
			},	
		},
	
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'city',		
				'objName': '3',	   
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn',	
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'list_0',	      
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn_send',	
			},	
		},
		{
			'save':{},
		},
		{
			'in_battle':{},	
		},
		{
			'click':{},	
		},
		{
			'click':{},	
		},
	],	
	'g005':[	
		{
			'goto':{
				'type': 2,	
				'cityID':['246','32','953'],
			},
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'city',		
				'objName': '0',	      
			},	
		},
		{
			'click':{},	
		},
		{
			'click':{ 
				'shade': 0,					
				'shield': 2,				
				'arrow': 0,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn2',	
			},	
		},
		{
			'save':{},
		},
		{
			'click':{	
			},	
		},
		{  
			'click':{	
			},	

		},
	],

	'g006': [                 
		{
			'talks': [
				['hero702', 'guide_npc002', 'new_guide_talk008'],				
			]	
		},
		{
			'click':{
				'shade': 2,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'ui',		
				'objName': 'btn_hero',	      
			},	
		},
		{
			'talks': [
				['hero702', 'guide_npc002', 'new_guide_talk009'],		
				['hero702', 'guide_npc003', 'new_guide_talk0200'],			
			]	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',		
				'objName': 'list_0',	      
			},	
		},
		{
			'save':{},
		},
		{
			'delay':2500,
		},
		{
			'click':{},	
		},

	],
	'g007': [
		[    
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'ui',		
					'objName': 'btn_hero',	      
				},	
			},
		],
		{
			'talks': [
				['hero702', 'guide_npc002', 'new_guide_talk022'],			
			]	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',		
				'objName': 'list_2',	      
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',		
				'objName': 'btn_lv',	      
			},	
		},
		{
			'delay':500,
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'list_0',	      
			},	
			'sound':'guidetalk05',
		},
		{
			'save':{},
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn_close',	      
			},	
		},
		{
			'talks': [
				['hero702', 'guide_npc002', 'new_guide_talk022'],			
			]	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',		
				'objName': 'btn_close',	      
			},	
		},
	],
	'g008': [              
		[    
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'ui',		
					'objName': 'btn_hero',	      
				},	
			},
		],
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',		
				'objName': 'list_0',	      
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',		
				'objName': 'btn_lv',	      
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'list_0',	      
			},	
		},
		{
			'save':{},
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn_close',	      
			},	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',		
				'objName': 'btn_close',	      
			},	
		},		

		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',		
				'objName': 'btn_close',	      
			},	
		},	
	],
          'g009':[			

		{
			'talks': [
				['hero702', 'guide_npc002', 'new_guide_talk023'],			
			],

		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'ui',
				'objName':'taskPanel',
			},			
		},	
		{
			'talks': [
				['hero403', 'guide_npc002', 'new_guide_talk024'],			
			],

		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',		
				'objName': 'list_0_btn_get',	      
			},	
		},
		{
			'save':{},
		},
		{
			'click':{},	
		},

	],
	'g0200':[			
		[    
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'ui',		
					'objName': 'taskPanel',	      
				},	
			},
		],	
		{
			'talks': [
				['hero702', 'guide_npc003', 'new_guide_talk025'],			
			],

		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',
				'objName':'list_0_btn_get',
			},			
		},
		{
			'save':{},
		},
		{
			'click':{},	
		},
		{
			'talks': [
				['hero403', 'guide_npc002', 'new_guide_talk026'],			
			],	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',
				'objName':'list_0_btn_go',
			},			
		},

	],
	'g022': [ 		

		{
			'click':{
				'shade': 2,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'hero_list',
				'objName':'2',
			},			
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 0,					
				'rect': 2,					
				'type': 'panel',		
				'objName': 'btn_2',	
			},	
		},
		{
			'save':{},
		},

	],
	'g022':[			

		{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'ui',		
					'objName': 'taskPanel',	      
				},	
			},
		{
			'talks': [
				['hero702', 'guide_npc003', 'new_guide_talk027'],			
			],	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',
				'objName':'list_0_btn_get',
			},			
		},

		{
			'save':{},
		},

		{
			'click':{
	
			},			
		},
		                           
	],
	'g023':[			
		[    
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'ui',		
					'objName': 'taskPanel',	      
				},	
			},
		],
		{
			'talks': [
				['hero403', 'guide_npc002', 'new_guide_talk028'],	
			]	
		},
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'scene',		
				'objName': 'btn_get',	      
			},	
		},
		{
			'save':{},
		},
		{
			'click':{

			},	
		},
                {
			'click':{ 

			},	
		},

	],
	



},
	
    'new_install3':{       
			'g002':[
				{
		
					'image': {
						'url': 'text_painting2',			
						'next': 2,			
					},
		
				
				
		
		
		
		
		
		
		
		
		
		
				},
				{
		
		
		
		
		
		
					'sound':'guidetalk02',		
					'talks': [
						['hero702', 'guide_npc002', 'new_guide_talk002'],    
						['hero403', 'guide_npc002', 'new_guide_talk002'],
						['hero702', 'guide_npc002', 'new_guide_talk003'],
					]			
				},
				{
					'recruit_hero':'hero749',		
		
				},
				{
					'delay':2500,
				},
				{
					'click':{},
				},
		
			],
			'g002':[
				{
					'sound':'guidetalk02',		
					'talks': [
						['hero702', 'guide_npc002', 'new_guide_talk002'],    
						['hero403', 'guide_npc002', 'new_guide_talk002'],
						['hero702', 'guide_npc002', 'new_guide_talk003'],
					]			
				},
				{
					'recruit_hero':'hero742',		
		
				},
				{
					'delay':2500,
				},
				{
					'click':{},
				},
		
			],
			'g003': [
				{	
					'talks': [
						['hero702', 'guide_npc002', 'new_guide_talk002'],    
						['hero403', 'guide_npc002', 'new_guide_talk002'],
						['hero702', 'guide_npc002', 'new_guide_talk003'],
					]			
				},
				{
					'send_method':[
						'build_pk_npc',				
						{'city_id':['246','32','953'],'alien_diff':0,'alien_difflv':2,},					
					],
				},
				{
					'save':{},					
				},
				
			],
			'g004': [		
				{
					'talks': [
						['hero702', 'guide_npc002', 'new_guide_talk004'],       
					]	
				},
				{
					'click':{
						'shade': 2,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'hero_list',
						'objName':'0',
					},			
				},
		
				{
					'click':{
						'shade': 0,					
						'shield': 2,				
						'arrow': 0,					
						'rect': 2,					
						'type': 'panel',		
						'objName': 'btn_2',	
					},	
					'sound':'guidetalk02',
				},	
				{
					'save':{},
				},
				{
					'talks': [
						['hero702', 'guide_npc002', 'new_guide_talk005'], 
					]	
				},
			],
			'g005': [		
				[
					{
						'click':{
							'shade': 2,					
							'shield': 2,				
							'arrow': 2,					
							'rect': 2,					
							'type': 'hero_list',
							'objName':'2',
						},			
					},
				],
				{
					'talks': [
						['hero702', 'guide_npc002', 'new_guide_talk004'],       
					]	
				},	
				{
					'click':{
						'shade': 0,					
						'shield': 2,				
						'arrow': 0,					
						'rect': 2,					
						'type': 'panel',		
						'objName': 'btn_2',	
					},	
					'sound':'guidetalk02',
				},	
				{
					'save':{},
				},
				{
					'talks': [
						['hero702', 'guide_npc002', 'new_guide_talk005'], 
					]	
				},
			],
			'g006': [
				{
					'talks': [
						['hero403', 'guide_npc002', 'new_guide_talk006'],
						['hero702', 'guide_npc002', 'new_guide_talk007'],				
					]	
				},
				{
					'goto':{
						'type': 2,	
						'cityID':['246','32','953'],
					},
				},
				{
					'click':{
						'shade': 0,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'city',		
						'objName': '5',	             
					},
					'sound':'guidetalk03',	
				},
				{
					'click':{   
						'shade': 0,					
						'shield': 2,				
						'arrow': 0,					
						'rect': 2,					
						'type': 'panel',		
						'objName': 'btn',	
					},	
				},
				{
					'click':{
						'shade': 0,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'panel',		
						'objName': 'list_0',	      
					},	
				},
				{
					'talks': [
						['hero403', 'guide_npc002', 'new_guide_talk006'],	
						['hero702', 'guide_npc002', 'new_guide_talk007'],				
					]	
				},
				{
					'click':{
						'shade': 0,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'panel',		
						'objName': 'list_2',	      
					},	
				},				
				{
					'click':{
						'shade': 0,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'panel',		
						'objName': 'btn_send',	
					},	
				},
				{
					'save':{},
				},
				{
					'in_battle':{},	
				},
			],
			'g007': [],
			'g008': [],
			'g009': [
				[    
					{
						'click':{
							'shade': 2,					
							'shield': 2,				
							'arrow': 2,					
							'rect': 2,					
							'type': 'ui',		
							'objName': 'btn_hero',	      
						},	
					},
				],
				{
					'talks': [
						['hero702', 'guide_npc002', 'new_guide_talk022'],			
					]	
				},
				{
					'click':{
						'shade': 0,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'scene',		
						'objName': 'list_2',	      
					},	
				},
				{
					'click':{
						'shade': 0,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'scene',		
						'objName': 'btn_lv',	      
					},	
				},
				{
					'delay':500,
				},
				{
					'click':{
						'shade': 0,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'panel',		
						'objName': 'list_0',	      
					},	
					'sound':'guidetalk05',
				},
				{
					'save':{},
				},
				{
					'click':{
						'shade': 0,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'panel',		
						'objName': 'btn_close',	      
					},	
				},
				{
					'talks': [
						['hero702', 'guide_npc002', 'new_guide_talk022'],			
					]	
				},
				{
					'click':{
						'shade': 0,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'scene',		
						'objName': 'btn_close',	      
					},	
				},
			],
			'g0200': [              
				[    
					{
						'click':{
							'shade': 2,					
							'shield': 2,				
							'arrow': 2,					
							'rect': 2,					
							'type': 'ui',		
							'objName': 'btn_hero',	      
						},	
					},
				],
				{
					'click':{
						'shade': 0,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'scene',		
						'objName': 'list_0',	      
					},	
				},
				{
					'click':{
						'shade': 0,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'scene',		
						'objName': 'btn_lv',	      
					},	
				},
				{
					'click':{
						'shade': 0,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'panel',		
						'objName': 'list_0',	      
					},	
				},
				{
					'save':{},
				},
				{
					'click':{
						'shade': 0,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'panel',		
						'objName': 'btn_close',	      
					},	
				},
				{
					'click':{
						'shade': 0,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'scene',		
						'objName': 'btn_close',	      
					},	
				},		
		
				{
					'click':{
						'shade': 0,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'scene',		
						'objName': 'btn_close',	      
					},	
				},	
			],
		          'g009':[			
		
				{
					'talks': [
						['hero702', 'guide_npc002', 'new_guide_talk023'],			
					],
		
				},
				{
					'click':{
						'shade': 0,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'ui',
						'objName':'taskPanel',
					},			
				},	
				{
					'talks': [
						['hero403', 'guide_npc002', 'new_guide_talk024'],			
					],
		
				},
				{
					'click':{
						'shade': 0,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'scene',		
						'objName': 'list_0_btn_get',	      
					},	
				},
				{
					'save':{},
				},
				{
					'click':{},	
				},
		
			],
			'g022':[			
				[    
					{
						'click':{
							'shade': 0,					
							'shield': 2,				
							'arrow': 2,					
							'rect': 2,					
							'type': 'ui',		
							'objName': 'taskPanel',	      
						},	
					},
				],	
				{
					'talks': [
						['hero702', 'guide_npc003', 'new_guide_talk025'],			
					],
		
				},
				{
					'click':{
						'shade': 0,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'scene',
						'objName':'list_0_btn_get',
					},			
				},
				{
					'save':{},
				},
				{
					'click':{},	
				},
				{
					'talks': [
						['hero403', 'guide_npc002', 'new_guide_talk026'],			
					],	
				},
				{
					'click':{
						'shade': 0,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'scene',
						'objName':'list_0_btn_go',
					},			
				},
		
			],
			'g022':[			
		
				{
						'click':{
							'shade': 0,					
							'shield': 2,				
							'arrow': 2,					
							'rect': 2,					
							'type': 'ui',		
							'objName': 'taskPanel',	      
						},	
					},
				{
					'talks': [
						['hero702', 'guide_npc003', 'new_guide_talk027'],			
					],	
				},
				{
					'click':{
						'shade': 0,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'scene',
						'objName':'list_0_btn_get',
					},			
				},
		
				{
					'save':{},
				},
		
				{
					'click':{
			
					},			
				},
				                           
			],
			'g023':[			
				[    
					{
						'click':{
							'shade': 0,					
							'shield': 2,				
							'arrow': 2,					
							'rect': 2,					
							'type': 'ui',		
							'objName': 'taskPanel',	      
						},	
					},
				],
				{
					'talks': [
						['hero403', 'guide_npc002', 'new_guide_talk028'],	
					]	
				},
				{
					'click':{
						'shade': 0,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'scene',		
						'objName': 'btn_get',	      
					},	
				},
				{
					'save':{},
				},
				{
					'click':{
		
					},	
				},
		                {
					'click':{ 
		
					},	
				},
		
			],
	



},

	'office_guide':{		
		'office_g002':[
			{
				'talks':[
					['hero403', 'guide_npc002', 'guide_talk2000'],	
				],
			},	
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'ui',		
					'objName': 'btn_more',	   
				},	
			},	
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'panel',		
					'objName': 'list_0',	   
				},	
			},	
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'btn',	   
				},	
			},
			{
				'talks':[
					['hero403', 'guide_npc002', 'guide_talk2002'],	
					['hero702', 'guide_npc002', 'guide_talk2002'],	
				],
				'sound':'guidetalk20',				
			},	
		],
		'office_g002':[
			[
				{
					'click':{
						'shade': 2,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'ui',		
						'objName': 'btn_more',	   
					},	
				},	
				{
					'click':{
						'shade': 0,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'panel',		
						'objName': 'list_0',	   
					},	
				},					
			],
			{
				'talks':[
					['hero702', 'guide_npc002', 'guide_talk2003'],	
				],
			},	
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'listRight_0',	   
				},	
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 0,					
					'rect': 2,					
					'type': 'panel',		
					'objName': 'btn',	   
				},	
			},
			{
				'talks':[
					['hero702', 'guide_npc002', 'guide_talk2004'],	
					['hero403', 'guide_npc002', 'guide_talk2005'],	
				],
				'sound':'guidetalk22',
			},
		],
	},

	'heroslv_guide':{		
		'heroslv_g002':[
			{
				'talks':[
					['hero403', 'guide_npc002', 'guide_talk2200'],	
					['hero403', 'guide_npc002', 'guide_talk222'],	
				],
			},
		],
	},
	
	'catchhero_guide':{		
		'catchhero_g002':[
			[
				{
					'click':{
						'shade': 2,					
						'shield': 2,				
						'arrow': 0,					
						'rect': 2,					
						'type': 'ui',		
						'objName': 'btn_map2',	   
					},	
				},
			],
			{
				'talks':[
					['hero702', 'guide_npc002', 'guide_talk2200'],	
				],
			},
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 0,					
					'rect': 2,					
					'type': 'ui',		
					'objName': 'btn_map2',	   
				},	
			},
			{
				'goto':{
					'type': 2,	
					'cityID':['246','32','953'],
				},
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'city',		
					'objName': '4',	   
				},	
			},
			{
				'talks':[
					['hero702', 'guide_npc002', 'guide_talk222'],	
				],
			},
		],
	},

	'country_guide':{		
		'country_g002':[
			{
				'talks':[
					['hero403', 'guide_npc002', 'guide_talk230'],	
					['hero403', 'guide_npc002', 'guide_talk232'],	
					['hero403', 'guide_npc002', 'guide_talk232'],	
				],
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'tab_2',	   
				},	
			},
			{
				'talks':[
					['hero403', 'guide_npc002', 'guide_talk233'],		
				],
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'tab_2',	   
				},	
			},
			{
				'talks':[
					['hero403', 'guide_npc002', 'guide_talk234'],		
				],
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'tab_3',	   
				},	
			},
			{
				'talks':[
					['hero403', 'guide_npc002', 'guide_talk295'],		
				],
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'tab_4',	   
				},	
			},
			{
				'talks':[
					['hero403', 'guide_npc002', 'guide_talk236'],		
				],
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'tab_5',	   
				},	
			},
			{
				'talks':[
					['hero403', 'guide_npc002', 'guide_talk237'],		
				],
			},
		],
	},
	
	'guild_guide':{		
		'guild_g002':[
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'ui',		
					'objName': 'btn_team',	   
				},	
			},







		],
	},

	'pk_guide':{		
		'pk_g002':[
			[
				{
					'click':{
						'shade': 2,					
						'shield': 2,				
						'arrow': 0,					
						'rect': 2,					
						'type': 'ui',		
						'objName': 'btn_map2',	
					},	
				},				
			],
			{
				'talks':[
					['hero702', 'guide_npc002', 'guide_talk220'],	
				],
			},		
			{
				'goto':{
					'type': 2,	
					'buildingID':'building008',
					'state':2,
					
				},
			},	
			{
				'talks':[
					['hero702', 'guide_npc002', 'guide_talk222'],	
				],
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 0,					
					'rect': 2,					
					'type': 'building',		
					'objName': 'building008',	   
				},	
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 0,					
					'rect': 2,					
					'type': 'panel',		
					'objName': 'btn_free',	
				        'close':2,
				},	
			},
			{
				'click':{},
			},
		],
		'pk_g002':[
			[
				{
					'click':{
						'shade': 2,					
						'shield': 2,				
						'arrow': 0,					
						'rect': 2,					
						'type': 'ui',		
						'objName': 'btn_map2',	
					},	
				},
				{
					'goto':{
						'type': 2,	
						'buildingID':'building008',
						'state':2,
						
					},
				},								
			],		
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 0,					
					'rect': 2,					
					'type': 'building',		
					'objName': 'building008',	   
				},	
			},
			{
				'talks':[
					['hero702', 'guide_npc002', 'guide_talk222'],	
				],
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'building',		
					'objName': '20',	   
				},	
			},
			{
				'talks':[
					['hero702', 'guide_npc002', 'guide_talk223'],	
					['hero702', 'guide_npc003', 'guide_talk224'],	
				],
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'list_0_btn',	   
				},	
			},
			{
				'talks':[
					['hero702', 'guide_npc002', 'guide_talk225'],	
				],
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 0,					
					'rect': 2,					
					'type': 'panel',		
					'objName': 'btn',	   
				},	
			},
		],
		
	},
	'resolve_guide':{			
		'resolve_g002':[
			[
				{
					'click':{
						'shade': 2,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'ui',		
						'objName': 'btn_map2',	   
					},	
				},
			],
			{
				'goto':{
					'type': 2,	
					'buildingID':'building006',
					'state':2,
					
				},
			},		
			{
				'talks':[
					['hero702', 'guide_npc002', 'guide_talk270'],	
				],
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'building',		
					'objName': 'building006',	      
				},	
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 0,					
					'rect': 2,					
					'type': 'panel',		
					'objName': 'btn_free',		
				        'close':2,
				},	
			},
			{
				'click':{},
			},
			{
				'talks':[
					['hero702', 'guide_npc002', 'guide_talk272'],	
				],
			},	
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'building',		
					'objName': 'building006',	      
				},	
			},		
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 0,					
					'rect': 2,					
					'type': 'building',		
					'objName': '23',	
				},	
			},
			{
				'talks':[
					['hero702', 'guide_npc002', 'guide_talk272'],	
					['hero702', 'guide_npc002', 'guide_talk273'],	
				],
			},
		],
	},
	'estate_guide':{		
		'estate_g002':[
			[
				{
					'click':{
						'shade': 2,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'ui',		
						'objName': 'btn_map2',	      
					},	
				},			
			],
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'ui',		
					'objName': 'btn_map2',	      
				},	
			},
			{
				'goto':{
					'type': 2,	
					'cityID':['246','32','953'],
				},
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'city',		
					'objName': '62',	      
				},	
			},
			{
				'talks':[
					['hero702', 'guide_npc002', 'guide_talk260'],	
					['hero702', 'guide_npc002', 'guide_talk262'],	
					['hero702', 'guide_npc002', 'guide_talk262'],	
					['hero702', 'guide_npc002', 'guide_talk263'],	
				],
			},
		],
	},
	'equip_guide':{			
		'equip_g002':[
			[
				{
					'click':{
						'shade': 2,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'ui',		
						'objName': 'btn_map2',	   
					},	
				},
			],
			{
				'goto':{
					'type': 2,	
					'buildingID':'building002',
					'state':2,
					
				},
			},		
			{
				'talks':[
					['hero702', 'guide_npc002', 'guide_talk280'],	
				],
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'building',		
					'objName': 'building002',	      
				},	
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 0,					
					'rect': 2,					
					'type': 'panel',		
					'objName': 'btn_free',		
				},	
			},	
			{
				'click':{},
			},	
		],
		'equip_g002':[
			[
				{
					'click':{
						'shade': 2,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'ui',		
						'objName': 'btn_map2',	   
					},	
				},
			],
			{
				'goto':{
					'type': 2,	
					'buildingID':'building002',
					'state':2,
					
				},
			},
			{
				'talks':[
					['hero702', 'guide_npc002', 'guide_talk282'],	
				],
			},	
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'building',		
					'objName': 'building002',	      
				},	
			},	
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'building',		
					'objName': '4',	      
				},	
			},
			{
				'talks':[
					['hero702', 'guide_npc002', 'guide_talk282'],	
				],
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'panel',		
					'objName': 'btn_make',	      
				},	
			},
			{
				'talks':[
					['hero702', 'guide_npc002', 'guide_talk283'],	
				],
			},
		],
	},	

	'shogun_guide':{
		'shogun_g002':[
			[
				{
					'click':{
						'shade': 2,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'ui',		
						'objName': 'btn_map2',	   
					},	
				},			
			],
			{
				'goto':{
					'type': 2,	
					'buildingID':'building002',
					'state':2,
					
				},
			},		
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'building',		
					'objName': 'building002',	      
				},	
			},	
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'building',		
					'objName': '26',	      
				},	
			},	
			{
				'talks':[
					['hero702', 'guide_npc002', 'guide_talk275'],	
					['hero702', 'guide_npc002', 'guide_talk292'],	
				],
			},
		],
	},
	
	'lookstar_guide':{
		'lookstar_g002':[
			[
				{
					'click':{
						'shade': 2,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'ui',		
						'objName': 'btn_map2',	   
					},	
				},			
			],
			{
				'goto':{
					'type': 2,	
					'buildingID':'building006',
					'state':2,
					
				},
			},
			{
				'talks':[
					['hero702', 'guide_npc002', 'guide_talk200'],	
				],
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'building',		
					'objName': 'building006',	      
				},	
			},	
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'building',		
					'objName': '22',	      
				},	
			},	
			{
				'talks':[
					['hero702', 'guide_npc002', 'guide_talk202'],	
					['hero702', 'guide_npc002', 'guide_talk202'],	
				],
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'panel',		
					'objName': 'btnOne',	      
				},	
			},
		],
	},
	'science_guide':{
		'science_g002':[
			[
				{
					'click':{
						'shade': 2,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'ui',		
						'objName': 'btn_map2',	   
					},	
				},			
			],
			{
				'goto':{
					'type': 2,	
					'buildingID':'building003',
					'state':2,
					
				},
			},
			{
				'talks':[
					['hero702', 'guide_npc002', 'guide_talk220'],	
				],
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'building',		
					'objName': 'building003',	      
				},	
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 0,					
					'rect': 2,					
					'type': 'panel',		
					'objName': 'btn_free',		
				},	
			},	
			{
				'talks':[
					['hero702', 'guide_npc002', 'guide_talk222'],	
				],
			},
		],	
	},

	'PZL222_guide':{
		'PZL222_g002':[                                                      
			{
				'talks':[
					['hero702', 'guide_npc002', 'guide_talk3200'],	
                                        ['hero702', 'guide_npc002', 'guide_talk322'],	
				],

			},
                        {
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': '0_2',
				},	
			},
		],	
	},
        'PZL222_guide':{	
		'PZL222_g002':[                                                      
			{
				'talks':[
					['hero702', 'guide_npc002', 'guide_talk322'],	
				],
			},
		],	
	},

	'mining_guide':{		
		'mining_g002':[
			{
				'talks':[
					['hero420','guide_npc004', 'guide_talk230'],
				],
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'btn_pray',		
				},
			},
			{
				'talks':[
					['hero420','guide_npc004', 'guide_talk232'],
				],
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'panel',		
					'objName': 'btn_pray',		
				},
			},
			{
				'delay':2500,
			},
			{
				'talks':[
					['hero420','guide_npc004', 'guide_talk232'],
					['hero420','guide_npc004', 'guide_talk233'],
				],
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'panel',		
					'objName': 'btn_close',		
				},
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'place_0',		
				},
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'panel',		
					'objName': 'list_hero_0',		
				},
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'panel',		
					'objName': 'btn_fight',		
				},
			},
			{
				'talks':[
					['hero420','guide_npc004', 'guide_talk234'],
				],
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'panel',		
					'objName': 'btn_close',		
				},
			},
			{
				'talks':[
					['hero420','guide_npc004', 'guide_talk295'],	
				],
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'btn_fight',		
				},
			},
			{
				'talks':[
					['hero420','guide_npc004', 'guide_talk236'],	
				],
			},
		],
	},
	'credit_guide':{
		'credit_g002':[
			{
				'talks':[
					['hero702','guide_npc002', 'guide_talk240'],	
					['hero702','guide_npc002', 'guide_talk242'],
				],
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'tab_2',		
				},
			},	
			{
				'talks':[
					['hero702','guide_npc002', 'guide_talk242'],	
				],
			},	
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'btn2',		
				},
			},	
			{
				'talks':[
					['hero702','guide_npc002', 'guide_talk243'],
				],
			},					
		],
	},
	'legend_guide':{		
		'legend_g002':[
		{
			'goto':{
				'type': 2,	
				'buildingID':'building026',
				'state':2,
				
			},
		},
		{
			'talks':[
				['0','guide_npc002', 'guide_talk244'],
				['0','guide_npc002', 'guide_talk245'],
			],
		},	
		{
			'click':{
				'shade': 0,					
				'shield': 2,				
				'arrow': 2,					
				'rect': 2,					
				'type': 'building',		
				'objName': 'building026',	
			},	
		}				
		],
	},
	'awaken_guide':{		
		'awaken_g002':[
			{
				'talks':[
					['0','guide_npc002', 'guide_talk246'],
					['0','guide_npc002', 'guide_talk247'],
				],
			},
				
		],
	},
	'animal_guide':{		

		'animal_g002':[
			{
				'talks': [
					[0, '', 'guide_talk250'],	
					[0, '', 'guide_talk252'],	
				]	
			},	
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'list_0',	      
				},	
			},
			{
				'talks': [
					[0, '',  'guide_talk252'],	

				]	
			},	
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'btn_beast',	      
				},	
			},
			{
				'talks':[
					[0, '',  'guide_talk253'],	
					[0, '',  'guide_talk254'],
				],
			},	
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'panel',		
					'objName': 'new_task',	
				},	
			},	
			{
				'talks':[
					[0, '',  'guide_talk275'],	
					[0, '',  'guide_talk256'],	
				],
			},				
		],
	},
	'tomb_guide':{		

		'tomb_g002':[
			{
				'talks': [
					['hero702', '', 'guide_talk260'],	
					['hero724', '', 'guide_talk262'],	
				]	
			},	
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'ui',		
					'objName': 'btn_fight',	      
				},	
			},
			{
				'talks': [
					['hero702', '', 'guide_talk262'],	
	
				]	
			},
	
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'tab_2',	      
				},	
			},

			{
				'talks': [
					['hero702', '', 'guide_talk263'],	
					['hero724', '', 'guide_talk264'],	
				]	
			},
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'btn_tomb',	     
				},	
			},
			{
				'talks': [
					['hero724', '', 'guide_talk265'],	
					['hero724', '', 'guide_talk266'],
					['hero724', '', 'guide_talk267'],	
				]	
			},
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'imgMattock',	
				},	
			},
	
				
		],
	},
	'gwent_guide':{		

		'gwent_g002':[
			{
				'talks': [
					['hero766', '', 'guide_talk270'],	
					['hero724', '', 'guide_talk272'],	
				]	
			},	
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'ui',		
					'objName': 'btn_fight',	      
				},	
			},
			{
				'talks': [
					['hero724', '', 'guide_talk272'],	
	
				]	
			},
	
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'tab_2',	      
				},	
			},

			{
				'talks': [
					['hero724', '', 'guide_talk273'],	
					['hero724', '', 'guide_talk274'],	
				]	
			},
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'btn_gwent',	     
					'operate': [
						['mStack_item2', 'scrollTo',0, 250], 
						['mStack_item2', 'vScrollBar.touchScrollEnable',0], 
					]
				},	
			},
			{
				'talks': [
					['hero724', '', 'guide_talk275'],	

				]	
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'list_0',	      
				},	
			},
			{
				'talks': [
					['hero724', '', 'guide_talk276'],	
					['hero724', '', 'guide_talk277'],	
				]	
			},		
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'panel',		
					'objName': 'btn0',	      
				},	
			},	
			{
				'talks': [
					['hero724', '', 'guide_talk278'],	
					['hero724', '', 'guide_talk279'],

				]	
			},	
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'btn_ok',	      
				},	
			},	
			{
				'talks': [
					['hero724', '', 'guide_talk280'],	

	
				]	
			},
		],
	},
	'gwentbattle_guide':{		

		'gwentbattle_g002':[
			{
				'talks': [
					['hero724', '', 'guide_talk282'],	
					['hero724', '', 'guide_talk282'],	
				]	
			},	
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'panel',		
					'objName': 'list_0',	      
				},	
			},
				
		],
	},
	'revived_guide':{		

		'revived_g002':[
			{
				'talks': [
					['hero724', '', 'guide_talk275'],	
					['hero787', '', 'guide_talk292'],	
				]	
			},	
			{
				'goto':{
					'type': 2,	
					'buildingID':'building028',
					'state':2,
				
				},
			},
			{
				'talks': [
					['hero724', '', 'guide_talk292'],	
					['hero787', '', 'guide_talk293'],	
				]	
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'building',		
					'objName': 'building028',	
				},	
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 0,					
					'rect': 2,					
					'type': 'panel',		
					'objName': 'btn_free',	
					'close':2,
				},	
			},
							
		],
		'revived_g002':[			
			{
				'talks': [
					['hero787', '', 'guide_talk294'],	
	
				]	
			},
	
			{
				'goto':{
					'type': 2,	
					'buildingID':'building028',
					'state':2,
				
				},
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'building',		
					'objName': 'building028',	
				},	
			},
			{
				'talks': [
					['hero787', '', 'guide_talk295'],	
					['hero724', '', 'guide_talk296'],	
				]	
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'building',		
					'objName': '26',						
				},	
			},
			{
				'talks': [
					['hero787', '', 'guide_talk297'],	
					['hero787', '', 'guide_talk298'],
	
				]	
			},
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'list_0_0',	
				},	
			},
			{
				'click':{},
			},
			{
				'click':{},
			},
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'panel',		
					'objName': 'btn_talk',	
				},
			},	
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'panel',		
					'objName': 'btn_finish',	
				},
			},	
			{
				'talks': [
					['hero787', '', 'guide_talk299'],	
					['hero787', '', 'guide_talk300'],
					['hero787', '', 'guide_talk302'],	
				]	
			},
		],
	},
	'turtle_guide':{
		'turtle_g002':[   
			{
				'talks': [
					['hero724', 'guide_npc005', 'guide_talk320'],				
					['hero786', 'guide_npc006', 'guide_talk322'],	
				]	
			},	
			{
				'goto':{
					'type': 2,	
					'buildingID':'building029',
					'state':2,
				
				},
			},
			{
				'talks': [				
					['hero786', 'guide_npc006', 'guide_talk322'],	
	
				]	
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'building',		
					'objName': 'building029',	
				},	
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 0,					
					'rect': 2,					
					'type': 'panel',		
					'objName': 'btn_free',	
					'close':2,
				},	
			},
							
		],
		'turtle_g002':[   
			[
				{
					'click':{
						'shade': 2,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'ui',		
						'objName': 'btn_map2',	   
					},	
				},			
			],
			{
				'goto':{
					'type': 2,	
					'buildingID':'building029',
					'state':2,
				
				},
			},
			{
				'talks': [
					['hero786', 'guide_npc006', 'guide_talk323'],	
					['hero724', 'guide_npc005', 'guide_talk324'],	
				]	
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'building',		
					'objName': 'building029',	
				},	
			},
			{
				'talks': [
					['hero786', 'guide_npc006', 'guide_talk325'],	

				]	
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'building',		
					'objName': '30',						
				},	
			},
			{
				'talks': [
					['hero786', 'guide_npc006', 'guide_talk326'],	

				]	
			},			
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'btnWildernessEnter',	      
				},	
			},
			{
				'talks': [
					['hero724', 'guide_npc005', 'guide_talk327'],	
					['hero786', 'guide_npc006', 'guide_talk328'],	
				]	
			},	
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'list_0_btn',	      
				},	
			},	
			{
				'talks': [      
					['hero786', 'guide_npc006', 'guide_talk329'],	

				]	
			},
			{
				'use_prop':['item2293',2],   
			},	
			{
				'click':{},
			},					
			{
				'talks': [   
					['hero724', 'guide_npc005', 'guide_talk330'],	
					['hero786', 'guide_npc006', 'guide_talk332'],	
				]	
			},

		],
		'turtle_g003':[   
			[
				{
					'talks': [   
						['hero724', 'guide_npc005', 'guide_talk333'],	
	
					]	
				},
				{
					'goto':{
						'type': 2,	
						'buildingID':'building029',
						'state':2,
					
					},
				},
				{
					'click':{
						'shade': 0,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'building',		
						'objName': 'building029',	
					},	
				},
				{
					'click':{
						'shade': 0,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'building',		
						'objName': '30',						
					},	
				},		
				{
					'click':{
						'shade': 0,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'scene',		
						'objName': 'btnWildernessEnter',	      
					},	
				},
				{
					'click':{
						'shade': 0,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'scene',		
						'objName': 'list_0',	      
					},	
				},			
			],
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'btn',	      
				},	
			},
			{
				'talks': [   
					['hero786', 'guide_npc006', 'guide_talk332'],	

				]	
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'panel',		
					'objName': 'godList_0',	      
				},	
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'panel',		
					'objName': 'btn',	      
				},	
			},
		],
	},

	'god_guide':{
		'god_g002':[
			{
				'talks': [
					['hero724', 'guide_npc005', 'guide_talk340'],			

				]	
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'list_0',	      
				},	
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'scene',		
					'objName': 'btn_god',	      
				},
			},	
			{
				'talks': [
					['hero724', 'guide_npc005', 'guide_talk342'],	

				]	
			},	
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'panel',		
					'objName': 'skillList_0',	      
				},	
			},	
			{
				'talks': [
					['hero724', 'guide_npc005', 'guide_talk342'],			
	
				]	
			},			
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'panel',		
					'objName': 'btn_close',	      
				},	
			},
			{
				'talks': [
					['hero724', 'guide_npc005', 'guide_talk343'],			
	
				]	
			},	
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'panel',		
					'objName': 'btn_install',	      
				},	
			},	
		],		
	},
	'peach_guide':{	
		'peach_g002':[
			[
				{
					'talks': [   
						['hero7700', 'guide_npc007', 'guide_talk957'],	
	
					]	
				},
				{
					'click':{
						'shade': 2,					
						'shield': 2,				
						'arrow': 2,					
						'rect': 2,					
						'type': 'ui',		
						'objName': 'btn_map2',	
					},	
				},
			
			],
			{
				'goto':{
					'homeEffect':'godsWell',				
				},
			},
			{
				'talks': [
					['hero7700', 'guide_npc007', 'guide_talk950'],				
					['hero724', 'guide_npc005', 'guide_talk952'],	
				]	
			},	
			{
				'click':{
					'shade': 2,					
					'shield': 2,				
					'arrow': 0,					
					'rect': 2,					
					'type': 'homeEffect',		
					'objName': 'godsWell_bubble',	      
				},	
			},
			{
				'talks': [
					['hero7700', 'guide_npc007', 'guide_talk952'],				
					['hero724', 'guide_npc005', 'guide_talk953'],	
				]	
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'panel',		
					'objName': 'btnShop',	      
				},	
			},
			{
				'talks': [
					['hero724', 'guide_npc005', 'guide_talk954'],				
					['hero7700', 'guide_npc007', 'guide_talk375'],	
				]	
			},
			{
				'click':{
					'shade': 0,					
					'shield': 2,				
					'arrow': 2,					
					'rect': 2,					
					'type': 'panel',		
					'objName': 'btnShop',	      
				},	
			},
			{
				'talks': [
					['hero7700', 'guide_npc007', 'guide_talk956'],				

				]	
			},
							
		],
	},
	'jump_guide':{
		'finish_ftask':[['246','244'],['32','62'],['953','375']],	
		'all_reward':{	
			'coin':950,
			'gold':82500,
			'food':2000000,
			'wood':2000000,
			'iron':2200000,
			'merit':800,
			'item002':26, 
			'item026':200, 
			'item032':5, 
			'item037':5, 
			'item232':4, 
			'item207':200, 
			'item225':25, 
			'item702':5, 		
		},
		'user_lv':8,	
		'get_hero':['hero702','hero702'],
    'task': {		
            'task032': [
                0, 
                2
            ],
    },
		'call':{
			'alien_first':{},		
		},
	},
}