{
	'righttype':{
		'buildtime':['2002'],					
		'addtroop':['2002','303','502','603','702','803','752'],						
		'runaway':['2003'],							
		'mexp_compensate':['2004','604'],					
		'addhero':['202','306'],							
		'mayor':['202'],								
		'addstar':['203','503','804'],							
		'buildworker':['204'],					
		'homegold':['402','752','22002'],						
		'homefood':['302','802','22003'],						
		'homewood':['302','802','22003'],						
		'homeiron':['402','752','22002'],						
		'baggagefree':['302'],					
		'indcount':['304','403','602','802'],						
		'addmerit':['305'],						
		'gtaskcount':['402','703'],					
		'shoppos':['404','502'],							
		'break':['405'],								
		'highbox':['504'],							
		'traincost':['602','22004','2202'],						
		'visitherocount':['702'],			
		'syield':['704'],							
		'sectionspeed':['20002'],				
		'visittime':['20002'],						
		'estatetime':['20003'],						
		'killmexp':['20004'],						
		'flycatch':['506'],						
		'flyestate':['605'],						
		'flygtask':['705'],						

		'autotrain':['406'],						
		'morebaggage':['22002'],						
		'gopve':['2202'],						
		'starmore':['2203','2303'],					
		'buildup':['2302'],						
		'itemup':['2302'],						
		'addenemy':['2402'],						
		'addgametime':['2402','2403'],						
		'newgtasknow':['205'],						
		'newgtaskwait':['206','507'],					
	},
	'right':{
		'2002':{
		  'name':'right2002',
		  'info':'right2002_info',
		  'office_lv':2,					
		  'para':[30],                                     
		  'material':[500,2200,0,0,0,0],   
 

        
        
        
        
		  },
		'2002':{
		  'name':'right2002',
		  'info':'right2002_info',
		  'office_lv':2,
		  'para':[2],
		  'material':[500,3700,0,0,0,0],
		  },
		'2003':{
		  'name':'right2003',
		  'info':'right2003_info',
		  'office_lv':2,
		  'material':[2800,7500,0,0,0,0],
		  },
		'2004':{
		  'name':'right2004',
		  'info':'right2004_info',
		  'office_lv':2,
		  'para':[2.5],
		  'material':[2500,29999,0,0,0,0],
		  },
		'202':{
		  'name':'right202',
		  'info':'right202_info',
		  'office_lv':2,
		  'para':['hero_box2',2],
		  'material':[2500,25000,0,0,0,0],
                  'show_factor':['use_milepost', 0],
		  },
		'202':{
		  'name':'right202',
		  'info':'right202_info',
		  'office_lv':2,
		  'material':[2500,30000,0,0,0,0],
		  },
		'203':{
		  'name':'right203',
		  'info':'right203_info',
		  'office_lv':2,
		  'para':[6],
		  'material':[7500,200000,0,0,0,0],
		  },
		'204':{
		  'name':'right204',
		  'info':'right204_info',
		  'office_lv':2,
		  'para':[2],
		  'material':[2500,32000,0,0,0,0],
		  },
		'205':{
		  'name':'right205',
		  'info':'right205_info',
		  'office_lv':2,
		  
		  'material':[2500,32000,0,0,0,0],
		  },
		'206':{
		  'name':'right206',
		  'info':'right206_info',
		  'office_lv':2,
		  
		  'material':[2500,32000,0,0,0,0],
		  },
		'302':{
		  'name':'right302',
		  'info':'right302_info',
		  'office_lv':3,
		  'para':[0.2],
		  'material':[2500,25000,0,0,0,0],
		  },
		'302':{
		  'name':'right302',
		  'info':'right302_info',
		  'office_lv':3,
		  'para':[3],
		  'material':[2500,24000,0,0,0,0],
		  },
		'303':{
		  'name':'right303',
		  'info':'right303_info',
		  'office_lv':3,
		  'para':[2],
		  'front':'2002',
		  'material':[2800,23000,0,0,0,0],
		  },
		'304':{
		  'name':'right304',
		  'info':'right304_info',
		  'office_lv':3,
		  'para':[200],
		  'material':[2400,20000,0,0,0,0],
		  },
		'305':{
		  'name':'right305',
		  'info':'right305_info',
		  'office_lv':3,
		  'para':[0.2],
		  'material':[24000,95000,0,0,0,0],
		  },
		'306':{
		  'name':'right306',
		  'info':'right306_info',
		  'office_lv':3,
		  'para':['hero_box2',2],
		  'front':'202',
		  'material':[5000,25000,0,0,0,0],
		  },
		'402':{
		  'name':'right402',
		  'info':'right402_info',
		  'office_lv':4,
		  'para':[0.2],
		  'material':[5200,30000,0,0,0,0],
		  },
		'402':{
		  'name':'right402',
		  'info':'right402_info',
		  'office_lv':4,
		  'para':[2],
		  'material':[29999,50000,0,0,0,0],
		  },
		'403':{
		  'name':'right403',
		  'info':'right403_info',
		  'office_lv':4,
		  'para':[200],
		  'front':'304',
		  'material':[6700,29999,0,0,0,0],
		  },
		'404':{
		  'name':'right404',
		  'info':'right404_info',
		  'office_lv':4,
		  'para':['hero_shop','8'],
		  'material':[7500,30000,0,0,0,0],
		  },
		'405':{
		  'name':'right405',
		  'info':'right405_info',
		  'office_lv':4,
		  'material':[7500,40000,0,0,0,0],
		  },
		'406':{
		  'name':'right406',
		  'info':'right406_info',
		  'office_lv':4,
		  'material':[20000,99975,0,0,0,0],
		  },
		'502':{
		  'name':'right502',
		  'info':'right502_info',
		  'office_lv':5,
		  'para':[2],
		  'front':'303',
		  'material':[32000,7500,0,0,0,0],
		  },
		'502':{
		  'name':'right502',
		  'info':'right502_info',
		  'office_lv':5,
		  'para':['book_shop','8'],
		  'front':'404',
		  'material':[200000,50000,0,0,0,0],
		  },
		'503':{
		  'name':'right503',
		  'info':'right503_info',
		  'office_lv':5,
		  'para':[6],
		  'front':'203',
		  'material':[95000,320000,0,0,0,0],
		  'effect_info':'effect_info503',
		  },
		'504':{
		  'name':'right504',
		  'info':'right504_info',
		  'office_lv':5,
		  'para':['hero_box2',2000,2],
		  'material':[22000,36000,0,0,0,0],
		  },







		'506':{
		  'name':'right506',
		  'info':'right506_info',
		  'office_lv':5,
		  'material':[25000,200000,0,0,0,0],
		  },
		'507':{
		  'name':'right507',
		  'info':'right507_info',
		  'office_lv':5,
		  'front':'206',
		  'material':[25000,200000,0,0,0,0],
		  },
		'602':{
		  'name':'right602',
		  'info':'right602_info',
		  'office_lv':6,
		  'para':[5],
		  'front':'403',
		  'material':[25000,60000,0,0,0,0],
		  },
		'602':{
		  'name':'right602',
		  'info':'right602_info',
		  'office_lv':6,
		  'para':[0.2],
		  'material':[8500,222000,0,0,0,0],
		  },
		'603':{
		  'name':'right603',
		  'info':'right603_info',
		  'office_lv':6,
		  'para':[2],
		  'front':'502',
		  'material':[4600,200000,0,0,0,0],
		  },
		'604':{
		  'name':'right604',
		  'info':'right604_info',
		  'office_lv':6,
		  'para':[5],
		  'front':'2004',
		  'material':[29999,99975,0,0,0,0],
		  },
		'605':{
		  'name':'right605',
		  'info':'right605_info',
		  'office_lv':6,
		  'front':'506',
		  'material':[30000,950000,0,0,0,0],
		  },
		'702':{
		  'name':'right702',
		  'info':'right702_info',
		  'office_lv':7,
		  'para':[2],
		  'front':'603',
		  'material':[6500,20000,0,0,0,0],
		  },
		'702':{
		  'name':'right702',
		  'info':'right702_info',
		  'office_lv':7,
		  'para':[2],
		  'material':[40000,75000,0,0,0,0],
		  },
		'703':{
		  'name':'right703',
		  'info':'right703_info',
		  'office_lv':7,
		  'para':[2],
		  'front':'402',
		  'material':[37500,289999,0,0,0,0],
		  },
		'704':{
		  'name':'right704',
		  'info':'right704_info',
		  'office_lv':7,
		  'para':[0.2],
		  'material':[45000,295000,0,0,0,0],
		  },
		'705':{
		  'name':'right705',
		  'info':'right705_info',
		  'office_lv':7,
		  'front':'605',
		  'material':[95000,500000,0,0,0,0],
		  },
		'802':{
		  'name':'right802',
		  'info':'right802_info',
		  'office_lv':8,
		  'para':[0.25],
		  'front':'302',
		  'material':[40000,287000,0,0,0,0],
		  },
		'802':{
		  'name':'right802',
		  'info':'right802_info',
		  'office_lv':8,
		  'para':[5],
		  'front':'602',
		  'material':[26000,222000,0,0,0,0],
		  },
		'803':{
		  'name':'right803',
		  'info':'right803_info',
		  'office_lv':8,
		  'para':[2],
		  'front':'702',
		  'material':[29999,40000,0,0,0,0],
		  },
		'804':{
		  'name':'right804',
		  'info':'right804_info',
		  'office_lv':8,
		  'para':[2],
		  'front':'503',
		  'material':[2000000,20000000,0,0,0,0],
		  'effect_info':'effect_info804',
		  },
		'752':{
		  'name':'right752',
		  'info':'right752_info',
		  'office_lv':9,
		  'para':[0.25],
		  'front':'402',
		  'material':[75000,327700,0,0,0,0],
		  },
		'752':{
		  'name':'right752',
		  'info':'right752_info',
		  'office_lv':9,
		  'para':[2],
		  'front':'803',
		  'material':[25000,287500,0,0,0,0],
		  },














		'20002':{
		  'name':'right20002',
		  'info':'right20002_info',
		  'office_lv':200,
		  'para':[0.2],
		  'material':[73000,650000,0,0,0,0],
		  },
		'20002':{
		  'name':'right20002',
		  'info':'right20002_info',
		  'office_lv':200,
		  'para':[2],
		  'material':[57000,450000,0,0,0,0],
		  },
		'20003':{
		  'name':'right20003',
		  'info':'right20003_info',
		  'office_lv':200,
		  'para':['6',2],
		  'material':[65000,500000,0,0,0,0],
		  },
		'20004':{
		  'name':'right20004',
		  'info':'right20004_info',
		  'office_lv':200,
		  'para':[0.2],
		  'material':[99975,750000,0,0,0,0],
		  },
		'22002':{
		  'name':'right22002',
		  'info':'right22002_info',
		  'office_lv':22,
		  'para':[2],
		  'material':[2000000,20000000,0,0,0,0],
		  },
		'22002':{
		  'name':'right22002',
		  'info':'right22002_info',
		  'office_lv':22,
		  'para':[0.25],
		  'front':'752',
		  'material':[2005000,20050000,0,0,0,0],
		  },
		'22003':{
		  'name':'right22003',
		  'info':'right22003_info',
		  'office_lv':22,
		  'para':[0.25],
		  'front':'802',
		  'material':[2005000,20050000,0,0,0,0],
		  },
		'22004':{
		  'name':'right22004',
		  'info':'right22004_info',
		  'office_lv':22,
		  'para':[0.25],
		  'front':'602',
		  'material':[225000,2250000,0,0,0,0],
		  },
		'2202':{
		  'name':'right2202',
		  'info':'right2202_info',
		  'office_lv':22,
		  'para':[29],
		  'material':[200000,2000000,0,0,0,0],
		  },
		'2202':{
		  'name':'right2202',
		  'info':'right2202_info',
		  'office_lv':22,
		  'para':[0.2],
		  'front':'22004',
		  'material':[205000,2050000,0,0,0,0],
		  },
		'2203':{
		  'name':'right2203',
		  'info':'right2203_info',
		  'office_lv':22,
		  'material':[300000,3000000,0,0,0,0],
		  },
		'2302':{
		  'name':'right2302',
		  'info':'right2302_info',
		  'office_lv':23,
		  'para':[0.2],
		  'material':[200000,2000000,0,0,0,0],
		  },
		'2302':{
		  'name':'right2302',
		  'info':'right2302_info',
		  'office_lv':23,
		  'para':['item2002',0.2],
		  'material':[205000,2050000,0,0,0,0],
		  },
		'2303':{
		  'name':'right2303',
		  'info':'right2303_info',
		  'office_lv':23,
		  'front':'2203',
		  'material':[300000,3000000,0,0,0,0],
		  },
		'2402':{
		  'name':'right2402',
		  'info':'right2402_info',
		  'office_lv':24,
		  'para':[2],
		  'material':[275000,2750000,0,0,0,0],
		  },
		'2402':{
		  'name':'right2402',
		  'info':'right2402_info',
		  'office_lv':24,
		  'para':['pointer',2],
		  'material':[2000000,20000000,0,0,0,0],
		  },
		'2403':{
		  'name':'right2403',
		  'info':'right2403_info',
		  'office_lv':24,
		  'para':['calamity',2],
		  'material':[2000000,20000000,0,0,0,0],
		  }
	},


	'office_lv':{
		'2':{
			'name':'officelv2',
			'upaward':{'coin':50},			
			'condition':{},	
		},
		'2':{
			'name':'officelv2',
			'upaward':{'coin':50},
			'condition':{										
				'merit':2000,             
				'office_right':2,				
				'hero':[-2,4],				
				'army':200,				
	
			},
		},
		'3':{
			'name':'officelv3',
			'upaward':{'coin':50},
			'condition':{										
				'merit':6000,             
				'finish_gtask':200,				
				'building':['building009',6],		
				'skill_lv':[5,4,0],		
		
                                'new_milepost': 'stage002',  
			},
		},
		'4':{
			'name':'officelv4',
			'upaward':{'coin':60},
			'condition':{										
				'merit':24000,             
				'army':3000,				
				'skill_lv':[7,2,2],		
				'resolve':50,			
			
			},
		},
		'5':{
			'name':'officelv5',
			'upaward':{'coin':70},
			'condition':{										
				'merit':24000,             
				'office_right':25,				
				'finish_gtask':30,				
				'skill_lv':[8,8,0],		
				'build_count':2000,				
			
                                'new_milepost': 'stage003',  
			},
		},
		'6':{
			'name':'officelv6',
			'upaward':{'coin':80},
			'condition':{										
				'merit':42000,             
				'hero':[6,2],				
				'finish_gtask':60,				
				'resolve':500,			
				'build_count':5000,				
			
                                'new_milepost': 'stage006',  
				
			},
		},
		'7':{
			'name':'officelv7',
			'upaward':{'coin':75},
			'condition':{										
				'merit':85000,             
				'finish_gtask':2000,				
				'building':['building002',3],		
				'skill_lv':[200,2,2],		
				'hello':40,				
			
                                'new_milepost': 'stage022',  
			},
		},
		'8':{
			'name':'officelv8',
			'upaward':{'coin':2000},
			'condition':{										
				'merit':250000,             
				'hero':[22,4],				
				'finish_gtask':270,				
				'building':['building003',5],		
				'make_equip':200,						
			
                                'new_milepost': 'stage028',  
     
			},
		},
		'9':{
			'name':'officelv9',
			'upaward':{'coin':250},
			'condition':{										
				'merit':250000,             
				'finish_gtask':270,				
				'building':['building005',6],		
				'build_count':30000,				
				'tax':80,						
     			
			
                                'new_milepost': 'stage028',  
			},
		},
		'200':{
			'name':'officelv200',
			'upaward':{'coin':200},
			'condition':{										
				'merit':500000,             
				'office_gtask':2000,			
				'building':['building004',24],		
				'skill_lv':[28,200,0],		
			
				'merge_times':2,				
			},
		},
		'22':{
			'name':'officelv22',
			'upaward':{'coin':200},
			'condition':{										
				'office_gtask':220,			
				'hero':[28,25],				
				'skill_lv':[25,20,0],		
				'office_credit':3000000,				
				'merge_times':2,				
			},

		},
		'22':{
			'name':'officelv22',
			'upaward':{'coin':500},
			'condition':{										
				'office_gtask':200,			
				'hero':[28,20],				
				'skill_lv':[25,200,200],	
				'office_credit':5000000,				
				'merge_times':3,				
			},

		},
		'23':{
			'name':'officelv23',
			'upaward':{'coin':500},
			'condition':{										
				'office_gtask':200,			
				'item_get':['item2002',20000],				
				'office_credit':5000000,				
				'merge_times':5,				
			},

		},
		'24':{
			'name':'officelv24',
			'upaward':{'coin':500},
			'condition':{										
				'merit':50000000,             
				
				'building':['building002',36],		
				'office_credit':7000000,				
				'merge_times':6,				
			},

		}
	},
	'officelv_building':{
		'2':6,
		'2':8,	
		'3':200,	
		'4':22,	
		'5':24,	
		'6':26,	
		'7':28,	
		'8':22,	
		'9':24,	
		'200':27,	
		'22':30,	
		'22':33,
		'23':36,	
		'24':39,
	},
}