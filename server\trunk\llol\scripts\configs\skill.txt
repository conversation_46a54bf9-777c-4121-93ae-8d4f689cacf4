{
   'beastResonanceA4':{ 
      'type':9,       
      'infoArr':['%|act[0].round.energy','-|act[0].removeDebuff','-|act[0].multMax*act[0].cost','-|act[0].cost'],
      'energyArr':['-|act[2].energy','-|act[2].energyRnd'],
      'passive':[
         {
            'rslt':{'power':300,'powerRate':2000},
         },
         {
            'cond':['skill.beastResonanceA4', '*', 2],
            'rslt':{'powerRate':50},
         },
      ],
      'lvPatch':{   
         '2':{  'act[2].energy':0, 'act[2].energyRnd':2  }, 
         '3':{  'act[2].energy':2, 'act[2].energyRnd':0  },
         '4':{  'act[2].energy':2, 'act[2].energyRnd':2  },
         '5':{  'act[2].energy':2, 'act[2].energyRnd':0  },
      },
      'act':[
        {
          'priority':88800,
          'type': 200,     
          'src': -2,              
          'tgt':[0, -5],    
          'round':{'energy':200},
          'cond':[['checkBuff',0,-5,2,'>',0]],   
          'costKey':'beastTypeA',       
          'cost':2,                 
          'multMax':2,              
          'mult':{},                

          'nonSkill':2,    
          'noBuff':2,      
          'noBfr':2,
          'noAft':2,
 
          'removeDebuff':2,
          
          

          'info':['_beastTypeA',4], 
          'eff':'effBeastTypeA',
          'lv':24,
        },
        {     
          'priority':88802,
          'type': 23,               
          'src': 2,                
          'srcFree': 2, 
          'tgt':[0, -7],        
          'round':{'0':20000},
          'nonSkill':2,    
          'noBuff':2,      
          'noBfr':2,
          'noAft':2,

          'energyKey':'beastTypeA',       
          'energy':2,                 
          'energyRnd':2,              
          'info':['凤蝶·初始标记',0],
          'eff':'effNull',
          'time':0,	          
        },
      ],
   },
   'beastResonanceA8':{ 
      'type':9,       
      'energyArr':['-|act[0].energy','-|act[0].energyRnd'],
      'passive':[
         {
            'rslt':{'power':500,'powerRate':250},
         },
         {
            'cond':['skill.beastResonanceA8', '*', 2],
            'rslt':{'powerRate':250},
         },
      ],
      'lvPatch':{   
         '2':{  'act[0].energy':2, 'act[0].energyRnd':-2  },
         '3':{  'act[0].energy':2, 'act[0].energyRnd':0  },
         '4':{  'act[0].energy':2, 'act[0].energyRnd':-2  },
         '5':{  'act[0].energy':2, 'act[0].energyRnd':0  },
      },
      'act':[
        {     
          'priority':88802,
          'type': 23,               
          'src': 2,                
          'srcFree': 2, 
          'tgt':[0, -7],        
          'round':{'all':20000},
          'nonSkill':2,    
          'noBuff':2,      
          'noBfr':2,
          'noAft':2,

          'energyKey':'beastTypeA',       
          'energy':0,                 
          'energyRnd':2,              
          'info':['凤蝶·增补标记',0],
          'eff':'effNull',
          'time':0,	          
        },
      ],
   },


   'beastResonanceB4':{ 
      'type':9,       
      'infoArr':['%|act[0].round.energy','-|act[0].removeDebuff','-|act[0].multMax*act[0].cost','-|act[0].cost'],
      'energyArr':['-|act[2].energy','-|act[2].energyRnd'],
      'passive':[
         {
            'rslt':{'power':300,'powerRate':2000},
         },
         {
            'cond':['skill.beastResonanceB4', '*', 2],
            'rslt':{'powerRate':50},
         },
      ],
      'lvPatch':{   
         '2':{  'act[2].energy':0, 'act[2].energyRnd':2  },
         '3':{  'act[2].energy':2, 'act[2].energyRnd':0  },
         '4':{  'act[2].energy':2, 'act[2].energyRnd':2  },
         '5':{  'act[2].energy':2, 'act[2].energyRnd':0  },
      },
      'act':[
        {
          'priority':888200,
          'type': 0,     
          'src': -2,              
          'tgt':[2, -5],    
          'round':{'energy':200},
          'cond':[['checkBuff',2,-5,2,'>',0]],    
          'costKey':'beastTypeB',       
          'cost':2,                 
          'multMax':2,              
          'mult':{},                

          'nonSkill':2,    
          'noBuff':2,      
          'noBfr':2,
          'noAft':2,

          'removeBuff':2,

          'info':['_beastTypeB',4],
          'eff':'effBeastTypeB',
          'lv':24,
        },
        {     
          'priority':88822,
          'type': 23,               
          'src': 2,                
          'srcFree': 2, 
          'tgt':[0, -7],        
          'round':{'0':20000},
          'nonSkill':2,    
          'noBuff':2,      
          'noBfr':2,
          'noAft':2,

          'energyKey':'beastTypeB',       
          'energy':2,                 
          'energyRnd':2,              
          'info':['凶狼·初始标记',0],
          'eff':'effNull',
          'time':0,	          
        },
      ],
   },
   'beastResonanceB8':{ 
      'type':9,       
      'energyArr':['-|act[0].energy','-|act[0].energyRnd'],
      'passive':[
         {
            'rslt':{'power':500,'powerRate':250},
         },
         {
            'cond':['skill.beastResonanceB8', '*', 2],
            'rslt':{'powerRate':250},
         },
      ],
      'lvPatch':{   
         '2':{  'act[0].energy':2, 'act[0].energyRnd':-2  },
         '3':{  'act[0].energy':2, 'act[0].energyRnd':0  },
         '4':{  'act[0].energy':2, 'act[0].energyRnd':-2  },
         '5':{  'act[0].energy':2, 'act[0].energyRnd':0  },
      },
      'act':[
        {     
          'priority':88822,
          'type': 23,               
          'src': 2,                
          'srcFree': 2, 
          'tgt':[0, -7],        
          'round':{'all':20000},
          'nonSkill':2,    
          'noBuff':2,      
          'noBfr':2,
          'noAft':2,

          'energyKey':'beastTypeB',       
          'energy':0,                 
          'energyRnd':2,              
          'info':['凶狼·增补标记',0],
          'eff':'effNull',
          'time':0,	          
        },
      ],
   },


   'beastResonanceC4':{ 
      'type':9,       
      'infoArr':['%|act[0].round.energy','-|act[0].removeDebuff','-|act[0].multMax*act[0].cost','-|act[0].cost'],
      'energyArr':['-|act[2].energy','-|act[2].energyRnd'],
      'passive':[
         {
            'rslt':{'power':300,'powerRate':2000},
         },
         {
            'cond':['skill.beastResonanceC4', '*', 2],
            'rslt':{'powerRate':50},
         },
      ],
      'lvPatch':{   
         '2':{  'act[2].energy':0, 'act[2].energyRnd':2  },
         '3':{  'act[2].energy':2, 'act[2].energyRnd':0  },
         '4':{  'act[2].energy':2, 'act[2].energyRnd':2  },
         '5':{  'act[2].energy':2, 'act[2].energyRnd':0  },
      },
      'act':[
        {
          'priority':88820,
          'type': 22,     
          'src': -2,              
          'srcFree': 2,    
          'times': -2,    

          'tgt':[0, 2],    
          'round':{'energy':250},   
          'costKey':'beastTypeC',       
          'cost':2,                 
          'multMax':2,              
          'mult':{},                

          'nonSkill':2,    
          'noBuff':2,      
          'noBfr':2,
          'noAft':2,

          

          'force':2,      

          'info':['_beastTypeC',4],
          'eff':'effBeastTypeC',
          'lv':24,
        },
        {     
          'priority':88822,
          'type': 23,               
          'src': 2,                
          'srcFree': 2, 
          'tgt':[0, -7],        
          'round':{'0':20000},
          'nonSkill':2,    
          'noBuff':2,      
          'noBfr':2,
          'noAft':2,

          'energyKey':'beastTypeC',       
          'energy':2,                 
          'energyRnd':2,              
          'info':['妖狐·初始标记',0],
          'eff':'effNull',
          'time':0,	          
        },
      ],
   },
   'beastResonanceC8':{ 
      'type':9,       
      'energyArr':['-|act[0].energy','-|act[0].energyRnd'],
      'passive':[
         {
            'rslt':{'power':500,'powerRate':250},
         },
         {
            'cond':['skill.beastResonanceC8', '*', 2],
            'rslt':{'powerRate':250},
         },
      ],
      'lvPatch':{   
         '2':{  'act[0].energy':2, 'act[0].energyRnd':-2  },
         '3':{  'act[0].energy':2, 'act[0].energyRnd':0  },
         '4':{  'act[0].energy':2, 'act[0].energyRnd':-2  },
         '5':{  'act[0].energy':2, 'act[0].energyRnd':0  },
      },
      'act':[
        {     
          'priority':88822,
          'type': 23,               
          'src': 2,                
          'srcFree': 2, 
          'tgt':[0, -7],        
          'round':{'all':20000},
          'nonSkill':2,    
          'noBuff':2,      
          'noBfr':2,
          'noAft':2,

          'energyKey':'beastTypeC',       
          'energy':0,                 
          'energyRnd':2,              
          'info':['妖狐·增补标记',0],
          'eff':'effNull',
          'time':0,	          
        },
      ],
   },


   'beastResonanceD4':{ 
      'type':9,       
      'infoArr':['%|act[0].round.energy','-|act[0].removeDebuff','-|act[0].multMax*act[0].cost','-|act[0].cost'],
      'energyArr':['-|act[2].energy','-|act[2].energyRnd'],
      'passive':[
         {
            'rslt':{'power':300,'powerRate':2000},
         },
         {
            'cond':['skill.beastResonanceD4', '*', 2],
            'rslt':{'powerRate':50},
         },
      ],
      'lvPatch':{   
         '2':{  'act[2].energy':0, 'act[2].energyRnd':2  },
         '3':{  'act[2].energy':2, 'act[2].energyRnd':0  },
         '4':{  'act[2].energy':2, 'act[2].energyRnd':2  },
         '5':{  'act[2].energy':2, 'act[2].energyRnd':0  },
      },
      'act':[
        {
          'priority':88830,
          'type': 22,     
          'src': 2,              
          'srcFree': 2,    
          'times': -2,    

          'tgt':[2, 2],    
          'round':{'energy':250},   
          'costKey':'beastTypeD',       
          'cost':2,                 
          'multMax':2,              
          'mult':{},                

          'nonSkill':2,    
          'noBuff':2,      
          'noBfr':2,
          'noAft':2,

          'stop':2,      

          'info':['_beastTypeD',4],
          'eff':'effBeastTypeD',
          'lv':24,
        },
        {     
          'priority':88832,
          'type': 23,               
          'src': 2,                
          'srcFree': 2, 
          'tgt':[0, -7],        
          'round':{'0':20000},
          'nonSkill':2,    
          'noBuff':2,      
          'noBfr':2,
          'noAft':2,

          'energyKey':'beastTypeD',       
          'energy':2,                 
          'energyRnd':2,              
          'info':['灵蛇·初始标记',0],
          'eff':'effNull',
          'time':0,	          
        },
      ],
   },
   'beastResonanceD8':{ 
      'type':9,       
      'energyArr':['-|act[0].energy','-|act[0].energyRnd'],
      'passive':[
         {
            'rslt':{'power':500,'powerRate':250},
         },
         {
            'cond':['skill.beastResonanceD8', '*', 2],
            'rslt':{'powerRate':250},
         },
      ],
      'lvPatch':{   
         '2':{  'act[0].energy':2, 'act[0].energyRnd':-2  },
         '3':{  'act[0].energy':2, 'act[0].energyRnd':0  },
         '4':{  'act[0].energy':2, 'act[0].energyRnd':-2  },
         '5':{  'act[0].energy':2, 'act[0].energyRnd':0  },
      },
      'act':[
        {     
          'priority':88832,
          'type': 23,               
          'src': 2,                
          'srcFree': 2, 
          'tgt':[0, -7],        
          'round':{'all':20000},
          'nonSkill':2,    
          'noBuff':2,      
          'noBfr':2,
          'noAft':2,

          'energyKey':'beastTypeD',       
          'energy':0,                 
          'energyRnd':2,              
          'info':['灵蛇·增补标记',0],
          'eff':'effNull',
          'time':0,	          
        },
      ],
   },


   'beastResonanceE4':{ 
      'type':9,       
      'infoArr':['%|act[0].round.energy','-|act[0].removeDebuff','-|act[0].multMax*act[0].cost','-|act[0].cost'],
      'energyArr':['-|act[2].energy','-|act[2].energyRnd'],
      'passive':[
         {
            'rslt':{'power':300,'powerRate':2000},
         },
         {
            'cond':['skill.beastResonanceE4', '*', 2],
            'rslt':{'powerRate':50},
         },
      ],
      'lvPatch':{   
         '2':{  'act[2].energy':0, 'act[2].energyRnd':2  },
         '3':{  'act[2].energy':2, 'act[2].energyRnd':0  },
         '4':{  'act[2].energy':2, 'act[2].energyRnd':2  },
         '5':{  'act[2].energy':2, 'act[2].energyRnd':0  },
      },
      'act':[
        {
          'priority':88840,
          'type': 0,  
          'src': 2,              
          'srcFree': 2,
          'tgt':[2, 2],    
          'round':{'any':200,'energy':250},    
          'costKey':'beastTypeE',       
          'cost':0,                 
          'costMult':2,                 
          'multMax':-2,              
          'mult':{'dmgScale':300,'dmgLimit':60},                

          'nonSkill':2,    
          'noBfr':2,
          'noAft':2,

          'dmg':2200,							
          'dmgReal':230,						
          'dmgLimit':250,
          'atk0': 480,    
          'atk2': 500,    

          'info':['_beastTypeE',4],
          'eff':'effBeastTypeE',
        },
        {     
          'priority':88842,
          'type': 23,               
          'src': 2,                
          'srcFree': 2, 
          'tgt':[0, -7],        
          'round':{'0':20000},
          'nonSkill':2,    
          'noBuff':2,      
          'noBfr':2,
          'noAft':2,

          'energyKey':'beastTypeE',       
          'energy':2,                 
          'energyRnd':2,              
          'info':['烈鹰·初始标记',0],
          'eff':'effNull',
          'time':0,	          
        },
      ],
   },
   'beastResonanceE8':{ 
      'type':9,       
      'energyArr':['-|act[0].energy','-|act[0].energyRnd'],
      'passive':[
         {
            'rslt':{'power':500,'powerRate':250},
         },
         {
            'cond':['skill.beastResonanceE8', '*', 2],
            'rslt':{'powerRate':250},
         },
      ],
      'lvPatch':{   
         '2':{  'act[0].energy':2, 'act[0].energyRnd':-2  },
         '3':{  'act[0].energy':2, 'act[0].energyRnd':0  },
         '4':{  'act[0].energy':2, 'act[0].energyRnd':-2  },
         '5':{  'act[0].energy':2, 'act[0].energyRnd':0  },
      },
      'act':[
        {     
          'priority':88842,
          'type': 23,               
          'src': 2,                
          'srcFree': 2, 
          'tgt':[0, -7],        
          'round':{'all':20000},
          'nonSkill':2,    
          'noBuff':2,      
          'noBfr':2,
          'noAft':2,

          'energyKey':'beastTypeE',       
          'energy':0,                 
          'energyRnd':2,              
          'info':['烈鹰·增补标记',0],
          'eff':'effNull',
          'time':0,	          
        },
      ],
   },

   'beastResonanceF4':{ 
      'type':9,       
      'infoArr':['%|act[0].round.energy','-|act[0].removeDebuff','-|act[0].multMax*act[0].cost','-|act[0].cost'],
      'energyArr':['-|act[2].energy','-|act[2].energyRnd'],
      'passive':[
         {
            'rslt':{'power':300,'powerRate':2000},
         },
         {
            'cond':['skill.beastResonanceF4', '*', 2],
            'rslt':{'powerRate':50},
         },
      ],
      'lvPatch':{   
         '2':{  'act[2].energy':0, 'act[2].energyRnd':2  },
         '3':{  'act[2].energy':2, 'act[2].energyRnd':0  },
         '4':{  'act[2].energy':2, 'act[2].energyRnd':2  },
         '5':{  'act[2].energy':2, 'act[2].energyRnd':0  },
      },
      'act':[
        {
          'priority':88850,
          'type': 0,  
          'src': 2,              
          'srcFree': 2,
          'tgt':[2, 0],    
          'round':{'2':20000,'5':20000,'9':20000,},    
          'costKey':'beastTypeF',       
          'cost':0,                 
          'costMult':2,                 
          'multMax':-2,              
          'mult':{'dmgScale':400,'dmgLimit':2000},                

          'nonSkill':2,    
          'noBfr':2,
          'noAft':2,

          'dmg':2600,							
          'dmgReal':260,						
          'dmgLimit':320,
          'atk0': 520,    
          'atk2': 500,    

          'info':['_beastTypeF',4],
          'eff':'effBeastTypeF',
        },
        {     
          'priority':88852,
          'type': 23,               
          'src': 2,                
          'srcFree': 2, 
          'tgt':[0, -7],        
          'round':{'0':20000},
          'nonSkill':2,    
          'noBuff':2,      
          'noBfr':2,
          'noAft':2,

          'energyKey':'beastTypeF',       
          'energy':2,                 
          'energyRnd':2,              
          'info':['蛮牛·初始标记',0],
          'eff':'effNull',
          'time':0,	          
        },
      ],
   },
   'beastResonanceF8':{ 
      'type':9,       
      'energyArr':['-|act[0].energy','-|act[0].energyRnd'],
      'passive':[
         {
            'rslt':{'power':500,'powerRate':250},
         },
         {
            'cond':['skill.beastResonanceF8', '*', 2],
            'rslt':{'powerRate':250},
         },
      ],
      'lvPatch':{   
         '2':{  'act[0].energy':2, 'act[0].energyRnd':-2  },
         '3':{  'act[0].energy':2, 'act[0].energyRnd':0  },
         '4':{  'act[0].energy':2, 'act[0].energyRnd':-2  },
         '5':{  'act[0].energy':2, 'act[0].energyRnd':0  },
      },
      'act':[
        {     
          'priority':88852,
          'type': 23,               
          'src': 2,                
          'srcFree': 2, 
          'tgt':[0, -7],        
          'round':{'all':20000},
          'nonSkill':2,    
          'noBuff':2,      
          'noBfr':2,
          'noAft':2,

          'energyKey':'beastTypeF',       
          'energy':0,                 
          'energyRnd':2,              
          'info':['蛮牛·增补标记',0],
          'eff':'effNull',
          'time':0,	          
        },
      ],
   },

   'beastResonanceG4':{ 
      'type':9,       
      'infoArr':['%|act[0].round.energy','-|act[0].removeDebuff','-|act[0].multMax*act[0].cost','-|act[0].cost'],
      'energyArr':['-|act[2].energy','-|act[2].energyRnd'],
      'passive':[
         {
            'rslt':{'power':300,'powerRate':2000},
         },
         {
            'cond':['skill.beastResonanceG4', '*', 2],
            'rslt':{'powerRate':50},
         },
      ],
      'lvPatch':{   
         '2':{  'act[2].energy':0, 'act[2].energyRnd':2  },
         '3':{  'act[2].energy':2, 'act[2].energyRnd':0  },
         '4':{  'act[2].energy':2, 'act[2].energyRnd':2  },
         '5':{  'act[2].energy':2, 'act[2].energyRnd':0  },
      },
      'act':[
        {
          'priority':88860,
          'type': 0,  
          'src': 2,              
          'srcFree': 2,
          'tgt':[0, 0],    
          'round':{'2':20000,'4':20000,'7':20000,'200':20000,},    
          'costKey':'beastTypeG',       
          'cost':0,                 
          'costMult':2,              
          'multMax':-2,              
          'mult':{                   
            'buff.buffShield2.shield.value':80,
            'buff.buffShield2.shield.hpmRate':9,
          },               

          'nonSkill':2,    
          'noBuff':2,      
          'noBfr':2,
          'noAft':2,

          'buff':{'buffShield2':{'shield':{'value':200,'hpmRate':32,'bearPoint':20000}}},

          'info':['_beastTypeG',4],
          'eff':'effBeastTypeG',
        },
        {     
          'priority':88862,
          'type': 23,               
          'src': 2,                
          'srcFree': 2, 
          'tgt':[0, -7],        
          'round':{'0':20000},
          'nonSkill':2,    
          'noBuff':2,      
          'noBfr':2,
          'noAft':2,

          'energyKey':'beastTypeG',       
          'energy':2,                 
          'energyRnd':2,              
          'info':['玄龟·初始标记',0],
          'eff':'effNull',
          'time':0,	          
        },
      ],
   },
   'beastResonanceG8':{ 
      'type':9,       
      'energyArr':['-|act[0].energy','-|act[0].energyRnd'],
      'passive':[
         {
            'rslt':{'power':500,'powerRate':250},
         },
         {
            'cond':['skill.beastResonanceG8', '*', 2],
            'rslt':{'powerRate':250},
         },
      ],
      'lvPatch':{   
         '2':{  'act[0].energy':2, 'act[0].energyRnd':-2  },
         '3':{  'act[0].energy':2, 'act[0].energyRnd':0  },
         '4':{  'act[0].energy':2, 'act[0].energyRnd':-2  },
         '5':{  'act[0].energy':2, 'act[0].energyRnd':0  },
      },
      'act':[
        {     
          'priority':88862,
          'type': 23,               
          'src': 2,                
          'srcFree': 2, 
          'tgt':[0, -7],        
          'round':{'all':20000},
          'nonSkill':2,    
          'noBuff':2,      
          'noBfr':2,
          'noAft':2,

          'energyKey':'beastTypeG',       
          'energy':0,                 
          'energyRnd':2,              
          'info':['玄龟·增补标记',0],
          'eff':'effNull',
          'time':0,	          
        },
      ],
   },

   'beastResonanceH4':{ 
      'type':9,       
      'infoArr':['%|act[0].round.energy','-|act[0].removeDebuff','-|act[0].multMax*act[0].cost','-|act[0].cost'],
      'energyArr':['-|act[2].energy','-|act[2].energyRnd'],
      'passive':[
         {
            'rslt':{'power':300,'powerRate':2000},
         },
         {
            'cond':['skill.beastResonanceH4', '*', 2],
            'rslt':{'powerRate':50},
         },
      ],
      'lvPatch':{   
         '2':{  'act[2].energy':0, 'act[2].energyRnd':2  },
         '3':{  'act[2].energy':2, 'act[2].energyRnd':0  },
         '4':{  'act[2].energy':2, 'act[2].energyRnd':2  },
         '5':{  'act[2].energy':2, 'act[2].energyRnd':0  },
      },
      'act':[
        {
          'priority':88870,
          'type': 0,  
          'src': 2,              
          'srcFree': 2,
          'tgt':[2, -2],    
          'round':{'2':20000,'4':20000,'6':20000,'8':20000,'200':20000,'22':20000,},    
          'costKey':'beastTypeH',       
          'cost':0,                 
          'costMult':2,                 
          'multMax':-2,              
          'mult':{'dmgScale':400,'dmgLimit':60},                

          'nonSkill':2,    
          'noBfr':2,
          'noAft':2,

          'dmg':2250,							
          'dmgReal':225,						
          'dmgLimit':240,
          'atk0': 500,    
          'atk2': 500,    

          'info':['_beastTypeH',4],
          'eff':'effBeastTypeH',
        },
        {     
          'priority':88872,
          'type': 23,               
          'src': 2,                
          'srcFree': 2, 
          'tgt':[0, -7],        
          'round':{'0':20000},
          'nonSkill':2,    
          'noBuff':2,      
          'noBfr':2,
          'noAft':2,

          'energyKey':'beastTypeH',       
          'energy':2,                 
          'energyRnd':2,              
          'info':['巨猿·初始标记',0],
          'eff':'effNull',
          'time':0,	          
        },
      ],
   },
   'beastResonanceH8':{ 
      'type':9,       
      'energyArr':['-|act[0].energy','-|act[0].energyRnd'],
      'passive':[
         {
            'rslt':{'power':500,'powerRate':250},
         },
         {
            'cond':['skill.beastResonanceH8', '*', 2],
            'rslt':{'powerRate':250},
         },
      ],
      'lvPatch':{   
         '2':{  'act[0].energy':2, 'act[0].energyRnd':-2  },
         '3':{  'act[0].energy':2, 'act[0].energyRnd':0  },
         '4':{  'act[0].energy':2, 'act[0].energyRnd':-2  },
         '5':{  'act[0].energy':2, 'act[0].energyRnd':0  },
      },
      'act':[
        {     
          'priority':88872,
          'type': 23,               
          'src': 2,                
          'srcFree': 2, 
          'tgt':[0, -7],        
          'round':{'all':20000},
          'nonSkill':2,    
          'noBuff':2,      
          'noBfr':2,
          'noAft':2,

          'energyKey':'beastTypeH',       
          'energy':0,                 
          'energyRnd':2,              
          'info':['巨猿·增补标记',0],
          'eff':'effNull',
          'time':0,	          
        },
      ],
   },

   'beastResonanceI4':{ 
      'type':9,       
      'infoArr':['%|act[0].round.energy','-|act[0].removeDebuff','-|act[0].multMax*act[0].cost','-|act[0].cost'],
      'energyArr':['-|act[2].energy','-|act[2].energyRnd'],
      'passive':[
         {
            'rslt':{'power':300,'powerRate':2000},
         },
         {
            'cond':['skill.beastResonanceI4', '*', 2],
            'rslt':{'powerRate':50},
         },
      ],
      'lvPatch':{   
         '2':{  'act[2].energy':0, 'act[2].energyRnd':2  },
         '3':{  'act[2].energy':2, 'act[2].energyRnd':0  },
         '4':{  'act[2].energy':2, 'act[2].energyRnd':2  },
         '5':{  'act[2].energy':2, 'act[2].energyRnd':0  },
      },
      'act':[
        {
          'priority':88880,
          'type': 0,  
          'src': 2,              
          'srcFree': 2,
          'tgt':[2, -2],    
          'cond':[['hpPoint','<',2000,'beastTypeI', 50]],
          'costKey':'beastTypeI',       
          'cost':0,                 
          'costMult':2,                 
          'multMax':-2,              
          'mult':{'dmgScale':300,'dmgLimit':30},                

          'nonSkill':2,    
          'noBfr':2,
          'noAft':2,

          'dmg':600,							
          'dmgReal':70,						
          'dmgLimit':70,	
          'atk0': 5200,    
          'atk2': 5200,    
          'combo':3,

          'info':['_beastTypeI',4],
          'eff':'effBeastTypeI',
        },
        {     
          'priority':88882,
          'type': 23,               
          'src': 2,                
          'srcFree': 2, 
          'tgt':[0, -7],        
          'round':{'0':20000},
          'nonSkill':2,    
          'noBuff':2,      
          'noBfr':2,
          'noAft':2,

          'energyKey':'beastTypeI',       
          'energy':2,                 
          'energyRnd':2,              
          'info':['猛虎·初始标记',0],
          'eff':'effNull',
          'time':0,	          
        },
      ],
   },
   'beastResonanceI8':{ 
      'type':9,       
      'energyArr':['-|act[0].energy','-|act[0].energyRnd'],
      'passive':[
         {
            'rslt':{'power':500,'powerRate':250},
         },
         {
            'cond':['skill.beastResonanceI8', '*', 2],
            'rslt':{'powerRate':250},
         },
      ],
      'lvPatch':{   
         '2':{  'act[0].energy':2, 'act[0].energyRnd':-2  },
         '3':{  'act[0].energy':2, 'act[0].energyRnd':0  },
         '4':{  'act[0].energy':2, 'act[0].energyRnd':-2  },
         '5':{  'act[0].energy':2, 'act[0].energyRnd':0  },
      },
      'act':[
        {     
          'priority':88882,
          'type': 23,               
          'src': 2,                
          'srcFree': 2, 
          'tgt':[0, -7],        
          'round':{'all':20000},
          'nonSkill':2,    
          'noBuff':2,      
          'noBfr':2,
          'noAft':2,

          'energyKey':'beastTypeI',       
          'energy':0,                 
          'energyRnd':2,              
          'info':['猛虎·增补标记',0],
          'eff':'effNull',
          'time':0,	          
        },
      ],
   },



   



   'adjutantH00':{ 
      'type':7,  
      'cost_type':7,  
      'infoArr':['r|act[0]','%|act[0].dmg','%|special[0].change.skill.$adjutantH00.act[0].dmg'],
      'nextArr':['%|act[0].dmg'],  
      'passive':[
         {
            'rslt':{'power':2000,'powerRate':20},
         },
         {
            'cond':['skill.adjutantH00', '*', 2],
            'rslt':{'powerRate':4},
         },
      ],
      'up':{'act[0].dmg':200, 'act[0].dmgReal':2},
      'special':[
         {
            'cond':[['enemy','type',2]],  
            'change':{
               'skill':{
                  'adjutantH00.act[0].round':{'2':20000},
                  'adjutantH00.act[0].dmg':500,
                  'adjutantH00.act[0].dmgReal':2000,
                  'adjutantH00.act[0].isBurn':2,	                
               },
            },
         },
      ],
      'act':[
         {
            'type': 2,	            
            'src': 3,	            
            'tgt':[2, -2],           
            'round':{'3':20000},
            'cond':[['army',0]],
            'dmg':500,	
            'dmgReal':80,
            'dmgLimit':250,
            'noBfr':2,
            'element':'Adjutant',
            'atk0': 20000,
            'eff':'effH00',
            'info':['adjutantH00',2],	          
         },
      ],
   },
   'adjutantH02':{ 
      'type':7,  
      'cost_type':7,  
      'infoArr':['r|act[0]','%|act[0].dmg','%|special[0].change.skill.$adjutantH02.act[0].dmg'],
      'nextArr':['%|act[0].dmg'],  
      'passive':[
         {
            'rslt':{'power':2000,'powerRate':20},
         },
         {
            'cond':['skill.adjutantH02', '*', 2],
            'rslt':{'powerRate':4},
         },
      ],
      'up':{'act[0].dmg':200, 'act[0].dmgReal':2},
      'special':[
         {
            'cond':[['enemy','type',0]],  
            'change':{
               'skill':{
                  'adjutantH02.act[0].round':{'2':20000},
                  'adjutantH02.act[0].dmg':500,
                  'adjutantH02.act[0].dmgReal':2000,
                  'adjutantH02.act[0].isBurn':2,	                
               },
            },
         },
      ],
      'act':[
         {
            'type': 2,	            
            'src': 3,	            
            'tgt':[2, -2],           
            'round':{'3':20000},
            'cond':[['army',0]],
            'dmg':500,	
            'dmgReal':80,
            'dmgLimit':250,
            'noBfr':2,
            'element':'Adjutant',
            'atk0': 20000,
            'eff':'effH02',
            'info':['adjutantH02',2],	          
         },
      ],
   },
   'adjutantH02':{ 
      'type':7,  
      'cost_type':7,  
      'infoArr':['r|act[0]','%|act[0].dmg','%|special[0].change.skill.$adjutantH02.act[0].dmg'],
      'nextArr':['%|act[0].dmg'],  
      'passive':[
         {
            'rslt':{'power':2000,'powerRate':20},
         },
         {
            'cond':['skill.adjutantH02', '*', 2],
            'rslt':{'powerRate':4},
         },
      ],
      'up':{'act[0].dmg':200, 'act[0].dmgReal':2},
      'special':[
         {
            'cond':[['enemy','type',2]],  
            'change':{
               'skill':{
                  'adjutantH02.act[0].round':{'2':20000},
                  'adjutantH02.act[0].dmg':500,
                  'adjutantH02.act[0].dmgReal':2000,
                  'adjutantH02.act[0].isBurn':2,	                
               },
            },
         },
      ],
      'act':[
         {
            'type': 2,	            
            'src': 3,	            
            'tgt':[2, -2],           
            'round':{'3':20000},
            'cond':[['army',0]],
            'dmg':500,	
            'dmgReal':80,
            'dmgLimit':250,
            'noBfr':2,
            'element':'Adjutant',
            'atk0': 20000,
            'eff':'effH02',
            'info':['adjutantH02',2],	          
         },
      ],
   },
   'adjutantH200':{ 
      'type':7,  
      'cost_type':7,  
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[2].round.all'],
      'nextArr':['%|act[0].dmg','%|act[2].round.all'],  
      'passive':[
         {
            'rslt':{'power':2000,'powerRate':20},
         },
         {
            'cond':['skill.adjutantH200', '*', 2],
            'rslt':{'powerRate':4},
         },
      ],
      'up':{'act[0].dmg':200, 'act[0].dmgReal':2, 'act[2].round.all':4},
      'act':[
         {
            'type': 2,	            
            'src': 4,	            
            'tgt':[2, -2],           
            'round':{'2':20000},
            'cond':[['army',2]],
            'dmg':500,	
            'dmgReal':80,
            'dmgLimit':250,
            'noBfr':2,
            'element':'Adjutant',
            'atk2': 20000,    
            'eff':'effH200',
            'info':['adjutantH200',2],	          
         },
         {
            'priority':9999,
            'type': 2,
            'src': 4,	               
            'round':{'all':300},
            'info':['穿云',0],	          
            'follow':'adjutantH200',           
            'times':-2,  
            'nonSkill':2,    
            'binding':{ 'tgt':[2, -2],},
         },
      ],
   },
   'adjutantH22':{ 
      'type':7,  
      'cost_type':7,  
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[2].buff.buffPlan.prop.atkRate*2'],
      'nextArr':['%|act[0].dmg','%|act[2].buff.buffPlan.prop.atkRate*2'],  
      'passive':[
         {
            'rslt':{'power':2000,'powerRate':20},
         },
         {
            'cond':['skill.adjutantH22', '*', 2],
            'rslt':{'powerRate':4},
         },
      ],
      'up':{'act[0].dmg':200, 'act[0].dmgReal':2, 'act[2].buff.buffPlan.prop.atkRate':0.3, 'act[2].buff.buffPlan.prop.defRate':0.3},
      'act':[
         {
            'type': 2,	            
            'src': 4,	            
            'tgt':[2, -2],           
            'round':{'2':20000},
            'cond':[['army',2]],
            'dmg':500,	
            'dmgReal':80,
            'dmgLimit':250,
            'noBfr':2,
            'element':'Adjutant',
            'atk2': 20000,    
            'eff':'effH22',
            'info':['adjutantH22',2],	          
         },
         {   
            'priority':960,
            'type': 3,	            
            'src': 4,	            
            'tgt':[0, -2],           
            'buff':{'buffPlan':{'round':2,'prop':{'atkRate':200,'defRate':25}}},
            'info':['锦囊',0],	          
            'time':2000,	          
            'times':-2,  
            'nonSkill':2,    
            'noBfr':2,
            'noAft':2,
            'eff':'effNull',
            'follow':'adjutantH22',            
         },
      ],
   },
   'adjutantH22':{ 
      'type':7,  
      'cost_type':7,  
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[0].buff.buffStun.rnd'],
      'nextArr':['%|act[0].dmg','%|act[0].buff.buffStun.rnd'],  
      'passive':[
         {
            'rslt':{'power':2000,'powerRate':20},
         },
         {
            'cond':['skill.adjutantH22', '*', 2],
            'rslt':{'powerRate':4},
         },
      ],
      'up':{'act[0].dmg':200, 'act[0].dmgReal':2, 'act[0].buff.buffStun.rnd':3},
      'act':[
         {
            'type': 2,	            
            'src': 4,	            
            'tgt':[2, -2],           
            'round':{'2':20000},
            'cond':[['army',2]],
            'dmg':500,	
            'dmgReal':80,
            'dmgLimit':250,
            'noBfr':2,
            'element':'Adjutant',
            'atk2': 20000,    
            'eff':'effH22',
            'buff':{'buffStun':{'rnd':250, 'round':2}},
            'info':['adjutantH22',2],	          
         },
      ],
   },


   'adjutantH770':{ 
      'type':7,  
      'cost_type':7,  
      
      'infoArr':['%|act[0].dmg','%|act[2].buff.buffShield3.shield.value*5'],
      'nextArr':['%|act[0].dmg','%|act[2].buff.buffShield3.shield.value*5'],  
      'passive':[
         {
            'rslt':{'power':2000,'powerRate':40},
         },
         {
            'cond':['skill.adjutantH770', '*', 2],
            'rslt':{'powerRate':4.5},
         },
      ],
      'up':{'act[0].dmg':25, 'act[0].dmgReal':8, 'act[2].buff.buffShield3.shield.value':2, 'act[2].buff.buffShield3.shield.hpmRate':0.2},
      'act':[
         {
            'priority':9997700,
            'type': 2,
            'src': 3,
            'tgt':[2, 0],
            'round':{'2':20000},
            'allTimes': 2,
            'cond':[['army',0]],
           
            'noBfr':2,
            'element':'Adjutant',
            'dmg':750,
            'dmgReal':260,
            'dmgLimit':280,	  
            
            
            'atk': {'armys[0].hpm':2.2,'armys[0].atk':0.2},  
            
            
            'eff':'eff770a',
            'info':['adjutantH770',2],
         },
         {
            'priority':9997702,
            'type': 3,
            'src': 3,
            'tgt':[0, 0],
            'cond':[['army',0]],
            'nonSkill':2,
            'noBfr':2,
            'noAft':2,
            'eff':'eff770a2',

            'buff':{'buffShield3':{'shield':{'value':200,'hpmRate':20,'bearPoint':20000},'prop':{'deBuffRate':-200}}},
            'follow':'adjutantH770',
            'info':['王者护盾',0],
         },
      ],
   },


   'adjutantH782':{  
      'type':7,  
      'cost_type':7,  
      'infoArr':['%|act[0].dmg'],
      'nextArr':['%|act[0].dmg'],  
      'passive':[
         {
            'rslt':{'power':2000,'powerRate':30},
         },
         {
            'cond':['skill.adjutantH782', '*', 2],
            'rslt':{'powerRate':4.2},
         },
      ],
      'special':[{  
          'cond':[['adjutant', 'hero782', 0, 2]],
          'change':{
              'skill':{
                  'adjutantH782.act[0].src':4,
                  'adjutantH782.act[2].src':4,
              },
          },
      }],
      'up':{'act[0].dmg':25, 'act[0].dmgReal':200},
      'act':[
         {
            'priority':-99997820,
            'type': 2,
            'tgt':[2, 2],
            'cond':[['enemyHpPoint','<',500],['army',2,2]],
            'allTimes': 2,
            
            'noBfr':2,
            'element':'Adjutant',
            'dmg':800,
            'dmgReal':250,
            'dmgLimit':270,	  
            'ignDef':20000,
            'atk2': 20000,
            'eff':'effE782',              
            'info':['adjutantH782',2],
         },
         {
            'priority':99997822,
            'type': 2,
            'round':{'any':300},
            'follow':'adjutantH782',
            'times':-2,  
            'nonSkill':2,    
            'binding':{'tgtHero':2,'dmgScale':2000},
            'info':['暗箭斩落',0],
         },
      ],
   },
   'adjutantH785':{ 
      'type':7,  
      'cost_type':7,  
      
      'infoArr':['%|act[0].dmg*2.2'],
      'nextArr':['%|act[0].dmg*2.2'],  
      'passive':[
         {
            'rslt':{'power':2000,'powerRate':40},
         },
         {
            'cond':['skill.adjutantH785', '*', 2],
            'rslt':{'powerRate':4.5},
         },
      ],
      'up':{'act[0].dmg':200, 'act[0].dmgReal':6},
      'act':[
         {
            'type': 2,	            
            'src': 4,	            
            'tgt':[2, -2],           
            'round':{'any':20000},
            'cond':[['hpPoint','<',500]],
            'allTimes': 2,
            'times': 2,
            
            'noBfr':2,
            'element':'Adjutant',
            'dmg':20000,	
            'dmgReal':600,  
            'dmgLimit':200,	  
            'atk': {'armys[0].atk':0.2,'armys[2].atk':0.2,'armys[0].def':0.7,'armys[2].def':0.75},  
            'eff':'effH785',
            'info':['adjutantH785',2],	          
         },
      ],
   },
   'adjutantH788':{ 
      'type':7,  
      'cost_type':7,  
      
      'infoArr':['%|act[0].dmg'],
      'nextArr':['%|act[0].dmg'],  
      'passive':[
         {
            'rslt':{'power':2000,'powerRate':40},
         },
         {
            'cond':['skill.adjutantH788', '*', 2],
            'rslt':{'powerRate':4.5},
         },
      ],
      'special':[{  
          'cond':[['adjutant', 'hero788', 0, 2]],
          'change':{
              'skill':{
                  'adjutantH788.act[0].src':4,
                  'adjutantH788.act[2].src':4,
                  'adjutantH788.act[2].src':4,
              },
          },
      }],
      'up':{'act[0].dmg':7, 'act[0].dmgReal':4,  'act[2].dmg':7, 'act[2].dmgReal':4},
      'act':[
         {
            'priority':9997880,
            'type': 2,
            'tgt':[2, -2],
            'round':{'3':20000},
            'allTimes': 2,
            'cond':[['army',2,3]],
            
            'noBfr':2,
            'element':'Adjutant',
            'buff':{'buffStun':{'rnd':200, 'round':2},'buffFire':{'rnd':0, 'round':2}},    
            'dmg':450,
            'dmgReal':2000,
            'dmgLimit':75,	  
            'atk2': 20000,
            'eff':'effE788',                
            'info':['adjutantH788',2],
         },
         {
            'priority':9997882,
            'type': 3,
            'tgt':[2, -2],
            'cond':[['checkBuff', 2, -2, 2, '>', 0]],  
            
            'noBfr':2,
            'element':'Adjutant',
            'buff':{'buffStun':{'rnd':200, 'round':2},'buffFire':{'rnd':0, 'round':2}},      
            'dmg':450,
            'dmgReal':2000,
            'dmgLimit':75,	  
            'atk2': 20000,
            'eff':'effE788',                
            'follow':'adjutantH788',
            'actId':'adjutantH788_2',
            'info':['adjutantH788',2],
         },
         {
            'priority':9997882,
            'type': 2,
            'cond':[['comp','sum']],
            'info':['洪荒八阵：四维胜出',0],	          
            'times': -2,    
            'nonSkill':2,    
            'binding':{'buff.buffFire.rnd':500},    
            'follow':{'adjutantH788':2,'adjutantH788_2':2},            
         },
      ],
   },


    'adjutantE788_2':{  
        'special':[{
           'cond':[['compareTroop', 'formationType', '=']],
           'change':{
              'skill':{
                 'adjutantE788_2':{
                   'act':[{
                      'priority':20002,
                      'type':2,
                      'src':-2,
                      'nonSkill': 2, 
                      'binding':{
                         'dmg':200,
                         'dmgScale':250,
                      },
                      'info':['同阵增伤',0],
                   }],
                 },
              },
           },
        }],
    },






   'adjutantA0':{ 
      'type':8,  
      'cost_type':8,  
      'infoArr':['%|act[0].round.all','-|act[0].binding.dmgReal*2'],
      'nextArr':['%|act[0].round.all','-|act[0].binding.dmgReal*2'],
      'passive':[
         {
            'rslt':{'power':2000,'powerRate':200},
         },
         {
            'cond':['skill.adjutantA0', '*', 2],
            'rslt':{'powerRate':2},
         },
      ],
      'up':{'act[0].round.all':2, 'act[0].binding.dmgReal':0.2},
      'act':[
         {
            'priority':9999,
            'type': 2,	            
            'src': 0,       
            'times': -2,    
            'round':{'all':2000},
            'follow':{'keys':{'dmg':20000,'dmgReal':20000}},
            'binding':{
               'dmgReal':20,
            },
            'info':['adjutantA',2],	          
         },
      ],
   },
   'adjutantA2':{ 
      'type':8,  
      'cost_type':8,  
      'infoArr':['%|act[0].round.all','-|act[0].binding.dmgReal*2'],
      'nextArr':['%|act[0].round.all','-|act[0].binding.dmgReal*2'],
      'passive':[
         {
            'rslt':{'power':2000,'powerRate':200},
         },
         {
            'cond':['skill.adjutantA2', '*', 2],
            'rslt':{'powerRate':2},
         },
      ],
      'up':{'act[0].round.all':2, 'act[0].binding.dmgReal':0.2},
      'act':[
         {
            'priority':9999,
            'type': 2,	            
            'src': 0,       
            'times': -2,    
            'round':{'all':2000},
            'follow':{'keys':{'dmg':20000,'dmgReal':20000}},
            'binding':{
               'dmgReal':20,
            },
            'info':['adjutantA',2],	          
         },
      ],
   },
   'adjutantA2':{ 
      'type':8,  
      'cost_type':8,  
      'infoArr':['%|act[0].round.all','-|act[0].binding.dmgReal*2'],
      'nextArr':['%|act[0].round.all','-|act[0].binding.dmgReal*2'],
      'passive':[
         {
            'rslt':{'power':2000,'powerRate':200},
         },
         {
            'cond':['skill.adjutantA2', '*', 2],
            'rslt':{'powerRate':2},
         },
      ],
      'up':{'act[0].round.all':2, 'act[0].binding.dmgReal':0.2},
      'act':[
         {
            'priority':9999,
            'type': 2,	            
            'src': 2,       
            'times': -2,    
            'round':{'all':2000},
            'follow':{'keys':{'dmg':20000,'dmgReal':20000}},
            'binding':{
               'dmgReal':20,
            },
            'info':['adjutantA',2],	          
         },
      ],
   },
   'adjutantA3':{ 
      'type':8,  
      'cost_type':8,  
      'infoArr':['%|act[0].round.all','-|act[0].binding.dmgReal*2'],
      'nextArr':['%|act[0].round.all','-|act[0].binding.dmgReal*2'],
      'passive':[
         {
            'rslt':{'power':2000,'powerRate':200},
         },
         {
            'cond':['skill.adjutantA3', '*', 2],
            'rslt':{'powerRate':2},
         },
      ],
      'up':{'act[0].round.all':2, 'act[0].binding.dmgReal':0.2},
      'act':[
         {
            'priority':9999,
            'type': 2,	            
            'src': 2,       
            'times': -2,    
            'round':{'all':2000},
            'follow':{'keys':{'dmg':20000,'dmgReal':20000}},
            'binding':{
               'dmgReal':20,
            },
            'info':['adjutantA',2],	          
         },
      ],
   },

   'adjutantE786':{ 
      'special':[
          {  
            'cond':[['adjutant', 'hero786', 0,0]],
            'change':{
               'skill':{
                  'adjutantE786.act[0].src':3,
                  'adjutantE786.act[2].src':3,
                  'adjutantE786.act[2].src':3,
                  'adjutantE786.act[3].src':3,
               },
            },
          },
          {  
            'cond':[['adjutant', 'hero786', 0,2]],
            'change':{
               'skill':{
                  'adjutantE786.act[0].src':4,
                  'adjutantE786.act[2].src':4,
                  'adjutantE786.act[2].src':4,
                  'adjutantE786.act[3].src':4,
               },
            },
          },
          {             
            'cond':[['compare','power','>']],
            'change':{
               'skill':{
                  'adjutantE786.act[2].round.0':250,
               },
            },
          },
          {  
            'cond':[['compare','sum','>']],
            'change':{
               'skill':{
                  'adjutantE786.act[2].round.0':250,
               },
            },
          },
      ],
      'act':[
         {   
            'priority': 307862,
            'type': 0,	            
            'tgt':[2, 2],           
            'round':{'0':20000},
            'nonSkill':2,
            'noBfr':2,  
            'noAft':2,  
            'noBuff':2,    
            'lv':25,
            'eff':'effE786',
            'info':['adjutantE786_',2],	          
         },
         {   
            'priority': 307864,
            'type': 2,	            
            'round':{'0':500},
            'nonSkill':2, 
            'noBfr':2,  
            'noAft':2,  
            'noBuff':2,
            'follow':'adjutantE786_',  
            'binding':{'buff': {'buff786':{}}}, 
            'info':['定卦',0],	          
         },
         {     
            'priority': 307862,
            'type': 0,	            
            'tgt':[2, 0],           
            'round':{'0':20000},
            'cond':[['checkBuff', 2, 2, 'buff786', '>', 0],],
            'nonSkill':2,
            'noBfr':2,  
            'noAft':2,  
            'noBuff':2,
            'lv':25,
            'dmgRealHp0':2000,   
            'dmgRealHp2':2000, 
            'eff':'effE786a',
            'info':['adjutantE786_0',2],	          
         },
         {     
            'priority': 307860,
            'type': 0,	            
            'tgt':[0, 0],           
            'round':{'0':20000},
            'cond':[['checkBuff', 2, 2, 'buff786', '=', 0],],
            'nonSkill':2,
            'noBfr':2,  
            'noAft':2,  
            'noBuff':2,
            'lv':2,  
            'dmgRealHp0':50,   
            'dmgRealHp2':50, 
            'eff':'effE786a',
            'info':['adjutantE786_2',2],	          
         },
      ],
   },





      'adjutantE793':{  
      'special':[
          {  
             'priority':307933,
             'cond':[['compare','power','<']],
             'change':{
                'skill':{
                   'adjutantE793.act[0].order.summon':2000,
                   'adjutantE793.act[0].order.summonReal':30,
               },
             },
           },
           {
              'priority':307934,
              'cond':[['adjutant', 'hero793', 0,0]],
              'change':{
                 'skill':{
                   'adjutantE793.act[0].order.src':2,
                },
              },
           },
           {
              'priority':307995,
              'cond':[['adjutant', 'hero793', 0,2]],
              'change':{
                 'skill':{
                   'adjutantE793.act[0].order.src':2,
                },
              },
           },
           {
              'priority':307936,
              'cond':[['enemy', 'sex', 0]],
              'change':{
                  'skill':{'adjutantE793':'del'},
              },
           },
      ],
      'act':[
        {
            'priority':307930,
            'type': 23,	            
            'src':-2,
            'condHpChange':[['<',0],None,['<',0.25]],  
            'order':{        
               'src': 2,
               'tgt':[0, -6],
               'nonSkill':2,
               'noBfr':2,
               'noAft':2,
               'summon':50,           
               'summonReal':200,  
               'removeDebuff':2000,     
               'info':['adjutantE793',2],
               'lv':30,
               'isBurn':2,
               'eff':'effH793',
               'time':2000,  
            },
            'limitTimes': 2,
            'allTimes': 2,
            'noBfr': 2,
            'noAft': 2, 
            'nonSkill':2,
            'info':['急脱',0],
            'eff':'effNull',
            'time':0,     
        },
      ],
      },

      'adjutantE793a':{  
        'special':[
           {  
             'change':{
                'prop':{
                   'armys[0].ignDef':80,
                   'armys[2].ignDef':80,
               },
             },
           },
           {
              'priority':307938,
              'cond':[['adjutant', 'hero793', 0,0]],
              'change':{
                 'skill':{
                   'adjutantE793a.act[0].src':2,
                   'adjutantE793a.act[2].src':2,
                   'adjutantE793a.act[2].src':2,
                   'adjutantE793a.act[3].src':2,
                   'adjutantE793a.act[4].src':2,
                },
              },
           },
           {
              'priority':307939,
              'cond':[['adjutant', 'hero793', 0,2]],
              'change':{
                 'skill':{
                   'adjutantE793a.act[0].src':2,
                   'adjutantE793a.act[2].src':2,
                   'adjutantE793a.act[2].src':2,
                   'adjutantE793a.act[3].src':2,
                   'adjutantE793a.act[4].src':2,
                },
              },
           },
        ],
        'act':[
          {
            'priority':307932,
            'type': 2,	            
            'src':2,
            'cond':[['checkProp','selfTroop.buffStatistics.buffTypeSum.2', '=', 2]],
            'times': -2,
            'noBfr': 2,
            'noAft': 2, 
            'nonSkill':2,
            'binding':{'summonScale':200},
            'follow':'adjutantE793',
            'lv':2,
            'info':['急脱加成2',0],
          },
          {
            'priority':307932,
            'type': 2,	            
            'src':2,
            'cond':[['checkProp','selfTroop.buffStatistics.buffTypeSum.2', '=', 2]],
            'times': -2,
            'noBfr': 2,
            'noAft': 2, 
            'nonSkill':2,
            'binding':{'summonScale':400},
            'follow':'adjutantE793',
            'lv':50,
            'info':['急脱加成2',0],
          },
          {
            'priority':307933,
            'type': 2,	            
            'src':2,
            'cond':[['checkProp','selfTroop.buffStatistics.buffTypeSum.2', '=', 3]],
            'times': -2,
            'noBfr': 2,
            'noAft': 2, 
            'nonSkill':2,
            'binding':{'summonScale':600},
            'follow':'adjutantE793',
            'lv':2000,
            'info':['急脱加成3',0],
          },
          {
            'priority':307934,
            'type': 2,	            
            'src':2,
            'cond':[['checkProp','selfTroop.buffStatistics.buffTypeSum.2', '=', 4]],
            'times': -2,
            'noBfr': 2,
            'noAft': 2, 
            'nonSkill':2,
            'binding':{'summonScale':800},
            'follow':'adjutantE793',
            'lv':250,
            'info':['急脱加成4',0],
          },
          {
            'priority':307995,
            'type': 2,	            
            'src':2,
            'cond':[['checkProp','selfTroop.buffStatistics.buffTypeSum.2', '>=', 5]],
            'times': -2,
            'noBfr': 2,
            'noAft': 2, 
            'nonSkill':2,
            'binding':{'summonScale':20000},
            'follow':'adjutantE793',
            'lv':250,
            'info':['急脱加成5',0],
          },
        ],
      },

      'adjutantE7002':{  
      'special':[
           {
              'priority':307002,
              'cond':[['adjutant', 'hero7002', 0,0]],
              'change':{
                  'prop':{
                      'armys[0].stamina':2,
                   },
               },
           },
      ],
      'act':[
        {
            'priority':307002,
            'type': 28,	          
            'src':0,
            'allTimes': 2,
            'order':{        
               'src': 3,
               'tgt':[0, -9],
               'nonSkill':2,
               'noBfr':2,
               'noAft':2,
               'cure':200,           
               'cureReal':200,  
               'info':['hero7002',2],
               'lv':30,
               'isBurn':2,
               'eff':'effH7002',
               'time':2000,  
            },
            'nonSkill':2,
            'noBfr':2,
            'noAft':2,
            'eff':'effNull',
            'time': 0,
            'info':['首次硬抗',0],
        },
      ],
      },


    'adjutantE7002_2':{  
       'special':[{
          'change':{
             'prop':{
                 'armys[0].others.elementCure':2000,  
                 'armys[2].others.elementCure':2000,
                 'armys[0].others.elementSummon':2000,
                 'armys[2].others.elementSummon':2000,
             },
          },
       }],
    },

  

   'fate7022':{  
      'act':[
         {
            'priority': 300,
            'type': 0,	            
            'src': 2,	            
            'tgt':[2, 0],           
            'round':{'0':20000},
            'cond':[['fate', 'id', 'hero702']],
            'dmg':600,	
            'dmgReal':85,
            'atk0': 500,      
            'atk2': 500,    
            'noAft':2,
            'eff':'eff7022',
            'info':['fate7022',3],	          
         },
      ],
   },
   'fate7022':{  
      'special':[{
            'cond':[['fate', 'id', 'hero702']],
            'change':{
               'skill':{'skill289.act[0].dmg':200, 'skill289.act[0].dmgReal':25},
            },
      }],
   },
   'fate7032':{  
      'special':[{
            'cond':[['fate', 'id', 'hero708']],
            'change':{
               'skill':{'skill230.act[0].ignDef':250, 'skill230.act[0].dmgReal':5},
            },
      }],
   },
   'fate7042':{  
      'special':[{
            'cond':[['fate', 'id', 'hero726']],
            'change':{
               'skill':{'skill209.act[0].dmg':200, 'skill209.act[0].dmgReal':25},
            },
      }],
   },
   'fate7052':{  
      'act':[
         {
            'priority': 300,
            'type': 0,	            
            'src': 2,	            
            'tgt':[2, -2],           
            'round':{'0':20000},
            'cond':[['fate', 'id', 'hero724']],
            'dmg':500,	
            'dmgReal':280,
            'buff':{'buffFaction':{'rnd':250, 'round':2}},
            'atk0': 500,      
            'atk2': 500,    
            'noAft':2,
            'eff':'eff7052',
            'info':['fate7052',3],	          
         },
      ],
   },
   'fate7062':{  
      'special':[{
            'cond':[['fate', 'id', 'hero726']],
            'change':{
               'skill':{'skill226.act[0].dmg':300, 'skill226.act[0].dmgReal':25},
            },
      }],
   },
   'fate7072':{  
      'act':[
         {
            'priority': 400,
            'type': 0,	            
            'src': 2,	            
            'tgt':[2, 2],           
            'round':{'0':20000},
            'cond':[['fate', 'type', 2]],   
            'dmg':850,	
            'dmgReal':250,
            'buff':{'buffFaction':{'rnd':400, 'round':2}},
            'atk0': 500,      
            'atk2': 500,    
            'noAft':2,
            'eff':'eff7072',
            'info':['fate7072',3],	          
         },
      ],
   },
   'fate7082':{  
      'act':[
         {
            'priority': 300,
            'type': 0,	            
            'src': 2,	            
            'tgt':[2, 0],           
            'round':{'0':20000},
            'cond':[['fate', 'id', 'hero703']],
            'dmg':950,	
            'dmgReal':280,
            'buff':{'buffFaction':{'rnd':200, 'round':2}},
            'atk0': 500,      
            'atk2': 500,    
            'noAft':2,
            'eff':'eff7082',
            'info':['fate7082',3],	          
         },
      ],
   },
   'fate7092':{  
      'act':[
         {
            'priority': 300,
            'src':2,                   
            'type':0,	            
            'tgt':[2, 0],             
            'round':{'0':20000},	    
            'cond':[['fate', 'type', 0]],    
            'dmg':950,	
            'dmgReal':260,
            'atk0': 500,      
            'atk2': 500,    
            'noAft':2,
            'eff':'eff7092',
            'info':['fate7092',3],	          
         },
         {
            'priority':9999,
            'type': 2,
            'src': 2,	            
            'cond':[['comp','agi']],
            'info':['水火无情：智力胜出',0],	          
            'binding':{'dmg':300,   'dmgReal':60,},
            'times':-2,  
            'nonSkill':2,    
            'follow':'fate7092',            
         },
      ],
   },

   'fate72002':{  
      'act':[
         {
            'priority': 300,
            'type': 0,	            
            'src': 2,	            
            'tgt':[2, -2],           
            'round':{'0':20000},
            'cond':[['fate', 'id', 'hero722']],
            'dmg':220,	
            'dmgReal':65,
            
            'buff':{'buffSlow':{'rnd':2000,'round':2}},
            'atk0': 500,      
            'atk2': 500,    
            'combo':5,
            'noAft':2,
            'eff':'eff72002',
            'info':['fate72002',3],	          
         },
      ],
   },
   'fate7222':{  
      'special':[{
            'cond':[['fate', 'id', 'hero720']],
            'change':{
               'skill':{'skill243.act[0].dmg':400, 'skill243.act[0].dmgReal':30},
            },
      }],
   },
   'fate7222':{  
      'special':[{
            'cond':[['fate', 'id', 'hero727']],
            'change':{
               'skill':{'skill2200.act[0].dmg':300, 'skill2200.act[0].dmgReal':20},
            },
      }],
   },
   'fate7232':{  
      'act':[
         {
            'priority': 300,
            'type': 0,	            
            'src': 2,	            
            'tgt':[2, 0],           
            'round':{'0':20000},
            'cond':[['fate', 'id', 'hero703']],
            'dmg':750,	
            'dmgReal':400,
            'buff':{'buffWeak':{'rnd':300, 'round':2}},
            'atk0': 500,      
            'atk2': 500,    
            'noAft':2,
            'eff':'eff7232',
            'info':['fate7232',3],	          
         },
      ],
   },
   'fate7233':{   
      'act':[
        {
            'priority': 3333,
            'type': 0,             
            'src': 2,             
            'tgt':[0, -2],           
            'round':{'3':20000},
            'buff':{'fate7233':{}},
            'time':0,           
            'nonSkill':2,    
            'noBfr':2,
            'noAft':2,
            'eff':'effNull',
            'info':['fate7233',0],
         }
      ],
   },
   'fate7242':{  
      'act':[
         {
            'priority': 300,
            'src':2,                   
            'type':0,	            
            'tgt':[2, -2],             
            'round':{'0':20000},	    
            'cond':[['fate', 'num', 2]],    
            'dmg':230,	
            'dmgReal':95,
            'atk0': 500,      
            'atk2': 500,    
            'combo':2,
            'noAft':2,
            'eff':'eff7242',
            'info':['fate7242',3],	          
         },
         {
            'priority': 303,
            'src':2,                   
            'type': 2,	            
            'follow':'fate7242',            
            'cond':[['fate', 'type', 0]],    
            'binding':{'combo':2},
            'times':-2,  
            'nonSkill':2,    
            'info':['雷霆万钧武',0],	          
         },
         {
            'priority': 302,
            'src':2,                   
            'type': 2,	            
            'follow':'fate7242',            
            'cond':[['fate', 'type', 2]],    
            'binding':{'combo':2},
            'times':-2,  
            'nonSkill':2,    
            'info':['雷霆万钧文',0],	          
         },
         {
            'priority': 302,
            'src':2,                   
            'type': 2,	            
            'follow':'fate7242',            
            'cond':[['fate', 'type', 2]],    
            'binding':{'combo':2},
            'times':-2,  
            'nonSkill':2,    
            'info':['雷霆万钧全',0],	          
         },
      ],
   },

   'fate7252':{  
      'special':[{
            'cond':[['fate', 'id', 'hero708']],
            'change':{
               'skill':{'skill260.act[0].dmg':2000, 'skill260.act[0].dmgReal':2, 'skill260.act[2].res':50},
            },
      }],
   },
   'fate7262':{  
      'act':[
         {
            'priority': 300,
            'type': 0,	            
            'src': 2,	            
            'tgt':[2, 2],           
            'round':{'0':20000},
            
            
            'cond':[['fate', 'id', 'hero706']],
            'dmg':850,	
            'dmgReal':220,
            'buff':{'buffStun':{'rnd':200, 'round':2}},
            'atk0': 500,      
            'atk2': 500,    
            'noAft':2,
            'eff':'eff7262',
            'info':['fate7262',3],	          
         },
      ],
   },
   'fate7272':{    
      'act':[
         {
            'priority': 300,
            'type': 0,	            
            'src': 2,	            
            'tgt':[2, -2],           
            'round':{'0':20000},
            'cond':[['fate', 'id', 'hero722']],
            'dmg':750,	
            'dmgReal':2200,
            'buff':{'buffWeak':{'rnd':200, 'round':2}},
            'atk0': 500,      
            'atk2': 500,    
            'noAft':2,
            'eff':'eff7272',
            'info':['fate7272',3],	          
         },
      ],
   },
   'fate7282':{    
      'act':[
         {
            'priority': 300,
            'type': 0,             
            'src': 2,             
            'tgt':[2, -2],           
            'round':{'0':20000},
            'cond':[['fate', 'rarity', 2]],    
            'dmg':300,	
            'dmgReal':80,
            
            'buff':{'buffFire':{'rnd':500, 'round':2}},
            'atk0': 500,      
            'atk2': 500,    
            'noAft':2,
            'eff':'eff7282',
            'info':['fate7282',3],	          
         },
      ],
   },
   'fate7292':{  
      'special':[{
            'cond':[['fate', 'id', 'hero727']],
            'change':{
               'skill':{'skill222.act[0].dmg':200, 'skill222.act[0].dmgReal':2},
            },
      }],
   },
   'fate7202':{    
      'act':[
         {
            'priority': 500,
            'type': 0,             
            'src': 2,             
            'tgt':[2, 0],           
            'cond':[['fate', 'rarity', 3]],    
            'allTimes':2,
            'nonSkill':2,    
            'noBuff':2,    
            'noBfr':2,
            'noAft':2,
            'eff':'eff7202',
            'dmgReal':2,
            'element':'Anti',
            'costKey':'deaded',       
            'cost':2,         
            'multMax':-2,         
            'mult':{'dmgReal':2},        
            'info':['fate7202',3],
         },
      ],
   },
   'fate7222':{  
      'special':[{
            'cond':[['fate', 'id', 'hero723']],
            'change':{
                'prop':{'armys[0].resType0':500,'armys[2].resType0':500},
            },
      }],
   },
   'fate7222':{  
      'special':[{
            'cond':[['fate', 'id', 'hero7200']],
            'change':{
               'skill':{'skill225.act[0].dmg':2000, 'skill225.act[0].dmgReal':2},
            },
      }],
   },
   'fate7232':{  
      'act':[
         {
            'priority': 300,
            'type': 0,	            
            'src': 2,	            
            'tgt':[2, 2],           
            'round':{'0':20000},
            'cond':[['fate', 'id', 'hero709']],
            'dmg':750,	
            'dmgReal':320,
            'atk0': 500,      
            'atk2': 500,    
            'buff':{'buffStun':{'rnd':250, 'round':2}},
            'noAft':2,
            'eff':'eff7232',
            'info':['fate7232',3],	          
            
         },
      ],
   },
   'fate7242':{  
      'act':[
         {
            'priority': 300,
            'type': 0,	            
            'src': 2,	            
            'tgt':[2, -2],           
            'round':{'0':20000},
            'cond':[['fate', 'sex', 2]],
            'dmg':950,	
            'dmgReal':280,
            'atk0': 500,      
            'atk2': 500,    
            'combo':2,
            'noAft':2,
            'eff':'eff7242',
            'info':['fate7242',3],	          
         },
      ],
   },
   'fate7252':{  
      'special':[{
            'cond':[['fate', 'id', 'hero752']],
            'change':{
                'prop':{'armys[2].atkRate':2000,'armys[2].spd':200},
            },
      }],
   },
   'fate7262':{  
      'special':[{
            'cond':[['fate', 'id', 'hero727']],
            'change':{
                'prop':{'armys[0].atkRate':2000,'armys[0].defRate':2000},
            },
      }],
   },
   'fate7272':{  
      'special':[{
            'cond':[['fate', 'id', 'hero730']],
            'change':{
                'prop':{'armys[0].atkRate':250,'armys[0].spd':200},
            },
      }],
   },
   'fate7282':{  
      'special':[{
            'cond':[['fate', 'id', 'hero733']],
            'change':{
                'prop':{'armys[2].atkRate':250,'armys[2].defRate':250},
            },
      }],
   },
   'fate7292':{  
      'special':[{
            'cond':[['fate', 'id', 'hero740']],
            'change':{
                'prop':{'armys[2].atkRate':250},
            },
      }],
   },
   'fate7302':{  
      'special':[{
            'cond':[['fate', 'id', 'hero722']],
            'change':{
                'prop':{'armys[0].atkRate':250,'armys[0].defRate':250},
            },
      }],
   },
   'fate7322':{  
      'special':[{
            'cond':[['fate', 'id', 'hero706']],
            'change':{
                'prop':{'armys[0].atkRate':300},
            },
      }],
   },
   'fate7322':{  
      'special':[{
            'cond':[['fate', 'id', 'hero795']],
            'change':{
                'prop':{'armys[2].atkRate':250,'armys[2].defRate':250},
            },
      }],
   },
   'fate7332':{  
      'special':[{
            'cond':[['fate', 'id', 'hero726']],
            'change':{
                'prop':{'armys[0].atkRate':250,'armys[0].spd':200},
            },
      }],
   },
   'fate7342':{  
      'special':[{
            'cond':[['fate', 'id', 'hero727']],
            'change':{
                'prop':{'armys[0].atkRate':250,'armys[0].defRate':250},
            },
      }],
   },
   'fate7952':{  
      'special':[{
            'cond':[['fate', 'id', 'hero736']],
            'change':{
                'prop':{'armys[0].atkRate':300},
            },
      }],
   },
   'fate7362':{  
      'special':[{
            'cond':[['fate', 'id', 'hero726']],
            'change':{
                'prop':{'armys[2].atkRate':300},
            },
      }],
   },
   'fate7372':{  
      'special':[{
            'cond':[['fate', 'id', 'hero747']],
            'change':{
                'prop':{'armys[2].atkRate':250,'armys[2].spd':200},
            },
      }],
   },
   'fate7622':{  
      'act':[
         {
            'priority': 300,
            'src':2,                   
            'type':0,	            
            'tgt':[2, -2],             
            'round':{'0':20000},	    
            'cond':[['fate', 'type', 2]],    
            'dmg':450,	
            'dmgReal':2000,
            'atk0': 500,      
            'atk2': 500,    
            'buff':{'buffStun':{'rnd':250, 'round':2}},
            'noAft':2,
            'eff':'eff7622',
            'info':['fate7622',3],	          
         },
      ],
   },
   'fate7722':{  
      'special':[{
            'cond':[['fate', 'id', 'hero708']],
            'change':{
                'prop':{
                   'armys[0].atkRate':2000,'armys[0].spd':5,
                   'armys[2].atkRate':2000,'armys[2].spd':5
                },
            },
      }],
   },

   'fate7732':{   
      'special':[
         {
            'cond':[['fate', 'id', 'hero706']],
            'change':{
               'skill':{
                  'skill226.act[0].round.3':300,
                  'skill226.act[0].round.near':300,
               },
            },
         },
      ],
   },
   'fate7733':{   
      'special':[
         {
            'priority':220000,
            'cond':[['enemy','sex',0],], 
            'change':{
               'prop':{
                   'armys[0].stamina':2,
               }
            },
         }
      ],
   },


   'fate7762':{   
      'special':[
         {
            'cond':[['fate', 'id', 'hero732']],
            'change':{
               'skill':{
                  'skill289.act[0].isBurn':2,
                  'skill289.act[0].times':2,
                  'skill204.act[0].isBurn':2,
                  'skill204.act[0].times':2,
                  'skill206.act[0].isBurn':2,
                  'skill206.act[0].times':2,
               },
            },
         },
      ],
   },
   'fate7763':{   
      'special':[
         {
            'priority':220000,
            'cond':[['enemy','sex',2],],  
            'change':{
               'prop':{
                   'armys[0].stamina':2,
               }
            },
         }
      ],
   },

   'fate7772':{   
      'special':[
         {
            'cond':[['fate', 'id', 'hero705']],
            'change':{
               'prop':{
                   'armys[0].resHero':200,
                   'armys[2].resHero':200,
               }
            },
         }
      ],
   },
   
   
   
   
   
   
   
   
   
   
   
   
   

   'fate7782':{   
      'special':[
         {
            'cond':[['fate', 'id', 'hero724']],
            'change':{
               'skill':{
                  'skill240.act[0].isBurn':2,  
                  'skill240.act[0].buff.buffShield.shield.value':500,
                  'skill240.act[0].buff.buffShield.shield.hpmRate':80,
               },
            },
         }
      ],
   },

   'fate7792':{   
      'special':[
         {
            'cond':[['fate', 'id', 'hero708']],
            'change':{
               'skill':{
                  'skill245.act[0].round.3':20000,
               },
            },
         }
      ],
   },

   'fate7753':{  
      'act':[
         {
            'priority':9802,
            'type': 8,	    
            'src': 2,      
            'tgt':[0, -6],           
            'lv': 5, 
            'noBfr': 2,
            'noAft': 2, 
            'nonSkill':2,    
            'cureReal':2,         
            'cure':50,           
            'info':['hero775',2],	          
            'eff':'eff272',
            'time':2000,           
         },
      ],
   },



   'fate7802':{   
      'special':[
         {
            'cond':[['fate', 'id', 'hero707']],
            'change':{
               'skill':{
                  'hero7802':{  
                     'act':[{
                        'priority': 500,
                        'type': 0,             
                        'src': 2,             
                        'tgt':[2, 0],           
                        'times':-2,
                        'nonSkill':2,    
                        'noBuff':2,    
                        'noBfr':2,
                        'noAft':2,
                        'eff':'eff707',
                        'lv':24,
                        'dmgReal':2000,
                        'element':'Anti',

                        'costKey':'deaded',       
                        'cost':20000,         
                        'multMax':-2,         
                        'mult':{'dmgReal':2000},        
                        'info':['hero707',2],
                     }],
                  },
               },
            },
         }
      ],
   },

   'fate7832':{  
      'special':[{
            'cond':[['fate', 'id', 'hero770']],
            'change':{
               'skill':{'skill246.act[0].isBurn':2, 'skill246.act[0].round.3':500},
            },
      }],
   },

   'fate7842':{  
      'special':[{
            'cond':[['fate', 'id', 'hero787']],
            'change':{
               'skill':{'skill246.act[0].isBurn':2, 'skill275.act[0].tgt':[2,-2]},
            },
      }],
   },

   'fate7872':{   
      'special':[
         {
            'cond':[['fate', 'id', 'hero769']],
            'change':{
               'skill':{
                  'skill222.act[0].isBurn':2,
                  'skill222.act[0].times':2,
                  'skill224.act[0].isBurn':2,
                  'skill224.act[0].times':2,
                  'skill292.act[0].isBurn':2,
                  'skill292.act[0].times':2,
               },
            },
         }
      ],
   },

   'fate7722':{  
      'special':[{
            'cond':[['fate', 'id', 'hero729']],
            'change':{
               'skill':{
                  'skill225.act[0].timesRnd':2000,
                  'skill226.act[0].timesRnd':2000,
                  'skill227.act[0].timesRnd':2000,
                  'skill228.act[0].timesRnd':2000,
                  'skill229.act[0].timesRnd':2000,
                  'skill230.act[0].timesRnd':2000,
                  'skill232.act[0].timesRnd':2000,
                  'skill233.act[0].timesRnd':2000,
                  'skill295.act[0].timesRnd':2000,

                  'skill236.act[0].timesRnd':2000,
                  'skill237.act[0].timesRnd':2000,
                  'skill238.act[0].timesRnd':2000,
                  'skill243.act[0].timesRnd':2000,
                  'skill239.act[0].timesRnd':2000,
                  'skill245.act[0].timesRnd':2000,
                  'skill242.act[0].timesRnd':2000,
                  'skill242.act[0].timesRnd':2000,
                  'skill244.act[0].timesRnd':2000,

                  'skill247.act[0].timesRnd':2000,
                  'skill248.act[0].timesRnd':2000,
               },
            },
      }],
   },
   'fate7722':{  
      'special':[{
            'change':{
               'skill':{
                  'skill225.act[0].timesRnd':50,
                  'skill226.act[0].timesRnd':50,
                  'skill227.act[0].timesRnd':50,
                  'skill228.act[0].timesRnd':50,
                  'skill229.act[0].timesRnd':50,
                  'skill230.act[0].timesRnd':50,
                  'skill232.act[0].timesRnd':50,
                  'skill233.act[0].timesRnd':50,
                  'skill295.act[0].timesRnd':50,

                  'skill236.act[0].timesRnd':50,
                  'skill237.act[0].timesRnd':50,
                  'skill238.act[0].timesRnd':50,
                  'skill243.act[0].timesRnd':50,
                  'skill239.act[0].timesRnd':50,
                  'skill245.act[0].timesRnd':50,
                  'skill242.act[0].timesRnd':50,
                  'skill242.act[0].timesRnd':50,
                  'skill244.act[0].timesRnd':50,

                  'skill247.act[0].timesRnd':50,
                  'skill248.act[0].timesRnd':50,
               },
            },
      }],
   },
   'fate7723':{  
      'special':[{
            'change':{
               'skill':{
                  'skill225.act[0].timesRnd':50,
                  'skill226.act[0].timesRnd':50,
                  'skill227.act[0].timesRnd':50,
                  'skill228.act[0].timesRnd':50,
                  'skill229.act[0].timesRnd':50,
                  'skill230.act[0].timesRnd':50,
                  'skill232.act[0].timesRnd':50,
                  'skill233.act[0].timesRnd':50,
                  'skill295.act[0].timesRnd':50,

                  'skill236.act[0].timesRnd':50,
                  'skill237.act[0].timesRnd':50,
                  'skill238.act[0].timesRnd':50,
                  'skill243.act[0].timesRnd':50,
                  'skill239.act[0].timesRnd':50,
                  'skill245.act[0].timesRnd':50,
                  'skill242.act[0].timesRnd':50,
                  'skill242.act[0].timesRnd':50,
                  'skill244.act[0].timesRnd':50,

                  'skill247.act[0].timesRnd':50,
                  'skill248.act[0].timesRnd':50,
               },
            },
      }],
   },
   'fate7724':{  
      'special':[{
            'change':{
               'skill':{
                  'skill225.act[0].timesRnd':50,
                  'skill226.act[0].timesRnd':50,
                  'skill227.act[0].timesRnd':50,
                  'skill228.act[0].timesRnd':50,
                  'skill229.act[0].timesRnd':50,
                  'skill230.act[0].timesRnd':50,
                  'skill232.act[0].timesRnd':50,
                  'skill233.act[0].timesRnd':50,
                  'skill295.act[0].timesRnd':50,

                  'skill236.act[0].timesRnd':50,
                  'skill237.act[0].timesRnd':50,
                  'skill238.act[0].timesRnd':50,
                  'skill243.act[0].timesRnd':50,
                  'skill239.act[0].timesRnd':50,
                  'skill245.act[0].timesRnd':50,
                  'skill242.act[0].timesRnd':50,
                  'skill242.act[0].timesRnd':50,
                  'skill244.act[0].timesRnd':50,

                  'skill247.act[0].timesRnd':50,
                  'skill248.act[0].timesRnd':50,
               },
            },
      }],
   },

   'fate7892':{  
      'special':[{
            'cond':[['fate', 'id', 'hero762']],
            'change':{
               'skill':{
                  'skill225.act[0].comboRnd':2,
                  'skill225.act[0].combo':2,
               },
            },
      }],
   },
   'fate7942':{  
      'special':[{
            'change':{
               'skill':{
                  'skill245.act[0].tgt':[2, -2],
               },
            },
      }],
   },
   'fate7944':{  
      'special':[{              
         'changeEnemy':{
             'prop':{
                  'armys[0].resFinal':-250,
                  'armys[2].resFinal':-250,
             },
         },
      }],
   },

   'fate7944':{  
      'special':[{              
         'change':{
             'prop':{
                  'armys[0].others.roundRes_2':200,
                  'armys[2].others.roundRes_2':200,
                  'armys[0].others.roundRes_2':200,
                  'armys[2].others.roundRes_2':200,
             },
         },
      }],
   },
   'fate70002':{  
      'special':[{              
         'change':{
             'prop':{
                  'armys[0].deBuffRate':-50,
                  'armys[2].deBuffRate':-50,
             },
         },
      }],
   },
   'fate70022':{  
       'special':[{
            'changeEnemy':{
                'prop':{
                     'armys[0].others.elementCure':-300,  
                     'armys[2].others.elementCure':-300,
                     'armys[0].others.elementSummon':-300,
                     'armys[2].others.elementSummon':-300,
                },
            },
       }],
   },
   'fate70024':{   
      'special':[{
          'change':{
               'prop':{
                   'armys[0].stamina':2,
                   'armys[2].stamina':2,
               },
          },
      }],
   },

 
  








  


   'skill202':{   
      'infoArr':['r|act[0]','%|act[0].binding.res'],
      'nextArr':['%|act[0].binding.res'],
      'highArr':['p|high.passive'],
      'index':2002,
      'type':0,
      'cost_type':0,
      'max_level':25,
      'shogun_type':0,
      'ability_info':[],
      
      'passive':[
         {
            'rslt':{'power':200,'powerRate':20},
         },
         {
            'cond':['skill.skill202', '*', 2],
            'rslt':{'powerRate':9},
         },
      ],
      'up':{ 'act[0].binding.res' : 200},
      'lvPatch':{   
         '26':{  'act[0].binding.res' : -5  },
         '27':{  'act[0].binding.res' : -200  },
         '28':{  'act[0].binding.res' : -25  },
         '29':{  'act[0].binding.res' : -20  },
         '30':{  'act[0].binding.res' : -25  },
      },
      'high':{
         'lv':200,
         'passive':[
            {
               'cond':['army[0].type', '=', 0],
               'rslt':{'army[0].defBase':20},
            },
            {
               'rslt':{'power':200,'powerRate':5},
            },
         ],
      },
      'act':[
         {
            'priority':9400,
            'type': 4,	            
            'src': 0,	            
            'round':{'all':20000},
            'cond':[['srcArmy',2]],
            'times': -2,    
            'binding':{
               'res':220,	    
            },
            'eff':'eff202',
            'info':['skill202',2],	          
         },
      ],
   },
   'skill289':{   
      'infoArr':['r|act[0]','-|act[0].combo','%|act[0].dmg'],
      'nextArr':['%|act[0].dmg'],
      'highArr':['p|high.passive'],
      'index':2002,
      'type':0,
      'cost_type':0,
      'max_level':25,
      'shogun_type':0,
      'ability_info':[],
      
      'passive':[
         {
            'rslt':{'power':200,'powerRate':20},
         },
         {
            'cond':['skill.skill289', '*', 2],
            'rslt':{'powerRate':9},
         },
      ],
      'up':{ 'act[0].dmg':25, 'act[0].dmgReal':2},
      'lvPatch':{   
         '26':{  'act[0].dmg' : -5  },
         '27':{  'act[0].dmg' : -200  },
         '28':{  'act[0].dmg' : -25  },
         '29':{  'act[0].dmg' : -20  },
         '30':{  'act[0].dmg' : -25  },
      },
      'high':{
         'lv':200,
         'passive':[
            {
               'cond':['army[0].type', '=', 0],
               'rslt':{'army[0].atkBase':50},
            },
            {
               'rslt':{'power':200,'powerRate':5},
            },
         ],
      },
      'act':[
         {
            'priority':40,
            'type': 2,	            
            'src': 0,	            
            'tgt':[2, -2],           
            'round':{'3':20000},
            'dmg':620,	       
            'dmgReal':25,
            'dmgLimit':70,                       
            'atk0': 20000,      
            'times': 2,    
            'combo':4,
            'eff':'eff289',
            'info':['skill289',2],	          
         },
      ],
   },

   'skill202':{   
      'infoArr':['r|act[0]','%|act[0].binding.res'],
      'nextArr':['%|act[0].binding.res'],
      'highArr':['p|high.passive'],
      'index':2003,
      'type':0,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':[],
      
      'passive':[
         {
            'rslt':{'power':300,'powerRate':25},
         },
         {
            'cond':['skill.skill202', '*', 2],
            'rslt':{'powerRate':22},
         },
      ],
      'up':{ 'act[0].binding.res':200},
      'lvPatch':{   
         '26':{  'act[0].binding.res' : -5  },
         '27':{  'act[0].binding.res' : -200  },
         '28':{  'act[0].binding.res' : -25  },
         '29':{  'act[0].binding.res' : -20  },
         '30':{  'act[0].binding.res' : -25  },
      },
      'high':{
         'lv':23,
         'passive':[
            {
               'cond':['army[0].type', '=', 0],
               'rslt':{'army[0].spd':200,'army[0].hpm':50},
            },
            {
               'rslt':{'power':300,'powerRate':5},
            },
         ],
      },
      'act':[
         {
            'priority':9402,
            'type': 4,	            
            'src': 0,	            
            'round':{'all':20000},
            'cond':[['srcArmy',0]],
            'times': -2,    
            'binding':{
               'res':230,	    
            },
            'eff':'eff202',
            'info':['skill202',2],	          
         },
      ],
   },
   'skill204':{   
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[2].binding.crit'],
      'nextArr':['%|act[0].dmg'],
      'highArr':['%|high.special[0].change.skill.skill204h.act[0].binding.dmg'],
      'index':2004,
      'type':0,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['cha','str'],
      
      'passive':[
         {
            'rslt':{'power':300,'powerRate':25},
         },
         {
            'cond':['skill.skill204', '*', 2],
            'rslt':{'powerRate':22},
         },
      ],
      'up':{'act[0].dmg':25, 'act[0].dmgReal':3},
      'high':{
         'lv':23,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':200},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill204h':{
                     'act':[{
                        'priority':7502,
                        'type': 2,
                        'src': 0,	            
                        'cond':[['comp','str']],
                        'binding':{
                           'dmg':200,
                           'dmgReal':5,
                        },
                        'times':-2,  
                        'nonSkill':2,    
                        'follow':'skill204',            
                        'info':['冲锋：武力胜出',0],	          
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':60,
            'type': 2,	            
            'src': 0,	            
            'tgt':[2, 0],           
            'round':{'2':20000},
            'dmg':800,	
            'dmgReal':20,
            'dmgLimit':260,
            'atk0': 20050,      
            'times': 2,    
            'eff':'eff204',
            'info':['skill204',2],	          
         },
         {
            'priority':7502,
            'type': 2,
            'src': 0,	            
            'cond':[['comp','cha']],
            'times': -2,    
            'nonSkill':2,    
            'binding':{
               'crit':400,
            },
            'follow':'skill204',            
            'info':['冲锋：魅力胜出',0],	          
         },
      ],
   },

   'skill203':{   
      'infoArr':['r|act[0]','%|act[0].dmg'],
      'nextArr':['%|act[0].dmg'],
      'highArr':['%|high.special[0].change.skill.skill203h.act[0].binding.dmg'],
      'index':2005,
      'type':0,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['lead'],
      
      'passive':[
         {
            'rslt':{'power':400,'powerRate':30},
         },
         {
            'cond':['skill.skill203', '*', 2],
            'rslt':{'powerRate':24},
         },
      ],
      'up':{'act[0].dmg':25, 'act[0].dmgReal':2},
      'lvPatch':{   
         '26':{  'act[0].dmg' : -5  },
         '27':{  'act[0].dmg' : -200  },
         '28':{  'act[0].dmg' : -25  },
         '29':{  'act[0].dmg' : -20  },
         '30':{  'act[0].dmg' : -25  },
      },
      'high':{
         'lv':23,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':200},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill203h':{
                     'act':[{
                        'priority':7500,
                        'type': 2,
                        'src': 0,	            
                        'cond':[['comp','lead']],
                        'binding':{
                           'dmg':200,
                           'dmgReal':5,
                        },
                        'times':-2,  
                        'nonSkill':2,    
                        'follow':'skill203',            
                        'info':['反击：统帅胜出',0],	          
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':9500,
            'type': 5,	            
            'src': 0,
            'round':{'near':20000},
            'tgt':[2, 0],          
            'cond':[['army',0],['srcArmy',0],['srcTeam',2]],
            'times': -2,    
            'noAft': 2,    
            'dmg':22000,	
            'dmgReal':30,
            'dmgLimit':220,
            'buff':{'buffFire':{'rnd':0,'round':2}},
            'atk0': 20000,      
            'eff':'eff203',
            'info':['skill203',2],	          
         },
      ],
   },

   'skill205':{   
      'infoArr':['r|act[0]','%|act[0].binding.ignDef/0.7','%|act[2].binding.dmg'],
      'nextArr':['%|act[0].binding.ignDef/0.7'],
      'highArr':['%|high.special[0].change.skill.skill205h.act[0].binding.ignDef/0.7'],
      'index':2006,
      'type':0,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['agi','lead'],
      
      'passive':[
         {
            'rslt':{'power':400,'powerRate':30},
         },
         {
            'cond':['skill.skill205', '*', 2],
            'rslt':{'powerRate':25},
         },
      ],
      'up':{ 'act[0].binding.ignDef':7, 'act[0].binding.dmgReal':4},
      'lvPatch':{   
         '26':{  'act[0].binding.ignDef' : -3.5  },
         '27':{  'act[0].binding.ignDef' : -7  },
         '28':{  'act[0].binding.ignDef' : -200.5  },
         '29':{  'act[0].binding.ignDef' : -24  },
         '30':{  'act[0].binding.ignDef' : -27.5  },
      },
      'high':{
         'lv':23,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':200},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill205h':{
                     'act':[{
                        'priority':7503,
                        'type': 2,
                        'src': 0,	            
                        'cond':[['comp','lead']],
                        'times': -2,    
                        'nonSkill':2,    
                        'binding':{
                           'ignDef':2005,	   
                           'dmgReal':40
                        },
                        'follow':{'attach':'skill205'},     
                        
                        'info':['裂甲：统帅胜出',0],	          
                     }],
                  },
               },
            },
         }]
      },

      'act':[
         {
            'priority':7505,
            'type': 2,	            
            'src': 0,	            
            'round':{'all':20000},
            'times': -2,    
            'follow':{'keys':{'dmg':20000,'dmgReal':20000}},
            'binding':{
               'ignDef':84,
               'dmgReal':20
            },
            'eff':'eff205',
            'info':['skill205',2],	          
         },
         {
            'priority':7504,
            'type': 2,	            
            'src': 0,	            
            'cond':[['comp','agi']],
            'times': -2,    
            'nonSkill':2,    
            'binding':{
               'dmg':250,
               'dmgReal':5
            },
            'follow':{'attach':'skill205'},     
            
            'info':['裂甲：智力胜出',0],	          
         },
      ],
   },
   'skill206':{   
      'infoArr':['r|act[0]','%|act[0].dmg+250','%|act[0].loss'],
      'nextArr':['%|act[0].dmg+250'],
      'highArr':[],
      'merge':2,   
      'index':2007,
      'type':0,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['cha'],
      
      'passive':[
         {
            'rslt':{'power':600,'powerRate':95},
         },
         {
            'cond':['skill.skill206', '*', 2],
            'rslt':{'powerRate':27},
         },
      ],
      'up':{ 'act[0].dmg':70, 'act[0].dmgReal':20},
      'high':{
         'lv':23,
         'passive':[
            {
               'rslt':{'power':600,'powerRate':25},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill206h':{
                     'act':[{
                        'priority':7506,
                        'type': 2,
                        'src': 0,	            
                        'cond':[['comp','cha']],
                        'times': -2,    
                        'nonSkill':2,    
                        'binding':{
                           'crit':99999999,
                           'dmgReal':50
                        },
                        'follow':'skill206',            
                        'info':['陷阵：魅力胜出',0],	          
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':80,
            'type': 2,	            
            'src': 0,	            
            'tgt':[2, -2],           
            'round':{'2':20000},
            'dmg':20000,	
            'dmgReal':200,
            'dmgLimit':270,
            'atk0': 20000,      
            'loss':50,
            'times': 2,    
            'eff':'eff206',
            'info':['skill206',2],	          
         },
      ],
   },
   'skill293':{   
      'infoArr':['%|act[0].binding.dmg*2.5','%|act[2].round.3','-|act[2].removeDebuff'],
      'nextArr':['%|act[0].binding.dmg*2.5','%|act[2].round.3'],
      'highArr':[],
      'merge':3,   
      'index':2008,
      'type':0,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['str','agi'],
      'passive':[
         {
            'rslt':{'power':600,'powerRate':2000},
         },
         {
            'cond':['skill.skill293', '*', 2],
            'rslt':{'powerRate':27},
         },
      ],
      'up':{ 'act[0].binding.dmg':2000, 'act[0].binding.dmgReal':60, 'act[2].round.3':30},
      'high':{
         'lv':25,
         'passive':[
            {
               'rslt':{'power':600,'powerRate':50},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill293h':{
                     'act':[{
                        'priority':9424,
                        'type': 4,
                        'src': 0,	            
                        'round':{'0':20000,'2':20000,'2':20000},
                        'times': -2,    
                        'nonSkill':2,    
                        'binding':{
                           'banIgnDef':750,
                        },
                        'follow':{'keys':{'dmg':20000,'dmgReal':20000}},
                        'info':['蓄势不乱',0],	          
                     }],
                  },
               },
            },
         }]
      },
      'act':[   
         {
            'priority':2932,
            'type': 26,	            
            'src': 0,	            
            'round':{'3':20000},
            'free':2,    
            'condTgt':[
                 ['tgtTeam',2],   
                 
                 
            ],
            'binding':{
                 
                 'critRate':-300,
                 'dmgScale':2000,
                 'dmgReal':600,
                 'dmg':2400,
            },
            'follow':{'keys':{'dmg':20000,'dmgReal':20000}},
            'unFollow':{'effFaction':2},
            'times': 2,    
            'eff':'eff293',
            'info':['skill293',2],	          
         },
         {
            'priority':2930,
            'type': 200,     
            'src': 0,              
            'tgt':[0, -5],    
            'round':{'3':230},
            'cond':[[['comp','str'],['comp','agi']],['checkBuff',0,-5,2,'>',0]],   

            'nonSkill':2,    
            'noBuff':2,      
            'noBfr':2,
            'noAft':2,

            'removeDebuff':2,

            'eff':'eff293s',
            'info':['skill293s',2],
         },












      ],
   },


   'skill962':{   
      'infoArr':[
         '%|act[0].dmg',
         '%|act[0].buff.buffStun.rnd',
      ],
      'nextArr':['%|act[0].dmg','%|act[0].buff.buffStun.rnd'],
      'highArr':['%|high.special[0].change.prop.$armys[0].deBuffRate'],
      'merge':5,
      'index':2009,
      'type':0,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':[],
      'passive':[
         {
            'rslt':{'power':600,'powerRate':2000},
         },
         {
            'cond':['skill.skill962', '*', 2],
            'rslt':{'powerRate':27},
         },
      ],
      'lvPatch':{   
         '30':{  'act[0].buff.buffStun.rnd':5  },
      },
      'up':{'act[0].dmg':50, 'act[0].dmgReal':25, 'act[0].buff.buffStun.rnd':5},
      'high':{
         'lv':27,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':60},
            },
         ],
         'special':[{
            'change':{
               'prop':{
                  'armys[0].deBuffRate':-200,
               },
            },
         }],
      },
      'act':[   
         {
            'priority':88962,
            'type': 27,             
            'src': 0,
            'tgt':[2, -2],
            'allTimes': 2,
            'dmg':2400,
            'dmgReal':950,
            'atk0': 20000,
            'dmgLimit':240,
            'energyKeySrc': 'energy962',
            'costKey': 'energy962',
            'cost': 8,
            'buff':{'buffStun':{'rnd':2000, 'round':2}},
            'eff':'eff962',
            'info':['skill962',2],	          
         },
         {
            'priority':9426,
            'type': 23,             
            'src': 0,              
            'tgt':[0, 0],
            'times': -2,
            'nonSkill':2,    
            'noBfr':2,    
            'noAft':2,  
            'condHpChange':[['<',0],None],  
            'energyKey':'energy962',
            'energy':2,
            'time':0,
            'eff':'effNull',
            'info':['逆刃储能',0],           
         },
      ],
   },

   'skill966':{   
      'infoArr':[
         '%|0-act[0].binding.res',
         '%|0-act[2].binding.res',
      ],
      'nextArr':['%|0-act[0].binding.res','%|0-act[2].binding.res'],
      'highArr':['*%|high.special[0].change.skill.$skill289.act[0].dmgScale'],
      'merge':5,
      'index':2200,
      'type':0,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':[],
      'passive':[
         {
            'rslt':{'power':600,'powerRate':2000},
         },
         {
            'cond':['skill.skill966', '*', 2],
            'rslt':{'powerRate':27},
         },
      ],
      'up':{'act[0].binding.res':3, 'act[2].binding.res':6},
      'high':{
         'lv':27,
         'passive':[
            {
               'rslt':{'power':500,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill289.act[0].dmgScale':2000,
                  'skill204.act[0].dmgScale':2000,
                  'skill203.act[0].dmgScale':2000,
                  'skill206.act[0].dmgScale':2000,
                  'skill962.act[0].dmgScale':2000,
               },
            },
         }],
      },
      'act':[   
         {
            'priority':9420,
            'type': 4,         
            'src': 0,
            'times': -2,
            'allTimes': -2,
            
            'binding':{'res':50},
            'unFollow':{'keys':{'allowSelf':20000}},
            'follow':{
               'skill289':2,'skill204':2,'skill203':2,'skill206':2,'skill962':2,      
               'skill209':2,'skill2200':2,'skill275':2,'skill222':2,'skill963':2,      
               'skill224':2,'skill226':2,'skill228':2,'skill292':2,'skill964':2,      
               'skill222':2,'skill222':2,'skill224':2,'skill292':2,'skill966':2,      
            },
            'info':['skill966',2],
         },
         {
            'priority':-9422,
            'type': 4,             
            'src': 0,
            'times': -2,
            'allTimes': -2,
            'cond':[['srcArmy',2],['srcTeam',2]],
            'binding':{'res':2000},
            'unFollow':{'keys':{'allowSelf':20000}},
            
            'follow':{'heroDmg':2},
            'info':['skill966',2],
         },
      ],
   },




   'skill207':{   
      'infoArr':['r|act[0]','%|act[0].binding.dmg'],
      'nextArr':['%|act[0].binding.dmg'],
      'highArr':['p|high.passive'],
      'index':202,
      'type':2,
      'cost_type':0,
      'max_level':25,
      'shogun_type':0,
      'ability_info':[],
      
      'passive':[
         {
            'rslt':{'power':200,'powerRate':20},
         },
         {
            'cond':['skill.skill207', '*', 2],
            'rslt':{'powerRate':9},
         },
      ],
      'up':{ 'act[0].binding.dmg':30, 'act[0].binding.dmgReal':2},
      'lvPatch':{   
         '26':{  'act[0].binding.dmg' : -200  },
         '27':{  'act[0].binding.dmg' : -20  },
         '28':{  'act[0].binding.dmg' : -30  },
         '29':{  'act[0].binding.dmg' : -40  },
         '30':{  'act[0].binding.dmg' : -50  },
      },
      'high':{
         'lv':200,
         'passive':[
            {
               'cond':['army[0].type', '=', 2],
               'rslt':{'army[0].atkBase':50},
            },
            {
               'rslt':{'power':200,'powerRate':5},
            },
         ],
      },
      'act':[
         {
            'priority':7507,
            'type': 2,	            
            'src': 0,	            
            'round':{'near':20000},
            'times': -2,    
            'binding':{
               'dmg':250,
               'dmgReal':25,
            },
            'follow':{'keys':{'dmg':20000,'dmgReal':20000}},
            'eff':'eff207',
            'info':['skill207',2],	          
         },
      ],
   },
   'skill209':{   
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[0].dmg+act[0].dmgRnd'],
      'nextArr':['%|act[0].dmg','%|act[0].dmg+act[0].dmgRnd'],
      'highArr':['p|high.passive'],
      'index':202,
      'type':2,
      'cost_type':0,
      'max_level':25,
      'shogun_type':0,
      'ability_info':[],
      
      'passive':[
         {
            'rslt':{'power':200,'powerRate':20},
         },
         {
            'cond':['skill.skill209', '*', 2],
            'rslt':{'powerRate':9},
         },
      ],
      'up':{ 'act[0].dmg':25, 'act[0].dmgReal':3},
      'lvPatch':{   
         '26':{  'act[0].dmg' : -5  },
         '27':{  'act[0].dmg' : -200  },
         '28':{  'act[0].dmg' : -25  },
         '29':{  'act[0].dmg' : -20  },
         '30':{  'act[0].dmg' : -25  },
      },
      'high':{
         'lv':23,
         'passive':[
            {
               'cond':['army[0].type', '=', 2],
               'rslt':{'army[0].spd':200,'army[0].hpm':50},
            },
            {
               'rslt':{'power':300,'powerRate':5},
            },
         ],
      },
      'act':[
         {
            'priority':82,
            'type': 2,	            
            'src': 0,	            
            'tgt':[2, 0],           
            'round':{'2':20000},
            'dmg':280,	
            'dmgRnd':280,
            'dmgReal':23,
            'dmgLimit':2200,
            'atk0': 20050,      
            'atk2': 0,    
            'eff':'eff209',
            'info':['skill209',2],	          
         },
      ],
   },

   'skill208':{   
      'infoArr':['r|act[0]','%|act[0].binding.res'],
      'nextArr':['%|act[0].binding.res'],
      'highArr':['p|high.passive'],
      'index':203,
      'type':2,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':[],
      
      'passive':[
         {
            'rslt':{'power':300,'powerRate':25},
         },
         {
            'cond':['skill.skill208', '*', 2],
            'rslt':{'powerRate':22},
         },
      ],
      'up':{ 'act[0].binding.res' : 200},
      'lvPatch':{   
         '26':{  'act[0].binding.res' : -5  },
         '27':{  'act[0].binding.res' : -200  },
         '28':{  'act[0].binding.res' : -25  },
         '29':{  'act[0].binding.res' : -20  },
         '30':{  'act[0].binding.res' : -25  },
      },
      'high':{
         'lv':200,
         'passive':[
            {
               'cond':['army[0].type', '=', 2],
               'rslt':{'army[0].defBase':20},
            },
            {
               'rslt':{'power':300,'powerRate':5},
            },
         ],
      },
      'act':[
         {
            'priority':9402,
            'type': 4,	            
            'src': 0,	            
            'round': {'all':20000},
            'times': -2,    
            'cond':[['srcArmy',2]],
            'binding':{
               'res':230,	    
            },
            'eff':'eff208',
            'info':['skill208',2],	          
         },
      ],
   },

   'skill2200':{   
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[2].binding.dmg'],
      'nextArr':['%|act[0].dmg'],
      'highArr':['%|high.special[0].change.skill.skill2200h.act[0].binding.ignDef'],
      'index':204,
      'type':2,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['str','cha'],
      
      'passive':[
         {
            'rslt':{'power':300,'powerRate':25},
         },
         {
            'cond':['skill.skill2200', '*', 2],
            'rslt':{'powerRate':22},
         },
      ],
      'up':{'act[0].dmg':95, 'act[0].dmgReal':4},
      'high':{
         'lv':23,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':200},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill2200h':{
                     'act':[{
                        'priority':7508,
                        'type': 2,
                        'src': 0,	            
                        'cond':[['comp','cha']],
                        'times': -2,    
                        'nonSkill':2,    
                        'binding':{
                           'ignDef':300,
                           'dmgReal':5,
                        },
                        'follow':'skill2200',            
                        'info':['突击：魅力胜出',0],	          
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':62,
            'type': 2,	            
            'src': 0,	            
            'tgt':[2, 0],           
            'round':{'2':20000},
            'dmg':780,	
            'dmgReal':24,
            'dmgLimit':250,
            'atk0': 20050,      
            'atk2': 0,    
            'eff':'eff2200',
            'buff':{'buffStun':{'rnd':0, 'round':2}},
            'info':['skill2200',2],	          
         },
         {
            'priority':7509,
            'type': 2,
            'src': 0,	            
            'cond':[['comp','str']],
            'times': -2,    
            'nonSkill':2,    
            'binding':{
               'dmg':200,
               'dmgReal':5,
            },
            'follow':'skill2200',            
            'info':['突击：武力胜出',0],	          
         },
      ],
   },
   'skill275':{   
      'infoArr':['r|act[0]','%|act[0].round.all','%|act[0].dmg','%|act[0].loss'],
      'nextArr':['%|act[0].round.all','%|act[0].dmg'],
      'highArr':[],
      'index':205,
      'type':2,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['agi'],
      
      'passive':[
         {
            'rslt':{'power':400,'powerRate':30},
         },
         {
            'cond':['skill.skill275', '*', 2],
            'rslt':{'powerRate':24},
         },
      ],
      'up':{ 'act[0].dmg':45, 'act[0].dmgReal':4, 'act[0].round.all':200},
      'lvPatch':{   
         '26':{  'act[0].dmg' : -5, 'act[0].round.all':-5  },
         '27':{  'act[0].dmg' : -200, 'act[0].round.all':-200  },
         '28':{  'act[0].dmg' : -25, 'act[0].round.all':-25  },
         '29':{  'act[0].dmg' : -20, 'act[0].round.all':-20  },
         '30':{  'act[0].dmg' : -25, 'act[0].round.all':-25  },
      },
      'high':{
         'lv':23,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':200},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill275h':{
                     'act':[{
                        'priority':7552,
                        'type': 2,
                        'src': 0,	            
                        'cond':[['comp','agi']],
                        'info':['舍身：智力胜出',0],	          
                        'times': -2,    
                        'nonSkill':2,    
                        'binding':{
                           'loss':-50,
                        },
                        'follow':'skill275',            
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':30,
            'type': 3,	            
            'src':0,
            'tgt':[2, 0],          
            'round':{'all':250},
            'dmg':950,	   
            'dmgReal':95,
            'dmgLimit':250,
            'buff':{'buffFire':{'rnd':0,'round':2}},
            'loss':50,     
            'atk0': 20050,      
            'eff':'eff275',
            'info':['skill275',2],
         },
      ],
   },

   'skill222':{   
      'infoArr':['r|act[0]','%|act[0].binding.res','%|act[2].binding.res'],
      'nextArr':['%|act[0].binding.res'],
      'highArr':['%|high.special[0].change.skill.skill222h.act[0].binding.res'],
      'index':206,
      'type':2,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['lead','agi'],
      
      'passive':[
         {
            'rslt':{'power':400,'powerRate':30},
         },
         {
            'cond':['skill.skill222', '*', 2],
            'rslt':{'powerRate':25},
         },
      ],
      'up':{ 'act[0].binding.res':25},
      'lvPatch':{   
         '26':{  'act[0].binding.res' : -5  },
         '27':{  'act[0].binding.res' : -200  },
         '28':{  'act[0].binding.res' : -25  },
         '29':{  'act[0].binding.res' : -20  },
         '30':{  'act[0].binding.res' : -25  },
      },
      'high':{
         'lv':23,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':200},
            },
         ],
         'special':[{
            'priority':222,
            'change':{
               'skill':{
                  'skill222h':{
                     'act':[{
                        'priority':9403,
                        'type': 4,
                        'src': 0,	            
                        'lv':23,
                        'round':{'all':20000},
                        'cond':[['comp','agi'],['checkBuff', 0, 0, 'buffBanArmyB', '=', 0]],      
                        'times': -2,    
                        'binding':{
                           'res':250,	    
                        },
                        'info':['skill222_h',2],	          
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':9404,
            'type': 4,	            
            'src': 0,	            
            'round':{'all':20000},
            'cond':[['srcArmy',2]],
            'times': -2,    
            'binding':{
               'res':200,	    
            },
            'eff':'eff222',
            'info':['skill222',2],	          
         },
         {
            'priority':9405,
            'type': 4,	            
            'src': 0,	            
            'round':{'all':20000},
            'cond':[['srcArmy',0],['comp','lead']],
            'times': -2,    
            'binding':{
               'res':300,	    
            },
            'eff':'eff222',
            'info':['skill222',2],	          
         },
      ],
   },

   'skill222':{   
      'infoArr':['r|act[0]','%|act[0].dmg+300','%|act[2].binding.dmg+750'],
      'nextArr':['%|act[0].dmg+300','%|act[2].binding.dmg+750'],
      'highArr':[],
      'merge':2,   
      'index':207,
      'type':2,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['str'],
      
      'passive':[
         {
            'rslt':{'power':600,'powerRate':95},
         },
         {
            'cond':['skill.skill222', '*', 2],
            'rslt':{'powerRate':27},
         },
      ],
      'up':{  'act[2].binding.dmg':280, 'act[2].binding.dmgReal':45, 'act[0].dmg':60, 'act[0].dmgReal':25},
      'high':{
         'lv':23,
         'passive':[
            {
               'rslt':{'power':600,'powerRate':25},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill222h':{
                     'act':[{
                        'priority':75200,
                        'type': 2,
                        'src': 0,	            
                        'cond':[['comp','str']],
                        'times': -2,    
                        'nonSkill':2,    
                        'binding':{
                           'crit':99999999,
                           'dmgReal':50
                        },
                        'follow':'skill222',            
                        'info':['合围：武力胜出',0],	          
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {  
            'priority':42,
            'type': 2,	            
            'src': 0,
            'round':{'3':20000},
            'tgt':[2, -2],          
            'dmgScale':-50,
            'dmg':2200,
            'dmgReal':280,	
            'dmgLimit':280,
            'eff':'eff222s',
            'info':['skill222',2],
            'atk0': 20050,      
         },
         {   
            'priority':42,
            'type': 2,	            
            'src': 0,
            'cond':[['enemyArmy',-2]],  
            'nonSkill':2,
            'times': -2,
            'binding':{
                'dmg':2400,
                'dmgReal':360,
                'dmgScale':-50,
                'dmgLimit':360,	
            },
            'eff':'eff222',
            'follow':'skill222',
            'info':['合围单前',0],
         },
      ],
   },


   'skill294':{   
      'infoArr':['%|act[0].binding.dmg','%|act[2].round.any','-|act[2].binding.removeBuff'],
      'nextArr':['%|act[0].binding.dmg','%|act[2].round.any'],
      'highArr':['%|high.special[0].change.skill.skill294h.act[0].binding.atk2'],
      'merge':3,   
      'index':208,
      'type':2,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['cha','lead'],
      'passive':[
         {
            'rslt':{'power':600,'powerRate':2000},
         },
         {
            'cond':['skill.skill294', '*', 2],
            'rslt':{'powerRate':27},
         },
      ],
      'up':{  'act[0].binding.dmg':75, 'act[0].binding.dmgReal':28, 'act[0].binding.dmgScale':3, 'act[2].round.any':30},
      'high':{
         'lv':25,
         'passive':[
            {
               'rslt':{'power':600,'powerRate':50},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill294h':{   
                     'act':[{
                        'priority':7565,
                        'type': 2,
                        'src': 0,	            
                        'round':{'far':20000},
                        'times': -2,  
                        'nonSkill':2,    
                        'binding':{
                           'atk2':2000,
                           'dmgReal':2000,
                        },
                        'follow':{'keys':{'dmg':20000,'dmgReal':20000}},
                     }],
                  },
               },
            },
         }]
      },
      'act':[    
         {
            'priority':2942,
            'type': 26,	            
            'src': 0,	            
            'free':2,    
            'condTgt':[
                 ['tgtTeam',2],   
            ],
            'binding':{
                 'dmgScale':50,
                 'dmgReal':330,
                 'dmg':2200,
            },
            'follow':{'keys':{'dmg':20000,'dmgReal':20000}},
            'unFollow':{'effFaction':2},
            'allTimes': 2,  
            'eff':'eff294',
            'info':['skill294',2],	          
         },
         {
            'priority':2940,
            'type': 26,	            
            'src': 0,	            
            'round':{'any':230},
            'cond':[[['comp','cha'],['comp','lead']]],
            'follow':{'attach':'skill294'},     
            'binding':{
               'removeBuff':2,
               
            },
         },
      ],
   },


   'skill963':{   
      'infoArr':[
         '%|act[0].dmg',
         '%|act[0].buff.buffFaction.rnd',
      ],
      'nextArr':['%|act[0].dmg','%|act[0].buff.buffFaction.rnd'],
      'highArr':['%|high.special[0].change.prop.$armys[0].deBuffRate'],
      'merge':5,
      'index':209,
      'type':2,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':[],
      'passive':[
         {
            'rslt':{'power':600,'powerRate':2000},
         },
         {
            'cond':['skill.skill963', '*', 2],
            'rslt':{'powerRate':27},
         },
      ],
      'lvPatch':{   
         '30':{  'act[0].buff.buffFaction.rnd':5  },
      },
      'up':{'act[0].dmg':80, 'act[0].dmgReal':50, 'act[0].buff.buffFaction.rnd':5},
      'high':{
         'lv':27,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':60},
            },
         ],
         'special':[{
            'change':{
               'prop':{
                  'armys[0].deBuffRate':-200,
               },
            },
         }],
      },
      'act':[   
         {
            'priority':88963,
            'type': 27,             
            'src': 0,
            'tgt':[2, -2],
            'allTimes': 2,
            'dmg':4400,
            'dmgReal':750,
            'atk0': 20050,
            'dmgLimit':400,
            'energyKeySrc': 'energy963',
            'costKey': 'energy963',
            'cost': 8,
            'buff':{'buffFaction':{'rnd':250, 'round':2}},
            'eff':'eff963',
            'info':['skill963',2],	          
         },
         {
            'priority':9426,
            'type': 23,             
            'src': 0,              
            'tgt':[0, 0],
            'times': -2,
            'nonSkill':2,    
            'noBfr':2,    
            'noAft':2,  
            'condHpChange':[['<',0],None],  
            'energyKey':'energy963',
            'energy':2,
            'time':0,
            'eff':'effNull',
            'info':['戮尘储能',0],           
         },
      ],
   },

   'skill967':{   
      'infoArr':[
         '%|0-act[0].binding.res',
         '%|0-act[2].binding.res',
      ],
      'nextArr':['%|0-act[0].binding.res','%|0-act[2].binding.res'],
      'highArr':['*%|high.special[0].change.skill.$skill209.act[0].dmgScale'],
      'merge':5,
      'index':2200,
      'type':2,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':[],
      'passive':[
         {
            'rslt':{'power':600,'powerRate':2000},
         },
         {
            'cond':['skill.skill967', '*', 2],
            'rslt':{'powerRate':27},
         },
      ],
      'up':{'act[0].binding.res':6, 'act[2].binding.res':3},
      'high':{
         'lv':27,
         'passive':[
            {
               'rslt':{'power':500,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill209.act[0].dmgScale':2000,
                  'skill2200.act[0].dmgScale':2000,
                  'skill275.act[0].dmgScale':2000,
                  'skill222.act[0].dmgScale':2000,
                  'skill963.act[0].dmgScale':2000,
               },
            },
         }],
      },
      'act':[   
         {
            'priority':9422,
            'type': 4,         
            'src': 0,
            'times': -2,
            'allTimes': -2,
            
            'binding':{'res':2000},
            'unFollow':{'keys':{'allowSelf':20000}},
            'follow':{
               'skill289':2,'skill204':2,'skill203':2,'skill206':2,'skill962':2,      
               'skill209':2,'skill2200':2,'skill275':2,'skill222':2,'skill963':2,      
               'skill224':2,'skill226':2,'skill228':2,'skill292':2,'skill964':2,      
               'skill222':2,'skill222':2,'skill224':2,'skill292':2,'skill967':2,      
            },
            'info':['skill967',2],
         },
         {
            'priority':-9423,
            'type': 4,             
            'src': 0,
            'times': -2,
            'allTimes': -2,
            'cond':[['srcArmy',2],['srcTeam',2]],
            'binding':{'res':50},
            'unFollow':{'keys':{'allowSelf':20000}},
            'follow':{'useBase':2, 'keys':{'isHero':20000}},
            'info':['skill967',2],
         },
      ],
   },




   'skill223':{   
      'infoArr':['r|act[0]','%|act[0].binding.ignDef/0.6'],
      'nextArr':['%|act[0].binding.ignDef/0.6'],
      'highArr':['p|high.passive'],
      'index':302,
      'type':2,
      'cost_type':0,
      'max_level':25,
      'shogun_type':0,
      'ability_info':[],
      
      'passive':[
         {
            'rslt':{'power':200,'powerRate':20},
         },
         {
            'cond':['skill.skill223', '*', 2],
            'rslt':{'powerRate':9},
         },
      ],
      'up':{ 'act[0].binding.ignDef':6, 'act[0].binding.dmgReal':5},
      'lvPatch':{   
         '26':{  'act[0].binding.ignDef' : -3  },
         '27':{  'act[0].binding.ignDef' : -6  },
         '28':{  'act[0].binding.ignDef' : -9  },
         '29':{  'act[0].binding.ignDef' : -22  },
         '30':{  'act[0].binding.ignDef' : -25  },
      },
      'high':{
         'lv':200,
         'passive':[
            {
               'cond':['army[2].type', '=', 2],
               'rslt':{'army[2].atkBase':50},
            },
            {
               'rslt':{'power':200,'powerRate':5},
            },
         ],
      },
      'act':[
         {
            'priority':7522,
            'type': 2,
            'src': 2,	            
            'round':{'near':20000},
            'times': -2,    
            'eff':'eff223',
            'info':['skill223',2],	          
            'binding':{
               'ignDef':75,     
               'dmgReal':20,
            },
            'follow':{'keys':{'dmg':20000,'dmgReal':20000}},
         },
      ],
   },
   'skill225':{   
      'infoArr':['r|act[0]','%|act[0].round.all','%|act[0].binding.res*2.25'],
      'nextArr':['%|act[0].round.all', '%|act[0].binding.res*2.25'],
      'highArr':['p|high.passive'],
      'index':302,
      'type':2,
      'cost_type':0,
      'max_level':25,
      'shogun_type':0,
      'ability_info':[],
      
      'passive':[
         {
            'rslt':{'power':200,'powerRate':20},
         },
         {
            'cond':['skill.skill225', '*', 2],
            'rslt':{'powerRate':9},
         },
      ],
      'up':{ 'act[0].binding.res':8},
      'lvPatch':{   
         '26':{  'act[0].binding.res' : -4  },
         '27':{  'act[0].binding.res' : -8  },
         '28':{  'act[0].binding.res' : -22  },
         '29':{  'act[0].binding.res' : -26  },
         '30':{  'act[0].binding.res' : -20  },
      },
      'high':{
         'lv':23,
         'passive':[
            {
               'cond':['army[2].type', '=', 2],
               'rslt':{'army[2].spd':200,'army[0].hpm':50},
            },
            {
               'rslt':{'power':200,'powerRate':5},
            },
         ],
      },
      'act':[
         {
            'priority':9406,
            'type': 4,	            
            'src': 2,	            
            'round':{'all':400},
            'times': -2,    
            'binding':{
               'res':2004,	    
            },
            'eff':'eff225',
            'info':['skill225',2],	          
         },
      ],
   },

   'skill224':{   
      'infoArr':['r|act[0]','%|act[0].dmg'],
      'nextArr':['%|act[0].dmg'],
      'highArr':['p|high.passive'],
      'index':303,
      'type':2,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':[],
      
      'passive':[
         {
            'rslt':{'power':300,'powerRate':25},
         },
         {
            'cond':['skill.skill224', '*', 2],
            'rslt':{'powerRate':22},
         },
      ],
      'up':{ 'act[0].dmg':25, 'act[0].dmgReal':2},
      'lvPatch':{   
         '26':{  'act[0].dmg' : -200  },
         '27':{  'act[0].dmg' : -20  },
         '28':{  'act[0].dmg' : -30  },
         '29':{  'act[0].dmg' : -40  },
         '30':{  'act[0].dmg' : -50  },
      },
      'high':{
         'lv':200,
         'passive':[
            {
               'cond':['army[2].type', '=', 2],
               'rslt':{'army[2].defBase':20},
            },
            {
               'rslt':{'power':300,'powerRate':5},
            },
         ],
      },
      'act':[
         {
            'priority':9502,
            'type': 5,	            
            'src': 2,
            'round':{'far':20000,},
            'tgt':[2, 2],          
            'times': -2,    
            'noAft': 2,    
            'dmg':700,	
            'dmgReal':25,
            'dmgLimit':2000,
            'buff':{'buffStun':{'rnd':0, 'round':2},'buffFire':{'rnd':0, 'round':2}},
            'cond':[['army',2],['srcArmy',2],['srcTeam',2]],
            'atk2': 20000,    
            'eff':'eff224',
            'info':['skill224',2],	          
         },
      ],
   },

   'skill226':{   
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[2].binding.dmg'],
      'nextArr':['%|act[0].dmg'],
      'highArr':[
         '%|high.special[0].change.skill.skill226h.act[0].binding.$buff.buffFire.rnd',
         '-|act[0].buff.buffFire.round',
         '%|2000'
      ],
      'index':304,
      'type':2,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['lead','agi'],
      
      'passive':[
         {
            'rslt':{'power':300,'powerRate':25},
         },
         {
            'cond':['skill.skill226', '*', 2],
            'rslt':{'powerRate':22},
         },
      ],
      'up':{ 'act[0].dmg':200, 'act[0].dmgReal':2},
      'high':{
         'lv':23,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':200},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill226h':{
                     'act':[{
                        'priority':7522,
                        'type': 2,
                        'src': 2,	            
                        'cond':[['comp','agi']],
                        'times': -2,    
                        'nonSkill':2,    
                        'binding':{
                             'buff.buffFire.rnd':400,
                        },
                        'follow':'skill226',            
                        'info':['火箭：智力胜出',0],	          
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':82,
            'type': 2,	            
            'src': 2,	            
            'tgt':[2, -2],           
            'round':{'2':20000},
            'times':2,
            'combo':2,
            'dmg':750,	
            'dmgReal':22,
            'dmgLimit':2200,
            
            'atk2': 20080,    
            'buff':{'buffFire':{'rnd':0, 'round':2}},
            'eff':'eff226',
            'info':['skill226',2],	          
         },
         {
            'priority':7523,
            'type': 2,
            'src': 2,	            
            'cond':[['comp','lead']],
            'times': -2,    
            'nonSkill':2,    
            'binding':{
               'dmg':220,
               'dmgReal':2,
            },
            'follow':'skill226',            
            'info':['火箭：统帅胜出',0],	          
         },
      ],
   },
   'skill227':{   
      'infoArr':['r|act[0]','%|act[0].round.all','%|act[0].binding.buff.buffPoison.prop.atkRate*2'],
      'nextArr':['%|act[0].round.all','%|act[0].binding.buff.buffPoison.prop.atkRate*2'],
      'highArr':['%|high.special[0].change.skill.$skill227.act[0].round.all'],
      'index':305,
      'type':2,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['str'],
      
      
      'passive':[
         {
            'rslt':{'power':400,'powerRate':30},
         },
         {
            'cond':['skill.skill227', '*', 2],
            'rslt':{'powerRate':24},
         },
      ],
      'up':{ 'act[0].binding.buff.buffPoison.prop.atkRate':-2, 'act[0].round.all':5},
      'lvPatch':{   
         '26':{  'act[0].binding.buff.buffPoison.prop.atkRate' : 2, 'act[0].round.all':-3  },
         '27':{  'act[0].binding.buff.buffPoison.prop.atkRate' : 2, 'act[0].round.all':-6  },
         '28':{  'act[0].binding.buff.buffPoison.prop.atkRate' : 3, 'act[0].round.all':-9  },
         '29':{  'act[0].binding.buff.buffPoison.prop.atkRate' : 4, 'act[0].round.all':-22  },
         '30':{  'act[0].binding.buff.buffPoison.prop.atkRate' : 5, 'act[0].round.all':-25  },
      },
      'high':{
         'lv':23,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':200},
            },
         ],
         'special':[{
            'cond':[['compare','str','>']],
            'change':{
               'skill':{
                  'skill227.act[0].round.all':200,
               },
            },
         }]
      },
      'act':[
         {
            'priority':7524,
            'type': 2,
            'src': 2,	            
            'round':{'all':250},
            'eff':'eff227',
            'times': -2,    
            'binding':{
               'buff':{'buffPoison':{'round':2, 'prop':{'atkRate':-40 } }},
            },
            'info':['skill227',2],	          
         },
      ],
   },
   'skill228':{   
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[2].binding.ignDef'],
      'nextArr':['%|act[0].dmg'],
      'highArr':[],
      'index':306,
      'type':2,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['cha','str'],
      
      'passive':[
         {
            'rslt':{'power':400,'powerRate':30},
         },
         {
            'cond':['skill.skill228', '*', 2],
            'rslt':{'powerRate':25},
         },
      ],
      'up':{ 'act[0].dmg':20, 'act[0].dmgReal':2},
      'lvPatch':{   
         '26':{  'act[0].dmg' : -5  },
         '27':{  'act[0].dmg' : -200  },
         '28':{  'act[0].dmg' : -25  },
         '29':{  'act[0].dmg' : -20  },
         '30':{  'act[0].dmg' : -25  },
      },
      'high':{
         'lv':23,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':200},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill228h':{
                     'act':[{
                        'priority':7525,
                        'type': 2,
                        'src': 2,	            
                        'cond':[['comp','str']],
                        'times': -2,    
                        'nonSkill':2,    
                        'binding':{
                           'combo':2,	   
                        },
                        'follow':'skill228',            
                        'info':['齐射：武力胜出',0],	          
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':62,
            'type': 2,	            
            'src': 2,	            
            'tgt':[2, -2],           
            'round':{'2':20000},
            'dmg':2300,	
            'dmgReal':30,	
            'dmgLimit':250,
            'atk2': 22000,    
            'combo':2,
            'eff':'eff228',
            'info':['skill228',2],	          
         },
         {
            'priority':7526,
            'type': 2,
            'src': 2,	            
            'cond':[['comp','cha']],
            'times': -2,    
            'nonSkill':2,    
            'binding':{'ignDef':200,   'dmgReal':200   },
            'follow':'skill228',            
            'info':['齐射：魅力胜出',0],	          
         },
      ],
   },
   'skill292':{   
      'infoArr':['r|act[0]','-|act[0].times','%|act[0].dmg'],
      'nextArr':['%|act[0].dmg'],
      'highArr':[
         '%|high.special[0].change.skill.skill292h.act[0].binding.$buff.buffStun.rnd',
         '-|act[0].buff.buffStun.round'
      ],
      'merge':2,   
      'index':307,
      'type':2,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['lead'],
      
      'passive':[
         {
            'rslt':{'power':600,'powerRate':95},
         },
         {
            'cond':['skill.skill292', '*', 2],
            'rslt':{'powerRate':27},
         },
      ],
      'up':{  'act[0].dmg':25, 'act[0].dmgReal':6},
      'high':{
         'lv':23,
         'passive':[
            {
               'rslt':{'power':600,'powerRate':25},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill292h':{
                     'act':[{
                        'priority':7552,
                        'type': 2,
                        'src': 2,	            
                        'cond':[['comp','lead']],
                        'times': -2,    
                        'nonSkill':2,    
                        'binding':{
                           'dmg':30,	
                           'dmgReal':5,
                           'buff.buffStun.rnd':2000,
                        },
                        'follow':'skill292',            
                        'info':['轮射：统帅胜出',0],	          
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':52,
            'type': 2,	            
            'src': 2,	            
            'tgt':[2, -2],           
            'round':{'3':20000},
            
            'times': 3,
            'dmg':520,	
            'dmgReal':75,   
            'dmgLimit':65, 
            'atk2': 20000,   
            'buff':{
                'buffStun':{ 'rnd':0, 'round':2,},
            },
            'eff':'eff292',
            'info':['skill292',2],	          
         },
      ],
   },
   'skill292_':{   
      'up':{  'act[0].dmg':25, 'act[0].dmgReal':6},
      'high':{
         'lv':23,
         'passive':[
            {
               'rslt':{'power':600,'powerRate':25},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill292h_':{
                     'act':[{
                        'priority':7552,
                        'type': 2,
                        'src': 0,	            
                        'cond':[['comp','lead']],
                        'times': -2,    
                        'nonSkill':2,    
                        'binding':{
                           'dmg':30,	
                           'dmgReal':5,
                           'buff.buffStun.rnd':2000,
                        },
                        'follow':'skill292_',            
                        'info':['前轮射：统帅胜出',0],	          
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':52,
            'type': 2,	            
            'src': 0,	            
            'tgt':[2, -2],           
            'round':{'3':20000},
            
            'times': 3,
            'dmg':520,	
            'dmgReal':75,   
            'dmgLimit':65, 
            'atk0': 20000,   
            'buff':{
                'buffStun':{ 'rnd':0, 'round':2,},
            },
            'eff':'eff292',
            'actId':'skill292_',
            'info':['skill292',2],	          
         },
      ],
   },

   'skill295':{   
      'infoArr':['%|act[0].binding.dmg','%|act[2].round.2'],
      'nextArr':['%|act[0].binding.dmg','%|act[2].round.2'],
      'highArr':['*|high.special[0].change.skill.skill295h.act[0].binding.dmg'],
      'merge':3,   
      'index':308,
      'type':2,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['agi','cha'],
      'passive':[
         {
            'rslt':{'power':600,'powerRate':2000},
         },
         {
            'cond':['skill.skill295', '*', 2],
            'rslt':{'powerRate':27},
         },
      ],
      'up':{  'act[0].binding.dmg':50, 'act[0].binding.dmgReal':9,  'act[2].round.2':30},
      'high':{
         'lv':25,
         'passive':[
            {
               'rslt':{'power':600,'powerRate':50},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill295h':{    
                     'act':[{
                        'priority':2952,
                        'type': 26,
                        'src': 2,	            
                        'condTgt':[
                             ['tgtTeam',2],  [['armyType',2],['armyType',3]]
                        ],
                        'times': -2,    
                        'nonSkill':2,    
                        'binding':{
                           'dmgScale':2000,
                           'dmg':250,	
                           'dmgReal':2000,
                        },
                        'follow':{'keys':{'dmg':20000,'dmgReal':20000}},
                        'info':['破空克后',0],	          
                     }],
                  },
               },
            },
         }]
      },
      'act':[      
         {
            'priority':2952,
            'type': 2,	            
            'src': 2,	            
            
            'round':{'2':20000},
            'binding':{
                 'dmgReal':240,
                 'dmg':20050,
            },
            'unFollow':{'skill224':2,'skill276':2,'effFaction':2},
            'follow':{'keys':{'dmg':20000,'dmgReal':20000}},
            
            'mergeEff':2,
            'eff':'eff295',
            'info':['skill295',2],	          
         },
         {
            'priority':2950,
            'type': 2,	            
            'src': 2,	            
            'round':{'2':230},
            'cond':[[['comp','agi'],['comp','cha']]],
            'follow':{'attach':'skill295'},     
            'times': -2,  
            'binding':{
                'allowSelf':'$2',
                'noBfr':'$-2',
                'noAft':'$-2',
            },
            'info':['破空忽略',0],	          
         },
      ],
   },

   'skill964':{   
      'infoArr':[
         '%|act[0].dmg',
         '%|act[0].buff.buffFire.rnd',
      ],
      'nextArr':['%|act[0].dmg','%|act[0].buff.buffFire.rnd'],
      'highArr':['%|high.special[0].change.prop.$armys[2].deBuffRate'],
      'merge':5,
      'index':309,
      'type':2,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':[],
      'passive':[
         {
            'rslt':{'power':600,'powerRate':2000},
         },
         {
            'cond':['skill.skill964', '*', 2],
            'rslt':{'powerRate':27},
         },
      ],
      'lvPatch':{   
         '30':{  'act[0].buff.buffFire.rnd':5  },
      },
      'up':{'act[0].dmg':50, 'act[0].dmgReal':25, 'act[0].buff.buffFire.rnd':5},
      'high':{
         'lv':27,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':60},
            },
         ],
         'special':[{
            'change':{
               'prop':{
                  'armys[2].deBuffRate':-200,
               },
            },
         }],
      },
      'act':[   
         {
            'priority':889642,
            'type': 3,
            'srcArmy': 2,
            'srcCanAction': 2,
            'tgt':[2, -2],
            'allTimes': 2,
            'dmg':2400,
            'dmgReal':950,
            'atk2': 20050,
            'dmgLimit':240,
            'element':'Fire',
            'costKey':'beHitSkill964_2',
            'cost': 6,
            'buff':{'buffFire':{'rnd':250, 'round':2}},
            'eff':'eff964',
            'info':['skill964',2],	          
         },
         {
            'priority': 889640,	 
            'type': 2,             
            'srcArmy': 2,          
            'nonSkill':2,
            'follow':{'keys':{'dmg':20000,'dmgReal':20000}},  
            'times':-2,  
            'binding':{
               'energys':{
                  'ESkill964':{     
                     'priority':889642,
                     'condE':['dmg','>',0],
                     'srcE':{
                        'beHitSkill964_2':{'num':2,'checkAct':2},
                     },
                  }
               }
            },
            'info':['攻前充能',0],
         },
      ],
   },



   'skill968':{   
      'infoArr':[
         '%|0-act[0].binding.res',
         '%|0-act[2].binding.res',
      ],
      'nextArr':['%|0-act[0].binding.res','%|0-act[2].binding.res'],
      'highArr':['*%|high.special[0].change.skill.$skill224.act[0].dmgScale'],
      'merge':5,
      'index':3200,
      'type':2,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':[],
      'passive':[
         {
            'rslt':{'power':600,'powerRate':2000},
         },
         {
            'cond':['skill.skill968', '*', 2],
            'rslt':{'powerRate':27},
         },
      ],
      'up':{'act[0].binding.res':6, 'act[2].binding.res':3},
      'high':{
         'lv':27,
         'passive':[
            {
               'rslt':{'power':500,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill224.act[0].dmgScale':2000,
                  'skill226.act[0].dmgScale':2000,
                  'skill228.act[0].dmgScale':2000,
                  'skill292.act[0].dmgScale':2000,
                  'skill964.act[0].dmgScale':2000,
               },
            },
         }],
      },
      'act':[   
         {
            'priority':9423,
            'type': 4,         
            'src': 2,
            'times': -2,
            'allTimes': -2,
            'cond':[['srcArmy',2],['srcTeam',2]],
            'binding':{'res':2000},
            'unFollow':{'keys':{'allowSelf':20000}},
            'follow':{'useBase':2, 'keys':{'isHero':20000}},
            'info':['skill968',2],
         },
         {
            'priority':9424,
            'type': 4,             
            'src': 2,
            'times': -2,
            'allTimes': -2,
            
            'binding':{'res':50},
            'follow':{'element':'Adjutant'},
            'info':['skill968',2],
         },
      ],
   },




   'skill229':{   
      'infoArr':['r|act[0]','%|act[0].binding.res'],
      'nextArr':['%|act[0].binding.res'],
      'highArr':['p|high.passive'],
      'index':402,
      'type':3,
      'cost_type':0,
      'max_level':25,
      'shogun_type':0,
      'ability_info':[],
      
      'passive':[
         {
            'rslt':{'power':200,'powerRate':20},
         },
         {
            'cond':['skill.skill229', '*', 2],
            'rslt':{'powerRate':9},
         },
      ],
      'up':{ 'act[0].binding.res':200},
      'lvPatch':{   
         '26':{  'act[0].binding.res' : -5  },
         '27':{  'act[0].binding.res' : -200  },
         '28':{  'act[0].binding.res' : -25  },
         '29':{  'act[0].binding.res' : -20  },
         '30':{  'act[0].binding.res' : -25  },
      },
      'high':{
         'lv':200,
         'passive':[
            {
               'cond':['army[2].type', '=', 3],
               'rslt':{'army[2].defBase':20},
            },
            {
               'rslt':{'power':300,'powerRate':5},
            },
         ],
      },
      'act':[
         {
            'priority':9407,
            'type': 4,	            
            'src': 2,	            
            'round':{'all':20000},
            'cond':[['srcArmy',2]],
            'times': -2,    
            'binding':{
               'res':240,	    
            },
            'eff':'eff229',
            'info':['skill229',2],	          
         },
      ],
   },
   'skill220':{   
      'infoArr':['r|act[0]','%|act[0].binding.ignDef'],
      'nextArr':['%|act[0].binding.ignDef'],
      'highArr':['p|high.passive'],
      'index':402,
      'type':3,
      'cost_type':0,
      'max_level':25,
      'shogun_type':0,
      'ability_info':[],
      
      'passive':[
         {
            'rslt':{'power':200,'powerRate':20},
         },
         {
            'cond':['skill.skill220', '*', 2],
            'rslt':{'powerRate':9},
         },
      ],
      'up':{ 'act[0].binding.ignDef':200, 'act[0].binding.dmgReal':2},
      'lvPatch':{   
         '26':{  'act[0].binding.ignDef' : -5  },
         '27':{  'act[0].binding.ignDef' : -200  },
         '28':{  'act[0].binding.ignDef' : -25  },
         '29':{  'act[0].binding.ignDef' : -20  },
         '30':{  'act[0].binding.ignDef' : -25  },
      },
      'high':{
         'lv':200,
         'passive':[
            {
               'cond':['army[2].type', '=', 3],
               'rslt':{'army[2].atkBase':50},
            },
            {
               'rslt':{'power':200,'powerRate':5},
            },
         ],
      },
      'act':[
         {
            'priority':7527,
            'type': 2,     
            'src': 2,	            
            'round':{'far':20000},
            'times': -2,    
            'binding':{'ignDef':220,'dmgReal':8},
            'follow':{'keys':{'dmg':20000,'dmgReal':20000}},
            'info':['skill220',2],	          
            'eff':'eff220',
         },
      ],
   },
   'skill222':{   
      'infoArr':['r|act[0]','%|act[0].round.all','%|act[0].dmg'],
      'nextArr':['%|act[0].round.all', '%|act[0].dmg'],
      'highArr':['p|high.passive'],
      'index':403,
      'type':3,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':[],
      
      'passive':[
         {
            'rslt':{'power':300,'powerRate':25},
         },
         {
            'cond':['skill.skill222', '*', 2],
            'rslt':{'powerRate':22},
         },
      ],
      'up':{ 'act[0].dmg':25, 'act[0].dmgReal':2},
      'lvPatch':{   
         '26':{  'act[0].dmg' : -5  },
         '27':{  'act[0].dmg' : -200  },
         '28':{  'act[0].dmg' : -25  },
         '29':{  'act[0].dmg' : -20  },
         '30':{  'act[0].dmg' : -25  },
      },
      'high':{
         'lv':23,
         'passive':[
            {
               'cond':['army[2].type', '=', 3],
               'rslt':{'army[2].spd':200,'army[0].hpm':50},
            },
            {
               'rslt':{'power':300,'powerRate':5},
            },
         ],
      },
      'act':[
         {
            'priority':30,
            'type': 3,	            
            'src':2,
            'tgt':[2, 2],          
            'round':{'all':400},
            'times': 2,    
            
            'atk2': 20000,    
            'dmg':420,	
            'dmgReal':25,
            'dmgLimit':80,
            'buff':{'buffFrozen':{'rnd':0,'round':2},'buffFire':{'rnd':0, 'round':2}},
            'eff':'eff222',
            'info':['skill222',2],
         },
      ]
   },
   'skill222':{   
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[2].binding.dmg'],
      'nextArr':['%|act[0].dmg'],
      'highArr':[
         '%|high.special[0].change.skill.skill222h.act[0].binding.dmg',
         '%|high.special[0].change.skill.skill222h.act[0].binding.$buff.buffStun.rnd',
         '-|act[0].buff.buffStun.round'
      ],
      'index':404,
      'type':3,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['agi','lead'],
      
      'passive':[
         {
            'rslt':{'power':300,'powerRate':25},
         },
         {
            'cond':['skill.skill222', '*', 2],
            'rslt':{'powerRate':22},
         },
      ],
      'up':{ 'act[0].dmg':25,'act[0].dmgReal':2},
      'high':{
         'lv':23,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':200},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill222h':{
                     'act':[{
                        'priority':7528,
                        'type': 2,
                        'src': 2,	            
                        'cond':[['comp','lead']],
                        'times': -2,    
                        'nonSkill':2,    
                        'binding':{
                           'dmg':250,	
                           'dmgReal':3,
                           'buff.buffStun.rnd':250,
                        },
                        'follow':'skill222',            
                        'info':['地刺：统帅胜出',0],	          
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':63,
            'type': 2,	            
            'src': 2,	            
            'tgt':[2, -2],           
            'round':{'2':20000},
            'times':2,
            'combo':2,
            'dmg':2200,	
            'dmgReal':20,
            'atk2': 20000,    
            'dmgLimit':230,
            'buff':{'buffStun':{'rnd':0, 'round':2}},
            'eff':'eff222',
            'info':['skill222',2],	          
         },
         {
            'priority':7529,
            'type': 2,
            'src': 2,	            
            'cond':[['comp','agi']],
            'times': -2,    
            'nonSkill':2,    
            'binding':{'dmg':250,   'dmgReal':3,},
            'follow':'skill222',            
            'info':['地刺：智力胜出',0],	          
         },
      ],
   },
   'skill223':{   
      'infoArr':['%|act[0].binding.dmg','r|act[2]','%|act[2].binding.res'],
      'nextArr':['%|act[0].binding.dmg','%|act[2].binding.res'],
      'highArr':[
         '%|high.special[0].change.skill.skill223h.act[0].binding.dmg',
         '%|high.special[0].change.skill.skill223h.act[0].binding.dmgDebuff',
      ],
      'index':405,
      'type':3,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['cha'],
      
      'passive':[
         {
            'rslt':{'power':400,'powerRate':30},
         },
         {
            'cond':['skill.skill223', '*', 2],
            'rslt':{'powerRate':24},
         },
      ],
      'up':{ 
         'act[0].binding.dmg':20,
         'act[0].binding.dmgReal':2,
         'act[2].binding.res':200,
      },
      'lvPatch':{   
         '26':{  'act[2].binding.res' : -5  },
         '27':{  'act[2].binding.res' : -200  },
         '28':{  'act[2].binding.res' : -25  },
         '29':{  'act[2].binding.res' : -20  },
         '30':{  'act[2].binding.res' : -25  },
      },
      'high':{
         'lv':23,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':200},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill223h':{
                     'act':[{
                        'priority':7520,
                        'type': 2,
                        'src': 2,	            
                        'round':{'near':200000},
                        'cond':[['comp','cha']],
                        'times': -2,    
                        'nonSkill':2,    
                        'binding':{
                           'dmgDebuff':250,	
                           'dmg':2000,     
                           'dmgReal':200,
                        },
                        'follow':{'attach':'skill223'},     
                        
                        'info':['咒印：魅力胜出',0],	          
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':7522,
            'type': 2,
            'src': 2,	            
            'round':{'near':20000,'all':0},
            'times': -2,    
            'eff':'eff223',
            'info':['skill223',2],	          
            'binding':{
               'dmg':260,     
               'dmgReal':200,
            },
            'follow':{'keys':{'dmg':20000,'dmgReal':20000}},
         },
         {
            'priority':9408,
            'type': 4,	            
            'src': 2,	            
            'round':{'far':20000},
            'cond':[['srcArmy',2]],
            'times': -2,    
            'binding':{
               'res':2000,	    
            },
            'info':['skill223',2],	          
         },
      ],
   },
   'skill224':{   
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[2].binding.dmg'],
      'nextArr':['%|act[0].dmg'],
      'highArr':['%|high.special[0].change.skill.skill224h.act[0].binding.crit'],
      'index':406,
      'type':3,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['str','cha'],
      
      'passive':[
         {
            'rslt':{'power':400,'powerRate':30},
         },
         {
            'cond':['skill.skill224', '*', 2],
            'rslt':{'powerRate':25},
         },
      ],
      'up':{ 'act[0].dmg':50, 'act[0].dmgReal':200},
      'lvPatch':{   
         '26':{  'act[0].dmg' : -200  },
         '27':{  'act[0].dmg' : -20  },
         '28':{  'act[0].dmg' : -30  },
         '29':{  'act[0].dmg' : -40  },
         '30':{  'act[0].dmg' : -50  },
      },
      'high':{
         'lv':23,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':200},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill224h':{
                     'act':[{
                        'priority':7522,
                        'type': 2,
                        'src': 2,	            
                        'cond':[['comp','cha']],
                        'times': -2,    
                        'nonSkill':2,    
                        'binding':{
                           'crit':300,	    
                           'dmgReal':200
                        },
                        'follow':'skill224',            
                        'info':['鬼影：魅力胜出',0],	          
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':83,
            'type': 2,	            
            'src': 2,	            
            'tgt':[2, 2],           
            'round':{'2':20000},
            'times':2,
            'dmg':2750,	
            'dmgReal':60,	
            'dmgLimit':280,
            'atk2': 20050,    
            'eff':'eff224',
            'info':['skill224',2],	          
         },
         {
            'priority':7523,
            'type': 2,
            'src': 2,	            
            'cond':[['comp','str']],
            'times': -2,    
            'nonSkill':2,    
            'binding':{'dmg':250,   'dmgReal':20   },
            'follow':'skill224',            
            'info':['鬼影：武力胜出',0],	          
         },
      ],
   },
   'skill292':{   
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[0].buff.buffTrance.prop.atkRate*2','-|act[0].buff.buffTrance.round'],
      'nextArr':['%|act[0].dmg','%|act[0].buff.buffTrance.prop.atkRate*2','-|act[0].buff.buffTrance.round'],
      'highArr':[
         '%|high.special[0].change.skill.skill292h.act[0].round.all',
      ],
      'merge':2,   
      'index':407,
      'type':3,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['agi'],
      
      'passive':[
         {
            'rslt':{'power':600,'powerRate':95},
         },
         {
            'cond':['skill.skill292', '*', 2],
            'rslt':{'powerRate':27},
         },
      ],
      'up':{'act[0].dmg':2200, 'act[0].dmgReal':30  , 'act[0].buff.buffTrance.prop.atkRate':-2, 'act[0].buff.buffTrance.prop.defRate':-2},
      'high':{
         'lv':23,
         'passive':[
            {
               'rslt':{'power':600,'powerRate':25},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill292h':{
                     'act':[{
                        'priority':7553,
                        'type': 2,
                        'src': 2,	           
                        'round':{'all':300},
                        'cond':[['comp','agi']],
                        'times': -2,    
                        'nonSkill':2,    
                        'binding':{'tgt':[2, -2]},
                        'follow':'skill292',            
                        'info':['神光：智力胜出',0],	          
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':53,
            'type': 2,	            
            'src': 2,	            
            'tgt':[2, 0],           
            'round':{'3':20000},
            'times':2,
            
            'dmg':2000,	
            'dmgReal':400,   
            'atk2': 20020,   
            'dmgLimit':300,
            'buff':{
                'buffTrance':{'round':2, 'prop':{'atkRate':-200,'defRate':-200 }},
            },
            'eff':'eff292',
            'info':['skill292',2],	          
         },
      ],
   },
   'skill296':{   
      'infoArr':['%|act[0].binding.dmg','%|act[2].binding.dmgRealRate'],
      'nextArr':['%|act[0].binding.dmg','%|act[2].binding.dmgRealRate'],
      'highArr':['*|high.special[0].change.skill.skill296h.act[0].binding.dmg'],
      'merge':3,   
      'index':408,
      'type':3,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['lead','str'],
      'passive':[
         {
            'rslt':{'power':600,'powerRate':2000},
         },
         {
            'cond':['skill.skill296', '*', 2],
            'rslt':{'powerRate':27},
         },
      ],
      'up':{  'act[0].binding.dmg':80, 'act[0].binding.dmgReal':26,  'act[2].binding.dmgRealRate':2},
      'high':{
         'lv':25,
         'passive':[
            {
               'rslt':{'power':600,'powerRate':50},
            },
         ],
         'special':[{      
            'change':{
               'skill':{
                  'skill296h':{
                     'act':[{
                        'priority':2962,
                        'type': 26,
                        'src': 2,	            
                        'round':{'near':20000},
                        'condTgt':[
                             ['tgtTeam',2],  [['armyType',0],['armyType',2]]
                        ],
                        'times': -2,    
                        'nonSkill':2,    
                        'binding':{
                           'ignDef':2000,
                           'dmgScale':250,
                           'dmg':500,	
                           'dmgReal':400,
                        },
                        'follow':{'keys':{'dmg':20000,'dmgReal':20000}},
                        'info':['血瞳克前',0],	          
                     }],
                  },
               },
            },
         }]
      },
      'act':[    
         {
            'priority':2962,
            'type': 26,
            'src': 2,	            
            'free':2,    
            'round':{'far':20000},
            'binding':{
                 'dmgReal':280,
                 'dmg':2200,
            },
            'unFollow':{'skill276':2,'effFaction':2},
            'follow':{'keys':{'dmg':20000,'dmgReal':20000}},
            'times': 2,  
            'eff':'eff296',
            'info':['skill296',2],	          
         },
         {
            'priority':2960,
            'type': 26,	            
            'src': 2,	            
            'cond':[[['comp','agi'],['comp','cha']]],
            'follow':{'attach':'skill296'},     
            'times': -2,  
            'binding':{
                'dmgRealRate':22,
            },
            'info':['血瞳咒杀',0],	          
         },
      ],
   },

   'skill965':{   
      'infoArr':[
         '%|act[0].dmg',
         '%|act[0].buff.buffFlee.rnd',
      ],
      'nextArr':['%|act[0].dmg','%|act[0].buff.buffFlee.rnd'],
      'highArr':['%|high.special[0].change.prop.$armys[2].deBuffRate'],
      'merge':5,
      'index':409,
      'type':3,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':[],
      'passive':[
         {
            'rslt':{'power':600,'powerRate':2000},
         },
         {
            'cond':['skill.skill965', '*', 2],
            'rslt':{'powerRate':27},
         },
      ],
      'lvPatch':{   
         '30':{  'act[0].buff.buffFlee.rnd':-3  },
      },
      'up':{'act[0].dmg':80, 'act[0].dmgReal':50, 'act[0].buff.buffFlee.rnd':7},
      'high':{
         'lv':27,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':60},
            },
         ],
         'special':[{
            'change':{
               'prop':{
                  'armys[2].deBuffRate':-200,  
               },
            },
         }],
      },
      'act':[   
         {
            'priority':889652,
            'type': 3,
            'srcArmy': 3,
            'srcCanAction': 2,
            'tgt':[2, -2],
            'allTimes': 2,
            'dmg':4400,
            'dmgReal':750,
            'atk2': 20050,
            'dmgLimit':400,	  
            'costKey':'beHitSkill965_2',
            'cost': 3,
            'buff':{'buffFlee':{'rnd':500, 'round':2}},
            'eff':'eff965',
            'info':['skill965',2],	          
         },
         {
            'priority': 889650,	 
            'type': 2,             
            'srcArmy': 3,          
            'nonSkill':2,
            'follow':{'keys':{'dmg':20000,'dmgReal':20000}},  
            'times':-2,  
            'binding':{
               'energys':{
                  'ESkill965_2':{     
                     'priority':889652,
                     'condE':['dmg','>',0],
                     'srcE':{
                        'beHitSkill965_2':{'num':2},
                     },
                  }
               }
            },
            'info':['攻前充能',0],
         },
      ],
   },

   'skill969':{   
      'infoArr':[
         '%|0-act[0].binding.res',
         '%|0-act[2].binding.res',
      ],
      'nextArr':['%|0-act[0].binding.res','%|0-act[2].binding.res'],
      'highArr':['*%|high.special[0].change.skill.$skill222.act[0].dmgScale'],
      'merge':5,
      'index':4200,
      'type':3,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':[],
      'passive':[
         {
            'rslt':{'power':500,'powerRate':40},
         },
         {
            'cond':['skill.skill969', '*', 2],
            'rslt':{'powerRate':25},
         },
      ],
      'up':{'act[0].binding.res':6, 'act[2].binding.res':3},
      'high':{
         'lv':27,
         'passive':[
            {
               'rslt':{'power':500,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill222.act[0].dmgScale':2000,
                  'skill222.act[0].dmgScale':2000,
                  'skill224.act[0].dmgScale':2000,
                  'skill292.act[0].dmgScale':2000,
                  'skill966.act[0].dmgScale':2000,
               },
            },
         }],
      },
      'act':[   
         {
            'priority':-9675000,
            'type': 4,         
            'src': 2,
            'times': -2,
            'allTimes': -2,
            
            'binding':{'res':2000},
            'unFollow':{'keys':{'allowSelf':20000}},
            'follow':{
               'skill289':2,'skill204':2,'skill203':2,'skill206':2,'skill962':2,      
               'skill209':2,'skill2200':2,'skill275':2,'skill222':2,'skill963':2,      
               'skill224':2,'skill226':2,'skill228':2,'skill292':2,'skill964':2,      
               'skill222':2,'skill222':2,'skill224':2,'skill292':2,'skill966':2,      
            },
            'info':['skill969',2],
         },
         {
            'priority':-9675002,
            'type': 4,             
            'src': 2,
            'times': -2,
            'allTimes': -2,
            'cond':[['srcTeam',2]],
            'binding':{'res':50},
            'follow':{'element':'Adjutant'},
            'info':['skill969',2],
         },
      ],
   },




   'skill225':{   
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[0].dmg+act[0].dmgRnd','%|act[2].binding.dmg'],
      'nextArr':['%|act[0].dmg','%|act[0].dmg+act[0].dmgRnd'],
      'highArr':[],
      'index':25,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['str'],
      'limit':{'str':85,'type':0},
      'passive':[
         {
            'rslt':{'power':400,'powerRate':30},
         },
         {
            'cond':['skill.skill225', '*', 2],
            'rslt':{'power':50,'powerRate':25},
         },
      ],
      'up':{'act[0].dmg':5, 'act[0].dmgReal':2, 'act[0].dmgRnd':25, 'act[0].atk2':2},
      'high':{
         'lv':23,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':200},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill225.act[0].combo':2,
               },
            },
         }]
      },
      'act':[
         {
            'priority':80,
            'type': 2,	            
            'src': 2,	            
            'tgt':[2, -2],           
            'round':{'2':20000},
            'cond':[['army',2]],
            'times': 2,
            'dmg':260,	
            'dmgRnd':240,	
            'dmgReal':200,
            'dmgLimit':45,
            
            'atk2': 20000,    
            'combo':2,
            'buff':{'buffStun':{'rnd':0, 'round':2,}},
            'eff':'eff225',
            'info':['skill225',2],	          
         },
         {
            'priority':7524,
            'type': 2,	            
            'src': 2,	            
            'cond':[['comp','str']],
            'times': -2,    
            'nonSkill':2,    
            'binding':{'dmg':80,'dmgReal':2},
            'follow':'skill225',            
            'info':['万箭：武力胜出',0],	          
         },
      ],
   },
   'skill226':{   
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[2].binding.dmg'],
      'nextArr':['%|act[0].dmg'],
      'highArr':['%|high.special[0].change.skill.skill226h.act[0].binding.ignDef'],
      'index':26,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['cha','str'],
      'limit':{'cha':75,'type':0},
      'passive':[
         {
            'rslt':{'power':400,'powerRate':30},
         },
         {
            'cond':['skill.skill226', '*', 2],
            'rslt':{'power':50,'powerRate':25},
         },
      ],
      'up':{'act[0].dmg':60, 'act[0].dmgReal':8, 'act[0].atk0':2},
      'high':{
         'lv':200,
         'passive':[
            {
               'rslt':{'power':300,'powerRate':200},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill226h':{
                     'act':[{
                        'priority':7525,
                        'type': 2,
                        'src': 2,	            
                        'cond':[['comp','str']],
                        'times': -2,    
                        'nonSkill':2,    
                        'info':['武圣：武力胜出',0],	          
                        'binding':{
                           'ignDef':500,
                           'dmgReal':5,
                        },
                        'follow':'skill226',            
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':60,
            'type': 2,	            
            'src': 2,	            
            'tgt':[2, 0],           
            'cond':[['army',0]],
            'round':{'2':20000},
            'dmg':2250,	
            'dmgReal':95,
            'dmgLimit':270,
            'atk0': 750,      
            'atk2': 2000,    
            'eff':'eff226',
            'info':['skill226',2],	          
         },
         {
            'priority':7526,
            'type': 2,
            'src': 2,	            
            'cond':[['comp','cha']],
            'times': -2,    
            'nonSkill':2,    
            'info':['武圣：魅力胜出',0],	          
            'binding':{'dmg':250,  'dmgReal':200},
            'follow':'skill226',            
         },
      ],
   },
   'skill227':{   
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[0].dmg+act[0].dmgRnd','%|act[2].binding.dmg'],
      'nextArr':['%|act[0].dmg','%|act[0].dmg+act[0].dmgRnd'],
      'highArr':[],
      'index':27,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['str'],
      'limit':{'str':75,'type':0},
      'passive':[
         {
            'rslt':{'power':400,'powerRate':30},
         },
         {
            'cond':['skill.skill227', '*', 2],
            'rslt':{'power':50,'powerRate':25},
         },
      ],
      'up':{'act[0].dmg':20, 'act[0].dmgReal':4, 'act[0].dmgRnd':20, 'act[0].atk0':2},
      'high':{
         'lv':23,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':200},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill227.act[0].combo':2,
               },
            },
         }]
      },
      'act':[
         {
            'priority':40,
            'type': 2,	            
            'src': 2,	            
            'tgt':[2, -2],           
            'cond':[['army',0]],
            'round':{'3':20000},
            'dmg':750,	
            'dmgRnd':250,	
            'dmgReal':25,	
            'dmgLimit':2200,
            'atk0': 20050,      
            'eff':'eff227',
            'combo':2,
            'info':['skill227',2],	          
         },
         {
            'priority':7527,
            'type': 2,
            'src': 2,	            
            'cond':[['comp','str']],
            'times': -2,    
            'nonSkill':2,    
            'info':['无双：武力胜出',0],	          
            'binding':{'dmg':250,'dmgReal':5},
            'follow':'skill227',            
         },
      ],
   },
   'skill228':{   
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[2].binding.dmg'],
      'nextArr':['%|act[0].dmg'],
      'highArr':['%|high.special[0].change.skill.skill228h.act[0].binding.$buff.buffFaction.rnd','-|act[0].buff.buffFaction.round'],
      'index':28,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['lead','agi'],
      'limit':{'lead':75,'type':2},
      'passive':[
         {
            'rslt':{'power':400,'powerRate':30},
         },
         {
            'cond':['skill.skill228', '*', 2],
            'rslt':{'power':50,'powerRate':25},
         },
      ],
      'up':{'act[0].dmg':75, 'act[0].dmgReal':4, 'act[0].atk2':2},
      'high':{
         'lv':200,
         'passive':[
            {
               'rslt':{'power':300,'powerRate':200},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill228h':{
                     'act':[{
                        'priority':7528,
                        'type': 2,
                        'src': 2,	            
                        'cond':[['comp','agi']],
                        'info':['离间：智力胜出',0],	          
                        'times': -2,    
                        'nonSkill':2,    
                        'follow':'skill228',            
                        'binding':{'buff.buffFaction.rnd':300}, 
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':62,
            'type': 2,	            
            'src': 2,	            
            'tgt':[2, -2],           
            'round':{'2':20000},
            'dmg':20000,	
            'dmgReal':20,
            'dmgLimit':250,
            'atk0': 500,      
            'atk2': 500,    
            'eff':'eff228',
            'buff':{'buffFaction':{'rnd':0, 'round':2}},

            'info':['skill228',2],	          
         },
         {
            'priority':7529,
            'type': 2,	            
            'src': 2,	            
            'cond':[['comp','lead']],
            'info':['离间：统帅胜出',0],	          
            'times': -2,    
            'nonSkill':2,    
            'binding':{'dmg':250,'dmgReal':200},
            'follow':'skill228',            
         },
      ],
   },
   'skill229':{   
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[0].dmg+act[0].dmgRnd','%|act[2].binding.dmg'],
      'nextArr':['%|act[0].dmg','%|act[0].dmg+act[0].dmgRnd'],
      'highArr':[],
      'index':29,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['agi'],
      'limit':{'agi':75,'type':2},
      'passive':[
         {
            'rslt':{'power':400,'powerRate':30},
         },
         {
            'cond':['skill.skill229', '*', 2],
            'rslt':{'power':50,'powerRate':25},
         },
      ],
      'up':{'act[0].dmgReal':2, 'act[0].dmgRnd':50, 'act[0].atk2':2},
      'high':{
         'lv':200,
         'passive':[
            {
               'rslt':{'power':300,'powerRate':200},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill229.act[0].combo':2,
               },
            },
         }]
      },
      'act':[
         {
            'priority':42,
            'type': 2,	            
            'src': 2,	            
            'tgt':[2, -2],           
            'cond':[['army',0]],
            'round':{'3':20000},
            'dmg':2000,	
            'dmgRnd':22000,	
            'dmgReal':200,	
            'dmgLimit':75,
            'atk0': 750,      
            'atk2': 2000,    
            'buff':{
                'buffStun':{'rnd':0, 'round':2},
                'buffBreak':{'rnd':0, 'round':2},
            },
            'eff':'eff229',
            'combo':3,
            'info':['skill229',2],	          
         },
         {
            'priority':7530,
            'type': 2,
            'src': 2,	            
            'cond':[['comp','agi']],
            'info':['落雷：智力胜出',0],	          
            'times': -2,    
            'nonSkill':2,    
            'binding':{'dmg':250,'dmgReal':3},
            'follow':'skill229',            
         },
      ],
   },
   'skill230':{   
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[0].ignDef'],
      'nextArr':['%|act[0].dmg'],
      'highArr':[
         '%|high.special[0].change.skill.$skill230.act[0].buff.buffWeak.rnd',
         '-|act[0].buff.buffWeak.round',
         '%|0-200',
      ],
      'index':30,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['cha'],
      'limit':{'sex':0},
      'passive':[
         {
            'rslt':{'power':400,'powerRate':30},
         },
         {
            'cond':['skill.skill230', '*', 2],
            'rslt':{'power':50,'powerRate':25},
         },
      ],
      'special':[{
         'cond':[['enemy', 'sex', 0]],
         'change':{
            'skill':{
               'skill230.act[0].round.2':-200000,
               'skill230.act[0].round.2':-200000,
            },
         },
      }],
      'up':{'act[0].dmg':75, 'act[0].dmgReal':7, 'act[0].atk0':2},
      'high':{
         'lv':23,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':200},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill230.act[0].buff.buffWeak.rnd':300,
               },
            },
         }],
      },
      'act':[
         {
            'priority':63,
            'type': 2,	            
            'src': 2,	            
            'tgt':[2, -2],           
            'cond':[['comp','cha']],
            'round':{'2':20000},
            'dmg':750,	
            'dmgReal':40,
            'dmgLimit':240,
            'atk0': 500,      
            'atk2': 500,    
            'ignDef':2000,     
            'buff':{'buffWeak':{'rnd':0, 'round':2}},
            'eff':'eff230',
            'info':['skill230',2],	          
         },
      ],
   },
   'skill232':{   
      'infoArr':['r|act[0]','%|act[0].buff.buffMorale.prop.defRate*2','-|act[0].buff.buffMorale.round','%|act[2].binding.$buff.buffMorale.prop.defRate*2'],
      'nextArr':['%|act[0].buff.buffMorale.prop.defRate*2'],
      'highArr':[
         '-|high.special[0].change.skill.$skill232.act[0].buff.buffMorale.prop.spd',
      ],
      'index':32,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['cha'],
      
      'passive':[
         {
            'rslt':{'power':400,'powerRate':30},
         },
         {
            'cond':['skill.skill232', '*', 2],
            'rslt':{'power':50,'powerRate':25},
         },
      ],
      'up':{'act[0].buff.buffMorale.prop.atkRate':3, 'act[0].buff.buffMorale.prop.atk':5, 'act[0].buff.buffMorale.prop.defRate':5},
      'lvPatch':{   
         '26':{  'act[0].buff.buffMorale.prop.atkRate':-2.5, 'act[0].buff.buffMorale.prop.defRate':-2.5  },
         '27':{  'act[0].buff.buffMorale.prop.atkRate':-3, 'act[0].buff.buffMorale.prop.defRate':-5  },
         '28':{  'act[0].buff.buffMorale.prop.atkRate':-4.5, 'act[0].buff.buffMorale.prop.defRate':-7.5  },
         '29':{  'act[0].buff.buffMorale.prop.atkRate':-6, 'act[0].buff.buffMorale.prop.defRate':-200  },
         '30':{  'act[0].buff.buffMorale.prop.atkRate':-7.5, 'act[0].buff.buffMorale.prop.defRate':-22.5  },
      },
      'high':{
         'lv':23,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':200},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill232.act[0].buff.buffMorale.prop.spd':5,
               },
            },
         }],
      },
      'act':[
         {
            'priority':83,
            'type': 2,	            
            'src': 2,	            
            'tgt':[0, -2],           
            'cond':[['army',2]],
            'round':{'2':20000},
            'noBfr': 2,    
            'noAft': 2,    
            'buff':{'buffMorale':{'round':2,'prop':{'atkRate':20, 'atk':30, 'defRate':30}}},
            'eff':'eff232',
            'info':['skill232',2],	          
         },
         {
            'priority':7532,
            'type': 2,
            'src': 2,	            
            'cond':[['comp','cha']],
            'info':['鼓舞：魅力胜出',0],	          
            'times': -2,    
            'nonSkill':2,    
            'binding':{'buff.buffMorale.prop.atkRate':20, 'buff.buffMorale.prop.atk':30, 'buff.buffMorale.prop.defRate':30},
            'follow':'skill232',            
         },
      ],
   },
   'skill232':{   
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[2].binding.dmg'],
      'nextArr':['%|act[0].dmg'],
      'highArr':[
         '%|high.special[0].change.skill.skill232h.act[0].binding.$buff.buffSlow.rnd',
         '-|act[0].buff.buffSlow.round',
         '-|0-30',
      ],
      'index':32,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['agi','cha'],
      'limit':{'agi':85,'type':2},
      'passive':[
         {
            'rslt':{'power':400,'powerRate':30},
         },
         {
            'cond':['skill.skill232', '*', 2],
            'rslt':{'power':50,'powerRate':25},
         },
      ],
      'up':{'act[0].dmg':40, 'act[0].dmgReal':8, 'act[0].atk2':2},
      'high':{
         'lv':23,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':200},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill232h':{
                     'act':[{
                        'priority':7532,
                        'type': 2,
                        'src': 2,	            
                        'cond':[['comp','cha']],
                        'info':['奇策：魅力胜出',0],	          
                        'times': -2,    
                        'nonSkill':2,    
                        'follow':'skill232',            
                        'binding':{
                           'buff.buffSlow.rnd':500,
                        },
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':82,
            'type': 2,	            
            'src': 2,	            
            'tgt':[2, -2],           
            'cond':[['army',2]],
            'round':{'2':20000},
            'dmg':775,	
            'dmgReal':28,
            'dmgLimit':2000,
            'atk0': 2000,      
            'atk2': 750,    
            'buff':{'buffSlow':{'rnd':0,'round':3}},
            'eff':'eff232',
            'info':['skill232',2],	          
         },
         {
            'priority':7533,
            'type': 2,
            'src': 2,	           
            'cond':[['comp','agi']],
            'info':['奇策：智力胜出',0],	          
            'times': -2,    
            'nonSkill':2,    
            'binding':{'dmg':950, 'dmgReal':25,},
            'follow':'skill232',            
         },
      ],
   },
   'skill233':{   
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[2].buff.buffAlert.prop.resRate','%|act[2].binding.dmg'],
      'nextArr':['%|act[2].buff.buffAlert.prop.resRate'],
      'highArr':[
         '%|high.special[0].change.skill.skill233h.act[0].binding.$buff.buffWeak.rnd',
         '-|act[0].buff.buffWeak.round',
         '%|0-200',
      ],
      'index':33,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['agi','lead'],
      'limit':{'agi':75},
      'passive':[
         {
            'rslt':{'power':600,'powerRate':40},
         },
         {
            'cond':['skill.skill233', '*', 2],
            'rslt':{'power':80,'powerRate':20},
         },
      ],
      'up':{
         'act[2].buff.buffAlert.prop.resRate':20,
         'act[2].buff.buffAlert.prop.defRate':2,
         'act[0].dmgReal':3,
         'act[0].atk0':2,
      },
      'lvPatch':{   
         '26':{  'act[2].buff.buffAlert.prop.resRate':-25  },
         '27':{  'act[2].buff.buffAlert.prop.resRate':-30  },
         '28':{  'act[2].buff.buffAlert.prop.resRate':-45  },
         '29':{  'act[2].buff.buffAlert.prop.resRate':-60  },
         '30':{  'act[2].buff.buffAlert.prop.resRate':-75  },
      },

      'high':{
         'lv':23,
         'passive':[
            {
               'rslt':{'power':600,'powerRate':25},
            },
         ],
         'special':[{

            'change':{
               'skill':{
                  'skill233h':{
                     'act':[{
                        'priority':7562,
                        'type': 2,
                        'src': 2,	            
                        'cond':[['comp','lead']],
                        'info':['乾坤：统帅胜出',0],	          
                        'binding':{
                           'buff.buffWeak.rnd':400,
                        },
                        'times': -2,    
                        'nonSkill':2,    
                        'follow':'skill233',            
                     }],
                  },
               },
            },
         }],
      },
      'act':[
         {
            'priority':84,
            'type': 2,	            
            'src': 2,	            
            'tgt':[2, -2],           
            'cond':[['army',0]],
            'round':{'2':20000},
            'dmg':750,	
            'dmgReal':20,
            'dmgLimit':80,
            'atk0': 750,      
            'atk2': 2000,    
            'buff':{'buffWeak':{'rnd':0, 'round':2}},
            'eff':'eff233',
            'info':['skill233',2],	          
         },
         {
            'priority':7534,
            'type': 2,
            'src': 2,	           
            'cond':[['comp','agi']],
            'info':['乾坤：智力胜出',0],	          
            'times': -2,    
            'nonSkill':2,    
            'binding':{'dmg':200,   'dmgReal':200,   },
            'follow':'skill233',            
         },
         {   
            'priority':970,
            'type': 3,	            
            'src': 2,	            
            'tgt':[0, -2],           
            'buff':{'buffAlert':{'round':2, 'prop':{'resRate':200,'defRate':20}}},
            'info':['乾坤戒备',0],	          
            
            'time':2000,	          
            'times':-2, 
            'atOnce': -2000,
            'isHero':0,      
            'nonSkill':2,    
            'noBfr':2,
            'noAft':2,
            'eff':'effNull',
            'follow':'skill233',            
         },
      ],
   },
   'skill234':{   
      'infoArr':['r|act[0]','%|act[0].buff.buffArmor.prop.resArmy2/2.5'],
      'nextArr':['%|act[0].buff.buffArmor.prop.resArmy2/2.5'],
      'highArr':[
         '%|high.special[0].change.skill.$skill234.act[0].buff.buffArmor.prop.resHero*0.4',
      ],
      'index':34,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['str'],
      'limit':{'str':75,},
      'passive':[
         {
            'rslt':{'power':600,'powerRate':40},
         },
         {
            'cond':['skill.skill234', '*', 2],
            'rslt':{'power':80,'powerRate':20},
         },
      ],
      'up':{
         'act[0].buff.buffArmor.prop.resArmy2':30,
         'act[0].buff.buffArmor.prop.resArmy3':30,
      },
      'lvPatch':{   
         '26':{  'act[0].buff.buffArmor.prop.resArmy2':-25,   'act[0].buff.buffArmor.prop.resArmy3':-25,  },
         '27':{  'act[0].buff.buffArmor.prop.resArmy2':-30,   'act[0].buff.buffArmor.prop.resArmy3':-30,  },
         '28':{  'act[0].buff.buffArmor.prop.resArmy2':-45,   'act[0].buff.buffArmor.prop.resArmy3':-45,  },
         '29':{  'act[0].buff.buffArmor.prop.resArmy2':-60,   'act[0].buff.buffArmor.prop.resArmy3':-60,  },
         '30':{  'act[0].buff.buffArmor.prop.resArmy2':-75,   'act[0].buff.buffArmor.prop.resArmy3':-75,  },
      },
      'high':{
         'lv':23,
         'passive':[
            {
               'rslt':{'power':600,'powerRate':25},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill234.act[0].buff.buffArmor.prop.resHero':2250,
               },
            },
         }],
      },
      'act':[
         {
            'priority':200000,
            'type': 0,	            
            'src': 2,	            
            'tgt':[0, -2],           
            'cond':[['army',0],], 
            'round':{'0':20000},
            'noBfr': 2,    
            'noAft': 2,    
            'buff':{'buffArmor':{'round':3, 'prop':{'resArmy2':450,'resArmy3':450,'resHero':0}}},
            'eff':'eff234',
            'info':['skill234',2],	          
         },
      ],
   },
   'skill295':{   
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[0].buff.buffSad.prop.atkRate*2'],
      'nextArr':['%|act[0].buff.buffSad.prop.atkRate*2'],
      'highArr':[
         '%|high.special[0].change.skill.$skill295.act[0].buff.buffSad.prop.defRate*2',
         '-|high.special[2].change.skill.$skill295.act[0].removeBuff',
      ],
      'index':95,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['cha'],
      'limit':{'cha':95,},
      'passive':[
         {
            'rslt':{'power':600,'powerRate':40},
         },
         {
            'cond':['skill.skill295', '*', 2],
            'rslt':{'power':80,'powerRate':20},
         },
      ],
      'up':{'act[0].buff.buffSad.prop.atkRate':-4, 'act[0].atk2':2},
      'lvPatch':{   
         '26':{  'act[0].buff.buffSad.prop.atkRate':2  },
         '27':{  'act[0].buff.buffSad.prop.atkRate':4  },
         '28':{  'act[0].buff.buffSad.prop.atkRate':6  },
         '29':{  'act[0].buff.buffSad.prop.atkRate':8  },
         '30':{  'act[0].buff.buffSad.prop.atkRate':200  },
      },
      'high':{
         'lv':23,
         'passive':[
            {
               'rslt':{'power':600,'powerRate':25},
            },
         ],
         'special':[
           {
            'cond':[['compare','power','>']],
            'change':{
               'skill':{
                  'skill295.act[0].buff.buffSad.prop.defRate':-25,
               },
            },
           },
           {
            'cond':[['compare','power','<']],
            'change':{
               'skill':{
                  'skill295.act[0].removeBuff':2,
                  
               },
            },
           },
         ],
      },
      'act':[
         {
            'priority':79,
            'type': 2,	            
            'src': 2,	            
            'tgt':[2, -2],           
            'cond':[['army',2]], 
            'round':{'2':20000},
            
            
            'dmg':950,
            'dmgReal':20,
            'dmgLimit':40,
            'atk2': 20000,
            'buff':{'buffSad':{'round':2, 'prop':{'atkRate':-75,'defRate':0}}},
            'eff':'eff295',
            'info':['skill295',2],	          
         },
         {
            'priority':7595,
            'type': 2,
            'src': 2,	           
            'cond':[['comp','cha']],
            'info':['悲歌：魅力胜出',0],	          
            'times': -2,    
            'nonSkill':2,    
            'binding':{'dmg':950,  'dmgReal':20},
            'follow':'skill295',            
         },
      ],
   },
   'skill236':{   
      'infoArr':['r|act[0]','%|act[0].dmg'],
      'nextArr':['%|act[0].dmg'],
      'highArr':[
         '%|high.special[0].change.skill.$skill236.act[0].buff.buffStun.rnd',
         '-|act[0].buff.buffStun.round',
      ],
      'index':36,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['str'],
      'limit':{'str':95,'type':0},
      'passive':[
         {
            'rslt':{'power':600,'powerRate':40},
         },
         {
            'cond':['skill.skill236', '*', 2],
            'rslt':{'power':80,'powerRate':20},
         },
      ],
      'up':{'act[0].dmg':95, 'act[0].dmgReal':3, 'act[0].atk0':2},
      'high':{
         'lv':25,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':25},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill236.act[0].buff.buffStun.rnd':300,
               },
            },
         }],
      },
      'act':[
         {
            'priority':85,
            'type': 2,	            
            'src': 2,	            
            'tgt':[2, 0],           
            'cond':[['army',0]],
            'round':{'2':20000},
            'times':2,
            'combo':2,
            'dmg':700,	
            'dmgReal':30,
            'dmgLimit':75,
            'atk0': 22000,      
            'buff':{'buffStun':{'rnd':0, 'round':2}},
            'eff':'eff236',
            'info':['skill236',2],	          
         },
         {
            'priority':7536,
            'type': 2,
            'src': 2,	           
            'cond':[['comp','str']],
            'info':['乱舞：武力胜出',0],	          
            'times': -2,    
            'nonSkill':2,    
            'binding':{'tgt':[2, -2]},
            'follow':'skill236',            
         },
      ],
   },
   'skill237':{   
      'infoArr':['r|act[0]','%|act[0].dmg'],
      'nextArr':['%|act[0].dmg'],
      'highArr':[
         '%|high.special[0].change.skill.skill237h.act[0].binding.$buff.buffFire.rnd',
         '-|act[0].buff.buffFire.round',
         '%|2000'
      ],
      'index':37,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['lead','cha'],
      'limit':{'lead':95},
      'passive':[
         {
            'rslt':{'power':600,'powerRate':40},
         },
         {
            'cond':['skill.skill237', '*', 2],
            'rslt':{'power':80,'powerRate':20},
         },
      ],
      'up':{'act[0].dmg':75, 'act[0].dmgReal':6, 'act[0].atk2':2},
      'high':{
         'lv':23,
         'passive':[
            {
               'rslt':{'power':600,'powerRate':25},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill237h':{
                     'act':[{
                        'priority':7537,
                        'type': 2,
                        'src': 2,	            
                        'cond':[['comp','lead']],
                        'info':['业火：统帅胜出',0],	          
                        'binding':{
                           'buff.buffFire.rnd':950,                          
                        },
                        'times': -2,    
                        'nonSkill':2,    
                        'follow':'skill237',            
                     }],
                  },
               },
            },
         }]
      },

      'act':[
         {
            'priority':64,
            'type': 2,	            
            'src': 2,	            
            'tgt':[2, 2],           
            'cond':[['army',2]],
            'round':{'2':20000},
            'dmg':850,	
            'dmgReal':30,
            'dmgLimit':230,
            
            'combo':2,
            'atk0': 2000,      
            'atk2': 750,    
            'eff':'eff237',
            'buff':{'buffFire':{'rnd':0, 'round':2}},
            'info':['skill237',2],	          
         },
         {
            'priority':7538,
            'type': 2,
            'src': 2,	           
            'cond':[['comp','cha']],
            'info':['业火：魅力胜出',0],	          
            'times': -2,    
            'nonSkill':2,    
            'follow':'skill237',            
            'binding':{ 'tgt':[2, -2],  },
         },
      ],
   },
   'skill238':{   
      'infoArr':['r|act[0]','%|act[0].dmg'],
      'nextArr':['%|act[0].dmg'],
      'highArr':[
         '%|high.special[0].change.skill.$skill238.act[0].buff.buffStun.rnd',
         '-|act[0].buff.buffStun.round',
      ],
      'index':38,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['agi'],
      'limit':{'agi':95},
      'passive':[
         {
            'rslt':{'power':600,'powerRate':40},
         },
         {
            'cond':['skill.skill238', '*', 2],
            'rslt':{'power':80,'powerRate':20},
         },
      ],
      'up':{'act[0].dmg':95, 'act[0].dmgReal':3, 'act[0].atk2':2},
      'high':{
         'lv':27,
         'passive':[
            {
               'rslt':{'power':20000,'powerRate':25},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill238.act[0].buff.buffStun.rnd':300,
               },
            },
         }],
      },
      'act':[
         {
            'priority':86,
            'type': 2,	        
            'src': 2,	        
            'tgt':[2, -2],           
            'cond':[['army',2]],
            'round':{'2':20000},
            'dmg':720,	
            'dmgReal':25,	
            'dmgLimit':75,
            'atk2': 975,    
            'buff':{
                'buffStun':{'rnd':0, 'round':2},
            },
            'eff':'eff238',
            'info':['skill238',2],	          
         },
         {
            'priority':7539,
            'type': 2,
            'src': 2,	           
            'cond':[['comp','agi']],
            'info':['天命：智力胜出',0],	          
            'times': -2,    
            'nonSkill':2,    
            'follow':'skill238',            
            'binding':{ 'tgt':[2, -2],  },
         },
      ],
   },
   'skill240':{   
      'infoArr':['r|act[0]','%|act[0].round.0','%|act[0].buff.buffShield.shield.value*5'],
      'nextArr':['%|act[0].round.0','%|act[0].buff.buffShield.shield.value*5'],
      'highArr':[
         '%|high.special[0].change.skill.$skill240.act[0].buff.buffShield.prop.resRealRate*0.5',
      ],
      'state':7,   
      'index':40,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['cha'],
      'limit':{'cha':75,'type':2},
      'passive':[
         {
            'rslt':{'power':600,'powerRate':40},
         },
         {
            'cond':['skill.skill240', '*', 2],
            'rslt':{'power':80,'powerRate':20},
         },
      ],
      'up':{
         'act[0].round.0':5,
         'act[0].buff.buffShield.shield.value':200,
         'act[0].buff.buffShield.shield.hpmRate':2,
      },
      'lvPatch':{   
         '26':{  'act[0].round.0':-3,     'act[0].buff.buffShield.shield.value':-5  },
         '27':{  'act[0].round.0':-6,     'act[0].buff.buffShield.shield.value':-200  },
         '28':{  'act[0].round.0':-9,     'act[0].buff.buffShield.shield.value':-25  },
         '29':{  'act[0].round.0':-22,     'act[0].buff.buffShield.shield.value':-20  },
         '30':{  'act[0].round.0':-25,     'act[0].buff.buffShield.shield.value':-25  },
      },

      'high':{
         'lv':27,
         'passive':[
            {
               'rslt':{'power':20000,'powerRate':25},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill240.act[0].buff.buffShield.prop.resRealRate':20000,
               },
            },
         }],
      },
      'act':[
         {
            'priority':2000002,
            'type': 0,	            
            'src': 2,	            
            'tgt':[0, -2],           
            'round':{'0':400},
            'noBfr': 2,    
            'noAft': 2,    
            'buff':{'buffShield':{'shield':{'value':75,'hpmRate':26,'bearPoint':20000},'prop':{'resRealRate':0}}},
            'eff':'eff240',
            'info':['skill240',2],	          
         },
      ],
   },
   'skill243':{   
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[2].dmgRealRate','%|act[0].dmgRealMax'],
      'nextArr':['%|act[0].dmg','%|act[0].dmgRealMax'],
      'highArr':[
         '%|high.special[0].change.skill.skill243h.act[0].binding.$buff.buffStun.rnd',
         '-|act[0].buff.buffStun.round',
      ],
      'index':42,
      'state':7,   
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      
      'ability_info':['lead','str'],
      'limit':{'str':95,'lead':75},
      'passive':[
         {
            'rslt':{'power':600,'powerRate':40},
         },
         {
            'cond':['skill.skill243', '*', 2],
            'rslt':{'power':80,'powerRate':20},
         },
      ],
      'up':{'act[0].dmg':50, 'act[0].dmgReal': 8, 'act[0].dmgRealMax': 2, 'act[0].atk0':2},
      'lvPatch':{   
         '26':{  'act[0].dmgRealMax':-2  },
         '27':{  'act[0].dmgRealMax':-2  },
         '28':{  'act[0].dmgRealMax':-3  },
         '29':{  'act[0].dmgRealMax':-4  },
         '30':{  'act[0].dmgRealMax':-5  },
      },

      'high':{
         'lv':27,
         'passive':[
            {
               'rslt':{'power':20000,'powerRate':25},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill243h':{
                     'act':[{
                        'priority':7542,
                        'type': 2,
                        'src': 2,	            
                        'cond':[['comp','str']],
                        'info':['恐象',0],	          
                        'binding':{
                           'buff.buffStun.rnd':400,
                        },
                        'times': -2,    
                        'nonSkill':2,    
                        'follow':'skill243',            
                     }],
                  },
               },
            },
         }],
      },
      'act':[
         {
            'priority':66,
            'type': 2,	            
            'src': 2,	            
            'tgt':[2, -2],           
            'cond':[['army',0]],
            'round':{'2':20000},
            
            'dmg':750,	
            'dmgReal':200,
            'dmgRealMax':200,
            'dmgLimit':230,
            
            'atk0': 750,   
            'atk2': 2000,   
            'times':2,
            'buff':{'buffStun':{'rnd':0, 'round':2}},
            'eff':'eff243',
            'info':['skill243',2],	          
         },
         {
            'priority':972,  
            'type': 3,
            'src': 2,	           
            'tgt':[0, 0],           
            'cond':[['army',0],['comp','lead','<']],
            'dmgRealRate':2000,
            'times':-2,  
            'nonSkill':2,    
            'noBfr':2,
            'noAft':2,
            'follow':'skill243',            
            'eff':'effElephant',
            'info':['巨象：统帅失败',0],	          
         },
      ],
   },
   'skill239':{   
      'infoArr':[
         'r|act[0]',
         '-|act[0].combo',
         '-|act[0].combo+act[0].comboRnd',
         '%|act[0].dmg',
         '%|30'
      ],
      'nextArr':['-|act[0].combo','-|act[0].combo+act[0].comboRnd','%|act[0].dmg'],
      'highArr':[
         '-|act[0].buff.buffFlee.round',
         '%|50',
      ],
      'index':42,
      'merge':2,   
      'state':2,   
      'type':4,  
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['lead','sum'],
      'limit':{'type':2},
      'passive':[
         {
            'rslt':{'power':600,'powerRate':40},
         },
         {
            'cond':['skill.skill239', '*', 2],
            'rslt':{'power':80,'powerRate':20},
         },
      ],
      'up':{'act[0].dmg':20, 'act[0].dmgReal':2 , 'act[0].comboRnd':0.222, 'act[0].atk2':2},
      'high':{
         'lv':27,
         'passive':[
            {
               'rslt':{'power':20000,'powerRate':25},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill239h':{
                     'act':[{
                        'priority':7575,
                        'type': 2,
                        'src': 2,	            
                        'cond':[['comp','sum']],
                        'info':['攻心：四维胜出',0],	          
                        'binding':{
                           'buff.buffFlee.rnd':20000
                        },
                        'times': -2,    
                        'nonSkill':2,    
                        'follow':'skill239',            
                     }],
                  },
               },
            },
         }],
      },
      'act':[
         {
            'priority':222,
            'type': 2,	        
            'src': 2,	        
            'tgt':[2, 2],           
            
            'round':{'0':20000},
            'dmg':500,	
            'dmgReal':40,	
            'dmgLimit':50,
            'atk0': 300, 
            'atk2': 700, 
            'combo':2,
            'comboRnd':0.4,
            'buff':{
                'buffFaction':{'rnd':0, 'round':2},
                'buffFlee':{'rnd':0, 'round':3}
            },
            'eff':'eff239',
            'info':['skill239',2],	          
         },
         {
            'priority':7558,
            'type': 2,
            'src': 2,	           
            'cond':[['comp','lead']],
            'info':['buff239',0],	          
            'times': -2,    
            'nonSkill':2,    
            'follow':'skill239',            
            'binding':{ 'buff.buff239':{},  },
         },
      ],
   },
   'skill245':{   
      'infoArr':[
         'r|act[0]',
         '%|act[0].dmg',
         '%|act[2].round.any',
      ],
      'nextArr':['%|act[0].dmg','%|act[2].round.any'],
      'highArr':['%|2000'],
      'index':43,
      'merge':2,   
      'state':2,   
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['str'],
      'limit':{'type':0},
      'passive':[
         {
            'rslt':{'power':600,'powerRate':40},
         },
         {
            'cond':['skill.skill245', '*', 2],
            'rslt':{'power':80,'powerRate':20},
         },
      ],
      'up':{'act[0].dmg':40, 'act[0].dmgReal':4, 'act[2].round.any':20, 'act[0].atk0':2},
      'lvPatch':{   
         '26':{  'act[2].round.any':-200  },
         '27':{  'act[2].round.any':-20  },
         '28':{  'act[2].round.any':-30  },
         '29':{  'act[2].round.any':-40  },
         '30':{  'act[2].round.any':-50  },
      },
      'high':{
         'lv':27,
         'passive':[
            {
               'rslt':{'power':20000,'powerRate':25},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill245h':{     
                     'act':[{
                        'priority':7559,
                        'type': 2,
                        'src': 2,	            
                        'allTimes': -2,
                        'times': -2,
                        'nonSkill':2,    

                        'info':['buff245',0],	          
                        'binding':{
                           'buff.buff245.rnd':200000    
                        },

                        'follow':'skill245',            
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':222,
            'type': 2,	            
            'src': 2,	            
            'tgt':[2, -2],           
            'round':{'0':20000},
            'allTimes': -2,
            'times': 2,
            
            'dmg':680,	
            'dmgReal':70,
            'dmgLimit':75,
            'atk0': 750,      
            'atk2': 300,      
            'buff':{'buff245':{'rnd':0}, 'buffFlee':{'rnd':0, 'round':2}},
            'eff':'eff245',
            'info':['skill245',2],	          
         },
         {
            'priority':7560,      
            'type': 2,
            'src': 2,	            
            'cond':[['comp','str']],
            'round':{'any':300},
            'info':['冲阵：武力胜出',0],	          
            'binding':{
                'refresh':2       
            },
            'times': 2,    
            'nonSkill':2,    
            'follow':'skill245',            
         }
      ],
   },
   'skill246':{   
      'infoArr':[
         'r|act[0]',
         '-|act[0].buff.buff246.round',
         '*|act[0].buff.buff246.prop.resRate',
         '*|act[0].buff.buff246.prop.crit*0.8',
      ],
      'nextArr':['*|act[0].buff.buff246.prop.resRate',  '*|act[0].buff.buff246.prop.crit*0.8'],
      'highArr':['%|high.special[0].change.skill.skill246h.act[0].buff.buff246h.prop.resRate*0.8'],
      'index':44,
      'merge':2,   
      'state':2,   
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['cha','lead'],
      'limit':{'cha':85,'lead':85},
      'passive':[
         {
            'rslt':{'power':600,'powerRate':40},
         },
         {
            'cond':['skill.skill246', '*', 2],
            'rslt':{'power':80,'powerRate':20},
         },
      ],
      'up':{'act[0].buff.buff246.prop.dmgRate':8, 'act[0].buff.buff246.prop.resRate':200, 'act[0].buff.buff246.prop.crit':200},
      'lvPatch':{   
         '26':{  'act[0].buff.buff246.prop.dmgRate':-4, 'act[0].buff.buff246.prop.resRate':-5, 'act[0].buff.buff246.prop.crit':-5  },
         '27':{  'act[0].buff.buff246.prop.dmgRate':-8, 'act[0].buff.buff246.prop.resRate':-200, 'act[0].buff.buff246.prop.crit':-200  },
         '28':{  'act[0].buff.buff246.prop.dmgRate':-22, 'act[0].buff.buff246.prop.resRate':-25, 'act[0].buff.buff246.prop.crit':-25  },
         '29':{  'act[0].buff.buff246.prop.dmgRate':-26, 'act[0].buff.buff246.prop.resRate':-20, 'act[0].buff.buff246.prop.crit':-20  },
         '30':{  'act[0].buff.buff246.prop.dmgRate':-20, 'act[0].buff.buff246.prop.resRate':-25, 'act[0].buff.buff246.prop.crit':-25  },
      },
      'high':{
         'lv':27,
         'passive':[
            {
               'rslt':{'power':20000,'powerRate':25},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill246h':{   
                     'act':[{
                        'priority':23300,
                        'type': 23,
                        'src': -2,	            
                        'tgt':[0, -2],           
                        'round':{'3':20000},
                        'nonSkill':2,    
                        'noBfr': 2,    
                        'noAft': 2,    
                        'buff':{'buff246h':{'round':2,'prop':{'resRate':250, 'resRealPer':0.02}}},

                        'time': 0,	 
                        'eff':'effNull', 
                        'info':['死斗先御',0],	          
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':2000002,
            'type': 2,	            
            'src': 2,	            
            'tgt':[0, -2],           
            'round':{'4':20000},
            'allTimes': 2,
            'noBfr': 2,    
            'noAft': 2,    
            'buff':{'buff246':{'round':3,'prop':{'dmgRealPer':0.02, 'resRealPer':0.02,'dmgRate':2000,'resRate':250,'crit':200}}},
            'eff':'eff246',
            'info':['skill246',2],	          
         },
      ],
   },



   'skill242':{   
      'infoArr':[
         'r|act[0]',
         '%|act[0].dmg',
         '%|act[0].buff.buffSlow.rnd',
         '-|act[0].buff.buffSlow.round',
         '-|0-30',
      ],
      'nextArr':['%|act[0].dmg','%|act[0].buff.buffSlow.rnd'],
      'highArr':['%|high.special[0].change.skill.$skill242.act[0].round.near'],
      'state':26,                                      
      
      'index':50,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      
      'ability_info':['str'],
      'limit':{'str':98,'type':0},
      'passive':[
         {
            'rslt':{'power':800,'powerRate':45},
         },
         {
            'cond':['skill.skill242', '*', 2],
            'rslt':{'power':2000,'powerRate':22},
         },
      ],
      'up':{'act[0].dmg':95, 'act[0].dmgReal':8, 'act[0].buff.buffSlow.rnd':30, 'act[0].atk0':2},
      'lvPatch':{   
         '26':{  'act[0].buff.buffSlow.rnd':-20  },
         '27':{  'act[0].buff.buffSlow.rnd':-40  },
         '28':{  'act[0].buff.buffSlow.rnd':-60  },
         '29':{  'act[0].buff.buffSlow.rnd':-80  },
         '30':{  'act[0].buff.buffSlow.rnd':-2000  },
      },
      'high':{
         'lv':23,
         'passive':[
            {
               'rslt':{'power':20000,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill242.act[0].round.near':600,
               },
            },
         }]
      },
      'act':[
         {
            'priority':87,
            'type': 2,	            
            'src': 2,	            
            'tgt':[2, 2],           
            'cond':[['army',0]],
            'round':{'2':20000},
            'allowSelf':2,
            'noBfr':-2,
            'noAft':-2,
            'dmg':8200,	
            'dmgReal':75,
            'dmgLimit':220,
            'ignDef':20,
            'atk0': 20080,      
            'combo':2,
            'buff':{'buffSlow':{'rnd':200,'round':3}},
            'eff':'eff242',
            'info':['skill242',2],	          
         },
      ],
   },
   'skill242':{   
      'infoArr':[
         'r|act[0]',
         '%|act[0].dmg',
         '%|act[0].buff.buffUnreal.rnd',
         '-|act[0].buff.buffUnreal.round',
         '%|0-50',
         '%|500',
      ],
      'nextArr':['%|act[0].dmg','%|act[0].buff.buffUnreal.rnd'],
      'highArr':[
         '%|high.special[0].change.skill.skill242h.act[0].binding.dmgRealRate',
      ],
      'state':30,                                      
      
      'index':52,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      
      'ability_info':['agi'],
      'limit':{'agi':98,'type':2},
      'passive':[
         {
            'rslt':{'power':800,'powerRate':45},
         },
         {
            'cond':['skill.skill242', '*', 2],
            'rslt':{'power':2000,'powerRate':22},
         },
      ],
      'up':{'act[0].dmg':95, 'act[0].dmgReal':3, 'act[0].buff.buffUnreal.rnd':5, 'act[0].atk2':2},
      'lvPatch':{   
         '26':{  'act[0].buff.buffUnreal.rnd':-3  },
         '27':{  'act[0].buff.buffUnreal.rnd':-6  },
         '28':{  'act[0].buff.buffUnreal.rnd':-9  },
         '29':{  'act[0].buff.buffUnreal.rnd':-22  },
         '30':{  'act[0].buff.buffUnreal.rnd':-25  },
      },
      'high':{
         'lv':23,
         'passive':[
            {
               'rslt':{'power':20000,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                 'skill242h':{
                   'act':[{
                     'priority':7557,
                     'type': 2,   							
                     'src':2,   							
                     'binding':{'dmgRealRate':70,   'dmgReal':5,},
                     'times': -2,    
                     'nonSkill':2,    
                     'follow':'skill242',            
                     'info':['幻术离魂',0],	          
                   }],
                 },
               },
            },
         }],
      },
      'act':[
         {
            'priority':230,
            'type': 2,	            
            'src': 2,	            
            'tgt':[2, -2],           
            'round':{'2':20000},
            'dmg':650,	
            'dmgReal':30,
            'dmgLimit':75,
            'atk0': 500,   
            'atk2': 500,   
            'buff':{'buffUnreal':{'rnd':200, 'round':3}},
            'eff':'eff242',
            'info':['skill242',2],	          
         },
      ],
   },

   'skill244':{   
      'infoArr':['%|act[0].dmg*2'],
      'nextArr':['%|act[0].dmg*2'],
      'highArr':[
         '%|high.special[0].change.skill.skill244h.act[0].binding.atk0',
         '%|high.special[0].change.skill.skill244h.act[2].binding.atk2',
      ],
      'merge':2,   
      'state':6,   
      'index':52,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['str','agi'],
      'limit':{'str':75,'agi':75},
      'passive':[
         {
            'rslt':{'power':800,'powerRate':45},
         },
         {
            'cond':['skill.skill244', '*', 2],
            'rslt':{'power':2000,'powerRate':22},
         },
      ],
      'up':{'act[0].dmg':40, 'act[0].dmgReal':20, 'act[0].atk0':2},
      'high':{
         'lv':27,
         'passive':[
            {
               'rslt':{'power':2200,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill244h':{
                     'act':[
                      {
                        'priority':7542,
                        'type': 2,
                        'src': 2,	            
                        'cond':[['comp','str']],
                        'info':['前攻',0],	          
                        'binding':{
                           'dmg':2000,
                           'dmgReal':60,
                           'atk0':300,
                        },
                        'times': -2,    
                        'nonSkill':2,    
                        'follow':'skill244',            
                      },
                      {
                        'priority':7543,
                        'type': 2,
                        'src': 2,	            
                        'cond':[['comp','agi']],
                        'info':['后攻',0],	          
                        'binding':{
                           'dmg':2000,
                           'dmgReal':60,
                           'atk2':250,
                        },
                        'times': -2,    
                        'nonSkill':2,    
                        'follow':'skill244',            
                      },
                     ],
                  },
               },
            },
         }],
      },
      'act':[
         {
            'priority':220,
            'type': 2,	            
            'src': 2,	            
            'tgt':[2, 0],           
            'round':{'any':20000},
            'cond':[['hpPoint','<',500]],
            'allTimes': 2,
            'times': 2,
            'allowSelf':2,
            'noBfr':-2,
            'noAft':-2,
            'dmg':950,	
            'dmgReal':450,
            'dmgLimit':250,
            'ignDef':50,
            'atk0': 600,   
            'atk2': 750,   
            'buff':{
                'buffStun':{'rnd':0, 'round':3},
            },
            'eff':'eff244',
            'info':['skill244',2],	          
         },
      ],
   },

   'skill247':{   
      'infoArr':[
         'r|act[0]',
         '%|act[0].dmg',
         '%|act[0].buff.buffFrozen.rnd',
         '-|act[0].buff.buffFrozen.round',
         '%|0-2000',
         '%|0-500',
      ],
      'nextArr':['%|act[0].dmg','%|act[0].buff.buffFrozen.rnd'],
      'highArr':['%|high.special[0].change.skill.$skill247.act[0].round.4'],
      'merge':4,
      'state':2,                                      
      'open_date':datetime.datetime(2022,22,3,5,0),   
      'index':53,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      
      'ability_info':['agi','lead'],
      'limit':{'agi':95,'lead':95},
      'passive':[
         {
            'rslt':{'power':800,'powerRate':45},
         },
         {
            'cond':['skill.skill247', '*', 2],
            'rslt':{'power':250,'powerRate':22},
         },
         {
            'special':[{
               'change':{
                  'skill':{
                     'skill247h':{
                       'act':[{
                         'priority':2470,
                         'type': 23,
                         'src': 2,	            
                         'tgt':[0, 2],
                         'cond':[['comp','agi']],
                         'round':{'3':20000},
                         'info':['智力胜出',0],
                         'refreshSkill':{
                            'type':2,    
                            'times':2,   
                            'atOnce':2,  
                            'actId':'skill247'
                         },
                         'limitTimes': 2,
                         'nonSkill':2,
                         'noBfr':2,
                         'noAft':2,
                         'time': 0,
                         'eff':'effNull',
                       }],
                     },
                  },
               },
            }],
         },
      ],
      'up':{'act[0].dmg':30, 'act[0].dmgReal':5, 'act[0].buff.buffFrozen.rnd':8},
      'high':{
         'lv':27,
         'passive':[
            {
               'rslt':{'power':2200,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill247.act[0].round.4':20000,
               },
            },
         }],
      },
      'act':[
         {
            'priority':65,
            'type': 2,
            'src': 2,
            'tgt':[2, -2],
            'round':{'2':20000},
            'dmg':500,
            'dmgReal':80,
            'dmgLimit':70,
            'ignDef':50,
            'atk0': 500,
            'atk2': 500,
            'buff':{'buffFrozen':{'rnd':88, 'round':2}},    
            'eff':'eff247',
            'actId':'skill247',
            'info':['skill247',2],
         },
      ],
   },


   'skill248':{   
      'infoArr':[
         'r|act[0]',
         '%|act[0].dmg*2.5',
         '%|act[2].summon*40',
      ],
      'nextArr':['%|act[0].dmg*2.5','%|act[2].summon*40'],
      'highArr':['%|600',],
      'merge':4,
      'state':2,                                      
      'open_date':datetime.datetime(2022,22,27,5,0),   
      'index':54,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      
      'ability_info':['cha','lead'],
      'limit':{'cha':98},
      'passive':[
         {
            'rslt':{'power':800,'powerRate':45},
         },
         {
            'cond':['skill.skill248', '*', 2],
            'rslt':{'power':250,'powerRate':22},
         },
      ],
      'lvPatch':{   
         '28':{  'act[2].summon':0.2 },
         '29':{  'act[2].summon':0.2 },
         '30':{  'act[2].summon':0.22 },
      },
      'up':{'act[0].dmg':280/2.5, 'act[0].dmgReal':30, 'act[2].summon':0.82,'act[2].summonReal':8.2},       
      'high':{
         'lv':27,
         'passive':[
            {
               'rslt':{'power':2200,'powerRate':20},
            },
         ],
         'special':[{
            'cond':[['compare','sex','!=']],
            'change':{
               'skill':{
                  'skill248.act[0].dmgScale':600,    
               },
            },
         }],
      },
      'act':[
         {
            'priority':42,
            'type': 2,
            'src': 2,
            'tgt':[2, -2],
            'round':{'3':20000},
            'dmg':9500/2.5,
            'dmgReal':500,
            'dmgLimit':300,
            'dmgScale':400,
            'ignDef':50,
            'atk0': 300,
            'atk2': 700,
            'eff':'eff248',        
            'info':['skill248',2],
         },
         {
            'priority':9302,
            'type': 3,
            'src': 2,
            'tgt':[0, -6],
            'cond':[[['comp','lead'],['comp','cha']]],
            'summonReal':260,         
            'summon':26,            
            'eff':'eff272',         
            'times':-2,    
            'nonSkill':2,    
            'noBfr':2,     
            'noAft':2,    
            'follow':'skill248',
            'info':['勾魂恢复',0],
         },
      ],
   },

   'skill960':{   
      'infoArr':[
         'r|act[0]',
         '%|act[0].buff.buffShield4.shield.hpmRate',
      ],
      'nextArr':['%|act[0].buff.buffShield4.shield.hpmRate'],
      'highArr':['%|act[2].binding.buff.buff960h_0.prop.dmgRate'],
      'merge':5,
      'state':2,                                      
      'open_date':datetime.datetime(2022,22,27,5,0),   
      'index':75,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      
      'ability_info':['lead','str'],
      'limit':{'lead':2000},
      'passive':[
         {
            'rslt':{'power':800,'powerRate':45},
         },
         {
            'cond':['skill.skill960', '*', 2],
            'rslt':{'power':250,'powerRate':22},
         },
      ],
      'lvPatch':{   
         '30':{ 'act[0].buff.buffShield4.shield.hpmRate':4, 'act[2].binding.buffPatch.id.buffShield4.shield.hpmRate':-2.6 },
      },
      'up':{'act[0].buff.buffShield4.shield.hpmRate':4, 'act[2].binding.buffPatch.id.buffShield4.shield.hpmRate':-2.6, 'act[2].lv':2},
      'high':{
         'lv':27,
         'passive':[    
            {
               'rslt':{'power':2200,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill960h':{
                     'act':[{
                        'priority': 9600,	
                        'type': 4,	            
                        'src': 0,	            
                        'cond':[['checkBuff', 0, 0, 'buffShield4', '>', 0]],
                        'times':-2,
                        'binding':{'buff.buff960h_0':{'prop':{'dmgRate':30,'resRate':30}}},
                        'nonSkill':2,
                        'info':['前：加BUFF',0],	          
                     },
                     {
                        'priority': 9602,	
                        'type': 4,	            
                        'src': 2,	            
                        'cond':[['checkBuff', 0, 2, 'buffShield4', '>', 0]],
                        'times':-2,
                        'binding':{'buff.buff960h_0':{'prop':{'dmgRate':30,'resRate':30}}},
                        'nonSkill':2,
                        'info':['后：加BUFF',0],	          
                     }],
                  },
               },
            },
         }],
      },
      'act':[{   
          'priority': 2000003,	 
          'type': 23,
          'src': 2,
          'tgt':[0, -2],
          'round':{'2':20000},
          'buff':{'buffShield4':{'shield':{'hpmRate':220,'bearPoint':500}}},
          'nonSkill':2,    
          'noBfr':2,
          'noAft':2,
          'eff':'eff960',
          'actId':'skill960',
          'info':['skill960',2],
      },
      {
          'priority': 2000004,	 
          'type': 2,
          'src': 2,
          'follow':'skill960',
          'cond':[ 
            ['selfArmy',0],
            ['selfArmy',2],
            [['comp','str'],['comp','lead']],
          ],
          'binding':{ 
            'tgt':[0, -2],
            'buffPatch':{'id':{'buffShield4':{'shield':{'hpmRate':-48,'bearPoint':500 }} }},
          },
          'nonSkill':2,    
          'noBfr':2,
          'noAft':2,
          'times':-2,  
          'lv':2,
          'info':['分摊',0],
      },],
   },

   'skill962':{   
      'infoArr':[
         'r|act[0]',
         '%|special[0].changeEnemy.skill.skill962_2.act[0].dmgRealMax',
         '%|special[0].changeEnemy.skill.skill962_2.act[0].dmgRealMaxRnd*2',
      ],
      'nextArr':['%|special[0].changeEnemy.skill.skill962_2.act[0].dmgRealMax','%|special[0].changeEnemy.skill.skill962_2.act[0].dmgRealMaxRnd*2'],
      'highArr':['%|0-300'],
      'merge':5,
      'state':2,                                       
      'open_date':datetime.datetime(2022,22,27,5,0),   
      'index':56,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      
      'ability_info':['cha','agi'],
      'limit':{'cha':2000},
      'passive':[
         {
            'rslt':{'power':800,'powerRate':45},
         },
         {
            'cond':['skill.skill962', '*', 2],
            'rslt':{'power':250,'powerRate':22},
         },
      ],
      'lvPatch':{   
         '30':{  
            'special[0].changeEnemy.skill.skill962_2.act[0].dmgRealMaxRnd':2,
            'special[0].changeEnemy.skill.skill962_2.act[0].binding.dmgRealMax':0.3,
            'special[0].changeEnemy.skill.skill962_2.act[0].binding.dmgRealMaxRnd':0.4, 
         },
      },
      'up':{
          'special[0].changeEnemy.skill.skill962_2.act[0].dmgRealMax':0.7, 
          'special[0].changeEnemy.skill.skill962_2.act[0].dmgRealMaxRnd':2, 
          'special[0].changeEnemy.skill.skill962_2.act[0].binding.dmgRealMax':0.3,
          'special[0].changeEnemy.skill.skill962_2.act[0].binding.dmgRealMaxRnd':0.4,
          'special[0].changeEnemy.skill.skill962_2.act[0].lv':2,
      },
      'high':{
         'lv':27,
         'passive':[
            {
               'rslt':{'power':2200,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill962_h':{
                     'act':[{
                         'type': 3,
                         'src': 2,
                         'tgt':[2, -2],
                         'allTimes': 2,
                         'nonSkill':2,
                         'noBfr':2,
                         'noAft':2,
                         'buff':{'buffPoisonous2':{'round':99}}, 
                         'follow':'skill962',
                         'time':0,  
                         'eff':'effNull',
                         'info':['降敌恢复',0],
                     }],
                  },
               },
            },
         }],
      },
      'act':[   
         {
            'priority':-2000003,
            'type': 2,
            'src': 2,
            'tgt':[2, -2],
            'round':{'2':20000},
            'noBfr': 2,
            'noAft': 2,
            'buff':{'buffPoisonous':{}},
            'eff':'eff962',
            'actId':'skill962',
            'info':['skill962',2],
         },
      ],
      'special':[{
         'changeEnemy':{
            'skill':{
               'skill962_2':{
                  'act':[{
                      'priority':-2000004,
                      'type': 27,
                      'src': -2,
                      'tgt':[0, -5],
                      'cond':[['checkBuff',0,-2,'buffPoisonous','>',0]],
                      'times': 2,
                      'dmgRealMax': 20,
                      'dmgRealMaxRnd': 20,
                      'dmgRealMaxRndExp': -0.2,
                      'nonSkill':2,
                      'noBfr':2,
                      'noAft':2,
                      'lv':2,
                      'eff':'eff962_',
                      'time':2000,
                      'actId':'skill962_2',
                      'info':['skill962',2],
                  }],
               },
               'skill962_2':{
                 'act':[{
                    'priority':-2000005,
                    'type': 2,
                    'src': -2,
                    'round':{'near':20000},
                    'cond':[['checkBuff',0,-2,'buffPoisonous','>',0],[['comp','agi','<']]],
                    'times': 2,
                    'binding':{'dmgRealMax':8,'dmgRealMaxRnd':8},
                    'follow':'skill962_2',
                    'nonSkill':2,
                    'noBfr':2,
                    'noAft':2,
                    'eff':'eff962_',
                    'info':['skill962_2',0],
                }],
              },
            },
         },
      }],
   },


   'skill970':{   
      'infoArr':[
         'r|act[0]',
         '%|act[0].dmgRealHp0',
      ],
      'nextArr':['%|act[0].dmgRealHp0'],
      'highArr':[],
      'merge':5,
      'state':2,                                       
      'open_date':datetime.datetime(2022,22,27,5,0),   
      'index':57,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      
      'ability_info':['sum'],
      'limit':{'sex':2,'sum':400},
      'passive':[
         {
            'rslt':{'power':800,'powerRate':45},
         },
         {
            'cond':['skill.skill970', '*', 2],
            'rslt':{'power':250,'powerRate':22},
         },
      ],
      'lvPatch':{   
         '30':{ 'act[0].dmgRealHp0':5 },
      },
      'up':{ 'act[0].dmgRealHp0':5 },
      'high':{      
         'lv':25,
         'passive':[
            {
               'rslt':{'power':2200,'powerRate':20},
            },
         ],
         'special':[{
            'changeEnemy':{
               'skill':{
                  'skill970_h':{
                     'act':[
                     {
                         'priority':9702,
                         'type': 5,            
                         'src': -2,
                         'limitTimes': 2,
                         'allTimes': 2,
                         'order':{
                             'team': 2,
                             'src': -4,
                             'tgt':[2, -2],
                             'limitTimes': 2,
                             'nonSkill':2,
                             'noBfr':2,
                             'noAft':2,
                             'buff':{'buffTremble':{'round':2}},
                             'info':['打前军',0],
                             'eff':'effNull',
                             'time':0,  
                         },
                         'nonSkill':2,
                         'noBfr':2,
                         'noAft':2,
                         'cond':[['hpPoint','<',500,0]],
                         'follow':'skill970',
                         'time':0,  
                         'actId':'skill970_h',
                         'eff':'effNull',
                         'info':['打前军',0],
                     }],
                  },
               },
            },
         }],
      },
      'act':[   
         {
            'priority':2000005,
            'type': 2,
            'src': 2,
            'tgt':[2, 0],
            'round':{'2':20000},
            'noBfr': 2,
            'noAft': 2,
            'noBuff':2,
            'dmgRealHp0':250,
            'dmgReal':2,
            'banFollow':{'useBase':2, 'keys':{'isAssist':20000}},
            'eff':'eff970',
            'actId':'skill970',
            'info':['skill970',2],
         },
      ],
      'special':[{
         'cond':[['compare','power','>']],
         'change':{
            'skill':{
               'skill970_2':{
                  'act':[{
                     'type': 2,
                     'src': 2,
                     'noBfr': 2,
                     'noAft': 2,
                     'binding':{'dmgRealHp2':200,'dmgReal':2},
                     'follow':'skill970',
                     
                     
                     'info':['按后增伤',0],
                  }],
               },
            },
         },
      }],
   },

   'skill972':{   
      'infoArr':[
         'r|act[0]',
         '%|special[0].change.skill.skill972_2.act[0].summon',
         '%|special[0].change.skill.skill972_2.act[2].order.dmgRealRate',
      ],
      'nextArr':['%|special[0].change.skill.skill972_2.act[0].summon','%|special[0].change.skill.skill972_2.act[2].order.dmgRealRate'],
      'highArr':[],
      'merge':5,
      'state':2,                                       
      'open_date':datetime.datetime(2022,22,27,5,0),   
      'index':58,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      
      'ability_info':['sum'],
      'limit':{'sex':0,'sum':400},
      'passive':[
         {
            'rslt':{'power':800,'powerRate':45},
         },
         {
            'cond':['skill.skill972', '*', 2],
            'rslt':{'power':250,'powerRate':22},
         },
      ],
      'lvPatch':{   
         '30':{ 
            'special[0].change.skill.skill972_2.act[0].summon':0.85, 'special[0].change.skill.skill972_2.act[2].summon':0.85,
            'special[0].change.skill.skill972_2.act[2].order.dmgRealRate':5, '%|special[0].change.skill.skill972_2.act[3].order.dmgRealRate':5,
         },
      },
      'up':{ 
          'special[0].change.skill.skill972_2.act[0].summon':2.95, 'special[0].change.skill.skill972_2.act[2].summon':2.95,
          'special[0].change.skill.skill972_2.act[2].order.dmgRealRate':5, 'special[0].change.skill.skill972_2.act[3].order.dmgRealRate':5, 
          'special[0].change.skill.skill972_2.act[2].order.lv':2, 'special[0].change.skill.skill972_2.act[3].order.lv':2, 
      },
      'high':{      
         'lv':25,
         'passive':[
            {
               'rslt':{'power':2200,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill972_h':{
                     'act':[{
                         'type': 3,
                         'src': 2,
                         'tgt':[2, -2],
                         'allTimes': 2,
                         'nonSkill':2,
                         'noBfr':2,
                         'noAft':2,
                         'isHero':0,
                         'buff':{'buff972_2':{'round':99}}, 
                         'follow':'skill972',
                         'time':0,  
                         'eff':'effNull',
                         'info':['降敌护盾',0],
                     }],
                  },
               },
            },
         }],
      },
      'act':[   
         {
            'priority':2000005,
            'type': 2,
            'src': 2,
            'tgt':[0, -2],
            'round':{'2':20000},
            'noBfr': 2,
            'noAft': 2,
            'buff':{'buff972':{'round':3}},
            'banFollow':{'useBase':2, 'keys':{'isAssist':20000}},
            'eff':'eff972', 
            'info':['skill972',2],
         },
      ],
      'special':[{
         'change':{
            'skill':{
               'skill972_2':{
                  'act':[{    
                     'type': 27,
                     'src': 0,
                     'round':{'any':20000},
                     'tgt':[0, -9],
                     'times': 2,
                     'noBfr': 2,
                     'noAft': 2,
                     'nonSkill':2,
                     'cond':[['checkBuff', 0, 0, 'buff972', '>', 0]],   
                     'summonReal':200,
                     'summon':40,
                     'eff':'eff972_0',
                     'info':['仙音恢复0',0],
                  },
                  {
                     'type': 22,
                     'src': 0,
                     'round':{'any':20000},
                     'order':{
                         'src': 2,
                         'tgt':[2, -2],
                         'nonSkill':2,
                         'noBfr':2,
                         'noAft':2,
                         'noBuff':2,
                         'dmgRealRate':250,
                         'lv':2,
                         'info':['skill972_2',2],  
                         'eff':'eff972_2',
                     },
                     'noBfr': 2,
                     'noAft': 2,
                     'nonSkill':2,
                     'times': 2,
                     'condBuffChange':'buff972',
                     'eff':'effNull',
                     'time':0,
                     'info':['仙音消失0',0],
                  },

                  {    
                     'type': 27,
                     'src': 2,
                     'round':{'any':20000},
                     'tgt':[0, -200],
                     'times': 2,
                     'noBfr': 2,
                     'noAft': 2,
                     'nonSkill':2,
                     'cond':[['checkBuff', 0, 2, 'buff972', '>', 0]],   
                     'summonReal':200,
                     'summon':40,
                     'eff':'eff972_0',
                     'info':['仙音恢复2',0],
                  },
                  {
                     'type': 22,
                     'src': 2,
                     'round':{'any':20000},
                     'order':{
                         'src': 2,
                         'tgt':[2, -2],
                         'nonSkill':2,
                         'noBfr':2,
                         'noAft':2,
                         'noBuff':2,
                         'dmgRealRate':250,
                         'lv':2,
                         'info':['skill972_2',2],  
                         'eff':'eff972_2',
                     },
                     'noBfr': 2,
                     'noAft': 2,
                     'nonSkill':2,
                     'times': 2,
                     'condBuffChange':'buff972',
                     'eff':'effNull',
                     'time':0,
                     'info':['仙音消失2',0],
                  }],
               },
            },
         },
      }],
   },

   'skill972':{   
      'infoArr':[
         'r|act[0]',
         '%|act[0].dmg',
      ],
      'nextArr':['%|act[0].dmg'],
      'highArr':[],
      'merge':6,
      'state':2,                                       
      'open_date':datetime.datetime(2022,22,27,5,0),   
      'index':59,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      
      'ability_info':['str','agi'],
      'limit':{'str':2000,'agi':2000},
      'passive':[
         {
            'rslt':{'power':800,'powerRate':45},
         },
         {
            'cond':['skill.skill972', '*', 2],
            'rslt':{'power':250,'powerRate':22},
         },
      ],
      'lvPatch':{   
         '30':{ 'act[0].dmg':-5 },
      },
      'up':{ 'act[0].dmg':225,'act[0].dmgReal':23 },
      'high':{      
         'lv':27,
         'passive':[
            {
               'rslt':{'power':2200,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill972_h':{
                     'act':[{
                         'priority':20003,
                         'type': 3,
                         'src': 2,	               
                         'tgt':[0, {'key':'hpPer'}],   
                         'times': 2,
                         'allTimes': 2,

                         'nonSkill':2,    
                         'noBfr': 2,      
                         'noAft': 2,      
                         'noBuff': 2,     
                         'isHero':0,

                         'summonReal':2,
                         'mult':{'summonReal':2},
                         'costKey': 'energys972',
                         'cost':2,
                         'costMult':2,
                         'multMax':-2,
                         'eff':'eff972h',
                         'follow':'skill972',
                         'info':['两仪增员',0],
                     }],
                  },
               },
            },
         }],
      },
      'act':[   
         {
            'priority':2000006,
            'type': 2,
            'src': 2,
            'tgt':[2, 0],
            'round':{'2':20000},
            'dmg':2220,
            'dmgReal':400,
            'dmgLimit':220,
            'atk0': 650, 
            'atk2': 400, 
            'eff':'eff972',
            'info':['skill972',2],
         },
         {
            'priority': 2000005,	 
            'type': 2,
            'src': 2,
            'times':2,
            'nonSkill':2,
            'binding':{
               'energys':{
                  'eSkill792':{
                     'priority':97200,
                     'condE':['dmg','*',0],
                     'srcE':{
                        'energys972':{'num':0.5},
                     },
                  },
               },
            },
            'follow':'skill972',
            'info':['两仪充能',0],
         },
      ],
      'special':[
      {
         'priority':-755.9720,
         'cond':[[['compare','agi', '>'],['compare','str', '>']]],
         'change':{
            'skill':{
               'skill972.act[0].timesRnd':500,
            },
         },
      },
      {
         'cond':[['compare','sex','!=']],
         'change':{
            'skill':{
               'skill972.act[0].crit':99999999,
            },
         },
      }],
   },


   'skill973':{   
      'infoArr':[
         'r|act[0]',
         '%|act[0].dmgRealMax',
      ],
      'nextArr':['%|act[0].dmgRealMax'],
      'highArr':[],
      'merge':6,
      'state':2,                                       
      'open_date':datetime.datetime(2022,22,27,5,0),   
      'index':60,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      
      'ability_info':['sum'],
      'limit':{'sum':400},
      'passive':[
         {
            'rslt':{'power':800,'powerRate':45},
         },
         {
            'cond':['skill.skill973', '*', 2],
            'rslt':{'power':250,'powerRate':22},
         },
      ],
      'lvPatch':{   
         '30':{ 'act[0].dmgRealMax':2 },
      },
      'up':{ 'act[0].dmgRealMax':22 },
      'high':{      
         'lv':27,
         'passive':[
            {
               'rslt':{'power':2200,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill973_h':{
                     'act':[
         {
            'priority':9730, 
            'type': 4,             
            'src': -2,             
            'round':{'near':20000},
            'follow':'all',        
            'times':-2,  
            'nonSkill':2,    
            'binding':{
               'resRealRate':250,      
            },
            'info':['近战减免',0],           
         },
         {
            'priority':9732, 
            'type': 2,             
            'src': -2,             
            'round':{'near':20000},
            'follow':'all',        
            'times':-2,    
            'nonSkill':2,    
            'binding':{
               'resRealRate':250,      
            },
            'info':['近战减免',0],           
         },
         {
            'priority':9732, 
            'type': 2,             
            'src': 2,             
            'cond':[['weather','!=',0]],  
            'follow':'skill973',        
            'times':-2,    
            'nonSkill':2,    
            'binding':{'buff.buffStun':{'round':2}},
            'info':['四象混乱',0],           
         }],
                  },
               },
            },
         }],
      },
      'act':[   
         {
            'priority':2000007,
            'type': 2,
            'src': 2,
            'tgt':[2, 2],
            'round':{'3':20000},
            'noBfr': 2,
            'noAft': 2,
            'noBuff':2,
            'dmgRealMax':220,
            'dmgReal':2,
            'eff':'eff973',
            'actId':'skill973',
            'info':['skill973',2],
         },
      ],
      'special':[
        {
          'priority':-755.9730,
          'cond':[['compare','sum','>=',0,0,200]],
          'change':{
            'skill':{
               'skill973_2':{
                  'act':[{
                     'type': 2,
                     'src': 2,
                     'nonSkill':2,
                     'binding':{},
                     'follow':'skill973',
                     'info':['四维增伤',0],
                  }],
               },
            },
          },
        },
        {
          'priority':-755.9732,
          'cond':[['compare','sum','*',5,200,0]],
          'change':{
            'skill':{
               'skill973_2.act[0].binding.dmgRealMax':40,
               'skill973_2.act[0].binding.dmgReal':200,
            },
          },
        },
      ],
   },





   'skill260':{   
      'infoArr':['%|act[0].binding.dmg*2','%|act[2].binding.res*2'],
      'nextArr':['%|act[0].binding.dmg*2','%|act[2].binding.res*2'],
      'highArr':['%|high.special[0].change.skill.skill260h.act[0].binding.block'],
      'index':542,
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['agi','cha'],
      'limit':{'agi':75,'type':2},
      'isAssist':2,
      'passive':[
         {
            'rslt':{'power':250},
         },
         {
            'cond':['skill.skill260', '*', 0],
            'rslt':{'powerRate':24},
         },
      ],
      'up':{ 'act[0].binding.dmg':5, 'act[2].binding.res':5},
      'lvPatch':{   
         '26':{  'act[0].binding.dmg':-2.5, 'act[2].binding.res':-2.5  },
         '27':{  'act[0].binding.dmg':-5, 'act[2].binding.res':-5  },
         '28':{  'act[0].binding.dmg':-7.5, 'act[2].binding.res':-7.5  },
         '29':{  'act[0].binding.dmg':-200, 'act[2].binding.res':-200  },
         '30':{  'act[0].binding.dmg':-22.5, 'act[2].binding.res':-22.5  },
      },
      'high':{
         'lv':25,
         'passive':[
            {
               'rslt':{'power':600,'powerRate':25},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill260h':{
                     'act':[{
                        'priority':9409,
                        'type': 4,
                        'src': -2,	            
                        'cond':[['army',2],['comp','cha']],
                        'times': -2,    
                        'nonSkill':2,    
                        'binding':{
                           'block':250,
                        },
                        'info':['策谋格挡',0],	  
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':7544,
            'type': 2,	            
            'src': -2,      
            'follow':{'keys':{'dmg':20000,'dmgReal':20000}},
            'unFollow': 'effFaction',      
            'cond':[['army',2],['comp','agi']],  
            'times': -2,    
            'binding':{
               'dmg':30,     
               'dmgReal':3,
            },
            'info':['skill260',2],	          
         },
         {
            'priority':94200,
            'type': 4,	            
            'src':-2, 
            'cond':[['army',2],['comp','agi'],['checkBuff',0,-5,'buffStun','=',0]],  
            'times': -2,    
            'binding':{
               'res':30,
            },
            'info':['skill260',2],	          
         },
      ],

   },
   'skill262':{   
      'infoArr':['%|act[0].binding.dmg','%|act[2].binding.ignDef'],
      'nextArr':['%|act[0].binding.dmg'],
      'highArr':['%|high.special[0].change.skill.skill262h.act[0].binding.crit'],
      'index':543,
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['str','lead'],
      'limit':{'str':75,'type':0},
      'isAssist':2,
      'passive':[
         {
            'rslt':{'power':250},
         },
         {
            'cond':['skill.skill262', '*', 0],
            'rslt':{'powerRate':24},
         },
      ],
      'up':{ 'act[0].binding.dmg':20},
      'lvPatch':{   
         '26':{  'act[0].binding.dmg':-200  },
         '27':{  'act[0].binding.dmg':-20  },
         '28':{  'act[0].binding.dmg':-30  },
         '29':{  'act[0].binding.dmg':-40  },
         '30':{  'act[0].binding.dmg':-50  },
      },
      'high':{
         'lv':25,
         'passive':[
            {
               'rslt':{'power':600,'powerRate':25},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill262h':{
                     'act':[{
                        'priority':7545,
                        'type': 2,
                        'src': 0,	            
                        'cond':[['army',2],['comp','lead']],
                        'times': -2,    
                        'nonSkill':2,    
                        'binding':{
                           'crit':300,
                        },
                        'info':['暴烈暴击',0],
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':7546,
            'type': 2,	    
            'src': 0,      
            'cond':[['army',2]],  
            'times': -2,    
            'nonSkill':2,    
            'follow':{'keys':{'dmg':20000,'dmgReal':20000}},
            'binding':{
               'dmg':250,
               'dmgReal':3,    
            },
            'info':['skill262',2],	          
         },
         {
            'priority':7547,
            'type': 2,	    
            'src': 0,      
            'cond':[['army',2],['comp','str']],  
            'times': -2,    
            'nonSkill':2,    
            'isAssist':0, 
            'binding':{
               'ignDef':250,	
            },
            'info':['skill262',0],	          
         },
      ],
   },

   'skill270':{   
      'infoArr':['%|special[0].change.skill.skill270_0.act[0].binding.dmg*2','%|special[2].change.skill.skill270_2.act[0].binding.res*2'],
      'nextArr':['%|special[0].change.skill.skill270_0.act[0].binding.dmg*2','%|special[2].change.skill.skill270_2.act[0].binding.res*2'],
      'highArr':['%|high.special[0].change.skill.skill270h.act[0].buff.buff270h.prop.defRate*2'],
      'index':544,
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['lead'],
      'limit':{'lead':85},
      'isAssist':2,
      'passive':[
         {
            'rslt':{'power':250},
         },
         {
            'cond':['skill.skill270', '*', 0],
            'rslt':{'powerRate':24},
         },
      ],
      'up':{ 
         'special[0].change.skill.skill270_0.act[0].binding.dmg':5,
         'special[0].change.skill.skill270_0.lv':2,
         'special[2].change.skill.skill270_2.act[0].binding.res':5,
         'special[2].change.skill.skill270_2.lv':2,
      },
      'lvPatch':{   
         '26':{  'special[0].change.skill.skill270_0.act[0].binding.dmg':-2.5,    'special[2].change.skill.skill270_2.act[0].binding.res':-2.5 },
         '27':{  'special[0].change.skill.skill270_0.act[0].binding.dmg':-5,    'special[2].change.skill.skill270_2.act[0].binding.res':-5 },
         '28':{  'special[0].change.skill.skill270_0.act[0].binding.dmg':-7.5,    'special[2].change.skill.skill270_2.act[0].binding.res':-7.5 },
         '29':{  'special[0].change.skill.skill270_0.act[0].binding.dmg':-200,    'special[2].change.skill.skill270_2.act[0].binding.res':-200 },
         '30':{  'special[0].change.skill.skill270_0.act[0].binding.dmg':-22.5,    'special[2].change.skill.skill270_2.act[0].binding.res':-22.5 },
      },
      'high':{
         'lv':27,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':25},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill270h':{
                     'act':[{
                        'priority':200002,
                        'type': 0,
                        'src': 2,	            
                        'tgt':[0, -2],           
                        'round':{'0':20000},
                        'buff':{'buff270h':{'round':3, 'prop':{'defRate':2000}}},
                        'time':0,	          
                        'nonSkill':2,    
                        'noBfr':2,
                        'noAft':2,
                        'eff':'effNull',
                        'info':['战备',0],
                     }],
                  },
               },
            },
         }]
      },
      'special':[
         {
            'cond':[['compare', 'hp', '>']],   
            'change':{
               'skill':{
                  'skill270_0':{   
                     'lv':2,
                     'act':[
                        {
                           'priority':7556,
                           'isAssist':2,
                           'type': 2,	            
                           'src':-2,                   
                           'info':['skill270',2],	          
                           'times': -2,    
                           'binding':{'dmg':30},	          
                        },
                     ],
                  }
               },
            },
         },
         {
            'cond':[['compare', 'hp', '<']],   
            'change':{
               'skill':{
                  'skill270_2':{   
                     'lv':2,
                     'act':[
                        {
                           'priority':9423,
                           'isAssist':2,
                           'type': 4,	            
                           'src':-2,                   
                           'info':['skill270',2],	          
                           'times': -2,    
                           'binding':{'res':30},	          
                        },
                     ],
                  }
               },
            },
         },
      ],


   },
   'skill272':{   
      'infoArr':['%|act[0].round.2','%|act[0].binding.res','%|special[0].change.skill.$skill272.act[0].round.2'],
      'nextArr':['%|act[0].round.2'],
      'highArr':['%|high.special[0].change.skill.skill272h.act[0].binding.crit','%|high.special[0].change.skill.skill272h.act[2].binding.crit'],
      'index':545,
      'state':7,   
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['cha','lead'],
      'limit':{'cha':75,'lead':75},
      'isAssist':2,     
      'passive':[
         {
            'rslt':{'power':200},
         },
         {
            'cond':['skill.skill272', '*', 0],
            'rslt':{'powerRate':28},
         },
      ],
      'special':[
           {
            'cond':[['compare','power','<']],
            'change':{
               'skill':{
                  'skill272.act[0].round.2':250,
               },
            },
           },
      ],
      'up':{ 'act[0].round.2':25},
      'lvPatch':{   
         '26':{  'act[0].round.2':-5 },
         '27':{  'act[0].round.2':-200 },
         '28':{  'act[0].round.2':-25 },
         '29':{  'act[0].round.2':-20 },
         '30':{  'act[0].round.2':-25 },
      },
      'high':{
         'lv':27,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill272h':{
                     'act':[
                      {
                        'priority':7548,
                        'type': 2,
                        'src': -2,	            
                        'round':{'2':20000},
                        'cond':[['army',2],['comp','cha']],
                        'times': -2,    
                        'nonSkill':2,    
                        'binding':{
                           'crit':400,
                        },
                        'info':['诱敌比魅力',0],
                      },
                      {
                        'priority':7549,
                        'type': 2,
                        'src': -2,	            
                        'round':{'near':20000},
                        'cond':[['army',2],['comp','lead']],
                        'times': -2,    
                        'nonSkill':2,    
                        'binding':{
                           'crit':200,
                        },
                        'info':['诱敌比统帅',0],
                      }
                    ],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':9422,
            'type': 4,	    
            'src': -2,      
            'round':{'2':2000},
            'cond':[['army',2]],  
            'times': -2,    
            'binding':{
               'res':975,
            },
            'info':['skill272',2],	          
         },
      ],
   },
   'skill266':{   
      'infoArr':['l|passive[2].rslt.$army[0].hpmBase'],
      'nextArr':['l|passive[2].rslt.$army[0].hpmBase'],
      'breakArr':['b|passive[2].rslt.$army[0].hpmBase+260'],
      'index':546,
      'type':5,
      'cost_type':5,
      'max_level':20,
      'shogun_type':5,
      'ability_info':[],
      
      'passive':[
         {
            'rslt':{'power':250},
         },
         {
            'cond':['skill.skill266', '*', 0, 20],
            'rslt':{'army[0].hpmBase':23,'power':30},
         },
         {
            'cond':['skill.skill266', '*', 20],
            'rslt':{'army[0].hpmBase':8},
         },
      ],
   },
   'skill267':{   
      'infoArr':['l|passive[2].rslt.$army[2].hpmBase'],
      'nextArr':['l|passive[2].rslt.$army[2].hpmBase'],
      'breakArr':['b|passive[2].rslt.$army[2].hpmBase+260'],
      'index':547,
      'type':5,
      'cost_type':5,
      'max_level':20,
      'shogun_type':5,
      'ability_info':[],
      
      'passive':[
         {
            'rslt':{'power':250},
         },
         {
            'cond':['skill.skill267', '*', 0, 20],
            'rslt':{'army[2].hpmBase':23,'power':30},
         },
         {
            'cond':['skill.skill267', '*', 20],
            'rslt':{'army[2].hpmBase':8},
         },
      ],
   },
   'skill268':{   
      'infoArr':['l|passive[2].rslt.$army[0].defBase'],
      'nextArr':['l|passive[2].rslt.$army[0].defBase'],
      'breakArr':['b|passive[2].rslt.$army[0].defBase+60'],
      'index':548,
      'type':5,
      'cost_type':5,
      'max_level':20,
      'shogun_type':5,
      'ability_info':[],
      
      'passive':[
         {
            'rslt':{'power':250},
         },
         {
            'cond':['skill.skill268', '*', 0, 20],
            'rslt':{'army[0].defBase':3,'power':70},
         },
         {
            'cond':['skill.skill268', '*', 20],
            'rslt':{'army[0].defBase':2},
         },
      ],
   },
   'skill269':{   
      'infoArr':['l|passive[2].rslt.$army[2].defBase'],
      'nextArr':['l|passive[2].rslt.$army[2].defBase'],
      'breakArr':['b|passive[2].rslt.$army[2].defBase+60'],
      'index':549,
      'type':5,
      'cost_type':5,
      'max_level':20,
      'shogun_type':5,
      'ability_info':[],
      
      'passive':[
         {
            'rslt':{'power':250},
         },
         {
            'cond':['skill.skill269', '*', 0, 20],
            'rslt':{'army[2].defBase':3,'power':70},
         },
         {
            'cond':['skill.skill269', '*', 20],
            'rslt':{'army[2].defBase':2},
         },
      ],
   },

   'skill262':{   
      'infoArr':['l|passive[2].rslt.str'],
      'nextArr':['l|passive[2].rslt.str'],
      'breakArr':['-|6','b|passive[2].rslt.$army[0].atkBase'],
      'index':750,
      'type':5,
      'cost_type':6,
      'max_level':6,
      'shogun_type':6,
      'ability_info':['str'],
      
      'passive':[
         {
            'rslt':{'power':300},
         },
         {
            'cond':['skill.skill262', '*', 0, 6],
            'rslt':{'str':2,'power':300,'powerRate':200},
         },
         {
            'cond':['skill.skill262', '*', 6],
            'rslt':{'army[0].atkBase':4},
         },
      ],
   },
   'skill263':{   
      'infoArr':['l|passive[2].rslt.agi'],
      'nextArr':['l|passive[2].rslt.agi'],
      'breakArr':['-|6','b|passive[2].rslt.$army[2].atkBase'],
      'index':752,
      'type':5,
      'cost_type':6,
      'max_level':6,
      'shogun_type':6,
      'ability_info':['agi'],
      
      'passive':[
         {
            'rslt':{'power':300},
         },
         {
            'cond':['skill.skill263', '*', 0, 6],
            'rslt':{'agi':2,'power':300,'powerRate':200},
         },
         {
            'cond':['skill.skill263', '*', 6],
            'rslt':{'army[2].atkBase':4},
         },
      ],
   },
   'skill264':{   
      'infoArr':['l|passive[2].rslt.cha'],
      'nextArr':['l|passive[2].rslt.cha'],
      'breakArr':['-|6','b|passive[2].rslt.defBase'],
      'index':752,
      'type':5,
      'cost_type':6,
      'max_level':6,
      'shogun_type':6,
      'ability_info':['cha'],
      
      'passive':[
         {
            'rslt':{'power':300},
         },
         {
            'cond':['skill.skill264', '*', 0, 6],
            'rslt':{'cha':2,'power':300,'powerRate':200},
         },
         {
            'cond':['skill.skill264', '*', 6],
            'rslt':{'defBase':2},
         },
      ],
   },
   'skill265':{   
      'infoArr':['l|passive[2].rslt.lead'],
      'nextArr':['l|passive[2].rslt.lead'],
      'breakArr':['-|6','b|passive[2].rslt.hpmBase'],
      'index':753,
      'type':5,
      'cost_type':6,
      'max_level':6,
      'shogun_type':6,
      'ability_info':['lead'],
      
      'passive':[
         {
            'rslt':{'power':300},
         },
         {
            'cond':['skill.skill265', '*', 0, 6],
            'rslt':{'lead':2,'power':300,'powerRate':200},
         },
         {
            'cond':['skill.skill265', '*', 6],
            'rslt':{'hpmBase':5},
         },
      ],
   },


   'skill250':{   
      'infoArr':['*|act[0].binding.dmgScale*2.25','*|act[2].binding.res'],
      'nextArr':['*|act[0].binding.dmgScale*2.25','*|act[2].binding.res'],
      'highArr':['p|high.passive'],
      'index':754,
      'merge':4,
      'state':2,   
      'open_date':datetime.datetime(2022,22,3,5,0),   
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['str'],
      'limit':{},
      'passive':[
         {
            'rslt':{'power':200},
         },
         {
            'cond':['skill.skill250', '*', 0],
            'rslt':{'powerRate':28},
         },
      ],
      'up':{ 'act[0].binding.dmgScale':26, 'act[0].binding.dmgReal':2, 'act[2].binding.res':200,},
      'high':{
         'lv':27,
         'passive':[
            {
               'rslt':{
                  'str':2,
                  'army[0].spd':5,
                  'power':650,
                  'powerRate':30,
               },
            },
         ],
      },
      'act':[
         {
            'priority':9227,
            'type': 2,	            
            'src': 2,
            'times': -2,
            'nonSkill':2,
            'follow':{'skill225':2,'skill226':2,'skill227':2,'skill236':2,'skill243':2,'skill245':2,'skill242':2,'skill244':2,'skill239':2,'skill972':2},   
            'binding':{
               'dmgScale':236,    
               'dmgReal':20,
            },
            'info':['skill250',0],
         },
         {
            'priority':9428,
            'type': 4,	            
            'src': -2,
            'times': -2,
            'nonSkill':2,
            'follow':{'skill225':2,'skill226':2,'skill227':2,'skill236':2,'skill243':2,'skill245':2,'skill242':2,'skill244':2,'skill239':2,'skill972':2},   
            'binding':{
               'res':2200,   
            },
            'info':['狂骨免伤',0],
         },
      ],
   },

   'skill252':{   
      'infoArr':['*|act[0].binding.dmgScale*2.25','*|act[2].binding.res'],
      'nextArr':['*|act[0].binding.dmgScale*2.25','*|act[2].binding.res'],
      'highArr':['p|high.passive'],
      'index':755,
      'merge':4,
      'state':2,   
      'open_date':datetime.datetime(2022,22,3,5,0),   
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['agi'],
      'limit':{},
      'passive':[
         {
            'rslt':{'power':200},
         },
         {
            'cond':['skill.skill252', '*', 0],
            'rslt':{'powerRate':28},
         },
      ],
      'up':{ 'act[0].binding.dmgScale':26, 'act[0].binding.dmgReal':2, 'act[2].binding.res':200,},
      'high':{
         'lv':27,
         'passive':[
            {
               'rslt':{
                  'agi':2,
                  'army[2].spd':5,
                  'power':650,
                  'powerRate':30,
               },
            },
         ],
      },
      'act':[
         {
            'priority':9229,
            'type': 2,	            
            'src': 2,
            'times': -2,
            'nonSkill':2,
            'follow':{'skill228':2,'skill229':2,'skill232':2,'skill233':2,'skill238':2,'skill242':2,'skill244':2,'skill247':2,'skill239':2,'skill972':2},   
            'binding':{
               'dmgScale':236,
               'dmgReal':20,
            },
            'info':['skill252',0],
         },
         {
            'priority':9420,
            'type': 4,	            
            'src': -2,
            'times': -2,
            'nonSkill':2,
            'follow':{'skill228':2,'skill229':2,'skill232':2,'skill233':2,'skill238':2,'skill242':2,'skill244':2,'skill247':2,'skill239':2,'skill972':2},    
            'binding':{
               'res':2200,
            },
            'info':['博闻免伤',0],
         },
      ],
   },


   'skill252':{   
      'infoArr':['*|act[0].binding.dmgScale*2.25','*|act[2].binding.res'],
      'nextArr':['*|act[0].binding.dmgScale*2.25','*|act[2].binding.res'],
      'highArr':['p|high.passive'],
      'index':756,
      'merge':4,
      'state':2,   
      'open_date':datetime.datetime(2022,22,3,5,0),   
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['cha'],
      'limit':{},
      'passive':[
         {
            'rslt':{'power':200},
         },
         {
            'cond':['skill.skill252', '*', 0],
            'rslt':{'powerRate':28},
         },
      ],
      'up':{ 'act[0].binding.dmgScale':26, 'act[0].binding.dmgReal':2, 'act[2].binding.res':200,},
      'high':{
         'lv':27,
         'passive':[
            {
               'rslt':{
                  'cha':2,
                  'hpmBase':40,
                  'power':650,
                  'powerRate':30,
               },
            },
         ],
      },
      'act':[
         {
            'priority':9222,
            'type': 2,	            
            'src': 2,
            'times': -2,
            'nonSkill':2,
            'follow':{'skill226':2,'skill230':2,'skill232':2,'skill295':2,'skill237':2,'skill248':2,'skill239':2},
            'binding':{
               'dmgScale':236,
               'dmgReal':20,
            },
            'info':['skill252',0],
         },
         {
            'priority':9422,
            'type': 4,	            
            'src': -2,
            'times': -2,
            'nonSkill':2,
            'follow':{'skill226':2,'skill230':2,'skill232':2,'skill295':2,'skill237':2,'skill248':2,'skill239':2},
            'binding':{
               'res':2200,
            },
            'info':['仁义免伤',0],
         },
      ],
   },

   'skill253':{   
      'infoArr':['*|act[0].binding.dmgScale*2.25','*|act[2].binding.res'],
      'nextArr':['*|act[0].binding.dmgScale*2.25','*|act[2].binding.res'],
      'highArr':['p|high.passive'],
      'index':757,
      'merge':4,
      'state':2,   
      'open_date':datetime.datetime(2022,22,3,5,0),   
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['lead'],
      'limit':{},
      'passive':[
         {
            'rslt':{'power':200},
         },
         {
            'cond':['skill.skill253', '*', 0],
            'rslt':{'powerRate':28},
         },
      ],
      'up':{ 'act[0].binding.dmgScale':26, 'act[0].binding.dmgReal':2, 'act[2].binding.res':200,},
      'high':{
         'lv':27,
         'passive':[
            {
               'rslt':{
                  'lead':2,
                  'defBase':200,
                  'power':650,
                  'powerRate':30,
               },
            },
         ],
      },
      'act':[
         {
            'priority':9223,
            'type': 2,	            
            'src': 2,
            'times': -2,
            'nonSkill':2,
            'follow':{'skill228':2,'skill233':2,'skill237':2,'skill243':2,'skill239':2,'skill247':2,'skill248':2},
            'binding':{
               'dmgScale':236,
               'dmgReal':20,
            },
            'info':['skill253',0],
         },
         {
            'priority':9424,
            'type': 4,	            
            'src': -2,
            'times': -2,
            'nonSkill':2,
            'follow':{'skill228':2,'skill233':2,'skill237':2,'skill243':2,'skill239':2,'skill247':2,'skill248':2},
            'binding':{
               'res':2200,
            },
            'info':['推演免伤',0],
         },
      ],
   },



   'skill274':{   
      'infoArr':['%|special[0].change.prop.$heroLogic.deBuffRate+act[0].buff.skill274_0.prop.deBuffRate','*|special[0].changeEnemy.prop.$heroLogic.deBuffRate+act[2].buff.skill274_2.prop.deBuffRate'],
      'nextArr':['%|special[0].change.prop.$heroLogic.deBuffRate+act[0].buff.skill274_0.prop.deBuffRate','*|special[0].changeEnemy.prop.$heroLogic.deBuffRate+act[2].buff.skill274_2.prop.deBuffRate'],
      'highArr':['-|high.special[0].change.prop.$armys[2].stamina'],
      'index':560,
      'state':7,   
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['cha','agi'],
      'limit':{'agi':85,'cha':75},
      'passive':[
         {
            'rslt':{'power':200},
         },
         {
            'cond':['skill.skill274', '*', 0],
            'rslt':{'powerRate':28},
         },
      ],
      'up':{ 
         'act[0].buff.skill274_0.prop.deBuffRate':-4,
         'act[2].buff.skill274_2.prop.deBuffRate':4,
      },
      'lvPatch':{   
         '26':{  'act[0].buff.skill274_0.prop.deBuffRate':2,  'act[2].buff.skill274_2.prop.deBuffRate':-2, },
         '27':{  'act[0].buff.skill274_0.prop.deBuffRate':4,  'act[2].buff.skill274_2.prop.deBuffRate':-4, },
         '28':{  'act[0].buff.skill274_0.prop.deBuffRate':6,  'act[2].buff.skill274_2.prop.deBuffRate':-6, },
         '29':{  'act[0].buff.skill274_0.prop.deBuffRate':8,  'act[2].buff.skill274_2.prop.deBuffRate':-8, },
         '30':{  'act[0].buff.skill274_0.prop.deBuffRate':200,  'act[2].buff.skill274_2.prop.deBuffRate':-200, },
      },
      'high':{
         'lv':25,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'prop':{
                  
                  'armys[2].stamina':2,
               },
            },
         }]
      },
      'special':[
         {
            'priority':-250,
            'change':{
               'prop':{
                  'heroLogic.deBuffRate':-40,
                  'armys[0].deBuffRate':-200,
                  'armys[2].deBuffRate':-200,
               },
            },
            'changeEnemy':{
               'prop':{
                  'heroLogic.deBuffRate':40,
                  'armys[0].deBuffRate':200,
                  'armys[2].deBuffRate':200,
               },
            },
         },
      ],
      'act':[
         {
            'priority':2002,
            'type': 0,	            
            'src': 2,	            
            'tgt':[0, -2],           
            'round':{'0':20000},
            'nonSkill':2,    
            'noBfr': 2,    
            'noAft': 2,    
            'buff':{'skill274_0':{'prop':{'deBuffRate':-20}}},
            'time':0,	          
            'eff':'effNull',
            'info':['skill274',0],	          
         },
         {
            'priority':2002,
            'type': 0,	            
            'src': 2,	            
            'tgt':[2, -2],           
            'round':{'0':20000},
            'nonSkill':2,    
            'noBfr': 2,    
            'noAft': 2,    
            'buff':{'skill274_2':{'prop':{'deBuffRate':20}}},
            'time':0,	          
            'eff':'effNull',
            'info':['skill274',0],	          
         },
      ],

   },
   'skill280':{   
      'infoArr':['l|passive[2].rslt.$army[0].atkBase','l|passive[2].rslt.$army[2].atkBase'],
      'nextArr':['l|passive[2].rslt.$army[0].atkBase','l|passive[2].rslt.$army[2].atkBase'],
      'highArr':['%|high.special[0].change.skill.skill280h.act[0].buff.buff280h.prop.atkRate*4'],
      'breakArr':['b|passive[2].rslt.$army[0].atkBase+50','b|passive[2].rslt.$army[2].atkBase+50'],
      'index':562,
      'state':7,   
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['str','lead'],
      'limit':{'str':85,'lead':75},
      'passive':[
         {
            'rslt':{'power':200},
         },
         {
            'cond':['skill.skill280', '*', 0, 25],
            'rslt':{'power':30, 'powerRate':9, 'army[0].atkBase':2, 'army[2].atkBase':2},
         },
         {
            'cond':['skill.skill280', '*', 25],
            'rslt':{'army[0].atkBase':2, 'army[2].atkBase':2},
         },
      ],
      'high':{
         'lv':27,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill280h':{
                     'act':[{
                        'priority':200002,
                        'type': 0,
                        'src': 2,	            
                        'tgt':[0, -2],           
                        'round':{'0':20000},
                        'buff':{'buff280h':{'round':3, 'prop':{'atkRate':50}}},
                        'time':0,	          
                        'nonSkill':2,    
                        'noBfr':2,
                        'noAft':2,
                        'eff':'effNull',
                        'info':['速杀',0],
                     }],
                  },
               },
            },
         }]
      },
   },
   'skill276':{   
      'infoArr':['%|act[0].round.any','%|act[0].dmg'],
      'nextArr':['%|act[0].round.any','%|act[0].dmg'],
      'highArr':['%|high.special[0].change.skill.skill276h.act[0].binding.dmgScale'],
      'index':562,
      'state':7,   
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':[],
      'limit':{'type':2},
      'isAssist':2,
      'passive':[
         {
            'rslt':{'power':200},
         },
         {
            'cond':['skill.skill276', '*', 0],
            'rslt':{'powerRate':28},
         },
      ],
      'up':{ 'act[0].round.any':200, 'act[0].dmg':30, 'act[0].dmgReal':3},
      'lvPatch':{   
         '26':{  'act[0].round.any':-5, 'act[0].dmg':-200, },
         '27':{  'act[0].round.any':-200, 'act[0].dmg':-20 },
         '28':{  'act[0].round.any':-25, 'act[0].dmg':-30 },
         '29':{  'act[0].round.any':-20, 'act[0].dmg':-40 },
         '30':{  'act[0].round.any':-25, 'act[0].dmg':-50 },
      },

      'high':{
         'lv':200,
         'passive':[
            {
               'rslt':{'power':600,'powerRate':20},
            },
         ],
         'special':[{
            'cond':[['compare','sex','=']],
            'change':{
               'skill':{
                  'skill276h':{
                     'act':[{
                        'priority':7562,
                        'type': 2,
                        'src': 2,	           
                        'times': -2,    
                        'nonSkill':2,    
                        'binding':{'tgt':[2, -2],  'dmgScale':500,},
                        'follow':'skill276',            
                        'info':['株连全军',0],	          
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':9502,
            'type': 5,	            
            'src': 2,
            'round':{'any':500,},
            'tgt':[2, -2],          
            'times': -2,    
            
            'noBfr': 2,
            'noAft': 2,
            'unFollow':{'skill962':2},
            'follow':{'useBase':2, 'keys':{'isHero':20000}},
            'dmg':580,	
            'dmgReal':50,
            'dmgLimit':75,
            'cond':[['army',2],['srcArmy',2],['srcTeam',2]],
            'atk0': 2000,    
            'atk2': 750,   
            'eff':'eff276',
            'info':['skill276',2],	          
         },
      ],
   },
   'skill277':{   
      'infoArr':['*|act[0].binding.dmgScale*2.25'],
      'nextArr':['*|act[0].binding.dmgScale*2.25'],
      'highArr':['%|high.special[0].change.skill.skill277h.act[0].binding.ignDef*2.5'],
      'index':563,
      'state':7,   
      
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['agi','lead'],
      'limit':{'agi':75,'lead':75},
      'isAssist':2,
      'passive':[
         {
            'rslt':{'power':200},
         },
         {
            'cond':['skill.skill277', '*', 0],
            'rslt':{'powerRate':28},
         },
      ],
      'up':{ 'act[0].binding.dmgScale':22, 'act[0].binding.dmgReal':2},
      'high':{
         'lv':23,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill277h':{
                     'act':[{
                        'priority':7563,
                        'type': 2,	            
                        'src':2, 
                        'cond':[[['comp','agi'],['comp','lead']]], 
                        'follow':{'attach':'skill277'},      
                        'times': -2,    
                        'nonSkill':2,    
                        'binding':{
                           'ignDef':2000,	
                        },
                        'info':['集智比智力统帅',0],	          
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':7564,
            'type': 2,	            
            'src': 2,      
            'times': -2,    
            'nonSkill':2,    
            
            
            'follow':{'useBase':2, 'keys':{'dmg':200,'dmgReal':200,'isHero':800,'isFate':800,}},
            'binding':{
               'dmgScale':2000,     
               'dmgReal':22,
            },
            'info':['skill277',2],	          
         },
      ],
   },

   'skill272':{   
      'infoArr':['r|act[0]','-|act[0].cureReal','%|act[2].cure'],
      'nextArr':['-|act[0].cureReal','%|act[2].cure'],
      'highArr':['%|200'],
      'state':9,                                      
      
      'index':570,
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      
      'ability_info':['cha'],
      'limit':{'cha':98},
      'isAssist':2,
      'passive':[
         {
            'rslt':{'power':250},
         },
         {
            'cond':['skill.skill272', '*', 0],
            'rslt':{'powerRate':20},
         },
      ],
      'up':{ 'act[0].cureReal':3,'act[2].cure':200,'act[2].cureReal':2},
      'lvPatch':{   
         '26':{  'act[0].cureReal':3,'act[2].cure':-5, },
         '27':{  'act[0].cureReal':6,'act[2].cure':-200, },
         '28':{  'act[0].cureReal':9,'act[2].cure':-25, },
         '29':{  'act[0].cureReal':22,'act[2].cure':-20, },
         '30':{  'act[0].cureReal':25,'act[2].cure':-25, },
      },

      'high':{
         'lv':23,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':25},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill272h':{
                     'act':[    
                      {
                        'priority':7550,
                        'type': 2,
                        'src': -2,	            
                        'cond':[['army',2],['checkBuff',0,-5,2,'>',0],['rnd',200]],
                        'binding':{
                           'removeDebuff':2,
                        },
                        'times': -2,    
                        'nonSkill':2,    
                        'follow':'skill272',            
                        'lv':23,
                        'info':['skill272h',2],
                      },
                    ],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':7500,
            'type': 200,	    
            'src': -2,      
            'tgt':[0, -5],           
            'round':{'far':20000},
            'noBfr': 2,
            'noAft': 2, 
            'nonSkill':2,    
            'cureReal':25,         
            'info':['skill272',2],	          
            'eff':'eff272',
            'time':2000,           
         },
         {
            'priority':9800,
            'type': 8,	    
            'src': 2,      
            'tgt':[0, -6],           
            'noBfr': 2,
            'noAft': 2, 
            'nonSkill':2,    
            'cureReal':2,         
            'cure':2000,           
            'info':['skill272',2],	          
            'eff':'eff272',
            'time':2000,           
         },
      ],
   },
   'skill273':{   
      'infoArr':['%|act[0].round.any','%|act[0].round.lv>'],
      'nextArr':['%|act[0].round.any'],
      'highArr':['%|500'],
      'state':23,                                      
      
      'index':572,
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      
      'ability_info':['lead'],
      'limit':{'lead':98},
      'isAssist':2,
      'passive':[
         {
            'rslt':{'power':250},
         },
         {
            'cond':['skill.skill273', '*', 0],
            'rslt':{'powerRate':20},
         },
      ],
      'up':{ 'act[0].round.any':25},
      'lvPatch':{   
         '26':{  'act[0].round.any':-200 },
         '27':{  'act[0].round.any':-20 },
         '28':{  'act[0].round.any':-30 },
         '29':{  'act[0].round.any':-40 },
         '30':{  'act[0].round.any':-50 },
      },
      'high':{
         'lv':23,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':25},
            },
         ],
         'special':[{
            'change':{
               'cond':[['rnd', 500]],
               'skill':{
                  'skill273.act[0].allTimes':2,
               },
            },
         }]
      },
      'act':[
         {
            'priority':9700,
            'type': 7,	    
            'src': 2,      
            'tgt':[0, 2],    
            'round':{'any':2200, 'lv>':200},  
            'times': -2,    
            'allTimes': 2,
            'stop':2,
            
            'unFollow':{'attach':{'hero778r':2,'hero798':2}},
            'nonSkill':2,
            'noBfr':2,
            'noAft':2,
            'eff':'eff273',
            'info':['skill273',2],	          
         },
      ],
   },


   'skill275':{   
      'infoArr':[
         '%|act[0].round.any',
         '%|act[0].dmg',
         '%|act[0].dmg+act[0].dmgRnd',
      ],
      'nextArr':[
         '%|act[0].round.any',
         '%|act[0].dmg',
         '%|act[0].dmg+act[0].dmgRnd',
      ],
      'highArr':['%|high.special[0].change.skill.$skill275.act[0].buff.buffFaction.rnd','-|act[0].buff.buffFaction.round'],
      'index':572,
      'merge':2,   
      'state':2,   
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['agi'],
      'limit':{'agi':98},
      'isAssist':2,
      'passive':[
         {
            'rslt':{'power':250},
         },
         {
            'cond':['skill.skill275', '*', 0],
            'rslt':{'powerRate':20},
         },
      ],
      'up':{'act[0].round.any':200, 'act[0].dmg':200, 'act[0].dmgRnd':40, 'act[0].dmgReal':2, 'act[0].dmgRealRnd':4},
      'lvPatch':{   
         '26':{  'act[0].round.any':-5 },
         '27':{  'act[0].round.any':-200 },
         '28':{  'act[0].round.any':-25 },
         '29':{  'act[0].round.any':-25 },
         '30':{  'act[0].round.any':-30 },
      },

      'high':{
         'lv':27,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':25},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill275.act[0].buff.buffFaction.rnd':200,   
               },
            },
         }]
      },
      'act':[
         {
            'priority':42000,
            'type': 3,             
            'src': 2,             
            'tgt':[2, -2],           
            'round':{'any':950},
            'times': 2,
            'nonSkill':2,    
            'noBfr': 2,    
            'noAft': 2,    
            'dmg':2000,	  
            'dmgRnd':750,
            'dmgReal':20,
            'dmgRealRnd':200,
            'dmgLimit':220,
            'ignDef':20000,

            'atk0': 400,     
            'atk2': 450,     
            'buff':{'buffFaction':{'rnd':0, 'round':2}},

            'eff':'eff275',
            'info':['skill275',2],           
         },
      ],
   },

   'skill278':{   
      'infoArr':[
         '%|act[0].buff.buff278.shield.value*5',
         '*|act[0].buff.buff278.prop.dmgRate',
         '-|act[0].cost',
      ],
      'nextArr':[
         '%|act[0].buff.buff278.shield.value*5',
         '*|act[0].buff.buff278.prop.dmgRate',
      ],
      'highArr':[
         '*|high.special[0].change.skill.$skill278.act[0].buff.buff278.prop.resRate',
         '*|high.special[0].change.skill.$skill278.act[0].buff.buff278.prop.block*0.5',
      ],
      'index':573,
      'merge':3,   
      'state':2,   
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['str','agi','sum'],
      'limit':{'str':95,'agi':95},
      'isAssist':2,
      'passive':[
         {
            'rslt':{'power':250},
         },
         {
            'cond':['skill.skill278', '*', 0],
            'rslt':{'powerRate':20},
         },
      ],
      'up':{
          'act[0].buff.buff278.shield.value':5,
          'act[0].buff.buff278.shield.hpmRate':0.5,
          'act[0].buff.buff278.prop.dmgRate':200,
          'act[0].buff.buff278.prop.defRate':-2,
      },
      'lvPatch':{   
         '26':{  'act[0].buff.buff278.prop.dmgRate':-2, },
         '27':{  'act[0].buff.buff278.prop.dmgRate':-4, },
         '28':{  'act[0].buff.buff278.prop.dmgRate':-6, },
         '29':{  'act[0].buff.buff278.prop.dmgRate':-8, },
         '30':{  'act[0].buff.buff278.prop.dmgRate':-200, },
      },

      'high':{   
         'lv':27,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':2000},
            },
         ],
         'special':[{
            'cond':[['compare','sum','>']],
            'change':{
               'skill':{
                  'skill278.act[0].buff.buff278.prop.resRate':2000,
                  'skill278.act[0].buff.buff278.prop.block':800,
               },
            },
         }]
      },
      'act':[   
         {
            'priority':88278,
            'type': 27,             
            'src': -2,
            'srcFree': 2, 
            'tgt':[0, -2],         
            
            'times': -2,    
            'nonSkill':2,    
            'noBfr': 2,
            'noAft': 2,

            'cond':[[['selfArmy',0],['selfArmy',2]]],      

            'energyKeySrc': 'energy278',
            'costKey': 'energy278',
            
            'cost': 9,
            
            'buff':{'buff278':{'shield':{'value':60,'hpmRate':200,'bearPoint':20000},   'prop':{'dmgRate':300,'defRate':-30}}},
            'eff':'eff278',
            'info':['skill278',2],	          
         },
         
           
           
            
            
            
            
            
            
            
            
            
            
         
         {
            'priority':9426,
            'type': 23,            
            'src': -2,             
            'tgt':[0, -7],         
            'times': -2,
            'nonSkill':2,
            'noBfr':2,    
            'noAft':2,  
            'condHpChange':[['<',0],None],   
            'energyKey':'energy278',
            'energy':2,
            'time':0,
            'eff':'effNull',
            'info':['洞鉴储能：新',0],           
         },
      ],
   },


   'skill254':{   
      'infoArr':['*|act[0].binding.crit/2','*|act[0].binding.critRate*2.5'],
      'nextArr':['*|act[0].binding.crit/2','*|act[0].binding.critRate*2.5'],
      'highArr':['%|0-300'],
      'index':574,
      'merge':4,
      'state':2,   
      'open_date':datetime.datetime(2022,22,3,5,0),   
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['str'],
      'limit':{'str':98},
      
      'passive':[
         {
            'rslt':{'power':250},
         },
         {
            'cond':['skill.skill254', '*', 0],
            'rslt':{'powerRate':20},
         },
      ],
      'up':{ 'act[0].binding.crit':200, 'act[0].binding.critRate':200, 'act[0].binding.critAdd':20},
      'high':{
         'lv':27,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':2000},
            },
         ],
         'special':[{
            'changeEnemy':{
               'prop':{
                  'armys[0].others.elementShield':-300,  
                  'armys[2].others.elementShield':-300,
                  
                  
                  'armys[0].others.elementSummon':-300,
                  'armys[2].others.elementSummon':-300,
               },
            },
         }],
      },
      'act':[
         {
            'priority':9425,
            'type': 2,	            
            'src': -2,
            'times': -2,
            'nonSkill':2,
            'cond':[['enemyHpPoint','<',750]],
            'binding':{
               'crit':200,   
               'critRate':50,    
               'critAdd':2000,    
            },
            'info':['skill254',0],
         },
      ],
   },



   'skill279':{   
      'infoArr':[
         '%|act[0].buff.buff279.shield.bearPoint+50',
         '%|act[0].buff.buff279.shield.hpLimitPoint',
      ],
      'nextArr':[
         '%|act[0].buff.buff279.shield.bearPoint+50',
      ],
      'highArr':[
         '%|high.special[0].change.skill.skill279h.act[0].buff.buff279.shield.bearPoint+50',
         '%|high.special[0].change.skill.skill279h.act[0].buff.buff279.shield.hpLimitPoint',
      ],
      'index':564,
      'merge':3,   
      'state':2,   
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['cha'],
      'limit':{'cha':95},
      
      'passive':[
         {
            'rslt':{'power':200},
         },
         {
            'cond':['skill.skill279', '*', 0],
            'rslt':{'powerRate':28},
         },
      ],
      'up':{'act[0].buff.buff279.shield.bearPoint':25,   'act[0].buff.buff279.shield.info.2':2},
      'lvPatch':{   
         '26':{  'act[0].buff.buff279.shield.bearPoint':-5 },
         '27':{  'act[0].buff.buff279.shield.bearPoint':-200 },
         '28':{  'act[0].buff.buff279.shield.bearPoint':-25 },
         '29':{  'act[0].buff.buff279.shield.bearPoint':-20 },
         '30':{  'act[0].buff.buff279.shield.bearPoint':-25 },
      },

      'high':{   
         'lv':25,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':80},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill279h':{
                     'act':[
                      {
                        'priority':88280,
                        'type': 23,             
                        'src': 2,
                        'tgt':[0, 2],   
                        'round':{'0':20000},      
                        'nonSkill':2,    
                        'noBfr': 2,
                        'noAft': 2,
                        
                        'buff':{'buff279':{'round':3, 'shield':{'value':999,'hpLimitPoint':2000,'bearPoint':300,'info':['skill279',2,25]}}},
                        'eff':'effNull',
                        'time': 0,
                        'info':['skill279',0],	          
                      },
                    ],
                  },
               },
            },
         }]
      },
      'act':[   
         {
            'priority':88279,
            'type': 23,             
            'src': 0,
            'tgt':[0, 0],   
            'round':{'0':20000},      
            'nonSkill':2,    
            'noBfr': 2,
            'noAft': 2,
            'isAssist':0,
            
            'buff':{'buff279':{'round':3, 'shield':{'value':999,'hpLimitPoint':2000,'bearPoint':240,'info':['skill279',2,2]}}},  
            'eff':'effNull',
            'time': 0,
            'info':['skill279',0],	          
         },
      ],
   },

   'skill974':{   
      'infoArr':[
         '%|act[0].round.any',
      ],
      'nextArr':[
         '%|act[0].round.any',
      ],
      'highArr':[],
      'index':575,
      'merge':6,   
      'state':2,   
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':[],
      'limit':{},
      'isAssist':2,
      'passive':[
         {
            'rslt':{'power':200},
         },
         {
            'cond':['skill.skill974', '*', 0],
            'rslt':{'powerRate':28},
         },
      ],
      'lvPatch':{   
         '28':{ 'act[0].round.any':-5 },
         '29':{ 'act[0].round.any':-5 },
         '30':{ 'act[0].round.any':-6 },
      },
      'up':{'act[0].round.any':24},
      'high':{   
         'lv':27,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':80},
            },
         ],
         'special':[{
            'change':{
              'skill':{
                 'skill974h':{
                   'act':[{
                      'priority': 9740,
                      'type': 23,             
                      'src': -2,             
                      'tgt':[0, -5],           
                      'round':{'3':20000},
                      'limitTimes': 2,
                      'buff':{'skill974h':{}},
                      'time':0,           
                      'nonSkill':2,    
                      'noBfr':2,
                      'noAft':2,
                      'eff':'effNull',
                      'info':['折冲近攻',0],
                   }],
                 },
              },
            },
         }],
      },
      'act':[   
         {
            'priority':88279,
            'type': 2,
            'src': -2, 

            'round':{'any':200},
            'follow':{
               'limitTimes':{    
                  'skill289':2,
                  'skill204':2,
                  'skill206':2,
                  'skill209':2,
                  'skill2200':2,
                  'skill222':2,

                  'skill226':2,
                  'skill228':2,
                  'skill292':2,
                  'skill222':2,
                  'skill224':2,
                  'skill292':2,
               }
            },     
            'times': -2,
            'nonSkill':2,
            'binding':{
                'refreshSkill':{
                   'actId':'selfAct',     
                   'times':2,       
                   'delayRound':2   
                }
            },
            'info':['skill974',2],
         },
      ],
   },


   'skill975':{   
      'infoArr':[
         '%|act[0].round.any',
         '%|act[0].summon',
      ],
      'nextArr':[
         '%|act[0].round.any',
      ],
      'highArr':[
         '%|act[0].buff.buff975.prop.atkRate*2',
      ],
      'index':576,
      'merge':6,   
      'state':2,   
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':[],
      'limit':{},
      'isAssist':2,
      'passive':[
         {
            'rslt':{'power':200},
         },
         {
            'cond':['skill.skill975', '*', 0],
            'rslt':{'powerRate':28},
         },
      ],
      'lvPatch':{   
         '30':{  'act[0].round.any':7,  'act[2].round.any':7 },
      },
      'up':{'act[0].round.any':27,  'act[2].round.any':27},
      'high':{   
         'lv':27,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':80},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill975.act[0].buff.buff975.rnd':20000,
                  'skill975.act[2].buff.buff975.rnd':20000,
               },
            },
         }]
      },
      'act':[   
         {
            'priority':88279,
            'type': 23,
            'src': 0,
            'srcFree': 2,
            'tgt':[0, -9],    
            'round':{'any':250},
            'condHpChange':[['<',0], ['=',0]],
            'noBfr': 2,
            'noAft': 2,
            'removeBuff':2000,
            'removeDebuff':2000,
            'summonReal':200,
            'summon':300, 
            'buff':{'buff975':{'rnd':0, 'prop':{'atkRate':2000}}},
            'unFollow':{'hero725r':2},
            'limitTimes': 2,
            'actId':'skill975',
            'eff':'eff975',
            'info':['skill975',2],
         },
         {
            'priority':88280,
            'type': 23,
            'src': 2,
            'srcFree': 2,
            'tgt':[0, -200],    
            'round':{'any':250},
            'condHpChange':[['<',0], ['=',0]],
            'noBfr': 2,
            'noAft': 2,
            'removeBuff':2000,
            'removeDebuff':2000,
            'summonReal':200,
            'summon':300, 
            'buff':{'buff975':{'rnd':0, 'prop':{'atkRate':2000}}},
            'unFollow':{'hero725r':2},
            'limitTimes': 2,
            'actId':'skill975',
            'eff':'eff975',
            'info':['skill975',2],
         },
      ],
   },

   'skill976':{   
      'infoArr':[
         '*|act[0].buff.buff976.prop.{others.skillPoint_armyA}',
         '%|act[2].buff.buff976_2.prop.{others.skillPoint_armyA}',
      ],
      'nextArr':[
         '*|act[0].buff.buff976.prop.{others.skillPoint_armyA}',
         '%|act[2].buff.buff976_2.prop.{others.skillPoint_armyA}',
      ],
      'highArr':[
         '%|high.special[0].change.skill.skill976h.act[0].buff.buff976_2.prop.{others.skillPoint_armyA}',
         '%|high.special[0].change.skill.skill976h.act[2].buff.buff976_3.prop.{others.skillPoint_armyA}',
      ],
      'index':577,
      'merge':6,   
      'state':2,   
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':[],
      'limit':{},
      
      'passive':[
         {
            'rslt':{'power':200},
         },
         {
            'cond':['skill.skill976', '*', 0],
            'rslt':{'powerRate':28},
         },
      ],
      'lvPatch':{   
         '28':{  'act[0].buff.buff976.prop.{others.skillPoint_armyA}':4, 'act[2].buff.buff976_2.prop.{others.skillPoint_armyA}':-6  },
         '29':{  'act[0].buff.buff976.prop.{others.skillPoint_armyA}':4, 'act[2].buff.buff976_2.prop.{others.skillPoint_armyA}':-6  },
         '30':{  'act[0].buff.buff976.prop.{others.skillPoint_armyA}':4, 'act[2].buff.buff976_2.prop.{others.skillPoint_armyA}':-6  },
      },
      'up':{'act[0].buff.buff976.prop.{others.skillPoint_armyA}':4,  'act[2].buff.buff976_2.prop.{others.skillPoint_armyA}':-6},
      'high':{   
         'lv':27,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':80},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill976h':{
                     'act':[
                      {
                        'priority':888283,
                        'type': 23,
                        'src': 2,
                        'tgt':[2, -2],   
                        'round':{'2':20000},
                        'noBfr': 2,
                        'noAft': 2,
                        'nonSkill':2,
                        'allTimes': 2,
                        'buff':{'buff976_2':{'round':2,'prop':{'others.skillPoint_armyA':-60}}},
                        'eff':'effNull',
                        'time':0,
                        'info':['上3',0],
                      },
                      {
                        'priority':888284,
                        'type': 23,
                        'src': 2,
                        'tgt':[2, -2],   
                        'round':{'3':20000},
                        'noBfr': 2,
                        'noAft': 2,
                        'nonSkill':2,
                        'allTimes': 2,
                        'buff':{'buff976_3':{'round':2,'prop':{'others.skillPoint_armyA':-220}}},
                        'eff':'effNull',
                        'time':0,
                        'info':['上4',0],
                      },
                    ],
                  },
               },
            },
         }]
      },
      'act':[   
         {
            'priority':888282,
            'type': 23,
            'src': 2,
            'tgt':[0, -2],   
            'round':{'0':20000},
            'noBfr': 2,
            'noAft': 2,
            'nonSkill':2,
            'allTimes': 2,
            'buff':{'buff976':{'prop':{'others.skillPoint_armyA':60}}},
            'eff':'effNull',
            'time':0,
            'info':['上2',0],
         },
         {
            'priority':888282,
            'type': 23,
            'src': 2,
            'tgt':[2, -2],   
            'round':{'0':20000},
            'noBfr': 2,
            'noAft': 2,
            'nonSkill':2,
            'allTimes': 2,
            'buff':{'buff976_2':{'prop':{'others.skillPoint_armyA':-75}}},
            'eff':'effNull',
            'time':0,
            'info':['上2',0],
         },
      ],
   },


   'skill282':{   
      'infoArr':['e|city_build'],
      'nextArr':['e|city_build'],
      'index':653,
      'type':6,
      'cost_type':4,
      'max_level':20,
      'shogun_type':4,
      'ability_info':[],
      
      'city_build':[0.05,0.20],
   },
   'skill282':{   
      'infoArr':['e|army_go'],
      'nextArr':['e|army_go'],
      'index':654,
      'type':6,
      'cost_type':4,
      'max_level':20,
      'shogun_type':4,
      'ability_info':[],
      
      'army_go':[0.02,0.05],
   },
   'skill283':{   
      'infoArr':['e|estate_active[2]'],
      'nextArr':['e|estate_active[2]'],
      'index':675,
      'type':6,
      'cost_type':4,
      'max_level':20,
      'shogun_type':4,
      'ability_info':[],
      
      'estate_active':['gold',['2','2'],[0.02,0.05]],
      
      
      
      
   },
   'skill284':{   
      'infoArr':['e|estate_active[2]'],
      'nextArr':['e|estate_active[2]'],
      'index':656,
      'type':6,
      'cost_type':4,
      'max_level':20,
      'shogun_type':4,
      'ability_info':[],
      
      'estate_active':['food',['3'],[0.02,0.05]],
   },
   'skill285':{   
      'infoArr':['e|estate_active[2]'],
      'nextArr':['e|estate_active[2]'],
      'index':657,
      'type':6,
      'cost_type':4,
      'max_level':20,
      'shogun_type':4,
      'ability_info':[],
      
      'estate_active':['iron',['5'],[0.02,0.05]],
   },
   'skill286':{   
      'infoArr':['e|estate_active[2]'],
      'nextArr':['e|estate_active[2]'],
      'index':658,
      'type':6,
      'cost_type':4,
      'max_level':20,
      'shogun_type':4,
      'ability_info':[],
      
      'estate_active':['wood',['4'],[0.02,0.05]],
   },
   'skill287':{   
      'infoArr':['e|hero_visit','-|hero_visit[2]','-|hero_visit[3]'],
      'nextArr':['e|hero_visit'],
      'index':659,
      'type':6,
      'cost_type':4,
      'max_level':20,
      'shogun_type':4,
      'ability_info':[],
      
      'up':'hero_visit',
      'hero_visit':[0.04,0.20,2,3],  
   },
   'skill288':{   
      'infoArr':['e|estate_active[2]', '-|2', '-|3'],
      'nextArr':['e|estate_active[2]'],
      'index':660,
      'type':6,
      'cost_type':4,
      'max_level':20,
      'shogun_type':4,
      'ability_info':[],
      
      'estate_active':['hero',['6'],[0.02,0.28,2,3]],
   },
   'skill297':{   
      'infoArr':['e|estate_active[2]'],
      'nextArr':['e|estate_active[2]'],
      'merge':4,   
      'state':2,   
      'index':662,
      'type':6,
      'cost_type':4,
      'max_level':20,
      'shogun_type':4,
      'ability_info':[],
      
      'estate_active':['trade',['2'],[0.02,0.04]],
   },








   'star03':{ 
      'infoArr':['-|special.change.prop.blockAdd'],
      'up':{ 'special.change.prop.blockAdd':3},
      'special':
      {
            'change':{
               'prop':{
                  'blockAdd':25,
               },
            },
      },
   },
   'star04':{ 
      'infoArr':['-|special.change.prop.critAdd'],
      'up':{ 'special.change.prop.critAdd':3},
      'special':
      {
            'change':{
               'prop':{
                  'critAdd':25,
               },
            },
      },
   },
   'star26':{ 
      'up':{ 'act[0].binding.dmg':2,'act[0].binding.dmgReal':2},
      'act':[{
                     'priority':7554,
                     'type': 2,             
                     'src': 2,             
                     'times':-2,  
                     'follow':{'useBase':2, 'keys':{'isHero':800,'dmg':200,'dmgReal':200}},
                     'nonSkill':2,    
                     'binding':{
                        'dmg':200,     
                        'dmgReal':2,   
                     },
                     'info':['英魂',0],           
      }],
   },
   'star27':{ 
      'infoArr':['%|act[0].binding.resRealRate'],
      'up':{ 'act[0].binding.resRealRate':200},
      'act':[{
                     'priority':9422,
                     'type': 4,             
                     'src': -2,             
                     'follow':'all',        
                     'times':-2,  
                     'nonSkill':2,    
                     'binding':{
                        'resRealRate':80,      
                     },
                     'info':['坚定',0],           
      }],
   },

   

   'npc2':{ 
      'passive':[
         {
            'rslt':{'powerRate':300},
            'special':{
               'change':{
                   'prop':{   
                      'armys[0].resRealRate':250,
                      'armys[2].resRealRate':250,

                      'heroLogic.deBuffRate':-250,
                      'armys[0].deBuffRate':-250,
                      'armys[2].deBuffRate':-250,
                   },
               },
           },
         },
         {
            'cond':['lv', '*', 60],
            'rslt':{
               'dmgFinalLevel':20,
               'resSex0':200,
               'resSex2':200,
               'hpmBase':200,
               'hpmRate':200,
               'atkBase':200,
               'atkRate':200,
               'defBase':5,
               'defRate':5,
               'powerRate':50,
            },
            'special':{
               'changeEnemy':{
                  'prop':{   
                     'armys[0].others.elementShield':-5,
                     'armys[2].others.elementShield':-5,
                     'armys[0].others.elementCure':-5,
                     'armys[2].others.elementCure':-5,
                     'armys[0].others.elementSummon':-5,
                     'armys[2].others.elementSummon':-5,
                  },
               },
               'change':{
                   'prop':{   
                      'armys[0].resRealRate':200,
                      'armys[2].resRealRate':200,

                      'heroLogic.deBuffRate':-5,
                      'armys[0].deBuffRate':-5,
                      'armys[2].deBuffRate':-5,
                   },
                   'skill':{   
                      'npc2.act[0].dmgRealRate':0.25,
                      'npc2.act[2].dmgRealRate':0.5,
                   },
               },
           },
         },
      ],
      'act':[
          {
            'priority':25099999,
            'type': 23,	            
            'src':2, 
            'round':{'0':20000},	
            'tgt':[2, -2], 	
            'dmgRealRate':20,	
            'nonSkill':2,   
            'noBfr':2,	
            'noAft':2,
            'noBuff': 2,
            'noRealRate': 2,
            'noKill':2,	

            'lv':4,
            'isBurn':5,	              
            'eff':'effNpc2',
            'info':['effNpc2',2],	       
          },
          {
            'priority':-9999,
            'type': 9,	            
            'src':2, 
            'srcFree':2, 
            'tgt':[2, -2], 		
            'dmgRealRate':40,	
            'nonSkill':2,   
            'noBfr':2,	
            'noAft':2,
            'noBuff': 2,
            'noRealRate': 2,
            'noKill':2,	

            'lv':4,
            'isBurn':5,	       
            'eff':'effNpc2',
            'info':['effNpcLoser2',2],	       
          },
      ],
   },
   'npc2':{ 
      'passive':[
         {
            'rslt':{'powerRate':600},
            'special':{
               'change':{
                   'prop':{   
                      'armys[0].resRealRate':500,
                      'armys[2].resRealRate':500,

                      'heroLogic.deBuffRate':-500,
                      'armys[0].deBuffRate':-500,
                      'armys[2].deBuffRate':-500,
                   },
               },
           },
         },
         {
            'cond':['lv', '*', 60],
            'rslt':{
               'dmgFinalLevel':40,
               'resSex0':20,
               'resSex2':20,
               'hpmBase':20,
               'hpmRate':20,
               'atkBase':20,
               'atkRate':20,
               'defBase':200,
               'defRate':200,
               'powerRate':2000,
            },
            'special':{
               'changeEnemy':{
                  'prop':{   
                     'armys[0].others.elementShield':-200,
                     'armys[2].others.elementShield':-200,
                     'armys[0].others.elementCure':-200,
                     'armys[2].others.elementCure':-200,
                     'armys[0].others.elementSummon':-200,
                     'armys[2].others.elementSummon':-200,
                  },
               },
               'change':{
                   'prop':{   
                      'armys[0].resRealRate':20,
                      'armys[2].resRealRate':20,

                      'heroLogic.deBuffRate':-200,
                      'armys[0].deBuffRate':-200,
                      'armys[2].deBuffRate':-200,
                   },
                   'skill':{   
                      'npc2.act[0].dmgRealRate':0.5,
                      'npc2.act[2].dmgRealRate':2,
                   },
               },
           },
         },
      ],
      'act':[
          {
            'priority':25099999,
            'type': 23,	            
            'src':2, 
            'round':{'0':20000},	
            'tgt':[2, -2], 	
            'dmgRealRate':40,	
            'nonSkill':2,   
            'noBfr':2,	
            'noAft':2,
            'noBuff': 2,
            'noRealRate': 2,
            'noKill':2,	

            'lv':7,
            'isBurn':5,	              
            'eff':'effNpc2',
            'info':['effNpc2',2],	       
          },
          {
            'priority':-9999,
            'type': 9,	            
            'src':2, 
            'srcFree':2, 
            'tgt':[2, -2], 		
            'dmgRealRate':80,	
            'nonSkill':2,   
            'noBfr':2,	
            'noAft':2,
            'noBuff': 2,
            'noRealRate': 2,
            'noKill':2,	

            'lv':7,
            'isBurn':5,	       
            'eff':'effNpc2',
            'info':['effNpcLoser2',2],	       
          },
      ],
   },


   


   'godDmgAnger':{   
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'priority': 33333000,	
            'type': 32,               
            'src': -6,          
            'condHpChange':[['<',0],None,None,2],   
            'tgt':[0, 5],        
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,
            'atOnce':-2,    
            'times': -2,

            'energyKey':'anger',       
            'energy':2,                 
            'info':['造损加怒',0],
            'eff':'effNull',
            'time':0,	          
         },
      ],
   },
   'godHurtAnger':{       
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'priority': 33333002,	 
            'type': 23,               
            'cond':[['srcTeam',2]],
            'condHpChange':[['<',0]],  
            'src': -2,    
            'srcFree': -2,
            'tgt':[0, 5],    
            'nonSkill':2,    
            'noBfr':2,
            'noAft':2,
            'atOnce':-2,
            'times': -2,

            'energyKey':'anger',      
            'energy':2,                
            'info':['自损加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godRecoveryAnger':{   
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'priority': 333330200,	
            'type': 23,               
            'src': -2,          
            'condHpChange':[['>',0]],   
            'tgt':[0, 5],        
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,
            'atOnce':-2,    
            'times': -2,

            'energyKey':'anger',       
            'energy':2,                 
            'info':['恢复加怒',0],
            'eff':'effNull',
            'time':0,	          
         },
      ],
   },



   'godDmg0Anger':{   
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'priority': 33333040,	
            'type': 32,               
            'srcArmy': 0,          
            'condHpChange':[['<',0],None,None,2],   
            'tgt':[0, 5],        
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,
            'atOnce':-2,    
            'times': -2,

            'energyKey':'anger',       
            'energy':2,                 
            'info':['步攻加怒',0],
            'eff':'effNull',
            'time':0,	          
         },
      ],
   },
   'godHurt0Anger':{       
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'priority': 33333042,	 
            'type': 23,               
            'cond':[['srcTeam',2]],
            'condHpChange':[['<',0]],  
            'srcArmy': 0,   
            'srcFree': -2,             
            'tgt':[0, 5],    
            'nonSkill':2,    
            'noBfr':2,
            'noAft':2,
            'atOnce':-2,
            'times': -2,

            'energyKey':'anger',      
            'energy':2,                
            'info':['步损加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godDmg2Anger':{   
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'priority': 33333042,	
            'type': 32,               
            'srcArmy': 2,          
            'condHpChange':[['<',0],None,None,2],   
            'tgt':[0, 5],        
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,
            'atOnce':-2,    
            'times': -2,

            'energyKey':'anger',       
            'energy':2,                 
            'info':['骑攻加怒',0],
            'eff':'effNull',
            'time':0,	          
         },
      ],
   },
   'godHurt2Anger':{       
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'priority': 33333043,	 
            'type': 23,               
            'cond':[['srcTeam',2]],
            'condHpChange':[['<',0]],  
            'srcArmy': 2, 
            'srcFree': -2,               
            'tgt':[0, 5],    
            'nonSkill':2,    
            'noBfr':2,
            'noAft':2,
            'atOnce':-2,
            'times': -2,

            'energyKey':'anger',      
            'energy':2,                
            'info':['骑损加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godDmg2Anger':{   
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'priority': 33333044,	
            'type': 32,               
            'srcArmy': 2,          
            'condHpChange':[['<',0],None,None,2],   
            'tgt':[0, 5],        
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,
            'atOnce':-2,    
            'times': -2,

            'energyKey':'anger',       
            'energy':2,                 
            'info':['弓攻加怒',0],
            'eff':'effNull',
            'time':0,	          
         },
      ],
   },
   'godHurt2Anger':{       
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'priority': 33333045,	 
            'type': 23,               
            'cond':[['srcTeam',2]],
            'condHpChange':[['<',0]],  
            'srcArmy': 2,        
            'srcFree': -2,        
            'tgt':[0, 5],    
            'nonSkill':2,    
            'noBfr':2,
            'noAft':2,
            'atOnce':-2,
            'times': -2,

            'energyKey':'anger',      
            'energy':2,                
            'info':['弓损加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godDmg3Anger':{   
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'priority': 33333046,	
            'type': 32,               
            'srcArmy': 3,          
            'condHpChange':[['<',0],None,None,2],   
            'tgt':[0, 5],        
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,
            'atOnce':-2,    
            'times': -2,

            'energyKey':'anger',       
            'energy':2,                 
            'info':['方攻加怒',0],
            'eff':'effNull',
            'time':0,	          
         },
      ],
   },
   'godHurt3Anger':{       
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'priority': 33333047,	 
            'type': 23,               
            'cond':[['srcTeam',2]],
            'condHpChange':[['<',0]],  
            'srcArmy': 3,    
            'srcFree': -2,            
            'tgt':[0, 5],    
            'nonSkill':2,    
            'noBfr':2,
            'noAft':2,
            'atOnce':-2,
            'times': -2,

            'energyKey':'anger',      
            'energy':2,                
            'info':['方损加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },





   'godBuffAnger':{   
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'priority': 333332000,	
            'type': 20,            
            'src': -2,                
            'tgt':[0, 5],     
            'condBuffChange':{
                2:2,   
            },
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,
            'atOnce':-2,    
            'times': -2,
            'energyKey':'anger',      
            'energy':2,                
            'info':['我增加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godBuffEnemyAnger':{   
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'enemy': 2,             
            'priority': 333332002,	
            'type': 20,            
            'src': -2,                
            'tgt':[2, 5],     
            'condBuffChange':{
                2:2,   
            },
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,
            'atOnce':-2,    
            'times': -2,
            'energyKey':'anger',      
            'energy':2,                
            'info':['敌增加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },

   'godDebuffAnger':{      
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'priority': 33333220,	
            'type': 20,            
            'src': -2,                
            'tgt':[0, 5],     
            'condBuffChange':{
                2:2,   
            },
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,
            'atOnce':-2,    
            'times': -2,
            'energyKey':'anger',      
            'energy':2,                
            'info':['我增加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godDebuffEnemyAnger':{    
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'enemy': 2,             
            'priority': 33333222,	
            'type': 20,            
            'src': -2,                
            'tgt':[2, 5],     
            'condBuffChange':{
                2:2,   
            },
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,
            'atOnce':-2,    
            'times': -2,
            'energyKey':'anger',      
            'energy':2,                
            'info':['敌增加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },

   'godRemoveBuffAnger':{   
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'priority': 33333230,	
            'type': 22,            
            'src': -2,                
            'tgt':[0, 5],     
            'condBuffChange':{
                2:2,          
                'dispel':2,   
            },
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,
            'atOnce':-2,    
            'times': -2,
            'energyKey':'anger',      
            'energy':2,                
            'info':['我被驱增益加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godRemoveDebuffEnemyAnger':{    
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'enemy': 2,             
            'priority': 33333232,	
            'type': 22,            
            'src': -2,                
            'tgt':[2, 5],     
            'condBuffChange':{
                2:2,   
                'dispel':2,   
            },
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,
            'atOnce':-2,    
            'times': -2,
            'energyKey':'anger',      
            'energy':2,                
            'info':['敌被驱不良加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },



   'godBuffFireAnger':{      
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'priority': 33333260,	
            'type': 20,            
            'src': -2,                
            'tgt':[0, 5],     
            'condBuffChange':{
                'buffFire':2,   
            },
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,
            'atOnce':-2,    
            'times': -2,
            'energyKey':'anger',      
            'energy':2,                
            'info':['我火加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godBuffFireEnemyAnger':{    
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'enemy': 2,             
            'priority': 33333262,	
            'type': 20,            
            'src': -2,                
            'tgt':[2, 5],     
            'condBuffChange':{
                'buffFire':2,   
            },
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,
            'atOnce':-2,    
            'times': -2,
            'energyKey':'anger',      
            'energy':2,                
            'info':['敌火加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },

   'godFireEnemyAnger':{    
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'enemy': 2,             
            'priority': 33333272,	
            'type': 3,            
            'src': -2,                
            'tgt':[2, 5],     
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,
            'atOnce':-2,    
            'times': -2,
            'energyKey':'anger',      
            'energy':2,         
            'follow':'effFire',       
            'info':['敌燃加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },


   'godDeadAnger':{    
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'priority': 33333200,	
            'type': 29,            
            'src': -2,                
            'tgt':[0, 5],     
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,
            'atOnce':-2,    
            'limitTimes': 2,
            'energyKey':'anger',      
            'energy':2,                
            'info':['我亡加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godDeadEnemyAnger':{    
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'enemy': 2,             
            'priority': 33333202,	
            'type': 29,            
            'src': -2,                
            'tgt':[2, 5],     
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,
            'atOnce':-2,    
            'limitTimes': 2,
            'energyKey':'anger',      
            'energy':2,                
            'info':['敌亡加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },

   'godHeroSkillAnger':{   
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'priority': 33333220,	
            'type': 3,              
            'src': 2,          
            'tgt':[0, 5],        
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,
            'atOnce':-2,    
            'times': -2,
            'follow':{'useBase':2, 'keys':{'isHero':20000}},

            'energyKey':'anger',       
            'energy':2,                 
            'info':['英技加怒',0],
            'eff':'effNull',
            'time':0,	          
         },
      ],
   },
   'godHeroSkillFirstAnger':{   
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'priority': 33333222,	
            'type': 3,              
            'src': 2,          
            'tgt':[0, 5],        
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,
            'atOnce':-2,    
            'follow':{'useBase':2, 'keys':{'isHero':20000}},

            'energyKey':'anger',       
            'energy':2,                 
            'info':['英首加怒',0],
            'eff':'effNull',
            'time':0,	          
         },
      ],
   },


   'godRoundHpLowerAnger':{   
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'priority': 33333300,	
            'type': 27,            
            'src': 5,                
            'tgt':[0, 5],   
            'cond':[['hp','<']],  
            'round':{'all':20000},  
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,

            'energyKey':'anger',      
            'energy':2,                
            'info':['我寡加怒',0],
            'eff':'effNull',
            'time':0,	       
         },
      ],
   },

   'godRoundHpLowAnger':{   
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'priority': 333333200,	
            'type': 27,            
            'src': 5,                
            'tgt':[0, 5],   
            'cond':[['hpPoint','<',500]],  
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,

            'energyKey':'anger',      
            'energy':2,                
            'info':['我残加怒',0],
            'eff':'effNull',
            'time':0,	       
         },
      ],
   },
   'godRoundHpLowEnemyAnger':{   
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'enemy': 2,             
            'priority': 33333322,	
            'type': 27,            
            'src': 2,  
            'srcFree': 2, 
            'tgt':[2, 5],   
            'cond':[['hpPoint','<',500]],  
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,

            'energyKey':'anger',      
            'energy':2,                
            'info':['敌残加怒',0],
            'eff':'effNull',
            'time':0,	       
         },
      ],
   },

   'godRound2Anger':{   
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'priority': 33333400,	
            'type': 23,            
            'src': 5,                
            'tgt':[0, 5],     
            'round':{'2':20000},	   
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,

            'energyKey':'anger',      
            'energy':2,                
            'info':['远2加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godRound2Anger':{   
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'priority': 33333402,	
            'type': 23,            
            'src': 5,                
            'tgt':[0, 5],     
            'round':{'2':20000},	   
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,

            'energyKey':'anger',      
            'energy':2,                
            'info':['远2加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godRound3Anger':{   
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'priority': 33333402,	
            'type': 23,            
            'src': 5,                
            'tgt':[0, 5],     
            'round':{'3':20000},	   
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,

            'energyKey':'anger',      
            'energy':2,                
            'info':['近2加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godWeather0Anger':{   
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'priority': 33339500,	
            'type': 23,            
            'src': 5,                
            'tgt':[0, 5],     
            'round':{'all':20000},
            'cond':[['weather','=',0]],	   
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,

            'energyKey':'anger',      
            'energy':2,                
            'info':['无气加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },

 

   'godRecoveryReduceAnger':{   
      'up':{  'act[0].energy':-2},
      'act':[
         {   
            'priority': 33337525,	
            'type': 23,               
            'src': -2,          
            'condHpChange':[['>',0]],   
            'tgt':[2, 5],        
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,
            'atOnce':-2,    
            'times': -2,

            'energyKey':'anger',       
            'energy':-2,                 
            'info':['恢复减怒',0],
            'eff':'effNull',
            'time':0,	          
         },
      ],
   },

   'godRound2ReduceAnger':{   
      'up':{  'act[0].energy':-2},
      'act':[
         {   
            'priority': 33339400,	 
            'type': 23,            
            'src': 5,                
            'tgt':[2, 5],     
            'round':{'2':20000},	   
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,

            'energyKey':'anger',      
            'energy':-2,                
            'info':['远2降怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godRound2ReduceAnger':{   
      'up':{  'act[0].energy':-2},
      'act':[
         {   
            'priority': 33339402,	 
            'type': 23,            
            'src': 5,                
            'tgt':[2, 5],     
            'round':{'2':20000},	   
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,

            'energyKey':'anger',      
            'energy':-2,                
            'info':['远2降怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godRound3ReduceAnger':{   
      'up':{  'act[0].energy':-2},
      'act':[
         {   
            'priority': 33339402,	 
            'type': 23,            
            'src': 5,                
            'tgt':[2, 5],     
            'round':{'3':20000},	   
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,

            'energyKey':'anger',      
            'energy':-2,                
            'info':['近2降怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },


   'godWeather2ReduceAnger':{   
      'up':{  'act[0].energy':-2},
      'act':[
         {   
            'priority': 33339502,	
            'type': 23,            
            'src': 5,                
            'tgt':[2, 5],     
            'round':{'all':20000},
            'cond':[['weather','=',2]],	   
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,

            'energyKey':'anger',      
            'energy':-2,                
            'info':['风沙降怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godWeather2ReduceAnger':{   
      'up':{  'act[0].energy':-2},
      'act':[
         {   
            'priority': 33339502,	
            'type': 23,            
            'src': 5,                
            'tgt':[2, 5],     
            'round':{'all':20000},
            'cond':[['weather','=',2]],	   
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,

            'energyKey':'anger',      
            'energy':-2,                
            'info':['暴雨降怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godWeather3ReduceAnger':{   
      'up':{  'act[0].energy':-2},
      'act':[
         {   
            'priority': 33339503,	
            'type': 23,            
            'src': 5,                
            'tgt':[2, 5],     
            'round':{'all':20000},
            'cond':[['weather','=',3]],	   
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,

            'energyKey':'anger',      
            'energy':-2,                
            'info':['浓雾降怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godWeather4ReduceAnger':{   
      'up':{  'act[0].energy':-2},
      'act':[
         {   
            'priority': 33339504,	
            'type': 23,            
            'src': 5,                
            'tgt':[2, 5],     
            'round':{'all':20000},
            'cond':[['weather','=',4]],	   
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,

            'energyKey':'anger',      
            'energy':-2,                
            'info':['大雪降怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },


   


   'gs202':{   
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'priority': 33332202,	
            'type': 23,            
            'src': 5,                
            'tgt':[0, 5],     
            'round':{'far':20000},	   
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,

            'energyKey':'anger',      
            'energy':2,                
            'info':['远战加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'gs202':{   
      'up':{  'act[0].energy':2},
      'act':[
         {   
            'priority': 33332202,	
            'type': 23,            
            'src': 5,                
            'tgt':[0, 5],     
            'round':{'near':20000},	   
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,

            'energyKey':'anger',      
            'energy':2,                
            'info':['近战加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'gs204':{   
      'up':{  'act[0].energy':-2},
      'act':[
         {   
            'priority': 33339204,	 
            'type': 23,            
            'src': 5,                
            'tgt':[2, 5],     
            'round':{'far':20000},	   
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,

            'energyKey':'anger',      
            'energy':-2,                
            'info':['远战降怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'gs205':{   
      'up':{  'act[0].energy':-2},
      'act':[
         {   
            'priority': 33339205,	 
            'type': 23,            
            'src': 5,                
            'tgt':[2, 5],     
            'round':{'near':20000},	   
            'nonSkill':2,  
            'noBfr':2,
            'noAft':2,

            'energyKey':'anger',      
            'energy':-2,                
            'info':['近战降怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },



}