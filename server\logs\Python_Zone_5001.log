[I 250729 01:10:56 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:10:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.68ms
[I 250729 01:10:56 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:10:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.22ms
[I 250729 01:10:56 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:10:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.03ms
[I 250729 01:10:56 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:10:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 339.73ms
[I 250729 01:10:57 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:11:07 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:11:07 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:11:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:11:07 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:11:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.15ms
[I 250729 01:11:07 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:11:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.92ms
[I 250729 01:11:07 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:11:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 345.20ms
[I 250729 01:11:17 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:11:18 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:11:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.61ms
[I 250729 01:11:18 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:11:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.59ms
[I 250729 01:11:18 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:11:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.01ms
[I 250729 01:11:18 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:11:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 344.70ms
[I 250729 01:11:27 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:11:29 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:11:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.65ms
[I 250729 01:11:29 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:11:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.18ms
[I 250729 01:11:29 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:11:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.07ms
[I 250729 01:11:29 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:11:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 348.17ms
[I 250729 01:11:37 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:11:40 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:11:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.67ms
[I 250729 01:11:40 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:11:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.14ms
[I 250729 01:11:40 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:11:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.89ms
[I 250729 01:11:40 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:11:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 338.70ms
[I 250729 01:11:47 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:11:51 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:11:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.65ms
[I 250729 01:11:51 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:11:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.58ms
[I 250729 01:11:51 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:11:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.04ms
[I 250729 01:11:51 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:11:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 343.15ms
[I 250729 01:11:57 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:12:02 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:12:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.67ms
[I 250729 01:12:02 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:12:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.17ms
[I 250729 01:12:02 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:12:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.93ms
[I 250729 01:12:02 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:12:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 337.17ms
[I 250729 01:12:07 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:12:13 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:12:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.65ms
[I 250729 01:12:13 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:12:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.11ms
[I 250729 01:12:13 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:12:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.95ms
[I 250729 01:12:13 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:12:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 349.51ms
[I 250729 01:12:17 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:12:18 server:41790] Message: 45, 5917, 1000000045, use_prop, 123.139.57.61, 1, {u'item_id': u'item099', u'item_num': 29, u'range_index': 2}, None
[I 250729 01:12:24 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:12:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.66ms
[I 250729 01:12:24 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:12:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 29.27ms
[I 250729 01:12:24 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:12:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.48ms
[I 250729 01:12:24 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:12:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 351.47ms
[I 250729 01:12:27 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:12:31 server:41790] Message: 58, 5665, 1000000045, hero_star_up, 123.139.57.61, 1, {u'if_cost': 0, u'hid': u'hero764'}, None
[I 250729 01:12:33 server:41790] Message: 94, 5665, 1000000045, hero_star_up, 123.139.57.61, 1, {u'if_cost': 0, u'hid': u'hero764'}, None
[I 250729 01:12:33 server:41790] Message: 93, 5665, 1000000045, hero_star_up, 123.139.57.61, 1, {u'if_cost': 0, u'hid': u'hero764'}, None
[I 250729 01:12:34 server:41790] Message: 92, 5665, 1000000045, hero_star_up, 123.139.57.61, 1, {u'if_cost': 0, u'hid': u'hero764'}, None
[I 250729 01:12:34 server:41790] Message: 93, 5665, 1000000045, hero_star_up, 123.139.57.61, 1, {u'if_cost': 0, u'hid': u'hero764'}, None
[I 250729 01:12:34 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:12:34 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:12:34 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:12:34 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 29.13ms
[I 250729 01:12:34 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:12:34 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.00ms
[I 250729 01:12:34 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:12:35 server:41790] Message: 95, 5664, 1000000045, hero_star_up, 123.139.57.61, 1, {u'if_cost': 0, u'hid': u'hero764'}, None
[I 250729 01:12:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 350.97ms
[I 250729 01:12:35 server:41790] Message: 94, 5664, 1000000045, hero_star_up, 123.139.57.61, 1, {u'if_cost': 0, u'hid': u'hero764'}, None
[I 250729 01:12:37 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:12:45 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:12:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:12:45 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:12:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.48ms
[I 250729 01:12:45 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:12:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.04ms
[I 250729 01:12:45 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:12:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 355.22ms
[I 250729 01:12:47 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:12:56 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:12:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:12:56 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:12:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.13ms
[I 250729 01:12:56 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:12:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.92ms
[I 250729 01:12:56 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:12:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 338.93ms
[I 250729 01:12:57 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:13:07 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:13:07 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:13:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.56ms
[I 250729 01:13:07 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:13:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.11ms
[I 250729 01:13:07 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:13:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.93ms
[I 250729 01:13:07 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:13:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 340.64ms
[I 250729 01:13:17 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:13:18 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:13:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.63ms
[I 250729 01:13:18 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:13:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.15ms
[I 250729 01:13:18 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:13:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.01ms
[I 250729 01:13:18 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:13:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 351.31ms
[I 250729 01:13:27 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:13:29 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:13:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.63ms
[I 250729 01:13:29 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:13:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.55ms
[I 250729 01:13:29 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:13:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.99ms
[I 250729 01:13:29 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:13:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 351.11ms
[I 250729 01:13:35 server:41790] Message: 60, 5683, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill201', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:13:36 server:41790] Message: 49, 5683, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill201', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:13:38 server:41790] Message: 49, 5683, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill201', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:13:38 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:13:39 server:41790] Message: 49, 5683, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill201', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:13:40 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:13:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.66ms
[I 250729 01:13:40 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:13:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 26.79ms
[I 250729 01:13:40 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:13:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.98ms
[I 250729 01:13:40 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:13:40 server:41790] Message: 49, 5683, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill201', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:13:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 344.62ms
[I 250729 01:13:41 server:41790] Message: 49, 5683, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill201', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:13:43 server:41790] Message: 49, 5683, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill201', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:13:44 server:41790] Message: 50, 5683, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill201', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:13:45 server:41790] Message: 50, 5683, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill201', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:13:47 server:41790] Message: 50, 5684, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill201', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:13:48 server:41790] Message: 50, 5684, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill201', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:13:48 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:13:49 server:41790] Message: 55, 5684, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill201', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:13:50 server:41790] Message: 50, 5684, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill201', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:13:51 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:13:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.66ms
[I 250729 01:13:51 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:13:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.24ms
[I 250729 01:13:51 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:13:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.97ms
[I 250729 01:13:51 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:13:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 347.39ms
[I 250729 01:13:52 server:41790] Message: 50, 5684, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill201', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:13:53 server:41790] Message: 50, 5684, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill201', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:13:54 server:41790] Message: 49, 5684, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill201', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:13:55 server:41790] Message: 49, 5684, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill201', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:13:57 server:41790] Message: 49, 5684, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill201', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:13:58 server:41790] Message: 50, 5684, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill201', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:13:59 server:41790] Message: 50, 5684, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill201', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:13:59 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:14:00 server:41790] Message: 50, 5684, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill201', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:01 server:41790] Message: 50, 5684, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill201', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:01 server:41790] Message: 51, 5684, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill201', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:02 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:14:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.66ms
[I 250729 01:14:02 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:14:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.40ms
[I 250729 01:14:02 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:14:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.01ms
[I 250729 01:14:02 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:14:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 341.80ms
[I 250729 01:14:02 server:41790] Message: 51, 5684, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill201', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:03 server:41790] Message: 51, 5684, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill201', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:09 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:14:13 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:14:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.61ms
[I 250729 01:14:13 server:41790] Message: 61, 5699, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill203', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:13 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:14:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.17ms
[I 250729 01:14:13 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:14:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.02ms
[I 250729 01:14:13 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:14:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 344.96ms
[I 250729 01:14:14 server:41790] Message: 49, 5699, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill203', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:15 server:41790] Message: 49, 5699, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill203', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:16 server:41790] Message: 50, 5699, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill203', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:18 server:41790] Message: 49, 5699, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill203', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:19 server:41790] Message: 49, 5699, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill203', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:19 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:14:20 server:41790] Message: 49, 5699, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill203', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:24 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:14:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:14:24 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:14:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.85ms
[I 250729 01:14:24 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:14:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.08ms
[I 250729 01:14:24 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:14:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 344.02ms
[I 250729 01:14:24 server:41790] Message: 49, 5699, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill203', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:26 server:41790] Message: 49, 5699, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill203', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:27 server:41790] Message: 49, 5700, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill203', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:28 server:41790] Message: 49, 5700, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill203', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:29 server:41790] Message: 49, 5700, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill203', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:29 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:14:31 server:41790] Message: 50, 5700, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill203', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:32 server:41790] Message: 50, 5700, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill203', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:33 server:41790] Message: 51, 5700, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill203', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:34 server:41790] Message: 50, 5700, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill203', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:35 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:14:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.61ms
[I 250729 01:14:35 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:14:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.17ms
[I 250729 01:14:35 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:14:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.95ms
[I 250729 01:14:35 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:14:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 344.98ms
[I 250729 01:14:36 server:41790] Message: 50, 5700, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill203', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:37 server:41790] Message: 50, 5700, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill203', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:38 server:41790] Message: 50, 5700, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill203', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:39 server:41790] Message: 50, 5700, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill203', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:40 server:41790] Message: 50, 5700, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill203', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:40 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:14:41 server:41790] Message: 50, 5700, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill203', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:42 server:41790] Message: 51, 5700, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill203', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:42 server:41790] Message: 50, 5700, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill203', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:43 server:41790] Message: 50, 5700, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill203', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:45 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:14:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.65ms
[I 250729 01:14:45 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:14:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.46ms
[I 250729 01:14:45 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:14:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.98ms
[I 250729 01:14:45 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:14:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 347.96ms
[I 250729 01:14:50 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:14:53 server:41790] Message: 61, 5715, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill202', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:54 server:41790] Message: 50, 5715, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill202', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:55 server:41790] Message: 49, 5715, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill202', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:14:56 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:14:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.63ms
[I 250729 01:14:56 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:14:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.40ms
[I 250729 01:14:56 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:14:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.95ms
[I 250729 01:14:56 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:14:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 345.13ms
[I 250729 01:15:00 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:15:06 server:41790] Message: 12, 5715, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill202', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:15:07 server:41790] Message: 49, 5715, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill202', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:15:07 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:15:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.60ms
[I 250729 01:15:07 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:15:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.15ms
[I 250729 01:15:07 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:15:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.95ms
[I 250729 01:15:07 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:15:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 343.66ms
[I 250729 01:15:08 server:41790] Message: 48, 5715, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill202', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:15:10 server:41790] Message: 49, 5715, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill202', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:15:11 server:41790] Message: 49, 5715, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill202', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:15:11 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:15:12 server:41790] Message: 49, 5715, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill202', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:15:14 server:41790] Message: 49, 5716, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill202', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:15:15 server:41790] Message: 49, 5716, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill202', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:15:16 server:41790] Message: 48, 5716, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill202', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:15:17 server:41790] Message: 50, 5716, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill202', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:15:18 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:15:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:15:18 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:15:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.25ms
[I 250729 01:15:18 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:15:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.94ms
[I 250729 01:15:18 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:15:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 343.68ms
[I 250729 01:15:19 server:41790] Message: 49, 5716, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill202', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:15:20 server:41790] Message: 48, 5716, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill202', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:15:21 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:15:23 server:41790] Message: 48, 5716, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill202', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:15:24 server:41790] Message: 49, 5716, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill202', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:15:26 server:41790] Message: 49, 5716, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill202', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:15:27 server:41790] Message: 48, 5716, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill202', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:15:28 server:41790] Message: 49, 5716, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill202', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:15:29 server:41790] Message: 49, 5716, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill202', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:15:29 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:15:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:15:29 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:15:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.13ms
[I 250729 01:15:29 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:15:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.96ms
[I 250729 01:15:29 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:15:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 343.87ms
[I 250729 01:15:30 server:41790] Message: 49, 5716, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill202', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:15:31 server:41790] Message: 50, 5716, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill202', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:15:31 server:41790] Message: 50, 5716, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill202', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:15:31 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:15:32 server:41790] Message: 50, 5716, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill202', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:15:40 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:15:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.61ms
[I 250729 01:15:40 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:15:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 26.79ms
[I 250729 01:15:40 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:15:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.99ms
[I 250729 01:15:40 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:15:41 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 344.82ms
[I 250729 01:15:41 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:15:51 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:15:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.59ms
[I 250729 01:15:51 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:15:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.12ms
[I 250729 01:15:51 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:15:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.95ms
[I 250729 01:15:51 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:15:51 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:15:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 347.22ms
[I 250729 01:15:56 server:41790] Message: 60, 5731, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill206', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:15:57 server:41790] Message: 49, 5731, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill206', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:15:59 server:41790] Message: 50, 5731, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill206', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:00 server:41790] Message: 49, 5731, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill206', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:01 server:41790] Message: 49, 5731, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill206', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:02 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:16:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.68ms
[I 250729 01:16:02 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:16:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.56ms
[I 250729 01:16:02 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:16:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.02ms
[I 250729 01:16:02 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:16:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 357.44ms
[I 250729 01:16:03 server:41790] Message: 49, 5731, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill206', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:03 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:16:04 server:41790] Message: 50, 5731, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill206', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:05 server:41790] Message: 48, 5731, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill206', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:06 server:41790] Message: 49, 5731, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill206', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:08 server:41790] Message: 49, 5732, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill206', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:09 server:41790] Message: 49, 5732, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill206', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:10 server:41790] Message: 49, 5732, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill206', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:12 server:41790] Message: 50, 5732, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill206', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:13 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:16:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.60ms
[I 250729 01:16:13 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:16:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.33ms
[I 250729 01:16:13 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:16:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.94ms
[I 250729 01:16:13 server:41790] Message: 49, 5732, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill206', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:13 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:16:13 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:16:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 347.26ms
[I 250729 01:16:14 server:41790] Message: 49, 5732, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill206', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:16 server:41790] Message: 49, 5732, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill206', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:17 server:41790] Message: 49, 5732, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill206', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:18 server:41790] Message: 49, 5732, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill206', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:19 server:41790] Message: 49, 5732, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill206', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:20 server:41790] Message: 50, 5732, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill206', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:21 server:41790] Message: 50, 5732, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill206', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:22 server:41790] Message: 50, 5731, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill206', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:23 server:41790] Message: 50, 5731, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill206', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:23 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:16:24 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:16:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.63ms
[I 250729 01:16:24 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:16:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.23ms
[I 250729 01:16:24 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:16:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.96ms
[I 250729 01:16:24 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:16:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 342.00ms
[I 250729 01:16:33 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:16:35 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:16:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:16:35 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:16:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.19ms
[I 250729 01:16:35 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:16:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.99ms
[I 250729 01:16:35 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:16:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 337.21ms
[I 250729 01:16:40 server:41790] Message: 60, 5746, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill204', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:41 server:41790] Message: 49, 5746, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill204', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:42 server:41790] Message: 49, 5746, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill204', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:44 server:41790] Message: 49, 5746, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill204', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:44 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:16:45 server:41790] Message: 49, 5746, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill204', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:46 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:16:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:16:46 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:16:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.17ms
[I 250729 01:16:46 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:16:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.02ms
[I 250729 01:16:46 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:16:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 345.67ms
[I 250729 01:16:46 server:41790] Message: 50, 5746, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill204', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:48 server:41790] Message: 49, 5746, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill204', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:49 server:41790] Message: 49, 5746, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill204', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:50 server:41790] Message: 48, 5746, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill204', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:51 server:41790] Message: 49, 5747, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill204', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:53 server:41790] Message: 48, 5747, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill204', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:54 server:41790] Message: 48, 5747, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill204', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:54 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:16:55 server:41790] Message: 50, 5747, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill204', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:57 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:16:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.63ms
[I 250729 01:16:57 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:16:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 51.74ms
[I 250729 01:16:57 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:16:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.03ms
[I 250729 01:16:57 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:16:57 server:41790] Message: 77, 5747, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill204', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 352.29ms
[I 250729 01:16:58 server:41790] Message: 49, 5747, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill204', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:16:59 server:41790] Message: 49, 5747, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill204', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:01 server:41790] Message: 49, 5747, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill204', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:02 server:41790] Message: 49, 5747, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill204', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:04 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:17:04 server:41790] Message: 49, 5747, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill204', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:06 server:41790] Message: 49, 5747, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill204', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:07 server:41790] Message: 50, 5747, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill204', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:08 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:17:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.65ms
[I 250729 01:17:08 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:17:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 26.87ms
[I 250729 01:17:08 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:17:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.96ms
[I 250729 01:17:08 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:17:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 346.88ms
[I 250729 01:17:09 server:41790] Message: 50, 5747, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill204', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:10 server:41790] Message: 50, 5747, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill204', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:11 server:41790] Message: 50, 5747, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill204', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:11 server:41790] Message: 49, 5747, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill204', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:14 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:17:19 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:17:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:17:19 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:17:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.42ms
[I 250729 01:17:19 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:17:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.08ms
[I 250729 01:17:19 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:17:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 346.25ms
[I 250729 01:17:24 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:17:26 server:41790] Message: 59, 5762, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill223', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:27 server:41790] Message: 49, 5762, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill223', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:29 server:41790] Message: 49, 5762, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill223', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:30 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:17:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:17:30 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:17:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.57ms
[I 250729 01:17:30 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:17:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.97ms
[I 250729 01:17:30 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:17:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 351.05ms
[I 250729 01:17:30 server:41790] Message: 48, 5762, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill223', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:31 server:41790] Message: 49, 5762, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill223', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:33 server:41790] Message: 49, 5762, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill223', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:34 server:41790] Message: 48, 5762, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill223', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:35 server:41790] Message: 48, 5762, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill223', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:35 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:17:37 server:41790] Message: 49, 5762, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill223', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:38 server:41790] Message: 48, 5763, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill223', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:40 server:41790] Message: 49, 5763, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill223', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:41 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:17:41 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:17:41 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:17:41 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.32ms
[I 250729 01:17:41 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:17:41 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.95ms
[I 250729 01:17:41 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:17:41 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 344.92ms
[I 250729 01:17:41 server:41790] Message: 49, 5763, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill223', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:42 server:41790] Message: 49, 5763, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill223', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:44 server:41790] Message: 49, 5763, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill223', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:45 server:41790] Message: 49, 5763, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill223', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:47 server:41790] Message: 49, 5763, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill223', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:47 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:17:48 server:41790] Message: 48, 5763, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill223', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:49 server:41790] Message: 49, 5763, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill223', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:51 server:41790] Message: 49, 5763, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill223', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:51 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:17:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.63ms
[I 250729 01:17:51 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:17:52 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 28.24ms
[I 250729 01:17:52 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:17:52 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.94ms
[I 250729 01:17:52 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:17:52 server:41790] Message: 50, 5763, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill223', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:52 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 345.84ms
[I 250729 01:17:53 server:41790] Message: 50, 5763, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill223', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:54 server:41790] Message: 50, 5763, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill223', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:55 server:41790] Message: 50, 5763, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill223', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:56 server:41790] Message: 50, 5763, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill223', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:57 server:41790] Message: 49, 5763, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill223', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:17:57 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:18:02 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:18:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.65ms
[I 250729 01:18:02 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:18:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.59ms
[I 250729 01:18:02 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:18:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.04ms
[I 250729 01:18:02 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:18:03 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 347.17ms
[I 250729 01:18:07 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:18:13 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:18:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:18:13 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:18:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.14ms
[I 250729 01:18:13 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:18:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.95ms
[I 250729 01:18:13 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:18:14 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 343.15ms
[I 250729 01:18:17 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:18:24 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:18:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.63ms
[I 250729 01:18:24 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:18:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.09ms
[I 250729 01:18:24 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:18:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.92ms
[I 250729 01:18:24 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:18:25 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 335.80ms
[I 250729 01:18:27 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:18:35 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:18:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.59ms
[I 250729 01:18:35 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:18:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.13ms
[I 250729 01:18:35 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:18:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.02ms
[I 250729 01:18:35 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:18:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 341.14ms
[I 250729 01:18:37 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:18:46 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:18:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.67ms
[I 250729 01:18:46 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:18:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.17ms
[I 250729 01:18:46 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:18:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.07ms
[I 250729 01:18:46 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:18:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 344.26ms
[I 250729 01:18:47 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:18:57 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:18:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.63ms
[I 250729 01:18:57 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:18:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.12ms
[I 250729 01:18:57 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:18:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.94ms
[I 250729 01:18:57 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:18:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 340.66ms
[I 250729 01:18:57 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:19:01 server:41790] Message: 56, 579, 1000000045, hero_fate, 123.139.57.61, 1, {u'hid': u'hero764', u'fate_id': u'fate7641'}, None
[I 250729 01:19:04 server:41790] Message: 92, 591, 1000000045, hero_fate, 123.139.57.61, 1, {u'hid': u'hero764', u'fate_id': u'fate7642'}, None
[I 250729 01:19:05 server:41790] Message: 91, 603, 1000000045, hero_fate, 123.139.57.61, 1, {u'hid': u'hero764', u'fate_id': u'fate7643'}, None
[I 250729 01:19:08 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:19:08 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:19:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:19:08 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:19:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.09ms
[I 250729 01:19:08 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:19:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.99ms
[I 250729 01:19:08 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:19:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 348.35ms
[I 250729 01:19:12 server:41790] Message: 13, 29303, 1000000045, hero_star_install, 123.139.57.61, 1, {u'position': 5, u'hid': u'hero764', u'star_id': u'star11|1767'}, None
[I 250729 01:19:15 server:41790] Message: 49, 29329, 1000000045, hero_star_install, 123.139.57.61, 1, {u'position': 10, u'hid': u'hero764', u'star_id': u'star13|1764'}, None
[I 250729 01:19:16 server:41790] Message: 49, 29354, 1000000045, hero_star_install, 123.139.57.61, 1, {u'position': 15, u'hid': u'hero764', u'star_id': u'star14|202'}, None
[I 250729 01:19:18 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:19:19 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:19:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:19:19 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:19:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.16ms
[I 250729 01:19:19 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:19:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.99ms
[I 250729 01:19:19 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:19:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 349.96ms
[I 250729 01:19:28 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:19:30 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:19:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:19:30 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:19:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.37ms
[I 250729 01:19:30 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:19:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.93ms
[I 250729 01:19:30 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:19:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 346.36ms
[I 250729 01:19:36 server:41790] Message: 13, 29354, 1000000045, hero_star_install, 123.139.57.61, 1, {u'position': 5, u'hid': u'hero764', u'star_id': u'star10|1658'}, None
[I 250729 01:19:38 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:19:41 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:19:41 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.61ms
[I 250729 01:19:41 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:19:41 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.21ms
[I 250729 01:19:41 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:19:41 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.02ms
[I 250729 01:19:41 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:19:41 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 347.30ms
[I 250729 01:19:42 server:41790] Message: 55, 33889, 1000000045, star_lv_up, 123.139.57.61, 1, {u'item_id': u'item603', u'star_id': u'star10|1658'}, None
[I 250729 01:19:42 server:41790] Message: 90, 33889, 1000000045, star_lv_up, 123.139.57.61, 1, {u'item_id': u'item603', u'star_id': u'star10|1658'}, None
[I 250729 01:19:42 server:41790] Message: 90, 33889, 1000000045, star_lv_up, 123.139.57.61, 1, {u'item_id': u'item603', u'star_id': u'star10|1658'}, None
[I 250729 01:19:43 server:41790] Message: 92, 33889, 1000000045, star_lv_up, 123.139.57.61, 1, {u'item_id': u'item603', u'star_id': u'star10|1658'}, None
[I 250729 01:19:43 server:41790] Message: 92, 33889, 1000000045, star_lv_up, 123.139.57.61, 1, {u'item_id': u'item603', u'star_id': u'star10|1658'}, None
[I 250729 01:19:47 server:41790] Message: 92, 33889, 1000000045, star_lv_up, 123.139.57.61, 1, {u'item_id': u'item603', u'star_id': u'star08|1508'}, None
[I 250729 01:19:47 server:41790] Message: 92, 33889, 1000000045, star_lv_up, 123.139.57.61, 1, {u'item_id': u'item603', u'star_id': u'star08|1508'}, None
[I 250729 01:19:47 server:41790] Message: 93, 33889, 1000000045, star_lv_up, 123.139.57.61, 1, {u'item_id': u'item603', u'star_id': u'star08|1508'}, None
[I 250729 01:19:48 server:41790] Message: 94, 33889, 1000000045, star_lv_up, 123.139.57.61, 1, {u'item_id': u'item603', u'star_id': u'star08|1508'}, None
[I 250729 01:19:48 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:19:48 server:41790] Message: 93, 33889, 1000000045, star_lv_up, 123.139.57.61, 1, {u'item_id': u'item603', u'star_id': u'star08|1508'}, None
[I 250729 01:19:51 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:19:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.65ms
[I 250729 01:19:51 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:19:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.22ms
[I 250729 01:19:51 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:19:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.95ms
[I 250729 01:19:51 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:19:52 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 353.60ms
[I 250729 01:19:58 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:20:00 server:41790] Message: 13, 29355, 1000000045, hero_star_install, 123.139.57.61, 1, {u'position': 10, u'hid': u'hero764', u'star_id': u'star12|1701'}, None
[I 250729 01:20:02 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:20:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.63ms
[I 250729 01:20:02 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:20:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.17ms
[I 250729 01:20:02 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:20:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.00ms
[I 250729 01:20:02 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:20:03 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 354.44ms
[I 250729 01:20:03 server:41790] Message: 93, 33889, 1000000045, star_lv_up, 123.139.57.61, 1, {u'item_id': u'item603', u'star_id': u'star12|1701'}, None
[I 250729 01:20:03 server:41790] Message: 93, 33889, 1000000045, star_lv_up, 123.139.57.61, 1, {u'item_id': u'item603', u'star_id': u'star12|1701'}, None
[I 250729 01:20:04 server:41790] Message: 94, 33889, 1000000045, star_lv_up, 123.139.57.61, 1, {u'item_id': u'item603', u'star_id': u'star12|1701'}, None
[I 250729 01:20:04 server:41790] Message: 91, 33889, 1000000045, star_lv_up, 123.139.57.61, 1, {u'item_id': u'item603', u'star_id': u'star12|1701'}, None
[I 250729 01:20:04 server:41790] Message: 92, 33888, 1000000045, star_lv_up, 123.139.57.61, 1, {u'item_id': u'item603', u'star_id': u'star12|1701'}, None
[I 250729 01:20:08 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:20:13 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:20:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.63ms
[I 250729 01:20:13 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:20:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.53ms
[I 250729 01:20:13 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:20:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.98ms
[I 250729 01:20:13 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:20:14 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 346.97ms
[I 250729 01:20:18 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:20:24 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:20:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.60ms
[I 250729 01:20:24 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:20:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.12ms
[I 250729 01:20:24 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:20:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.91ms
[I 250729 01:20:24 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:20:25 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 340.40ms
[I 250729 01:20:28 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:20:35 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:20:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:20:35 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:20:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.31ms
[I 250729 01:20:35 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:20:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.01ms
[I 250729 01:20:35 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:20:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 351.11ms
[I 250729 01:20:38 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:20:46 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:20:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:20:46 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:20:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.17ms
[I 250729 01:20:46 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:20:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.92ms
[I 250729 01:20:46 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:20:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 345.60ms
[I 250729 01:20:48 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:20:57 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:20:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.65ms
[I 250729 01:20:57 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:20:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.19ms
[I 250729 01:20:57 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:20:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.98ms
[I 250729 01:20:57 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:20:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 347.07ms
[I 250729 01:20:58 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:21:08 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:21:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.68ms
[I 250729 01:21:08 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:21:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.17ms
[I 250729 01:21:08 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:21:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.96ms
[I 250729 01:21:08 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:21:08 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:21:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 348.29ms
[I 250729 01:21:18 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:21:19 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:21:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.65ms
[I 250729 01:21:19 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:21:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.18ms
[I 250729 01:21:19 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:21:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.00ms
[I 250729 01:21:19 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:21:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 345.33ms
[I 250729 01:21:28 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:21:30 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:21:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.63ms
[I 250729 01:21:30 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:21:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.20ms
[I 250729 01:21:30 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:21:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.94ms
[I 250729 01:21:30 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:21:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 342.18ms
[I 250729 01:21:38 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:21:40 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:21:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.71ms
[I 250729 01:21:40 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:21:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.94ms
[I 250729 01:21:40 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:21:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.01ms
[I 250729 01:21:40 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:21:41 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 343.02ms
[I 250729 01:21:48 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:21:51 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:21:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:21:51 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:21:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.15ms
[I 250729 01:21:51 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:21:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.96ms
[I 250729 01:21:51 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:21:52 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 335.96ms
[I 250729 01:21:58 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:21:59 server:41790] Message: 13, 30131, 1000000045, hero_star_install, 123.139.57.61, 1, {u'position': 10, u'hid': u'hero777', u'star_id': u'star13|1764'}, None
[I 250729 01:22:02 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:22:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.63ms
[I 250729 01:22:02 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:22:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.11ms
[I 250729 01:22:02 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:22:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.94ms
[I 250729 01:22:02 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:22:03 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 343.99ms
[I 250729 01:22:04 server:41790] Message: 57, 33890, 1000000045, star_lv_up, 123.139.57.61, 1, {u'item_id': u'item603', u'star_id': u'star13|1764'}, None
[I 250729 01:22:04 server:41790] Message: 53, 33890, 1000000045, star_lv_up, 123.139.57.61, 1, {u'item_id': u'item603', u'star_id': u'star13|1764'}, None
[I 250729 01:22:04 server:41790] Message: 53, 33890, 1000000045, star_lv_up, 123.139.57.61, 1, {u'item_id': u'item603', u'star_id': u'star13|1764'}, None
[I 250729 01:22:04 server:41790] Message: 53, 33890, 1000000045, star_lv_up, 123.139.57.61, 1, {u'item_id': u'item603', u'star_id': u'star13|1764'}, None
[I 250729 01:22:05 server:41790] Message: 52, 33890, 1000000045, star_lv_up, 123.139.57.61, 1, {u'item_id': u'item603', u'star_id': u'star13|1764'}, None
[I 250729 01:22:05 server:41790] Message: 54, 33890, 1000000045, star_lv_up, 123.139.57.61, 1, {u'item_id': u'item603', u'star_id': u'star13|1764'}, None
[I 250729 01:22:05 server:41790] Message: 52, 33890, 1000000045, star_lv_up, 123.139.57.61, 1, {u'item_id': u'item603', u'star_id': u'star13|1764'}, None
[I 250729 01:22:05 server:41790] Message: 51, 33890, 1000000045, star_lv_up, 123.139.57.61, 1, {u'item_id': u'item603', u'star_id': u'star13|1764'}, None
[I 250729 01:22:06 server:41790] Message: 53, 33891, 1000000045, star_lv_up, 123.139.57.61, 1, {u'item_id': u'item603', u'star_id': u'star13|1764'}, None
[I 250729 01:22:08 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:22:13 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:22:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.65ms
[I 250729 01:22:13 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:22:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.16ms
[I 250729 01:22:13 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:22:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.97ms
[I 250729 01:22:13 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:22:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 341.84ms
[I 250729 01:22:18 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:22:23 server:41790] Message: 13, 29384, 1000000045, hero_star_install, 123.139.57.61, 1, {u'position': 16, u'hid': u'hero764', u'star_id': u'star21|1768'}, None
[I 250729 01:22:24 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:22:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:22:24 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:22:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.12ms
[I 250729 01:22:24 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:22:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.88ms
[I 250729 01:22:24 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:22:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 347.02ms
[I 250729 01:22:28 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:22:29 server:41790] Message: 13, 29410, 1000000045, hero_star_install, 123.139.57.61, 1, {u'position': 19, u'hid': u'hero764', u'star_id': u'star07|1838'}, None
[I 250729 01:22:31 server:41790] Message: 50, 29436, 1000000045, hero_star_install, 123.139.57.61, 1, {u'position': 18, u'hid': u'hero764', u'star_id': u'star06|1782'}, None
[I 250729 01:22:32 server:41790] Message: 49, 29462, 1000000045, hero_star_install, 123.139.57.61, 1, {u'position': 17, u'hid': u'hero764', u'star_id': u'star05|1777'}, None
[I 250729 01:22:35 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:22:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.65ms
[I 250729 01:22:35 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:22:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.19ms
[I 250729 01:22:35 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:22:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.95ms
[I 250729 01:22:35 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:22:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 341.53ms
[I 250729 01:22:38 server:41790] Message: 13, 29487, 1000000045, hero_star_install, 123.139.57.61, 1, {u'position': 7, u'hid': u'hero764', u'star_id': u'star05|1830'}, None
[I 250729 01:22:38 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:22:40 server:41790] Message: 49, 29512, 1000000045, hero_star_install, 123.139.57.61, 1, {u'position': 9, u'hid': u'hero764', u'star_id': u'star07|1832'}, None
[I 250729 01:22:41 server:41790] Message: 50, 29537, 1000000045, hero_star_install, 123.139.57.61, 1, {u'position': 6, u'hid': u'hero764', u'star_id': u'star18|1771'}, None
[I 250729 01:22:44 server:41790] Message: 50, 29562, 1000000045, hero_star_install, 123.139.57.61, 1, {u'position': 8, u'hid': u'hero764', u'star_id': u'star06|1877'}, None
[I 250729 01:22:46 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:22:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.66ms
[I 250729 01:22:46 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:22:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.14ms
[I 250729 01:22:46 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:22:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.96ms
[I 250729 01:22:46 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:22:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 338.53ms
[I 250729 01:22:47 server:41790] Message: 50, 29587, 1000000045, hero_star_install, 123.139.57.61, 1, {u'position': 1, u'hid': u'hero764', u'star_id': u'star17|1776'}, None
[I 250729 01:22:48 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:22:49 server:41790] Message: 49, 29612, 1000000045, hero_star_install, 123.139.57.61, 1, {u'position': 4, u'hid': u'hero764', u'star_id': u'star07|1836'}, None
[I 250729 01:22:51 server:41790] Message: 49, 29637, 1000000045, hero_star_install, 123.139.57.61, 1, {u'position': 2, u'hid': u'hero764', u'star_id': u'star05|1839'}, None
[I 250729 01:22:55 server:41790] Message: 49, 29662, 1000000045, hero_star_install, 123.139.57.61, 1, {u'position': 3, u'hid': u'hero764', u'star_id': u'star06|1872'}, None
[I 250729 01:22:57 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:22:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:22:57 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:22:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.22ms
[I 250729 01:22:57 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:22:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.02ms
[I 250729 01:22:57 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:22:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 343.40ms
[I 250729 01:22:58 server:41790] Message: 49, 29688, 1000000045, hero_star_install, 123.139.57.61, 1, {u'position': 12, u'hid': u'hero764', u'star_id': u'star05|1801'}, None
[I 250729 01:22:58 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:23:00 server:41790] Message: 49, 29714, 1000000045, hero_star_install, 123.139.57.61, 1, {u'position': 14, u'hid': u'hero764', u'star_id': u'star07|1781'}, None
[I 250729 01:23:02 server:41790] Message: 50, 29740, 1000000045, hero_star_install, 123.139.57.61, 1, {u'position': 11, u'hid': u'hero764', u'star_id': u'star16|1783'}, None
[I 250729 01:23:04 server:41790] Message: 49, 29766, 1000000045, hero_star_install, 123.139.57.61, 1, {u'position': 13, u'hid': u'hero764', u'star_id': u'star06|1824'}, None
[I 250729 01:23:08 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:23:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.63ms
[I 250729 01:23:08 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:23:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.16ms
[I 250729 01:23:08 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:23:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.37ms
[I 250729 01:23:08 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:23:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 347.79ms
[I 250729 01:23:08 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:23:18 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:23:18 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:23:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.63ms
[I 250729 01:23:18 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:23:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.15ms
[I 250729 01:23:18 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:23:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.93ms
[I 250729 01:23:18 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:23:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 338.74ms
[I 250729 01:23:28 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:23:29 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:23:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:23:29 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:23:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.16ms
[I 250729 01:23:29 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:23:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.97ms
[I 250729 01:23:29 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:23:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 336.06ms
[I 250729 01:23:38 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:23:40 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:23:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.65ms
[I 250729 01:23:40 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:23:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.16ms
[I 250729 01:23:40 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:23:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.96ms
[I 250729 01:23:40 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:23:41 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 339.72ms
[I 250729 01:23:43 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:43 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:43 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:43 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:43 server:41790] Message: 1, 6034, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:43 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:45 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:45 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:45 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:45 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:45 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:46 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:46 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:46 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:46 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:46 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:46 server:41790] Message: 1, 6034, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:47 server:41790] Message: 1, 6034, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:47 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:47 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:47 server:41790] Message: 1, 6034, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:47 server:41790] Message: 1, 6034, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:47 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:48 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:48 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:48 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:48 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:48 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:48 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:48 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:23:48 server:41790] Message: 1, 6034, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:49 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:49 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:49 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:49 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:49 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:49 server:41790] Message: 1, 6034, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:49 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:50 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:50 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:50 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:50 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:50 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:50 server:41790] Message: 1, 6036, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:51 server:41790] Message: 1, 6036, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:51 server:41790] Message: 1, 6036, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:51 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:51 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:51 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:23:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.58ms
[I 250729 01:23:51 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:23:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.21ms
[I 250729 01:23:51 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:23:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.07ms
[I 250729 01:23:51 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:23:51 server:41790] Message: 12, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:51 server:41790] Message: 1, 6034, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:52 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 344.91ms
[I 250729 01:23:52 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:52 server:41790] Message: 1, 6034, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:52 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:52 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:52 server:41790] Message: 1, 6035, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:52 server:41790] Message: 1, 6033, 1000000045, hero_resolve, 123.139.57.61, 1, {u'item_id': u'item764', u'resolve_num': 10}, None
[I 250729 01:23:58 server:41790] Message: 14, 6186, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill205', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:23:59 server:41790] Message: 50, 6186, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill205', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:23:59 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:24:01 server:41790] Message: 49, 6187, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill205', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:24:02 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:24:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:24:02 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:24:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.51ms
[I 250729 01:24:02 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:24:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 7.81ms
[I 250729 01:24:02 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:24:02 server:41790] Message: 50, 6187, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill205', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:24:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 350.16ms
[I 250729 01:24:04 server:41790] Message: 50, 6187, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill205', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:24:05 server:41790] Message: 51, 6186, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill205', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:24:07 server:41790] Message: 50, 6186, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill205', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:24:08 server:41790] Message: 50, 6186, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill205', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:24:09 server:41790] Message: 50, 6186, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill205', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:24:09 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:24:11 server:41790] Message: 50, 6186, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill205', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:24:12 server:41790] Message: 50, 6186, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill205', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:24:13 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:24:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.66ms
[I 250729 01:24:13 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:24:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.43ms
[I 250729 01:24:13 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:24:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.99ms
[I 250729 01:24:13 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:24:13 server:41790] Message: 50, 6186, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill205', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:24:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 353.99ms
[I 250729 01:24:14 server:41790] Message: 51, 6186, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill205', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:24:19 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:24:24 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:24:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:24:24 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:24:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.66ms
[I 250729 01:24:24 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:24:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.01ms
[I 250729 01:24:24 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:24:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 345.34ms
[I 250729 01:24:29 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:24:35 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:24:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.65ms
[I 250729 01:24:35 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:24:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.22ms
[I 250729 01:24:35 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:24:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.98ms
[I 250729 01:24:35 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:24:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 348.13ms
[I 250729 01:24:36 web:2162] 101 GET /gateway/ (112.47.197.116) 0.41ms
[I 250729 01:24:36 server:41455] WebSocket opened
[I 250729 01:24:36 server:29800] on_login, 1000000046
[I 250729 01:24:36 server:41790] Message: 31, 274924, 1000000046, login, 112.47.197.116, 1, {u'uid': 46, u'zone': u'1', u'pf_key': u'13489551313', u'pf_data': None, u'user_code': u'', u'pf': u'developer', u'sessionid': u'72856022f3736f7078904ecdb66ce54b|1753723475|developer'}, None
[I 250729 01:24:36 server:41902] api call: 118.178.139.38, get_use_merge, {'zone': u'1'}
[I 250729 01:24:36 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.60ms
[I 250729 01:24:37 server:41790] Message: 0, 200, 1000000046, get_my_pk_yard_hids, 112.47.197.116, 1, {}, None
[I 250729 01:24:37 server:41790] Message: 29, 179468, 1000000046, w.get_info, 112.47.197.116, 1, {}, None
[E 250729 01:24:37 concurrent:140] Future exception was never retrieved: CurlError: HTTP 599: Connection timed out after 1040 milliseconds
[I 250729 01:24:37 server:41790] Message: 0, 695, 1000000046, get_city_visit, 112.47.197.116, 1, {}, None
[I 250729 01:24:37 server:41790] Message: 0, 1896, 1000000046, w.get_user_list, 112.47.197.116, 1, {u'uids': [u'1000000029', u'1000000031', u'1000000045', u'1000000046', u'1000000053', u'1000000061', u'1000000069', u'1000000070', u'1000000073', u'1000000077', u'1000000103', u'1000000119', u'1000000126', u'1000000284']}, None
[I 250729 01:24:38 server:41790] Message: 0, 6625, 1000000046, get_fight_log, 112.47.197.116, 1, {}, None
[I 250729 01:24:38 server:41790] Message: 0, 1019, 1000000046, w.get_my_troops, 112.47.197.116, 1, {}, None
[I 250729 01:24:39 server:41790] Message: 0, 264, 1000000046, get_fight_task, 112.47.197.116, 1, {}, None
[I 250729 01:24:39 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:24:40 server:41790] Message: 0, 1765, 1000000046, get_pk_npc, 112.47.197.116, 1, {}, None
[I 250729 01:24:42 server:41790] Message: 21, 82031, 1000000046, w.troop_add, 112.47.197.116, 1, {u'hid': u'hero720', u'is_pay': False}, None
[I 250729 01:24:46 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:24:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.63ms
[I 250729 01:24:46 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:24:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 26.34ms
[I 250729 01:24:46 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:24:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.11ms
[I 250729 01:24:46 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:24:46 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:24:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 346.33ms
[I 250729 01:24:47 server:41790] Message: 3, 11799, 1000000046, get_rank_by_type, 112.47.197.116, 1, {u'rank_type': u'power'}, None
[I 250729 01:24:49 server:41790] Message: 2, 18529, 1000000046, user_info, 112.47.197.116, 1, {u'uid': 1000000045}, None
[I 250729 01:24:49 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:24:54 server:41790] Message: 62, 6201, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill224', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:24:56 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:24:57 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:24:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:24:57 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:24:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 26.64ms
[I 250729 01:24:57 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:24:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.98ms
[I 250729 01:24:57 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:24:57 server:41790] Message: 203, 6216, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill292', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:24:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 348.72ms
[I 250729 01:24:59 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:25:06 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:25:07 server:41790] Message: 2, 19378, 1000000046, user_info, 112.47.197.116, 1, {u'uid': 1000000046}, None
[I 250729 01:25:08 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:25:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.61ms
[I 250729 01:25:08 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:25:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.27ms
[I 250729 01:25:08 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:25:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.01ms
[I 250729 01:25:08 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:25:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 347.48ms
[I 250729 01:25:09 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:25:16 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:25:17 server:41790] Message: 3, 11799, 1000000046, get_rank_by_type, 112.47.197.116, 1, {u'rank_type': u'power'}, None
[I 250729 01:25:19 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:25:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:25:19 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:25:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.18ms
[I 250729 01:25:19 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:25:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.02ms
[I 250729 01:25:19 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:25:19 server:41790] Message: 2, 18529, 1000000046, user_info, 112.47.197.116, 1, {u'uid': 1000000045}, None
[I 250729 01:25:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 342.04ms
[I 250729 01:25:19 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:25:26 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:25:29 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:25:29 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:25:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.61ms
[I 250729 01:25:29 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:25:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.13ms
[I 250729 01:25:29 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:25:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.99ms
[I 250729 01:25:29 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:25:30 server:41790] Message: 61, 6231, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill222', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:25:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 343.13ms
[I 250729 01:25:31 server:41790] Message: 50, 6231, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill222', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:25:33 server:41790] Message: 50, 6231, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill222', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:25:34 server:41790] Message: 50, 6231, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill222', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:25:36 server:41790] Message: 50, 6231, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill222', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:25:36 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:25:37 server:41790] Message: 50, 6231, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill222', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:25:38 server:41790] Message: 50, 6231, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill222', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:25:40 server:41790] Message: 49, 6231, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill222', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:25:40 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:25:40 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:25:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.65ms
[I 250729 01:25:40 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:25:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.24ms
[I 250729 01:25:40 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:25:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.95ms
[I 250729 01:25:40 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:25:41 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 346.36ms
[I 250729 01:25:41 server:41790] Message: 49, 6231, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill222', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:25:43 server:41790] Message: 50, 6232, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill222', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:25:44 server:41790] Message: 49, 6232, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill222', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:25:46 server:41790] Message: 49, 6232, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill222', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:25:46 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:25:47 server:41790] Message: 51, 6232, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill222', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:25:49 server:41790] Message: 49, 6232, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill222', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:25:50 server:41790] Message: 49, 6232, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill222', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:25:50 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:25:51 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:25:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:25:51 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:25:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.23ms
[I 250729 01:25:51 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:25:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.94ms
[I 250729 01:25:51 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:25:52 server:41790] Message: 49, 6232, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill222', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:25:52 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 345.53ms
[I 250729 01:25:53 server:41790] Message: 50, 6232, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill222', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:25:54 server:41790] Message: 50, 6232, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill222', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:25:56 server:41790] Message: 50, 6232, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill222', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:25:56 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:25:57 server:41790] Message: 51, 6232, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill222', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:25:58 server:41790] Message: 50, 6232, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill222', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:25:59 server:41790] Message: 51, 6232, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill222', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:26:00 server:41790] Message: 51, 6232, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill222', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:26:00 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:26:01 server:41790] Message: 51, 6232, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill222', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:26:02 server:41790] Message: 51, 6232, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill222', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:26:02 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:26:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.63ms
[I 250729 01:26:02 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:26:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.26ms
[I 250729 01:26:02 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:26:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.01ms
[I 250729 01:26:02 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:26:03 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 346.54ms
[I 250729 01:26:04 server:41790] Message: 11, 6541, 1000000046, hero_skill_lv_up, 112.47.197.116, 1, {u'skill_id': u'skill271', u'hid': u'hero7700', u'fast_learn': 0}, None
[I 250729 01:26:06 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:26:10 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:26:13 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:26:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.58ms
[I 250729 01:26:13 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:26:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 25.87ms
[I 250729 01:26:13 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:26:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.96ms
[I 250729 01:26:13 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:26:14 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 342.46ms
[I 250729 01:26:15 server:41790] Message: 2, 19378, 1000000046, user_info, 112.47.197.116, 1, {u'uid': 1000000046}, None
[I 250729 01:26:16 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:26:20 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:26:21 server:41790] Message: 61, 6247, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill221', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:26:23 server:41790] Message: 50, 6247, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill221', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:26:24 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:26:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.63ms
[I 250729 01:26:24 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:26:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.46ms
[I 250729 01:26:24 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:26:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.98ms
[I 250729 01:26:24 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:26:24 server:41790] Message: 154, 6247, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill221', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:26:25 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 348.23ms
[I 250729 01:26:26 server:41790] Message: 50, 6247, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill221', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:26:26 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:26:27 server:41790] Message: 50, 6247, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill221', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:26:29 server:41790] Message: 50, 6247, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill221', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:26:30 server:41790] Message: 50, 6247, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill221', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:26:30 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:26:32 server:41790] Message: 50, 6247, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill221', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:26:33 server:41790] Message: 50, 6247, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill221', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:26:35 server:41790] Message: 50, 6248, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill221', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:26:35 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:26:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:26:35 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:26:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 28.45ms
[I 250729 01:26:35 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:26:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.95ms
[I 250729 01:26:35 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:26:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 343.67ms
[I 250729 01:26:36 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:26:36 server:41790] Message: 50, 6248, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill221', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:26:38 server:41790] Message: 50, 6248, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill221', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:26:39 server:41790] Message: 51, 6248, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill221', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:26:41 server:41790] Message: 49, 6248, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill221', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:26:41 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:26:42 server:41790] Message: 50, 6248, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill221', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:26:43 server:41790] Message: 49, 6248, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill221', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:26:45 server:41790] Message: 49, 6248, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill221', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:26:46 server:41790] Message: 3, 11799, 1000000046, get_rank_by_type, 112.47.197.116, 1, {u'rank_type': u'power'}, None
[I 250729 01:26:46 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:26:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.61ms
[I 250729 01:26:46 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:26:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.30ms
[I 250729 01:26:46 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:26:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.97ms
[I 250729 01:26:46 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:26:46 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:26:46 server:41790] Message: 50, 6248, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill221', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:26:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 350.41ms
[I 250729 01:26:47 server:41790] Message: 23, 145784, 1000000046, get_rank_by_type, 112.47.197.116, 1, {u'rank_type': u'hero_power'}, None
[I 250729 01:26:48 server:41790] Message: 50, 6248, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill221', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:26:49 server:41790] Message: 50, 6248, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill221', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:26:50 server:41790] Message: 52, 6248, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill221', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:26:51 server:41790] Message: 50, 6248, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill221', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:26:51 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:26:52 server:41790] Message: 50, 6248, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill221', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:26:53 server:41790] Message: 2, 19378, 1000000046, user_info, 112.47.197.116, 1, {u'uid': 1000000046}, None
[I 250729 01:26:53 server:41790] Message: 50, 6248, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill221', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:26:54 server:41790] Message: 51, 6248, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill221', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:26:56 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:26:57 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:26:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:26:57 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:26:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.38ms
[I 250729 01:26:57 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:26:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.00ms
[I 250729 01:26:57 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:26:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 350.45ms
[I 250729 01:27:00 server:41790] Message: 42, 2329, 1000000046, army_science_rate_up, 112.47.197.116, 1, {u'up_num': 10, u'b_id': u'building012'}, None
[I 250729 01:27:01 server:41790] Message: 43, 2258, 1000000046, army_science_rate_safeup, 112.47.197.116, 1, {u'b_id': u'building012'}, None
[I 250729 01:27:02 server:41790] Message: 43, 2328, 1000000046, army_science_rate_up, 112.47.197.116, 1, {u'up_num': 10, u'b_id': u'building012'}, None
[I 250729 01:27:02 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:27:03 server:41790] Message: 43, 2328, 1000000046, army_science_rate_up, 112.47.197.116, 1, {u'up_num': 1, u'b_id': u'building012'}, None
[I 250729 01:27:03 server:41790] Message: 42, 2328, 1000000046, army_science_rate_up, 112.47.197.116, 1, {u'up_num': 1, u'b_id': u'building012'}, None
[I 250729 01:27:04 server:41790] Message: 118, 2258, 1000000046, army_science_rate_safeup, 112.47.197.116, 1, {u'b_id': u'building012'}, None
[I 250729 01:27:06 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:27:07 server:41790] Message: 109, 2328, 1000000046, army_science_rate_up, 112.47.197.116, 1, {u'up_num': 10, u'b_id': u'building011'}, None
[I 250729 01:27:07 server:41790] Message: 42, 2328, 1000000046, army_science_rate_up, 112.47.197.116, 1, {u'up_num': 10, u'b_id': u'building011'}, None
[I 250729 01:27:08 server:41790] Message: 61, 6263, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill220', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:27:08 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:27:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:27:08 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:27:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 52.03ms
[I 250729 01:27:08 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:27:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.01ms
[I 250729 01:27:08 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:27:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 354.01ms
[I 250729 01:27:09 server:41790] Message: 49, 6263, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill220', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:27:09 server:41790] Message: 42, 2328, 1000000046, army_science_rate_up, 112.47.197.116, 1, {u'up_num': 10, u'b_id': u'building009'}, None
[I 250729 01:27:10 server:41790] Message: 100, 2329, 1000000046, army_science_rate_up, 112.47.197.116, 1, {u'up_num': 10, u'b_id': u'building009'}, None
[I 250729 01:27:11 server:41790] Message: 50, 6263, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill220', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:27:11 server:41790] Message: 43, 2328, 1000000046, army_science_lv_up, 112.47.197.116, 1, {u'b_id': u'building009'}, None
[I 250729 01:27:12 server:41790] Message: 103, 2255, 1000000046, army_science_lv_safeup, 112.47.197.116, 1, {u'b_id': u'building009'}, None
[I 250729 01:27:12 server:41790] Message: 42, 6263, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill220', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:27:12 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:27:13 server:41790] Message: 50, 6263, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill220', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:27:15 server:41790] Message: 50, 6263, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill220', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:27:16 server:41790] Message: 42, 2327, 1000000046, army_science_rate_up, 112.47.197.116, 1, {u'up_num': 10, u'b_id': u'building010'}, None
[I 250729 01:27:16 server:41790] Message: 42, 2328, 1000000046, army_science_rate_up, 112.47.197.116, 1, {u'up_num': 10, u'b_id': u'building010'}, None
[I 250729 01:27:16 server:41790] Message: 49, 6263, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill220', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:27:16 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:27:17 server:41790] Message: 101, 2257, 1000000046, army_science_rate_safeup, 112.47.197.116, 1, {u'b_id': u'building010'}, None
[I 250729 01:27:19 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:27:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:27:19 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:27:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 49.80ms
[I 250729 01:27:19 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:27:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.03ms
[I 250729 01:27:19 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:27:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 350.89ms
[I 250729 01:27:20 server:41790] Message: 43, 2327, 1000000046, army_science_rate_up, 112.47.197.116, 1, {u'up_num': 10, u'b_id': u'building010'}, None
[I 250729 01:27:21 server:41790] Message: 50, 6263, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill220', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:27:22 server:41790] Message: 49, 6263, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill220', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:27:22 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:27:23 server:41790] Message: 2, 19378, 1000000046, user_info, 112.47.197.116, 1, {u'uid': 1000000046}, None
[I 250729 01:27:23 server:41790] Message: 50, 6264, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill220', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:27:25 server:41790] Message: 49, 6264, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill220', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:27:26 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:27:30 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:27:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.65ms
[I 250729 01:27:30 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:27:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 50.03ms
[I 250729 01:27:30 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:27:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.02ms
[I 250729 01:27:30 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:27:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 348.08ms
[I 250729 01:27:31 server:41790] Message: 13, 6264, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill220', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:27:32 server:41790] Message: 50, 6264, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill220', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:27:32 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:27:33 server:41790] Message: 50, 6264, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill220', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:27:35 server:41790] Message: 51, 6264, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill220', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:27:36 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:27:36 server:41790] Message: 50, 6264, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill220', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:27:40 server:41790] Message: 3, 11799, 1000000046, get_rank_by_type, 112.47.197.116, 1, {u'rank_type': u'power'}, None
[I 250729 01:27:41 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:27:41 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:27:41 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:27:41 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.07ms
[I 250729 01:27:41 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:27:41 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.02ms
[I 250729 01:27:41 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:27:41 server:41790] Message: 2, 18529, 1000000046, user_info, 112.47.197.116, 1, {u'uid': 1000000045}, None
[I 250729 01:27:41 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 341.53ms
[I 250729 01:27:41 server:41790] Message: 14, 6264, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill220', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:27:43 server:41790] Message: 50, 6264, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill220', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:27:43 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:27:44 server:41790] Message: 50, 6264, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill220', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:27:45 server:41790] Message: 51, 6264, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill220', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:27:46 server:41790] Message: 2, 19378, 1000000046, user_info, 112.47.197.116, 1, {u'uid': 1000000046}, None
[I 250729 01:27:46 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:27:46 server:41790] Message: 51, 6264, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill220', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:27:47 server:41790] Message: 51, 6264, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill220', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:27:48 server:41790] Message: 51, 6264, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill220', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:27:49 server:41790] Message: 52, 6264, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill220', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:27:50 server:41790] Message: 52, 6264, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill220', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:27:52 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:27:52 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.67ms
[I 250729 01:27:52 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:27:52 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.19ms
[I 250729 01:27:52 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:27:52 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.96ms
[I 250729 01:27:52 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:27:52 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 343.17ms
[I 250729 01:27:53 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:27:56 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:28:00 server:41790] Message: 2, 18001, 1000000046, user_info, 112.47.197.116, 1, {u'uid': 1000000103}, None
[I 250729 01:28:03 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:28:03 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.63ms
[I 250729 01:28:03 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:28:03 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.14ms
[I 250729 01:28:03 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:28:03 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.98ms
[I 250729 01:28:03 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:28:03 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:28:03 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 339.22ms
[I 250729 01:28:06 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:28:09 server:41790] Message: 2, 19378, 1000000046, user_info, 112.47.197.116, 1, {u'uid': 1000000046}, None
[I 250729 01:28:11 server:41790] Message: 60, 6279, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill273', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:28:13 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:28:14 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:28:14 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:28:14 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:28:14 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.16ms
[I 250729 01:28:14 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:28:14 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.98ms
[I 250729 01:28:14 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:28:14 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 345.69ms
[I 250729 01:28:14 server:41790] Message: 98, 6294, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill277', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:28:16 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:28:17 server:41790] Message: 97, 6309, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill280', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:28:20 server:41790] Message: 97, 6324, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill274', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:28:23 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:28:24 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:28:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:28:24 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:28:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.28ms
[I 250729 01:28:24 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:28:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.04ms
[I 250729 01:28:24 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:28:25 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 338.87ms
[I 250729 01:28:26 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:28:26 server:41790] Message: 62, 6339, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill271', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:28:33 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:28:35 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:28:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.65ms
[I 250729 01:28:35 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:28:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.08ms
[I 250729 01:28:35 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:28:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.98ms
[I 250729 01:28:35 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:28:36 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 346.92ms
[I 250729 01:28:36 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:28:40 server:41790] Message: 41, 106, 1000000046, try_to_pay, 112.47.197.116, 1, {u'pid': u'gd1573', u'ptype': u'limit_recharge'}, None
[I 250729 01:28:42 server:41902] api call: 118.178.139.38, user_pay, {'pid': u'gd1573', 'pay_id': u'gd1573|20250729012842|ucoin', 'uid': 46, 'zone': u'1'}
[I 250729 01:28:42 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 22.53ms
[I 250729 01:28:42 server:41790] Message: 0, 2250, 1000000046, get_club_redbag, 112.47.197.116, 1, {}, None
[I 250729 01:28:43 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:28:46 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:28:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.65ms
[I 250729 01:28:46 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:28:46 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:28:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 26.23ms
[I 250729 01:28:46 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:28:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.02ms
[I 250729 01:28:46 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:28:47 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 341.82ms
[I 250729 01:28:47 server:41790] Message: 62, 6354, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill260', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:28:49 server:41790] Message: 12, 6624, 1000000046, hero_skill_lv_up, 112.47.197.116, 1, {u'skill_id': u'skill211', u'hid': u'hero720', u'fast_learn': 0}, None
[I 250729 01:28:53 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:28:56 server:41790] Message: 42, 106, 1000000046, try_to_pay, 112.47.197.116, 1, {u'pid': u'gd1573', u'ptype': u'limit_recharge'}, None
[I 250729 01:28:56 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:28:57 server:41902] api call: 118.178.139.38, user_pay, {'pid': u'gd1573', 'pay_id': u'gd1573|20250729012857|ucoin', 'uid': 46, 'zone': u'1'}
[I 250729 01:28:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 22.68ms
[I 250729 01:28:57 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:28:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.52ms
[I 250729 01:28:57 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:28:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 49.91ms
[I 250729 01:28:57 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:28:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.93ms
[I 250729 01:28:57 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:28:58 server:41790] Message: 175, 106, 1000000046, try_to_pay, 112.47.197.116, 1, {u'pid': u'gd1573', u'ptype': u'limit_recharge'}, None
[I 250729 01:28:58 server:41790] Message: 0, 2250, 1000000046, get_club_redbag, 112.47.197.116, 1, {}, None
[I 250729 01:28:58 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 344.69ms
[I 250729 01:28:59 server:41790] Message: 62, 6369, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill272', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:29:00 server:41790] Message: 42, 106, 1000000046, try_to_pay, 112.47.197.116, 1, {u'pid': u'gd1573', u'ptype': u'limit_recharge'}, None
[I 250729 01:29:00 server:41902] api call: 118.178.139.38, user_pay, {'pid': u'gd1573', 'pay_id': u'gd1573|20250729012900|ucoin', 'uid': 46, 'zone': u'1'}
[I 250729 01:29:00 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 22.53ms
[I 250729 01:29:01 server:41790] Message: 0, 2250, 1000000046, get_club_redbag, 112.47.197.116, 1, {}, None
[I 250729 01:29:03 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:29:04 server:41790] Message: 14, 6625, 1000000046, hero_skill_lv_up, 112.47.197.116, 1, {u'skill_id': u'skill211', u'hid': u'hero720', u'fast_learn': 0}, None
[I 250729 01:29:06 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:29:08 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:29:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.60ms
[I 250729 01:29:08 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:29:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 49.97ms
[I 250729 01:29:08 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:29:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.95ms
[I 250729 01:29:08 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:29:09 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 347.35ms
[I 250729 01:29:10 server:41790] Message: 14, 6517, 1000000046, hero_skill_lv_up, 112.47.197.116, 1, {u'skill_id': u'skill206', u'hid': u'hero716', u'fast_learn': 0}, None
[I 250729 01:29:10 server:41790] Message: 11, 6516, 1000000046, hero_skill_lv_up, 112.47.197.116, 1, {u'skill_id': u'skill206', u'hid': u'hero716', u'fast_learn': 0}, None
[I 250729 01:29:12 server:41790] Message: 96, 6384, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill237', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:29:14 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:29:16 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:29:19 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:29:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.61ms
[I 250729 01:29:19 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:29:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 49.68ms
[I 250729 01:29:19 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:29:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.99ms
[I 250729 01:29:19 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:29:20 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 344.61ms
[I 250729 01:29:22 server:41790] Message: 70, 6399, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill242', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:29:24 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:29:26 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:29:27 server:41790] Message: 101, 6414, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill243', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:29:30 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:29:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.65ms
[I 250729 01:29:30 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:29:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.17ms
[I 250729 01:29:30 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:29:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.96ms
[I 250729 01:29:30 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:29:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 346.87ms
[I 250729 01:29:34 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:29:36 server:41790] Message: 43, 6458, 1000000046, hero_counter_lvup, 112.47.197.116, 1, {u'item_id': u'item7700', u'counter_index': 0, u'hid': u'hero716'}, None
[I 250729 01:29:36 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:29:38 server:41790] Message: 43, 6457, 1000000046, hero_counter_lvup, 112.47.197.116, 1, {u'item_id': u'item7700', u'counter_index': 0, u'hid': u'hero716'}, None
[I 250729 01:29:38 server:41790] Message: 61, 6429, 1000000045, hero_skill_lv_up, 123.139.57.61, 1, {u'skill_id': u'skill238', u'hid': u'hero764', u'fast_learn': 0}, None
[I 250729 01:29:41 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:29:41 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.63ms
[I 250729 01:29:41 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:29:41 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 50.58ms
[I 250729 01:29:41 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:29:41 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.14ms
[I 250729 01:29:41 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:29:41 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 350.89ms
[I 250729 01:29:42 server:41790] Message: 2, 19406, 1000000046, user_info, 112.47.197.116, 1, {u'uid': 1000000046}, None
[I 250729 01:29:44 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:29:46 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:29:49 server:41790] Message: 55, 1249, 1000000045, hero_fate, 123.139.57.61, 1, {u'hid': u'hero764', u'fate_id': u'fate7644'}, None
[I 250729 01:29:52 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:29:52 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:29:52 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:29:52 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 26.99ms
[I 250729 01:29:52 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:29:52 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.01ms
[I 250729 01:29:52 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:29:52 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 352.41ms
[I 250729 01:29:54 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:29:56 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:30:03 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:30:03 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:30:03 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:30:03 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.13ms
[I 250729 01:30:03 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:30:03 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.97ms
[I 250729 01:30:03 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:30:03 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 347.32ms
[I 250729 01:30:04 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:30:06 server:41790] Message: 0, 220, 1000000046, update_user, 112.47.197.116, 1, {u'key': [u'coin']}, None
[I 250729 01:30:08 server:41790] Message: 2, 19406, 1000000046, user_info, 112.47.197.116, 1, {u'uid': 1000000046}, None
[I 250729 01:30:13 server:41817] WebSocket closed, 1000000046
[I 250729 01:30:13 server:29822] on_logout, 1000000046
[I 250729 01:30:14 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:30:14 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.65ms
[I 250729 01:30:14 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:30:14 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 27.82ms
[I 250729 01:30:14 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:30:14 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.00ms
[I 250729 01:30:14 server:41790] Message: 0, 222, 1000000045, update_user, 123.139.57.61, 1, {u'key': [u'coin']}, None
[I 250729 01:30:14 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:30:14 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 342.69ms
