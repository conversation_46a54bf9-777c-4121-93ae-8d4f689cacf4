[I 250729 01:11:11 server:41790] Message: 13, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building010', u'cost': 0}, None
[I 250729 01:11:11 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:11:13 server:41790] Message: 13, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building009', u'cost': 0}, None
[I 250729 01:11:13 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:11:16 server:41790] Message: 13, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building011', u'cost': 0}, None
[I 250729 01:11:16 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:11:18 server:41902] api call: **************, get_server_status, {}
[I 250729 01:11:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.62ms
[I 250729 01:11:18 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:11:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 31.63ms
[I 250729 01:11:18 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:11:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.01ms
[I 250729 01:11:18 server:41902] api call: **************, backup_world, {}
[I 250729 01:11:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 114.29ms
[I 250729 01:11:18 server:41790] Message: 13, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building012', u'cost': 0}, None
[I 250729 01:11:18 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:11:19 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:11:19 server:41790] Message: 98, 5208, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill238', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:11:21 server:41790] Message: 0, 218, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:11:21 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building005', u'cost': 0}, None
[I 250729 01:11:21 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:11:24 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building002', u'cost': 0}, None
[I 250729 01:11:24 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:11:25 server:41817] WebSocket closed, 5000000265
[I 250729 01:11:25 server:29822] on_logout, 5000000265
[I 250729 01:11:25 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building002', u'cost': 0}, None
[I 250729 01:11:25 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:11:26 web:2162] 101 GET /gateway/ (*************) 0.43ms
[I 250729 01:11:26 server:41455] WebSocket opened
[I 250729 01:11:26 server:29800] on_login, 5000000265
[I 250729 01:11:26 server:41790] Message: 39, 229038, 5000000265, login, *************, 5, {u'uid': 265, u'zone': u'5', u'pf_key': u'a186934055', u'pf_data': None, u'user_code': u'', u'pf': u'developer', u'sessionid': u'f2635819f5e061cf1ed11d66448420f1|1753722496|developer'}, None
[I 250729 01:11:27 server:41902] api call: **************, get_use_merge, {'zone': u'5'}
[I 250729 01:11:27 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.57ms
[E 250729 01:11:27 concurrent:140] Future exception was never retrieved: CurlError: HTTP 599: Connection timed out after 1040 milliseconds
[I 250729 01:11:28 server:41790] Message: 62, 5223, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill235', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:11:28 server:41790] Message: 14, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building007', u'cost': 0}, None
[I 250729 01:11:29 server:41902] api call: **************, get_server_status, {}
[I 250729 01:11:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.56ms
[I 250729 01:11:29 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:11:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 31.21ms
[I 250729 01:11:29 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:11:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.92ms
[I 250729 01:11:29 server:41902] api call: **************, backup_world, {}
[I 250729 01:11:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 113.45ms
[I 250729 01:11:29 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:11:29 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:11:29 server:41790] Message: 52, 5223, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill235', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:11:31 server:41790] Message: 14, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building008', u'cost': 0}, None
[I 250729 01:11:31 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:11:36 server:41790] Message: 0, 218, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:11:37 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building021', u'cost': 0}, None
[I 250729 01:11:37 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:11:39 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:11:39 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building024', u'cost': 0}, None
[I 250729 01:11:39 server:41902] api call: **************, get_server_status, {}
[I 250729 01:11:39 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.66ms
[I 250729 01:11:39 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:11:39 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 32.58ms
[I 250729 01:11:39 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:11:39 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:11:39 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.01ms
[I 250729 01:11:39 server:41902] api call: **************, backup_world, {}
[I 250729 01:11:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 112.92ms
[I 250729 01:11:41 server:41790] Message: 12, 5223, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill229', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:11:41 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building023', u'cost': 0}, None
[I 250729 01:11:42 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:11:42 server:41790] Message: 49, 5223, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill229', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:11:42 server:41790] Message: 49, 5223, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill229', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:11:43 server:41790] Message: 49, 5223, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill229', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:11:43 server:41790] Message: 49, 5223, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill229', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:11:43 server:41790] Message: 49, 5223, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill229', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:11:43 server:41790] Message: 49, 5223, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill229', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:11:44 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building020', u'cost': 0}, None
[I 250729 01:11:44 server:41790] Message: 49, 5223, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill229', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:11:44 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:11:44 server:41790] Message: 49, 5224, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill229', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:11:45 server:41790] Message: 49, 5224, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill229', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:11:45 server:41790] Message: 48, 5224, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill229', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:11:45 server:41790] Message: 49, 5224, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill229', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:11:45 server:41790] Message: 49, 5224, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill229', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:11:46 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building019', u'cost': 0}, None
[I 250729 01:11:46 server:41790] Message: 49, 5224, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill229', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:11:46 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:11:46 server:41790] Message: 50, 5224, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill229', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:11:47 server:41790] Message: 49, 5224, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill229', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:11:47 server:41790] Message: 0, 218, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:11:47 server:41790] Message: 49, 5224, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill229', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:11:47 server:41790] Message: 49, 5224, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill229', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:11:48 server:41790] Message: 50, 5224, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill229', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:11:48 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building022', u'cost': 0}, None
[I 250729 01:11:48 server:41790] Message: 50, 5224, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill229', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:11:48 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:11:48 server:41790] Message: 49, 5224, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill229', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:11:49 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:11:50 server:41902] api call: **************, get_server_status, {}
[I 250729 01:11:50 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.63ms
[I 250729 01:11:50 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:11:50 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 34.42ms
[I 250729 01:11:50 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:11:50 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.00ms
[I 250729 01:11:50 server:41902] api call: **************, backup_world, {}
[I 250729 01:11:50 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 109.74ms
[I 250729 01:11:53 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building015', u'cost': 0}, None
[I 250729 01:11:53 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:11:55 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building018', u'cost': 0}, None
[I 250729 01:11:55 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:11:56 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:11:57 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building017', u'cost': 0}, None
[I 250729 01:11:58 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:11:59 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:12:00 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building016', u'cost': 0}, None
[I 250729 01:12:00 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:12:01 server:41902] api call: **************, get_server_status, {}
[I 250729 01:12:01 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.66ms
[I 250729 01:12:01 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:12:01 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 8.08ms
[I 250729 01:12:01 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:12:01 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.94ms
[I 250729 01:12:01 server:41902] api call: **************, backup_world, {}
[I 250729 01:12:01 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 106.21ms
[I 250729 01:12:01 server:41790] Message: 58, 5454, 5000000265, use_prop, *************, 5, {u'item_id': -1, u'item_num': 1, u'range_index': -1}, None
[I 250729 01:12:03 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building014', u'cost': 0}, None
[I 250729 01:12:04 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:12:06 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building013', u'cost': 0}, None
[I 250729 01:12:06 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:12:06 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:12:09 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:12:10 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building003', u'cost': 0}, None
[I 250729 01:12:10 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:12:11 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building003', u'cost': 0}, None
[I 250729 01:12:11 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:12:12 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building003', u'cost': 0}, None
[I 250729 01:12:12 server:41902] api call: **************, get_server_status, {}
[I 250729 01:12:12 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.62ms
[I 250729 01:12:12 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:12:12 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 32.66ms
[I 250729 01:12:12 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:12:12 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.06ms
[I 250729 01:12:12 server:41902] api call: **************, backup_world, {}
[I 250729 01:12:12 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:12:12 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 112.45ms
[I 250729 01:12:15 server:41790] Message: 3, 19494, 5000000314, building_lvup, *************, 5, {u'bid': u'building001', u'cost': 0}, None
[I 250729 01:12:15 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:12:16 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:12:19 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:12:23 server:41902] api call: **************, get_server_status, {}
[I 250729 01:12:23 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.65ms
[I 250729 01:12:23 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:12:23 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 7.77ms
[I 250729 01:12:23 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:12:23 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.01ms
[I 250729 01:12:23 server:41902] api call: **************, backup_world, {}
[I 250729 01:12:23 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 108.14ms
[I 250729 01:12:26 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:12:29 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:12:29 server:41790] Message: 1, 3614, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:29 server:41790] Message: 1, 3614, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:30 server:41790] Message: 1, 3614, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:30 server:41790] Message: 1, 3616, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:30 server:41790] Message: 1, 3617, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:30 server:41790] Message: 1, 3616, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:31 server:41790] Message: 1, 3617, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:31 server:41790] Message: 2, 3617, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:31 server:41790] Message: 1, 3617, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:31 server:41790] Message: 46, 4556, 5000000265, use_prop, *************, 5, {u'item_id': u'item006', u'item_num': 483, u'range_index': 0}, None
[I 250729 01:12:31 server:41790] Message: 1, 3617, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:32 server:41790] Message: 1, 3617, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:32 server:41790] Message: 1, 3617, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:32 server:41790] Message: 1, 3617, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:32 server:41790] Message: 1, 3617, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:32 server:41790] Message: 1, 3617, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:33 server:41790] Message: 3, 3618, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:33 server:41790] Message: 3, 3618, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:33 server:41790] Message: 1, 3617, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:33 server:41790] Message: 1, 3617, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:34 server:41902] api call: **************, get_server_status, {}
[I 250729 01:12:34 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.60ms
[I 250729 01:12:34 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:12:34 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 32.11ms
[I 250729 01:12:34 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:12:34 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.99ms
[I 250729 01:12:34 server:41902] api call: **************, backup_world, {}
[I 250729 01:12:34 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 109.72ms
[I 250729 01:12:36 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:12:39 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:12:43 server:41790] Message: 1, 3648, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item765', u'resolve_num': 10}, None
[I 250729 01:12:43 server:41790] Message: 1, 3648, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item765', u'resolve_num': 10}, None
[I 250729 01:12:43 server:41790] Message: 1, 3648, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item765', u'resolve_num': 10}, None
[I 250729 01:12:44 server:41790] Message: 1, 3647, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item765', u'resolve_num': 10}, None
[I 250729 01:12:44 server:41790] Message: 1, 3648, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item765', u'resolve_num': 10}, None
[I 250729 01:12:44 server:41790] Message: 1, 3648, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item765', u'resolve_num': 10}, None
[I 250729 01:12:45 server:41902] api call: **************, get_server_status, {}
[I 250729 01:12:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.62ms
[I 250729 01:12:45 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:12:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 7.81ms
[I 250729 01:12:45 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:12:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.05ms
[I 250729 01:12:45 server:41902] api call: **************, backup_world, {}
[I 250729 01:12:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 109.97ms
[I 250729 01:12:46 server:41790] Message: 1, 3648, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:46 server:41790] Message: 1, 3648, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:46 server:41790] Message: 1, 3647, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:46 server:41790] Message: 1, 3647, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:46 server:41790] Message: 1, 3647, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:47 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:12:47 server:41790] Message: 1, 3646, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:47 server:41790] Message: 1, 3647, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:47 server:41790] Message: 1, 3648, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:47 server:41790] Message: 1, 3646, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:48 server:41790] Message: 1, 3647, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:48 server:41790] Message: 1, 3648, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:48 server:41790] Message: 1, 3648, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:48 server:41790] Message: 1, 3647, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:48 server:41790] Message: 1, 3646, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:49 server:41790] Message: 1, 3646, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:49 server:41790] Message: 1, 3647, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:49 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:12:49 server:41790] Message: 1, 3647, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:49 server:41790] Message: 1, 3647, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:50 server:41790] Message: 1, 3648, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:50 server:41790] Message: 1, 3648, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:50 server:41790] Message: 1, 3646, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:50 server:41790] Message: 1, 3648, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:51 server:41790] Message: 1, 3647, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:51 server:41790] Message: 1, 3648, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:51 server:41790] Message: 1, 3647, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:51 server:41790] Message: 1, 3648, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:51 server:41790] Message: 1, 3646, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:52 server:41790] Message: 1, 3647, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:52 server:41790] Message: 1, 3646, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:52 server:41790] Message: 1, 3647, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:52 server:41790] Message: 1, 3647, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:53 server:41790] Message: 1, 3646, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:53 server:41790] Message: 1, 3646, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:53 server:41790] Message: 1, 3647, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:53 server:41790] Message: 1, 3647, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:53 server:41790] Message: 1, 3647, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:54 server:41790] Message: 1, 3647, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:54 server:41790] Message: 1, 3645, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:54 server:41790] Message: 1, 3646, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:54 server:41790] Message: 1, 3646, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:55 server:41790] Message: 1, 3645, 5000000314, hero_resolve, *************, 5, {u'item_id': u'item718', u'resolve_num': 10}, None
[I 250729 01:12:56 server:41902] api call: **************, get_server_status, {}
[I 250729 01:12:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.63ms
[I 250729 01:12:56 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:12:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 8.11ms
[I 250729 01:12:56 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:12:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.99ms
[I 250729 01:12:56 server:41902] api call: **************, backup_world, {}
[I 250729 01:12:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 112.14ms
[I 250729 01:12:57 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:12:59 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:13:01 server:41790] Message: 20, 3317, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero768'}, None
[I 250729 01:13:01 server:41790] Message: 17, 3317, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero768'}, None
[I 250729 01:13:02 server:41790] Message: 17, 3317, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero768'}, None
[I 250729 01:13:02 server:41790] Message: 17, 3317, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero768'}, None
[I 250729 01:13:02 server:41790] Message: 17, 3317, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero768'}, None
[I 250729 01:13:03 server:41790] Message: 18, 3317, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero768'}, None
[I 250729 01:13:03 server:41790] Message: 17, 3318, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero768'}, None
[I 250729 01:13:03 server:41790] Message: 17, 3318, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero768'}, None
[I 250729 01:13:04 server:41790] Message: 17, 3318, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero768'}, None
[I 250729 01:13:04 server:41790] Message: 18, 3320, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero768'}, None
[I 250729 01:13:04 server:41790] Message: 17, 3319, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero768'}, None
[I 250729 01:13:05 server:41790] Message: 18, 3319, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero768'}, None
[I 250729 01:13:07 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:13:07 server:41790] Message: 17, 3319, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero768'}, None
[I 250729 01:13:07 server:41902] api call: **************, get_server_status, {}
[I 250729 01:13:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.53ms
[I 250729 01:13:07 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:13:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 7.66ms
[I 250729 01:13:07 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:13:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.92ms
[I 250729 01:13:07 server:41902] api call: **************, backup_world, {}
[I 250729 01:13:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 111.90ms
[I 250729 01:13:07 server:41790] Message: 18, 3319, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero768'}, None
[I 250729 01:13:08 server:41790] Message: 17, 3319, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero768'}, None
[I 250729 01:13:08 server:41790] Message: 18, 3319, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero768'}, None
[I 250729 01:13:09 server:41790] Message: 17, 3319, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero768'}, None
[I 250729 01:13:09 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:13:17 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:13:18 server:41902] api call: **************, get_server_status, {}
[I 250729 01:13:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.63ms
[I 250729 01:13:18 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:13:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 7.84ms
[I 250729 01:13:18 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:13:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.98ms
[I 250729 01:13:18 server:41902] api call: **************, backup_world, {}
[I 250729 01:13:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 111.28ms
[I 250729 01:13:19 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:13:19 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:19 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:20 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:20 server:41790] Message: 1, 4831, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:21 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:21 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:21 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:21 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:21 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:21 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:21 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:22 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:22 server:41790] Message: 1, 4831, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:22 server:41790] Message: 1, 4831, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:22 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:22 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:22 server:41790] Message: 1, 4831, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:22 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:23 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:23 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:23 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:23 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:23 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:23 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:23 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:24 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:24 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:24 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:24 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:24 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:25 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:25 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:25 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:25 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:25 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:25 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:26 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:26 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:26 server:41790] Message: 1, 4831, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:26 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:26 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:27 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:13:27 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:27 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:27 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:27 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:27 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:27 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:28 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:28 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:28 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:28 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:28 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:28 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:28 server:41902] api call: **************, get_server_status, {}
[I 250729 01:13:28 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.54ms
[I 250729 01:13:28 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:13:28 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 25.84ms
[I 250729 01:13:28 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:13:28 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.97ms
[I 250729 01:13:29 server:41902] api call: **************, backup_world, {}
[I 250729 01:13:29 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 110.79ms
[I 250729 01:13:29 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:29 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:29 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:13:29 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:29 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:29 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:30 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:30 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:30 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:30 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:30 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:30 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:31 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:31 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:31 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:31 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:31 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:31 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:32 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:32 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:32 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:32 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:32 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:32 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:33 server:41790] Message: 2, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:33 server:41790] Message: 2, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:33 server:41790] Message: 2, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:33 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:33 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:33 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:34 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:34 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:34 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:34 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:34 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:35 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:35 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:35 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:35 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:35 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:35 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:36 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:36 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:36 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:36 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:36 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:36 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:37 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:13:37 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:37 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:37 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:37 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:37 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:37 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:38 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:38 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:39 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:13:39 server:41902] api call: **************, get_server_status, {}
[I 250729 01:13:39 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.60ms
[I 250729 01:13:39 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:13:39 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 26.73ms
[I 250729 01:13:39 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:13:39 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.03ms
[I 250729 01:13:39 server:41902] api call: **************, backup_world, {}
[I 250729 01:13:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 116.08ms
[I 250729 01:13:40 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:40 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:41 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:41 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:41 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:42 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:42 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:42 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:42 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:42 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:43 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:43 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:43 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:44 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:44 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:44 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:44 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:44 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:44 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:47 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:47 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:13:47 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:47 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:47 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:48 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:48 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:48 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:48 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:48 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:49 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:49 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:49 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:49 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:49 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:13:49 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:49 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:50 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:50 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:50 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:50 server:41902] api call: **************, get_server_status, {}
[I 250729 01:13:50 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.53ms
[I 250729 01:13:50 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:13:50 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 26.57ms
[I 250729 01:13:50 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:13:50 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.04ms
[I 250729 01:13:50 server:41902] api call: **************, backup_world, {}
[I 250729 01:13:50 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:50 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 111.69ms
[I 250729 01:13:51 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:51 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:51 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:51 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:51 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:51 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:52 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:52 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:52 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:52 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:52 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:53 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:53 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:53 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:53 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:53 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:53 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:54 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:54 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:54 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:54 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:54 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:54 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:55 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:55 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:55 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:55 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:55 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:55 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:56 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:56 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:56 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:56 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:56 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:57 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:13:57 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:57 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:57 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:57 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:57 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:58 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:58 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:58 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:58 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:58 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:59 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:59 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:59 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:59 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:13:59 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:13:59 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:00 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:00 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:01 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:01 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:01 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:01 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:01 server:41902] api call: **************, get_server_status, {}
[I 250729 01:14:01 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.54ms
[I 250729 01:14:01 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:14:01 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 25.79ms
[I 250729 01:14:01 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:14:01 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.93ms
[I 250729 01:14:01 server:41902] api call: **************, backup_world, {}
[I 250729 01:14:01 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:01 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 105.04ms
[I 250729 01:14:02 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:02 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:02 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:02 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:02 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:03 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:03 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:03 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:03 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:04 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:04 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:04 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:04 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:05 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:05 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:05 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:05 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:06 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:06 server:41790] Message: 1, 2956, 5000000314, building_lvup, *************, 5, {u'bid': u'building002', u'cost': 0}, None
[I 250729 01:14:06 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:06 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:14:06 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:06 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:07 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:14:07 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:07 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:08 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:08 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:09 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:09 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:09 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:09 server:41790] Message: 15, 2956, 5000000314, building_lvup, *************, 5, {u'bid': u'building012', u'cost': 0}, None
[I 250729 01:14:09 server:41790] Message: 1, 4833, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:09 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:09 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:09 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:14:09 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:14:10 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:10 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:10 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:10 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:10 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:11 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:11 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:11 server:41790] Message: 1, 4831, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item714', u'resolve_num': 10}, None
[I 250729 01:14:12 server:41902] api call: **************, get_server_status, {}
[I 250729 01:14:12 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.61ms
[I 250729 01:14:12 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:14:12 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 32.19ms
[I 250729 01:14:12 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:14:12 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.01ms
[I 250729 01:14:12 server:41902] api call: **************, backup_world, {}
[I 250729 01:14:12 server:41790] Message: 39, 2956, 5000000314, building_lvup, *************, 5, {u'bid': u'building010', u'cost': 0}, None
[I 250729 01:14:12 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 109.79ms
[I 250729 01:14:13 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:14:14 server:41790] Message: 50, 4963, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill229', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:14:14 server:41790] Message: 50, 4963, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill229', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:14:15 server:41790] Message: 50, 4963, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill229', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:14:16 server:41790] Message: 12, 2956, 5000000314, building_lvup, *************, 5, {u'bid': u'building009', u'cost': 0}, None
[I 250729 01:14:16 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:14:17 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:14:17 server:41790] Message: 49, 4963, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill230', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:14:18 server:41790] Message: 49, 4963, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill230', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:14:18 server:41790] Message: 49, 4963, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill230', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:14:18 server:41790] Message: 49, 4963, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill230', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:14:19 server:41790] Message: 49, 4963, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill230', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:14:19 server:41790] Message: 48, 4963, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill230', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:14:19 server:41790] Message: 49, 4963, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill230', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:14:19 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:14:20 server:41790] Message: 49, 4963, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill230', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:14:20 server:41790] Message: 16, 2956, 5000000314, building_lvup, *************, 5, {u'bid': u'building011', u'cost': 0}, None
[I 250729 01:14:20 server:41790] Message: 48, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill230', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:14:20 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:14:20 server:41790] Message: 48, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill230', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:14:21 server:41790] Message: 49, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill230', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:14:21 server:41790] Message: 49, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill230', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:14:21 server:41790] Message: 49, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill230', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:14:22 server:41790] Message: 49, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill230', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:14:22 server:41790] Message: 49, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill230', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:14:22 server:41790] Message: 49, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill230', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:14:23 server:41790] Message: 49, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill230', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:14:23 server:41790] Message: 48, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill230', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:14:23 server:41790] Message: 50, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill230', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:14:23 server:41902] api call: **************, get_server_status, {}
[I 250729 01:14:23 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.59ms
[I 250729 01:14:23 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:14:23 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 31.71ms
[I 250729 01:14:23 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:14:23 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.01ms
[I 250729 01:14:23 server:41902] api call: **************, backup_world, {}
[I 250729 01:14:23 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 107.32ms
[I 250729 01:14:23 server:41790] Message: 49, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill230', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:14:23 server:41790] Message: 1, 2956, 5000000314, building_lvup, *************, 5, {u'bid': u'building013', u'cost': 0}, None
[I 250729 01:14:24 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:14:24 server:41790] Message: 50, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill230', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:14:24 server:41790] Message: 51, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill230', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:14:24 server:41790] Message: 50, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill230', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:14:25 server:41790] Message: 49, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill230', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:14:26 server:41790] Message: 1, 2956, 5000000314, building_lvup, *************, 5, {u'bid': u'building014', u'cost': 0}, None
[I 250729 01:14:26 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:14:27 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:14:28 server:41790] Message: 1, 2956, 5000000314, building_lvup, *************, 5, {u'bid': u'building015', u'cost': 0}, None
[I 250729 01:14:28 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:14:29 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:14:31 server:41790] Message: 1, 2956, 5000000314, building_lvup, *************, 5, {u'bid': u'building018', u'cost': 0}, None
[I 250729 01:14:31 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:14:34 server:41790] Message: 1, 2956, 5000000314, building_lvup, *************, 5, {u'bid': u'building017', u'cost': 0}, None
[I 250729 01:14:34 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:14:34 server:41902] api call: **************, get_server_status, {}
[I 250729 01:14:34 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.59ms
[I 250729 01:14:34 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:14:34 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 31.90ms
[I 250729 01:14:34 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:14:34 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.94ms
[I 250729 01:14:34 server:41902] api call: **************, backup_world, {}
[I 250729 01:14:34 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 113.59ms
[I 250729 01:14:36 server:41790] Message: 1, 2956, 5000000314, building_lvup, *************, 5, {u'bid': u'building016', u'cost': 0}, None
[I 250729 01:14:36 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:14:37 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:14:39 server:41790] Message: 1, 2956, 5000000314, building_lvup, *************, 5, {u'bid': u'building024', u'cost': 0}, None
[I 250729 01:14:39 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:14:40 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:14:42 server:41790] Message: 1, 2956, 5000000314, building_lvup, *************, 5, {u'bid': u'building021', u'cost': 0}, None
[I 250729 01:14:43 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:14:45 server:41790] Message: 1, 2956, 5000000314, building_lvup, *************, 5, {u'bid': u'building020', u'cost': 0}, None
[I 250729 01:14:45 server:41902] api call: **************, get_server_status, {}
[I 250729 01:14:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.58ms
[I 250729 01:14:45 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:14:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 8.07ms
[I 250729 01:14:45 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:14:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.05ms
[I 250729 01:14:45 server:41902] api call: **************, backup_world, {}
[I 250729 01:14:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 111.48ms
[I 250729 01:14:45 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:14:47 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:14:47 server:41790] Message: 1, 2956, 5000000314, building_lvup, *************, 5, {u'bid': u'building023', u'cost': 0}, None
[I 250729 01:14:47 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:14:48 server:41790] Message: 46, 4558, 5000000265, use_prop, *************, 5, {u'item_id': u'item006', u'item_num': 444, u'range_index': 2}, None
[I 250729 01:14:49 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:14:50 server:41790] Message: 1, 2956, 5000000314, building_lvup, *************, 5, {u'bid': u'building022', u'cost': 0}, None
[I 250729 01:14:50 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:14:52 server:41790] Message: 1, 2956, 5000000314, building_lvup, *************, 5, {u'bid': u'building019', u'cost': 0}, None
[I 250729 01:14:52 server:41790] Message: 0, 2040, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:14:56 server:41902] api call: **************, get_server_status, {}
[I 250729 01:14:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.60ms
[I 250729 01:14:56 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:14:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 31.58ms
[I 250729 01:14:56 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:14:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.05ms
[I 250729 01:14:56 server:41902] api call: **************, backup_world, {}
[I 250729 01:14:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 111.95ms
[I 250729 01:14:56 server:41790] Message: 3, 19135, 5000000314, building_lvup, *************, 5, {u'bid': u'building001', u'cost': 0}, None
[I 250729 01:14:57 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:14:57 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:14:57 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:14:57 server:41790] Message: 2, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:14:57 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:14:58 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:14:58 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:14:58 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:14:58 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:14:58 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:14:58 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:14:58 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:14:58 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:14:59 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:14:59 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:14:59 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:14:59 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:15:02 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:02 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:03 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:03 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:04 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:04 server:41790] Message: 1, 2956, 5000000314, building_lvup, *************, 5, {u'bid': u'building003', u'cost': 0}, None
[I 250729 01:15:04 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:04 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:15:04 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:04 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:04 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:05 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:05 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:06 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:06 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:07 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:15:07 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:07 server:41902] api call: **************, get_server_status, {}
[I 250729 01:15:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.61ms
[I 250729 01:15:07 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:15:07 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 58.51ms
[I 250729 01:15:07 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:15:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.02ms
[I 250729 01:15:07 server:41902] api call: **************, backup_world, {}
[I 250729 01:15:07 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 112.67ms
[I 250729 01:15:07 server:41790] Message: 2, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:07 server:41790] Message: 19, 2956, 5000000314, building_lvup, *************, 5, {u'bid': u'building010', u'cost': 0}, None
[I 250729 01:15:07 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:08 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:08 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:15:08 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:08 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:08 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:08 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:08 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:09 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:09 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:09 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:15:10 server:41790] Message: 14, 2956, 5000000314, building_lvup, *************, 5, {u'bid': u'building009', u'cost': 0}, None
[I 250729 01:15:10 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:10 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:10 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:15:10 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:10 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:11 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:11 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:11 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:11 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:11 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:12 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:12 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:13 server:41790] Message: 14, 2956, 5000000314, building_lvup, *************, 5, {u'bid': u'building011', u'cost': 0}, None
[I 250729 01:15:13 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:13 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:15:13 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:13 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:13 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:13 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:13 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:14 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:14 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:14 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:14 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:14 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:14 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:15 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:15 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:15 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:15 server:41790] Message: 13, 2956, 5000000314, building_lvup, *************, 5, {u'bid': u'building012', u'cost': 0}, None
[I 250729 01:15:15 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:15 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:15:15 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:16 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:16 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:16 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:16 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:16 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:16 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:16 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:17 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:17 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:15:17 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:17 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:17 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:17 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:17 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:18 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:18 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:18 server:41902] api call: **************, get_server_status, {}
[I 250729 01:15:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.62ms
[I 250729 01:15:18 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:15:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 39.59ms
[I 250729 01:15:18 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:15:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.07ms
[I 250729 01:15:18 server:41902] api call: **************, backup_world, {}
[I 250729 01:15:18 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 112.21ms
[I 250729 01:15:18 server:41790] Message: 1, 2956, 5000000314, building_lvup, *************, 5, {u'bid': u'building022', u'cost': 0}, None
[I 250729 01:15:18 server:41790] Message: 2, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:18 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:15:18 server:41790] Message: 2, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:18 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:19 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:19 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:19 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:19 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:19 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:19 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:19 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:20 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:15:20 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:20 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:20 server:41790] Message: 1, 2956, 5000000314, building_lvup, *************, 5, {u'bid': u'building019', u'cost': 0}, None
[I 250729 01:15:20 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:20 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:20 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:15:20 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:21 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:21 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:21 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:21 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:21 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:21 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:22 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:22 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:22 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:22 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:22 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:22 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:23 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:23 server:41790] Message: 1, 2956, 5000000314, building_lvup, *************, 5, {u'bid': u'building020', u'cost': 0}, None
[I 250729 01:15:23 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:23 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:15:23 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:23 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:23 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:24 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:24 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:24 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:24 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:24 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:24 server:41790] Message: 1, 4837, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:25 server:41790] Message: 1, 2956, 5000000314, building_lvup, *************, 5, {u'bid': u'building023', u'cost': 0}, None
[I 250729 01:15:25 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:25 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:25 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:15:25 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:25 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:25 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:25 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:26 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:26 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:26 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:26 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:26 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:27 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:15:27 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:27 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:27 server:41790] Message: 1, 2956, 5000000314, building_lvup, *************, 5, {u'bid': u'building024', u'cost': 0}, None
[I 250729 01:15:27 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:27 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:15:27 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:28 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:29 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:29 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:29 server:41902] api call: **************, get_server_status, {}
[I 250729 01:15:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.52ms
[I 250729 01:15:29 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:15:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 31.31ms
[I 250729 01:15:29 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:15:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.02ms
[I 250729 01:15:29 server:41902] api call: **************, backup_world, {}
[I 250729 01:15:29 server:41790] Message: 7, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 111.14ms
[I 250729 01:15:29 server:41790] Message: 2, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:29 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:29 server:41790] Message: 1, 2956, 5000000314, building_lvup, *************, 5, {u'bid': u'building021', u'cost': 0}, None
[I 250729 01:15:29 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:30 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:15:30 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:15:30 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:30 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:30 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:30 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:30 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:31 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:31 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:31 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:31 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:31 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:32 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:32 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:32 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:32 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:32 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:32 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:33 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:33 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:33 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:33 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:33 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:34 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:34 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:34 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:34 server:41790] Message: 5, 787, 5000000314, office_lv_up, *************, 5, {}, None
[I 250729 01:15:34 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:35 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:35 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:35 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:35 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:35 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:36 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:36 server:41790] Message: 6, 1071, 5000000314, office_right_unblock, *************, 5, {u'right_id': u'601'}, None
[I 250729 01:15:36 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:36 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:36 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:36 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:37 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:37 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:15:37 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:37 server:41790] Message: 6, 1077, 5000000314, office_right_unblock, *************, 5, {u'right_id': u'602'}, None
[I 250729 01:15:38 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:38 server:41790] Message: 6, 1084, 5000000314, office_right_unblock, *************, 5, {u'right_id': u'603'}, None
[I 250729 01:15:38 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:38 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:38 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:39 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:39 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:39 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:40 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:40 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:15:40 server:41902] api call: **************, get_server_status, {}
[I 250729 01:15:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.54ms
[I 250729 01:15:40 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:15:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 31.91ms
[I 250729 01:15:40 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:15:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.96ms
[I 250729 01:15:40 server:41902] api call: **************, backup_world, {}
[I 250729 01:15:40 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 108.91ms
[I 250729 01:15:40 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:40 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:40 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:40 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:41 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:41 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:41 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:41 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:41 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:42 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:42 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:42 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:42 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building014', u'cost': 0}, None
[I 250729 01:15:43 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:43 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:15:43 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:43 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:43 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:43 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:44 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:44 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:44 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:44 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:44 server:41790] Message: 1, 4836, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:45 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building015', u'cost': 0}, None
[I 250729 01:15:45 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:45 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:45 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:15:45 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:45 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:46 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:46 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:46 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:46 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:46 server:41790] Message: 1, 4835, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:46 server:41790] Message: 1, 4834, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item761', u'resolve_num': 10}, None
[I 250729 01:15:47 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:15:47 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building018', u'cost': 0}, None
[I 250729 01:15:47 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:15:49 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building017', u'cost': 0}, None
[I 250729 01:15:49 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:15:50 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:15:51 server:41902] api call: **************, get_server_status, {}
[I 250729 01:15:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.64ms
[I 250729 01:15:51 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:15:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 32.37ms
[I 250729 01:15:51 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:15:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.98ms
[I 250729 01:15:51 server:41902] api call: **************, backup_world, {}
[I 250729 01:15:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 104.33ms
[I 250729 01:15:51 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building016', u'cost': 0}, None
[I 250729 01:15:52 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:15:53 server:41790] Message: 13, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill233', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:15:54 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building013', u'cost': 0}, None
[I 250729 01:15:54 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill233', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:15:54 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:15:54 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill233', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:15:55 server:41790] Message: 50, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill233', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:15:55 server:41790] Message: 50, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill233', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:15:56 server:41790] Message: 51, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill233', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:15:56 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill233', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:15:57 server:41790] Message: 50, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill233', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:15:57 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:15:57 server:41790] Message: 50, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill233', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:00 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:16:00 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill235', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:00 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill235', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:01 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill235', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:01 server:41790] Message: 50, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill235', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:01 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building006', u'cost': 0}, None
[I 250729 01:16:01 server:41902] api call: **************, get_server_status, {}
[I 250729 01:16:01 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.55ms
[I 250729 01:16:02 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:16:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 31.68ms
[I 250729 01:16:02 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:16:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.16ms
[I 250729 01:16:02 server:41902] api call: **************, backup_world, {}
[I 250729 01:16:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 114.24ms
[I 250729 01:16:02 server:41790] Message: 50, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill235', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:02 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:16:02 server:41790] Message: 50, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill235', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:02 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill235', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:03 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill235', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:04 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill235', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:04 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill235', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:04 server:41790] Message: 50, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill235', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:05 web:2162] 101 GET /gateway/ (39.144.137.235) 0.40ms
[I 250729 01:16:05 server:41455] WebSocket opened
[I 250729 01:16:05 server:29800] on_login, 5000000299
[I 250729 01:16:05 server:41790] Message: 9, 98980, 5000000299, login, 39.144.137.235, 5, {u'uid': 299, u'zone': u'5', u'pf_key': u'13666268530', u'pf_data': None, u'user_code': u'', u'pf': u'developer', u'sessionid': u'b0d2d15597ac843147ad77286724f41e|1753721229|developer'}, None
[I 250729 01:16:05 server:41790] Message: 48, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill235', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:05 server:41902] api call: **************, get_use_merge, {'zone': u'5'}
[I 250729 01:16:05 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.58ms
[I 250729 01:16:05 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill235', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:06 server:41817] WebSocket closed, 5000000299
[I 250729 01:16:06 server:29822] on_logout, 5000000299
[E 250729 01:16:06 concurrent:140] Future exception was never retrieved: CurlError: HTTP 599: Connection timed out after 1039 milliseconds
[I 250729 01:16:06 server:41790] Message: 50, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill235', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:06 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill235', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:06 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill235', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:07 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:16:07 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill235', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:07 server:41790] Message: 50, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill235', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:08 server:41790] Message: 50, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill235', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:08 server:41790] Message: 50, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill235', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:09 server:41902] api call: **************, user_pay, {'pid': u'pay2', 'pay_id': u'pay2|20250729011609|ucoin', 'uid': 314, 'zone': u'5'}
[I 250729 01:16:09 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 3.68ms
[I 250729 01:16:10 server:41790] Message: 0, 2353, 5000000314, get_club_redbag, *************, 5, {}, None
[I 250729 01:16:10 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:16:11 server:41790] Message: 7, 10114, 5000000314, get_week_card_gift, *************, 5, {}, None
[I 250729 01:16:12 server:41902] api call: **************, get_server_status, {}
[I 250729 01:16:12 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.63ms
[I 250729 01:16:12 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:16:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 42.80ms
[I 250729 01:16:13 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:16:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.07ms
[I 250729 01:16:13 server:41902] api call: **************, backup_world, {}
[I 250729 01:16:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 113.96ms
[I 250729 01:16:14 server:41790] Message: 7, 12991, 5000000314, get_lvup_reward, *************, 5, {u'reward_key': 9}, None
[I 250729 01:16:14 server:41790] Message: 7, 12959, 5000000314, get_lvup_reward, *************, 5, {u'reward_key': 10}, None
[I 250729 01:16:15 server:41790] Message: 7, 12911, 5000000314, get_lvup_reward, *************, 5, {u'reward_key': 11}, None
[I 250729 01:16:15 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:16 server:41790] Message: 1, 4831, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:16 server:41790] Message: 7, 12875, 5000000314, get_lvup_reward, *************, 5, {u'reward_key': 12}, None
[I 250729 01:16:16 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:16 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:16 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:16 server:41790] Message: 7, 12843, 5000000314, get_lvup_reward, *************, 5, {u'reward_key': 13}, None
[I 250729 01:16:16 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:16 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:17 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:17 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:16:17 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:17 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:17 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:18 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:18 server:41790] Message: 1, 4831, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:18 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:18 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:18 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:18 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:19 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:19 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:19 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:19 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:19 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:19 server:41790] Message: 1, 4831, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:20 server:41790] Message: 1, 4831, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:20 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:16:20 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:20 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:20 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:20 server:41790] Message: 1, 4831, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:21 server:41790] Message: 3, 19010, 5000000314, building_lvup, *************, 5, {u'bid': u'building001', u'cost': 0}, None
[I 250729 01:16:21 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:16:23 server:41902] api call: **************, get_server_status, {}
[I 250729 01:16:23 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.63ms
[I 250729 01:16:23 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:16:23 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 31.72ms
[I 250729 01:16:23 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:16:23 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.02ms
[I 250729 01:16:23 server:41902] api call: **************, backup_world, {}
[I 250729 01:16:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 103.90ms
[I 250729 01:16:26 server:41790] Message: 14, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill235', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:27 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:16:28 server:41790] Message: 13, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building010', u'cost': 0}, None
[I 250729 01:16:28 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:16:30 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:16:30 server:41790] Message: 14, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building009', u'cost': 0}, None
[I 250729 01:16:31 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:16:33 server:41790] Message: 2, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:33 server:41790] Message: 16, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building011', u'cost': 0}, None
[I 250729 01:16:33 server:41790] Message: 2, 4831, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:33 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:33 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:16:33 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:34 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:34 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:34 server:41790] Message: 1, 4831, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:34 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:34 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:34 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:34 server:41902] api call: **************, get_server_status, {}
[I 250729 01:16:34 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.56ms
[I 250729 01:16:34 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:16:34 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 31.56ms
[I 250729 01:16:34 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:16:34 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.05ms
[I 250729 01:16:34 server:41902] api call: **************, backup_world, {}
[I 250729 01:16:34 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 107.57ms
[I 250729 01:16:35 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:35 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:35 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:35 server:41790] Message: 1, 4831, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:35 server:41790] Message: 13, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building012', u'cost': 0}, None
[I 250729 01:16:36 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:36 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:36 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:16:36 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:36 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:36 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:36 server:41790] Message: 1, 4831, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:37 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:37 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:16:37 server:41790] Message: 1, 4831, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:37 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:37 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:37 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:37 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:38 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:38 server:41790] Message: 1, 4831, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:38 server:41790] Message: 1, 4831, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:38 server:41790] Message: 1, 4831, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:38 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building005', u'cost': 0}, None
[I 250729 01:16:38 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:16:39 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:39 server:41790] Message: 1, 4831, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:39 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:39 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:39 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:39 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:40 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:40 server:41790] Message: 1, 4831, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:40 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:16:40 server:41790] Message: 1, 4831, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:40 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:40 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:40 server:41790] Message: 1, 4831, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:41 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:41 server:41790] Message: 1, 4831, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:41 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:41 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:41 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:41 server:41790] Message: 1, 4831, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:42 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:42 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building013', u'cost': 0}, None
[I 250729 01:16:42 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:42 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:16:42 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:42 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:42 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:43 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:43 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:43 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:43 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:44 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:44 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building014', u'cost': 0}, None
[I 250729 01:16:44 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:44 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:16:44 server:41790] Message: 1, 4831, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:44 server:41790] Message: 1, 4830, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:45 server:41790] Message: 1, 4831, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:45 server:41790] Message: 1, 4830, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:45 server:41790] Message: 1, 4830, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:45 server:41902] api call: **************, get_server_status, {}
[I 250729 01:16:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.56ms
[I 250729 01:16:45 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:16:45 server:41790] Message: 1, 4831, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 54.17ms
[I 250729 01:16:45 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:16:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.96ms
[I 250729 01:16:45 server:41902] api call: **************, backup_world, {}
[I 250729 01:16:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 111.08ms
[I 250729 01:16:46 server:41790] Message: 2, 4831, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:46 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:46 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building015', u'cost': 0}, None
[I 250729 01:16:46 server:41790] Message: 1, 4832, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:46 server:41790] Message: 1, 4831, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item723', u'resolve_num': 10}, None
[I 250729 01:16:46 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:16:47 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:16:49 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building018', u'cost': 0}, None
[I 250729 01:16:49 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:16:50 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:16:50 server:41790] Message: 12, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill235', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:51 server:41790] Message: 50, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill235', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:51 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building017', u'cost': 0}, None
[I 250729 01:16:51 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:16:53 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building016', u'cost': 0}, None
[I 250729 01:16:54 server:41790] Message: 49, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill238', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:54 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:16:54 server:41790] Message: 49, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill238', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:54 server:41790] Message: 49, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill238', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:55 server:41790] Message: 48, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill238', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:55 server:41790] Message: 49, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill238', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:55 server:41790] Message: 49, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill238', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:56 server:41790] Message: 48, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill238', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:56 server:41790] Message: 48, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill238', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:56 server:41902] api call: **************, get_server_status, {}
[I 250729 01:16:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.60ms
[I 250729 01:16:56 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:16:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 53.71ms
[I 250729 01:16:56 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:16:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.97ms
[I 250729 01:16:56 server:41790] Message: 48, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill238', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:56 server:41902] api call: **************, backup_world, {}
[I 250729 01:16:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 106.96ms
[I 250729 01:16:57 server:41790] Message: 48, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill238', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:57 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:16:57 server:41790] Message: 48, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill238', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:57 server:41790] Message: 49, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill238', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:58 server:41790] Message: 48, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill238', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:58 server:41790] Message: 49, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill238', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:58 server:41790] Message: 48, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill238', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:59 server:41790] Message: 48, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill238', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:59 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building019', u'cost': 0}, None
[I 250729 01:16:59 server:41790] Message: 48, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill238', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:16:59 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:16:59 server:41790] Message: 48, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill238', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:00 server:41790] Message: 49, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill238', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:00 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:17:00 server:41790] Message: 48, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill238', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:00 server:41790] Message: 49, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill238', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:01 server:41790] Message: 50, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill238', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:01 server:41790] Message: 49, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill238', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:01 server:41790] Message: 49, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill238', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:01 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building020', u'cost': 0}, None
[I 250729 01:17:02 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:17:03 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building021', u'cost': 0}, None
[I 250729 01:17:04 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:17:04 server:41790] Message: 50, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill201', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:04 server:41790] Message: 49, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill201', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:04 server:41790] Message: 49, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill201', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:05 server:41790] Message: 48, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill201', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:05 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building024', u'cost': 0}, None
[I 250729 01:17:05 server:41790] Message: 48, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill201', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:06 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:17:06 server:41790] Message: 48, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill201', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:06 server:41790] Message: 48, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill201', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:07 server:41790] Message: 50, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill201', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:07 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:17:07 server:41790] Message: 49, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill201', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:07 server:41902] api call: **************, get_server_status, {}
[I 250729 01:17:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.57ms
[I 250729 01:17:07 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:17:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 31.75ms
[I 250729 01:17:07 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:17:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.96ms
[I 250729 01:17:07 server:41902] api call: **************, backup_world, {}
[I 250729 01:17:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 108.09ms
[I 250729 01:17:07 server:41790] Message: 48, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill201', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:08 server:41790] Message: 49, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill201', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:08 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building023', u'cost': 0}, None
[I 250729 01:17:08 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:17:08 server:41790] Message: 51, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill201', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:08 server:41790] Message: 48, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill201', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:09 server:41790] Message: 49, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill201', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:09 server:41790] Message: 49, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill201', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:09 server:41790] Message: 49, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill201', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:09 server:41790] Message: 48, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill201', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:10 server:41790] Message: 1, 2954, 5000000314, building_lvup, *************, 5, {u'bid': u'building022', u'cost': 0}, None
[I 250729 01:17:10 server:41790] Message: 48, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill201', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:10 server:41790] Message: 0, 2399, 5000000314, get_pk_npc, *************, 5, {}, None
[I 250729 01:17:10 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:17:10 server:41790] Message: 49, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill201', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:10 server:41790] Message: 49, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill201', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:10 server:41790] Message: 49, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill201', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:11 server:41790] Message: 50, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill201', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:11 server:41790] Message: 49, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill201', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:11 server:41790] Message: 50, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill201', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:14 server:41790] Message: 48, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill289', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:14 server:41790] Message: 48, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill289', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:14 server:41790] Message: 48, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill289', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:15 server:41790] Message: 48, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill289', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:15 server:41790] Message: 49, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill289', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:15 server:41790] Message: 48, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill289', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:15 server:41790] Message: 48, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill289', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:16 server:41790] Message: 48, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill289', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:16 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill289', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:16 server:41790] Message: 48, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill289', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:16 server:41790] Message: 48, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill289', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:17 server:41790] Message: 50, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill289', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:17 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:17:17 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill289', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:17 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill289', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:18 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill289', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:18 server:41902] api call: **************, get_server_status, {}
[I 250729 01:17:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.54ms
[I 250729 01:17:18 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:17:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 31.50ms
[I 250729 01:17:18 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:17:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.03ms
[I 250729 01:17:18 server:41902] api call: **************, backup_world, {}
[I 250729 01:17:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 106.66ms
[I 250729 01:17:18 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill289', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:19 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill289', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:19 server:41790] Message: 48, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill289', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:20 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:17:21 server:41790] Message: 50, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill289', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:21 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill289', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:21 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill289', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:21 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill289', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:22 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill289', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:22 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill289', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:24 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill202', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:25 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill202', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:25 server:41790] Message: 48, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill202', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:25 server:41790] Message: 48, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill202', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:25 server:41790] Message: 48, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill202', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:25 server:41790] Message: 48, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill202', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:26 server:41790] Message: 52, 5008, 5000000314, do_gtask, *************, 5, {u'donate_num': 0, u'hid': u'hero7702', u'task_id': u'gtask004'}, None
[I 250729 01:17:26 server:41790] Message: 48, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill202', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:26 server:41790] Message: 48, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill202', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:26 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill202', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:27 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:17:27 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill202', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:27 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill202', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:28 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill202', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:28 server:41790] Message: 48, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill202', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:28 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill202', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:28 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill202', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:29 server:41790] Message: 48, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill202', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:29 server:41790] Message: 48, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill202', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:29 server:41902] api call: **************, get_server_status, {}
[I 250729 01:17:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.59ms
[I 250729 01:17:29 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:17:29 server:41790] Message: 48, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill202', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 33.44ms
[I 250729 01:17:29 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:17:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.95ms
[I 250729 01:17:29 server:41902] api call: **************, backup_world, {}
[I 250729 01:17:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 113.54ms
[I 250729 01:17:29 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill202', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:30 server:41790] Message: 50, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill202', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:30 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:17:30 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill202', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:30 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill202', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:30 server:41790] Message: 50, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill202', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:31 server:41790] Message: 50, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill202', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:31 server:41790] Message: 0, 779, 5000000314, w.get_city_info, *************, 5, {u'is_simple': True, u'cid': 244}, None
[I 250729 01:17:33 server:41790] Message: 0, 1950, 5000000314, get_ftask_city_open_reward, *************, 5, {u'city_id': u'246'}, None
[I 250729 01:17:35 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill203', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:35 server:41790] Message: 48, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill203', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:36 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill203', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:36 server:41790] Message: 48, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill203', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:36 server:41790] Message: 50, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill203', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:37 server:41790] Message: 0, 1950, 5000000314, get_ftask_city_open_reward, *************, 5, {u'city_id': u'340'}, None
[I 250729 01:17:37 server:41790] Message: 48, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill203', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:37 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:17:37 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill203', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:37 server:41790] Message: 49, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill203', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:38 server:41790] Message: 48, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill203', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:38 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill203', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:38 server:41790] Message: 0, 793, 5000000314, get_gtask, *************, 5, {}, None
[I 250729 01:17:39 server:41790] Message: 48, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill203', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:39 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill203', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:39 server:41790] Message: 3, 14061, 5000000314, get_gtask_reward, *************, 5, {u'cost': 0, u'task_id': u'gtask004'}, None
[I 250729 01:17:39 server:41790] Message: 48, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill203', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:39 server:41790] Message: 48, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill203', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:40 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill203', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:40 server:41902] api call: **************, get_server_status, {}
[I 250729 01:17:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.59ms
[I 250729 01:17:40 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:17:40 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:17:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 36.51ms
[I 250729 01:17:40 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:17:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.96ms
[I 250729 01:17:40 server:41902] api call: **************, backup_world, {}
[I 250729 01:17:40 server:41790] Message: 84, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill203', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 123.44ms
[I 250729 01:17:40 server:41790] Message: 48, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill203', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:41 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill203', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:45 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill219', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:46 server:41790] Message: 48, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill219', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:46 server:41790] Message: 48, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill219', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:47 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:17:47 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill219', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:47 server:41790] Message: 48, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill219', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:48 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill219', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:48 server:41790] Message: 48, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill219', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:48 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill219', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:49 server:41790] Message: 50, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill219', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:49 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill219', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:49 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill219', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:50 server:41790] Message: 52, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill219', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:50 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:17:50 server:41790] Message: 48, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill219', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:51 server:41790] Message: 48, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill219', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:51 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill219', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:51 server:41902] api call: **************, get_server_status, {}
[I 250729 01:17:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.55ms
[I 250729 01:17:51 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:17:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 26.54ms
[I 250729 01:17:51 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:17:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.09ms
[I 250729 01:17:51 server:41902] api call: **************, backup_world, {}
[I 250729 01:17:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 112.98ms
[I 250729 01:17:53 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill219', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:54 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill219', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:54 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill219', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:54 server:41790] Message: 50, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill219', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:55 server:41790] Message: 50, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill219', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:55 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill219', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:57 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:17:58 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill221', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:59 server:41790] Message: 48, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill221', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:17:59 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill221', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:00 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill221', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:00 server:41790] Message: 49, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill221', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:00 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:18:00 server:41790] Message: 49, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill221', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:01 server:41790] Message: 48, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill221', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:01 server:41790] Message: 49, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill221', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:02 server:41790] Message: 7, 12818, 5000000314, get_pay_ploy_reward, *************, 5, {u'ploy_key': u'o2', u'reward_key': 30}, None
[I 250729 01:18:02 server:41790] Message: 51, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill221', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:02 server:41902] api call: **************, get_server_status, {}
[I 250729 01:18:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.58ms
[I 250729 01:18:02 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:18:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 32.33ms
[I 250729 01:18:02 server:41790] Message: 49, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill221', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:02 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:18:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.06ms
[I 250729 01:18:02 server:41902] api call: **************, backup_world, {}
[I 250729 01:18:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 107.71ms
[I 250729 01:18:02 server:41790] Message: 50, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill221', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:03 server:41790] Message: 49, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill221', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:03 server:41790] Message: 48, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill221', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:03 server:41790] Message: 48, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill221', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:04 server:41790] Message: 50, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill221', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:04 server:41790] Message: 49, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill221', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:04 server:41790] Message: 49, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill221', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:05 server:41790] Message: 49, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill221', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:05 server:41790] Message: 7, 9920, 5000000314, get_office_reward, *************, 5, {u'office': u'5'}, None
[I 250729 01:18:05 server:41790] Message: 49, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill221', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:06 server:41790] Message: 49, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill221', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:07 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:18:09 server:41790] Message: 49, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill205', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:09 server:41790] Message: 48, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill205', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:10 server:41790] Message: 48, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill205', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:10 server:41790] Message: 49, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill205', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:10 server:41790] Message: 49, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill205', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:10 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:18:11 server:41790] Message: 48, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill205', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:11 server:41790] Message: 49, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill205', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:11 server:41790] Message: 49, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill205', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:12 server:41790] Message: 48, 4969, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill205', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:12 server:41790] Message: 48, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill205', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:12 server:41790] Message: 48, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill205', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:13 server:41902] api call: **************, get_server_status, {}
[I 250729 01:18:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.61ms
[I 250729 01:18:13 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:18:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 32.98ms
[I 250729 01:18:13 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:18:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.02ms
[I 250729 01:18:13 server:41902] api call: **************, backup_world, {}
[I 250729 01:18:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 112.02ms
[I 250729 01:18:17 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:18:20 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:18:24 server:41902] api call: **************, get_server_status, {}
[I 250729 01:18:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.66ms
[I 250729 01:18:24 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:18:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.18ms
[I 250729 01:18:24 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:18:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.96ms
[I 250729 01:18:24 server:41902] api call: **************, backup_world, {}
[I 250729 01:18:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 110.41ms
[I 250729 01:18:25 server:41790] Message: 13, 3795, 5000000314, use_prop, *************, 5, {u'item_id': -1, u'item_num': 1, u'range_index': -1}, None
[I 250729 01:18:27 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:18:30 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:18:31 server:41790] Message: 0, 748, 5000000314, w.get_city_info, *************, 5, {u'is_simple': True, u'cid': 340}, None
[I 250729 01:18:32 server:41790] Message: 0, 748, 5000000314, w.get_city_info, *************, 5, {u'is_simple': True, u'cid': 340}, None
[I 250729 01:18:35 server:41902] api call: **************, get_server_status, {}
[I 250729 01:18:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.61ms
[I 250729 01:18:35 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:18:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 8.32ms
[I 250729 01:18:35 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:18:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.00ms
[I 250729 01:18:35 server:41902] api call: **************, backup_world, {}
[I 250729 01:18:35 server:41790] Message: 2, 1506, 5000000314, build_city_build, *************, 5, {u'bid': u'b01', u'cost': 1, u'hid': u'hero777', u'gear': 4, u'cid': u'340'}, None
[I 250729 01:18:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 108.20ms
[I 250729 01:18:35 server:41790] Message: 1, 4825, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:18:35 server:41790] Message: 5, 303, 5000000314, city_build_event_reward, *************, 5, {u'bid': u'b01', u'event_key': u'reward', u'cid': u'340'}, None
[I 250729 01:18:35 server:41790] Message: 1, 4825, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:18:35 server:41790] Message: 0, 4002, 5000000314, city_build_reward, *************, 5, {u'bid': u'b01', u'cid': u'340'}, None
[I 250729 01:18:36 server:41790] Message: 1, 4824, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:18:36 server:41790] Message: 1, 4825, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:18:36 server:41790] Message: 1, 4825, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:18:36 server:41790] Message: 2, 1506, 5000000314, build_city_build, *************, 5, {u'bid': u'b01', u'cost': 1, u'hid': u'hero777', u'gear': 4, u'cid': u'340'}, None
[I 250729 01:18:36 server:41790] Message: 1, 4824, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:18:36 server:41790] Message: 1, 4825, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:18:36 server:41790] Message: 1, 4825, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:18:37 server:41790] Message: 1, 4825, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:18:37 server:41790] Message: 1, 4824, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:18:37 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:18:38 server:41790] Message: 5, 298, 5000000314, city_build_event_reward, *************, 5, {u'bid': u'b01', u'event_key': u'cae1', u'cid': u'340'}, None
[I 250729 01:18:38 server:41790] Message: 0, 4002, 5000000314, city_build_reward, *************, 5, {u'bid': u'b01', u'cid': u'340'}, None
[I 250729 01:18:39 server:41790] Message: 2, 1506, 5000000314, build_city_build, *************, 5, {u'bid': u'b01', u'cost': 1, u'hid': u'hero777', u'gear': 4, u'cid': u'340'}, None
[I 250729 01:18:39 server:41790] Message: 0, 4002, 5000000314, city_build_reward, *************, 5, {u'bid': u'b01', u'cid': u'340'}, None
[I 250729 01:18:39 server:41790] Message: 2, 1506, 5000000314, build_city_build, *************, 5, {u'bid': u'b01', u'cost': 1, u'hid': u'hero777', u'gear': 4, u'cid': u'340'}, None
[I 250729 01:18:39 server:41790] Message: 0, 4002, 5000000314, city_build_reward, *************, 5, {u'bid': u'b01', u'cid': u'340'}, None
[I 250729 01:18:40 server:41790] Message: 2, 1506, 5000000314, build_city_build, *************, 5, {u'bid': u'b01', u'cost': 1, u'hid': u'hero777', u'gear': 4, u'cid': u'340'}, None
[I 250729 01:18:40 server:41790] Message: 0, 4002, 5000000314, city_build_reward, *************, 5, {u'bid': u'b01', u'cid': u'340'}, None
[I 250729 01:18:40 server:41790] Message: 2, 1506, 5000000314, build_city_build, *************, 5, {u'bid': u'b01', u'cost': 1, u'hid': u'hero777', u'gear': 4, u'cid': u'340'}, None
[I 250729 01:18:41 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:18:41 server:41790] Message: 5, 303, 5000000314, city_build_event_reward, *************, 5, {u'bid': u'b01', u'event_key': u'reward', u'cid': u'340'}, None
[I 250729 01:18:41 server:41790] Message: 0, 4002, 5000000314, city_build_reward, *************, 5, {u'bid': u'b01', u'cid': u'340'}, None
[I 250729 01:18:42 server:41790] Message: 2, 1506, 5000000314, build_city_build, *************, 5, {u'bid': u'b01', u'cost': 1, u'hid': u'hero777', u'gear': 4, u'cid': u'340'}, None
[I 250729 01:18:42 server:41790] Message: 0, 4002, 5000000314, city_build_reward, *************, 5, {u'bid': u'b01', u'cid': u'340'}, None
[I 250729 01:18:42 server:41790] Message: 2, 1506, 5000000314, build_city_build, *************, 5, {u'bid': u'b01', u'cost': 1, u'hid': u'hero777', u'gear': 4, u'cid': u'340'}, None
[I 250729 01:18:42 server:41790] Message: 13, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill292', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:42 server:41790] Message: 0, 4002, 5000000314, city_build_reward, *************, 5, {u'bid': u'b01', u'cid': u'340'}, None
[I 250729 01:18:42 server:41790] Message: 50, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill292', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:43 server:41790] Message: 50, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill292', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:43 server:41790] Message: 2, 1506, 5000000314, build_city_build, *************, 5, {u'bid': u'b01', u'cost': 1, u'hid': u'hero777', u'gear': 4, u'cid': u'340'}, None
[I 250729 01:18:43 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill292', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:43 server:41790] Message: 0, 4002, 5000000314, city_build_reward, *************, 5, {u'bid': u'b01', u'cid': u'340'}, None
[I 250729 01:18:43 server:41790] Message: 50, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill292', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:43 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill292', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:44 server:41790] Message: 2, 1506, 5000000314, build_city_build, *************, 5, {u'bid': u'b01', u'cost': 1, u'hid': u'hero777', u'gear': 4, u'cid': u'340'}, None
[I 250729 01:18:44 server:41790] Message: 50, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill292', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:44 server:41790] Message: 0, 4002, 5000000314, city_build_reward, *************, 5, {u'bid': u'b01', u'cid': u'340'}, None
[I 250729 01:18:44 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill292', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:44 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill292', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:44 server:41790] Message: 2, 1506, 5000000314, build_city_build, *************, 5, {u'bid': u'b01', u'cost': 1, u'hid': u'hero777', u'gear': 4, u'cid': u'340'}, None
[I 250729 01:18:45 server:41790] Message: 0, 4002, 5000000314, city_build_reward, *************, 5, {u'bid': u'b01', u'cid': u'340'}, None
[I 250729 01:18:45 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill292', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:45 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill292', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:45 server:41790] Message: 2, 1506, 5000000314, build_city_build, *************, 5, {u'bid': u'b01', u'cost': 1, u'hid': u'hero777', u'gear': 4, u'cid': u'340'}, None
[I 250729 01:18:45 server:41790] Message: 0, 4002, 5000000314, city_build_reward, *************, 5, {u'bid': u'b01', u'cid': u'340'}, None
[I 250729 01:18:45 server:41902] api call: **************, get_server_status, {}
[I 250729 01:18:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.59ms
[I 250729 01:18:46 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:18:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 31.31ms
[I 250729 01:18:46 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:18:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.06ms
[I 250729 01:18:46 server:41902] api call: **************, backup_world, {}
[I 250729 01:18:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 106.93ms
[I 250729 01:18:46 server:41790] Message: 2, 1506, 5000000314, build_city_build, *************, 5, {u'bid': u'b01', u'cost': 1, u'hid': u'hero777', u'gear': 4, u'cid': u'340'}, None
[I 250729 01:18:47 server:41790] Message: 5, 303, 5000000314, city_build_event_reward, *************, 5, {u'bid': u'b01', u'event_key': u'reward', u'cid': u'340'}, None
[I 250729 01:18:47 server:41790] Message: 0, 4003, 5000000314, city_build_reward, *************, 5, {u'bid': u'b01', u'cid': u'340'}, None
[I 250729 01:18:47 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:18:47 server:41790] Message: 2, 1506, 5000000314, build_city_build, *************, 5, {u'bid': u'b01', u'cost': 1, u'hid': u'hero777', u'gear': 4, u'cid': u'340'}, None
[I 250729 01:18:48 server:41790] Message: 5, 303, 5000000314, city_build_event_reward, *************, 5, {u'bid': u'b01', u'event_key': u'reward', u'cid': u'340'}, None
[I 250729 01:18:48 server:41790] Message: 50, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill271', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:48 server:41790] Message: 0, 4003, 5000000314, city_build_reward, *************, 5, {u'bid': u'b01', u'cid': u'340'}, None
[I 250729 01:18:48 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill271', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:49 server:41790] Message: 2, 1506, 5000000314, build_city_build, *************, 5, {u'bid': u'b01', u'cost': 1, u'hid': u'hero777', u'gear': 4, u'cid': u'340'}, None
[I 250729 01:18:49 server:41790] Message: 0, 4003, 5000000314, city_build_reward, *************, 5, {u'bid': u'b01', u'cid': u'340'}, None
[I 250729 01:18:49 server:41790] Message: 50, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill271', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:49 server:41790] Message: 49, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill271', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:51 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:18:53 server:41790] Message: 50, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill280', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:53 server:41790] Message: 49, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill280', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:53 server:41790] Message: 51, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill280', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:54 server:41790] Message: 6, 1093, 5000000314, office_right_unblock, *************, 5, {u'right_id': u'604'}, None
[I 250729 01:18:54 server:41790] Message: 50, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill280', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:54 server:41790] Message: 49, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill280', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:55 server:41790] Message: 49, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill280', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:55 server:41790] Message: 6, 1099, 5000000314, office_right_unblock, *************, 5, {u'right_id': u'605'}, None
[I 250729 01:18:56 server:41902] api call: **************, get_server_status, {}
[I 250729 01:18:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.64ms
[I 250729 01:18:56 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:18:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 31.28ms
[I 250729 01:18:56 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:18:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.97ms
[I 250729 01:18:56 server:41902] api call: **************, backup_world, {}
[I 250729 01:18:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 114.35ms
[I 250729 01:18:57 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:18:57 server:41790] Message: 50, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill271', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:18:58 server:41790] Message: 50, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill271', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:19:01 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:19:07 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:19:07 server:41902] api call: **************, get_server_status, {}
[I 250729 01:19:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.63ms
[I 250729 01:19:07 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:19:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 25.72ms
[I 250729 01:19:07 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:19:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.94ms
[I 250729 01:19:07 server:41902] api call: **************, backup_world, {}
[I 250729 01:19:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 113.17ms
[I 250729 01:19:09 server:41790] Message: 0, 1952, 5000000314, get_ftask_city_open_reward, *************, 5, {u'city_id': u'388'}, None
[I 250729 01:19:11 server:41790] Message: 0, 807, 5000000314, w.get_city_info, *************, 5, {u'is_simple': True, u'cid': 388}, None
[I 250729 01:19:11 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:19:17 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:19:18 server:41902] api call: **************, get_server_status, {}
[I 250729 01:19:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.64ms
[I 250729 01:19:18 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:19:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 7.84ms
[I 250729 01:19:18 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:19:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.96ms
[I 250729 01:19:18 server:41902] api call: **************, backup_world, {}
[I 250729 01:19:18 server:41790] Message: 7, 4041, 5000000314, hero_city_visit, *************, 5, {u'city_id': u'388', u'cost': 1, u'hid': u'hero770'}, None
[I 250729 01:19:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 113.22ms
[I 250729 01:19:19 server:41790] Message: 5, 3409, 5000000314, hero_city_visit_reward, *************, 5, {u'city_id': u'388'}, None
[I 250729 01:19:21 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:19:24 server:41790] Message: 0, 1953, 5000000314, get_ftask_city_open_reward, *************, 5, {u'city_id': u'209'}, None
[I 250729 01:19:24 server:41790] Message: 2, 7927, 5000000265, get_rank_by_type, *************, 5, {u'rank_type': u'power'}, None
[I 250729 01:19:25 server:41790] Message: 0, 829, 5000000314, w.get_city_info, *************, 5, {u'is_simple': True, u'cid': 209}, None
[I 250729 01:19:27 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:19:29 server:41902] api call: **************, get_server_status, {}
[I 250729 01:19:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.61ms
[I 250729 01:19:29 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:19:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 8.26ms
[I 250729 01:19:29 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:19:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.95ms
[I 250729 01:19:29 server:41902] api call: **************, backup_world, {}
[I 250729 01:19:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 107.12ms
[I 250729 01:19:31 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:19:34 server:41790] Message: 6, 4167, 5000000314, hero_city_visit, *************, 5, {u'city_id': u'209', u'cost': 1, u'hid': u'hero770'}, None
[I 250729 01:19:35 server:41790] Message: 5, 3520, 5000000314, hero_city_visit_reward, *************, 5, {u'city_id': u'209'}, None
[I 250729 01:19:37 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:19:40 server:41790] Message: 33, 118162, 5000000265, w.troop_create, *************, 5, {u'hid': u'hero716', u'is_pay': False, u'is_xyz': False}, None
[I 250729 01:19:40 server:41902] api call: **************, get_server_status, {}
[I 250729 01:19:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.57ms
[I 250729 01:19:40 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:19:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 32.69ms
[I 250729 01:19:40 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:19:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.99ms
[I 250729 01:19:40 server:41902] api call: **************, backup_world, {}
[I 250729 01:19:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 108.57ms
[I 250729 01:19:41 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:19:43 server:41790] Message: 33, 118161, 5000000265, w.troop_create, *************, 5, {u'hid': u'hero7702', u'is_pay': False, u'is_xyz': False}, None
[I 250729 01:19:44 server:41790] Message: 0, 1955, 5000000314, get_ftask_city_open_reward, *************, 5, {u'city_id': u'222'}, None
[I 250729 01:19:47 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:19:51 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:19:51 server:41902] api call: **************, get_server_status, {}
[I 250729 01:19:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.61ms
[I 250729 01:19:51 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:19:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 31.61ms
[I 250729 01:19:51 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:19:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.94ms
[I 250729 01:19:51 server:41902] api call: **************, backup_world, {}
[I 250729 01:19:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 113.70ms
[I 250729 01:19:52 server:41790] Message: 0, 1956, 5000000314, get_ftask_city_open_reward, *************, 5, {u'city_id': u'344'}, None
[I 250729 01:19:53 server:41790] Message: 0, 1957, 5000000314, get_ftask_city_open_reward, *************, 5, {u'city_id': u'342'}, None
[I 250729 01:19:54 server:41790] Message: 0, 1103, 5000000314, w.get_city_info, *************, 5, {u'is_simple': True, u'cid': 344}, None
[I 250729 01:19:55 server:41790] Message: 6, 4278, 5000000314, hero_city_visit, *************, 5, {u'city_id': u'344', u'cost': 1, u'hid': u'hero770'}, None
[I 250729 01:19:57 server:41790] Message: 5, 3646, 5000000314, hero_city_visit_reward, *************, 5, {u'city_id': u'344'}, None
[I 250729 01:19:57 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:20:01 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:20:02 server:41902] api call: **************, get_server_status, {}
[I 250729 01:20:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.61ms
[I 250729 01:20:02 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:20:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 8.50ms
[I 250729 01:20:02 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:20:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.94ms
[I 250729 01:20:02 server:41902] api call: **************, backup_world, {}
[I 250729 01:20:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 114.13ms
[I 250729 01:20:04 server:41790] Message: 0, 866, 5000000314, w.get_city_info, *************, 5, {u'is_simple': True, u'cid': 306}, None
[I 250729 01:20:05 server:41790] Message: 6, 4405, 5000000314, hero_city_visit, *************, 5, {u'city_id': u'306', u'cost': 1, u'hid': u'hero770'}, None
[I 250729 01:20:06 server:41790] Message: 5, 3772, 5000000314, hero_city_visit_reward, *************, 5, {u'city_id': u'306'}, None
[I 250729 01:20:07 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:20:11 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:20:11 server:41790] Message: 0, 1958, 5000000314, get_ftask_city_open_reward, *************, 5, {u'city_id': u'303'}, None
[I 250729 01:20:13 server:41902] api call: **************, get_server_status, {}
[I 250729 01:20:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.63ms
[I 250729 01:20:13 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:20:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 7.83ms
[I 250729 01:20:13 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:20:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.96ms
[I 250729 01:20:13 server:41902] api call: **************, backup_world, {}
[I 250729 01:20:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 114.39ms
[I 250729 01:20:16 server:41790] Message: 0, 1959, 5000000314, get_ftask_city_open_reward, *************, 5, {u'city_id': u'338'}, None
[I 250729 01:20:17 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:20:17 server:41790] Message: 0, 854, 5000000314, w.get_city_info, *************, 5, {u'is_simple': True, u'cid': 338}, None
[I 250729 01:20:20 server:41790] Message: 7, 4531, 5000000314, hero_city_visit, *************, 5, {u'city_id': u'338', u'cost': 1, u'hid': u'hero770'}, None
[I 250729 01:20:21 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:20:22 server:41790] Message: 0, 668, 5000000314, w.get_city_info, *************, 5, {u'is_simple': True, u'cid': 335}, None
[I 250729 01:20:24 server:41902] api call: **************, get_server_status, {}
[I 250729 01:20:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.66ms
[I 250729 01:20:24 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:20:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 8.21ms
[I 250729 01:20:24 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:20:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.95ms
[I 250729 01:20:24 server:41902] api call: **************, backup_world, {}
[I 250729 01:20:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 120.40ms
[I 250729 01:20:27 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:20:29 server:41790] Message: 6, 1607, 5000000314, get_task_reward, *************, 5, {u'kind': u'main', u'task_id': u'main_6'}, None
[I 250729 01:20:31 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:20:32 server:41790] Message: 5, 740, 5000000314, visit_event_reward, *************, 5, {u'city_id': u'338', u'event_key': u'reward'}, None
[I 250729 01:20:32 server:41790] Message: 2, 14439, 5000000314, estate_build_visit_reward, *************, 5, {}, None
[I 250729 01:20:35 server:41902] api call: **************, get_server_status, {}
[I 250729 01:20:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.65ms
[I 250729 01:20:35 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:20:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 7.98ms
[I 250729 01:20:35 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:20:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.03ms
[I 250729 01:20:35 server:41902] api call: **************, backup_world, {}
[I 250729 01:20:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 106.46ms
[I 250729 01:20:37 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:20:41 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:20:45 server:41790] Message: 17, 5389, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle001'}, None
[I 250729 01:20:45 server:41790] Message: 0, 2981, 5000000265, get_shop, *************, 5, {u'shop_id': u'hero_shop'}, None
[I 250729 01:20:45 server:41902] api call: **************, get_server_status, {}
[I 250729 01:20:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.59ms
[I 250729 01:20:45 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:20:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 8.46ms
[I 250729 01:20:45 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:20:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.98ms
[I 250729 01:20:46 server:41902] api call: **************, backup_world, {}
[I 250729 01:20:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 105.87ms
[I 250729 01:20:47 server:41790] Message: 0, 2981, 5000000265, get_shop, *************, 5, {u'shop_id': u'soul_shop'}, None
[I 250729 01:20:47 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:20:48 server:41790] Message: 15, 5408, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle002'}, None
[I 250729 01:20:50 server:41790] Message: 0, 2981, 5000000265, get_shop, *************, 5, {u'shop_id': u'travel_shop'}, None
[I 250729 01:20:51 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:20:52 server:41790] Message: 0, 2981, 5000000265, get_shop, *************, 5, {u'shop_id': u'treasuer_shop'}, None
[I 250729 01:20:52 server:41790] Message: 17, 6145, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772', u'hero768'], u'repeat': 0, u'battle_id': u'battle003'}, None
[I 250729 01:20:54 server:41790] Message: 0, 2981, 5000000265, get_shop, *************, 5, {u'shop_id': u'tournament_shop'}, None
[I 250729 01:20:56 server:41790] Message: 15, 5479, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle004'}, None
[I 250729 01:20:56 server:41902] api call: **************, get_server_status, {}
[I 250729 01:20:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.61ms
[I 250729 01:20:56 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:20:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 8.58ms
[I 250729 01:20:56 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:20:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.91ms
[I 250729 01:20:56 server:41902] api call: **************, backup_world, {}
[I 250729 01:20:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 106.49ms
[I 250729 01:20:57 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:20:57 server:41790] Message: 0, 2981, 5000000265, get_shop, *************, 5, {u'shop_id': u'mining_shop'}, None
[I 250729 01:21:00 server:41790] Message: 14, 5477, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle005'}, None
[I 250729 01:21:00 server:41790] Message: 1, 15089, 5000000265, buy_shop, *************, 5, {u'shop_id': u'mining_shop', u'goods_id': u'5'}, None
[I 250729 01:21:01 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:21:03 server:41790] Message: 15, 5488, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle006'}, None
[I 250729 01:21:07 server:41790] Message: 1, 3463, 5000000314, pve_combat, *************, 5, {u'repeat': 1, u'battle_id': u'battle006'}, None
[I 250729 01:21:07 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:21:07 server:41902] api call: **************, get_server_status, {}
[I 250729 01:21:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.60ms
[I 250729 01:21:07 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:21:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 31.98ms
[I 250729 01:21:07 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:21:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.00ms
[I 250729 01:21:07 server:41902] api call: **************, backup_world, {}
[I 250729 01:21:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 107.18ms
[I 250729 01:21:10 server:41790] Message: 19, 5519, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle007'}, None
[I 250729 01:21:11 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:21:11 server:41790] Message: 48, 2583, 5000000265, get_task_reward, *************, 5, {u'kind': u'common', u'task_id': u'index4'}, None
[I 250729 01:21:14 server:41790] Message: 16, 5520, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle008'}, None
[I 250729 01:21:17 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:21:18 server:41790] Message: 17, 5530, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle009'}, None
[I 250729 01:21:18 server:41902] api call: **************, get_server_status, {}
[I 250729 01:21:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.63ms
[I 250729 01:21:18 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:21:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 31.47ms
[I 250729 01:21:18 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:21:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.00ms
[I 250729 01:21:18 server:41902] api call: **************, backup_world, {}
[I 250729 01:21:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 106.10ms
[I 250729 01:21:19 server:41790] Message: 2, 62879, 5000000265, get_grab_log, *************, 5, {}, None
[I 250729 01:21:20 server:41790] Message: 46, 5230, 5000000265, harv_mining, *************, 5, {u'resId': 1}, None
[I 250729 01:21:21 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:21:21 server:41790] Message: 18, 6243, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772', u'hero768'], u'repeat': 0, u'battle_id': u'battle010'}, None
[I 250729 01:21:22 server:41790] Message: 50, 5134, 5000000265, harv_mining, *************, 5, {u'resId': 0}, None
[I 250729 01:21:25 server:41790] Message: 17, 6244, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772', u'hero768'], u'repeat': 0, u'battle_id': u'battle011'}, None
[I 250729 01:21:27 server:41790] Message: 45, 467, 5000000265, start_minine, *************, 5, {u'hids': [u'hero773', u'hero772', u'hero716'], u'resId': 1}, None
[I 250729 01:21:27 server:41790] Message: 0, 219, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:21:29 server:41902] api call: **************, get_server_status, {}
[I 250729 01:21:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.62ms
[I 250729 01:21:29 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:21:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 31.90ms
[I 250729 01:21:29 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:21:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.95ms
[I 250729 01:21:29 server:41902] api call: **************, backup_world, {}
[I 250729 01:21:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 105.46ms
[I 250729 01:21:29 server:41790] Message: 22, 6989, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772', u'hero768', u'hero760'], u'repeat': 0, u'battle_id': u'battle012'}, None
[I 250729 01:21:31 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:21:31 server:41790] Message: 45, 564, 5000000265, start_minine, *************, 5, {u'hids': [u'hero714', u'hero7702', u'hero760'], u'resId': 0}, None
[I 250729 01:21:32 server:41790] Message: 5, 3497, 5000000314, get_pve_star_reward, *************, 5, {u'reward_index': 0, u'chapter_id': u'chapter001'}, None
[I 250729 01:21:32 server:41790] Message: 6, 3500, 5000000314, get_pve_star_reward, *************, 5, {u'reward_index': 1, u'chapter_id': u'chapter001'}, None
[I 250729 01:21:33 server:41790] Message: 5, 3517, 5000000314, get_pve_star_reward, *************, 5, {u'reward_index': 2, u'chapter_id': u'chapter001'}, None
[I 250729 01:21:36 server:41790] Message: 19, 5665, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle013'}, None
[I 250729 01:21:37 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:21:39 server:41790] Message: 15, 5666, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle014'}, None
[I 250729 01:21:40 server:41902] api call: **************, get_server_status, {}
[I 250729 01:21:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.60ms
[I 250729 01:21:40 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:21:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 31.89ms
[I 250729 01:21:40 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:21:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.96ms
[I 250729 01:21:40 server:41902] api call: **************, backup_world, {}
[I 250729 01:21:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 111.78ms
[I 250729 01:21:41 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:21:43 server:41790] Message: 16, 6369, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772', u'hero768'], u'repeat': 0, u'battle_id': u'battle015'}, None
[I 250729 01:21:47 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:21:48 server:41790] Message: 16, 5705, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle016'}, None
[I 250729 01:21:51 server:41902] api call: **************, get_server_status, {}
[I 250729 01:21:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.57ms
[I 250729 01:21:51 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:21:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 8.12ms
[I 250729 01:21:51 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:21:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.88ms
[I 250729 01:21:51 server:41902] api call: **************, backup_world, {}
[I 250729 01:21:51 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:21:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 112.42ms
[I 250729 01:21:52 server:41790] Message: 16, 5704, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle017'}, None
[I 250729 01:21:56 server:41790] Message: 15, 5728, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle018'}, None
[I 250729 01:21:57 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:22:00 server:41790] Message: 16, 5745, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle019'}, None
[I 250729 01:22:01 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:22:02 server:41902] api call: **************, get_server_status, {}
[I 250729 01:22:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.63ms
[I 250729 01:22:02 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:22:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 8.71ms
[I 250729 01:22:02 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:22:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.08ms
[I 250729 01:22:02 server:41902] api call: **************, backup_world, {}
[I 250729 01:22:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 111.17ms
[I 250729 01:22:03 server:41790] Message: 16, 5746, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle020'}, None
[I 250729 01:22:07 server:41790] Message: 19, 6453, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772', u'hero768'], u'repeat': 0, u'battle_id': u'battle021'}, None
[I 250729 01:22:07 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:22:11 server:41790] Message: 18, 5786, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle022'}, None
[I 250729 01:22:11 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:22:13 server:41902] api call: **************, get_server_status, {}
[I 250729 01:22:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.59ms
[I 250729 01:22:13 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:22:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 8.33ms
[I 250729 01:22:13 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:22:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.04ms
[I 250729 01:22:13 server:41902] api call: **************, backup_world, {}
[I 250729 01:22:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 111.98ms
[I 250729 01:22:14 server:41790] Message: 15, 5786, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle023'}, None
[I 250729 01:22:17 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:22:18 server:41790] Message: 15, 5807, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle024'}, None
[I 250729 01:22:21 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:22:21 server:41790] Message: 5, 3722, 5000000314, get_pve_star_reward, *************, 5, {u'reward_index': 0, u'chapter_id': u'chapter002'}, None
[I 250729 01:22:22 server:41790] Message: 5, 3725, 5000000314, get_pve_star_reward, *************, 5, {u'reward_index': 1, u'chapter_id': u'chapter002'}, None
[I 250729 01:22:23 server:41790] Message: 6, 3729, 5000000314, get_pve_star_reward, *************, 5, {u'reward_index': 2, u'chapter_id': u'chapter002'}, None
[I 250729 01:22:23 server:41902] api call: **************, get_server_status, {}
[I 250729 01:22:23 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.61ms
[I 250729 01:22:23 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:22:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 8.50ms
[I 250729 01:22:24 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:22:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.02ms
[I 250729 01:22:24 server:41902] api call: **************, backup_world, {}
[I 250729 01:22:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 111.67ms
[I 250729 01:22:26 server:41790] Message: 18, 5860, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle025'}, None
[I 250729 01:22:27 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:22:31 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:22:32 server:41790] Message: 18, 3459, 5000000314, recruit_hero, *************, 5, {u'hid': u'hero715'}, None
[I 250729 01:22:33 server:41790] Message: 55, 3460, 5000000314, recruit_hero, *************, 5, {u'hid': u'hero708'}, None
[I 250729 01:22:33 server:41790] Message: 55, 3459, 5000000314, recruit_hero, *************, 5, {u'hid': u'hero733'}, None
[I 250729 01:22:34 server:41902] api call: **************, get_server_status, {}
[I 250729 01:22:34 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.63ms
[I 250729 01:22:34 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:22:34 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 8.38ms
[I 250729 01:22:34 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:22:34 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.03ms
[I 250729 01:22:34 server:41902] api call: **************, backup_world, {}
[I 250729 01:22:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 105.42ms
[I 250729 01:22:35 server:41790] Message: 66, 3459, 5000000314, recruit_hero, *************, 5, {u'hid': u'hero732'}, None
[I 250729 01:22:37 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:22:39 server:41790] Message: 54, 3585, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero760'}, None
[I 250729 01:22:39 server:41790] Message: 53, 3585, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero760'}, None
[I 250729 01:22:39 server:41790] Message: 53, 3585, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero760'}, None
[I 250729 01:22:40 server:41790] Message: 53, 3585, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero760'}, None
[I 250729 01:22:40 server:41790] Message: 53, 3585, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero760'}, None
[I 250729 01:22:41 server:41790] Message: 53, 3585, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero760'}, None
[I 250729 01:22:41 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:22:45 server:41902] api call: **************, get_server_status, {}
[I 250729 01:22:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.62ms
[I 250729 01:22:45 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:22:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 8.51ms
[I 250729 01:22:45 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:22:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.01ms
[I 250729 01:22:45 server:41902] api call: **************, backup_world, {}
[I 250729 01:22:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 107.06ms
[I 250729 01:22:47 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:22:51 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:22:55 server:41790] Message: 18, 3662, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero7702'}, None
[I 250729 01:22:56 server:41790] Message: 54, 3662, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero7702'}, None
[I 250729 01:22:56 server:41790] Message: 54, 3662, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero7702'}, None
[I 250729 01:22:56 server:41902] api call: **************, get_server_status, {}
[I 250729 01:22:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.54ms
[I 250729 01:22:56 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:22:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 7.78ms
[I 250729 01:22:56 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:22:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.91ms
[I 250729 01:22:56 server:41902] api call: **************, backup_world, {}
[I 250729 01:22:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 106.77ms
[I 250729 01:22:56 server:41790] Message: 55, 3662, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero7702'}, None
[I 250729 01:22:57 server:41790] Message: 54, 3662, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero7702'}, None
[I 250729 01:22:57 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:22:57 server:41790] Message: 53, 3662, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero7702'}, None
[I 250729 01:23:00 server:41790] Message: 48, 3625, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item004', u'hid': u'hero7702'}, None
[I 250729 01:23:01 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:23:01 server:41790] Message: 48, 3625, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero7702'}, None
[I 250729 01:23:02 server:41790] Message: 48, 3625, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero7702'}, None
[I 250729 01:23:02 server:41790] Message: 48, 3626, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero7702'}, None
[I 250729 01:23:02 server:41790] Message: 48, 3625, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero7702'}, None
[I 250729 01:23:03 server:41790] Message: 48, 3626, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero7702'}, None
[I 250729 01:23:07 server:41902] api call: **************, get_server_status, {}
[I 250729 01:23:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.64ms
[I 250729 01:23:07 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:23:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 8.14ms
[I 250729 01:23:07 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:23:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.92ms
[I 250729 01:23:07 server:41902] api call: **************, backup_world, {}
[I 250729 01:23:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 121.70ms
[I 250729 01:23:07 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:23:07 server:41790] Message: 55, 3675, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero772'}, None
[I 250729 01:23:08 server:41790] Message: 54, 3675, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero772'}, None
[I 250729 01:23:08 server:41790] Message: 54, 3675, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero772'}, None
[I 250729 01:23:08 server:41790] Message: 54, 3675, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero772'}, None
[I 250729 01:23:09 server:41790] Message: 54, 3675, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero772'}, None
[I 250729 01:23:09 server:41790] Message: 54, 3675, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero772'}, None
[I 250729 01:23:11 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:23:12 server:41790] Message: 48, 3640, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero772'}, None
[I 250729 01:23:12 server:41790] Message: 48, 3639, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero772'}, None
[I 250729 01:23:13 server:41790] Message: 48, 3639, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero772'}, None
[I 250729 01:23:13 server:41790] Message: 48, 3639, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero772'}, None
[I 250729 01:23:13 server:41790] Message: 48, 3639, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero772'}, None
[I 250729 01:23:13 server:41790] Message: 48, 3640, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero772'}, None
[I 250729 01:23:13 server:41790] Message: 48, 3640, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero772'}, None
[I 250729 01:23:14 server:41790] Message: 48, 3641, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero772'}, None
[I 250729 01:23:14 server:41790] Message: 48, 3641, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero772'}, None
[I 250729 01:23:14 server:41790] Message: 48, 3641, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero772'}, None
[I 250729 01:23:16 server:41790] Message: 45, 108, 5000000265, try_to_pay, *************, 5, {u'pid': u'gd1573', u'ptype': u'limit_recharge'}, None
[I 250729 01:23:17 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:23:18 server:41902] api call: **************, get_server_status, {}
[I 250729 01:23:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.62ms
[I 250729 01:23:18 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:23:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 7.85ms
[I 250729 01:23:18 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:23:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.98ms
[I 250729 01:23:18 server:41902] api call: **************, backup_world, {}
[I 250729 01:23:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 112.96ms
[I 250729 01:23:19 server:41790] Message: 44, 108, 5000000265, try_to_pay, *************, 5, {u'pid': u'gd1573', u'ptype': u'limit_recharge'}, None
[I 250729 01:23:19 server:41790] Message: 12, 3548, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero760'}, None
[I 250729 01:23:19 server:41790] Message: 49, 3549, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero760'}, None
[I 250729 01:23:20 server:41790] Message: 48, 3548, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero760'}, None
[I 250729 01:23:20 server:41902] api call: **************, user_pay, {'pid': u'gd1573', 'pay_id': u'gd1573|20250729012320|ucoin', 'uid': 265, 'zone': u'5'}
[I 250729 01:23:20 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 34.33ms
[I 250729 01:23:20 server:41790] Message: 49, 3548, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero760'}, None
[I 250729 01:23:20 server:41902] api call: **************, user_pay, {'pid': u'gd1573', 'pay_id': u'gd1573|20250729012320|ucoin', 'uid': 265, 'zone': u'5'}
[I 250729 01:23:20 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.71ms
[I 250729 01:23:20 server:41790] Message: 49, 3548, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero760'}, None
[I 250729 01:23:20 server:41790] Message: 48, 3548, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero760'}, None
[I 250729 01:23:20 server:41790] Message: 0, 2241, 5000000265, get_club_redbag, *************, 5, {}, None
[I 250729 01:23:20 server:41790] Message: 47, 3549, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero760'}, None
[I 250729 01:23:21 server:41790] Message: 49, 3548, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero760'}, None
[I 250729 01:23:21 server:41790] Message: 48, 3549, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero760'}, None
[I 250729 01:23:21 server:41790] Message: 48, 3549, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero760'}, None
[I 250729 01:23:21 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:23:21 server:41790] Message: 48, 3549, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero760'}, None
[I 250729 01:23:24 server:41790] Message: 44, 108, 5000000265, try_to_pay, *************, 5, {u'pid': u'gd1573', u'ptype': u'limit_recharge'}, None
[I 250729 01:23:25 server:41902] api call: **************, user_pay, {'pid': u'gd1573', 'pay_id': u'gd1573|20250729012325|ucoin', 'uid': 265, 'zone': u'5'}
[I 250729 01:23:25 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 33.89ms
[I 250729 01:23:26 server:41790] Message: 0, 2241, 5000000265, get_club_redbag, *************, 5, {}, None
[I 250729 01:23:27 server:41790] Message: 44, 108, 5000000265, try_to_pay, *************, 5, {u'pid': u'gd1573', u'ptype': u'limit_recharge'}, None
[I 250729 01:23:27 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:23:28 server:41790] Message: 44, 108, 5000000265, try_to_pay, *************, 5, {u'pid': u'gd1573', u'ptype': u'limit_recharge'}, None
[I 250729 01:23:29 server:41902] api call: **************, get_server_status, {}
[I 250729 01:23:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.61ms
[I 250729 01:23:29 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:23:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 36.74ms
[I 250729 01:23:29 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:23:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.19ms
[I 250729 01:23:29 server:41902] api call: **************, backup_world, {}
[I 250729 01:23:29 server:41902] api call: **************, user_pay, {'pid': u'gd1573', 'pay_id': u'gd1573|20250729012329|ucoin', 'uid': 265, 'zone': u'5'}
[I 250729 01:23:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 68.12ms
[I 250729 01:23:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 119.43ms
[I 250729 01:23:29 server:41790] Message: 61, 3605, 5000000314, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill238', u'hid': u'hero760', u'fast_learn': 0}, None
[I 250729 01:23:29 server:41790] Message: 0, 2241, 5000000265, get_club_redbag, *************, 5, {}, None
[I 250729 01:23:31 server:41790] Message: 50, 108, 5000000265, try_to_pay, *************, 5, {u'pid': u'gd1573', u'ptype': u'limit_recharge'}, None
[I 250729 01:23:31 server:41790] Message: 48, 3605, 5000000314, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill238', u'hid': u'hero760', u'fast_learn': 0}, None
[I 250729 01:23:31 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:23:32 server:41902] api call: **************, user_pay, {'pid': u'gd1573', 'pay_id': u'gd1573|20250729012332|ucoin', 'uid': 265, 'zone': u'5'}
[I 250729 01:23:32 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 34.85ms
[I 250729 01:23:32 server:41790] Message: 0, 2241, 5000000265, get_club_redbag, *************, 5, {}, None
[I 250729 01:23:33 server:41790] Message: 49, 3605, 5000000314, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill238', u'hid': u'hero760', u'fast_learn': 0}, None
[I 250729 01:23:33 server:41790] Message: 44, 108, 5000000265, try_to_pay, *************, 5, {u'pid': u'gd1573', u'ptype': u'limit_recharge'}, None
[I 250729 01:23:34 server:41902] api call: **************, user_pay, {'pid': u'gd1573', 'pay_id': u'gd1573|20250729012334|ucoin', 'uid': 265, 'zone': u'5'}
[I 250729 01:23:34 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 34.43ms
[I 250729 01:23:35 server:41790] Message: 0, 2241, 5000000265, get_club_redbag, *************, 5, {}, None
[I 250729 01:23:35 server:41790] Message: 49, 3605, 5000000314, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill238', u'hid': u'hero760', u'fast_learn': 0}, None
[I 250729 01:23:35 server:41790] Message: 45, 108, 5000000265, try_to_pay, *************, 5, {u'pid': u'gd1573', u'ptype': u'limit_recharge'}, None
[I 250729 01:23:36 server:41790] Message: 50, 3605, 5000000314, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill238', u'hid': u'hero760', u'fast_learn': 0}, None
[I 250729 01:23:37 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:23:40 server:41902] api call: **************, get_server_status, {}
[I 250729 01:23:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.63ms
[I 250729 01:23:40 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:23:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 32.21ms
[I 250729 01:23:40 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:23:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.03ms
[I 250729 01:23:40 server:41902] api call: **************, backup_world, {}
[I 250729 01:23:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 112.24ms
[I 250729 01:23:41 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:23:45 server:41790] Message: 44, 108, 5000000265, try_to_pay, *************, 5, {u'pid': u'gd1571', u'ptype': u'limit_recharge'}, None
[I 250729 01:23:47 server:41902] api call: **************, user_pay, {'pid': u'gd1571', 'pay_id': u'gd1571|20250729012347|ucoin', 'uid': 265, 'zone': u'5'}
[I 250729 01:23:47 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 34.58ms
[I 250729 01:23:47 server:41790] Message: 0, 2241, 5000000265, get_club_redbag, *************, 5, {}, None
[I 250729 01:23:47 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:23:48 server:41790] Message: 17, 460, 5000000314, hero_fate, *************, 5, {u'hid': u'hero7700', u'fate_id': u'fate77004'}, None
[I 250729 01:23:48 server:41790] Message: 44, 108, 5000000265, try_to_pay, *************, 5, {u'pid': u'gd1571', u'ptype': u'limit_recharge'}, None
[I 250729 01:23:49 server:41902] api call: **************, user_pay, {'pid': u'gd1571', 'pay_id': u'gd1571|20250729012349|ucoin', 'uid': 265, 'zone': u'5'}
[I 250729 01:23:49 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 34.40ms
[I 250729 01:23:50 server:41790] Message: 0, 2241, 5000000265, get_club_redbag, *************, 5, {}, None
[I 250729 01:23:50 server:41790] Message: 53, 3568, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero7700'}, None
[I 250729 01:23:51 server:41790] Message: 55, 3568, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero7700'}, None
[I 250729 01:23:51 server:41790] Message: 45, 108, 5000000265, try_to_pay, *************, 5, {u'pid': u'gd1571', u'ptype': u'limit_recharge'}, None
[I 250729 01:23:51 server:41902] api call: **************, get_server_status, {}
[I 250729 01:23:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.55ms
[I 250729 01:23:51 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:23:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 31.09ms
[I 250729 01:23:51 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:23:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.00ms
[I 250729 01:23:51 server:41902] api call: **************, backup_world, {}
[I 250729 01:23:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 106.49ms
[I 250729 01:23:51 server:41790] Message: 54, 3568, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero7700'}, None
[I 250729 01:23:51 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:23:51 server:41902] api call: **************, user_pay, {'pid': u'gd1571', 'pay_id': u'gd1571|20250729012351|ucoin', 'uid': 265, 'zone': u'5'}
[I 250729 01:23:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 34.27ms
[I 250729 01:23:51 server:41790] Message: 54, 3568, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero7700'}, None
[I 250729 01:23:51 server:41790] Message: 0, 2241, 5000000265, get_club_redbag, *************, 5, {}, None
[I 250729 01:23:52 server:41790] Message: 54, 3568, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero7700'}, None
[I 250729 01:23:52 server:41790] Message: 54, 3568, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero7700'}, None
[I 250729 01:23:52 server:41790] Message: 44, 108, 5000000265, try_to_pay, *************, 5, {u'pid': u'gd1571', u'ptype': u'limit_recharge'}, None
[I 250729 01:23:53 server:41790] Message: 54, 3568, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero7700'}, None
[I 250729 01:23:53 server:41790] Message: 54, 3568, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero7700'}, None
[I 250729 01:23:53 server:41902] api call: **************, user_pay, {'pid': u'gd1571', 'pay_id': u'gd1571|20250729012353|ucoin', 'uid': 265, 'zone': u'5'}
[I 250729 01:23:53 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 34.16ms
[I 250729 01:23:53 server:41790] Message: 53, 3568, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero7700'}, None
[I 250729 01:23:53 server:41790] Message: 0, 2241, 5000000265, get_club_redbag, *************, 5, {}, None
[I 250729 01:23:54 server:41790] Message: 54, 3570, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero7700'}, None
[I 250729 01:23:54 server:41790] Message: 53, 3570, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero7700'}, None
[I 250729 01:23:54 server:41790] Message: 55, 3570, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero7700'}, None
[I 250729 01:23:55 server:41790] Message: 53, 3570, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero7700'}, None
[I 250729 01:23:56 server:41790] Message: 44, 108, 5000000265, try_to_pay, *************, 5, {u'pid': u'gd1572', u'ptype': u'limit_recharge'}, None
[I 250729 01:23:56 server:41790] Message: 63, 3570, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero7700'}, None
[I 250729 01:23:56 server:41790] Message: 54, 3570, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero7700'}, None
[I 250729 01:23:57 server:41902] api call: **************, user_pay, {'pid': u'gd1572', 'pay_id': u'gd1572|20250729012357|ucoin', 'uid': 265, 'zone': u'5'}
[I 250729 01:23:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 34.33ms
[I 250729 01:23:57 server:41790] Message: 53, 3570, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero7700'}, None
[I 250729 01:23:57 server:41790] Message: 0, 2241, 5000000265, get_club_redbag, *************, 5, {}, None
[I 250729 01:23:57 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:23:57 server:41790] Message: 54, 3570, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero7700'}, None
[I 250729 01:23:59 server:41790] Message: 45, 108, 5000000265, try_to_pay, *************, 5, {u'pid': u'gd1572', u'ptype': u'limit_recharge'}, None
[I 250729 01:24:00 server:41902] api call: **************, user_pay, {'pid': u'gd1572', 'pay_id': u'gd1572|20250729012400|ucoin', 'uid': 265, 'zone': u'5'}
[I 250729 01:24:00 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 34.24ms
[I 250729 01:24:00 server:41790] Message: 68, 3538, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero7700'}, None
[I 250729 01:24:00 server:41790] Message: 49, 3539, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero7700'}, None
[I 250729 01:24:00 server:41790] Message: 0, 2241, 5000000265, get_club_redbag, *************, 5, {}, None
[I 250729 01:24:00 server:41790] Message: 48, 3538, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero7700'}, None
[I 250729 01:24:00 server:41790] Message: 48, 3539, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero7700'}, None
[I 250729 01:24:01 server:41790] Message: 48, 3538, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero7700'}, None
[I 250729 01:24:01 server:41790] Message: 48, 3538, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero7700'}, None
[I 250729 01:24:01 server:41790] Message: 44, 108, 5000000265, try_to_pay, *************, 5, {u'pid': u'gd1572', u'ptype': u'limit_recharge'}, None
[I 250729 01:24:01 server:41790] Message: 47, 3538, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero7700'}, None
[I 250729 01:24:01 server:41790] Message: 48, 3538, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero7700'}, None
[I 250729 01:24:01 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:24:01 server:41790] Message: 48, 3539, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero7700'}, None
[I 250729 01:24:01 server:41790] Message: 48, 3538, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero7700'}, None
[I 250729 01:24:02 server:41902] api call: **************, get_server_status, {}
[I 250729 01:24:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.62ms
[I 250729 01:24:02 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:24:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 31.68ms
[I 250729 01:24:02 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:24:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.15ms
[I 250729 01:24:02 server:41902] api call: **************, backup_world, {}
[I 250729 01:24:02 server:41790] Message: 48, 3539, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero7700'}, None
[I 250729 01:24:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 105.34ms
[I 250729 01:24:02 server:41902] api call: **************, user_pay, {'pid': u'gd1572', 'pay_id': u'gd1572|20250729012402|ucoin', 'uid': 265, 'zone': u'5'}
[I 250729 01:24:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 34.32ms
[I 250729 01:24:02 server:41790] Message: 48, 3539, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero7700'}, None
[I 250729 01:24:02 server:41790] Message: 0, 2241, 5000000265, get_club_redbag, *************, 5, {}, None
[I 250729 01:24:02 server:41790] Message: 48, 3539, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero7700'}, None
[I 250729 01:24:07 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:24:11 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:24:12 server:41902] api call: **************, get_server_status, {}
[I 250729 01:24:12 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.62ms
[I 250729 01:24:12 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:24:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 31.79ms
[I 250729 01:24:13 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:24:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.02ms
[I 250729 01:24:13 server:41902] api call: **************, backup_world, {}
[I 250729 01:24:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 111.15ms
[I 250729 01:24:16 server:41790] Message: 45, 4560, 5000000265, use_prop, *************, 5, {u'item_id': u'item087', u'item_num': 353, u'range_index': 3}, None
[I 250729 01:24:16 server:41790] Message: 18, 10280, 5000000314, hero_equip_install, *************, 5, {u'hid': u'hero773', u'eid': u'equip059'}, None
[I 250729 01:24:17 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:24:18 server:41790] Message: 54, 10292, 5000000314, hero_equip_install, *************, 5, {u'hid': u'hero773', u'eid': u'equip060'}, None
[I 250729 01:24:19 server:41790] Message: 54, 10304, 5000000314, hero_equip_install, *************, 5, {u'hid': u'hero773', u'eid': u'equip061'}, None
[I 250729 01:24:21 server:41790] Message: 55, 10316, 5000000314, hero_equip_install, *************, 5, {u'hid': u'hero773', u'eid': u'equip062'}, None
[I 250729 01:24:21 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:24:22 server:41790] Message: 55, 10344, 5000000314, hero_equip_install, *************, 5, {u'hid': u'hero773', u'eid': u'equip063'}, None
[I 250729 01:24:23 server:41902] api call: **************, get_server_status, {}
[I 250729 01:24:23 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.64ms
[I 250729 01:24:23 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:24:23 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 32.24ms
[I 250729 01:24:23 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:24:23 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.95ms
[I 250729 01:24:23 server:41902] api call: **************, backup_world, {}
[I 250729 01:24:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 104.86ms
[I 250729 01:24:27 server:41790] Message: 45, 4548, 5000000265, use_prop, *************, 5, {u'item_id': u'item098', u'item_num': 47, u'range_index': 2}, None
[I 250729 01:24:27 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:24:30 server:41790] Message: 17, 3630, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero773'}, None
[I 250729 01:24:30 server:41790] Message: 54, 3630, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero773'}, None
[I 250729 01:24:30 server:41790] Message: 53, 3630, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero773'}, None
[I 250729 01:24:31 server:41790] Message: 53, 3630, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero773'}, None
[I 250729 01:24:31 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:24:31 server:41790] Message: 53, 3630, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero773'}, None
[I 250729 01:24:32 server:41790] Message: 55, 3630, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero773'}, None
[I 250729 01:24:32 server:41790] Message: 53, 3630, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero773'}, None
[I 250729 01:24:33 server:41790] Message: 53, 3630, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero773'}, None
[I 250729 01:24:33 server:41790] Message: 54, 3630, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero773'}, None
[I 250729 01:24:34 server:41790] Message: 53, 3632, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero773'}, None
[I 250729 01:24:34 server:41790] Message: 54, 3632, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero773'}, None
[I 250729 01:24:34 server:41902] api call: **************, get_server_status, {}
[I 250729 01:24:34 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.56ms
[I 250729 01:24:34 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:24:34 server:41790] Message: 55, 3632, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero773'}, None
[I 250729 01:24:34 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 33.07ms
[I 250729 01:24:34 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:24:34 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.00ms
[I 250729 01:24:34 server:41902] api call: **************, backup_world, {}
[I 250729 01:24:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 106.94ms
[I 250729 01:24:35 server:41790] Message: 54, 3632, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero773'}, None
[I 250729 01:24:35 server:41790] Message: 58, 3632, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero773'}, None
[I 250729 01:24:36 server:41790] Message: 54, 3632, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero773'}, None
[I 250729 01:24:36 server:41790] Message: 54, 3632, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero773'}, None
[I 250729 01:24:36 server:41790] Message: 54, 3632, 5000000314, hero_star_up, *************, 5, {u'if_cost': 0, u'hid': u'hero773'}, None
[I 250729 01:24:37 server:41790] Message: 50, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill292', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:24:37 server:41790] Message: 50, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill292', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:24:37 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:24:38 server:41790] Message: 50, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill292', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:24:38 server:41790] Message: 50, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill292', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:24:38 server:41790] Message: 50, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill292', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:24:38 server:41790] Message: 51, 4968, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill292', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:24:39 server:41790] Message: 50, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill292', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:24:39 server:41790] Message: 50, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill292', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:24:41 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:24:42 server:41790] Message: 2, 12019, 5000000314, w.troop_create, *************, 5, {u'hid': u'hero773', u'is_pay': False, u'is_xyz': False}, None
[I 250729 01:24:45 server:41902] api call: **************, get_server_status, {}
[I 250729 01:24:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.67ms
[I 250729 01:24:45 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:24:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 31.87ms
[I 250729 01:24:45 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:24:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.98ms
[I 250729 01:24:45 server:41902] api call: **************, backup_world, {}
[I 250729 01:24:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 115.96ms
[I 250729 01:24:47 server:41790] Message: 2, 12017, 5000000314, w.troop_add, *************, 5, {u'hid': u'hero772', u'is_pay': False}, None
[I 250729 01:24:47 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:24:49 server:41790] Message: 2, 12016, 5000000314, w.troop_add, *************, 5, {u'hid': u'hero760', u'is_pay': False}, None
[I 250729 01:24:50 server:41790] Message: 2, 12011, 5000000314, w.troop_add, *************, 5, {u'hid': u'hero7700', u'is_pay': False}, None
[I 250729 01:24:51 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:24:51 server:41790] Message: 2, 12010, 5000000314, w.troop_add, *************, 5, {u'hid': u'hero771', u'is_pay': False}, None
[I 250729 01:24:53 server:41790] Message: 13, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill274', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:24:53 server:41790] Message: 48, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill274', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:24:54 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill274', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:24:54 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill274', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:24:54 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill274', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:24:54 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill274', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:24:55 server:41790] Message: 49, 4967, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill274', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:24:55 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill274', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:24:55 server:41790] Message: 50, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill274', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:24:56 server:41902] api call: **************, get_server_status, {}
[I 250729 01:24:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.63ms
[I 250729 01:24:56 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:24:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 31.47ms
[I 250729 01:24:56 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:24:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.97ms
[I 250729 01:24:56 server:41902] api call: **************, backup_world, {}
[I 250729 01:24:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 108.62ms
[I 250729 01:24:57 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:24:58 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill277', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:24:58 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill277', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:24:58 server:41790] Message: 50, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill277', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:24:58 server:41790] Message: 50, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill277', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:24:59 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill277', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:24:59 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill277', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:24:59 server:41790] Message: 49, 4966, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill277', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:24:59 server:41790] Message: 49, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill277', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:25:00 server:41790] Message: 49, 4965, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill277', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:25:01 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:25:07 server:41790] Message: 17, 591, 5000000314, hero_fate, *************, 5, {u'hid': u'hero772', u'fate_id': u'fate7722'}, None
[I 250729 01:25:07 server:41902] api call: **************, get_server_status, {}
[I 250729 01:25:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.57ms
[I 250729 01:25:07 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:25:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 32.09ms
[I 250729 01:25:07 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:25:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.94ms
[I 250729 01:25:07 server:41902] api call: **************, backup_world, {}
[I 250729 01:25:07 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:25:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 112.90ms
[I 250729 01:25:11 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:25:14 server:41790] Message: 59, 3708, 5000000314, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill233', u'hid': u'hero772', u'fast_learn': 0}, None
[I 250729 01:25:16 server:41790] Message: 48, 3708, 5000000314, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill233', u'hid': u'hero772', u'fast_learn': 0}, None
[I 250729 01:25:17 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:25:18 server:41790] Message: 49, 3708, 5000000314, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill233', u'hid': u'hero772', u'fast_learn': 0}, None
[I 250729 01:25:18 server:41902] api call: **************, get_server_status, {}
[I 250729 01:25:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.60ms
[I 250729 01:25:18 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:25:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 8.32ms
[I 250729 01:25:18 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:25:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.09ms
[I 250729 01:25:18 server:41902] api call: **************, backup_world, {}
[I 250729 01:25:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 114.61ms
[I 250729 01:25:20 server:41790] Message: 49, 3708, 5000000314, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill233', u'hid': u'hero772', u'fast_learn': 0}, None
[I 250729 01:25:22 server:41790] Message: 48, 3708, 5000000314, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill233', u'hid': u'hero772', u'fast_learn': 0}, None
[I 250729 01:25:22 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:25:24 server:41790] Message: 49, 3708, 5000000314, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill233', u'hid': u'hero772', u'fast_learn': 0}, None
[I 250729 01:25:27 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:25:29 server:41902] api call: **************, get_server_status, {}
[I 250729 01:25:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.64ms
[I 250729 01:25:29 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:25:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 8.57ms
[I 250729 01:25:29 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:25:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.95ms
[I 250729 01:25:29 server:41902] api call: **************, backup_world, {}
[I 250729 01:25:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 108.73ms
[I 250729 01:25:30 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:30 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:31 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:31 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:31 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:31 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:31 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:31 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:32 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:32 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:32 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:32 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:32 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:25:32 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:32 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:32 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:33 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:33 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:33 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:33 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:33 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:33 server:41790] Message: 2, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:33 server:41790] Message: 2, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:34 server:41790] Message: 2, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:34 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:34 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:34 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:34 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:35 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:35 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:35 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:35 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:35 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:35 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:35 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:36 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:36 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:36 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:36 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:36 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:36 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:37 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:37 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:37 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:37 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:37 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:25:37 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:38 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:38 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:38 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:38 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:38 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:38 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:38 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:39 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:39 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:39 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:39 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:39 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:39 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:40 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:40 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:40 server:41902] api call: **************, get_server_status, {}
[I 250729 01:25:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.51ms
[I 250729 01:25:40 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:25:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 26.30ms
[I 250729 01:25:40 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:25:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.10ms
[I 250729 01:25:40 server:41902] api call: **************, backup_world, {}
[I 250729 01:25:40 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 108.91ms
[I 250729 01:25:40 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:40 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:41 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:41 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:41 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:41 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:41 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:41 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:42 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:42 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:42 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:42 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:42 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:25:43 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:43 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:43 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:43 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:43 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:44 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:44 server:41790] Message: 60, 3723, 5000000314, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill235', u'hid': u'hero772', u'fast_learn': 0}, None
[I 250729 01:25:44 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:44 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:44 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:44 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:44 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:45 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:45 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:45 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:45 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:45 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:45 server:41790] Message: 49, 3723, 5000000314, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill235', u'hid': u'hero772', u'fast_learn': 0}, None
[I 250729 01:25:45 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:46 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:46 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:46 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:46 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:46 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:47 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:47 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:47 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:47 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:25:47 server:41790] Message: 48, 3723, 5000000314, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill235', u'hid': u'hero772', u'fast_learn': 0}, None
[I 250729 01:25:47 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:48 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:48 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:48 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:48 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:48 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:48 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:49 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:49 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:49 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:49 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:49 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:49 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:50 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:50 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:50 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:50 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:50 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:51 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:51 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:51 server:41902] api call: **************, get_server_status, {}
[I 250729 01:25:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.63ms
[I 250729 01:25:51 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:25:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 31.81ms
[I 250729 01:25:51 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:25:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.04ms
[I 250729 01:25:51 server:41902] api call: **************, backup_world, {}
[I 250729 01:25:51 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 114.42ms
[I 250729 01:25:51 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:51 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:51 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:52 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:52 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:52 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:52 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:52 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:25:52 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:53 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:53 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:53 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:53 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:53 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:53 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:54 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:54 server:41790] Message: 1, 4823, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:54 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:54 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:54 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:54 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:55 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:55 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:55 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:55 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:55 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:55 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:55 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:56 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:56 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:56 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:56 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:56 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:56 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:57 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:57 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:57 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:57 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:57 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:25:57 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:58 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:58 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:58 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:58 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:58 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:58 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:59 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:59 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:59 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:59 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:59 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:25:59 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:00 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:00 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:00 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:00 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:00 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:00 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:01 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:01 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:01 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:01 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:01 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:01 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:02 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:02 server:41902] api call: **************, get_server_status, {}
[I 250729 01:26:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.65ms
[I 250729 01:26:02 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:26:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 26.82ms
[I 250729 01:26:02 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:26:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.08ms
[I 250729 01:26:02 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:02 server:41902] api call: **************, backup_world, {}
[I 250729 01:26:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 115.50ms
[I 250729 01:26:02 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:02 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:02 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:02 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:26:02 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:03 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:03 server:41790] Message: 2, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:03 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:03 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:03 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:03 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:04 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:04 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:04 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:04 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:04 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:05 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:05 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:05 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:05 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:05 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:05 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:06 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:06 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:06 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:06 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:06 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:06 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:07 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:07 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:07 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:26:07 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:08 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:08 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:08 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:08 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:08 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:09 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:09 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:09 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:09 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:09 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:10 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:10 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:10 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:10 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:10 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:11 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:11 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:11 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:11 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:12 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:12 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:26:13 server:41902] api call: **************, get_server_status, {}
[I 250729 01:26:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.59ms
[I 250729 01:26:13 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:26:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 26.34ms
[I 250729 01:26:13 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:26:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.93ms
[I 250729 01:26:13 server:41902] api call: **************, backup_world, {}
[I 250729 01:26:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 115.52ms
[I 250729 01:26:13 server:41790] Message: 2, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:13 server:41790] Message: 2, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item767', u'resolve_num': 10}, None
[I 250729 01:26:17 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:26:18 server:41790] Message: 14, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill205', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:26:18 server:41790] Message: 50, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill205', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:26:18 server:41790] Message: 50, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill205', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:26:18 server:41790] Message: 50, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill205', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:26:19 server:41790] Message: 49, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill205', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:26:19 server:41790] Message: 50, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill205', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:26:19 server:41790] Message: 50, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill205', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:26:20 server:41790] Message: 51, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill205', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:26:20 server:41790] Message: 50, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill205', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:26:20 server:41790] Message: 51, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill205', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:26:21 server:41790] Message: 51, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill205', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:26:21 server:41790] Message: 50, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill205', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:26:21 server:41790] Message: 50, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill205', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:26:22 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:26:24 server:41902] api call: **************, get_server_status, {}
[I 250729 01:26:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.64ms
[I 250729 01:26:24 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:26:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 26.02ms
[I 250729 01:26:24 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:26:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.06ms
[I 250729 01:26:24 server:41902] api call: **************, backup_world, {}
[I 250729 01:26:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 108.50ms
[I 250729 01:26:25 server:41790] Message: 53, 544, 5000000314, hero_fate, *************, 5, {u'hid': u'hero773', u'fate_id': u'fate7731'}, None
[I 250729 01:26:27 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:26:28 server:41790] Message: 53, 556, 5000000314, hero_fate, *************, 5, {u'hid': u'hero773', u'fate_id': u'fate7734'}, None
[I 250729 01:26:29 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:29 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:29 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:29 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:30 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:30 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:30 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:30 server:41790] Message: 1, 4819, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:30 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:30 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:30 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:31 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:31 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:31 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:31 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:31 server:41790] Message: 48, 3623, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item004', u'hid': u'hero773'}, None
[I 250729 01:26:31 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:31 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:31 server:41790] Message: 48, 3623, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item004', u'hid': u'hero773'}, None
[I 250729 01:26:32 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:32 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:32 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:32 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:32 server:41790] Message: 48, 3622, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero773'}, None
[I 250729 01:26:32 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:32 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:32 server:41790] Message: 1, 4819, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:33 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:26:33 server:41790] Message: 47, 3622, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero773'}, None
[I 250729 01:26:33 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:33 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:33 server:41790] Message: 48, 3622, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero773'}, None
[I 250729 01:26:33 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:33 server:41790] Message: 48, 3622, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero773'}, None
[I 250729 01:26:33 server:41790] Message: 48, 3623, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero773'}, None
[I 250729 01:26:33 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:33 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:33 server:41790] Message: 47, 3622, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero773'}, None
[I 250729 01:26:34 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:34 server:41790] Message: 48, 3623, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero773'}, None
[I 250729 01:26:34 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:34 server:41790] Message: 48, 3623, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero773'}, None
[I 250729 01:26:34 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:34 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:34 server:41790] Message: 47, 3623, 5000000314, hero_lv_up, *************, 5, {u'item_id': u'item003', u'hid': u'hero773'}, None
[I 250729 01:26:34 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:34 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:34 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:35 server:41902] api call: **************, get_server_status, {}
[I 250729 01:26:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.55ms
[I 250729 01:26:35 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:26:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 31.89ms
[I 250729 01:26:35 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:26:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.18ms
[I 250729 01:26:35 server:41902] api call: **************, backup_world, {}
[I 250729 01:26:35 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 113.32ms
[I 250729 01:26:35 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:35 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:35 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:35 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:35 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:36 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:36 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:36 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:36 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:36 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:36 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:37 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:37 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:37 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:37 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:37 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:37 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:26:37 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:38 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:38 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:38 server:41790] Message: 1, 4819, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:38 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:38 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:38 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:38 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:39 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:39 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:39 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:39 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:39 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:39 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:40 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:40 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:40 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:40 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:41 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:41 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:41 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:41 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:41 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:41 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:42 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:42 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:42 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:42 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:42 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:42 server:41790] Message: 2, 12009, 5000000314, w.troop_add, *************, 5, {u'hid': u'hero773', u'is_pay': False}, None
[I 250729 01:26:42 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:43 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:26:43 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:43 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:43 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:43 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:43 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:43 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:44 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:44 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:44 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:44 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:44 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:44 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:45 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:45 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:45 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:45 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:45 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:45 server:41902] api call: **************, get_server_status, {}
[I 250729 01:26:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.56ms
[I 250729 01:26:45 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:45 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:26:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 32.35ms
[I 250729 01:26:46 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:26:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.04ms
[I 250729 01:26:46 server:41902] api call: **************, backup_world, {}
[I 250729 01:26:46 server:41790] Message: 2, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 114.27ms
[I 250729 01:26:46 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:46 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:46 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:46 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:46 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:47 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:47 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:47 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:47 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:47 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:47 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:26:48 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:48 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:48 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:48 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:48 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:48 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:49 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:49 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:49 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:49 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:49 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:49 server:41790] Message: 6, 2207, 5000000314, w.troop_dissmiss, *************, 5, {u'force': False, u'hid': u'hero7702'}, None
[I 250729 01:26:49 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:50 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:50 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:50 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:50 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:51 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:51 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:51 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:51 server:41790] Message: 2, 12012, 5000000314, w.troop_create, *************, 5, {u'hid': u'hero7702', u'is_pay': False, u'is_xyz': False}, None
[I 250729 01:26:51 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:51 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:51 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:52 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:52 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:52 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:52 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:52 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:53 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:53 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:26:53 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:53 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:53 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:54 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:54 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:54 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:55 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:55 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:55 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:56 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:26:56 server:41902] api call: **************, get_server_status, {}
[I 250729 01:26:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.60ms
[I 250729 01:26:56 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:26:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 32.03ms
[I 250729 01:26:56 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:26:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.94ms
[I 250729 01:26:56 server:41902] api call: **************, backup_world, {}
[I 250729 01:26:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 109.95ms
[I 250729 01:26:57 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:27:00 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:00 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:01 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:02 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:02 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:02 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:02 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:02 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:02 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:03 server:41790] Message: 199, 40518, 5000000314, pk_npc_captain_fight, *************, 5, {u'hids': [u'hero772', u'hero773', u'hero7702']}, None
[I 250729 01:27:03 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:27:03 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[E 250729 01:27:03 server:6890] data is None
[E 250729 01:27:03 server:6890] data is None
[E 250729 01:27:03 server:6890] data is None
[I 250729 01:27:03 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:03 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:03 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:03 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:03 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:04 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:04 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:04 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:04 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:04 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:05 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:05 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:05 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:06 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:06 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:07 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:27:07 server:41902] api call: **************, get_server_status, {}
[I 250729 01:27:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.53ms
[I 250729 01:27:07 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:27:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 33.23ms
[I 250729 01:27:07 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:27:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.96ms
[I 250729 01:27:07 server:41902] api call: **************, backup_world, {}
[I 250729 01:27:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 114.10ms
[I 250729 01:27:13 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:27:17 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:27:18 server:41902] api call: **************, get_server_status, {}
[I 250729 01:27:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.66ms
[I 250729 01:27:18 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:27:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.15ms
[I 250729 01:27:18 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:27:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.05ms
[I 250729 01:27:18 server:41902] api call: **************, backup_world, {}
[I 250729 01:27:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 108.12ms
[I 250729 01:27:20 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:20 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:20 server:41790] Message: 24, 5882, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle026'}, None
[I 250729 01:27:21 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:21 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:21 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:22 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:22 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:22 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:22 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:22 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:23 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:27:23 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:23 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:23 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:23 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:23 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:24 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:24 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:24 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:24 server:41790] Message: 21, 5922, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle027'}, None
[I 250729 01:27:24 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:24 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:24 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:25 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:25 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:25 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:25 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:25 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:25 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:26 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:26 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:26 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:26 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:26 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:26 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:26 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:27 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:27 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:27 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:27 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:27 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:27:27 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:28 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:28 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:28 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:28 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:28 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:28 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:28 server:41790] Message: 18, 5913, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle028'}, None
[I 250729 01:27:29 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:29 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:29 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:29 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:29 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:29 server:41902] api call: **************, get_server_status, {}
[I 250729 01:27:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.62ms
[I 250729 01:27:29 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:27:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 33.30ms
[I 250729 01:27:29 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:27:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.91ms
[I 250729 01:27:29 server:41902] api call: **************, backup_world, {}
[I 250729 01:27:29 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 111.20ms
[I 250729 01:27:30 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:30 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:30 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:30 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:30 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:30 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:30 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:31 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:31 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:31 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:31 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:31 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:31 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:32 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:32 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:32 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:32 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:32 server:41790] Message: 19, 5922, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle029'}, None
[I 250729 01:27:32 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:32 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:32 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:33 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:33 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:27:33 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:33 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:33 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:33 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:33 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:34 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:34 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:34 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:34 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:34 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:34 server:41790] Message: 1, 4819, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:35 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:35 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:35 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:35 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:35 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:35 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:35 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:36 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:36 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:36 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:36 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:36 server:41790] Message: 19, 6788, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772', u'hero773'], u'repeat': 0, u'battle_id': u'battle030'}, None
[I 250729 01:27:36 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:36 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:37 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:37 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:37 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:37 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:37 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:37 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:27:37 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:38 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:38 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:38 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:38 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:38 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:39 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:39 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:39 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:39 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:39 server:41790] Message: 1, 4819, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:39 server:41790] Message: 1, 4819, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:40 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:40 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:40 server:41790] Message: 20, 5944, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle031'}, None
[I 250729 01:27:40 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:40 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:40 server:41902] api call: **************, get_server_status, {}
[I 250729 01:27:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.61ms
[I 250729 01:27:40 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:27:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 35.73ms
[I 250729 01:27:40 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:27:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.02ms
[I 250729 01:27:40 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:40 server:41902] api call: **************, backup_world, {}
[I 250729 01:27:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 111.54ms
[I 250729 01:27:41 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:41 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:41 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:41 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:41 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:41 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:42 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:42 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:42 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:42 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:42 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:43 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:43 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:27:43 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:43 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:43 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:43 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:43 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:43 server:41790] Message: 20, 5948, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle032'}, None
[I 250729 01:27:44 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:44 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:44 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:45 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:45 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:45 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:46 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:46 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:46 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:47 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:27:48 server:41790] Message: 22, 6810, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772', u'hero773'], u'repeat': 0, u'battle_id': u'battle033'}, None
[I 250729 01:27:49 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:49 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:49 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:49 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:49 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:50 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:50 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:50 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:50 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:50 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:50 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:51 server:41902] api call: **************, get_server_status, {}
[I 250729 01:27:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.65ms
[I 250729 01:27:51 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:27:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 33.15ms
[I 250729 01:27:51 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:27:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.97ms
[I 250729 01:27:51 server:41902] api call: **************, backup_world, {}
[I 250729 01:27:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 107.32ms
[I 250729 01:27:52 server:41790] Message: 20, 5973, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle034'}, None
[I 250729 01:27:53 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:27:54 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:55 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:55 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:56 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:56 server:41790] Message: 18, 5972, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle035'}, None
[I 250729 01:27:56 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:56 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:56 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:56 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:56 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:56 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:57 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:57 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:57 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:57 server:41790] Message: 1, 4819, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:57 server:41790] Message: 1, 4819, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:57 server:41790] Message: 1, 4819, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:57 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:27:58 server:41790] Message: 1, 4819, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:58 server:41790] Message: 1, 4819, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:58 server:41790] Message: 1, 4819, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:58 server:41790] Message: 1, 4819, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:59 web:2162] 101 GET /gateway/ (39.187.110.234) 0.39ms
[I 250729 01:27:59 server:41455] WebSocket opened
[I 250729 01:27:59 server:29800] on_login, 5000000312
[I 250729 01:27:59 server:41790] Message: 5, 42323, 5000000312, login, 39.187.110.234, 5, {u'uid': 312, u'zone': u'5', u'pf_key': u'18386198068', u'pf_data': None, u'user_code': u'', u'pf': u'developer', u'sessionid': u'462964ba1e0062fe04272aa44b4d96af|1753723676|developer'}, None
[I 250729 01:27:59 server:41902] api call: **************, get_use_merge, {'zone': u'5'}
[I 250729 01:27:59 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.52ms
[I 250729 01:27:59 server:41790] Message: 1, 4819, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:59 server:41790] Message: 0, 112, 5000000312, get_my_pk_yard_hids, 39.187.110.234, 5, {}, None
[I 250729 01:27:59 server:41790] Message: 1, 4819, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:59 server:41790] Message: 30, 167299, 5000000312, w.get_info, 39.187.110.234, 5, {}, None
[I 250729 01:27:59 server:41790] Message: 1, 4818, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item710', u'resolve_num': 10}, None
[I 250729 01:27:59 server:41790] Message: 0, 688, 5000000312, get_city_visit, 39.187.110.234, 5, {}, None
[I 250729 01:27:59 server:41790] Message: 0, 106, 5000000312, get_fight_log, 39.187.110.234, 5, {}, None
[I 250729 01:27:59 server:41790] Message: 0, 523, 5000000312, w.get_my_troops, 39.187.110.234, 5, {}, None
[I 250729 01:27:59 server:41790] Message: 20, 6013, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle036'}, None
[E 250729 01:28:00 concurrent:140] Future exception was never retrieved: CurlError: HTTP 599: Connection timed out after 1040 milliseconds
[I 250729 01:28:00 server:41790] Message: 0, 199, 5000000312, get_fight_task, 39.187.110.234, 5, {}, None
[I 250729 01:28:01 server:41790] Message: 0, 1991, 5000000312, get_pk_npc, 39.187.110.234, 5, {}, None
[I 250729 01:28:02 server:41902] api call: **************, get_server_status, {}
[I 250729 01:28:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.65ms
[I 250729 01:28:02 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:28:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 38.29ms
[I 250729 01:28:02 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:28:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.08ms
[I 250729 01:28:02 server:41902] api call: **************, backup_world, {}
[I 250729 01:28:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 107.81ms
[I 250729 01:28:02 server:41790] Message: 8, 3866, 5000000314, get_pve_star_reward, *************, 5, {u'reward_index': 0, u'chapter_id': u'chapter003'}, None
[I 250729 01:28:03 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:28:03 server:41790] Message: 8, 3869, 5000000314, get_pve_star_reward, *************, 5, {u'reward_index': 1, u'chapter_id': u'chapter003'}, None
[I 250729 01:28:03 server:41790] Message: 52, 4962, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill203', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:28:03 server:41790] Message: 51, 4962, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill203', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:28:04 server:41790] Message: 50, 4962, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill203', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:28:04 server:41790] Message: 8, 3874, 5000000314, get_pve_star_reward, *************, 5, {u'reward_index': 2, u'chapter_id': u'chapter003'}, None
[I 250729 01:28:04 server:41790] Message: 51, 4962, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill203', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:28:05 server:41790] Message: 51, 4962, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill203', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:28:05 server:41790] Message: 51, 4962, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill203', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:28:07 server:41790] Message: 20, 6074, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle037'}, None
[I 250729 01:28:07 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:28:09 server:41790] Message: 0, 219, 5000000312, update_user, 39.187.110.234, 5, {u'key': [u'coin']}, None
[I 250729 01:28:10 server:41790] Message: 17, 6076, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle038'}, None
[I 250729 01:28:11 server:41790] Message: 2, 2477, 5000000312, w.get_users, 39.187.110.234, 5, {u'type': 0, u'page': 0}, None
[I 250729 01:28:13 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:28:13 server:41790] Message: 1, 8559, 5000000312, user_info, 39.187.110.234, 5, {u'uid': 5000000299}, None
[I 250729 01:28:13 server:41902] api call: **************, get_server_status, {}
[I 250729 01:28:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.67ms
[I 250729 01:28:13 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:28:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 32.94ms
[I 250729 01:28:13 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:28:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.02ms
[I 250729 01:28:13 server:41902] api call: **************, backup_world, {}
[I 250729 01:28:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 108.43ms
[I 250729 01:28:14 server:41790] Message: 17, 6098, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle039'}, None
[I 250729 01:28:17 server:41790] Message: 1, 8559, 5000000312, user_info, 39.187.110.234, 5, {u'uid': 5000000299}, None
[I 250729 01:28:17 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:28:18 server:41790] Message: 19, 6102, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle040'}, None
[I 250729 01:28:19 server:41790] Message: 0, 219, 5000000312, update_user, 39.187.110.234, 5, {u'key': [u'coin']}, None
[I 250729 01:28:22 server:41790] Message: 17, 6102, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle041'}, None
[I 250729 01:28:23 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:28:24 server:41902] api call: **************, get_server_status, {}
[I 250729 01:28:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.64ms
[I 250729 01:28:24 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:28:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 10.56ms
[I 250729 01:28:24 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:28:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.99ms
[I 250729 01:28:24 server:41902] api call: **************, backup_world, {}
[I 250729 01:28:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 107.47ms
[I 250729 01:28:25 server:41790] Message: 19, 6126, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle042'}, None
[I 250729 01:28:27 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:28:29 server:41790] Message: 0, 219, 5000000312, update_user, 39.187.110.234, 5, {u'key': [u'coin']}, None
[I 250729 01:28:29 server:41790] Message: 17, 6124, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle043'}, None
[I 250729 01:28:32 server:41790] Message: 8, 3961, 5000000314, get_pve_star_reward, *************, 5, {u'reward_index': 0, u'chapter_id': u'chapter004'}, None
[I 250729 01:28:33 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:28:34 server:41790] Message: 21, 6128, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle044'}, None
[I 250729 01:28:35 server:41902] api call: **************, get_server_status, {}
[I 250729 01:28:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.60ms
[I 250729 01:28:35 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:28:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 9.49ms
[I 250729 01:28:35 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:28:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.95ms
[I 250729 01:28:35 server:41902] api call: **************, backup_world, {}
[I 250729 01:28:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 111.91ms
[I 250729 01:28:37 server:41790] Message: 2, 2477, 5000000312, w.get_users, 39.187.110.234, 5, {u'type': 1, u'page': 0}, None
[I 250729 01:28:37 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:28:38 server:41790] Message: 20, 6152, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle045'}, None
[I 250729 01:28:38 server:41790] Message: 44, 108, 5000000265, try_to_pay, *************, 5, {u'pid': u'gd1572', u'ptype': u'limit_recharge'}, None
[I 250729 01:28:39 server:41790] Message: 0, 219, 5000000312, update_user, 39.187.110.234, 5, {u'key': [u'coin']}, None
[I 250729 01:28:39 server:41902] api call: **************, user_pay, {'pid': u'gd1572', 'pay_id': u'gd1572|20250729012839|ucoin', 'uid': 265, 'zone': u'5'}
[I 250729 01:28:39 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 34.30ms
[I 250729 01:28:40 server:41790] Message: 0, 2241, 5000000265, get_club_redbag, *************, 5, {}, None
[I 250729 01:28:41 server:41790] Message: 2, 12909, 5000000312, user_info, 39.187.110.234, 5, {u'uid': 5000000265}, None
[I 250729 01:28:42 server:41790] Message: 16, 6149, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle046'}, None
[I 250729 01:28:43 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:28:45 server:41790] Message: 19, 6150, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772'], u'repeat': 0, u'battle_id': u'battle047'}, None
[I 250729 01:28:46 server:41902] api call: **************, get_server_status, {}
[I 250729 01:28:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.62ms
[I 250729 01:28:46 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:28:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 35.16ms
[I 250729 01:28:46 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:28:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.03ms
[I 250729 01:28:46 server:41902] api call: **************, backup_world, {}
[I 250729 01:28:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 112.12ms
[I 250729 01:28:47 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:28:49 server:41790] Message: 0, 219, 5000000312, update_user, 39.187.110.234, 5, {u'key': [u'coin']}, None
[I 250729 01:28:50 server:41790] Message: 23, 7043, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772', u'hero773'], u'repeat': 0, u'battle_id': u'battle048'}, None
[I 250729 01:28:52 server:41790] Message: 45, 4543, 5000000265, use_prop, *************, 5, {u'item_id': u'item098', u'item_num': 14, u'range_index': 3}, None
[I 250729 01:28:52 server:41790] Message: 7, 4026, 5000000314, get_pve_star_reward, *************, 5, {u'reward_index': 1, u'chapter_id': u'chapter004'}, None
[I 250729 01:28:53 server:41790] Message: 2, 12769, 5000000312, user_info, 39.187.110.234, 5, {u'uid': 5000000276}, None
[I 250729 01:28:53 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:28:53 server:41790] Message: 8, 4030, 5000000314, get_pve_star_reward, *************, 5, {u'reward_index': 2, u'chapter_id': u'chapter004'}, None
[I 250729 01:28:56 server:41790] Message: 26, 7113, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772', u'hero773'], u'repeat': 0, u'battle_id': u'battle049'}, None
[I 250729 01:28:57 server:41902] api call: **************, get_server_status, {}
[I 250729 01:28:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.57ms
[I 250729 01:28:57 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:28:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 32.59ms
[I 250729 01:28:57 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:28:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.94ms
[I 250729 01:28:57 server:41902] api call: **************, backup_world, {}
[I 250729 01:28:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 115.99ms
[I 250729 01:28:57 server:41790] Message: 2, 14489, 5000000312, user_info, 39.187.110.234, 5, {u'uid': 5000000245}, None
[I 250729 01:28:57 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:28:59 server:41790] Message: 0, 219, 5000000312, update_user, 39.187.110.234, 5, {u'key': [u'coin']}, None
[I 250729 01:29:01 server:41790] Message: 22, 7118, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772', u'hero773'], u'repeat': 0, u'battle_id': u'battle050'}, None
[I 250729 01:29:01 server:41790] Message: 2, 12909, 5000000312, user_info, 39.187.110.234, 5, {u'uid': 5000000265}, None
[I 250729 01:29:03 server:41790] Message: 2, 12769, 5000000312, user_info, 39.187.110.234, 5, {u'uid': 5000000276}, None
[I 250729 01:29:03 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:29:04 server:41790] Message: 2, 13511, 5000000312, user_info, 39.187.110.234, 5, {u'uid': 5000000243}, None
[I 250729 01:29:05 server:41790] Message: 27, 7142, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772', u'hero773'], u'repeat': 0, u'battle_id': u'battle051'}, None
[I 250729 01:29:06 server:41790] Message: 2, 12892, 5000000312, user_info, 39.187.110.234, 5, {u'uid': 5000000273}, None
[I 250729 01:29:07 server:41790] Message: 2, 11002, 5000000312, user_info, 39.187.110.234, 5, {u'uid': 5000000264}, None
[I 250729 01:29:08 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:29:08 server:41902] api call: **************, get_server_status, {}
[I 250729 01:29:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.61ms
[I 250729 01:29:08 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:29:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 9.38ms
[I 250729 01:29:08 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:29:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.90ms
[I 250729 01:29:08 server:41902] api call: **************, backup_world, {}
[I 250729 01:29:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 118.89ms
[I 250729 01:29:08 server:41790] Message: 3, 10947, 5000000312, user_info, 39.187.110.234, 5, {u'uid': 5000000252}, None
[I 250729 01:29:09 server:41790] Message: 0, 219, 5000000312, update_user, 39.187.110.234, 5, {u'key': [u'coin']}, None
[I 250729 01:29:09 server:41790] Message: 22, 7157, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772', u'hero773'], u'repeat': 0, u'battle_id': u'battle052'}, None
[I 250729 01:29:10 server:41790] Message: 1, 9298, 5000000312, user_info, 39.187.110.234, 5, {u'uid': 5000000270}, None
[I 250729 01:29:13 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:29:13 server:41790] Message: 22, 7156, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772', u'hero773'], u'repeat': 0, u'battle_id': u'battle053'}, None
[I 250729 01:29:13 server:41790] Message: 0, 2239, 5000000312, get_club_redbag, 39.187.110.234, 5, {}, None
[I 250729 01:29:13 server:41790] Message: 0, 263, 5000000312, update_user, 39.187.110.234, 5, {u'key': [u'year_build', u'year_dead_num', u'year_kill_num']}, None
[I 250729 01:29:18 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:29:18 server:41790] Message: 22, 7182, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772', u'hero773'], u'repeat': 0, u'battle_id': u'battle054'}, None
[I 250729 01:29:19 server:41902] api call: **************, get_server_status, {}
[I 250729 01:29:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.62ms
[I 250729 01:29:19 server:41790] Message: 0, 219, 5000000312, update_user, 39.187.110.234, 5, {u'key': [u'coin']}, None
[I 250729 01:29:19 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:29:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 9.57ms
[I 250729 01:29:19 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:29:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 1.03ms
[I 250729 01:29:19 server:41902] api call: **************, backup_world, {}
[I 250729 01:29:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 111.77ms
[I 250729 01:29:19 server:41790] Message: 2, 2477, 5000000312, w.get_users, 39.187.110.234, 5, {u'type': 0, u'page': 0}, None
[I 250729 01:29:20 server:41790] Message: 1, 8559, 5000000312, user_info, 39.187.110.234, 5, {u'uid': 5000000299}, None
[I 250729 01:29:21 server:41790] Message: 1, 4819, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:22 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:22 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:22 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:22 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:22 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:23 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:23 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:23 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:29:23 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:23 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:23 server:41790] Message: 19, 7199, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772', u'hero773'], u'repeat': 0, u'battle_id': u'battle055'}, None
[I 250729 01:29:23 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:24 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:24 server:41790] Message: 1, 4819, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:24 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:24 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:24 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:24 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:25 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:25 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:25 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:25 server:41790] Message: 1, 4819, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:25 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:25 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:25 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:26 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:26 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:26 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:26 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:26 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:27 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:27 server:41790] Message: 1, 4819, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:27 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:27 server:41790] Message: 22, 7198, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772', u'hero773'], u'repeat': 0, u'battle_id': u'battle056'}, None
[I 250729 01:29:27 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:27 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:27 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:27 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:28 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:28 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:29:28 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:28 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:29 server:41790] Message: 0, 219, 5000000312, update_user, 39.187.110.234, 5, {u'key': [u'coin']}, None
[I 250729 01:29:30 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:30 server:41902] api call: **************, get_server_status, {}
[I 250729 01:29:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.53ms
[I 250729 01:29:30 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:29:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 33.76ms
[I 250729 01:29:30 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:29:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.90ms
[I 250729 01:29:30 server:41902] api call: **************, backup_world, {}
[I 250729 01:29:30 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 111.11ms
[I 250729 01:29:30 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:30 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:30 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:31 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:31 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:31 server:41790] Message: 21, 7226, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772', u'hero773'], u'repeat': 0, u'battle_id': u'battle057'}, None
[I 250729 01:29:31 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:31 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:31 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:31 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:32 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:32 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:32 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:32 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:32 server:41790] Message: 2, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:32 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:33 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:33 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:33 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:29:33 server:41790] Message: 1, 4822, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:33 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:33 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:33 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:34 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:34 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:34 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:35 server:41790] Message: 23, 7241, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772', u'hero773'], u'repeat': 0, u'battle_id': u'battle058'}, None
[I 250729 01:29:38 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:29:38 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:38 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:38 server:41790] Message: 22, 7240, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772', u'hero773'], u'repeat': 0, u'battle_id': u'battle059'}, None
[I 250729 01:29:39 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:39 server:41790] Message: 0, 219, 5000000312, update_user, 39.187.110.234, 5, {u'key': [u'coin']}, None
[I 250729 01:29:39 server:41790] Message: 1, 4821, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:39 server:41790] Message: 1, 4820, 5000000265, hero_resolve, *************, 5, {u'item_id': u'item766', u'resolve_num': 10}, None
[I 250729 01:29:41 server:41902] api call: **************, get_server_status, {}
[I 250729 01:29:41 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.64ms
[I 250729 01:29:41 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:29:41 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 33.41ms
[I 250729 01:29:41 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:29:41 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.99ms
[I 250729 01:29:41 server:41902] api call: **************, backup_world, {}
[I 250729 01:29:41 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 111.55ms
[I 250729 01:29:42 server:41790] Message: 21, 7282, 5000000314, pve_combat, *************, 5, {u'hids': [u'hero7702', u'hero772', u'hero773'], u'repeat': 0, u'battle_id': u'battle060'}, None
[I 250729 01:29:43 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:29:43 server:41790] Message: 51, 4964, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill224', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:29:44 server:41790] Message: 51, 4963, 5000000265, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill224', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:29:45 server:41790] Message: 8, 4236, 5000000314, get_pve_star_reward, *************, 5, {u'reward_index': 0, u'chapter_id': u'chapter005'}, None
[I 250729 01:29:45 server:41790] Message: 8, 4240, 5000000314, get_pve_star_reward, *************, 5, {u'reward_index': 1, u'chapter_id': u'chapter005'}, None
[I 250729 01:29:46 server:41790] Message: 8, 4243, 5000000314, get_pve_star_reward, *************, 5, {u'reward_index': 2, u'chapter_id': u'chapter005'}, None
[I 250729 01:29:48 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:29:49 server:41790] Message: 0, 219, 5000000312, update_user, 39.187.110.234, 5, {u'key': [u'coin']}, None
[I 250729 01:29:51 server:41902] api call: **************, get_server_status, {}
[I 250729 01:29:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.61ms
[I 250729 01:29:51 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:29:52 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 33.37ms
[I 250729 01:29:52 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:29:52 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.98ms
[I 250729 01:29:52 server:41902] api call: **************, backup_world, {}
[I 250729 01:29:52 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 110.21ms
[I 250729 01:29:53 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:29:54 server:41790] Message: 12, 3807, 5000000314, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill209', u'hid': u'hero772', u'fast_learn': 0}, None
[I 250729 01:29:58 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:29:59 server:41790] Message: 0, 219, 5000000312, update_user, 39.187.110.234, 5, {u'key': [u'coin']}, None
[I 250729 01:30:02 server:41902] api call: **************, get_server_status, {}
[I 250729 01:30:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.67ms
[I 250729 01:30:02 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:30:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 9.74ms
[I 250729 01:30:02 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:30:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.97ms
[I 250729 01:30:02 server:41902] api call: **************, backup_world, {}
[I 250729 01:30:03 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 109.01ms
[I 250729 01:30:03 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:30:08 server:41790] Message: 0, 220, 5000000265, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:30:09 server:41790] Message: 0, 219, 5000000312, update_user, 39.187.110.234, 5, {u'key': [u'coin']}, None
[I 250729 01:30:09 server:41790] Message: 59, 3765, 5000000314, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill289', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:30:10 server:41790] Message: 0, 686, 5000000312, get_gtask, 39.187.110.234, 5, {}, None
[I 250729 01:30:13 server:41790] Message: 0, 221, 5000000314, update_user, *************, 5, {u'key': [u'coin']}, None
[I 250729 01:30:13 server:41902] api call: **************, get_server_status, {}
[I 250729 01:30:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.62ms
[I 250729 01:30:13 server:41902] api call: **************, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:30:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 14.71ms
[I 250729 01:30:13 server:41902] api call: **************, backup_fight_log, {'if_all': 0}
[I 250729 01:30:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 0.96ms
[I 250729 01:30:13 server:41902] api call: **************, backup_world, {}
[I 250729 01:30:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 107.34ms
[I 250729 01:30:15 server:41790] Message: 45, 108, 5000000265, try_to_pay, *************, 5, {u'pid': u'gd1572', u'ptype': u'limit_recharge'}, None
[I 250729 01:30:15 server:41790] Message: 58, 3780, 5000000314, hero_skill_lv_up, *************, 5, {u'skill_id': u'skill219', u'hid': u'hero7702', u'fast_learn': 0}, None
[I 250729 01:30:16 server:41902] api call: **************, user_pay, {'pid': u'gd1572', 'pay_id': u'gd1572|20250729013016|ucoin', 'uid': 265, 'zone': u'5'}
[I 250729 01:30:16 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (**************) 34.49ms
