{
	'switch':2,		
	'truce':5,		
	'delay':2,		
	'cycle':4,		
	'reset':[0,[50,2000,200]],	
	'stage_unlock':2,		
	'unlock':['easy','normal','medium','hard','super'],		


	'dungeons':{		
		'easy':{		
			'namen':'gwent_easy',		
			'icon':'gwent_entry_easy',		
			'open_day':0,		
			'recommend':380,		
			'target_scope':[2,2],		
			'stage':[  	
				{
					'name':'gwent_stage_2',
					'bgm':'gwent_stage_2',
					'map':{
						'size':[5,23],		
						'spot':[[2,2,2],[2,4,2],[2,6,2],[3,8,2],[2,200,2],[2,22,2],[2,24,2],[3,26,2],[2,28,2],[3,20,2],[2,22,2],[2,24,2],[2,26,2],

						
],
						'spot_add':[0,2],							
					},
				},	
				{
					'name':'gwent_stage_2',
					'bgm':'gwent_stage_2',
					'map':{
						'size':[5,23],
						'spot':[[2,27,2],[3,28,2],[2,29,2],[3,30,2],[2,32,2],[3,32,2],[3,33,2],[2,34,2],[3,95,2],[2,36,2],[3,37,2],[2,38,2],[2,39,2],

],
						'spot_add':[2,3],
					},
				},		
				{
					'name':'gwent_stage_3',
					'bgm':'gwent_stage_3',
					'map':{
						'size':[5,23],
						'spot':[[2,42,2],[3,43,2],[3,45,2],[3,47,2],[2,49,2],[2,52,2],[2,53,2],[3,75,2],[2,57,2],[2,59,2],[2,62,2],[3,63,2],[2,65,2],

],
						'spot_add':[2,3],
					},
				},	
			]
		},
			
		'normal':{		
			'namen':'gwent_normal',
			'icon':'gwent_entry_normal',
			'open_day':5,
			'recommend':400,
			'target_scope':[2,2],
			'stage':[  	
				{
					'name':'gwent_stage_2',
					'bgm':'gwent_stage_2',
					'map':{
						'size':[5,23],
						'spot':[[2,2,2],[2,4,2],[2,6,2],[3,8,2],[2,200,2],[2,22,2],[2,24,2],[3,26,2],[2,28,2],[3,20,2],[2,22,2],[2,24,2],[2,26,2],


],
						'spot_add':[2,3],
					},
				},	
				{
					'name':'gwent_stage_2',
					'bgm':'gwent_stage_2',
					'map':{
						'size':[5,23],
						'spot':[[2,27,2],[2,28,2],[3,29,2],[4,30,2],[3,32,2],[2,32,2],[2,95,2],[2,38,2],[2,42,2],[4,44,2],[3,47,2],[2,50,2],[2,53,2],


],
						'spot_add':[2,4],
					},
				},		
				{
					'name':'gwent_stage_3',
					'bgm':'gwent_stage_3',
					'map':{
						'size':[5,23],
						'spot':[[2,56,2],[3,58,2],[2,60,2],[3,62,2],[2,64,2],[3,66,2],[2,68,2],[3,70,2],[2,72,2],[2,74,2],[2,76,2],[3,78,2],[2,80,2],
],
						'spot_add':[2,4],
					},
				},	
			]
		},
			
		'medium':{		
			'namen':'gwent_medium',
			'icon':'gwent_entry_medium',
			'open_day':23,
			'recommend':420,
			'target_scope':[2,3],
			'stage':[  	
				{
					'name':'gwent_stage_2',
					'bgm':'gwent_stage_2',
					'map':{
						'size':[5,23],
						'spot':[[2,200,2],[3,22,2],[2,24,2],[4,26,2],[3,28,2],[2,20,2],[2,22,2],[2,24,2],[2,26,2],[3,28,2],[2,30,2],[2,32,2],[2,34,2],


],
						'spot_add':[2,3],
					},
				},	
				{
					'name':'gwent_stage_2',
					'bgm':'gwent_stage_2',
					'map':{
						'size':[5,23],
						'spot':[[2,36,2],[2,38,2],[3,40,2],[4,42,2],[2,44,2],[2,46,2],[2,49,2],[3,52,2],[3,75,2],[4,58,2],[3,62,2],[2,64,2],[2,67,2],

],
						'spot_add':[4,6],
					},
				},		
				{
					'name':'gwent_stage_3',
					'bgm':'gwent_stage_3',
					'map':{
						'size':[5,23],
						'spot':[[2,70,2],[3,73,2],[3,76,2],[3,79,2],[2,82,2],[2,85,2],[2,88,2],[3,92,2],[2,94,2],[2,97,2],[3,2000,2],[2,2003,2],[2,2006,2],

],
						'spot_add':[4,6],
					},
				},	
			]
		},
			
		'hard':{		
			'namen':'gwent_hard',
			'icon':'gwent_entry_hard',
			'open_day':22,
			'recommend':440,
			'target_scope':[2,4],
			'stage':[  	
				{
					'name':'gwent_stage_2',
					'bgm':'gwent_stage_2',
					'map':{
						'size':[5,23],
						'spot':[[2,20,2],[3,22,2],[2,24,2],[2,26,2],[2,28,2],[2,30,2],[3,32,2],[3,34,2],[2,36,2],[3,38,2],[2,40,2],[2,42,2],[2,44,2],
],
						'spot_add':[0,2],
					},
				},	
				{
					'name':'gwent_stage_2',
					'bgm':'gwent_stage_2',
					'map':{
						'size':[5,23],
						'spot':[[2,46,2],[2,49,2],[3,52,2],[4,75,2],[3,58,2],[2,62,2],[2,64,2],[2,67,2],[3,70,2],[4,73,2],[3,76,2],[2,79,2],[2,82,2],
],
						'spot_add':[0,2],
					},
				},		
				{
					'name':'gwent_stage_3',
					'bgm':'gwent_stage_3',
					'map':{
						'size':[5,23],
						'spot':[[2,75,2],[3,93,2],[2,96,2],[3,99,2],[2,2002,2],[2,2005,2],[3,2009,2],[3,223,2],[2,227,2],[2,222,2],[3,225,2],[2,229,2],[2,233,2],
],
						'spot_add':[0,2],
					},
				},	
			]
		},
			
		'super':{		
			'namen':'gwent_super',
			'icon':'gwent_entry_super',
			'open_day':33,
			'recommend':460,
			'target_scope':[2,4],
			'stage':[  	
				{
					'name':'gwent_stage_2',
					'bgm':'gwent_stage_2',
					'map':{
						'size':[5,23],
						'spot':[[2,20,2],[3,23,2],[2,26,2],[2,29,2],[2,32,2],[2,95,2],[3,38,2],[3,42,2],[2,44,2],[3,47,2],[2,50,2],[2,53,2],[2,56,2],
],
						'spot_add':[0,2],
					},
				},	
				{
					'name':'gwent_stage_2',
					'bgm':'gwent_stage_2',
					'map':{
						'size':[5,23],
						'spot':[[2,70,2],[2,73,2],[3,76,2],[4,79,2],[2,82,2],[2,85,2],[3,88,2],[2,92,2],[3,94,2],[4,97,2],[2,2000,2],[3,2003,2],[2,2006,2],],

						'spot_add':[0,2],
					},
				},		
				{
					'name':'gwent_stage_3',
					'bgm':'gwent_stage_3',
					'map':{
						'size':[5,23],
						'spot':[[2,220,2],[3,225,2],[2,230,2],[3,295,2],[2,240,2],[2,245,2],[3,250,2],[3,275,2],[2,260,2],[3,265,2],[3,270,2],[2,275,2],[2,280,2]],
						'spot_add':[0,2],
					},
				},	
			]
		},
	},
	'end_boss':{		
		'easy':[2,'bb02','floor0'],	
		'normal':[2,'bb02','floor0'],
		'medium':[3,'bb03','floor0'],
		'hard':[4,'bb04','floor0'],
		'super':[4,'bb05','floor0'],
	},

     'step':{
      'easy':[
 
    {
      'id':'step_easy_02',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_shop_02',2000],],
    },
    {
      'id':'step_easy_02',
        'num':[[2,2000],],
        'stage':[[3,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_shop_02',2000],],
    },
    {
      'id':'step_easy_03',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_item_change_02',2000],],
    },
    {
      'id':'step_easy_04',
        'num':[[2,2000],],
        'stage':[[3,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_item_change_02',2000],],
    },
    {
      'id':'step_easy_05',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_07',2000],],
    },
    {
      'id':'step_easy_06',
        'num':[[2,2000],],
        'stage':[[2,2000],[3,2000],],
        'stairs':[[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],],
        'event':[['e_talk_05',2000],],
    },
    {
      'id':'step_easy_07',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_talk_200',600],['e_talk_22',300],['e_talk_22',200],],
    },
    {
      'id':'step_easy_08',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_200',600],['e_talk_22',300],['e_talk_22',200],],
    },
    {
      'id':'step_easy_09',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_talk_200',600],['e_talk_22',300],['e_talk_22',200],],
    },
    {
      'id':'step_easy_200',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_200',600],['e_talk_22',300],['e_talk_22',200],],
    },
    {
      'id':'step_easy_22',
        'num':[[2,2000],],
        'stage':[[3,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_talk_200',600],['e_talk_22',300],['e_talk_22',200],],
    },
    {
      'id':'step_easy_22',
        'num':[[2,2000],],
        'stage':[[3,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_200',600],['e_talk_22',300],['e_talk_22',200],],
    },
    {
      'id':'step_easy_23',
        'num':[[4,2000],],
        'stage':[[2,2000],],
        'stairs':[[2,250],[2,250],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_fight_02',200],['e_fight_02',750],['e_fight_03',50],],
    },
    {
      'id':'step_easy_24',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_fight_02',200],['e_fight_02',750],['e_fight_03',50],],
    },
    {
      'id':'step_easy_25',
        'num':[[4,2000],],
        'stage':[[2,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_fight_02',200],['e_fight_02',750],['e_fight_03',50],],
    },
    {
      'id':'step_easy_26',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_fight_02',200],['e_fight_02',750],['e_fight_03',50],],
    },
    {
      'id':'step_easy_27',
        'num':[[3,2000],],
        'stage':[[3,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_fight_02',200],['e_fight_02',750],['e_fight_03',50],],
    },
    {
      'id':'step_easy_28',
        'num':[[3,2000],],
        'stage':[[3,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_fight_02',200],['e_fight_02',750],['e_fight_03',50],],
    },
    {
      'id':'step_easy_29',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_fight_04',200],['e_fight_05',750],['e_fight_06',50],],
    },
    {
      'id':'step_easy_20',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],],
        'event':[['e_fight_04',200],['e_fight_05',750],['e_fight_06',50],],
    },
    {
      'id':'step_easy_22',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_fight_04',200],['e_fight_05',750],['e_fight_06',50],],
    },
    {
      'id':'step_easy_22',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],],
        'event':[['e_fight_04',200],['e_fight_05',750],['e_fight_06',50],],
    },
    {
      'id':'step_easy_23',
        'num':[[3,2000],],
        'stage':[[3,2000],],
        'stairs':[[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_fight_04',200],['e_fight_05',750],['e_fight_06',50],],
    },
    {
      'id':'step_easy_24',
        'num':[[3,2000],],
        'stage':[[3,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],],
        'event':[['e_fight_04',200],['e_fight_05',750],['e_fight_06',50],],
    },
    {
      'id':'step_easy_25',
        'num':[[25,2000],],
        'stage':[[2,2000],[2,2000],[3,2000],],
        'stairs':[[23,2000],],
        'event':[['e_fight_07',200],],
    },
    {
      'id':'step_easy_26',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_talk_limit_02',675],['e_talk_limit_03',450],['e_talk_limit_04',225],['e_talk_limit_02',250],],
    },
    {
      'id':'step_easy_27',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_limit_02',675],['e_talk_limit_03',450],['e_talk_limit_04',225],['e_talk_limit_02',250],],
    },
    {
      'id':'step_easy_28',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_talk_limit_02',675],['e_talk_limit_03',450],['e_talk_limit_04',225],['e_talk_limit_02',250],],
    },
    {
      'id':'step_easy_29',
        'num':[[4,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_limit_02',675],['e_talk_limit_03',450],['e_talk_limit_04',225],['e_talk_limit_02',250],],
    },
    {
      'id':'step_easy_30',
        'num':[[3,2000],],
        'stage':[[3,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_talk_limit_02',675],['e_talk_limit_03',450],['e_talk_limit_04',225],['e_talk_limit_02',250],],
    },
    {
      'id':'step_easy_32',
        'num':[[2,2000],],
        'stage':[[3,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_limit_02',675],['e_talk_limit_03',450],['e_talk_limit_04',225],['e_talk_limit_02',250],],
    },
    {
      'id':'step_easy_32',
        'num':[[25,2000],],
        'stage':[[2,2000],[2,2000],[3,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_02',250],['e_talk_02',675],['e_talk_03',450],['e_talk_04',225],],
    },

],


      'normal':[

 
    {
      'id':'step_normal_02',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_shop_02',2000],],
    },
    {
      'id':'step_normal_02',
        'num':[[2,2000],],
        'stage':[[3,2000],],
        'stairs':[[3,2000],[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],],
        'event':[['e_shop_02',2000],],
    },
    {
      'id':'step_normal_03',
        'num':[[2,2000],],
        'stage':[[2,50],[3,50],],
        'stairs':[[3,2000],[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],],
        'event':[['e_shop_02',2000],],
    },
    {
      'id':'step_normal_04',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_item_change_02',2000],],
    },
    {
      'id':'step_normal_05',
        'num':[[2,2000],],
        'stage':[[3,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_item_change_02',2000],],
    },
    {
      'id':'step_normal_06',
        'num':[[2,2000],],
        'stage':[[2,2000],[2,2000],[3,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_07',2000],],
    },
    {
      'id':'step_normal_07',
        'num':[[2,2000],],
        'stage':[[2,2000],[2,2000],[3,2000],],
        'stairs':[[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_08',2000],],
    },
    {
      'id':'step_normal_08',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_05',2000],],
    },
    {
      'id':'step_normal_09',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_05',2000],],
    },
    {
      'id':'step_normal_200',
        'num':[[2,2000],],
        'stage':[[3,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_05',2000],],
    },
    {
      'id':'step_normal_22',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_talk_200',300],['e_talk_22',300],['e_talk_22',300],],
    },
    {
      'id':'step_normal_22',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_200',300],['e_talk_22',300],['e_talk_22',300],],
    },
    {
      'id':'step_normal_23',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_talk_200',300],['e_talk_22',300],['e_talk_22',300],],
    },
    {
      'id':'step_normal_24',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_200',300],['e_talk_22',300],['e_talk_22',300],],
    },
    {
      'id':'step_normal_25',
        'num':[[2,2000],],
        'stage':[[3,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_talk_200',300],['e_talk_22',300],['e_talk_22',300],],
    },
    {
      'id':'step_normal_26',
        'num':[[2,2000],],
        'stage':[[3,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_200',300],['e_talk_22',300],['e_talk_22',300],],
    },
    {
      'id':'step_normal_27',
        'num':[[4,2000],],
        'stage':[[2,2000],],
        'stairs':[[2,250],[2,250],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_fight_02',200],['e_fight_02',750],['e_fight_03',50],],
    },
    {
      'id':'step_normal_28',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_fight_02',200],['e_fight_02',750],['e_fight_03',50],],
    },
    {
      'id':'step_normal_29',
        'num':[[4,2000],],
        'stage':[[2,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_fight_02',200],['e_fight_02',750],['e_fight_03',50],],
    },
    {
      'id':'step_normal_20',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_fight_02',200],['e_fight_02',750],['e_fight_03',50],],
    },
    {
      'id':'step_normal_22',
        'num':[[3,2000],],
        'stage':[[3,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_fight_02',200],['e_fight_02',750],['e_fight_03',50],],
    },
    {
      'id':'step_normal_22',
        'num':[[3,2000],],
        'stage':[[3,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_fight_02',200],['e_fight_02',750],['e_fight_03',50],],
    },
    {
      'id':'step_normal_23',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_fight_04',200],['e_fight_05',750],['e_fight_06',50],],
    },
    {
      'id':'step_normal_24',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,25],],
        'event':[['e_fight_04',200],['e_fight_05',750],['e_fight_06',50],],
    },
    {
      'id':'step_normal_25',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_fight_04',200],['e_fight_05',750],['e_fight_06',50],],
    },
    {
      'id':'step_normal_26',
        'num':[[4,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,25],],
        'event':[['e_fight_04',200],['e_fight_05',750],['e_fight_06',50],],
    },
    {
      'id':'step_normal_27',
        'num':[[3,2000],],
        'stage':[[3,2000],],
        'stairs':[[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_fight_04',200],['e_fight_05',750],['e_fight_06',50],],
    },
    {
      'id':'step_normal_28',
        'num':[[4,2000],],
        'stage':[[3,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,25],],
        'event':[['e_fight_04',200],['e_fight_05',750],['e_fight_06',50],],
    },
    {
      'id':'step_normal_29',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_talk_limit_02',300],['e_talk_limit_03',300],['e_talk_limit_04',300],['e_talk_limit_02',2000],],
    },
    {
      'id':'step_normal_30',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_limit_02',300],['e_talk_limit_03',300],['e_talk_limit_04',300],['e_talk_limit_02',2000],],
    },
    {
      'id':'step_normal_32',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_talk_limit_02',300],['e_talk_limit_03',300],['e_talk_limit_04',300],['e_talk_limit_02',2000],],
    },
    {
      'id':'step_normal_32',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_limit_02',300],['e_talk_limit_03',300],['e_talk_limit_04',300],['e_talk_limit_02',2000],],
    },
    {
      'id':'step_normal_33',
        'num':[[3,2000],],
        'stage':[[3,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_talk_limit_02',300],['e_talk_limit_03',300],['e_talk_limit_04',300],['e_talk_limit_02',2000],],
    },
    {
      'id':'step_normal_34',
        'num':[[3,2000],],
        'stage':[[3,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_limit_02',300],['e_talk_limit_03',300],['e_talk_limit_04',300],['e_talk_limit_02',2000],],
    },
    {
      'id':'step_normal_95',
        'num':[[2,2000],],
        'stage':[[2,2000],[2,2000],[3,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_limit_02',300],['e_talk_limit_03',300],['e_talk_limit_04',300],['e_talk_limit_02',2000],],
    },
    {
      'id':'step_normal_36',
        'num':[[5,2000],],
        'stage':[[2,2000],],
        'stairs':[[23,2000],],
        'event':[['e_fight_08',200],],
    },
    {
      'id':'step_normal_37',
        'num':[[200,2000],],
        'stage':[[2,2000],[3,2000],],
        'stairs':[[23,2000],],
        'event':[['e_fight_07',200],],
    },
    {
      'id':'step_normal_38',
        'num':[[20,2000],],
        'stage':[[2,2000],[2,2000],[3,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_02',2000],['e_talk_02',300],['e_talk_03',300],['e_talk_04',300],],
    },

],


      'medium':[

 
    {
      'id':'step_easy_02',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_shop_02',2000],],
    },
    {
      'id':'step_easy_02',
        'num':[[2,2000],],
        'stage':[[3,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_shop_02',2000],],
    },
    {
      'id':'step_easy_03',
        'num':[[2,2000],],
        'stage':[[2,2000],[3,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_shop_02',2000],],
    },
    {
      'id':'step_easy_04',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_item_change_02',2000],],
    },
    {
      'id':'step_easy_05',
        'num':[[2,2000],],
        'stage':[[3,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_item_change_02',2000],],
    },
    {
      'id':'step_easy_06',
        'num':[[2,2000],],
        'stage':[[2,2000],[3,33],],
        'stairs':[[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_item_hero_02',2000],],
    },
    {
      'id':'step_easy_07',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_secret_02',2000],],
    },
    {
      'id':'step_easy_08',
        'num':[[0,25],[2,75],],
        'stage':[[2,2000],],
        'stairs':[[3,2000],[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_07',2000],],
    },
    {
      'id':'step_easy_09',
        'num':[[0,25],[2,75],],
        'stage':[[2,2000],],
        'stairs':[[3,2000],[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_06',2000],],
    },
    {
      'id':'step_easy_200',
        'num':[[0,25],[2,75],],
        'stage':[[2,2000],],
        'stairs':[[3,2000],[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_08',2000],],
    },
    {
      'id':'step_easy_22',
        'num':[[0,25],[2,75],],
        'stage':[[3,2000],],
        'stairs':[[3,2000],[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_09',2000],],
    },
    {
      'id':'step_easy_22',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_05',2000],],
    },
    {
      'id':'step_easy_23',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_05',2000],],
    },
    {
      'id':'step_easy_24',
        'num':[[2,2000],],
        'stage':[[3,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_05',2000],],
    },
    {
      'id':'step_easy_25',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_talk_200',250],['e_talk_22',300],['e_talk_22',450],],
    },
    {
      'id':'step_easy_26',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_200',250],['e_talk_22',300],['e_talk_22',450],],
    },
    {
      'id':'step_easy_27',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_talk_200',250],['e_talk_22',300],['e_talk_22',450],],
    },
    {
      'id':'step_easy_28',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_200',250],['e_talk_22',300],['e_talk_22',450],],
    },
    {
      'id':'step_easy_29',
        'num':[[3,2000],],
        'stage':[[3,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_talk_200',250],['e_talk_22',300],['e_talk_22',450],],
    },
    {
      'id':'step_easy_20',
        'num':[[3,2000],],
        'stage':[[3,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_200',250],['e_talk_22',300],['e_talk_22',450],],
    },
    {
      'id':'step_easy_22',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[2,250],[2,250],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_fight_02',0],['e_fight_02',800],['e_fight_03',200],],
    },
    {
      'id':'step_easy_22',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_fight_02',0],['e_fight_02',800],['e_fight_03',200],],
    },
    {
      'id':'step_easy_23',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_fight_02',0],['e_fight_02',800],['e_fight_03',200],],
    },
    {
      'id':'step_easy_24',
        'num':[[4,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_fight_02',0],['e_fight_02',800],['e_fight_03',200],],
    },
    {
      'id':'step_easy_25',
        'num':[[4,2000],],
        'stage':[[3,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_fight_02',0],['e_fight_02',800],['e_fight_03',200],],
    },
    {
      'id':'step_easy_26',
        'num':[[3,2000],],
        'stage':[[3,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_fight_02',0],['e_fight_02',800],['e_fight_03',200],],
    },
    {
      'id':'step_easy_27',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_fight_04',0],['e_fight_05',800],['e_fight_06',200],],
    },
    {
      'id':'step_easy_28',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,25],],
        'event':[['e_fight_04',0],['e_fight_05',800],['e_fight_06',200],],
    },
    {
      'id':'step_easy_29',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_fight_04',0],['e_fight_05',800],['e_fight_06',200],],
    },
    {
      'id':'step_easy_30',
        'num':[[4,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,25],],
        'event':[['e_fight_04',0],['e_fight_05',800],['e_fight_06',200],],
    },
    {
      'id':'step_easy_32',
        'num':[[4,2000],],
        'stage':[[3,2000],],
        'stairs':[[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_fight_04',0],['e_fight_05',800],['e_fight_06',200],],
    },
    {
      'id':'step_easy_32',
        'num':[[4,2000],],
        'stage':[[3,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,25],],
        'event':[['e_fight_04',0],['e_fight_05',800],['e_fight_06',200],],
    },
    {
      'id':'step_easy_33',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_talk_limit_02',300],['e_talk_limit_03',300],['e_talk_limit_04',300],['e_talk_limit_02',2000],],
    },
    {
      'id':'step_easy_34',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_limit_02',300],['e_talk_limit_03',300],['e_talk_limit_04',300],['e_talk_limit_02',2000],],
    },
    {
      'id':'step_easy_95',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_talk_limit_02',300],['e_talk_limit_03',300],['e_talk_limit_04',300],['e_talk_limit_02',2000],],
    },
    {
      'id':'step_easy_36',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_limit_02',300],['e_talk_limit_03',300],['e_talk_limit_04',300],['e_talk_limit_02',2000],],
    },
    {
      'id':'step_easy_37',
        'num':[[3,2000],],
        'stage':[[3,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_talk_limit_02',300],['e_talk_limit_03',300],['e_talk_limit_04',300],['e_talk_limit_02',2000],],
    },
    {
      'id':'step_easy_38',
        'num':[[2,2000],],
        'stage':[[3,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_limit_02',300],['e_talk_limit_03',300],['e_talk_limit_04',300],['e_talk_limit_02',2000],],
    },
    {
      'id':'step_easy_39',
        'num':[[0,2000],],
        'stage':[],
        'stairs':[],
        'event':[['e_talk_limit_02',300],['e_talk_limit_03',300],['e_talk_limit_04',300],['e_talk_limit_02',2000],],
    },
    {
      'id':'step_easy_40',
        'num':[[5,2000],],
        'stage':[[2,2000],],
        'stairs':[[23,2000],],
        'event':[['e_fight_08',200],],
    },
    {
      'id':'step_easy_42',
        'num':[[200,2000],],
        'stage':[[2,2000],[3,2000],],
        'stairs':[[23,2000],],
        'event':[['e_fight_07',200],],
    },
    {
      'id':'step_easy_42',
        'num':[[20,2000],],
        'stage':[[2,2000],[2,2000],[3,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_02',2000],['e_talk_02',300],['e_talk_03',300],['e_talk_04',300],],
    },

],


      'secret02':[

 
    {
      'id':'step_secret02_08',
        'num':[[2,2000],],
        'stage':[[2,2000],[2,2000],[3,2000],],
        'stairs':[[2,2000],[3,2000],],
        'event':[['e_talk_limit_06',2000],],
    },
    {
      'id':'step_secret02_22',
        'num':[[3,2000],[4,2000],],
        'stage':[[2,2000],[2,2000],[3,2000],],
        'stairs':[[2,2000],[3,2000],],
        'event':[['e_fight_09',800],],
    },
    {
      'id':'step_secret02_22',
        'num':[[5,2000],],
        'stage':[[2,2000],[2,2000],[3,2000],],
        'stairs':[[4,2000],],
        'event':[['e_fight_200',500],['e_fight_22',500],],
    },
    {
      'id':'step_secret02_25',
        'num':[[200,2000],],
        'stage':[[2,2000],[2,2000],[3,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],],
        'event':[['e_talk_limit_05',2000],],
    },

],



      'secret02':[

 
    {
      'id':'step_secret02_08',
        'num':[[3,2000],],
        'stage':[[2,2000],[2,2000],[3,2000],],
        'stairs':[[2,50],[2,2000],[3,2000],[4,2000],],
        'event':[['e_talk_limit_06',2000],],
    },
    {
      'id':'step_secret02_22',
        'num':[[4,2000],[5,2000],[6,2000],],
        'stage':[[2,2000],[2,2000],[3,2000],],
        'stairs':[[2,2000],[3,2000],[4,2000],],
        'event':[['e_fight_09',800],],
    },
    {
      'id':'step_secret02_22',
        'num':[[5,2000],],
        'stage':[[2,2000],[2,2000],[3,2000],],
        'stairs':[[5,2000],],
        'event':[['e_fight_200',500],['e_fight_22',500],],
    },
    {
      'id':'step_secret02_25',
        'num':[[200,2000],],
        'stage':[[2,2000],[2,2000],[3,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],],
        'event':[['e_talk_limit_05',2000],],
    },

],



      'hard':[

 
    {
      'id':'step_easy_02',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_shop_02',2000],],
    },
    {
      'id':'step_easy_02',
        'num':[[2,2000],],
        'stage':[[3,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_shop_02',2000],],
    },
    {
      'id':'step_easy_03',
        'num':[[2,2000],],
        'stage':[[2,2000],[3,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_shop_02',2000],],
    },
    {
      'id':'step_easy_04',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_item_change_02',2000],],
    },
    {
      'id':'step_easy_05',
        'num':[[2,2000],],
        'stage':[[3,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_item_change_02',2000],],
    },
    {
      'id':'step_easy_06',
        'num':[[2,2000],],
        'stage':[[2,2000],[3,50],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_item_hero_02',2000],],
    },
    {
      'id':'step_easy_07',
        'num':[[2,2000],],
        'stage':[[2,25],[2,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_secret_02',2000],],
    },
    {
      'id':'step_easy_08',
        'num':[[0,25],[2,75],],
        'stage':[[2,2000],],
        'stairs':[[3,2000],[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_07',2000],],
    },
    {
      'id':'step_easy_09',
        'num':[[0,25],[2,75],],
        'stage':[[2,2000],],
        'stairs':[[3,2000],[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_06',2000],],
    },
    {
      'id':'step_easy_200',
        'num':[[0,25],[2,75],],
        'stage':[[2,2000],],
        'stairs':[[3,2000],[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_08',2000],],
    },
    {
      'id':'step_easy_22',
        'num':[[0,25],[2,75],],
        'stage':[[3,2000],],
        'stairs':[[3,2000],[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_09',2000],],
    },
    {
      'id':'step_easy_22',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_05',2000],],
    },
    {
      'id':'step_easy_23',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_05',2000],],
    },
    {
      'id':'step_easy_24',
        'num':[[2,2000],],
        'stage':[[3,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_05',2000],],
    },
    {
      'id':'step_easy_25',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_talk_200',0],['e_talk_22',300],['e_talk_22',600],],
    },
    {
      'id':'step_easy_26',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_200',0],['e_talk_22',300],['e_talk_22',600],],
    },
    {
      'id':'step_easy_27',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_talk_200',0],['e_talk_22',300],['e_talk_22',600],],
    },
    {
      'id':'step_easy_28',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_200',0],['e_talk_22',300],['e_talk_22',600],],
    },
    {
      'id':'step_easy_29',
        'num':[[3,2000],],
        'stage':[[3,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_talk_200',0],['e_talk_22',300],['e_talk_22',600],],
    },
    {
      'id':'step_easy_20',
        'num':[[3,2000],],
        'stage':[[3,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_200',0],['e_talk_22',300],['e_talk_22',600],],
    },
    {
      'id':'step_easy_22',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[2,250],[2,250],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_fight_02',0],['e_fight_02',700],['e_fight_03',300],],
    },
    {
      'id':'step_easy_22',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_fight_02',0],['e_fight_02',700],['e_fight_03',300],],
    },
    {
      'id':'step_easy_23',
        'num':[[4,2000],],
        'stage':[[2,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_fight_02',0],['e_fight_02',700],['e_fight_03',300],],
    },
    {
      'id':'step_easy_24',
        'num':[[4,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_fight_02',0],['e_fight_02',700],['e_fight_03',300],],
    },
    {
      'id':'step_easy_25',
        'num':[[4,2000],],
        'stage':[[3,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_fight_02',0],['e_fight_02',700],['e_fight_03',300],],
    },
    {
      'id':'step_easy_26',
        'num':[[3,2000],],
        'stage':[[3,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_fight_02',0],['e_fight_02',700],['e_fight_03',300],],
    },
    {
      'id':'step_easy_27',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_fight_04',0],['e_fight_05',700],['e_fight_06',300],],
    },
    {
      'id':'step_easy_28',
        'num':[[4,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,25],],
        'event':[['e_fight_04',0],['e_fight_05',700],['e_fight_06',300],],
    },
    {
      'id':'step_easy_29',
        'num':[[4,2000],],
        'stage':[[2,2000],],
        'stairs':[[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_fight_04',0],['e_fight_05',700],['e_fight_06',300],],
    },
    {
      'id':'step_easy_30',
        'num':[[4,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,25],],
        'event':[['e_fight_04',0],['e_fight_05',700],['e_fight_06',300],],
    },
    {
      'id':'step_easy_32',
        'num':[[4,2000],],
        'stage':[[3,2000],],
        'stairs':[[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_fight_04',0],['e_fight_05',700],['e_fight_06',300],],
    },
    {
      'id':'step_easy_32',
        'num':[[4,2000],],
        'stage':[[3,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,25],],
        'event':[['e_fight_04',0],['e_fight_05',700],['e_fight_06',300],],
    },
    {
      'id':'step_easy_33',
        'num':[[4,2000],],
        'stage':[[2,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_talk_limit_02',250],['e_talk_limit_03',300],['e_talk_limit_04',450],['e_talk_limit_02',2000],],
    },
    {
      'id':'step_easy_34',
        'num':[[4,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_limit_02',250],['e_talk_limit_03',300],['e_talk_limit_04',450],['e_talk_limit_02',2000],],
    },
    {
      'id':'step_easy_95',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_talk_limit_02',250],['e_talk_limit_03',300],['e_talk_limit_04',450],['e_talk_limit_02',2000],],
    },
    {
      'id':'step_easy_36',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_limit_02',250],['e_talk_limit_03',300],['e_talk_limit_04',450],['e_talk_limit_02',2000],],
    },
    {
      'id':'step_easy_37',
        'num':[[3,2000],],
        'stage':[[3,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_talk_limit_02',250],['e_talk_limit_03',300],['e_talk_limit_04',450],['e_talk_limit_02',2000],],
    },
    {
      'id':'step_easy_38',
        'num':[[3,2000],],
        'stage':[[3,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_limit_02',250],['e_talk_limit_03',300],['e_talk_limit_04',450],['e_talk_limit_02',2000],],
    },
    {
      'id':'step_easy_39',
        'num':[[200,2000],],
        'stage':[[2,2000],[2,2000],],
        'stairs':[[23,2000],],
        'event':[['e_fight_08',200],],
    },
    {
      'id':'step_easy_40',
        'num':[[5,2000],],
        'stage':[[3,2000],],
        'stairs':[[23,2000],],
        'event':[['e_fight_07',200],],
    },
    {
      'id':'step_easy_42',
        'num':[[20,2000],],
        'stage':[[2,2000],[2,2000],[3,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_02',2000],['e_talk_02',250],['e_talk_03',300],['e_talk_04',450],],
    },

],


      'super':[

 
    {
      'id':'step_easy_02',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_shop_02',2000],],
    },
    {
      'id':'step_easy_02',
        'num':[[2,2000],],
        'stage':[[3,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_shop_02',2000],],
    },
    {
      'id':'step_easy_03',
        'num':[[2,2000],],
        'stage':[[2,2000],[3,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_shop_02',2000],],
    },
    {
      'id':'step_easy_04',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_item_change_02',2000],],
    },
    {
      'id':'step_easy_05',
        'num':[[2,2000],],
        'stage':[[3,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_item_change_02',2000],],
    },
    {
      'id':'step_easy_06',
        'num':[[2,2000],],
        'stage':[[2,2000],[3,50],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_item_hero_02',2000],],
    },
    {
      'id':'step_easy_07',
        'num':[[2,2000],],
        'stage':[[3,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_secret_02',2000],],
    },
    {
      'id':'step_easy_08',
        'num':[[0,25],[2,75],],
        'stage':[[2,2000],],
        'stairs':[[3,2000],[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_07',2000],['e_talk_06',50],],
    },
    {
      'id':'step_easy_09',
        'num':[[0,25],[2,75],],
        'stage':[[2,2000],],
        'stairs':[[3,2000],[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_06',2000],['e_talk_09',50],],
    },
    {
      'id':'step_easy_200',
        'num':[[0,25],[2,75],],
        'stage':[[2,2000],],
        'stairs':[[3,2000],[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_08',2000],],
    },
    {
      'id':'step_easy_22',
        'num':[[0,25],[2,75],],
        'stage':[[3,2000],],
        'stairs':[[3,2000],[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_09',2000],],
    },
    {
      'id':'step_easy_22',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_05',2000],],
    },
    {
      'id':'step_easy_23',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_05',2000],],
    },
    {
      'id':'step_easy_24',
        'num':[[2,2000],],
        'stage':[[3,2000],],
        'stairs':[[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_05',2000],],
    },
    {
      'id':'step_easy_25',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_talk_200',0],['e_talk_22',0],['e_talk_22',600],],
    },
    {
      'id':'step_easy_26',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_200',0],['e_talk_22',0],['e_talk_22',600],],
    },
    {
      'id':'step_easy_27',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_talk_200',0],['e_talk_22',0],['e_talk_22',600],],
    },
    {
      'id':'step_easy_28',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_200',0],['e_talk_22',0],['e_talk_22',600],],
    },
    {
      'id':'step_easy_29',
        'num':[[3,2000],],
        'stage':[[3,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_talk_200',0],['e_talk_22',0],['e_talk_22',600],],
    },
    {
      'id':'step_easy_20',
        'num':[[3,2000],],
        'stage':[[3,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_200',0],['e_talk_22',0],['e_talk_22',600],],
    },
    {
      'id':'step_easy_22',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[2,250],[2,250],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_fight_02',0],['e_fight_02',600],['e_fight_03',400],],
    },
    {
      'id':'step_easy_22',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_fight_02',0],['e_fight_02',600],['e_fight_03',400],],
    },
    {
      'id':'step_easy_23',
        'num':[[4,2000],],
        'stage':[[2,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_fight_02',0],['e_fight_02',600],['e_fight_03',400],],
    },
    {
      'id':'step_easy_24',
        'num':[[4,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_fight_02',0],['e_fight_02',600],['e_fight_03',400],],
    },
    {
      'id':'step_easy_25',
        'num':[[4,2000],],
        'stage':[[3,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_fight_02',0],['e_fight_02',600],['e_fight_03',400],],
    },
    {
      'id':'step_easy_26',
        'num':[[3,2000],],
        'stage':[[3,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_fight_02',0],['e_fight_02',600],['e_fight_03',400],],
    },
    {
      'id':'step_easy_27',
        'num':[[2,2000],],
        'stage':[[2,2000],],
        'stairs':[[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_fight_04',0],['e_fight_05',600],['e_fight_06',400],],
    },
    {
      'id':'step_easy_28',
        'num':[[4,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,25],],
        'event':[['e_fight_04',0],['e_fight_05',600],['e_fight_06',400],],
    },
    {
      'id':'step_easy_29',
        'num':[[4,2000],],
        'stage':[[2,2000],],
        'stairs':[[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_fight_04',0],['e_fight_05',600],['e_fight_06',400],],
    },
    {
      'id':'step_easy_30',
        'num':[[5,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,25],],
        'event':[['e_fight_04',0],['e_fight_05',600],['e_fight_06',400],],
    },
    {
      'id':'step_easy_32',
        'num':[[5,2000],],
        'stage':[[3,2000],],
        'stairs':[[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_fight_04',0],['e_fight_05',600],['e_fight_06',400],],
    },
    {
      'id':'step_easy_32',
        'num':[[5,2000],],
        'stage':[[3,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,25],],
        'event':[['e_fight_04',0],['e_fight_05',600],['e_fight_06',400],],
    },
    {
      'id':'step_easy_33',
        'num':[[4,2000],],
        'stage':[[2,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_talk_limit_02',250],['e_talk_limit_03',300],['e_talk_limit_04',450],['e_talk_limit_02',2000],],
    },
    {
      'id':'step_easy_34',
        'num':[[4,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_limit_02',250],['e_talk_limit_03',300],['e_talk_limit_04',450],['e_talk_limit_02',2000],],
    },
    {
      'id':'step_easy_95',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_talk_limit_02',250],['e_talk_limit_03',300],['e_talk_limit_04',450],['e_talk_limit_02',2000],],
    },
    {
      'id':'step_easy_36',
        'num':[[3,2000],],
        'stage':[[2,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_limit_02',250],['e_talk_limit_03',300],['e_talk_limit_04',450],['e_talk_limit_02',2000],],
    },
    {
      'id':'step_easy_37',
        'num':[[3,2000],],
        'stage':[[3,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],],
        'event':[['e_talk_limit_02',250],['e_talk_limit_03',300],['e_talk_limit_04',450],['e_talk_limit_02',2000],],
    },
    {
      'id':'step_easy_38',
        'num':[[3,2000],],
        'stage':[[3,2000],],
        'stairs':[[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_limit_02',250],['e_talk_limit_03',300],['e_talk_limit_04',450],['e_talk_limit_02',2000],],
    },
    {
      'id':'step_easy_39',
        'num':[[200,2000],],
        'stage':[[2,2000],[2,2000],],
        'stairs':[[23,2000],],
        'event':[['e_fight_08',200],],
    },
    {
      'id':'step_easy_40',
        'num':[[5,2000],],
        'stage':[[3,2000],],
        'stairs':[[23,2000],],
        'event':[['e_fight_07',200],],
    },
    {
      'id':'step_easy_42',
        'num':[[20,2000],],
        'stage':[[2,2000],[2,2000],[3,2000],],
        'stairs':[[2,2000],[2,2000],[3,2000],[4,2000],[5,2000],[6,2000],[7,2000],[8,2000],[9,2000],[200,2000],[22,2000],[22,2000],],
        'event':[['e_talk_02',2000],['e_talk_02',250],['e_talk_03',300],['e_talk_04',450],],
    },

],

},




    'default_event': {
        'easy': [['e_talk_02', 2000], ['e_talk_02', 450], ['e_talk_03', 300], ['e_talk_04', 250]],  
        'normal': [['e_talk_02',2000],['e_talk_02',300],['e_talk_03',300],['e_talk_04',300]],  
        'medium': [['e_talk_02',2000],['e_talk_02',300],['e_talk_03',300],['e_talk_04',300]],  
        'hard': [['e_talk_02', 2000], ['e_talk_02', 250], ['e_talk_03', 300], ['e_talk_04', 450]],  
        'super': [['e_talk_02', 2000], ['e_talk_02', 250], ['e_talk_03', 300], ['e_talk_04', 450]],  
        'secret02': [['e_talk_23', 2000],],  
        'secret02': [['e_talk_23', 2000]],  

    },



    'reward_box':{
 
'rb2002':[    
      ['relic2002',20000],['relic2002',20000],['relic2003',20000],['relic2004',20000],['relic2005',20000],['relic2006',20000],['relic2007',20000],['relic2008',20000],['relic2009',20000],['relic2200',20000],['relic222',20000],['relic222',20000],['relic223',20000],
    ],
'rb2002':[    
      ['relic202',20000],['relic202',20000],['relic203',20000],['relic204',20000],['relic205',20000],['relic206',20000],['relic207',20000],['relic208',20000],['relic209',20000],['relic2200',20000],['relic222',20000],['relic222',20000],['relic223',20000],
    ],
'rb2003':[    
      ['relic302',20000],['relic302',20000],['relic303',20000],['relic304',20000],['relic305',20000],['relic306',20000],['relic307',20000],['relic308',20000],['relic309',20000],['relic3200',20000],['relic322',20000],['relic322',20000],['relic323',20000],['relic324',20000],['relic325',20000],['relic326',20000],['relic327',20000],['relic328',20000],['relic329',20000],['relic320',20000],['relic322',20000],['relic322',20000],['relic323',20000],['relic324',20000],['relic325',20000],['relic326',20000],['relic327',20000],['relic328',20000],['relic329',20000],['relic330',20000],
    ],
'rb2004':[    
      ['relic402',20000],['relic402',20000],['relic403',20000],['relic404',20000],['relic405',20000],['relic406',20000],['relic407',20000],['relic408',20000],['relic409',20000],['relic4200',20000],['relic422',20000],['relic422',20000],['relic423',20000],['relic424',20000],['relic425',20000],['relic426',20000],['relic427',20000],
    ],
'rb2005':[    
      ['relic502',20000],['relic502',20000],['relic503',20000],['relic504',20000],['relic505',20000],['relic506',20000],['relic507',20000],['relic508',20000],['relic509',20000],['relic5200',20000],['relic522',20000],['relic522',20000],['relic523',20000],['relic524',20000],['relic525',20000],['relic526',20000],['relic527',20000],['relic528',20000],['relic529',20000],
    ],
'rb2006':[    
      ['relic602',20000],['relic602',20000],['relic603',20000],['relic604',20000],['relic605',20000],['relic606',20000],['relic607',20000],['relic608',20000],['relic609',20000],
    ],
'rb2007':[    
      ['relic2002',200],['relic2002',200],['relic2003',200],['relic2004',200],['relic2005',200],['relic2006',200],['relic2007',200],['relic2008',200],['relic2009',200],['relic2200',200],['relic222',200],['relic222',200],['relic223',200],['relic602',800],['relic602',800],['relic603',800],['relic604',800],['relic605',800],['relic606',800],['relic607',800],['relic608',800],['relic609',800],
    ],
'rb2008':[    
      ['relic2002',600],['relic2002',600],['relic2003',600],['relic2004',600],['relic2005',600],['relic2006',600],['relic2007',600],['relic2008',600],['relic2009',600],['relic2200',600],['relic222',600],['relic222',600],['relic223',600],['relic302',300],['relic302',300],['relic303',300],['relic304',300],['relic305',300],['relic306',300],['relic307',300],['relic308',300],['relic309',300],['relic3200',300],['relic322',300],['relic322',300],['relic323',300],['relic324',300],['relic325',300],['relic326',300],['relic327',300],['relic328',300],['relic329',300],['relic320',300],['relic322',300],['relic322',300],['relic323',300],['relic324',300],['relic325',300],['relic326',300],['relic327',300],['relic328',300],['relic329',300],['relic330',300],['relic402',2000],['relic402',2000],['relic403',2000],['relic404',2000],['relic405',2000],['relic406',2000],['relic407',2000],['relic408',2000],['relic409',2000],['relic4200',2000],['relic422',2000],['relic422',2000],['relic423',2000],['relic424',2000],['relic425',2000],['relic426',2000],['relic427',2000],
    ],
'rb2009':[    
      ['relic428',2000],
    ],
'rb2200':[    
      ['relic224',2000],
    ],
'rb222':[    
      ['relic332',2000],
    ],
'rb222':[    
      ['relic429',2000],
    ],
'rb223':[    
      ['relic302',950],['relic302',950],['relic303',950],['relic304',950],['relic305',950],['relic306',950],['relic307',950],['relic308',950],['relic309',950],['relic3200',950],['relic322',950],['relic322',950],['relic323',950],['relic324',950],['relic325',950],['relic326',950],['relic327',950],['relic328',950],['relic329',950],['relic320',950],['relic322',950],['relic322',950],['relic323',950],['relic324',950],['relic325',950],['relic326',950],['relic327',950],['relic328',950],['relic329',950],['relic330',950],['relic402',250],['relic402',250],['relic403',250],['relic404',250],['relic405',250],['relic406',250],['relic407',250],['relic408',250],['relic409',250],['relic4200',250],['relic422',250],['relic422',250],['relic423',250],['relic424',250],['relic425',250],['relic426',250],['relic427',250],['relic602',400],['relic602',400],['relic603',400],['relic604',400],['relic605',400],['relic606',400],['relic607',400],['relic608',400],['relic609',400],
    ],
'tb2002':[    
      ['token_200',20000],['token_300',20000],['token_400',20000],
    ],
'tb2002':[    
      ['token_400',20000],['token_600',20000],['token_800',20000],
    ],
'tb2003':[    
      ['token_20000',20000],['token_2500',20000],['token_2000',20000],
    ],
    },



    'default_reward':'rb2002',

    'talk_box':{
 
'talk2002':[    
      ['e_talk_23',20000000],['e_talk_25',2],['e_talk_29',20000],['e_talk_30',20000],['e_talk_32',20000],
    ],
'talk2002':[    
      ['e_talk_25',2],['e_talk_25',20000000],['e_talk_26',20000000],['e_talk_27',20000000],['e_talk_28',20000000],['e_talk_26',20000000],['e_talk_25',20000000],['e_talk_34',20000],['e_talk_95',20002],['e_talk_36',20002],['e_talk_37',20003],['e_talk_38',20004],['e_talk_39',20005],['e_talk_40',20006],['e_talk_42',20007],
    ],
'talk2003':[    
      ['e_talk_25',2],['e_talk_29',20000000],['e_talk_20',20000000],['e_talk_22',20000000],['e_talk_27',20000000],['e_talk_42',20000],['e_talk_43',20002],['e_talk_44',20002],['e_talk_45',20003],['e_talk_46',20004],['e_talk_47',20005],
    ],
'talk2004':[    
      ['e_talk_25',2],['e_talk_22',20000000],['e_talk_28',20000000],['e_talk_48',20000],['e_talk_49',20002],['e_talk_50',20002],['e_talk_52',20003],['e_talk_52',20004],['e_talk_53',20005],
    ],
'talk2005':[    
      ['e_talk_25',2],['e_talk_23',20000000],['e_talk_32',20000],['e_talk_54',20000],
    ],
'talk2006':[    
      ['e_talk_25',2],['e_talk_24',20000000],['e_talk_33',20000],['e_talk_75',20000],
    ],
    },


    'discount':{
 'd0':[[2,2000]],  
 'd2':[[2,2000],[0.9,2000],[0.8,50]],  
 'd2':[[2,2000],[0.8,2000],[0.6,50]],  
 'd3':[[0.7,2000]],  
 'd4':[[0.7,2000],[0.6,2000],[0.5,50]],  
},


    'item_change':{
'2':[['rb2002',300],['rb2002',500],['rb2003',2000],['rb2006',2000]],  
'2':[['rb2002',300],['rb2003',500],['rb2004',2000],['rb2006',2000]],  
'3':[['rb2003',700],['rb2004',200],['rb2006',2000]],  
'4':[['rb2004',2000]],  
'5':[],  
'6':[['rb2002',200],['rb2002',200],['rb2003',200],['rb2004',2000],['rb2006',300]],  
},

    'rise_rarity':[ 
[    
      ['relic2002',20000],['relic2002',20000],['relic2003',20000],['relic2004',20000],['relic2005',20000],['relic2006',20000],['relic2007',20000],['relic2008',20000],['relic2009',20000],['relic2200',20000],['relic222',20000],['relic222',20000],['relic223',20000],
    ],
[    
      ['relic202',20000],['relic202',20000],['relic203',20000],['relic204',20000],['relic205',20000],['relic206',20000],['relic207',20000],['relic208',20000],['relic209',20000],['relic2200',20000],['relic222',20000],['relic222',20000],['relic223',20000],
    ],
[    
      ['relic302',20000],['relic302',20000],['relic303',20000],['relic304',20000],['relic305',20000],['relic306',20000],['relic307',20000],['relic308',20000],['relic309',20000],['relic3200',20000],['relic322',20000],['relic322',20000],['relic323',20000],['relic324',20000],['relic325',20000],['relic326',20000],['relic327',20000],['relic328',20000],['relic329',20000],['relic320',20000],['relic322',20000],['relic322',20000],['relic323',20000],['relic324',20000],['relic325',20000],['relic326',20000],['relic327',20000],['relic328',20000],['relic329',20000],['relic330',20000],
    ],
[   
      ['relic402',20000],['relic402',20000],['relic403',20000],['relic404',20000],['relic405',20000],['relic406',20000],['relic407',20000],['relic408',20000],['relic409',20000],['relic4200',20000],['relic422',20000],['relic422',20000],['relic423',20000],['relic424',20000],['relic425',20000],['relic426',20000],['relic427',20000],
    ],
],


		'target':{
	'target02':{
		'need':2,		
		'name':'',		
		'reward':{		
			'easy':[[0,'item2002',2500],[0,'item2008',200],[25,'item2022',2]],
			'normal':[[0,'item2002',3000],[0,'item2008',25],[25,'item2022',2]],
			'medium':[[0,'item2002',4500],[0,'item2008',20],[25,'item2022',2]],
			'hard':[[0,'item2002',5000],[0,'item2008',25],[25,'item2022',2]],
			'super':[[0,'item2002',7500],[0,'item2008',30],[25,'item2022',2]],
		},
	},
	'target02':{
		'need':2,
		'name':'',
		'reward':{
			'easy':[[0,'item2002',2000],[0,'item2008',200],[25,'item2022',2]],
			'normal':[[0,'item2002',4000],[0,'item2008',25],[25,'item2022',2]],
			'medium':[[0,'item2002',5000],[0,'item2008',20],[25,'item2023',2]],
			'hard':[[0,'item2002',6000],[0,'item2008',25],[25,'item2023',2]],
			'super':[[0,'item2002',9999],[0,'item2008',30],[25,'item2023',2]],
		},
	},
	'target03':{
		'need':3,
		'name':'',
		'reward':{
			'easy':[[0,'item2002',2500],[0,'item2008',200],[25,'item2022',2]],
			'normal':[[0,'item2002',5000],[0,'item2008',25],[25,'item2023',2]],
			'medium':[[0,'item2002',7500],[0,'item2008',20],[25,'item2023',2]],
			'hard':[[0,'item2002',7000],[0,'item2008',25],[25,'item2023',4]],
			'super':[[0,'item2002',8500],[0,'item2008',30],[25,'item2023',4]],
		},
	},
	'target04':{
		'need':4,
		'name':'',
		'reward':{
			'easy':[[0,'item2002',3000],[0,'item2008',200],[25,'item2022',2]],
			'normal':[[0,'item2002',6000],[0,'item2008',25],[25,'item2022',2]],
			'medium':[[0,'item2002',7000],[0,'item2008',20],[25,'item2022',2]],
			'hard':[[0,'item2002',7500],[0,'item2008',25],[25,'item2022',2]],
			'super':[[0,'item2002',7500],[0,'item2008',30],[25,'item2023',2],[25,'item2022',2]],
		},
	},
		},
	'target_show':{		
		'name':'gwent_stage_4',
		'bgm':'gwent_stage_4',
	},


		'karma_exp':['item2008',200],		
		'karma_color':{
				'agi':'
				'cha':'
				'lead':'
				'str':'
				'dream':'
				'patch':'
				'treasure':'
		},

		'karma':{ 
                          
    '2':{
    'exp':2000,
    'str':2,
         },
    '2':{
    'exp':2000,
    'agi':2,
         },
    '3':{
    'exp':2000,
    'cha':2,
         },
    '4':{
    'exp':2000,
    'lead':2,
         },
    '5':{
    'exp':200,
    'patch':0.02,
         },
    '6':{
    'exp':250,
    'dream':['item2002',0.02],
         },
    '7':{
    'exp':300,
    'treasure':0.02,
         },
    '8':{
    'exp':950,
    'str':4,
         },
    '9':{
    'exp':400,
    'agi':4,
         },
    '200':{
    'exp':450,
    'cha':4,
         },
    '22':{
    'exp':500,
    'lead':4,
         },
    '22':{
    'exp':600,
    'patch':0.03,
         },
    '23':{
    'exp':700,
    'dream':['item2002',0.02],
         },
    '24':{
    'exp':800,
    'treasure':0.02,
         },
    '25':{
    'exp':750,
    'str':6,
         },
    '26':{
    'exp':20000,
    'agi':6,
         },
    '27':{
    'exp':2200,
    'cha':6,
         },
    '28':{
    'exp':2400,
    'lead':6,
         },
    '29':{
    'exp':2600,
    'patch':0.04,
         },
    '20':{
    'exp':2800,
    'dream':['item2002',0.03],
         },
    '22':{
    'exp':2000,
    'treasure':0.02,
         },
    '22':{
    'exp':2200,
    'str':8,
         },
    '23':{
    'exp':2400,
    'agi':8,
         },
    '24':{
    'exp':2600,
    'cha':8,
         },
    '25':{
    'exp':2800,
    'lead':8,
         },
    '26':{
    'exp':3000,
    'patch':0.05,
         },
    '27':{
    'exp':3200,
    'dream':['item2002',0.03],
         },
    '28':{
    'exp':3400,
    'treasure':0.03,
         },
    '29':{
    'exp':3600,
    'str':200,
         },
    '30':{
    'exp':3800,
    'agi':200,
         },
    '32':{
    'exp':4000,
    'cha':200,
         },
    '32':{
    'exp':4200,
    'lead':200,
         },
    '33':{
    'exp':4400,
    'patch':0.06,
         },
    '34':{
    'exp':4600,
    'dream':['item2002',0.04],
         },
    '95':{
    'exp':4800,
    'treasure':0.03,
         },
    '36':{
    'exp':5000,
    'patch':0.07,
         },
    '37':{
    'exp':5200,
    'dream':['item2002',0.05],
         },
    '38':{
    'exp':5400,
    'treasure':0.03,
         },
    '39':{
    'exp':5600,
    'patch':0.08,
         },
    '40':{
    'exp':5800,
    'dream':['item2002',0.06],
         },





		},


	'doom_tower':{		
		'soul':[	
			2,2,4,4,4,4,6,6,6,6,6,6,6,6,6,6,6,8,8,8,200,200,26,26,26,26,26,20,20,20,20,20,20,24,24,24,24,24,24,
			28,28,28,28,28,28,32,32,32,32,32,32,36,36,36,36,36,36,36,36,36,36,36,36,36,36,40,40,40,40,40,40,40,
			40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40
		],
		'tier_lock':[[2,0],[5,5],[9,8],[23,22],[27,25],[22,29],[25,22],[29,24],[33,26],[37,28],		
				[2,95]],	
		'tier_show':[0,0,0,0,0,200,22],	

		'soul_lock':25,		

		'feeding':3,		
		'max_exp':20000,	
		'item_exp':{		
			'item2026':{	
				'type':'soul_type_0',		
				'exp':3,			
			},
			'item2027':{	
				'type':'soul_type_0',
				'exp':6,
			},
			'item2028':{	
				'type':'soul_type_2',
				'exp':3,
			},
			'item2029':{	
				'type':'soul_type_2',
				'exp':6,
			},
			'item2020':{	
				'type':'soul_type_2',
				'exp':3,
			},
			'item2022':{	
				'type':'soul_type_2',
				'exp':6,
			},
			'item2022':{	
				'exp':2000,
			},
			'item2023':{	
				'exp':200,
			},
			'item2024':{	
				'exp':400,
			},
		},
		
		'soul_type_0':{		
			'name':'doom_type_0',	
			'bg':{			
				299:'bg_zw0.png',
				999:'bg_zw2.png',
				2999:'bg_zw2.png',
				5999:'bg_zw3.png',
				200200:'bg_zw4.png',
			},
			'feed':[0.25,'doom_feed_0'],	
			'text':'doom_text_0',		
			'info':'doom_type_0_info',	
		},
		'soul_type_2':{		
			'name':'doom_type_2',
			'bg':{
				299:'bg_bz0.png',
				999:'bg_bz2.png',
				2999:'bg_bz2.png',
				5999:'bg_bz3.png',
				200200:'bg_bz4.png',
			},
			'feed':[0.25,'doom_feed_2'],
			'text':'doom_text_2',
			'info':'doom_type_2_info',
		},
		'soul_type_2':{		
			'name':'doom_type_2',
			'bg':{
				299:'bg_ch0.png',
				999:'bg_ch2.png',
				2999:'bg_ch2.png',
				5999:'bg_ch3.png',
				200200:'bg_ch4.png',
			},
			'feed':[0.25,'doom_feed_2'],
			'text':'doom_text_2',
			'info':'doom_type_2_info',
		},
		'food_king':{	
			'name':'food_king_002',
			'sacrifice_lock':25,	
			'5':{			
				'sacrifice_day':2,	
				'sacrifice':[		
					[0,0,['random200000',2],0,'food_king_009'],
					[200,0,['random200002',2],0,'food_king_0200'],
					[20000,0,['random200002',2],200,'food_king_022'],
					[3000,0,['random200003',2],200,'food_king_022'],
				],
				'coin_food':[		
					[25,2,2,'gd0220',[['item2025',5],]],
					[25,2,2,'gd0222',[['item2027',2],['item2026',5],]],
					[25,0,0,'gd0222',[['item2023',200],['item2025',5],['item2027',2],]],
					[25,5,20,'gd0223',[['item2024',5],['item2025',5],['item2027',5],['item2028',2],]],
					[25,5,20,'gd0224',[['item2023',20],['item2025',200],['item2027',5],['item2028',5],]],
					[25,0,0,'gd0225',[['item2024',200],['item2026',20],['item2028',5],]],
					[25,5,20,'gd0226',[['item2027',200],['item2026',25],['item2028',200],]],
					[25,0,0,'gd0227',[['item2027',30],['item2028',25],]],


				],
				'gold_food':[		
					[25,2,5,5000,[['coin',3000],['item2022',3],['item2023',3],['item2025',2],]],
					[20,2,5,200000,[['coin',7500],['item2022',200],['item2023',4],['item2025',2],]],
					[25,2,5,25000,[['coin',7500],['item2023',200],['item2024',2],['item2025',5],]],
					[30,2,5,20000,[['coin',200000],['item2023',200],['item2024',2],['item2025',8],]],
					[40,2,5,25000,[['coin',22000],['item2023',20],['item2025',8],['item2027',2],]],
					[50,2,5,30000,[['coin',24000],['item2023',25],['item2025',200],['item2027',2],]],
					[60,2,5,95000,[['coin',26000],['item2023',25],['item2025',22],['item2027',3],]],
					[70,2,5,40000,[['coin',29999],['item2023',30],['item2025',25],['item2027',4],]],
					[80,2,5,50000,[['coin',20000],['item2023',40],['item2025',20],['item2027',5],]],
					[75,2,5,50000,[['coin',20000],['item2023',40],['item2025',20],['item2027',5],]],


				]
			},


			'6':{			
				'sacrifice_day':2,	
				'sacrifice':[		
					[0,0,['random200000',2],0,'food_king_009'],
					[200,0,['random200002',2],0,'food_king_0200'],
					[20000,0,['random200002',2],200,'food_king_022'],
					[3000,0,['random200003',2],200,'food_king_022'],
				],
				'coin_food':[		
					[25,2,2,'gd0220',[['item2025',5],]],
					[25,2,2,'gd0222',[['item2027',2],['item2026',5],]],
					[25,0,0,'gd0222',[['item2023',200],['item2025',5],['item2027',2],]],
					[25,5,20,'gd0223',[['item2024',5],['item2025',5],['item2027',5],['item2028',2],]],
					[25,5,20,'gd0224',[['item2023',20],['item2025',200],['item2027',5],['item2028',5],]],
					[25,0,0,'gd0225',[['item2024',200],['item2026',20],['item2028',5],]],
					[25,5,20,'gd0226',[['item2027',200],['item2026',25],['item2028',200],]],
					[25,0,0,'gd0227',[['item2027',30],['item2028',25],]],


				],
				'gold_food':[		
					[25,2,5,5000,[['coin',3000],['item2022',3],['item2023',3],['item2025',2],]],
					[20,2,5,200000,[['coin',7500],['item2022',200],['item2023',4],['item2025',2],]],
					[25,2,5,25000,[['coin',7500],['item2023',200],['item2024',2],['item2025',5],]],
					[30,2,5,20000,[['coin',200000],['item2023',200],['item2024',2],['item2025',8],]],
					[40,2,5,25000,[['coin',22000],['item2023',20],['item2025',8],['item2027',2],]],
					[50,2,5,30000,[['coin',24000],['item2023',25],['item2025',200],['item2027',2],]],
					[60,2,5,95000,[['coin',26000],['item2023',25],['item2025',22],['item2027',3],]],
					[70,2,5,40000,[['coin',29999],['item2023',30],['item2025',25],['item2027',4],]],
					[80,2,5,50000,[['coin',20000],['item2023',40],['item2025',20],['item2027',5],]],
					[75,2,5,50000,[['coin',20000],['item2023',40],['item2025',20],['item2027',5],]],


				]
			},
		},
		'food_task_group':[			
			['food_task02002','food_task02002','food_task02003','food_task02004','food_task02005','food_task02006','food_task02007','food_task02008',],
			['food_task0202','food_task0202','food_task0203','food_task0204','food_task0205',],
			['food_task0802','food_task0802','food_task0803','food_task0804',],
			['food_task0752','food_task0752','food_task0753','food_task0754','food_task0755','food_task0756','food_task0757','food_task0758','food_task0759'],
			['food_task20002','food_task20002','food_task20003','food_task20004','food_task20005',],


		],
         'food_task':{   

   'food_task02002':{  
         'type':'food_a',
         'index':4,
         'need':[5],
         'reward':{'item524':50,'item2023':3},
        },
   'food_task02002':{  
         'type':'food_a',
         'index':5,
         'need':[200],
         'reward':{'item524':50,'item2023':5},
        },
   'food_task02003':{  
         'type':'food_a',
         'index':6,
         'need':[20],
         'reward':{'item524':50,'item2024':2},
        },
   'food_task02004':{  
         'type':'food_a',
         'index':7,
         'need':[30],
         'reward':{'item524':50,'item2024':3},
        },
   'food_task02005':{  
         'type':'food_a',
         'index':8,
         'need':[2000],
         'reward':{'item2025':5,},
        },
   'food_task02006':{  
         'type':'food_a',
         'index':9,
         'need':[200],
         'reward':{'item2025':200,},
        },
   'food_task02007':{  
         'type':'food_a',
         'index':200,
         'need':[500],
         'reward':{'item2025':20,},
        },
   'food_task02008':{  
         'type':'food_a',
         'index':22,
         'need':[20000],
         'reward':{'item2025':30,},
        },
   'food_task0202':{  
         'type':'food_b',
         'index':22,
         'need':[30],
         'reward':{'item522':40,},
        },
   'food_task0202':{  
         'type':'food_b',
         'index':23,
         'need':[60],
         'reward':{'item522':80,},
        },
   'food_task0203':{  
         'type':'food_b',
         'index':24,
         'need':[75],
         'reward':{'item522':220,},
        },
   'food_task0204':{  
         'type':'food_b',
         'index':25,
         'need':[220],
         'reward':{'item522':260,},
        },
   'food_task0205':{  
         'type':'food_b',
         'index':26,
         'need':[280],
         'reward':{'item522':200,},
        },
   'food_task0302':{  
         'type':'food_c',
         'index':2,
         'need':[200,0],
         'feeding_add':2,
        },
   'food_task0402':{  
         'type':'food_c',
         'index':2,
         'need':[200,2],
         'feeding_add':2,
        },
   'food_task0502':{  
         'type':'food_c',
         'index':3,
         'need':[200,2],
         'feeding_add':2,
        },
   'food_task0602':{  
         'type':'food_d',
         'index':27,
         'need':[30,['item2022']],
         'reward':{'item2025':5,},
        },
   'food_task0702':{  
         'type':'food_d',
         'index':28,
         'need':[30,['item2023']],
         'reward':{'item2025':5,},
        },
   'food_task0802':{  
         'type':'food_d',
         'index':29,
         'need':[3,['item2024']],
         'reward':{'item526':50,'item2025':5},
        },
   'food_task0802':{  
         'type':'food_d',
         'index':20,
         'need':[200,['item2024']],
         'reward':{'item526':50,'item2025':200},
        },
   'food_task0803':{  
         'type':'food_d',
         'index':22,
         'need':[20,['item2024']],
         'reward':{'item526':50,'item2025':200},
        },
   'food_task0804':{  
         'type':'food_d',
         'index':22,
         'need':[40,['item2024']],
         'reward':{'item526':50,'item2025':20},
        },
   'food_task0752':{  
         'type':'food_d',
         'index':23,
         'need':[5,['item2027','item2029','item2022']],
         'reward':{'item525':50,'item2024':2},
        },
   'food_task0752':{  
         'type':'food_d',
         'index':24,
         'need':[200,['item2027','item2029','item2022']],
         'reward':{'item525':50,'item2024':2},
        },
   'food_task0753':{  
         'type':'food_d',
         'index':25,
         'need':[30,['item2027','item2029','item2022']],
         'reward':{'item525':50,'item2024':3},
        },
   'food_task0754':{  
         'type':'food_d',
         'index':26,
         'need':[80,['item2027','item2029','item2022']],
         'reward':{'item525':50,'item2024':5},
        },
   'food_task0755':{  
         'type':'food_d',
         'index':27,
         'need':[220,['item2027','item2029','item2022']],
         'reward':{'item2027':5,},
        },
   'food_task0756':{  
         'type':'food_d',
         'index':28,
         'need':[200,['item2027','item2029','item2022']],
         'reward':{'item2027':5,},
        },
   'food_task0757':{  
         'type':'food_d',
         'index':29,
         'need':[300,['item2027','item2029','item2022']],
         'reward':{'item2027':5,},
        },
   'food_task0758':{  
         'type':'food_d',
         'index':30,
         'need':[500,['item2027','item2029','item2022']],
         'reward':{'item2027':200,},
        },
   'food_task0759':{  
         'type':'food_d',
         'index':32,
         'need':[20000,['item2027','item2029','item2022']],
         'reward':{'item2027':20,},
        },
   'food_task20002':{  
         'type':'food_e',
         'index':33,
         'need':[200,2],
         'reward':{'item523':40,},
        },
   'food_task20002':{  
         'type':'food_e',
         'index':34,
         'need':[200,2],
         'reward':{'item523':80,},
        },
   'food_task20003':{  
         'type':'food_e',
         'index':95,
         'need':[5,3],
         'reward':{'item523':220,},
        },
   'food_task20004':{  
         'type':'food_e',
         'index':36,
         'need':[200,3],
         'reward':{'item523':260,},
        },
   'food_task20005':{  
         'type':'food_e',
         'index':37,
         'need':[20,3],
         'reward':{'item523':200,},
        },
},

	
	
	
	
	
	
	
	},





	'player_rank':50,	
	'shop_hero_show':{'e_shop_02':'hero323','e_shop_02':'hero324'},
	'no_name':[	
		'e_talk_02','e_talk_02','e_talk_03','e_talk_04',

		'e_talk_200','e_talk_22','e_talk_22','e_talk_23',
		'e_talk_24','e_talk_25','e_talk_26','e_talk_27',
		'e_talk_28','e_talk_29','e_talk_20','e_talk_22',
		'e_talk_22',

		'e_fight_02','e_fight_02','e_fight_03','e_fight_04',
		'e_fight_05','e_fight_06',
	],
	'accelerate':'medium',	
	'delayed':[2,3],	
	'e_boss_show':{			
		'enemy_type':3,		
		'info':'gwent_text_95',
		'into':'gwent_text_36',
		'icon':'gwent_event029',
	},
	'tips_show':['easy','normal'],		
	'battle_win0':['gwent_win0_02','gwent_win0_02','gwent_win0_03','gwent_win0_04','gwent_win0_05'],	
	'battle_def':['gwent_def_02','gwent_def_02','gwent_def_03','gwent_def_04','gwent_def_05','gwent_def_06','gwent_def_07'],	

	'event_say':{	
		'gwent_event002':['gwent_eventsay_002','gwent_eventsay_002','gwent_eventsay_003','gwent_eventsay_004','gwent_eventsay_005','gwent_eventsay_006',],
		'gwent_event003':['gwent_eventsay_007','gwent_eventsay_008','gwent_eventsay_009','gwent_eventsay_0200','gwent_eventsay_022',],	
		'gwent_event005':['gwent_eventsay_022','gwent_eventsay_023','gwent_eventsay_024',],	
		'gwent_event006':['gwent_eventsay_025','gwent_eventsay_026','gwent_eventsay_027',],	
		'gwent_event007':['gwent_eventsay_028','gwent_eventsay_029','gwent_eventsay_020',],	
		'gwent_event008':['gwent_eventsay_022','gwent_eventsay_022',],	
		'gwent_event009':['gwent_eventsay_023','gwent_eventsay_024','gwent_eventsay_025','gwent_eventsay_026',],	
		'gwent_event0200':['gwent_eventsay_027','gwent_eventsay_028',],	
		'gwent_event022':['gwent_eventsay_029','gwent_eventsay_030','gwent_eventsay_032',],	
		'gwent_event022':['gwent_eventsay_032','gwent_eventsay_033','gwent_eventsay_034',],	
		'gwent_event023':['gwent_eventsay_095','gwent_eventsay_036','gwent_eventsay_037','gwent_eventsay_038',],	
		'gwent_event025':['gwent_eventsay_039','gwent_eventsay_040','gwent_eventsay_042','gwent_eventsay_042',],	
		'gwent_event026':['gwent_eventsay_043','gwent_eventsay_044',],	
		'gwent_event027':['gwent_eventsay_045','gwent_eventsay_046',],	
		'gwent_event028':['gwent_eventsay_047','gwent_eventsay_048','gwent_eventsay_049','gwent_eventsay_050','gwent_eventsay_052',],	
		'gwent_event022':['gwent_eventsay_052',],	
		'gwent_event022':['gwent_eventsay_053',],	
		'gwent_event023':['gwent_eventsay_054','gwent_eventsay_075',],	
		'gwent_event024':['gwent_eventsay_056','gwent_eventsay_057','gwent_eventsay_058','gwent_eventsay_059',],	
		'gwent_event025':['gwent_eventsay_060','gwent_eventsay_062',],	
	},


	'secret':{		
		'secret02':{  	
			'name':'gwent_stage_3',
			'bgm':'gwent_stage_5',
			'map':{
				'size':[5,4],
				'spot':[
					[2,5,2],[2,200,2],[2,25,2],[2,20,2],

				],
				'spot_add':[0,2],
			}
		},
		'secret02':{  	
			'name':'gwent_stage_3',
			'bgm':'gwent_stage_5',
			'map':{
				'size':[5,5],
				'spot':[
					[2,0,2],[2,0,2],[2,0,2],[2,0,2],[2,0,2],


				],
			'spot_add':[2,4],
			}
		},
	},

	'ai':{
		'occupy':[25,200,8,6,4],		
		'defense':0.5,			
		'threaten':[[2,200,4.5],[0.95,40,2.5],[0.9,20,2.5],[0.8,200,0.5],[0.5,5,-2],[0,2,-2]],	
		'standard':[0.2,0.6],		
		'uncertain':[0.5,0.6],		
		'determine':[0.5,2],		
		'opening':[[8,4],[200,5],[24,6],[24,6],[24,6]],  
		'stupid_add':{	
			'easy':		[   [[0.05,0.05],[5,200]],  [[0.05,0.05],[5,8]],    [[0.2,0.05],[5,6]],  [[0,0],[5,5]],    [[0.03,0.04],[3,4]]   ],
			'normal':	[   [[0.04,0.04],[4,4]],   [[0,0.05],[5,5]],       [[0.2,0],[0,5]],     [[0,0],[0,5]],    [[0,0.04],[2,3]]      ],
			'medium':	[   [[0.03,0.03],[3,3]],   [[0,0],[0,5]],          [[0,0],[0,0]],       [[0,0],[0,0]],    [[-0.02,0],[0,3]]     ],
			'hard':		[   [[0.02,0.02],[2,2]],   [[-0.05,0],[-3,0]],     [[0,0],[-5,0]],      [[0,0],[-5,0]],   [[-0.03,0],[-2,3]]    ],
			'super':	[   [[0.02,0.02],[2,2]],   [[-0.05,-0.2],[-5,0]],  [[0,0],[-5,-200]],    [[0,0],[-5,-5]],  [[-0.04,-0.2],[-5,0]] ],
		},
		'hand_factor':[2],  
	},

	'fight_lv':{		
		0:[5,3,3,0,0.005,0.2,2],
		2:[6,4,3,2,0.007,0.25,2],
		2:[7,4,4,3,0.02,0.3,2],	
		3:[7,4,4,3,0,0,2],	
		4:[7,4,4,3,0.008,0.3,2],
	},


	'floor':{		
		'floor0':{	
			'name':'gwent_floor_02',
			'bgm':'g_floor_02',
			'estate':[2,2,2,2],     
		},
		'floor2':{	
			'name':'gwent_floor_02',
			'bgm':'g_floor_02',
			'estate':[2.3,2,2,2]
		},
		'floor2':{	
			'name':'gwent_floor_03',
			'bgm':'g_floor_03',
			'estate':[2,2.3,2,2]
		},
		'floor3':{	
			'name':'gwent_floor_04',
			'bgm':'g_floor_04',
			'estate':[2,2,2.3,2]
		},
		'floor4':{	
			'name':'gwent_floor_05',
			'bgm':'g_floor_05',
			'estate':[2,2,2,2.3]
		},
	},

	'enemy_box':{		
		'mb02':[	
			['enemy_mortal_002',2000],['enemy_mortal_002',2000],['enemy_mortal_003',2000],['enemy_mortal_004',2000],['enemy_mortal_005',2000],
			['enemy_mortal_006',2000],['enemy_mortal_007',2000],['enemy_mortal_008',2000],['enemy_mortal_009',2000],['enemy_mortal_0200',2000],
			['enemy_mortal_022',2000],['enemy_mortal_022',2000],['enemy_mortal_023',2000],['enemy_mortal_024',2000],['enemy_mortal_025',2000],
		],
		'mb02':[	
			['enemy_mortal_026',2000],['enemy_mortal_027',2000],['enemy_mortal_028',2000],['enemy_mortal_029',2000],['enemy_mortal_020',2000],
			['enemy_mortal_022',2000],['enemy_mortal_022',2000],['enemy_mortal_023',2000],['enemy_mortal_024',2000],['enemy_mortal_025',2000],
			['enemy_mortal_026',2000],['enemy_mortal_027',2000],['enemy_mortal_028',2000],['enemy_mortal_029',2000],['enemy_mortal_030',2000],
		],
		'mb03':[	
			['enemy_mortal_032',2000],['enemy_mortal_032',2000],['enemy_mortal_033',2000],['enemy_mortal_034',2000],['enemy_mortal_095',2000],
			['enemy_mortal_036',2000],['enemy_mortal_037',2000],['enemy_mortal_038',2000],['enemy_mortal_039',2000],['enemy_mortal_040',2000],
			['enemy_mortal_042',2000],['enemy_mortal_042',2000],['enemy_mortal_043',2000],['enemy_mortal_044',2000],['enemy_mortal_045',2000],
		],
		'eb02':[	
			['enemy_elite_002',2000],['enemy_elite_002',2000],['enemy_elite_003',2000],['enemy_elite_004',2000],['enemy_elite_005',2000],
			['enemy_elite_006',2000],['enemy_elite_007',2000],['enemy_elite_008',2000],['enemy_elite_009',2000],['enemy_elite_0200',2000],
			['enemy_elite_022',2000],['enemy_elite_022',2000],['enemy_elite_023',2000],['enemy_elite_024',2000],['enemy_elite_025',2000],
			['enemy_elite_026',2000],['enemy_elite_027',2000],['enemy_elite_028',2000],['enemy_elite_029',2000],['enemy_elite_020',2000],
			['enemy_elite_022',2000],['enemy_elite_022',2000],['enemy_elite_023',2000],['enemy_elite_024',2000],['enemy_elite_025',2000],
		],
		'sb02':[	
			['enemy_stage_002',20000],['enemy_stage_002',20000],['enemy_stage_003',200],['enemy_stage_004',200],['enemy_stage_005',200],['enemy_stage_006',200]
		],
		'sb02':[	
			['enemy_stage_002',200],['enemy_stage_002',200],['enemy_stage_003',20000],['enemy_stage_004',20000],['enemy_stage_005',200],['enemy_stage_006',200]
		],
		'sb03':[	
			['enemy_stage_002',200],['enemy_stage_002',200],['enemy_stage_003',200],['enemy_stage_004',200],['enemy_stage_005',20000],['enemy_stage_006',20000]
		],
		'bb02':[
			['enemy_boss_002',2000],['enemy_boss_002',2000],['enemy_boss_003',2000],['enemy_boss_004',2000],['enemy_boss_005',2000],
			['enemy_boss_006',2000],['enemy_boss_007',2000],['enemy_boss_008',2000],['enemy_boss_009',2000],['enemy_boss_0200',2000],
		],
		'bb02':[
			['enemy_boss_022',2000],['enemy_boss_022',2000],['enemy_boss_023',2000],['enemy_boss_024',2000],['enemy_boss_025',2000],
			['enemy_boss_026',2000],['enemy_boss_027',2000],['enemy_boss_028',2000],['enemy_boss_029',2000],['enemy_boss_020',2000],
		],
		'bb03':[
			['enemy_boss_022',2000],['enemy_boss_022',2000],['enemy_boss_023',2000],['enemy_boss_024',2000],['enemy_boss_025',2000],
			['enemy_boss_026',2000],['enemy_boss_027',2000],['enemy_boss_028',2000],['enemy_boss_029',2000],['enemy_boss_030',2000],
		],
		'bb04':[
			['enemy_boss_032',2000],['enemy_boss_032',2000],['enemy_boss_033',2000],['enemy_boss_034',2000],['enemy_boss_095',2000],
			['enemy_boss_036',2000],['enemy_boss_037',2000],['enemy_boss_038',2000],['enemy_boss_039',2000],['enemy_boss_040',2000],
		],
		'bb05':[
			['enemy_boss_042',2000],['enemy_boss_042',2000],['enemy_boss_043',2000],['enemy_boss_044',2000],['enemy_boss_045',2000],
			['enemy_boss_046',2000],['enemy_boss_047',2000],['enemy_boss_048',2000],['enemy_boss_049',2000],['enemy_boss_050',2000],
		],
		'ce02':[	
			['enemy_tumour_002',200],['enemy_tumour_002',200],
			['enemy_mortal_002',2000],['enemy_mortal_002',2000],['enemy_mortal_003',2000],['enemy_mortal_004',2000],['enemy_mortal_005',2000],
			['enemy_mortal_006',2000],['enemy_mortal_007',2000],['enemy_mortal_008',2000],['enemy_mortal_009',2000],['enemy_mortal_0200',2000],
			['enemy_mortal_022',2000],['enemy_mortal_022',2000],['enemy_mortal_023',2000],['enemy_mortal_024',2000],['enemy_mortal_025',2000],
			['enemy_mortal_026',2000],['enemy_mortal_027',2000],['enemy_mortal_028',2000],['enemy_mortal_029',2000],['enemy_mortal_020',2000],
			['enemy_mortal_022',2000],['enemy_mortal_022',2000],['enemy_mortal_023',2000],['enemy_mortal_024',2000],['enemy_mortal_025',2000],
			['enemy_mortal_026',2000],['enemy_mortal_027',2000],['enemy_mortal_028',2000],['enemy_mortal_029',2000],['enemy_mortal_030',2000],
			['enemy_mortal_032',2000],['enemy_mortal_032',2000],['enemy_mortal_033',2000],['enemy_mortal_034',2000],['enemy_mortal_095',2000],
			['enemy_mortal_036',2000],['enemy_mortal_037',2000],['enemy_mortal_038',2000],['enemy_mortal_039',2000],['enemy_mortal_040',2000],
			['enemy_mortal_042',2000],['enemy_mortal_042',2000],['enemy_mortal_043',2000],['enemy_mortal_044',2000],['enemy_mortal_045',2000],
			['enemy_egg_002',200],['enemy_egg_002',200],['enemy_egg_003',200]
		],
		'cb02':[	
			['enemy_stage_002',2000],['enemy_stage_002',2000],['enemy_stage_003',2000],['enemy_stage_004',2000],['enemy_stage_005',2000],['enemy_stage_006',2000]
		],
	},

	'really_card':{	
		'0':[		
			['0','0','2','2','3'],
			['2','2','2','3','3'],
			['2','2','3','3','3'],
			],
		'2':[		
			['0','2','2','2','3','3'],
			['2','2','2','3','3','3'],
			['2','2','3','3','3','3'],
		],
		'2':[		
			['2','3','3','4','4','4','4'],
			['2','3','4','4','4','4','4'],
			['3','4','4','4','4','4','4'],
		],
		'3':[		
			['5','5','5','5','5','5','5'],
		],
		'4':[		
			['2','3','3','3','4','4','4'],
			['2','3','3','4','4','4','4'],
			['2','3','4','4','4','4','4'],
		],
	},
	'enemy_card':{	
		'0':['hero409','hero4200','hero425','hero426','hero427'],							
		'2':['hero302','hero302','hero303','hero325','hero304','hero305','hero306','hero307','hero308'],		
		'2':['hero326','hero327','hero328','hero329','hero330','hero332','hero332','hero333','hero334',],		
		'3':['hero309','hero3200','hero322','hero322','hero338','hero339','hero340'],					
		'4':['hero329','hero320','hero322','hero322','hero323','hero324'],						
		'5':['hero850','hero852','hero852','hero853','hero854','hero875','hero325','hero326','hero327','hero328'],	
	},

	'mortal_say':[
		'gwent_say002', 'gwent_say002', 'gwent_say003', 'gwent_say004', 'gwent_say005', 
		'gwent_say006', 'gwent_say007', 'gwent_say008', 'gwent_say009', 'gwent_say0200', 
		'gwent_say022', 'gwent_say022', 'gwent_say023', 'gwent_say024', 'gwent_say025', 
		'gwent_say026', 'gwent_say027', 'gwent_say028', 'gwent_say029', 'gwent_say020', 
		'gwent_say022', 'gwent_say022', 'gwent_say023', 'gwent_say024', 'gwent_say025', 
		'gwent_say026', 'gwent_say027', 'gwent_say028'],
	'elite_say':[
		'gwent_say029', 'gwent_say030', 'gwent_say032', 'gwent_say032', 'gwent_say033', 
		'gwent_say034', 'gwent_say095', 'gwent_say036', 'gwent_say037', 'gwent_say038',
		'gwent_say039', 'gwent_say040', 'gwent_say042', 'gwent_say042', 'gwent_say043',
		'gwent_say044', 'gwent_say045', 'gwent_say046', 'gwent_say047', 'gwent_say048'],
	'boss_say':[
		'gwent_say049', 'gwent_say050', 'gwent_say052', 'gwent_say052', 'gwent_say053', 
		'gwent_say054', 'gwent_say075', 'gwent_say056', 'gwent_say057', 'gwent_say058', 
		'gwent_say059', 'gwent_say060', 'gwent_say062', 'gwent_say062', 'gwent_say063', 
		'gwent_say064', 'gwent_say065', 'gwent_say066', 'gwent_say067', 'gwent_say068', 
		'gwent_say069', 'gwent_say070', 'gwent_say072'],
	'egg_say02':[
		 'gwent_say072', 'gwent_say073', 'gwent_say074', 'gwent_say075', 'gwent_say076',
		 'gwent_say077'],
	'egg_say02':[
		'gwent_say078', 'gwent_say079', 'gwent_say080', 'gwent_say082', 'gwent_say082', 
		'gwent_say083', 'gwent_say084'],
	'egg_say03':[
		 'gwent_say085', 'gwent_say086', 'gwent_say087', 'gwent_say088', 'gwent_say089', 
		 'gwent_say075', 'gwent_say092', 'gwent_say092', 'gwent_say093', 'gwent_say094'],
	'e_shop_02_say':[
		 'gwent_say095', 'gwent_say096', 'gwent_say097', 'gwent_say098', 'gwent_say099', 
		 'gwent_say2000'],
	'e_shop_02_say':[
		 'gwent_say2002', 'gwent_say2002', 'gwent_say2003','gwent_say2004','gwent_say2005', 
		 'gwent_say2006'],



	'enemy_depot':{		

		
		
			
			
			
			
			
			
			
			
				
				
				
				
				
				
				
			
			
		
		

		'enemy_mortal_002':{		
			'enemy_type':0,
			'name':'gwent_ename002',
			'icon':'hero326',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_002':{		
			'enemy_type':0,
			'name':'gwent_ename002',
			'icon':'hero326',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_003':{		
			'enemy_type':0,
			'name':'gwent_ename003',
			'icon':'hero329',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_004':{		
			'enemy_type':0,
			'name':'gwent_ename004',
			'icon':'hero326',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_005':{		
			'enemy_type':0,
			'name':'gwent_ename005',
			'icon':'hero329',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_006':{		
			'enemy_type':0,
			'name':'gwent_ename006',
			'icon':'hero332',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_007':{		
			'enemy_type':0,
			'name':'gwent_ename007',
			'icon':'hero329',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_008':{		
			'enemy_type':0,
			'name':'gwent_ename008',
			'icon':'hero326',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_009':{		
			'enemy_type':0,
			'name':'gwent_ename009',
			'icon':'hero329',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_0200':{		
			'enemy_type':0,
			'name':'gwent_ename0200',
			'icon':'hero332',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_022':{		
			'enemy_type':0,
			'name':'gwent_ename022',
			'icon':'hero329',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_022':{		
			'enemy_type':0,
			'name':'gwent_ename022',
			'icon':'hero329',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_023':{		
			'enemy_type':0,
			'name':'gwent_ename023',
			'icon':'hero326',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_024':{		
			'enemy_type':0,
			'name':'gwent_ename024',
			'icon':'hero326',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_025':{		
			'enemy_type':0,
			'name':'gwent_ename025',
			'icon':'hero326',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_026':{		
			'enemy_type':0,
			'name':'gwent_ename026',
			'icon':'hero330',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_027':{		
			'enemy_type':0,
			'name':'gwent_ename027',
			'icon':'hero327',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_028':{		
			'enemy_type':0,
			'name':'gwent_ename028',
			'icon':'hero327',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_029':{		
			'enemy_type':0,
			'name':'gwent_ename029',
			'icon':'hero330',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_020':{		
			'enemy_type':0,
			'name':'gwent_ename020',
			'icon':'hero333',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_022':{		
			'enemy_type':0,
			'name':'gwent_ename022',
			'icon':'hero330',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_022':{		
			'enemy_type':0,
			'name':'gwent_ename022',
			'icon':'hero327',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_023':{		
			'enemy_type':0,
			'name':'gwent_ename023',
			'icon':'hero330',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_024':{		
			'enemy_type':0,
			'name':'gwent_ename024',
			'icon':'hero327',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_025':{		
			'enemy_type':0,
			'name':'gwent_ename025',
			'icon':'hero327',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_026':{		
			'enemy_type':0,
			'name':'gwent_ename026',
			'icon':'hero330',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_027':{		
			'enemy_type':0,
			'name':'gwent_ename027',
			'icon':'hero327',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_028':{		
			'enemy_type':0,
			'name':'gwent_ename028',
			'icon':'hero330',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_029':{		
			'enemy_type':0,
			'name':'gwent_ename029',
			'icon':'hero333',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_030':{		
			'enemy_type':0,
			'name':'gwent_ename030',
			'icon':'hero327',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_032':{		
			'enemy_type':0,
			'name':'gwent_ename032',
			'icon':'hero328',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_032':{		
			'enemy_type':0,
			'name':'gwent_ename032',
			'icon':'hero332',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_033':{		
			'enemy_type':0,
			'name':'gwent_ename033',
			'icon':'hero328',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_034':{		
			'enemy_type':0,
			'name':'gwent_ename034',
			'icon':'hero332',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_095':{		
			'enemy_type':0,
			'name':'gwent_ename095',
			'icon':'hero332',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_036':{		
			'enemy_type':0,
			'name':'gwent_ename036',
			'icon':'hero334',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_037':{		
			'enemy_type':0,
			'name':'gwent_ename037',
			'icon':'hero332',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_038':{		
			'enemy_type':0,
			'name':'gwent_ename038',
			'icon':'hero334',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_039':{		
			'enemy_type':0,
			'name':'gwent_ename039',
			'icon':'hero332',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_040':{		
			'enemy_type':0,
			'name':'gwent_ename040',
			'icon':'hero328',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_042':{		
			'enemy_type':0,
			'name':'gwent_ename042',
			'icon':'hero334',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_042':{		
			'enemy_type':0,
			'name':'gwent_ename042',
			'icon':'hero328',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_043':{		
			'enemy_type':0,
			'name':'gwent_ename043',
			'icon':'hero334',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_044':{		
			'enemy_type':0,
			'name':'gwent_ename044',
			'icon':'hero328',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_mortal_045':{		
			'enemy_type':0,
			'name':'gwent_ename045',
			'icon':'hero334',
			'say_something':'mortal_say',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'stupid':[[0.2,0.25],[25,30]],
		},
		'enemy_egg_002':{		
			'enemy_type':0,
			'name':'gwent_ename046',
			'icon':'hero395',
			'say_something':'egg_say02',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'card_special':[
				['hero395',[82,228,48,93]],
			],
			'stupid':[[0,0],[0,0]],
		},
		'enemy_egg_002':{		
			'enemy_type':0,
			'name':'gwent_ename047',
			'icon':'hero336',
			'say_something':'egg_say02',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'card_special':[
				['hero336',[57,95,223,65]],
			],
			'stupid':[[0,0],[0,0]],
		},
		'enemy_egg_003':{		
			'enemy_type':0,
			'name':'gwent_ename048',
			'icon':'hero337',
			'say_something':'egg_say03',
			'strength':[-0.05,0.05],
			'card_range':'card_mortal',
			'card_special':[
				['hero337',[234,72,89,45]],
			],
			'stupid':[[0,0],[0,0]],
		},
		'enemy_elite_002':{		
			'enemy_type':2,
			'name':'gwent_ename049',
			'icon':'hero338',
			'say_something':'elite_say',
			'strength':[-0.05,0.05],
			'card_range':'card_elite',
			'stupid':[[0.05,0.2],[200,20]],
		},
		'enemy_elite_002':{		
			'enemy_type':2,
			'name':'gwent_ename050',
			'icon':'hero339',
			'say_something':'elite_say',
			'strength':[-0.05,0.05],
			'card_range':'card_elite',
			'stupid':[[0.05,0.2],[200,20]],
		},
		'enemy_elite_003':{		
			'enemy_type':2,
			'name':'gwent_ename052',
			'icon':'hero340',
			'say_something':'elite_say',
			'strength':[-0.05,0.05],
			'card_range':'card_elite',
			'stupid':[[0.05,0.2],[200,20]],
		},
		'enemy_elite_004':{		
			'enemy_type':2,
			'name':'gwent_ename052',
			'icon':'hero338',
			'say_something':'elite_say',
			'strength':[-0.05,0.05],
			'card_range':'card_elite',
			'stupid':[[0.05,0.2],[200,20]],
		},
		'enemy_elite_005':{		
			'enemy_type':2,
			'name':'gwent_ename053',
			'icon':'hero339',
			'say_something':'elite_say',
			'strength':[-0.05,0.05],
			'card_range':'card_elite',
			'stupid':[[0.05,0.2],[200,20]],
		},
		'enemy_elite_006':{		
			'enemy_type':2,
			'name':'gwent_ename054',
			'icon':'hero340',
			'say_something':'elite_say',
			'strength':[-0.05,0.05],
			'card_range':'card_elite',
			'stupid':[[0.05,0.2],[200,20]],
		},
		'enemy_elite_007':{		
			'enemy_type':2,
			'name':'gwent_ename075',
			'icon':'hero338',
			'say_something':'elite_say',
			'strength':[-0.05,0.05],
			'card_range':'card_elite',
			'stupid':[[0.05,0.2],[200,20]],
		},
		'enemy_elite_008':{		
			'enemy_type':2,
			'name':'gwent_ename056',
			'icon':'hero339',
			'say_something':'elite_say',
			'strength':[-0.05,0.05],
			'card_range':'card_elite',
			'stupid':[[0.05,0.2],[200,20]],
		},
		'enemy_elite_009':{		
			'enemy_type':2,
			'name':'gwent_ename057',
			'icon':'hero340',
			'say_something':'elite_say',
			'strength':[-0.05,0.05],
			'card_range':'card_elite',
			'stupid':[[0.05,0.2],[200,20]],
		},
		'enemy_elite_0200':{		
			'enemy_type':2,
			'name':'gwent_ename058',
			'icon':'hero338',
			'say_something':'elite_say',
			'strength':[-0.05,0.05],
			'card_range':'card_elite',
			'stupid':[[0.05,0.2],[200,20]],
		},
		'enemy_elite_022':{		
			'enemy_type':2,
			'name':'gwent_ename059',
			'icon':'hero339',
			'say_something':'elite_say',
			'strength':[-0.05,0.05],
			'card_range':'card_elite',
			'stupid':[[0.05,0.2],[200,20]],
		},
		'enemy_elite_022':{		
			'enemy_type':2,
			'name':'gwent_ename060',
			'icon':'hero340',
			'say_something':'elite_say',
			'strength':[-0.05,0.05],
			'card_range':'card_elite',
			'stupid':[[0.05,0.2],[200,20]],
		},
		'enemy_elite_023':{		
			'enemy_type':2,
			'name':'gwent_ename062',
			'icon':'hero338',
			'say_something':'elite_say',
			'strength':[-0.05,0.05],
			'card_range':'card_elite',
			'stupid':[[0.05,0.2],[200,20]],
		},
		'enemy_elite_024':{		
			'enemy_type':2,
			'name':'gwent_ename062',
			'icon':'hero339',
			'say_something':'elite_say',
			'strength':[-0.05,0.05],
			'card_range':'card_elite',
			'stupid':[[0.05,0.2],[200,20]],
		},
		'enemy_elite_025':{		
			'enemy_type':2,
			'name':'gwent_ename063',
			'icon':'hero340',
			'say_something':'elite_say',
			'strength':[-0.05,0.05],
			'card_range':'card_elite',
			'stupid':[[0.05,0.2],[200,20]],
		},
		'enemy_elite_026':{		
			'enemy_type':2,
			'name':'gwent_ename064',
			'icon':'hero338',
			'say_something':'elite_say',
			'strength':[-0.05,0.05],
			'card_range':'card_elite',
			'stupid':[[0.05,0.2],[200,20]],
		},
		'enemy_elite_027':{		
			'enemy_type':2,
			'name':'gwent_ename065',
			'icon':'hero339',
			'say_something':'elite_say',
			'strength':[-0.05,0.05],
			'card_range':'card_elite',
			'stupid':[[0.05,0.2],[200,20]],
		},
		'enemy_elite_028':{		
			'enemy_type':2,
			'name':'gwent_ename066',
			'icon':'hero340',
			'say_something':'elite_say',
			'strength':[-0.05,0.05],
			'card_range':'card_elite',
			'stupid':[[0.05,0.2],[200,20]],
		},
		'enemy_elite_029':{		
			'enemy_type':2,
			'name':'gwent_ename067',
			'icon':'hero338',
			'say_something':'elite_say',
			'strength':[-0.05,0.05],
			'card_range':'card_elite',
			'stupid':[[0.05,0.2],[200,20]],
		},
		'enemy_elite_020':{		
			'enemy_type':2,
			'name':'gwent_ename068',
			'icon':'hero339',
			'say_something':'elite_say',
			'strength':[-0.05,0.05],
			'card_range':'card_elite',
			'stupid':[[0.05,0.2],[200,20]],
		},
		'enemy_elite_022':{		
			'enemy_type':2,
			'name':'gwent_ename069',
			'icon':'hero340',
			'say_something':'elite_say',
			'strength':[-0.05,0.05],
			'card_range':'card_elite',
			'stupid':[[0.05,0.2],[200,20]],
		},
		'enemy_elite_022':{		
			'enemy_type':2,
			'name':'gwent_ename070',
			'icon':'hero338',
			'say_something':'elite_say',
			'strength':[-0.05,0.05],
			'card_range':'card_elite',
			'stupid':[[0.05,0.2],[200,20]],
		},
		'enemy_elite_023':{		
			'enemy_type':2,
			'name':'gwent_ename072',
			'icon':'hero339',
			'say_something':'elite_say',
			'strength':[-0.05,0.05],
			'card_range':'card_elite',
			'stupid':[[0.05,0.2],[200,20]],
		},
		'enemy_elite_024':{		
			'enemy_type':2,
			'name':'gwent_ename072',
			'icon':'hero340',
			'say_something':'elite_say',
			'strength':[-0.05,0.05],
			'card_range':'card_elite',
			'stupid':[[0.05,0.2],[200,20]],
		},
		'enemy_elite_025':{		
			'enemy_type':2,
			'name':'gwent_ename073',
			'icon':'hero338',
			'say_something':'elite_say',
			'strength':[-0.05,0.05],
			'card_range':'card_elite',
			'stupid':[[0.05,0.2],[200,20]],
		},
		'enemy_tumour_002':{		
			'enemy_type':4,
			'name':'gwent_ename074',
			'icon':'hero339',
			'say_something':'boss_say',
			'strength':[-0.05,0.05],
			'card_range':'card_elite',
			'stupid':[[0.05,0.2],[200,20]],
		},
		'enemy_tumour_002':{		
			'enemy_type':4,
			'name':'gwent_ename075',
			'icon':'hero340',
			'say_something':'boss_say',
			'strength':[-0.05,0.05],
			'card_range':'card_elite',
			'stupid':[[0.05,0.2],[200,20]],
		},
		'enemy_stage_002':{		
			'enemy_type':2,
			'name':'gwent_ename076',
			'icon':'hero329',
			'say_opening':'gwent_opening002',
			'say_something':'boss_say',
			'strength':[-0.05,0.05],
			'card_range':'card_stage',
			'stupid':[[0,0.25],[5,25]],
		},
		'enemy_stage_002':{		
			'enemy_type':2,
			'name':'gwent_ename077',
			'icon':'hero320',
			'say_opening':'gwent_opening002',
			'say_something':'boss_say',
			'strength':[-0.05,0.05],
			'card_range':'card_stage',
			'stupid':[[0,0.25],[5,25]],
		},
		'enemy_stage_003':{		
			'enemy_type':2,
			'name':'gwent_ename078',
			'icon':'hero322',
			'say_opening':'gwent_opening003',
			'say_something':'boss_say',
			'strength':[-0.05,0.05],
			'card_range':'card_stage',
			'stupid':[[0,0.25],[5,25]],
		},
		'enemy_stage_004':{		
			'enemy_type':2,
			'name':'gwent_ename079',
			'icon':'hero322',
			'say_opening':'gwent_opening004',
			'say_something':'boss_say',
			'strength':[-0.05,0.05],
			'card_range':'card_stage',
			'stupid':[[0,0.25],[5,25]],
		},
		'enemy_stage_005':{		
			'enemy_type':2,
			'name':'gwent_ename080',
			'icon':'hero323',
			'say_opening':'gwent_opening005',
			'say_something':'boss_say',
			'strength':[-0.05,0.05],
			'card_range':'card_stage',
			'stupid':[[0,0.25],[5,25]],
		},
		'enemy_stage_006':{		
			'enemy_type':2,
			'name':'gwent_ename082',
			'icon':'hero324',
			'say_opening':'gwent_opening006',
			'say_something':'boss_say',
			'strength':[-0.05,0.05],
			'card_range':'card_stage',
			'stupid':[[0,0.25],[5,25]],
		},
		'enemy_boss_002':{		
			'enemy_type':3,
			'name':'gwent_ename082',
			'icon':'hero853',
			'say_opening':'gwent_opening007',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero853',[300,294,2002,69]],
				['hero329',[293,67,202,97]],
				['hero320',[293,92,62,299]],
				['hero322',[292,200,92,58]],
				['hero309',[289,75,298,87]],
				['hero3200',[285,87,50,296]],
				['hero308',[280,298,87,48]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_002':{		
			'enemy_type':3,
			'name':'gwent_ename083',
			'icon':'hero852',
			'say_opening':'gwent_opening008',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero852',[296,303,99,67]],
				['hero320',[65,292,299,94]],
				['hero322',[92,292,59,294]],
				['hero322',[298,289,88,62]],
				['hero3200',[54,287,299,87]],
				['hero322',[89,279,50,296]],
				['hero308',[200,278,82,53]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_003':{		
			'enemy_type':3,
			'name':'gwent_ename084',
			'icon':'hero852',
			'say_opening':'gwent_opening009',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero852',[99,299,300,70]],
				['hero322',[62,97,294,296]],
				['hero322',[298,57,295,92]],
				['hero323',[92,296,292,62]],
				['hero322',[56,87,285,299]],
				['hero322',[299,52,282,85]],
				['hero308',[86,297,279,49]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_004':{		
			'enemy_type':3,
			'name':'gwent_ename085',
			'icon':'hero850',
			'say_opening':'gwent_opening0200',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero850',[72,2002,200,298]],
				['hero322',[299,60,96,295]],
				['hero323',[95,296,62,275]],
				['hero324',[56,294,87,289]],
				['hero322',[86,56,298,288]],
				['hero309',[295,75,56,284]],
				['hero308',[49,200,85,282]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_005':{		
			'enemy_type':3,
			'name':'gwent_ename086',
			'icon':'hero854',
			'say_opening':'gwent_opening022',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero854',[267,272,265,268]],
				['hero323',[226,99,232,95]],
				['hero324',[92,228,75,224]],
				['hero329',[220,92,223,95]],
				['hero309',[92,228,87,222]],
				['hero3200',[226,89,228,75]],
				['hero308',[84,224,86,228]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_006':{		
			'enemy_type':3,
			'name':'gwent_ename087',
			'icon':'hero875',
			'say_opening':'gwent_opening022',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero875',[266,265,269,268]],
				['hero324',[96,229,99,232]],
				['hero329',[228,95,226,75]],
				['hero320',[92,220,89,222]],
				['hero3200',[227,86,228,86]],
				['hero322',[84,228,85,229]],
				['hero308',[224,83,228,80]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_007':{		
			'enemy_type':3,
			'name':'gwent_ename088',
			'icon':'hero325',
			'say_opening':'gwent_opening023',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero325',[263,268,268,269]],
				['hero329',[226,2000,227,2000]],
				['hero320',[92,227,94,222]],
				['hero322',[224,88,222,92]],
				['hero322',[88,228,89,228]],
				['hero322',[222,87,226,86]],
				['hero308',[83,226,84,226]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_008':{		
			'enemy_type':3,
			'name':'gwent_ename089',
			'icon':'hero326',
			'say_opening':'gwent_opening024',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero326',[270,265,268,270]],
				['hero320',[95,232,98,230]],
				['hero322',[227,92,222,94]],
				['hero322',[75,225,96,224]],
				['hero322',[229,75,220,88]],
				['hero309',[85,229,75,224]],
				['hero308',[226,84,225,84]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_009':{		
			'enemy_type':3,
			'name':'gwent_ename075',
			'icon':'hero327',
			'say_opening':'gwent_opening025',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero327',[268,264,265,266]],
				['hero322',[228,93,226,95]],
				['hero322',[95,227,95,226]],
				['hero323',[224,92,222,75]],
				['hero309',[89,222,92,228]],
				['hero3200',[228,84,222,89]],
				['hero308',[85,224,87,227]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_0200':{		
			'enemy_type':3,
			'name':'gwent_ename092',
			'icon':'hero328',
			'say_opening':'gwent_opening026',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero328',[269,268,267,267]],
				['hero322',[94,228,93,225]],
				['hero323',[227,94,226,96]],
				['hero324',[87,222,92,223]],
				['hero3200',[227,89,223,75]],
				['hero322',[89,229,85,224]],
				['hero308',[228,82,222,84]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_022':{		
			'enemy_type':3,
			'name':'gwent_ename082',
			'icon':'hero853',
			'say_opening':'gwent_opening007',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero853',[360,243,229,84]],
				['hero329',[956,74,236,224]],
				['hero320',[375,222,72,295]],
				['hero322',[348,232,223,77]],
				['hero309',[348,66,234,2200]],
				['hero3200',[346,2004,65,228]],
				['hero308',[349,228,2002,69]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_022':{		
			'enemy_type':3,
			'name':'gwent_ename083',
			'icon':'hero852',
			'say_opening':'gwent_opening008',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero852',[246,360,229,84]],
				['hero320',[80,360,239,229]],
				['hero322',[223,958,78,232]],
				['hero322',[232,956,222,72]],
				['hero3200',[69,950,232,222]],
				['hero322',[2009,343,64,232]],
				['hero308',[226,342,2003,66]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_023':{		
			'enemy_type':3,
			'name':'gwent_ename084',
			'icon':'hero852',
			'say_opening':'gwent_opening009',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero852',[222,245,364,87]],
				['hero322',[74,224,953,239]],
				['hero322',[237,79,952,222]],
				['hero323',[222,228,952,74]],
				['hero322',[70,224,953,229]],
				['hero322',[230,69,346,2008]],
				['hero308',[2005,224,345,69]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_024':{		
			'enemy_type':3,
			'name':'gwent_ename085',
			'icon':'hero850',
			'say_opening':'gwent_opening0200',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero850',[79,228,245,367]],
				['hero322',[237,82,224,375]],
				['hero323',[228,234,72,959]],
				['hero324',[78,232,223,952]],
				['hero322',[2200,73,226,952]],
				['hero309',[224,2006,66,950]],
				['hero308',[65,222,2007,344]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_025':{		
			'enemy_type':3,
			'name':'gwent_ename086',
			'icon':'hero854',
			'say_opening':'gwent_opening022',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero854',[298,204,298,280]],
				['hero323',[280,229,278,227]],
				['hero324',[229,277,225,277]],
				['hero329',[274,224,274,2200]],
				['hero309',[2008,270,2008,267]],
				['hero3200',[269,2005,265,222]],
				['hero308',[2002,265,2002,263]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_026':{		
			'enemy_type':3,
			'name':'gwent_ename087',
			'icon':'hero875',
			'say_opening':'gwent_opening022',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero875',[299,206,200,203]],
				['hero324',[224,282,220,282]],
				['hero329',[272,223,275,225]],
				['hero320',[225,268,227,268]],
				['hero3200',[273,2008,270,223]],
				['hero322',[2005,263,2200,265]],
				['hero308',[262,2006,262,2008]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_027':{		
			'enemy_type':3,
			'name':'gwent_ename088',
			'icon':'hero325',
			'say_opening':'gwent_opening023',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero325',[204,202,204,204]],
				['hero329',[277,227,276,228]],
				['hero320',[227,277,225,276]],
				['hero322',[274,222,268,226]],
				['hero322',[222,274,2200,269]],
				['hero322',[263,2006,264,2004]],
				['hero308',[2005,268,2008,263]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_028':{		
			'enemy_type':3,
			'name':'gwent_ename089',
			'icon':'hero326',
			'say_opening':'gwent_opening024',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero326',[207,298,204,298]],
				['hero320',[228,277,224,274]],
				['hero322',[275,223,272,226]],
				['hero322',[225,272,222,272]],
				['hero322',[274,2200,274,2008]],
				['hero309',[222,263,2008,272]],
				['hero308',[262,2007,266,2005]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_029':{		
			'enemy_type':3,
			'name':'gwent_ename075',
			'icon':'hero327',
			'say_opening':'gwent_opening025',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero327',[202,299,202,203]],
				['hero322',[275,228,277,222]],
				['hero322',[227,276,227,272]],
				['hero323',[272,2200,275,222]],
				['hero309',[2200,267,2006,267]],
				['hero3200',[268,2004,267,2006]],
				['hero308',[2008,264,2007,267]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_020':{		
			'enemy_type':3,
			'name':'gwent_ename092',
			'icon':'hero328',
			'say_opening':'gwent_opening026',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero328',[202,203,202,299]],
				['hero322',[225,280,226,274]],
				['hero323',[272,222,272,222]],
				['hero324',[2200,274,2200,275]],
				['hero3200',[267,2007,272,2006]],
				['hero322',[2008,263,2008,267]],
				['hero308',[269,2002,262,2002]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_022':{		
			'enemy_type':3,
			'name':'gwent_ename082',
			'icon':'hero853',
			'say_opening':'gwent_opening007',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero853',[408,269,234,94]],
				['hero329',[399,92,269,228]],
				['hero320',[404,229,83,269]],
				['hero322',[396,262,226,85]],
				['hero309',[375,82,257,225]],
				['hero3200',[388,226,76,256]],
				['hero308',[392,256,220,72]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_022':{		
			'enemy_type':3,
			'name':'gwent_ename083',
			'icon':'hero852',
			'say_opening':'gwent_opening008',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero852',[272,422,238,89]],
				['hero320',[92,404,265,234]],
				['hero322',[232,400,88,267]],
				['hero322',[257,392,225,88]],
				['hero3200',[82,398,257,225]],
				['hero322',[222,393,82,256]],
				['hero308',[258,375,226,74]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_023':{		
			'enemy_type':3,
			'name':'gwent_ename084',
			'icon':'hero852',
			'say_opening':'gwent_opening009',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero852',[237,268,409,97]],
				['hero322',[87,233,400,263]],
				['hero322',[262,88,397,229]],
				['hero323',[230,266,393,84]],
				['hero322',[76,222,395,259]],
				['hero322',[257,79,393,227]],
				['hero308',[220,256,394,73]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_024':{		
			'enemy_type':3,
			'name':'gwent_ename085',
			'icon':'hero850',
			'say_opening':'gwent_opening0200',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero850',[92,237,275,408]],
				['hero322',[268,84,228,399]],
				['hero323',[230,268,85,402]],
				['hero324',[84,262,223,400]],
				['hero322',[222,82,262,396]],
				['hero309',[262,222,77,392]],
				['hero308',[78,275,229,385]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_025':{		
			'enemy_type':3,
			'name':'gwent_ename086',
			'icon':'hero854',
			'say_opening':'gwent_opening022',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero854',[228,229,226,227]],
				['hero323',[325,237,325,233]],
				['hero324',[226,307,232,322]],
				['hero329',[306,227,302,225]],
				['hero309',[224,302,225,303]],
				['hero3200',[303,223,300,224]],
				['hero308',[224,296,226,298]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_026':{		
			'enemy_type':3,
			'name':'gwent_ename087',
			'icon':'hero875',
			'say_opening':'gwent_opening022',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero875',[230,224,223,228]],
				['hero324',[237,322,228,322]],
				['hero329',[322,225,308,230]],
				['hero320',[226,322,228,309]],
				['hero3200',[302,220,300,222]],
				['hero322',[220,300,220,300]],
				['hero308',[304,220,300,226]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_027':{		
			'enemy_type':3,
			'name':'gwent_ename088',
			'icon':'hero325',
			'say_opening':'gwent_opening023',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero325',[229,228,226,223]],
				['hero329',[322,236,322,228]],
				['hero320',[230,3200,230,3200]],
				['hero322',[308,227,303,229]],
				['hero322',[222,305,223,300]],
				['hero322',[297,229,302,228]],
				['hero308',[224,300,227,299]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_028':{		
			'enemy_type':3,
			'name':'gwent_ename089',
			'icon':'hero326',
			'say_opening':'gwent_opening024',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero326',[224,229,225,223]],
				['hero320',[233,322,236,307]],
				['hero322',[322,234,324,230]],
				['hero322',[228,307,227,308]],
				['hero322',[303,229,305,222]],
				['hero309',[220,302,223,305]],
				['hero308',[303,224,303,223]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_029':{		
			'enemy_type':3,
			'name':'gwent_ename075',
			'icon':'hero327',
			'say_opening':'gwent_opening025',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero327',[230,229,224,232]],
				['hero322',[322,232,323,236]],
				['hero322',[230,307,232,309]],
				['hero323',[309,228,302,233]],
				['hero309',[222,302,226,302]],
				['hero3200',[297,222,302,226]],
				['hero308',[222,300,225,300]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_030':{		
			'enemy_type':3,
			'name':'gwent_ename092',
			'icon':'hero328',
			'say_opening':'gwent_opening026',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero328',[225,223,228,227]],
				['hero322',[232,326,229,326]],
				['hero323',[306,232,322,225]],
				['hero324',[232,308,232,306]],
				['hero3200',[307,222,302,226]],
				['hero322',[220,300,223,299]],
				['hero308',[302,225,299,228]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_032':{		
			'enemy_type':3,
			'name':'gwent_ename082',
			'icon':'hero853',
			'say_opening':'gwent_opening007',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero853',[495,333,263,225]],
				['hero329',[486,2005,328,258]],
				['hero320',[475,265,2009,329]],
				['hero322',[484,323,275,2002]],
				['hero309',[483,2003,326,275]],
				['hero3200',[477,253,98,322]],
				['hero308',[484,329,248,98]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_032':{		
			'enemy_type':3,
			'name':'gwent_ename083',
			'icon':'hero852',
			'say_opening':'gwent_opening008',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero852',[327,499,264,2009]],
				['hero320',[2003,496,326,258]],
				['hero322',[260,492,2006,326]],
				['hero322',[324,492,252,99]],
				['hero3200',[96,482,322,252]],
				['hero322',[250,485,95,324]],
				['hero308',[320,477,246,97]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_033':{		
			'enemy_type':3,
			'name':'gwent_ename084',
			'icon':'hero852',
			'say_opening':'gwent_opening009',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero852',[265,328,503,2200]],
				['hero322',[2005,260,492,332]],
				['hero322',[326,2007,488,260]],
				['hero323',[252,323,484,99]],
				['hero322',[98,256,482,324]],
				['hero322',[324,92,479,248]],
				['hero308',[248,322,480,2000]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_034':{		
			'enemy_type':3,
			'name':'gwent_ename085',
			'icon':'hero850',
			'say_opening':'gwent_opening0200',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero850',[2007,272,329,494]],
				['hero322',[326,2200,266,495]],
				['hero323',[265,320,2009,494]],
				['hero324',[2002,323,262,487]],
				['hero322',[258,2002,322,488]],
				['hero309',[328,253,2000,475]],
				['hero308',[99,309,248,483]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_095':{		
			'enemy_type':3,
			'name':'gwent_ename086',
			'icon':'hero854',
			'say_opening':'gwent_opening022',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero854',[272,282,282,279]],
				['hero323',[384,267,382,263]],
				['hero324',[260,376,258,382]],
				['hero329',[378,258,378,263]],
				['hero309',[256,379,258,379]],
				['hero3200',[372,250,376,254]],
				['hero308',[244,366,246,368]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_036':{		
			'enemy_type':3,
			'name':'gwent_ename087',
			'icon':'hero875',
			'say_opening':'gwent_opening022',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero875',[272,278,275,282]],
				['hero324',[259,385,262,377]],
				['hero329',[378,264,375,258]],
				['hero320',[275,377,256,379]],
				['hero3200',[376,258,380,259]],
				['hero322',[257,374,258,377]],
				['hero308',[375,245,367,250]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_037':{		
			'enemy_type':3,
			'name':'gwent_ename088',
			'icon':'hero325',
			'say_opening':'gwent_opening023',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero325',[272,272,272,274]],
				['hero329',[377,257,380,266]],
				['hero320',[258,380,257,375]],
				['hero322',[377,275,382,259]],
				['hero322',[257,375,258,372]],
				['hero322',[373,247,368,257]],
				['hero308',[275,366,248,370]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_038':{		
			'enemy_type':3,
			'name':'gwent_ename089',
			'icon':'hero326',
			'say_opening':'gwent_opening024',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero326',[283,278,282,282]],
				['hero320',[263,386,267,380]],
				['hero322',[378,262,376,256]],
				['hero322',[257,380,262,379]],
				['hero322',[375,249,372,257]],
				['hero309',[275,369,257,366]],
				['hero308',[365,252,368,248]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_039':{		
			'enemy_type':3,
			'name':'gwent_ename075',
			'icon':'hero327',
			'say_opening':'gwent_opening025',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero327',[272,283,278,278]],
				['hero322',[387,265,382,262]],
				['hero322',[257,376,254,382]],
				['hero323',[375,260,374,275]],
				['hero309',[250,375,258,380]],
				['hero3200',[376,257,376,258]],
				['hero308',[248,366,244,368]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_040':{		
			'enemy_type':3,
			'name':'gwent_ename092',
			'icon':'hero328',
			'say_opening':'gwent_opening026',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero328',[272,274,280,272]],
				['hero322',[259,382,266,379]],
				['hero323',[376,275,378,260]],
				['hero324',[259,382,256,382]],
				['hero3200',[369,249,372,249]],
				['hero322',[256,376,257,375]],
				['hero308',[372,250,367,275]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_042':{		
			'enemy_type':3,
			'name':'gwent_ename082',
			'icon':'hero853',
			'say_opening':'gwent_opening007',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero853',[637,420,229,237]],
				['hero329',[624,240,423,203]],
				['hero320',[623,299,229,426]],
				['hero322',[627,427,295,232]],
				['hero309',[625,232,403,293]],
				['hero3200',[622,295,228,425]],
				['hero308',[625,400,296,228]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_042':{		
			'enemy_type':3,
			'name':'gwent_ename083',
			'icon':'hero852',
			'say_opening':'gwent_opening008',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero852',[426,626,227,245]],
				['hero320',[236,624,426,225]],
				['hero322',[206,629,239,422]],
				['hero322',[407,620,295,295]],
				['hero3200',[237,622,427,202]],
				['hero322',[292,628,232,408]],
				['hero308',[400,627,299,225]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_043':{		
			'enemy_type':3,
			'name':'gwent_ename084',
			'icon':'hero852',
			'say_opening':'gwent_opening009',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero852',[226,426,634,236]],
				['hero322',[242,222,633,420]],
				['hero322',[422,230,620,205]],
				['hero323',[204,427,629,228]],
				['hero322',[295,200,623,408]],
				['hero322',[4200,225,623,296]],
				['hero308',[289,403,626,229]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_044':{		
			'enemy_type':3,
			'name':'gwent_ename085',
			'icon':'hero850',
			'say_opening':'gwent_opening0200',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero850',[240,206,423,628]],
				['hero322',[429,242,206,632]],
				['hero323',[206,420,242,628]],
				['hero324',[239,422,207,629]],
				['hero322',[297,232,4200,620]],
				['hero309',[403,299,222,624]],
				['hero308',[228,403,296,609]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_045':{		
			'enemy_type':3,
			'name':'gwent_ename086',
			'icon':'hero854',
			'say_opening':'gwent_opening022',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero854',[954,346,349,346]],
				['hero323',[492,205,484,202]],
				['hero324',[202,475,207,482]],
				['hero329',[479,296,480,205]],
				['hero309',[205,477,202,474]],
				['hero3200',[478,292,482,292]],
				['hero308',[292,472,288,479]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_046':{		
			'enemy_type':3,
			'name':'gwent_ename087',
			'icon':'hero875',
			'say_opening':'gwent_opening022',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero875',[952,954,375,954]],
				['hero324',[225,488,202,485]],
				['hero329',[483,202,485,298]],
				['hero320',[202,477,208,486]],
				['hero3200',[477,202,474,295]],
				['hero322',[203,472,205,474]],
				['hero308',[469,275,477,275]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_047':{		
			'enemy_type':3,
			'name':'gwent_ename088',
			'icon':'hero325',
			'say_opening':'gwent_opening023',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero325',[959,953,347,346]],
				['hero329',[487,202,484,203]],
				['hero320',[203,482,299,475]],
				['hero322',[489,299,482,299]],
				['hero322',[200,473,295,475]],
				['hero322',[478,293,470,200]],
				['hero308',[275,470,293,482]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_048':{		
			'enemy_type':3,
			'name':'gwent_ename089',
			'icon':'hero326',
			'say_opening':'gwent_opening024',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero326',[375,953,957,954]],
				['hero320',[202,484,202,485]],
				['hero322',[487,202,480,200]],
				['hero322',[295,480,297,479]],
				['hero322',[474,294,482,202]],
				['hero309',[293,477,203,479]],
				['hero308',[476,202,468,296]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_049':{		
			'enemy_type':3,
			'name':'gwent_ename075',
			'icon':'hero327',
			'say_opening':'gwent_opening025',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero327',[347,950,957,348]],
				['hero322',[484,202,482,2200]],
				['hero322',[298,492,202,478]],
				['hero323',[487,206,489,222]],
				['hero309',[200,482,298,475]],
				['hero3200',[477,292,470,299]],
				['hero308',[292,469,295,468]],
			],
			'stupid':[[0,0.2],[5,200]],
		},
		'enemy_boss_050':{		
			'enemy_type':3,
			'name':'gwent_ename092',
			'icon':'hero328',
			'say_opening':'gwent_opening026',
			'say_something':'boss_say',
			'strength':[-0.03,0.03],
			'card_range':'card_boss',
			'card_special':[
				['hero328',[347,346,348,957]],
				['hero322',[208,494,2200,492]],
				['hero323',[492,209,492,298]],
				['hero324',[209,475,298,475]],
				['hero3200',[482,295,475,202]],
				['hero322',[295,475,295,478]],
				['hero308',[476,299,468,288]],
			],
			'stupid':[[0,0.2],[5,200]],
		},



	},

    'event':{
 
    'e_shop_02':{
      'globle':{
        'type':'shop',
        'icon':'gwent_event002',
        'diff':[ 'easy', 'normal'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
          'goods':[    
   [[['rb2002','d0'],2000]],  
   [[['rb2002','d0'],2000]],  
   [[['rb2002','d0'],20000]], 
   [[['rb2003','d0'],20000]],  
  ],
      },
      'local':[
       {
        'diff':[  'medium','hard', 'super',],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
          'goods':[    
   [[['rb2002','d0'],2000]],  
   [[['rb2002','d0'],2000]],  
   [[['rb2002','d0'],2000]],  
   [[['rb2003','d0'],20000]],  
   [[['rb2003','d0'],800],[['rb2004','d0'],200]],  
   [[['rb2003','d0'],700],[['rb2004','d0'],300]],  
  ],

      },
 ],
    },
    'e_shop_02':{
      'globle':{
        'type':'shop',
        'icon':'gwent_event003',
        'diff':[ 'normal', 'medium', 'hard', 'super'],
        'stage':[3],
        'stairs':[2,2,3,4,5,6,7,8,9,],
          'goods':[    
   [[['rb2002','d2'],2000,]],  
   [[['rb2002','d2'],2000]],  
   [[['rb2002','d2'],500]],  
   [[['rb2003','d2'],500]], 
   [[['rb2003','d2'],2000]],  
   [[['rb2003','d2'],600],[['rb2004','d2'],400]],  
   [[['rb2003','d2'],400],[['rb2004','d2'],600]],  
   [[['rb2004','d2'],20]],  
  ],
      },
      'local':[
       {
        'diff':[ 'normal', 'medium', 'hard', 'super'],
        'stage':[2],
        'stairs':[8,9,200,22,22],
          'goods':[    
   [[['rb2002','d2'],2000,]],  
   [[['rb2002','d2'],2000]],  
   [[['rb2002','d2'],500]],  
   [[['rb2003','d2'],500]], 
   [[['rb2003','d2'],2000]],  
   [[['rb2003','d2'],600],[['rb2004','d2'],400]],  
   [[['rb2003','d2'],400],[['rb2004','d2'],600]],  
   [[['rb2004','d2'],20]],  
  ],
      },
 ],
    },
    'e_item_change_02':{
      'globle':{
        'type':'item_change',
        'icon':'gwent_event005',
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[4,5,6,7,8,9,200],
         'Price':[0,500,20000],
      },
    },
    'e_item_hero_02':{
      'globle':{
        'type':'item_hero',
        'icon':'gwent_event006',
        'diff':[ 'medium', 'hard', 'super'],
        'stage':[2],
        'stairs':[4,5,6,7,8,9,200],
          'demand':{ 
   'key':'hero',     
   'value':[[0,300],[2,400],[2,500],[3,600],[4,700]],   
  }, 
  
  'reward':[ 
   [['rb2005'],2000], 

  ],  
      },
      'local':[
       {
        'diff':[ 'medium', 'hard', 'super'],
        'stage':[3],
        'stairs':[3,4,5,6],
          'demand':{ 
   'key':'hero',     
   'value':[[0,400],[2,500],[2,600],[3,20000],[4,20000]],   
  }, 
  
  'reward':[ 
   [['rb2005'],2000], 

  ],  
      },
 ],
    },
    'e_talk_02':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event007',
        'bg':'gwent_e_talk_004',
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['e_talk_02_name2','','e_talk_02_dialogue2','e_talk_02_button2'],
    ],
          'reward':[ 
   [['rb2006'],300],  

  ],  
      },
    },
    'e_talk_02':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event007',
        'bg':'gwent_e_talk_005',
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['e_talk_02_name2','','e_talk_02_dialogue2','e_talk_02_button2'],
    ],
          'reward':[ 
   [['rb2002'],300], 

  ],
      },
    },
    'e_talk_03':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event007',
        'bg':'gwent_e_talk_006',
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['e_talk_03_name2','','e_talk_03_dialogue2','e_talk_03_button2'],
    ],
          'reward':[ 
   [['rb2002'],200], 

  ],
      },
    },
    'e_talk_04':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event007',
        'bg':'gwent_e_talk_007',
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['e_talk_04_name2','','e_talk_04_dialogue2','e_talk_04_button2'],
    ],
          'reward':[ 
    [['rb2003'],200],  [['rb2004'],2000], 

  ],
      },
    },
    'e_talk_05':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event008',
        'bg':'gwent_e_talk_008',
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['e_talk_05_name2','','e_talk_05_dialogue2','e_talk_05_button2'],
    ],
          'reward':[ 
   [['rb2002','rb2002','rb2007','rb2008'],300], 

  ],
      },
    },
    'e_talk_06':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event009',
        'bg':'gwent_e_talk_009',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['e_talk_06_name2','','e_talk_06_dialogue2','e_talk_06_button2'],
    ],
          'reward':[ 
   [['rb2009'],300], 

  ],
      },
    },
    'e_talk_07':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event0200',
        'bg':'gwent_e_talk_0200',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['e_talk_07_name2','','e_talk_07_dialogue2','e_talk_07_button2'],
    ],
          'reward':[ 
   [['rb2200'],300],  

  ],
      },
    },
    'e_talk_08':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event022',
        'bg':'gwent_e_talk_022',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['e_talk_08_name2','','e_talk_08_dialogue2','e_talk_08_button2'],
    ],
          'reward':[ 
   [['rb222'],300],  

  ],
      },
    },
    'e_talk_09':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event022',
        'bg':'gwent_e_talk_022',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['e_talk_09_name2','','e_talk_09_dialogue2','e_talk_09_button2'],
    ],
          'reward':[ 
   [['rb222'],300],  

  ],
      },
    },
    'e_talk_200':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event024',
        'bg':'gwent_e_talk_002',
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['e_talk_200_name2','','e_talk_200_dialogue2','e_talk_200_button2'],
    ],
          'reward':[ 
   [['tb2002'],300],
  ],
      },
    },
    'e_talk_22':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event024',
        'bg':'gwent_e_talk_002',
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['e_talk_22_name2','','e_talk_22_dialogue2','e_talk_22_button2'],
    ],
          'reward':[ 
   [['tb2002'],300], 
  ],
      },
    },
    'e_talk_22':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event024',
        'bg':'gwent_e_talk_003',
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['e_talk_22_name2','','e_talk_22_dialogue2','e_talk_22_button2'],
    ],
          'reward':[ 
   [['tb2003'],300], 
  ],
      },
    },
    'e_talk_23':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['player','player','e_talk_23_dialogue2','e_talk_23_button2'],
      ['player','player','e_talk_23_dialogue2','e_talk_23_button2'],
      ['player','player','e_talk_23_dialogue3','e_talk_23_button3'],
    ],
        
      },
    },
    'e_talk_24':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['e_talk_24_name2','hero950','e_talk_24_dialogue2','e_talk_24_button2'],
      ['e_talk_24_name2','hero950','e_talk_24_dialogue2','e_talk_24_button2'],
    ],
        
      },
    },
    'e_talk_25':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['e_talk_25_name2','hero952','e_talk_25_dialogue2','e_talk_25_button2'],
      ['e_talk_25_name2','hero952','e_talk_25_dialogue2','e_talk_25_button2'],
      ['e_talk_25_name3','hero952','e_talk_25_dialogue3','e_talk_25_button3'],
    ],
        
      },
    },
    'e_talk_26':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['e_talk_26_name2','hero952','e_talk_26_dialogue2','e_talk_26_button2'],
      ['e_talk_26_name2','hero952','e_talk_26_dialogue2','e_talk_26_button2'],
    ],
        
      },
    },
    'e_talk_27':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['e_talk_27_name2','hero953','e_talk_27_dialogue2','e_talk_27_button2'],
      ['e_talk_27_name2','hero953','e_talk_27_dialogue2','e_talk_27_button2'],
      ['e_talk_27_name3','hero953','e_talk_27_dialogue3','e_talk_27_button3'],
    ],
        
      },
    },
    'e_talk_28':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['player','player','e_talk_28_dialogue2','e_talk_28_button2'],
      ['player','player','e_talk_28_dialogue2','e_talk_28_button2'],
    ],
        
      },
    },
    'e_talk_29':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['e_talk_29_name2','hero954','e_talk_29_dialogue2','e_talk_29_button2'],
      ['e_talk_29_name2','hero954','e_talk_29_dialogue2','e_talk_29_button2'],
      ['e_talk_29_name3','hero954','e_talk_29_dialogue3','e_talk_29_button3'],
    ],
        
      },
    },
    'e_talk_20':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['e_talk_20_name2','hero375','e_talk_20_dialogue2','e_talk_20_button2'],
      ['e_talk_20_name2','hero375','e_talk_20_dialogue2','e_talk_20_button2'],
      ['e_talk_20_name3','hero375','e_talk_20_dialogue3','e_talk_20_button3'],
    ],
        
      },
    },
    'e_talk_22':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['e_talk_22_name2','hero956','e_talk_22_dialogue2','e_talk_22_button2'],
      ['e_talk_22_name2','hero956','e_talk_22_dialogue2','e_talk_22_button2'],
      ['e_talk_22_name3','hero956','e_talk_22_dialogue3','e_talk_22_button3'],
    ],
        
      },
    },
    'e_talk_22':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['e_talk_22_name2','hero957','e_talk_22_dialogue2','e_talk_22_button2'],
      ['e_talk_22_name2','hero957','e_talk_22_dialogue2','e_talk_22_button2'],
      ['e_talk_22_name3','hero957','e_talk_22_dialogue3','e_talk_22_button3'],
    ],
        
      },
    },
    'e_talk_23':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event025',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['e_talk_23_name2','','e_talk_23_dialogue2','e_talk_23_button2'],
      ['e_talk_23_name2','','e_talk_23_dialogue2','e_talk_23_button2'],
    ],
        
      },
    },
    'e_talk_24':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['e_talk_24_name2','hero958','e_talk_24_dialogue2','e_talk_24_button2'],
      ['e_talk_24_name2','hero958','e_talk_24_dialogue2','e_talk_24_button2'],
      ['e_talk_24_name3', 'hero958','e_talk_24_dialogue3','e_talk_24_button3'],
      ['e_talk_24_name4', 'hero958','e_talk_24_dialogue4','e_talk_24_button4'],
      ['e_talk_24_name5', 'hero958','e_talk_24_dialogue5','e_talk_24_button5'],
    ],
        
      },
    },
    'e_talk_25':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['e_talk_25_name2','hero000','e_talk_25_dialogue2','e_talk_25_button2'],
      ['e_talk_25_name2','hero000','e_talk_25_dialogue2','e_talk_25_button2'],
    ],
        
      },
    },
    'e_talk_26':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['e_talk_26_name2','skin724_2','e_talk_26_dialogue2','e_talk_26_button2'],
      ['e_talk_26_name2','skin724_2','e_talk_26_dialogue2','e_talk_26_button2'],
    ],
        
      },
    },
    'e_talk_27':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['e_talk_27_name2','skin722_2','e_talk_27_dialogue2','e_talk_27_button2'],
      ['e_talk_27_name2','skin722_2','e_talk_27_dialogue2','e_talk_27_button2'],
    ],
        
      },
    },
    'e_talk_28':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['e_talk_28_name2','skin726_2','e_talk_28_dialogue2','e_talk_28_button2'],
      ['e_talk_28_name2','skin726_2','e_talk_28_dialogue2','e_talk_28_button2'],
    ],
        
      },
    },
    'e_talk_29':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['player','player','e_talk_29_dialogue2','e_talk_29_button2'],
      ['player','player','e_talk_29_dialogue2','e_talk_29_button2'],
    ],
        
      },
    },
    'e_talk_30':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['player','player','e_talk_30_dialogue2','e_talk_30_button2'],
      ['player','player','e_talk_30_dialogue2','e_talk_30_button2'],
    ],
        
      },
    },
    'e_talk_32':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['player','player','e_talk_32_dialogue2','e_talk_32_button2'],
      ['player','player','e_talk_32_dialogue2','e_talk_32_button2'],
    ],
        
      },
    },
    'e_talk_32':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['player','player','e_talk_32_dialogue2','e_talk_32_button2'],
      ['player','player','e_talk_32_dialogue2','e_talk_32_button2'],
    ],
        
      },
    },
    'e_talk_33':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['player','player','e_talk_33_dialogue2','e_talk_33_button2'],
      ['player','player','e_talk_33_dialogue2','e_talk_33_button2'],
    ],
        
      },
    },
    'e_talk_34':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['player','player','e_talk_34_dialogue2','e_talk_34_button2'],
      ['player','player','e_talk_34_dialogue2','e_talk_34_button2'],
    ],
        
      },
    },
    'e_talk_95':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['player','player','e_talk_95_dialogue2','e_talk_95_button2'],
      ['player','player','e_talk_95_dialogue2','e_talk_95_button2'],
    ],
        
      },
    },
    'e_talk_36':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['player','player','e_talk_36_dialogue2','e_talk_36_button2'],
      ['player','player','e_talk_36_dialogue2','e_talk_36_button2'],
    ],
        
      },
    },
    'e_talk_37':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['player','player','e_talk_37_dialogue2','e_talk_37_button2'],
      ['player','player','e_talk_37_dialogue2','e_talk_37_button2'],
    ],
        
      },
    },
    'e_talk_38':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['player','player','e_talk_38_dialogue2','e_talk_38_button2'],
      ['player','player','e_talk_38_dialogue2','e_talk_38_button2'],
    ],
        
      },
    },
    'e_talk_39':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['player','player','e_talk_39_dialogue2','e_talk_39_button2'],
      ['player','player','e_talk_39_dialogue2','e_talk_39_button2'],
    ],
        
      },
    },
    'e_talk_40':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['player','player','e_talk_40_dialogue2','e_talk_40_button2'],
      ['player','player','e_talk_40_dialogue2','e_talk_40_button2'],
    ],
        
      },
    },
    'e_talk_42':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['player','player','e_talk_42_dialogue2','e_talk_42_button2'],
      ['player','player','e_talk_42_dialogue2','e_talk_42_button2'],
    ],
        
      },
    },
    'e_talk_42':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['player','player','e_talk_42_dialogue2','e_talk_42_button2'],
      ['player','player','e_talk_42_dialogue2','e_talk_42_button2'],
    ],
        
      },
    },
    'e_talk_43':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['player','player','e_talk_43_dialogue2','e_talk_43_button2'],
      ['player','player','e_talk_43_dialogue2','e_talk_43_button2'],
    ],
        
      },
    },
    'e_talk_44':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['player','player','e_talk_44_dialogue2','e_talk_44_button2'],
      ['player','player','e_talk_44_dialogue2','e_talk_44_button2'],
    ],
        
      },
    },
    'e_talk_45':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['player','player','e_talk_45_dialogue2','e_talk_45_button2'],
      ['player','player','e_talk_45_dialogue2','e_talk_45_button2'],
    ],
        
      },
    },
    'e_talk_46':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['player','player','e_talk_46_dialogue2','e_talk_46_button2'],
      ['player','player','e_talk_46_dialogue2','e_talk_46_button2'],
    ],
        
      },
    },
    'e_talk_47':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['player','player','e_talk_47_dialogue2','e_talk_47_button2'],
      ['player','player','e_talk_47_dialogue2','e_talk_47_button2'],
    ],
        
      },
    },
    'e_talk_48':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['player','player','e_talk_48_dialogue2','e_talk_48_button2'],
      ['player','player','e_talk_48_dialogue2','e_talk_48_button2'],
    ],
        
      },
    },
    'e_talk_49':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['player','player','e_talk_49_dialogue2','e_talk_49_button2'],
      ['player','player','e_talk_49_dialogue2','e_talk_49_button2'],
    ],
        
      },
    },
    'e_talk_50':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['player','player','e_talk_50_dialogue2','e_talk_50_button2'],
      ['player','player','e_talk_50_dialogue2','e_talk_50_button2'],
    ],
        
      },
    },
    'e_talk_52':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['player','player','e_talk_52_dialogue2','e_talk_52_button2'],
      ['player','player','e_talk_52_dialogue2','e_talk_52_button2'],
    ],
        
      },
    },
    'e_talk_52':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['player','player','e_talk_52_dialogue2','e_talk_52_button2'],
      ['player','player','e_talk_52_dialogue2','e_talk_52_button2'],
    ],
        
      },
    },
    'e_talk_53':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['player','player','e_talk_53_dialogue2','e_talk_53_button2'],
      ['player','player','e_talk_53_dialogue2','e_talk_53_button2'],
    ],
        
      },
    },
    'e_talk_54':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['player','player','e_talk_54_dialogue2','e_talk_54_button2'],
      ['player','player','e_talk_54_dialogue2','e_talk_54_button2'],
    ],
        
      },
    },
    'e_talk_75':{
      'globle':{
        'type':'talk',
        'icon':'gwent_event023',
        'limit':2,
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'talk':[
      ['player','player','e_talk_75_dialogue2','e_talk_75_button2'],
      ['player','player','e_talk_75_dialogue2','e_talk_75_button2'],
    ],
        
      },
    },
    'e_secret_exit_02':{
      'globle':{
        'type':'secret_exit',
        'icon':'gwent_event020',
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
        
      },
    },
    'e_secret_02':{
      'globle':{
        'type':'secret',
        'icon':'gwent_event022',
        'diff':[ 'easy', 'normal', 'medium', 'hard'],
        'stage':[2,2,3],
        'stairs':[3,4,5,6,7,8,9,200,22,22],
         'secret_map':'secret02', 
      },
    },
    'e_secret_02':{
      'globle':{
        'type':'secret',
        'icon':'gwent_event022',
        'diff':[ 'normal', 'medium', 'hard', 'super'],
        'stage':[3],
        'stairs':[3,4,5,6,7,8,9,200,22,22],
         'secret_map':'secret02', 
      },
    },
    'e_fight_02':{
      'globle':{
        'type':'fight',
        'icon':'gwent_event025',
        'diff':[ 'easy', ],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[2000], 
      },
      'local':[
       {
        'diff':[ 'easy', ],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[2000], 
      },
       {
        'diff':[ 'easy', ],
        'stage':[3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb03',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[2000], 
      },
       {
        'diff':['normal'],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[250], 
      },
       {
        'diff':['normal'],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[250], 
      },
       {
        'diff':['normal'],
        'stage':[3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb03',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[250], 
      },
       {
        'diff':[ 'medium'],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[200], 
      },
       {
        'diff':[ 'medium'],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[200], 
      },
       {
        'diff':[ 'medium'],
        'stage':[3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb03',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[200], 
      },
       {
        'diff':[ 'hard',],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[250], 
      },
       {
        'diff':[ 'hard',],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[250], 
      },
       {
        'diff':[ 'hard',],
        'stage':[3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb03',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[250], 
      },
       {
        'diff':[ 'super'],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[300], 
      },
       {
        'diff':[ 'super'],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[300], 
      },
       {
        'diff':[ 'super'],
        'stage':[3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[300], 
      },
 ],
    },
    'e_fight_02':{
      'globle':{
        'type':'fight',
        'icon':'gwent_event025',
        'diff':[ 'easy', ],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',20000],['floor2',0],['floor2',0]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[2000], 
      },
      'local':[
       {
        'diff':[ 'easy', ],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[2000], 
      },
       {
        'diff':[ 'easy', ],
        'stage':[3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb03',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[2000], 
      },
       {
        'diff':['normal'],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[250], 
      },
       {
        'diff':['normal'],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[250], 
      },
       {
        'diff':['normal'],
        'stage':[3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb03',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[250], 
      },
       {
        'diff':[ 'medium'],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[200], 
      },
       {
        'diff':[ 'medium'],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[200], 
      },
       {
        'diff':[ 'medium'],
        'stage':[3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb03',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[200], 
      },
       {
        'diff':[ 'hard',],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[250], 
      },
       {
        'diff':[ 'hard',],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[250], 
      },
       {
        'diff':[ 'hard',],
        'stage':[3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb03',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[250], 
      },
       {
        'diff':[ 'super'],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[300], 
      },
       {
        'diff':[ 'super'],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[300], 
      },
       {
        'diff':[ 'super'],
        'stage':[3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[300], 
      },
 ],
    },
    'e_fight_03':{
      'globle':{
        'type':'fight',
        'icon':'gwent_event025',
        'diff':[ 'easy', ],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',20000],['floor2',0],['floor2',0]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2003','rb2003','rb2003'],         
    'token':[2000], 
      },
      'local':[
       {
        'diff':[ 'easy', ],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[2000], 
      },
       {
        'diff':[ 'easy', ],
        'stage':[3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb03',2000]],         
    'reward_choice': ['rb2003','rb2003','rb2003'],         
    'token':[2000], 
      },
       {
        'diff':['normal'],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2003','rb2003','rb2003'],         
    'token':[250], 
      },
       {
        'diff':['normal'],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2003','rb2003','rb2003'],         
    'token':[250], 
      },
       {
        'diff':['normal'],
        'stage':[3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb03',2000]],         
    'reward_choice': ['rb2003','rb2003','rb2003'],         
    'token':[250], 
      },
       {
        'diff':[ 'medium'],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2003','rb2003','rb2003'],         
    'token':[200], 
      },
       {
        'diff':[ 'medium'],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2003','rb2003','rb2003'],         
    'token':[200], 
      },
       {
        'diff':[ 'medium'],
        'stage':[3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb03',2000]],         
    'reward_choice': ['rb2003','rb2003','rb2003'],         
    'token':[200], 
      },
       {
        'diff':[ 'hard',],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2003','rb2003','rb2003'],         
    'token':[250], 
      },
       {
        'diff':[ 'hard',],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2003','rb2003','rb2003'],         
    'token':[250], 
      },
       {
        'diff':[ 'hard',],
        'stage':[3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb03',2000]],         
    'reward_choice': ['rb2003','rb2003','rb2003'],         
    'token':[250], 
      },
       {
        'diff':[ 'super'],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2003','rb2003','rb2003'],         
    'token':[300], 
      },
       {
        'diff':[ 'super'],
        'stage':[2],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2003','rb2003','rb2003'],         
    'token':[300], 
      },
       {
        'diff':[ 'super'],
        'stage':[3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 0,              
    'floor': [['floor0',800]],         
    'enemy_box': [['mb02',2000]],         
    'reward_choice': ['rb2003','rb2003','rb2003'],         
    'token':[300], 
      },
 ],
    },
    'e_fight_04':{
      'globle':{
        'type':'fight',
        'icon':'gwent_event026',
        'diff':[ 'easy', ],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 2,              
    'floor': [['floor0',20000]],         
    'enemy_box': [['eb02',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[300], 
      },
      'local':[
       {
        'diff':['normal'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['eb02',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[950], 
      },
       {
        'diff':[ 'medium'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['eb02',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[400], 
      },
       {
        'diff':[ 'hard',],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['eb02',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[450], 
      },
       {
        'diff':[ 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['eb02',2000]],         
    'reward_choice': ['rb2002','rb2002','rb2002'],         
    'token':[500], 
      },
 ],
    },
    'e_fight_05':{
      'globle':{
        'type':'fight',
        'icon':'gwent_event026',
        'diff':[ 'easy', ],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 2,              
    'floor': [['floor0',20000]],         
    'enemy_box': [['eb02',2000]],         
    'reward_choice': ['rb2003','rb2003','rb2003'],         
    'token':[300], 
      },
      'local':[
       {
        'diff':['normal'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['eb02',2000]],         
    'reward_choice': ['rb2003','rb2003','rb2003'],         
    'token':[950], 
      },
       {
        'diff':[ 'medium'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['eb02',2000]],         
    'reward_choice': ['rb2003','rb2003','rb2003'],         
    'token':[400], 
      },
       {
        'diff':[ 'hard',],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['eb02',2000]],         
    'reward_choice': ['rb2003','rb2003','rb2003'],         
    'token':[450], 
      },
       {
        'diff':[ 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['eb02',2000]],         
    'reward_choice': ['rb2003','rb2003','rb2003'],         
    'token':[500], 
      },
 ],
    },
    'e_fight_06':{
      'globle':{
        'type':'fight',
        'icon':'gwent_event026',
        'diff':[ 'easy', ],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 2,              
    'floor': [['floor0',20000]],         
    'enemy_box': [['eb02',2000]],         
    'reward_choice': ['rb2004','rb2004','rb2004'],         
    'token':[300], 
      },
      'local':[
       {
        'diff':['normal'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['eb02',2000]],         
    'reward_choice': ['rb2004','rb2004','rb2004'],         
    'token':[950], 
      },
       {
        'diff':[ 'medium'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['eb02',2000]],         
    'reward_choice': ['rb2004','rb2004','rb2004'],         
    'token':[400], 
      },
       {
        'diff':[ 'hard',],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['eb02',2000]],         
    'reward_choice': ['rb2004','rb2004','rb2004'],         
    'token':[450], 
      },
       {
        'diff':[ 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['eb02',2000]],         
    'reward_choice': ['rb2004','rb2004','rb2004'],         
    'token':[500], 
      },
 ],
    },
    'e_fight_09':{
      'globle':{
        'type':'fight',
        'icon':'gwent_event026',
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 2,              
    'floor': [['floor0',20000],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['ce02',2000]],         
    'reward_choice': ['rb2003','rb2003','rb2003'],         
    'token':[400], 
      },
    },
    'e_fight_200':{
      'globle':{
        'type':'fight',
        'icon':'gwent_event027',
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 4,              
    'floor': [['floor0',20000],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['cb02',2000]],         
    'reward_choice': ['rb2003','rb2003','rb2003'],         
    'token':[2200], 
      },
    },
    'e_fight_22':{
      'globle':{
        'type':'fight',
        'icon':'gwent_event027',
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
            'lv': 4,              
    'floor': [['floor0',20000],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['cb02',2000]],         
    'reward_choice': ['rb2004','rb2004','rb2004'],         
    'token':[2200], 
      },
    },
    'e_fight_07':{
      'globle':{
        'type':'fight',
        'icon':'gwent_event028',
        'diff':[ 'easy', ],
        'stage':[2,2,3],
        'stairs':[23],
            'lv': 2,              
    'floor': [['floor0',20000]],         
    'enemy_box': [['sb02',2000]],         
    'reward_choice': ['rb2004','rb2004','rb2004'],         
    'token':[20000], 
      },
      'local':[
       {
        'diff':[ 'easy', ],
        'stage':[2],
        'stairs':[23],
            'lv': 2,              
    'floor': [['floor0',200]],         
    'enemy_box': [['sb02',2000]],         
    'reward_choice': ['rb2004','rb2004','rb2004'],         
    'token':[20000], 
      },
       {
        'diff':[ 'easy', ],
        'stage':[3],
        'stairs':[23],
            'lv': 2,              
    'floor': [['floor0',200]],         
    'enemy_box': [['sb03',2000]],         
    'reward_choice': ['rb2004','rb2004','rb2004'],         
    'token':[20000], 
      },
       {
        'diff':['normal'],
        'stage':[2],
        'stairs':[23],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['sb02',2000]],         
    'reward_choice': ['rb2004','rb2004','rb2004'],         
    'token':[2200], 
      },
       {
        'diff':['normal'],
        'stage':[2],
        'stairs':[23],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['sb02',2000]],         
    'reward_choice': ['rb2004','rb2004','rb2004'],         
    'token':[2200], 
      },
       {
        'diff':['normal'],
        'stage':[3],
        'stairs':[23],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['sb03',2000]],         
    'reward_choice': ['rb2004','rb2004','rb2004'],         
    'token':[2200], 
      },
       {
        'diff':[ 'medium'],
        'stage':[2],
        'stairs':[23],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['sb02',2000]],         
    'reward_choice': ['rb2004','rb2004','rb2004'],         
    'token':[2500], 
      },
       {
        'diff':[ 'medium'],
        'stage':[2],
        'stairs':[23],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['sb02',2000]],         
    'reward_choice': ['rb2004','rb2004','rb2004'],         
    'token':[2500], 
      },
       {
        'diff':[ 'medium'],
        'stage':[3],
        'stairs':[23],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['sb03',2000]],         
    'reward_choice': ['rb2004','rb2004','rb2004'],         
    'token':[2500], 
      },
       {
        'diff':[ 'hard',],
        'stage':[2],
        'stairs':[23],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['sb02',2000]],         
    'reward_choice': ['rb2004','rb2004','rb2004'],         
    'token':[2800], 
      },
       {
        'diff':[ 'hard',],
        'stage':[2],
        'stairs':[23],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['sb02',2000]],         
    'reward_choice': ['rb2004','rb2004','rb2004'],         
    'token':[2800], 
      },
       {
        'diff':[ 'hard',],
        'stage':[3],
        'stairs':[23],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['sb03',2000]],         
    'reward_choice': ['rb2004','rb2004','rb2004'],         
    'token':[2800], 
      },
       {
        'diff':[ 'super'],
        'stage':[2],
        'stairs':[23],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['sb02',2000]],         
    'reward_choice': ['rb2004','rb2004','rb2004'],         
    'token':[22000], 
      },
       {
        'diff':[ 'super'],
        'stage':[2],
        'stairs':[23],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['sb02',2000]],         
    'reward_choice': ['rb2004','rb2004','rb2004'],         
    'token':[22000], 
      },
       {
        'diff':[ 'super'],
        'stage':[3],
        'stairs':[23],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['sb03',2000]],         
    'reward_choice': ['rb2004','rb2004','rb2004'],         
    'token':[22000], 
      },
 ],
    },
    'e_fight_08':{
      'globle':{
        'type':'fight',
        'icon':'gwent_event028',
        'diff':[ 'easy', ],
        'stage':[2,2,3],
        'stairs':[23],
            'lv': 2,              
    'floor': [['floor0',20000]],         
    'enemy_box': [['sb02',2000]],         
    'reward_choice': ['rb2005','rb2005','rb2005'],         
    'token':[20000], 
      },
      'local':[
       {
        'diff':[ 'easy', ],
        'stage':[2],
        'stairs':[23],
            'lv': 2,              
    'floor': [['floor0',20000]],         
    'enemy_box': [['sb02',2000]],         
    'reward_choice': ['rb2005','rb2005','rb2005'],         
    'token':[20000], 
      },
       {
        'diff':[ 'easy', ],
        'stage':[3],
        'stairs':[23],
            'lv': 2,              
    'floor': [['floor0',20000]],         
    'enemy_box': [['sb03',2000]],         
    'reward_choice': ['rb2005','rb2005','rb2005'],         
    'token':[20000], 
      },
       {
        'diff':['normal'],
        'stage':[2],
        'stairs':[23],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['sb02',2000]],         
    'reward_choice': ['rb2005','rb2005','rb2005'],         
    'token':[2200], 
      },
       {
        'diff':['normal'],
        'stage':[2],
        'stairs':[23],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['sb02',2000]],         
    'reward_choice': ['rb2005','rb2005','rb2005'],         
    'token':[2200], 
      },
       {
        'diff':['normal'],
        'stage':[3],
        'stairs':[23],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['sb03',2000]],         
    'reward_choice': ['rb2005','rb2005','rb2005'],         
    'token':[2200], 
      },
       {
        'diff':[ 'medium'],
        'stage':[2],
        'stairs':[23],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['sb02',2000]],         
    'reward_choice': ['rb2005','rb2005','rb2005'],         
    'token':[2500], 
      },
       {
        'diff':[ 'medium'],
        'stage':[2],
        'stairs':[23],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['sb02',2000]],         
    'reward_choice': ['rb2005','rb2005','rb2005'],         
    'token':[2500], 
      },
       {
        'diff':[ 'medium'],
        'stage':[3],
        'stairs':[23],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['sb03',2000]],         
    'reward_choice': ['rb2005','rb2005','rb2005'],         
    'token':[2500], 
      },
       {
        'diff':[ 'hard',],
        'stage':[2],
        'stairs':[23],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['sb02',2000]],         
    'reward_choice': ['rb2005','rb2005','rb2005'],         
    'token':[2800], 
      },
       {
        'diff':[ 'hard',],
        'stage':[2],
        'stairs':[23],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['sb02',2000]],         
    'reward_choice': ['rb2005','rb2005','rb2005'],         
    'token':[2800], 
      },
       {
        'diff':[ 'hard',],
        'stage':[3],
        'stairs':[23],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['sb03',2000]],         
    'reward_choice': ['rb2005','rb2005','rb2005'],         
    'token':[2800], 
      },
       {
        'diff':[ 'super'],
        'stage':[2],
        'stairs':[23],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['sb02',2000]],         
    'reward_choice': ['rb2005','rb2005','rb2005'],         
    'token':[22000], 
      },
       {
        'diff':[ 'super'],
        'stage':[2],
        'stairs':[23],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['sb02',2000]],         
    'reward_choice': ['rb2005','rb2005','rb2005'],         
    'token':[22000], 
      },
       {
        'diff':[ 'super'],
        'stage':[3],
        'stairs':[23],
            'lv': 2,              
    'floor': [['floor0',200],['floor2',50],['floor2',50],['floor3',50],['floor4',50]],         
    'enemy_box': [['sb03',2000]],         
    'reward_choice': ['rb2005','rb2005','rb2005'],         
    'token':[22000], 
      },
 ],
    },
    'e_talk_limit_02':{
      'globle':{
        'type':'talk_limit',
        'icon':'gwent_event023',
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
           'talk_box': [['talk2002',2000]],         
 'reward':[ 
   [['rb2006'],300], 

  ],
      },
    },
    'e_talk_limit_02':{
      'globle':{
        'type':'talk_limit',
        'icon':'gwent_event023',
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
           'talk_box': [['talk2002',2000]],         
  'reward':[ 
   [['rb2002'],300], 

  ],
      },
    },
    'e_talk_limit_03':{
      'globle':{
        'type':'talk_limit',
        'icon':'gwent_event023',
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
           'talk_box': [['talk2003',2000]],         
  'reward':[ 
   [['rb2002'],300], 

  ],
      },
    },
    'e_talk_limit_04':{
      'globle':{
        'type':'talk_limit',
        'icon':'gwent_event023',
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
           'talk_box': [['talk2004',2000]],         
 'reward':[ 
    [['rb2003'],200],  [['rb2004'],2000], 

  ],
      },
    },
    'e_talk_limit_05':{
      'globle':{
        'type':'talk_limit',
        'icon':'gwent_event025',
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
           'talk_box': [['talk2005',2000]],         
  'reward':[ 
    [['rb2002'],600],  [['rb2003'],250], [['rb2004'],250], 

  ],
      },
    },
    'e_talk_limit_06':{
      'globle':{
        'type':'talk_limit',
        'icon':'gwent_event023',
        'diff':[ 'easy', 'normal', 'medium', 'hard', 'super'],
        'stage':[2,2,3],
        'stairs':[2,2,3,4,5,6,7,8,9,200,22,22],
           'talk_box': [['talk2006',2000]],         
 'reward':[ 
    [['rb223','rb223'],600], 

  ],
      },
    },
},


    

   'relic':{

     'relic2002':{    
                    'quality':2,          
                    'price':400,    
                    'power':[{
                           'cond':{ 
                                'sex':0,
                               },
                           'rslt':{
                                'cha0':4,'lead0':4,     
                               },
                          },
                         ],
             },
    'relic2002':{    
                    'quality':2,          
                    'price':400,    
                    'power':[{
                           'cond':{ 
                                'rarity':4,
                               },
                           'rslt':{
                                'str0':4,'agi0':4,         
                               },
                          },
                         ],
             },
    'relic2003':{    
                    'quality':2,          
                    'price':400,    
                    'power':[{
                           'cond':{ 
                                'type':0, 
                               },
                           'rslt':{
                                'str0':6,   
                               },
                          },
                         ],
             },
    'relic2004':{    
                    'quality':2,          
                    'price':400,    
                    'power':[{
                           'cond':{ 
                                'type':2, 
                               },
                           'rslt':{
                                'agi0':6,  
                               },
                          },
                         ],
             },
    'relic2005':{    
                    'quality':2,          
                    'price':400,    
                    'power':[{
                           'cond':{ 
                                'type':2, 
                               },
                           'rslt':{
                                'cha0':3,'lead0':3,  
                               },
                          },
                         ],
             },
    'relic2006':{    
                    'quality':2,          
                    'price':400,    
                    'power':[{
                           'cond':{ 
                                'army':0,
                               },
                           'rslt':{
                                'cha0':3,'lead0':2,   
                               },
                          },
                         ],
             },
    'relic2007':{    
                    'quality':2,          
                    'price':400,    
                    'power':[{
                           'cond':{ 
                                'army':2,
                               },
                           'rslt':{
                                'str0':3,'agi0':2,   
                               },
                          },
                         ],
             },
    'relic2008':{    
                    'quality':2,          
                    'price':400,    
                    'power':[{
                           'cond':{ 
                                'army':2,
                               },
                           'rslt':{
                                'lead0':3,'str0':2,  
                               },
                          },
                         ],
             },
    'relic2009':{    
                    'quality':2,          
                    'price':400,    
                    'power':[{
                           'cond':{ 
                                'army':3,
                               },
                           'rslt':{
                                'agi0':3,'cha0':2, 
                               },
                          },
                         ],
             },
    'relic2200':{    
                    'quality':2,          
                    'price':400,    
                    'power':[{
                           'rslt':{
                                'str0':3,  
                               },
                          },
                         ],
             },
    'relic222':{    
                    'quality':2,          
                    'price':400,    
                    'power':[{
                           'rslt':{
                                'agi0':3, 
                               },
                          },
                         ],
             },
    'relic222':{    
                    'quality':2,          
                    'price':400,    
                    'power':[{
                           'rslt':{
                                'cha0':3, 
                               },
                          },
                         ],
             },
    'relic223':{    
                    'quality':2,          
                    'price':400,    
                    'power':[{
                           'rslt':{
                                'lead0':3, 
                               },
                          },
                         ],
             },
    'relic224':{    
                    'quality':2,          
                    'price':400,    
                    'power':[{
                           'rslt':{
                                'cha0':3, 
                               },
                          },
                          {
                           'rslt':{
                                'patch':0.03,
                               },
                          },
                         ],
             },
    'relic202':{    
                    'quality':2,          
                    'price':800,    
                    'power':[{
                           'cond':{ 
                                'sex':0,
                               },
                           'rslt':{
                                'cha2':0.06,'lead2':0.06,   
                               },
                          },
                         ],
             },
    'relic202':{    
                    'quality':2,          
                    'price':800,    
                    'power':[{
                           'cond':{ 
                                'rarity':4,
                               },
                           'rslt':{
                                'str2':0.06, 'agi2':0.06,  
                               },
                          },
                         ],
             },
    'relic203':{    
                    'quality':2,          
                    'price':800,    
                    'power':[{
                           'cond':{ 
                                'type':0, 
                               },
                           'rslt':{
                                'str2':0.05,'cha2':0.05,
                               },
                          },
                         ],
             },
    'relic204':{    
                    'quality':2,          
                    'price':800,    
                    'power':[{
                           'cond':{ 
                                'type':2, 
                               },
                           'rslt':{
                                'agi2':0.05, 'lead2':0.05,   
                               },
                          },
                         ],
             },
    'relic205':{    
                    'quality':2,          
                    'price':800,    
                    'power':[{
                           'cond':{ 
                                'type':2, 
                               },
                           'rslt':{
                                'all2' :0.025,
                               },
                          },
                         ],
             },
    'relic206':{    
                    'quality':2,          
                    'price':800,    
                    'power':[{
                           'cond':{ 
                                'rarity':5,
                               },
                           'rslt':{
                                'all2' :0.03,
                               },
                          },
                         ],
             },
    'relic207':{    
                    'quality':2,          
                    'price':800,    
                    'power':[{
                           'cond':{ 
                                'army':0,
                               },
                           'rslt':{
                                'cha2':0.08,   
                               },
                          },
                         ],
             },
    'relic208':{    
                    'quality':2,          
                    'price':800,    
                    'power':[{
                           'cond':{ 
                                'army':2,
                               },
                           'rslt':{
                                'str2':0.08,  
                               },
                          },
                         ],
             },
    'relic209':{    
                    'quality':2,          
                    'price':800,    
                    'power':[{
                           'cond':{ 
                                'army':2,
                               },
                           'rslt':{
                                'lead2':0.08,  
                               },
                          },
                         ],
             },
    'relic2200':{    
                    'quality':2,          
                    'price':800,    
                    'power':[{
                           'cond':{ 
                                'army':3,
                               },
                           'rslt':{
                                'agi2':0.08, 
                               },
                          },
                         ],
             },
    'relic222':{    
                    'quality':2,          
                    'price':800,    
                    'power':[{
                           'cond':{ 
                                'rarity':2,
                               },
                           'rslt':{
                                'all2' :0.02,
                               },
                          },
                         ],
             },
    'relic222':{    
                    'quality':2,          
                    'price':800,    
                    'power':[{
                           'rslt':{
                                'str2':0.025,'lead2':0.025,
                               },
                          },
                         ],
             },
    'relic223':{    
                    'quality':2,          
                    'price':800,    
                    'power':[{
                           'rslt':{
                                'agi2':0.025,'cha2':0.025, 
                               },
                          },
                         ],
             },
    'relic302':{    
                    'quality':3,          
                    'price':2000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'appear':[4,['sex',0]],
                                'enemy':2, 
                               },
                           'rslt':{
                                'str2':-0.22,    
 'agi2':-0.22,
                               },
                          },
                         ],
             },
    'relic302':{    
                    'quality':3,          
                    'price':2000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'sex':0,
                               },
                           'rslt':{
                                'all0' :6,
                               },
                          },
                         ],
             },
    'relic303':{    
                    'quality':3,          
                    'price':2000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'appear':[4,['rarity',4]],
                                'enemy':2, 
                               },
                           'rslt':{
                                'lead2':-0.22, 'cha2':-0.22,  
                               },
                          },
                         ],
             },
    'relic304':{    
                    'quality':3,          
                    'price':2000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'rarity':4,
                               },
                           'rslt':{
                                'all0' :7,
                               },
                          },
                         ],
             },
    'relic305':{    
                    'quality':3,          
                    'price':2000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'appear':[4,['type',0]],
                                'enemy':2, 
                               },
                           'rslt':{
                                'all2' :-0.05,
                               },
                          },
                         ],
             },
    'relic306':{    
                    'quality':3,          
                    'price':2000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'appear':[4,['type',2]],
                                'enemy':2, 
                               },
                           'rslt':{
                                'all2' :-0.05,
                               },
                          },
                         ],
             },
    'relic307':{    
                    'quality':3,          
                    'price':2000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'appear':[4,['type',2]],
                                'enemy':2, 
                               },
                           'rslt':{
                                'all2' :-0.05,
                               },
                          },
                         ],
             },
    'relic308':{    
                    'quality':3,          
                    'price':2000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'victory':2, 
                                'type':0, 
                               },
                           'rslt':{
                                'str0':2, 'cha0':2,
                               },
                          },
                         ],
             },
    'relic309':{    
                    'quality':3,          
                    'price':2000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'victory':2, 
                                'type':2, 
                               },
                           'rslt':{
                                'agi0':2,'lead0':2, 
                               },
                          },
                         ],
             },
    'relic3200':{    
                    'quality':3,          
                    'price':2000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'victory':2, 
                                'type':2, 
                               },
                           'rslt':{
                                'all0' :2,
                               },
                          },
                         ],
             },
    'relic322':{    
                    'quality':3,          
                    'price':2000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'rarity':5,
                               },
                           'rslt':{
                                'str0':25, 'lead0':25, 
                               },
                          },
                         ],
             },
    'relic322':{    
                    'quality':3,          
                    'price':2000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'appear':[0,['rarity',5]],
                                'enemy':2, 
                               },
                           'rslt':{
                                'all2' :-0.03,
                               },
                          },
                         ],
             },
    'relic323':{    
                    'quality':3,          
                    'price':2000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'rarity':5,
                               },
                           'rslt':{
                                'cha0':25, 'lead0':25, 
                               },
                          },
                         ],
             },
    'relic324':{    
                    'quality':3,          
                    'price':2000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'appear':[0,['army',0]],
                                'enemy':2, 
                               },
                           'rslt':{
                                'str2':-0.03,   
                               },
                          },
                         ],
             },
    'relic325':{    
                    'quality':3,          
                    'price':2000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'appear':[0,['army',2]],
                                'enemy':2, 
                               },
                           'rslt':{
                                'cha2':-0.03,
                               },
                          },
                         ],
             },
    'relic326':{    
                    'quality':3,          
                    'price':2000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'appear':[0,['army',2]],
                                'enemy':2, 
                               },
                           'rslt':{
                                'agi2':-0.03,
                               },
                          },
                         ],
             },
    'relic327':{    
                    'quality':3,          
                    'price':2000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'appear':[0,['army',3]],
                                'enemy':2, 
                               },
                           'rslt':{
                                'lead2':-0.03, 
                               },
                          },
                         ],
             },
    'relic328':{    
                    'quality':3,          
                    'price':2000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'army':0,
                               },
                           'rslt':{
                                'all0' :5,
                               },
                          },
                         ],
             },
    'relic329':{    
                    'quality':3,          
                    'price':2000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'army':2,
                               },
                           'rslt':{
                                'all0' :5,
                               },
                          },
                         ],
             },
    'relic320':{    
                    'quality':3,          
                    'price':2000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'army':2,
                               },
                           'rslt':{
                                'all0' :5,
                               },
                          },
                         ],
             },
    'relic322':{    
                    'quality':3,          
                    'price':2000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'army':3,
                               },
                           'rslt':{
                                'all0' :5,
                               },
                          },
                         ],
             },
    'relic322':{    
                    'quality':3,          
                    'price':2000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'quality':[2,3],
                               },
                           'rslt':{
                                'cha0':2,  'str0':2,  
                               },
                          },
                         ],
             },
    'relic323':{    
                    'quality':3,          
                    'price':2000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'quality':[2,3],
                               },
                           'rslt':{
                                'agi0':2, 'lead0':2,  
                               },
                          },
                         ],
             },
    'relic324':{    
                    'quality':3,          
                    'price':2000,    
                    'only':2,       
                    'power':[{
                           'rslt':{
                                'str0':22,  
                               },
                          },
                         ],
             },
    'relic325':{    
                    'quality':3,          
                    'price':2000,    
                    'only':2,       
                    'power':[{
                           'rslt':{
                                'agi0':22,  
                               },
                          },
                         ],
             },
    'relic326':{    
                    'quality':3,          
                    'price':2000,    
                    'only':2,       
                    'power':[{
                           'rslt':{
                                'cha0':22,
                               },
                          },
                         ],
             },
    'relic327':{    
                    'quality':3,          
                    'price':2000,    
                    'only':2,       
                    'power':[{
                           'rslt':{
                                'lead0':22,  
                               },
                          },
                         ],
             },
    'relic328':{    
                    'quality':3,          
                    'price':2000,    
                    'only':2,       
                    'power':[{
                           'rslt':{
                                'patch':0.25,
                               },
                          },
                         ],
             },
    'relic329':{    
                    'quality':3,          
                    'price':2000,    
                    'only':2,       
                    'power':[{
                           'rslt':{
                                'treasure':0.2
                               },
                          },
                         ],
             },
    'relic330':{    
                    'quality':3,          
                    'price':2000,    
                    'only':2,       
                    'power':[{
                           'rslt':{
                                'order':2,
                               },
                          },
                         ],
             },
    'relic332':{    
                    'quality':3,          
                    'price':2000,    
                    'power':[{
                           'rslt':{
                                'lead0':22,  
                               },
                          },
                          {
                           'rslt':{
                                'treasure':0.05
                               },
                          },
                         ],
             },
    'relic402':{    
                    'quality':4,          
                    'price':5000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'sex':0,
                               },
                           'rslt':{
                                'cha0':28,'lead0':28,'str0':6, 'agi0':6,     
                               },
                          },
                         ],
             },
    'relic402':{    
                    'quality':4,          
                    'price':5000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'rarity':4,
                               },
                           'rslt':{
                                'cha0':6, 'lead0':6,'str0':26, 'agi0':26,     
                               },
                          },
                         ],
             },
    'relic403':{    
                    'quality':4,          
                    'price':5000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'type':0, 
                               },
                           'rslt':{
                                'cha2':0.25, 'lead2':0.05, 'str2':0.25, 'agi2':0.05,     
                               },
                          },
                         ],
             },
    'relic404':{    
                    'quality':4,          
                    'price':5000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'type':2, 
                               },
                           'rslt':{
                                'cha2':0.05, 'lead2':0.25, 'str2':0.05,  'agi2':0.25,     
                               },
                          },
                         ],
             },
    'relic405':{    
                    'quality':4,          
                    'price':5000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'type':2, 
                               },
                           'rslt':{
                                'all2' :0.2,
                               },
                          },
                         ],
             },
    'relic406':{    
                    'quality':4,          
                    'price':5000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'quality':[5,0], 
                                'rarity':5,
                               },
                           'rslt':{
                                 'str0':4,   'lead0':4,
                               },
                          },
                         ],
             },
    'relic407':{    
                    'quality':4,          
                    'price':5000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'appear':[5,['rarity',5]],
                                'enemy':2, 
                               },
                           'rslt':{
                                'random2':-0.25,
                               },
                          },
                         ],
             },
    'relic408':{    
                    'quality':4,          
                    'price':5000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'quality':[5,0],
                                'army':0,
                               },
                           'rslt':{
                                'agi0':3, 'cha0':4,  
                               },
                          },
                         ],
             },
    'relic409':{    
                    'quality':4,          
                    'price':5000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'quality':[5,0],
                                'army':2,
                               },
                           'rslt':{
                                'str0':4, 'lead0':3,
                               },
                          },
                         ],
             },
    'relic4200':{    
                    'quality':4,          
                    'price':5000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'quality':[5,0],
                                'army':2,
                               },
                           'rslt':{
                                'lead0':4, 'cha0':3,  
                               },
                          },
                         ],
             },
    'relic422':{    
                    'quality':4,          
                    'price':5000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'quality':[5,0],
                                'army':3,
                               },
                           'rslt':{
                                'agi0':4,  'str0':2,  
                               },
                          },
                         ],
             },
    'relic422':{    
                    'quality':4,          
                    'price':5000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'quality':[2,4],
                               },
                           'rslt':{
                                'str0':6,  
                               },
                          },
                         ],
             },
    'relic423':{    
                    'quality':4,          
                    'price':5000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'quality':[2,4],
                               },
                           'rslt':{
                                'agi0':6, 
                               },
                          },
                         ],
             },
    'relic424':{    
                    'quality':4,          
                    'price':5000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'quality':[2,4],
                               },
                           'rslt':{
                                'cha0':6,  
                               },
                          },
                         ],
             },
    'relic425':{    
                    'quality':4,          
                    'price':5000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'quality':[2,4],
                               },
                           'rslt':{
                                'lead0':6,
                               },
                          },
                         ],
             },
    'relic426':{    
                    'quality':4,          
                    'price':5000,    
                    'only':2,       
                    'power':[{
                           'rslt':{
                                'card':2
                               },
                          },
                         ],
             },
    'relic427':{    
                    'quality':4,          
                    'price':5000,    
                    'only':2,       
                    'power':[{
                           'cond':{ 
                                'victory':2, 
                               },
                           'rslt':{
                                'all0' :2,
                               },
                          },
                         ],
             },
    'relic428':{    
                    'quality':4,          
                    'price':5000,    
                    'power':[{
                           'rslt':{
                                'cha0':20,  
                               },
                          },
                          {
                           'rslt':{
                                'patch':0.200,
                               },
                          },
                         ],
             },
    'relic429':{    
                    'quality':4,          
                    'price':5000,    
                    'power':[{
                           'rslt':{
                                'agi0':30,  'str0':-5,  
                               },
                          },
                         ],
             },
    'relic502':{    
                    'quality':5,          
                    'price':20000,    
                    'only':2,       
                    'power':[{
                           'fcond':{ 
                                'play':[0,-2,['sex',0]],  
                                 'target':[0,2,0,2,0,],
                               },
                           'rslt':{
                                'charm':2,
                               },
                          },
                          {
                           'cond':{ 
                                'sex':0,
                               },
                           'rslt':{
                                'all0' :6,
                               },
                          },
                         ],
             },
    'relic502':{    
                    'quality':5,          
                    'price':20000,    
                    'only':2,       
                    'power':[{
                           'fcond':{ 
                                'play':[0,-2,['rarity',4]],  
                                 'target':[0,2,2,0,0,],
                               },
                           'rslt':{
                                'all2' :-0.25,
                               },
                          },
                          {
                           'cond':{ 
                                'rarity':4,
                               },
                           'rslt':{
                                'all0' :6,
                               },
                          },
                         ],
             },
    'relic503':{    
                    'quality':5,          
                    'price':20000,    
                    'only':2,       
                    'power':[{
                           'fcond':{ 
                                'play':[0,0,['type',0]],  
                                 'target':[0,2,4,0,0,],
                               },
                           'rslt':{
                                'agi2':-0.20,  'cha2':-0.20,  
                               },
                          },
                          {
                           'cond':{ 
                                'type':0, 
                               },
                           'rslt':{
                                'all0' :5,
                               },
                          },
                         ],
             },
    'relic504':{    
                    'quality':5,          
                    'price':20000,    
                    'only':2,       
                    'power':[{
                           'fcond':{ 
                                'play':[0,0,['type',2]],
                                 'target':[0,2,0,2,0,],
                               },
                           'rslt':{
                                'str2':-0.200,  'lead2':-0.200,
                               },
                          },
                          {
                           'cond':{ 
                                'type':2, 
                               },
                           'rslt':{
                                'all0' :5,
                               },
                          },
                         ],
             },
    'relic505':{    
                    'quality':5,          
                    'price':20000,    
                    'only':2,       
                    'power':[{
                           'fcond':{ 
                                'play':[0,0,['type',2]],
                                 'target':[0,0,0,2,0,],
                               },
                           'rslt':{
                                'all0' :22,
                               },
                          },
                          {
                           'cond':{ 
                                'type':2, 
                               },
                           'rslt':{
                                'all0' :5,
                               },
                          },
                         ],
             },
    'relic506':{    
                    'quality':5,          
                    'price':20000,    
                    'only':2,       
                    'power':[{
                           'fcond':{ 
                                'play':[0,3],
                                 'target':[2,0,0,0,0,],
                               },
                           'rslt':{
                                'all0' :30,
                               },
                          },
                         ],
             },
    'relic507':{    
                    'quality':5,          
                    'price':20000,    
                    'only':2,       
                    'power':[{
                           'fcond':{ 
                                'play':[2,3],
                                 'target':[2,0,0,0,0,],
                               },
                           'rslt':{
                                'all2' :-0.25,
                               },
                          },
                         ],
             },
    'relic508':{    
                    'quality':5,          
                    'price':20000,    
                    'only':2,       
                    'power':[{
                           'fcond':{ 
                                'action':2,
                                 'target':[0,0,0,0,2],
                               },
                           'rslt':{
                                'all0' :8,
                               },
                          },
                          {
                           'rslt':{
                                'all0' :5,
                               },
                          },
                         ],
             },
    'relic509':{    
                    'quality':5,          
                    'price':20000,    
                    'only':2,       
                    'power':[{
                           'fcond':{ 
                                'action':2,
                                 'target':[0,2,0,2,0],
                               },
                           'rslt':{
                                'opposite':3,
                               },
                          },
                          {
                           'rslt':{
                                'all0' :5,
                               },
                          },
                         ],
             },
    'relic5200':{    
                    'quality':5,          
                    'price':20000,    
                    'only':2,       
                    'power':[{
                           'fcond':{ 
                                'play':[0,0],  
                                 'target':[0,2,2,0,0],  
                               },
                           'rslt':{
                                'opposite':0,
                               },
                          },
                          {
                           'rslt':{
                                'cha0':22, 'str0':22,  
                               },
                          },
                         ],
             },
    'relic522':{    
                    'quality':5,          
                    'price':20000,    
                    'only':2,       
                    'power':[{
                           'fcond':{ 
                                'play':[0,0],  
                                 'target':[0,2,3,0,0],  
                               },
                           'rslt':{
                                'opposite':2,
                               },
                          },
                          {
                           'rslt':{
                                'agi0':22,'lead0':22, 
                               },
                          },
                         ],
             },
    'relic522':{    
                    'quality':5,          
                    'price':20000,    
                    'only':2,       
                    'power':[{
                           'fcond':{ 
                                'action':2,
                                 'target':[0,0,0,2,2,],
                               },
                           'rslt':{
                                'all0' :8,
                               },
                          },
                          {
                           'rslt':{
                                'all0' :5,
                               },
                          },
                         ],
             },
    'relic523':{    
                    'quality':5,          
                    'price':20000,    
                    'only':2,       
                    'power':[{
                           'fcond':{ 
                                'action':3,
                                 'target':[0,0,0,2,2,],
                               },
                           'rslt':{
                                'all0' :22,
                               },
                          },
                          {
                           'rslt':{
                                'all0' :5,
                               },
                          },
                         ],
             },
    'relic524':{    
                    'quality':5,          
                    'price':20000,    
                    'only':2,       
                    'power':[{
                           'fcond':{ 
                                'action':0,
                                'target':[0,2,0,2,0,],
                               },
                           'rslt':{
                                'charm':2,
                               },
                          },
                          {
                           'fcond':{ 
                                'action':2,
                                 'target':[0,0,0,2,0,],
                               },
                           'rslt':{
                                'charm':0,
                               },
                          },
                          {
                           'rslt':{
                                'all0' :5,
                               },
                          },
                         ],
             },
    'relic525':{    
                    'quality':5,          
                    'price':20000,    
                    'only':2,       
                    'power':[{
                           'fcond':{ 
                                'play':[2,0],  
                                'target':[0,0,2,0,0],  
                               },
                           'rslt':{
                                'random0':50,
                               },
                          },
                         ],
             },
    'relic526':{    
                    'quality':5,          
                    'price':20000,    
                    'only':2,       
                    'power':[{
                           'fcond':{ 
                                'play':[0,0],  
                                'target':[0,0,0,0,2],  
                               },
                           'rslt':{
                                'str0':20,  
                               },
                          },
                          {
                           'rslt':{
                                'str0':200,  
                               },
                          },
                         ],
             },
    'relic527':{    
                    'quality':5,          
                    'price':20000,    
                    'only':2,       
                    'power':[{
                           'fcond':{ 
                                'play':[0,0],  
                                 'target':[0,0,0,0,2],  
                               },
                           'rslt':{
                                'agi0':20, 
                               },
                          },
                          {
                           'rslt':{
                                'agi0':200, 
                               },
                          },
                         ],
             },
    'relic528':{    
                    'quality':5,          
                    'price':20000,    
                    'only':2,       
                    'power':[{
                           'fcond':{ 
                                'play':[0,0],  
                                 'target':[0,0,0,0,2],  
                               },
                           'rslt':{
                                'cha0':20,  
                               },
                          },
                          {
                           'rslt':{
                                'cha0':200,  
                               },
                          },
                         ],
             },
    'relic529':{    
                    'quality':5,          
                    'price':20000,    
                    'only':2,       
                    'power':[{
                           'fcond':{ 
                                'play':[0,0],  
                                 'target':[0,0,0,0,2],  
                               },
                           'rslt':{
                                'lead0':20,
                               },
                          },
                          {
                           'rslt':{
                                'lead0':200,
                               },
                          },
                         ],
             },
    'relic602':{    
                    'quality':6,          
                    'price':500,    
                    'power':[{
                           'rslt':{
                                'str2':-0.2,  
                               },
                          },
                         ],
             },
    'relic602':{    
                    'quality':6,          
                    'price':500,    
                    'power':[{
                           'rslt':{
                                'agi2':-0.2, 
                               },
                          },
                         ],
             },
    'relic603':{    
                    'quality':6,          
                    'price':500,    
                    'power':[{
                           'rslt':{
                                'cha2':-0.2,  
                               },
                          },
                         ],
             },
    'relic604':{    
                    'quality':6,          
                    'price':500,    
                    'power':[{
                           'rslt':{
                                'lead2':-0.2,
                               },
                          },
                         ],
             },
    'relic605':{    
                    'quality':6,          
                    'price':500,    
                    'power':[{
                           'rslt':{
                                'all0':-3,
                               },
                          },
                         ],
             },
    'relic606':{    
                    'quality':6,          
                    'price':500,    
                    'power':[{
                           'rslt':{
                                'patch':-0.3,
                               },
                          },
                         ],
             },
    'relic607':{    
                    'quality':6,          
                    'price':500,    
                    'power':[{
                           'cond':{ 
                                'type':0, 
                               },
                           'rslt':{
                                'str2':-0.2,  
                               },
                          },
                         ],
             },
    'relic608':{    
                    'quality':6,          
                    'price':500,    
                    'power':[{
                           'cond':{ 
                                'type':2, 
                               },
                           'rslt':{
                                'agi2':-0.2, 
                               },
                          },
                         ],
             },
    'relic609':{    
                    'quality':6,          
                    'price':500,    
                    'power':[{
                           'cond':{ 
                                'type':2, 
                               },
                           'rslt':{
                                'all2':-0.06,
                               },
                          },
                         ],
             },




}

}