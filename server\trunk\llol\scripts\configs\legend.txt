{
    'merge':2,	 	
    'initial_times':5,
    'need_building':['building025',2],
    'coin_buy':[50,2000,250,200,250,2000,250,200,250,2000,250,200,250,2000,250,200,250,2000,250,200,250,2000,250,200,250,2000,250,200,250,2000,250,200,250,2000,250,200,250,2000,250,200,250,2000,250,200,250,2000,250,200,250,2000,250,200,250,2000,250,200,250,2000,250,200,250,2000,250,200,250],
    'foreshow':2,
    'help_tips':'legend0',
    'heros_2':{
          
          'hero769': {
            'index': 2,
             'road':{        
                'open_date':2,          
                'limit_thero_type':[],  
                'max_battle':5,         
                'uname':'legend_hero769',
                'need_kill':20000000,    
                'lv_add':5,
                'recommend_power':450000,  
                'reward':{'item769':50,'item079':50,'item082':50,}, 
             },
             'experience': {  
                'limit_thero_type':[],  
                'recommend_power':450000,  
                'max_battle':6,         
                'uname':'legends_hero769',
                'fixed_reward':[
[0.2,{'item769':2},[['random958',25],['random960',2],],],
[0.4,{'item769':2},[['random958',25],['random960',2],],],
[0.6,{'item769':3},[['random958',25],['random960',2],],],
[0.8,{'item769':4},[['random958',25],['random960',2],],],
[2,{'item769':5},[['random958',25],['random960',2],],],


          
                 ],
              },
           },
    },
    'heros_2':{
          
          'hero769': {
            'index': 2,
             'road':{        
                'open_date':2,          
                'limit_thero_type':[],  
                'max_battle':5,         
                'uname':'legend_hero769',
                'need_kill':20000000,    
                'recommend_power':450000,  
                'lv_add':5,
                'reward':{'item769':50,'item079':50,'item082':50,}, 
             },
             'experience': {  
                'limit_thero_type':[],  
                'recommend_power':450000,  
                'max_battle':6,         
                'uname':'legends_hero769',
                'fixed_reward':[
[0.2,{'item769':2},[['random958',25],['random960',2],],],
[0.4,{'item769':2},[['random958',25],['random960',2],],],
[0.6,{'item769':3},[['random958',25],['random960',2],],],
[0.8,{'item769':4},[['random958',25],['random960',2],],],
[2,{'item769':5},[['random958',25],['random960',2],],],

                 ],
              },
           },
         
          'hero783': {
            'index': 2,
             'road':{        
                'open_date':2,          
                'limit_thero_type':[],  
                'max_battle':5,         
                'uname':'legend_hero783',
                'need_kill':2500000,    
                'recommend_power':20000000,  
                'lv_add':5,
                'reward':{'item783':50,'item079':50,'item082':50,}, 
             },
             'experience': {  
                'limit_thero_type':[],  
                'max_battle':6,         
                'recommend_power':20000000,  
                'uname':'legends_hero783',
                'fixed_reward':[
[0.2,{'item783':2},[['random957',25],['random962',2],],],
[0.4,{'item783':2},[['random957',25],['random962',2],],],
[0.6,{'item783':3},[['random957',25],['random962',2],],],
[0.8,{'item783':4},[['random957',25],['random962',2],],],
[2,{'item783':5},[['random957',25],['random962',2],],],


          
                 ],
              },
           },
    },
    'heros_3':{
          
          'hero769': {
            'index': 2,
             'road':{        
                'open_date':2,          
                'limit_thero_type':[],  
                'max_battle':5,         
                'uname':'legend_hero769',
                'need_kill':20000000,    
                'recommend_power':450000,  
                'lv_add':5,
                'reward':{'item769':50,'item079':50,'item082':50,}, 
             },
             'experience': {  
                'limit_thero_type':[],  
                'recommend_power':450000,  
                'max_battle':6,         
                'uname':'legends_hero769',
                'fixed_reward':[
[0.2,{'item769':2},[['random958',25],['random960',2],],],
[0.4,{'item769':2},[['random958',25],['random960',2],],],
[0.6,{'item769':3},[['random958',25],['random960',2],],],
[0.8,{'item769':4},[['random958',25],['random960',2],],],
[2,{'item769':5},[['random958',25],['random960',2],],],

                 ],
              },
           },
         
          'hero783': {
            'index': 2,
             'road':{        
                'open_date':2,          
                'limit_thero_type':[],  
                'max_battle':5,         
                'uname':'legend_hero783',
                'need_kill':2500000,    
                'recommend_power':20000000,  
                'lv_add':5,
                'reward':{'item783':50,'item079':50,'item082':50,}, 
             },
             'experience': {  
                'limit_thero_type':[],  
                'recommend_power':20000000,  
                'max_battle':6,         
                'uname':'legends_hero783',
                'fixed_reward':[
[0.2,{'item783':2},[['random957',25],['random962',2],],],
[0.4,{'item783':2},[['random957',25],['random962',2],],],
[0.6,{'item783':3},[['random957',25],['random962',2],],],
[0.8,{'item783':4},[['random957',25],['random962',2],],],
[2,{'item783':5},[['random957',25],['random962',2],],],


          
                 ],
              },
           },

         
          'hero786': {
            'index': 3,
             'road':{        
                'open_date':2,          
                'limit_thero_type':[],  
                'max_battle':5,         
                'uname':'legend_hero786',
                'need_kill':2500000,    
                'recommend_power':2000000,  
                'lv_add':5,
                'reward':{'item786':50,'item079':50,'item082':50,}, 
             },
             'experience': {  
                'limit_thero_type':[],  
                'recommend_power':2000000,  
                'max_battle':6,         
                'uname':'legends_hero786',
                'fixed_reward':[
[0.2,{'item786':2},[['random958',25],['random962',2],],],
[0.4,{'item786':2},[['random958',25],['random962',2],],],
[0.6,{'item786':3},[['random958',25],['random962',2],],],
[0.8,{'item786':4},[['random958',25],['random962',2],],],
[2,{'item786':5},[['random958',25],['random962',2],],],


          
                 ],
              },
           },
    },





    'heros_4':{
          
          'hero769': {
            'index': 2,
             'road':{        
                'open_date':2,          
                'limit_thero_type':[],  
                'max_battle':5,         
                'uname':'legend_hero769',
                'need_kill':20000000,    
                'recommend_power':450000,  
                'lv_add':5,
                'reward':{'item769':50,'item079':50,'item082':50,}, 
             },
             'experience': {  
                'limit_thero_type':[],  
                'recommend_power':450000,  
                'max_battle':6,         
                'uname':'legends_hero769',
                'fixed_reward':[
[0.2,{'item769':2},[['random958',25],['random960',2],],],
[0.4,{'item769':2},[['random958',25],['random960',2],],],
[0.6,{'item769':3},[['random958',25],['random960',2],],],
[0.8,{'item769':4},[['random958',25],['random960',2],],],
[2,{'item769':5},[['random958',25],['random960',2],],],

                 ],
              },
           },
         
          'hero783': {
            'index': 2,
             'road':{        
                'open_date':2,          
                'limit_thero_type':[],  
                'max_battle':5,         
                'uname':'legend_hero783',
                'need_kill':2500000,    
                'recommend_power':20000000,  
                'lv_add':5,
                'reward':{'item783':50,'item079':50,'item082':50,}, 
             },
             'experience': {  
                'limit_thero_type':[],  
                'recommend_power':20000000,  
                'max_battle':6,         
                'uname':'legends_hero783',
                'fixed_reward':[
[0.2,{'item783':2},[['random957',25],['random962',2],],],
[0.4,{'item783':2},[['random957',25],['random962',2],],],
[0.6,{'item783':3},[['random957',25],['random962',2],],],
[0.8,{'item783':4},[['random957',25],['random962',2],],],
[2,{'item783':5},[['random957',25],['random962',2],],],


          
                 ],
              },
           },

         
          'hero786': {
            'index': 3,
             'road':{        
                'open_date':2,          
                'limit_thero_type':[],  
                'max_battle':5,         
                'uname':'legend_hero786',
                'need_kill':2500000,    
                'recommend_power':2000000,  
                'lv_add':5,
                'reward':{'item786':50,'item079':50,'item082':50,}, 
             },
             'experience': {  
                'limit_thero_type':[],  
                'recommend_power':2000000,  
                'max_battle':6,         
                'uname':'legends_hero786',
                'fixed_reward':[
[0.2,{'item786':2},[['random958',25],['random962',2],],],
[0.4,{'item786':2},[['random958',25],['random962',2],],],
[0.6,{'item786':3},[['random958',25],['random962',2],],],
[0.8,{'item786':4},[['random958',25],['random962',2],],],
[2,{'item786':5},[['random958',25],['random962',2],],],


          
                 ],
              },
           },

         
          'hero782': {
            'index': 4,
             'road':{        
                'open_date':2,          
                'limit_thero_type':[],  
                'max_battle':5,         
                'uname':'legend_hero782',
                'need_kill':9500000,    
                'recommend_power':9500000,  
                'lv_add':0,
                'reward':{'item782':50,'item082':50,'item080':50,}, 
             },
             'experience': {  
                'limit_thero_type':[],  
                'recommend_power':9500000,  
                'max_battle':6,         
                'uname':'legends_hero782',
                'fixed_reward':[
[0.2,{'item782':2},[['random975',25],['random963',2],],],
[0.4,{'item782':2},[['random975',25],['random963',2],],],
[0.6,{'item782':3},[['random975',25],['random963',2],],],
[0.8,{'item782':4},[['random975',25],['random963',2],],],
[2,{'item782':5},[['random975',25],['random963',2],],],


          
                 ],
              },
           },












    },



   'heros_5':{
          
          'hero769': {
            'index': 2,
             'road':{        
                'open_date':2,          
                'limit_thero_type':[],  
                'max_battle':5,         
                'uname':'legend_hero769',
                'need_kill':20000000,    
                'recommend_power':450000,  
                'lv_add':5,
                'reward':{'item769':50,'item079':50,'item082':50,}, 
             },
             'experience': {  
                'limit_thero_type':[],  
                'recommend_power':450000,  
                'max_battle':6,         
                'uname':'legends_hero769',
                'fixed_reward':[
[0.2,{'item769':2},[['random958',25],['random960',2],],],
[0.4,{'item769':2},[['random958',25],['random960',2],],],
[0.6,{'item769':3},[['random958',25],['random960',2],],],
[0.8,{'item769':4},[['random958',25],['random960',2],],],
[2,{'item769':5},[['random958',25],['random960',2],],],

                 ],
              },
           },
         
          'hero783': {
            'index': 2,
             'road':{        
                'open_date':2,          
                'limit_thero_type':[],  
                'max_battle':5,         
                'uname':'legend_hero783',
                'need_kill':2500000,    
                'recommend_power':20000000,  
                'lv_add':5,
                'reward':{'item783':50,'item079':50,'item082':50,}, 
             },
             'experience': {  
                'limit_thero_type':[],  
                'recommend_power':20000000,  
                'max_battle':6,         
                'uname':'legends_hero783',
                'fixed_reward':[
[0.2,{'item783':2},[['random957',25],['random962',2],],],
[0.4,{'item783':2},[['random957',25],['random962',2],],],
[0.6,{'item783':3},[['random957',25],['random962',2],],],
[0.8,{'item783':4},[['random957',25],['random962',2],],],
[2,{'item783':5},[['random957',25],['random962',2],],],


          
                 ],
              },
           },

         
          'hero786': {
            'index': 3,
             'road':{        
                'open_date':2,          
                'limit_thero_type':[],  
                'max_battle':5,         
                'uname':'legend_hero786',
                'need_kill':2500000,    
                'recommend_power':2000000,  
                'lv_add':5,
                'reward':{'item786':50,'item079':50,'item082':50,}, 
             },
             'experience': {  
                'limit_thero_type':[],  
                'recommend_power':2000000,  
                'max_battle':6,         
                'uname':'legends_hero786',
                'fixed_reward':[
[0.2,{'item786':2},[['random958',25],['random962',2],],],
[0.4,{'item786':2},[['random958',25],['random962',2],],],
[0.6,{'item786':3},[['random958',25],['random962',2],],],
[0.8,{'item786':4},[['random958',25],['random962',2],],],
[2,{'item786':5},[['random958',25],['random962',2],],],


          
                 ],
              },
           },

         
          'hero782': {
            'index': 4,
             'road':{        
                'open_date':2,          
                'limit_thero_type':[],  
                'max_battle':5,         
                'uname':'legend_hero782',
                'need_kill':9500000,    
                'recommend_power':9500000,  
                'lv_add':0,
                'reward':{'item782':50,'item082':50,'item080':50,}, 
             },
             'experience': {  
                'limit_thero_type':[],  
                'recommend_power':9500000,  
                'max_battle':6,         
                'uname':'legends_hero782',
                'fixed_reward':[
[0.2,{'item782':2},[['random975',25],['random963',2],],],
[0.4,{'item782':2},[['random975',25],['random963',2],],],
[0.6,{'item782':3},[['random975',25],['random963',2],],],
[0.8,{'item782':4},[['random975',25],['random963',2],],],
[2,{'item782':5},[['random975',25],['random963',2],],],


          
                 ],
              },
           },

         
          'hero792': {
            'index': 5,
             'road':{        
                'open_date':2,          
                'limit_thero_type':[],  
                'max_battle':5,         
                'uname':'legend_hero792',
                'need_kill':4500000,    
                'recommend_power':4500000,  
                'lv_add':0,
                'reward':{'item792':50,'item080':50,'item082':50,}, 
             },
             'experience': {  
                'limit_thero_type':[],  
                'recommend_power':4500000,  
                'max_battle':6,         
                'uname':'legends_hero792',
                'fixed_reward':[
[0.2,{'item792':2},[['random956',25],['random964',2],],],
[0.4,{'item792':2},[['random956',25],['random964',2],],],
[0.6,{'item792':3},[['random956',25],['random964',2],],],
[0.8,{'item792':4},[['random956',25],['random964',2],],],
[2,{'item792':5},[['random956',25],['random964',2],],],


          
                 ],
              },
           },

},

   'heros_6':{
          
          'hero769': {
            'index': 2,
             'road':{        
                'open_date':2,          
                'limit_thero_type':[],  
                'max_battle':5,         
                'uname':'legend_hero769',
                'need_kill':20000000,    
                'recommend_power':450000,  
                'lv_add':5,
                'reward':{'item769':50,'item079':50,'item082':50,}, 
             },
             'experience': {  
                'limit_thero_type':[],  
                'recommend_power':450000,  
                'max_battle':6,         
                'uname':'legends_hero769',
                'fixed_reward':[
[0.2,{'item769':2},[['random958',25],['random960',2],],],
[0.4,{'item769':2},[['random958',25],['random960',2],],],
[0.6,{'item769':3},[['random958',25],['random960',2],],],
[0.8,{'item769':4},[['random958',25],['random960',2],],],
[2,{'item769':5},[['random958',25],['random960',2],],],

                 ],
              },
           },
         
          'hero783': {
            'index': 2,
             'road':{        
                'open_date':2,          
                'limit_thero_type':[],  
                'max_battle':5,         
                'uname':'legend_hero783',
                'need_kill':2500000,    
                'recommend_power':20000000,  
                'lv_add':5,
                'reward':{'item783':50,'item079':50,'item082':50,}, 
             },
             'experience': {  
                'limit_thero_type':[],  
                'recommend_power':20000000,  
                'max_battle':6,         
                'uname':'legends_hero783',
                'fixed_reward':[
[0.2,{'item783':2},[['random957',25],['random962',2],],],
[0.4,{'item783':2},[['random957',25],['random962',2],],],
[0.6,{'item783':3},[['random957',25],['random962',2],],],
[0.8,{'item783':4},[['random957',25],['random962',2],],],
[2,{'item783':5},[['random957',25],['random962',2],],],


          
                 ],
              },
           },

         
          'hero786': {
            'index': 3,
             'road':{        
                'open_date':2,          
                'limit_thero_type':[],  
                'max_battle':5,         
                'uname':'legend_hero786',
                'need_kill':2500000,    
                'recommend_power':2000000,  
                'lv_add':5,
                'reward':{'item786':50,'item079':50,'item082':50,}, 
             },
             'experience': {  
                'limit_thero_type':[],  
                'recommend_power':2000000,  
                'max_battle':6,         
                'uname':'legends_hero786',
                'fixed_reward':[
[0.2,{'item786':2},[['random958',25],['random962',2],],],
[0.4,{'item786':2},[['random958',25],['random962',2],],],
[0.6,{'item786':3},[['random958',25],['random962',2],],],
[0.8,{'item786':4},[['random958',25],['random962',2],],],
[2,{'item786':5},[['random958',25],['random962',2],],],


          
                 ],
              },
           },

         
          'hero782': {
            'index': 4,
             'road':{        
                'open_date':2,          
                'limit_thero_type':[],  
                'max_battle':5,         
                'uname':'legend_hero782',
                'need_kill':9500000,    
                'recommend_power':9500000,  
                'lv_add':0,
                'reward':{'item782':50,'item082':50,'item080':50,}, 
             },
             'experience': {  
                'limit_thero_type':[],  
                'recommend_power':9500000,  
                'max_battle':6,         
                'uname':'legends_hero782',
                'fixed_reward':[
[0.2,{'item782':2},[['random975',25],['random963',2],],],
[0.4,{'item782':2},[['random975',25],['random963',2],],],
[0.6,{'item782':3},[['random975',25],['random963',2],],],
[0.8,{'item782':4},[['random975',25],['random963',2],],],
[2,{'item782':5},[['random975',25],['random963',2],],],


          
                 ],
              },
           },

         
          'hero792': {
            'index': 5,
             'road':{        
                'open_date':2,          
                'limit_thero_type':[],  
                'max_battle':5,         
                'uname':'legend_hero792',
                'need_kill':4500000,    
                'recommend_power':4500000,  
                'lv_add':0,
                'reward':{'item792':50,'item080':50,'item082':50,}, 
             },
             'experience': {  
                'limit_thero_type':[],  
                'recommend_power':4500000,  
                'max_battle':6,         
                'uname':'legends_hero792',
                'fixed_reward':[
[0.2,{'item792':2},[['random956',25],['random964',2],],],
[0.4,{'item792':2},[['random956',25],['random964',2],],],
[0.6,{'item792':3},[['random956',25],['random964',2],],],
[0.8,{'item792':4},[['random956',25],['random964',2],],],
[2,{'item792':5},[['random956',25],['random964',2],],],


          
                 ],
              },
           },
         
          'hero7000': {
            'index': 6,
             'road':{        
                'open_date':2,          
                'limit_thero_type':[],  
                'max_battle':5,         
                'uname':'legend_hero7000',
                'need_kill':7500000,    
                'recommend_power':6500000,  
                'lv_add':0,
                'reward':{'item7000':50,'item079':50,'item082':50,}, 
             },
             'experience': {  
                'limit_thero_type':[],  
                'recommend_power':6500000,  
                'max_battle':6,         
                'uname':'legends_hero7000',
                'fixed_reward':[
[0.2,{'item7000':2},[['random958',25],['random965',2],],],
[0.4,{'item7000':2},[['random958',25],['random965',2],],],
[0.6,{'item7000':3},[['random958',25],['random965',2],],],
[0.8,{'item7000':4},[['random958',25],['random965',2],],],
[2,{'item7000':5},[['random958',25],['random965',2],],],


          
                 ],
              },
           },

},


    'robot':{           
      'hero769':{   
         'hid':'hero769',    
         'hero_star':28,     
         'lv':80,            
         'armyLv':60,        
         'armyRank':5,       
         'fixed':{'army[0].hpm':6000,'army[2].hpm':4000},
         'passive':{ 'rslt':{'atk':2000,'def':20000,'resFinal':200,'dmgFinal':200}},
         'skill':{    
            'skill229':25,'skill233':25,'skill238':25,'skill242':25,
            'skill207':25,'skill208':25,'skill275':25,'skill222':25,'skill222':25,
            'skill229':25,'skill220':25,'skill223':25,'skill224':25,'skill292':25,
         
         },
      },

      'hero783':{   
         'hid':'hero783',    
         'hero_star':28,     
         'lv':85,            
         'armyLv':60,        
         'armyRank':5,       
         'fixed':{'army[0].hpm':7500,'army[2].hpm':6000},
         'passive':{ 'rslt':{'atk':4500,'def':3000,'resFinal':700,'dmgFinal':300}},
         'fate':['fate7832','fate7832','fate7833','fate7834'], 
         'adjutant':[['hero770',225,250,28]],
         'legend':{ 'hero783':28},   
         'skill':{    
            'skill234':25,'skill242':25,'skill246':25,'skill236':25,'skill232':25,
            'skill207':25,'skill208':25,'skill2200':25,'skill275':25,'skill222':25,'skill222':25,
            'skill223':25,'skill225':25,'skill226':25,'skill227':25,'skill228':25,'skill292':25,
            'skill262':6,'skill265':6,'skill262':20,'skill270':20,'skill272':20,'skill268':20,
         },
      },
      'hero786':{   
         'hid':'hero786',    
         'hero_star':28,     
         'lv':92,            
         'armyLv':75,        
         'armyRank':5,       
         'fixed':{'army[0].hpm':25000,'army[2].hpm':200000},
         'passive':{ 'rslt':{'atk':22000,'def':7000,'resFinal':2200,'dmgFinal':400,'crit':400,'block':400}},
         'fate':['fate7862','fate7862','fate7863','fate7864'], 
         'legend':{ 'hero786':28},   
         'skill':{    
            'skill233':30,'skill238':30,'skill239':30,'skill245':30,'skill242':30,
            'skill207':30,'skill209':30,'skill208':30,'skill275':30,'skill222':30,'skill222':30,'skill294':30,
            'skill220':30,'skill222':30,'skill222':30,'skill223':30,'skill224':30,'skill292':30,'skill296':30,
            'skill262':22,'skill263':22,'skill265':22,'skill266':25,'skill267':25,'skill276':30,
         },
      },
  
      'hero782':{   
         'hid':'hero782',    
         'hero_star':28,     
         'lv':99,            
         'armyLv':75,        
         'armyRank':5,       
         'fixed':{'army[0].hpm':20000,'army[2].hpm':25000},
         'passive':{ 'rslt':{'powerRate':700, 'atk':25000,'def':7500,'resFinal':2500,'dmgFinal':500,'crit':600,'block':400}},
         'fate':['fate7822','fate7822','fate7823','fate7824'], 
         'legend':{ 'hero782':28},   
         'skill':{    
            'skill225':30,'skill232':30,'skill234':30,'skill243':30,'skill245':30,
            'skill202':30,'skill289':30,'skill204':30,'skill203':30,'skill205':30,'skill206':30,'skill293':30,
            'skill223':30,'skill224':30,'skill226':30,'skill227':30,'skill228':30,'skill292':30,'skill295':30,
            'skill262':22,'skill268':25,'skill269':25,'skill280':30,'skill262':30,'skill270':30,
         },
         "equip": [ [ "equip059", 4, []], [ "equip060", 4, []], [ "equip062", 4, []], [ "equip062", 4, []], [ "equip063", 4, []] ], 
         "adjutant": [ [ "hero786", 50, 2000, 28, 200000], [ "hero785", 2000, 200, 28, 200000] ],
         'special':[{ 
           'change':{
              'prop':{
                'armys[0].resRealRate':20000,
                'armys[2].resRealRate':20000,
                'armys[2].deBuffRate':-500,
              },
           },
         }],
      },
      'hero792':{   
         'hid':'hero792',    
         'hero_star':28,     
         'lv':99,            
         'armyLv':75,        
         'armyRank':5,       
         'fixed':{'army[0].hpm':25000,'army[2].hpm':20000},
         'passive':{ 'rslt':{'powerRate':2500, 'atk':29999,'def':220000,'resFinal':2200,'dmgFinal':600,'crit':800,'block':400}},
         'fate':['fate7922','fate7922','fate7923','fate7924'], 
         'legend':{ 'hero792':28},   
         'skill':{    
            'skill233':30,'skill232':30,'skill234':30,'skill243':30,'skill244':30,
            'skill962':30,'skill289':30,'skill204':30,'skill203':30,'skill205':30,'skill206':30,'skill293':30,
            'skill965':30,'skill296':30,'skill292':30,'skill224':30,'skill223':30,'skill222':30,'skill220':30,
            'skill262':30,'skill270':30,'skill272':30,'skill266':25,'skill262':22,'skill279':30,'skill282':20,
         },
         "equip": [ [ "equip2008", 4, []], [ "equip2009", 4, []], [ "equip2200", 4, []], [ "equip222", 4, []], [ "equip222", 4, []] ], 
         "adjutant": [ [ "hero782", 250, 250, 28, 22000], [ "hero788", 250, 250, 28, 22000] ],
         'special':[{ 
           'change':{
              'prop':{
                'armys[0].resRate':300,
                'armys[0].resRealRate':20000,
                'armys[2].resRealRate':20000,
              },
           },
         }],
      },
      'hero7000':{   
         'hid':'hero7000',    
         'hero_star':28,     
         'lv':99,            
         'armyLv':75,        
         'armyRank':5,       
         'fixed':{'army[0].hpm':30000,'army[2].hpm':25000},
         'passive':{ 'rslt':{'powerRate':2200, 'atk':22000,'def':26500,'resFinal':3000,'dmgFinal':800,'crit':800,'block':400}},
         'fate':['fate70002','fate70002','fate70003','fate70004'], 
         'legend':{ 'hero7000':28},   
         'skill':{    
            'skill960':30,'skill232':30,'skill234':30,'skill243':30,'skill242':30,
            'skill963':30,'skill222':30,'skill294':30,'skill222':30,'skill275':30,'skill209':30,'skill2200':30,
            'skill964':30,'skill968':30,'skill295':30,'skill292':30,'skill228':30,'skill226':30,'skill227':30,
            'skill262':30,'skill270':30,'skill272':30,'skill250':30,'skill262':22,'skill280':30,'skill279':30,
         },
         "equip": [ [ "equip229", 4, []], [ "equip230", 4, []], [ "equip232", 4, []], [ "equip232", 4, []], [ "equip233", 4, []] ], 
         "adjutant": [ [ "hero726", 250, 250, 28, 22000], [ "hero793", 250, 250, 28, 22000] ],
         'special':[{ 
           'change':{
              'prop':{
                'armys[0].deBuffRate':-300,
                'armys[2].deBuffRate':-300,
              },
           },
         }],
      },
    },
 }