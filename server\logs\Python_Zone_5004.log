[I 250729 01:10:56 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:10:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:10:56 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:10:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.11ms
[I 250729 01:10:56 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:10:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.88ms
[I 250729 01:10:56 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:10:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 319.17ms
[I 250729 01:11:07 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:11:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.61ms
[I 250729 01:11:07 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:11:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.11ms
[I 250729 01:11:07 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:11:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.94ms
[I 250729 01:11:07 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:11:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 317.30ms
[I 250729 01:11:18 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:11:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:11:18 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:11:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.59ms
[I 250729 01:11:18 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:11:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.06ms
[I 250729 01:11:18 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:11:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 329.98ms
[I 250729 01:11:29 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:11:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.61ms
[I 250729 01:11:29 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:11:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.13ms
[I 250729 01:11:29 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:11:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.96ms
[I 250729 01:11:29 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:11:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 314.75ms
[I 250729 01:11:40 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:11:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.65ms
[I 250729 01:11:40 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:11:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.13ms
[I 250729 01:11:40 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:11:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.03ms
[I 250729 01:11:40 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:11:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 313.80ms
[I 250729 01:11:50 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:11:50 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.63ms
[I 250729 01:11:51 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:11:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.15ms
[I 250729 01:11:51 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:11:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.01ms
[I 250729 01:11:51 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:11:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 312.96ms
[I 250729 01:12:01 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:12:01 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:12:01 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:12:01 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.11ms
[I 250729 01:12:01 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:12:01 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.98ms
[I 250729 01:12:01 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:12:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 317.48ms
[I 250729 01:12:12 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:12:12 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:12:12 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:12:12 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.09ms
[I 250729 01:12:12 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:12:12 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.93ms
[I 250729 01:12:12 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:12:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 317.80ms
[I 250729 01:12:23 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:12:23 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:12:23 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:12:23 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.10ms
[I 250729 01:12:23 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:12:23 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.93ms
[I 250729 01:12:23 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:12:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 319.18ms
[I 250729 01:12:34 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:12:34 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:12:34 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:12:34 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.15ms
[I 250729 01:12:34 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:12:34 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.90ms
[I 250729 01:12:34 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:12:34 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 315.29ms
[I 250729 01:12:45 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:12:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.67ms
[I 250729 01:12:45 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:12:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.10ms
[I 250729 01:12:45 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:12:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.06ms
[I 250729 01:12:45 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:12:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 322.58ms
[I 250729 01:12:56 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:12:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:12:56 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:12:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.12ms
[I 250729 01:12:56 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:12:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.99ms
[I 250729 01:12:56 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:12:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 317.65ms
[I 250729 01:13:07 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:13:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.60ms
[I 250729 01:13:07 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:13:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.24ms
[I 250729 01:13:07 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:13:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.98ms
[I 250729 01:13:07 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:13:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 311.94ms
[I 250729 01:13:18 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:13:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.61ms
[I 250729 01:13:18 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:13:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.10ms
[I 250729 01:13:18 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:13:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.96ms
[I 250729 01:13:18 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:13:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 319.97ms
[I 250729 01:13:29 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:13:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:13:29 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:13:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.56ms
[I 250729 01:13:29 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:13:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.05ms
[I 250729 01:13:29 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:13:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 312.91ms
[I 250729 01:13:40 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:13:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:13:40 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:13:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.15ms
[I 250729 01:13:40 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:13:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.99ms
[I 250729 01:13:40 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:13:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 314.32ms
[I 250729 01:13:50 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:13:50 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.58ms
[I 250729 01:13:50 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:13:50 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.11ms
[I 250729 01:13:50 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:13:50 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.96ms
[I 250729 01:13:50 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:13:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 314.85ms
[I 250729 01:14:01 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:14:01 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.61ms
[I 250729 01:14:01 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:14:01 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.17ms
[I 250729 01:14:01 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:14:01 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.98ms
[I 250729 01:14:01 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:14:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 320.46ms
[I 250729 01:14:12 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:14:12 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:14:12 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:14:12 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.19ms
[I 250729 01:14:12 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:14:12 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.96ms
[I 250729 01:14:12 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:14:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 312.45ms
[I 250729 01:14:23 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:14:23 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.65ms
[I 250729 01:14:23 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:14:23 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.10ms
[I 250729 01:14:23 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:14:23 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.97ms
[I 250729 01:14:23 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:14:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 319.90ms
[I 250729 01:14:34 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:14:34 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:14:34 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:14:34 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.11ms
[I 250729 01:14:34 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:14:34 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.04ms
[I 250729 01:14:34 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:14:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 317.82ms
[I 250729 01:14:45 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:14:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.63ms
[I 250729 01:14:45 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:14:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.11ms
[I 250729 01:14:45 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:14:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.95ms
[I 250729 01:14:45 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:14:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 314.31ms
[I 250729 01:14:56 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:14:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.59ms
[I 250729 01:14:56 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:14:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.12ms
[I 250729 01:14:56 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:14:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.93ms
[I 250729 01:14:56 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:14:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 313.81ms
[I 250729 01:15:07 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:15:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.61ms
[I 250729 01:15:07 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:15:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.17ms
[I 250729 01:15:07 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:15:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.95ms
[I 250729 01:15:07 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:15:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 330.20ms
[I 250729 01:15:18 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:15:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:15:18 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:15:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.11ms
[I 250729 01:15:18 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:15:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.06ms
[I 250729 01:15:18 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:15:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 314.60ms
[I 250729 01:15:29 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:15:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.61ms
[I 250729 01:15:29 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:15:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.59ms
[I 250729 01:15:29 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:15:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.04ms
[I 250729 01:15:29 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:15:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 332.47ms
[I 250729 01:15:40 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:15:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.60ms
[I 250729 01:15:40 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:15:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.16ms
[I 250729 01:15:40 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:15:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.94ms
[I 250729 01:15:40 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:15:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 312.41ms
[I 250729 01:15:51 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:15:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:15:51 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:15:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.11ms
[I 250729 01:15:51 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:15:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.91ms
[I 250729 01:15:51 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:15:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 318.85ms
[I 250729 01:16:02 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:16:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.63ms
[I 250729 01:16:02 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:16:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.33ms
[I 250729 01:16:02 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:16:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.02ms
[I 250729 01:16:02 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:16:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 328.41ms
[I 250729 01:16:13 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:16:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.63ms
[I 250729 01:16:13 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:16:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.19ms
[I 250729 01:16:13 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:16:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.06ms
[I 250729 01:16:13 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:16:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 329.63ms
[I 250729 01:16:24 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:16:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:16:24 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:16:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.13ms
[I 250729 01:16:24 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:16:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.33ms
[I 250729 01:16:24 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:16:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 315.73ms
[I 250729 01:16:35 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:16:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.61ms
[I 250729 01:16:35 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:16:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.25ms
[I 250729 01:16:35 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:16:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.11ms
[I 250729 01:16:35 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:16:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 323.17ms
[I 250729 01:16:45 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:16:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.61ms
[I 250729 01:16:45 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:16:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.13ms
[I 250729 01:16:45 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:16:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.94ms
[I 250729 01:16:45 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:16:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 325.65ms
[I 250729 01:16:56 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:16:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:16:56 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:16:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.15ms
[I 250729 01:16:56 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:16:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.97ms
[I 250729 01:16:56 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:16:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 315.91ms
[I 250729 01:17:07 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:17:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.61ms
[I 250729 01:17:07 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:17:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.13ms
[I 250729 01:17:07 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:17:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.90ms
[I 250729 01:17:07 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:17:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 319.68ms
[I 250729 01:17:18 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:17:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.63ms
[I 250729 01:17:18 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:17:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.12ms
[I 250729 01:17:18 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:17:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.89ms
[I 250729 01:17:18 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:17:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 320.30ms
[I 250729 01:17:29 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:17:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:17:29 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:17:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.49ms
[I 250729 01:17:29 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:17:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.02ms
[I 250729 01:17:29 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:17:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 314.89ms
[I 250729 01:17:40 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:17:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.60ms
[I 250729 01:17:40 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:17:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.17ms
[I 250729 01:17:40 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:17:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.90ms
[I 250729 01:17:40 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:17:41 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 321.55ms
[I 250729 01:17:51 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:17:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:17:51 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:17:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.12ms
[I 250729 01:17:51 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:17:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.94ms
[I 250729 01:17:51 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:17:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 323.13ms
[I 250729 01:18:02 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:18:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.65ms
[I 250729 01:18:02 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:18:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.14ms
[I 250729 01:18:02 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:18:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.02ms
[I 250729 01:18:02 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:18:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 323.80ms
[I 250729 01:18:13 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:18:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.65ms
[I 250729 01:18:13 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:18:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.17ms
[I 250729 01:18:13 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:18:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.99ms
[I 250729 01:18:13 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:18:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 318.82ms
[I 250729 01:18:24 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:18:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.58ms
[I 250729 01:18:24 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:18:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.34ms
[I 250729 01:18:24 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:18:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.05ms
[I 250729 01:18:24 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:18:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 316.92ms
[I 250729 01:18:35 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:18:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:18:35 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:18:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.20ms
[I 250729 01:18:35 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:18:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.97ms
[I 250729 01:18:35 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:18:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 323.27ms
[I 250729 01:18:46 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:18:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.65ms
[I 250729 01:18:46 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:18:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.22ms
[I 250729 01:18:46 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:18:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.05ms
[I 250729 01:18:46 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:18:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 324.44ms
[I 250729 01:18:57 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:18:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:18:57 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:18:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.24ms
[I 250729 01:18:57 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:18:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.02ms
[I 250729 01:18:57 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:18:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 313.07ms
[I 250729 01:19:07 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:19:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:19:07 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:19:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.15ms
[I 250729 01:19:07 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:19:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.01ms
[I 250729 01:19:07 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:19:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 321.07ms
[I 250729 01:19:18 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:19:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:19:18 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:19:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.21ms
[I 250729 01:19:18 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:19:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.05ms
[I 250729 01:19:18 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:19:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 313.52ms
[I 250729 01:19:29 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:19:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.61ms
[I 250729 01:19:29 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:19:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.47ms
[I 250729 01:19:29 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:19:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.99ms
[I 250729 01:19:29 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:19:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 319.25ms
[I 250729 01:19:40 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:19:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.61ms
[I 250729 01:19:40 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:19:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.17ms
[I 250729 01:19:40 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:19:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.96ms
[I 250729 01:19:40 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:19:41 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 311.84ms
[I 250729 01:19:51 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:19:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.59ms
[I 250729 01:19:51 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:19:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.09ms
[I 250729 01:19:51 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:19:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.99ms
[I 250729 01:19:51 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:19:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 319.31ms
[I 250729 01:20:02 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:20:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:20:02 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:20:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.21ms
[I 250729 01:20:02 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:20:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.98ms
[I 250729 01:20:02 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:20:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 315.00ms
[I 250729 01:20:13 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:20:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.92ms
[I 250729 01:20:13 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:20:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.40ms
[I 250729 01:20:13 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:20:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.92ms
[I 250729 01:20:13 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:20:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 315.51ms
[I 250729 01:20:24 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:20:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.61ms
[I 250729 01:20:24 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:20:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.13ms
[I 250729 01:20:24 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:20:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.97ms
[I 250729 01:20:24 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:20:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 320.97ms
[I 250729 01:20:35 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:20:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:20:35 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:20:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.08ms
[I 250729 01:20:35 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:20:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.92ms
[I 250729 01:20:35 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:20:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 323.19ms
[I 250729 01:20:46 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:20:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:20:46 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:20:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.17ms
[I 250729 01:20:46 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:20:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.04ms
[I 250729 01:20:46 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:20:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 317.73ms
[I 250729 01:20:56 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:20:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.65ms
[I 250729 01:20:57 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:20:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.14ms
[I 250729 01:20:57 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:20:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.92ms
[I 250729 01:20:57 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:20:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 317.77ms
[I 250729 01:21:07 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:21:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:21:07 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:21:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.15ms
[I 250729 01:21:07 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:21:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.02ms
[I 250729 01:21:07 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:21:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 316.64ms
[I 250729 01:21:18 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:21:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:21:18 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:21:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.15ms
[I 250729 01:21:18 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:21:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.96ms
[I 250729 01:21:18 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:21:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 314.02ms
[I 250729 01:21:29 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:21:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.61ms
[I 250729 01:21:29 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:21:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.12ms
[I 250729 01:21:29 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:21:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.96ms
[I 250729 01:21:29 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:21:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 320.74ms
[I 250729 01:21:40 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:21:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.63ms
[I 250729 01:21:40 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:21:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.49ms
[I 250729 01:21:40 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:21:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.96ms
[I 250729 01:21:40 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:21:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 310.83ms
[I 250729 01:21:51 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:21:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.58ms
[I 250729 01:21:51 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:21:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.14ms
[I 250729 01:21:51 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:21:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.94ms
[I 250729 01:21:51 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:21:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 313.07ms
[I 250729 01:22:02 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:22:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.61ms
[I 250729 01:22:02 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:22:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.13ms
[I 250729 01:22:02 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:22:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.95ms
[I 250729 01:22:02 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:22:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 313.88ms
[I 250729 01:22:13 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:22:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.63ms
[I 250729 01:22:13 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:22:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.13ms
[I 250729 01:22:13 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:22:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.94ms
[I 250729 01:22:13 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:22:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 322.59ms
[I 250729 01:22:24 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:22:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:22:24 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:22:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.13ms
[I 250729 01:22:24 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:22:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.97ms
[I 250729 01:22:24 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:22:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 315.40ms
[I 250729 01:22:35 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:22:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:22:35 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:22:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.16ms
[I 250729 01:22:35 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:22:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.92ms
[I 250729 01:22:35 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:22:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 319.41ms
[I 250729 01:22:45 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:22:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.60ms
[I 250729 01:22:45 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:22:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.12ms
[I 250729 01:22:45 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:22:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.01ms
[I 250729 01:22:45 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:22:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 322.14ms
[I 250729 01:22:56 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:22:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.63ms
[I 250729 01:22:56 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:22:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.15ms
[I 250729 01:22:56 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:22:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.00ms
[I 250729 01:22:56 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:22:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 315.54ms
[I 250729 01:23:07 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:23:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.61ms
[I 250729 01:23:07 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:23:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.15ms
[I 250729 01:23:07 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:23:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.97ms
[I 250729 01:23:07 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:23:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 312.79ms
[I 250729 01:23:18 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:23:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.61ms
[I 250729 01:23:18 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:23:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.15ms
[I 250729 01:23:18 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:23:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.95ms
[I 250729 01:23:18 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:23:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 315.07ms
[I 250729 01:23:29 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:23:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.61ms
[I 250729 01:23:29 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:23:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.17ms
[I 250729 01:23:29 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:23:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.00ms
[I 250729 01:23:29 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:23:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 319.40ms
[I 250729 01:23:40 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:23:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.57ms
[I 250729 01:23:40 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:23:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.13ms
[I 250729 01:23:40 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:23:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.91ms
[I 250729 01:23:40 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:23:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 314.09ms
[I 250729 01:23:51 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:23:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:23:51 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:23:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.52ms
[I 250729 01:23:51 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:23:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.96ms
[I 250729 01:23:51 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:23:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 319.97ms
[I 250729 01:24:02 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:24:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:24:02 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:24:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.12ms
[I 250729 01:24:02 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:24:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.95ms
[I 250729 01:24:02 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:24:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 319.90ms
[I 250729 01:24:13 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:24:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.63ms
[I 250729 01:24:13 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:24:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.15ms
[I 250729 01:24:13 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:24:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.95ms
[I 250729 01:24:13 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:24:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 317.77ms
[I 250729 01:24:24 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:24:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:24:24 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:24:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.25ms
[I 250729 01:24:24 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:24:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.97ms
[I 250729 01:24:24 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:24:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 320.51ms
[I 250729 01:24:35 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:24:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.63ms
[I 250729 01:24:35 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:24:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.16ms
[I 250729 01:24:35 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:24:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.94ms
[I 250729 01:24:35 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:24:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 318.84ms
[I 250729 01:24:45 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:24:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:24:45 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:24:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.14ms
[I 250729 01:24:45 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:24:45 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.96ms
[I 250729 01:24:45 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:24:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 317.29ms
[I 250729 01:24:56 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:24:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.61ms
[I 250729 01:24:56 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:24:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.14ms
[I 250729 01:24:56 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:24:56 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.95ms
[I 250729 01:24:56 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:24:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 313.24ms
[I 250729 01:25:07 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:25:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:25:07 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:25:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.08ms
[I 250729 01:25:07 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:25:07 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.96ms
[I 250729 01:25:07 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:25:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 318.87ms
[I 250729 01:25:18 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:25:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.65ms
[I 250729 01:25:18 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:25:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.11ms
[I 250729 01:25:18 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:25:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.91ms
[I 250729 01:25:18 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:25:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 316.48ms
[I 250729 01:25:29 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:25:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:25:29 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:25:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.14ms
[I 250729 01:25:29 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:25:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.94ms
[I 250729 01:25:29 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:25:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 317.45ms
[I 250729 01:25:40 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:25:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.63ms
[I 250729 01:25:40 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:25:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.13ms
[I 250729 01:25:40 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:25:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.94ms
[I 250729 01:25:40 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:25:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 312.60ms
[I 250729 01:25:51 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:25:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.59ms
[I 250729 01:25:51 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:25:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.62ms
[I 250729 01:25:51 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:25:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.04ms
[I 250729 01:25:51 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:25:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 328.69ms
[I 250729 01:26:02 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:26:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:26:02 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:26:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.60ms
[I 250729 01:26:02 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:26:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.02ms
[I 250729 01:26:02 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:26:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 312.86ms
[I 250729 01:26:13 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:26:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.61ms
[I 250729 01:26:13 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:26:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 12.22ms
[I 250729 01:26:13 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:26:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.99ms
[I 250729 01:26:13 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:26:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 333.70ms
[I 250729 01:26:24 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:26:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.66ms
[I 250729 01:26:24 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:26:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.26ms
[I 250729 01:26:24 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:26:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.00ms
[I 250729 01:26:24 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:26:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 335.99ms
[I 250729 01:26:35 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:26:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.60ms
[I 250729 01:26:35 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:26:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.19ms
[I 250729 01:26:35 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:26:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.89ms
[I 250729 01:26:35 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:26:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 327.51ms
[I 250729 01:26:46 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:26:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.60ms
[I 250729 01:26:46 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:26:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.16ms
[I 250729 01:26:46 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:26:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.92ms
[I 250729 01:26:46 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:26:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 319.25ms
[I 250729 01:26:57 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:26:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.61ms
[I 250729 01:26:57 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:26:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.12ms
[I 250729 01:26:57 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:26:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.97ms
[I 250729 01:26:57 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:26:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 328.20ms
[I 250729 01:27:08 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:27:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.61ms
[I 250729 01:27:08 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:27:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.19ms
[I 250729 01:27:08 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:27:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.01ms
[I 250729 01:27:08 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:27:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 317.96ms
[I 250729 01:27:18 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:27:18 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:27:19 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:27:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.15ms
[I 250729 01:27:19 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:27:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.97ms
[I 250729 01:27:19 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:27:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 327.68ms
[I 250729 01:27:29 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:27:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:27:29 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:27:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.06ms
[I 250729 01:27:29 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:27:29 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.99ms
[I 250729 01:27:29 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:27:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 314.63ms
[I 250729 01:27:40 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:27:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.61ms
[I 250729 01:27:40 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:27:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.11ms
[I 250729 01:27:40 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:27:40 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.92ms
[I 250729 01:27:40 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:27:41 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 314.70ms
[I 250729 01:27:51 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:27:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.59ms
[I 250729 01:27:51 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:27:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.49ms
[I 250729 01:27:51 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:27:51 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.96ms
[I 250729 01:27:51 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:27:52 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 314.47ms
[I 250729 01:28:02 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:28:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:28:02 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:28:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.21ms
[I 250729 01:28:02 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:28:02 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.97ms
[I 250729 01:28:02 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:28:03 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 318.71ms
[I 250729 01:28:13 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:28:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:28:13 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:28:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.11ms
[I 250729 01:28:13 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:28:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.94ms
[I 250729 01:28:13 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:28:14 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 317.78ms
[I 250729 01:28:24 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:28:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.66ms
[I 250729 01:28:24 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:28:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.18ms
[I 250729 01:28:24 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:28:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.93ms
[I 250729 01:28:24 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:28:24 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 320.06ms
[I 250729 01:28:35 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:28:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.60ms
[I 250729 01:28:35 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:28:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.11ms
[I 250729 01:28:35 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:28:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.00ms
[I 250729 01:28:35 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:28:35 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 319.02ms
[I 250729 01:28:46 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:28:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:28:46 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:28:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.12ms
[I 250729 01:28:46 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:28:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.99ms
[I 250729 01:28:46 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:28:46 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 316.94ms
[I 250729 01:28:57 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:28:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:28:57 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:28:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.15ms
[I 250729 01:28:57 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:28:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.94ms
[I 250729 01:28:57 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:28:57 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 325.22ms
[I 250729 01:29:08 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:29:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.58ms
[I 250729 01:29:08 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:29:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.22ms
[I 250729 01:29:08 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:29:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.02ms
[I 250729 01:29:08 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:29:08 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 320.94ms
[I 250729 01:29:19 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:29:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.63ms
[I 250729 01:29:19 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:29:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.16ms
[I 250729 01:29:19 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:29:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.02ms
[I 250729 01:29:19 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:29:19 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 320.66ms
[I 250729 01:29:30 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:29:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.60ms
[I 250729 01:29:30 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:29:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.14ms
[I 250729 01:29:30 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:29:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.97ms
[I 250729 01:29:30 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:29:30 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 315.92ms
[I 250729 01:29:41 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:29:41 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.63ms
[I 250729 01:29:41 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:29:41 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.13ms
[I 250729 01:29:41 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:29:41 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.93ms
[I 250729 01:29:41 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:29:41 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 317.29ms
[I 250729 01:29:52 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:29:52 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:29:52 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:29:52 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.49ms
[I 250729 01:29:52 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:29:52 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.98ms
[I 250729 01:29:52 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:29:52 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 316.10ms
[I 250729 01:30:03 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:30:03 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.64ms
[I 250729 01:30:03 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:30:03 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.17ms
[I 250729 01:30:03 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:30:03 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.94ms
[I 250729 01:30:03 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:30:03 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 319.52ms
[I 250729 01:30:13 server:41902] api call: 118.178.139.38, get_server_status, {}
[I 250729 01:30:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.62ms
[I 250729 01:30:13 server:41902] api call: 118.178.139.38, backup_user, {'if_honour_done': 0, 'if_all': 0}
[I 250729 01:30:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 1.12ms
[I 250729 01:30:13 server:41902] api call: 118.178.139.38, backup_fight_log, {'if_all': 0}
[I 250729 01:30:13 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 0.99ms
[I 250729 01:30:13 server:41902] api call: 118.178.139.38, backup_world, {}
[I 250729 01:30:14 web:2162] 200 POST /api/?pwd=sdljflasjdfldasjflk (118.178.139.38) 315.56ms
