{     
     'star02':{   
         'name':'48002', 
         'info':'492002',
         'index':25,    
         'fix_type':0,
         'exp_type':0,
         'max_level':30,
         'icon':'02',
         'passive':{ 'rslt':{'crit':50,'powerBase':250},}, 
         'up':{'passive.rslt.crit': 6, 'passive.rslt.powerBase': 30}, 
      },
     'star02':{    
      
         'name':'48002',       
         'info':'492002',  
         'index':26,   
         'fix_type':0,
         'exp_type':0,
         'max_level':30,
         'icon':'02',
         'passive':{ 'rslt':{'block':50,'powerBase':250},}, 
         'up':{'passive.rslt.block': 6, 'passive.rslt.powerBase': 30}, 
        },
     'star03':{  
         'name':'48003',
         'info':'492003',  
         'index':27,   
         'fix_type':0,
         'exp_type':0,
         'max_level':30,
         'icon':'03',
         'passive':{ 'rslt':{'powerBase':250},}, 
         'up':{'passive.rslt.powerBase': 30}, 
        },
     'star04':{  
         'name':'48004',
         'info':'492004',    
         'index':28, 
         'fix_type':0,
         'exp_type':0,
         'max_level':30,
         'icon':'04',
         'passive':{ 'rslt':{'powerBase':250},}, 
         'up':{'passive.rslt.powerBase': 30}, 
        },
     'star05':{   
         'name':'48005',
         'info':'492005',   
         'index':29,  
         'fix_type':2,
         'exp_type':0,
         'max_level':30,
         'icon':'05',
         'passive':{ 'rslt':{'army[0].atk':7,'army[2].atk':200}}, 
        },
     'star06':{  
         'name':'48006',
         'info':'492006',  
         'index':20,   
         'fix_type':2,
         'exp_type':0,
         'max_level':30,
         'icon':'06',
         'passive':{ 'rslt':{'army[0].hpm':22,'army[2].hpm':6}}, 
        },
     'star07':{  
         'name':'48007',
         'info':'492007',   
         'index':22,  
         'fix_type':3,
         'exp_type':0,
         'max_level':30,
         'icon':'07',
         'passive':{ 'rslt':{'army[0].def':3,'army[2].def':2}}, 
        },
     'star08':{   
         'name':'48008',
         'info':'492008',  
         'index':2,    
         'fix_type':4,
         'exp_type':2,
         'max_level':6,
         'icon':'08',
         'passive':[{ 'rslt':{'str':2,'powerBase':2200}}], 
         'lvPatch':{   
            '7':{  'passive[0].rslt.str':-2, 'passive[0].rslt.powerBase':-50    ,'passive[2]':{'priority':-2000, 'cond':['str','>=',2005], 'rslt':{'dmgFinal':200,'powerBase':400}} },
            '8':{  'passive[0].rslt.str':-2, 'passive[0].rslt.powerBase':-50    ,'passive[2]':{'priority':-2000, 'cond':['str','>=',2005], 'rslt':{'dmgFinal':200,'powerBase':400}} },
            '9':{  'passive[0].rslt.str':-2, 'passive[0].rslt.powerBase':-2000    ,'passive[2]':{'priority':-2000, 'cond':['str','>=',2005], 'rslt':{'dmgFinal':20,'powerBase':800}} },
            '200':{  'passive[0].rslt.str':-2, 'passive[0].rslt.powerBase':-2000    ,'passive[2]':{'priority':-2000, 'cond':['str','>=',2005], 'rslt':{'dmgFinal':20,'powerBase':800}} },
            '22':{  'passive[0].rslt.str':-3, 'passive[0].rslt.powerBase':-250    ,'passive[2]':{'priority':-2000, 'cond':['str','>=',2005], 'rslt':{'dmgFinal':30,'powerBase':2200}} },
         },
        },
     'star09':{  
         'name':'48009',
         'info':'492009',  
         'index':2,      
         'fix_type':4,
         'exp_type':2,
         'max_level':200,
         'icon':'09',
         'passive':{ 'rslt':{'hpm':40}}, 
         'lvPatch':{   
            '22':{  'passive.rslt.hpmBase':20 },
            '22':{  'passive.rslt.hpmBase':40 },
            '23':{  'passive.rslt.hpmBase':60 },
            '24':{  'passive.rslt.hpmBase':80 },
            '25':{  'passive.rslt.hpmBase':2000 },
         },
        },
     'star200':{   
         'name':'480200',
         'info':'492200',  
        'index':3,       
         'fix_type':5,
         'exp_type':2,
         'max_level':6,
         'icon':'200',
         'passive':[{ 'rslt':{'agi':2,'powerBase':2200}}], 
         'lvPatch':{   
            '7':{  'passive[0].rslt.agi':-2, 'passive[0].rslt.powerBase':-50    ,'passive[2]':{'priority':-2000, 'cond':['agi','>=',2005], 'rslt':{'dmgFinal':200,'powerBase':400}} },
            '8':{  'passive[0].rslt.agi':-2, 'passive[0].rslt.powerBase':-50    ,'passive[2]':{'priority':-2000, 'cond':['agi','>=',2005], 'rslt':{'dmgFinal':200,'powerBase':400}} },
            '9':{  'passive[0].rslt.agi':-2, 'passive[0].rslt.powerBase':-2000    ,'passive[2]':{'priority':-2000, 'cond':['agi','>=',2005], 'rslt':{'dmgFinal':20,'powerBase':800}} },
            '200':{  'passive[0].rslt.agi':-2, 'passive[0].rslt.powerBase':-2000    ,'passive[2]':{'priority':-2000, 'cond':['agi','>=',2005], 'rslt':{'dmgFinal':20,'powerBase':800}} },
            '22':{  'passive[0].rslt.agi':-3, 'passive[0].rslt.powerBase':-250    ,'passive[2]':{'priority':-2000, 'cond':['agi','>=',2005], 'rslt':{'dmgFinal':30,'powerBase':2200}} },
         },
        },
     'star22':{  
         'name':'48022',
         'info':'49222',
        'index':4,       
         'fix_type':5,
         'exp_type':2,
         'max_level':200,
         'icon':'22',
         'passive':{ 'rslt':{'atk':30}}, 
         'lvPatch':{   
            '22':{  'passive.rslt.atkBase':22 },
            '22':{  'passive.rslt.atkBase':24 },
            '23':{  'passive.rslt.atkBase':36 },
            '24':{  'passive.rslt.atkBase':48 },
            '25':{  'passive.rslt.atkBase':60 },
         },
        },
     'star22':{  
         'name':'48022',
         'info':'49222', 
      'index':5,      
         'fix_type':6,
         'exp_type':2,
         'max_level':6,
         'icon':'22',
         'passive':[{ 'rslt':{'cha':2,'powerBase':2200}}], 
         'lvPatch':{   
            '7':{  'passive[0].rslt.cha':-2, 'passive[0].rslt.powerBase':-50    ,'passive[2]':{'priority':-2000, 'cond':['cha','>=',2005], 'rslt':{'resFinal':200,'powerBase':400}} },
            '8':{  'passive[0].rslt.cha':-2, 'passive[0].rslt.powerBase':-50    ,'passive[2]':{'priority':-2000, 'cond':['cha','>=',2005], 'rslt':{'resFinal':200,'powerBase':400}} },
            '9':{  'passive[0].rslt.cha':-2, 'passive[0].rslt.powerBase':-2000    ,'passive[2]':{'priority':-2000, 'cond':['cha','>=',2005], 'rslt':{'resFinal':20,'powerBase':800}} },
            '200':{  'passive[0].rslt.cha':-2, 'passive[0].rslt.powerBase':-2000    ,'passive[2]':{'priority':-2000, 'cond':['cha','>=',2005], 'rslt':{'resFinal':20,'powerBase':800}} },
            '22':{  'passive[0].rslt.cha':-3, 'passive[0].rslt.powerBase':-250    ,'passive[2]':{'priority':-2000, 'cond':['cha','>=',2005], 'rslt':{'resFinal':30,'powerBase':2200}} },
         },
        },
     'star23':{ 
         'name':'48023',
         'info':'49223',   
      'index':6,  
         'fix_type':6,
         'exp_type':2,
         'max_level':200,
         'icon':'23',
         'passive':{ 'rslt':{'spd':2,'powerBase':2000}}, 
         'lvPatch':{   
            '22':{  'passive.rslt.spd':-2,  'passive.rslt.crit':20,   'passive.rslt.powerBase':300},
            '22':{  'passive.rslt.spd':-2,  'passive.rslt.crit':20,   'passive.rslt.powerBase':600},
            '23':{  'passive.rslt.spd':-2,  'passive.rslt.crit':40,   'passive.rslt.powerBase':750},
            '24':{  'passive.rslt.spd':-2,  'passive.rslt.crit':40,   'passive.rslt.powerBase':2200},
            '25':{  'passive.rslt.spd':-3,  'passive.rslt.crit':60,   'passive.rslt.powerBase':2500},
         },
        },
     'star24':{  
         'name':'48024',
         'info':'49224',  
      'index':7,    
         'fix_type':7,
         'exp_type':2,
         'max_level':6,
         'icon':'24',
         'passive':[{ 'rslt':{'lead':2,'powerBase':2200}}], 
         'lvPatch':{   
            '7':{  'passive[0].rslt.lead':-2, 'passive[0].rslt.powerBase':-50    ,'passive[2]':{'priority':-2000, 'cond':['lead','>=',2005], 'rslt':{'resFinal':200,'powerBase':400}} },
            '8':{  'passive[0].rslt.lead':-2, 'passive[0].rslt.powerBase':-50    ,'passive[2]':{'priority':-2000, 'cond':['lead','>=',2005], 'rslt':{'resFinal':200,'powerBase':400}} },
            '9':{  'passive[0].rslt.lead':-2, 'passive[0].rslt.powerBase':-2000    ,'passive[2]':{'priority':-2000, 'cond':['lead','>=',2005], 'rslt':{'resFinal':20,'powerBase':800}} },
            '200':{  'passive[0].rslt.lead':-2, 'passive[0].rslt.powerBase':-2000    ,'passive[2]':{'priority':-2000, 'cond':['lead','>=',2005], 'rslt':{'resFinal':20,'powerBase':800}} },
            '22':{  'passive[0].rslt.lead':-3, 'passive[0].rslt.powerBase':-250    ,'passive[2]':{'priority':-2000, 'cond':['lead','>=',2005], 'rslt':{'resFinal':30,'powerBase':2200}} },
         },
        },
     'star25':{ 
         'name':'48025',
         'info':'49225',    
      'index':8,  
         'fix_type':7,
         'exp_type':2,
         'max_level':200,
         'icon':'25',
         'passive':{ 'rslt':{'def':30}}, 
         'lvPatch':{   
            '22':{  'passive.rslt.defBase':200 },
            '22':{  'passive.rslt.defBase':20 },
            '23':{  'passive.rslt.defBase':30 },
            '24':{  'passive.rslt.defBase':40 },
            '25':{  'passive.rslt.defBase':50 },
         },
        },
     'star26':{ 
         'name':'48026',
         'info':'49226',  
         'index':9,   
         'fix_type':0,
         'exp_type':0,
         'max_level':30,
         'icon':'26',
         'passive':{ 'rslt':{'dmgSkill':60, 'resHero':60, 'powerBase':250},}, 
         'up':{'passive.rslt.dmgSkill': 8, 'passive.rslt.resHero': 8,'passive.rslt.powerBase': 30}, 
        },
     'star27':{ 
         'name':'48027',
         'info':'49227', 
         'index':200,    
         'fix_type':0,
         'exp_type':0,
         'max_level':30,
         'icon':'27',
         'passive':{ 'rslt':{'powerBase':250},}, 
         'up':{'passive.rslt.powerBase': 30}, 
        },
     'star28':{ 
         'name':'48028',
         'info':'49228',  
         'index':24,   
         'fix_type':0,
         'exp_type':0,
         'max_level':30,
         'icon':'28',
         'passive':{ 'cond':['army[0].type', '=', 0], 'rslt':{'army[0].dmgFinal':25, 'army[0].resFinal':25, 'powerBase':250},}, 
         'up':{'passive.rslt.$army[0].dmgFinal': 3, 'passive.rslt.$army[0].resFinal': 3,'passive.rslt.powerBase': 30}, 
        },
     'star29':{ 
         'name':'48029',
         'info':'49229',   
         'index':23,  
         'fix_type':0,
         'exp_type':0,
         'max_level':30,
         'icon':'29',
         'passive':{ 'cond':['army[0].type', '=', 2], 'rslt':{'army[0].dmgFinal':25, 'army[0].resFinal':25, 'powerBase':250},}, 
         'up':{'passive.rslt.$army[0].dmgFinal': 3, 'passive.rslt.$army[0].resFinal': 3,'passive.rslt.powerBase': 30}, 
        },
     'star20':{ 
         'name':'48020',
         'info':'49220',  
         'index':22,   
         'fix_type':0,
         'exp_type':0,
         'max_level':30,
         'icon':'20',
         'passive':{ 'cond':['army[2].type', '=', 2], 'rslt':{'army[2].dmgFinal':25, 'army[2].resFinal':25, 'powerBase':250},}, 
         'up':{'passive.rslt.$army[2].dmgFinal': 3, 'passive.rslt.$army[2].resFinal': 3,'passive.rslt.powerBase': 30}, 
        },
     'star22':{ 
         'name':'48022',
         'info':'49222',  
         'index':22,   
         'fix_type':0,
         'exp_type':0,
         'max_level':30,
         'icon':'22',
         'passive':{ 'cond':['army[2].type', '=', 3], 'rslt':{'army[2].dmgFinal':25, 'army[2].resFinal':25, 'powerBase':250},}, 
         'up':{'passive.rslt.$army[2].dmgFinal': 3, 'passive.rslt.$army[2].resFinal': 3,'passive.rslt.powerBase': 30}, 
        },


     

     'star50':{   
         'name':'48050',
         'info':'49250',   
         'index':53,  
         'fix_type':2,
         'exp_type':3,
         'max_level':30,
         'icon':'50',
         'passive':{ 'cond':['army[0].type', '=', 0], 'rslt':{'army[0].atk':25,'army[0].atkBase':2}}, 
     },
     'star52':{   
         'name':'48052',
         'info':'49252',   
         'index':52,  
         'fix_type':2,
         'exp_type':3,
         'max_level':30,
         'icon':'52',
         'passive':{ 'cond':['army[0].type', '=', 2], 'rslt':{'army[0].atk':25,'army[0].atkBase':2}}, 
     },
     'star52':{   
         'name':'48052',
         'info':'49252',   
         'index':52,  
         'fix_type':2,
         'exp_type':3,
         'max_level':30,
         'icon':'52',
         'passive':{ 'cond':['army[2].type', '=', 2], 'rslt':{'army[2].atk':25,'army[2].atkBase':2}}, 
     },
     'star53':{   
         'name':'48053',
         'info':'49253',   
         'index':50,  
         'fix_type':2,
         'exp_type':3,
         'max_level':30,
         'icon':'53',
         'passive':{ 'cond':['army[2].type', '=', 3], 'rslt':{'army[2].atk':25,'army[2].atkBase':2}}, 
     },


     'star60':{  
         'name':'48060',
         'info':'49260',  
         'index':63,   
         'fix_type':2,
         'exp_type':3,
         'max_level':30,
         'icon':'60',
         'passive':{ 'cond':['army[0].type', '=', 0], 'rslt':{'army[0].hpm':95,'army[0].hpmBase':2}}, 
     },
     'star62':{  
         'name':'48062',
         'info':'49262',  
         'index':62,   
         'fix_type':2,
         'exp_type':3,
         'max_level':30,
         'icon':'62',
         'passive':{ 'cond':['army[0].type', '=', 2], 'rslt':{'army[0].hpm':95,'army[0].hpmBase':2}}, 
     },
     'star62':{  
         'name':'48062',
         'info':'49262',  
         'index':62,   
         'fix_type':2,
         'exp_type':3,
         'max_level':30,
         'icon':'62',
         'passive':{ 'cond':['army[2].type', '=', 2], 'rslt':{'army[2].hpm':95,'army[2].hpmBase':2}}, 
     },
     'star63':{  
         'name':'48063',
         'info':'49263',  
         'index':60,   
         'fix_type':2,
         'exp_type':3,
         'max_level':30,
         'icon':'63',
         'passive':{ 'cond':['army[2].type', '=', 3], 'rslt':{'army[2].hpm':95,'army[2].hpmBase':2}}, 
     },

     'star70':{  
         'name':'48070',
         'info':'49270',  
         'index':73,   
         'fix_type':3,
         'exp_type':3,
         'max_level':30,
         'icon':'70',
         'passive':{ 'cond':['army[0].type', '=', 0], 'rslt':{'army[0].def':25,'army[0].defBase':2}}, 
     },
     'star72':{  
         'name':'48072',
         'info':'49272',  
         'index':72,   
         'fix_type':3,
         'exp_type':3,
         'max_level':30,
         'icon':'72',
         'passive':{ 'cond':['army[0].type', '=', 2], 'rslt':{'army[0].def':25,'army[0].defBase':2}}, 
     },
     'star72':{  
         'name':'48072',
         'info':'49272',  
         'index':72,   
         'fix_type':3,
         'exp_type':3,
         'max_level':30,
         'icon':'72',
         'passive':{ 'cond':['army[2].type', '=', 2], 'rslt':{'army[2].def':25,'army[2].defBase':2}}, 
     },
     'star73':{  
         'name':'48073',
         'info':'49273',  
         'index':70,   
         'fix_type':3,
         'exp_type':3,
         'max_level':30,
         'icon':'73',
         'passive':{ 'cond':['army[2].type', '=', 3], 'rslt':{'army[2].def':25,'army[2].defBase':2}}, 
     },

}