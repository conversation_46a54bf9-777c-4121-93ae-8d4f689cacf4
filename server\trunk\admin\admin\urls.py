#-*- coding: utf-8 -*-
from django.conf.urls.defaults import *


urlpatterns = patterns('admin.views.main',
    (r'^$', 'index'),
    (r'^logout/$', 'logout'),
    (r'^verify_code/$', 'img_verify_code'),
    (r'^sms_verify_code/$', 'sms_verify_code'),
    (r'^left/$', 'left'),
    (r'^welcome/$', 'welcome'),
    (r'^admin/$', 'admin'),
    (r'^edit_add_admin/$', 'edit_add_admin'),
    (r'^edit_add_admin_p/$', 'edit_add_admin_p'),
    (r'^change_password/$', 'change_password'),
    (r'^change_password_p/$', 'change_password_p'),
    (r'^reset_password/$', 'reset_password'),
    (r'^reset_password2/$', 'reset_password2'),
    (r'^del_admin/$', 'del_admin'),
    (r'^reset_phone_num/$', 'reset_phone_num'),
    (r'^admin_op_logs/$', 'admin_op_logs'),
    (r'^roles/$', 'get_roles'),
    (r'^edit_add_role/$', 'edit_add_role'),
    (r'^edit_add_role_p/$', 'edit_add_role_p'),
    (r'^delete_role/$', 'delete_role'),
    (r'^admin_pay_records/$', 'admin_pay_records'),
    (r'^get_role_permissions/$', 'get_role_permissions'),
    (r'^force_logout_user/$', 'force_logout_user'),

)

urlpatterns += patterns('admin.views.publish',
    (r'^publish/push_server_config/$', 'push_server_config'),
    (r'^publish/publish/$', 'publish'),
    (r'^publish/publish_notice/$', 'publish_notice'),
    (r'^publish/force_update_config/$', 'force_update_config'),
    (r'^publish/jump_config_version/$', 'jump_config_version'), 
    (r'^publish/service_submit/$', 'service_submit'),
    (r'^publish/get_server_status/$', 'get_server_status'),
    (r'^publish/get_server_list/$', 'get_server_list'),
    (r'^publish/get_publish_log/$', 'get_publish_log'),
    (r'^publish/publish_duplicate/$', 'publish_duplicate'),
)

urlpatterns += patterns('admin.views.waiter',
    (r'^waiter/view_waiter/$', 'view_waiter'),
    (r'^waiter/edit_waiter/$', 'edit_waiter'),
    (r'^waiter/edit_waiter_p/$', 'edit_waiter_p'),
    (r'^waiter/del_waiter/$', 'del_waiter'),
    (r'^waiter/waiter_game/$', 'waiter_game'),
    (r'^waiter/waiter_pay/$', 'waiter_pay'),
    (r'^waiter/admin_waiter/$', 'admin_waiter'),
)

urlpatterns += patterns('admin.views.app_user',
    (r'^app_user/change_sessionid/$', 'change_sessionid'),
    (r'^app_user/view_app_user/$', 'view_app_user'),
    (r'^app_user/drop_user_prop/$', 'drop_user_prop'),
    (r'^app_user/drop_user_prop_p/$', 'drop_user_prop_p'),
    (r'^app_user/view_drop_prop/$', 'view_drop_prop'),
    (r'^app_user/freeze_user/$', 'freeze_user'),
    (r'^app_user/get_user_hero_dict/$', 'get_user_hero_dict'),
    (r'^app_user/user_interim_pwd/$', 'user_interim_pwd'),
    (r'^app_user/view_user_tel/$', 'view_user_tel'),
    (r'^app_user/freeze_do/$', 'freeze_do'),
    (r'^app_user/freeze_view/$', 'freeze_view'),
    (r'^app_user/freeze_history/$', 'freeze_history'),
    (r'^app_user/view_world/$', 'view_world'),
    (r'^app_user/download_world/$', 'download_world'),
    (r'^app_user/view_world_report/$', 'view_world_report'),
    (r'^app_user/get_world_report_hero_dict/$', 'get_world_report_hero_dict'),
    (r'^app_user/user_power_rank/$', 'user_power_rank'),
    (r'^app_user/hero_power_rank/$', 'hero_power_rank'),
    (r'^app_user/user_pay_rank/$', 'user_pay_rank'),
    (r'^app_user/view_user_fight/$', 'view_user_fight'),
    (r'^app_user/view_session_key/$', 'view_session_key'),
    (r'^app_user/mod_app_user/$', 'mod_app_user'),
    (r'^app_user/mod_user_tel/$', 'mod_user_tel'),
    (r'^app_user/init_user/$', 'init_user'),
    (r'^app_user/save_user/$', 'save_user'),
    (r'^app_user/admin_pay/$', 'admin_pay'),
    (r'^app_user/change_country/$', 'change_country'),
    (r'^app_user/grant_ucoin/$', 'grant_ucoin'),   
    (r'^app_user/grant_gtask/$', 'grant_gtask'), 
    (r'^app_user/weixin_admin_pay/$', 'weixin_admin_pay'),
    (r'^app_user/pay_url_img/$', 'pay_url_img'),
    (r'^app_user/save_world/$', 'save_world'),
    (r'^app_user/change_world/$', 'change_world'),
    (r'^app_user/init_pk_yard/$', 'init_pk_yard'),
    (r'^app_user/coin_records/$', 'coin_records'),
    (r'^app_user/join_zone_server/$', 'join_zone_server'),
    (r'^app_user/free_api_black/$', 'free_api_black'),

    (r'^app_user/view_user_rank/$', 'view_user_rank'),
    (r'^app_user/cleanup_troops/$', 'cleanup_troops'),
    (r'^app_user/cleanup_ucoin/$', 'cleanup_ucoin'),
    (r'^app_user/cleanup_homeland_building/$', 'cleanup_homeland_building'),
    (r'^app_user/cleanup_ng_task/$', 'cleanup_ng_task'),
    (r'^app_user/refresh_tomb/$', 'refresh_tomb'),
    (r'^app_user/relieve_duplicate_ban/$', 'relieve_duplicate_ban'),

    (r'^app_user/chat_log/$', 'chat_log'),
    (r'^app_user/user_msg_log/$', 'user_msg_log'),
    (r'^app_user/change_country_notice/$', 'change_country_notice'),
    (r'^app_user/view_duplicate/$', 'view_duplicate'),
    (r'^app_user/get_duplicate_join_user/$', 'get_duplicate_join_user'),
    (r'^app_user/get_duplicate_join_user/$', 'get_duplicate_join_user'),
    (r'^app_user/get_duplicate_user_hero_dict/$', 'get_duplicate_user_hero_dict'),
    (r'^app_user/get_db_user_dict/$', 'get_db_user_dict'),
    (r'^app_user/get_duplicate_server_info/$', 'get_duplicate_server_info'),
    (r'^app_user/get_duplicate_city_dict/$', 'get_duplicate_city_dict'),
    (r'^app_user/get_duplicate_world_dict/$', 'get_duplicate_world_dict'),
    (r'^app_user/get_duplicate_user_dict/$', 'get_duplicate_user_dict'),
    (r'^app_user/get_duplicate_fight_js_data/$', 'get_duplicate_fight_js_data'),
    (r'^app_user/duplicate_chat/$', 'duplicate_chat'),
    (r'^app_user/duplication_operate/$', 'duplication_operate'),
    (r'^app_user/admin_join_duplicate/$', 'admin_join_duplicate'),
    (r'^app_user/get_duplicate_server_status/$', 'get_duplicate_server_status'),
    (r'^app_user/duplicate_status_cache_data/$', 'get_duplicate_status_cache_data'),
    (r'^app_user/create_role/$', 'create_role'),
    (r'^app_user/get_fight_logs/$', 'get_fight_logs'),
    (r'^app_user/get_battle_hero_dict/$', 'get_battle_hero_dict'),
    (r'^app_user/get_battle_dict/$', 'get_battle_dict'),
    (r'^app_user/cancel_member/$', 'cancel_member'),
    (r'^app_user/simulate_merge/$', 'simulate_merge'),
    (r'^app_user/sync_invitation/$', 'sync_invitation'),
    (r'^app_user/get_pk_yard_log/$', 'get_pk_yard_log'),
    (r'^app_user/ignore_user_list/$', 'ignore_user_list'),
    (r'^app_user/test_story_dungeon/$', 'test_story_dungeon'),
)


urlpatterns += patterns('admin.views.admin_head',
    (r'^admin_head/view_head/$', 'view_head'),
    (r'^admin_head/pass_head/$', 'pass_head'),
    (r'^admin_head/mod_head_status/$', 'mod_head_status'),
)

urlpatterns += patterns('admin.views.flash_setting',
    (r'^flash/setting/$', 'setting'),
    (r'^flash/setting_p/$', 'setting_p'),
    (r'^flash/api/$', 'api'),
)

urlpatterns += patterns('admin.views.system_setting',
    (r'^system/setting/$', 'setting'),
    (r'^system/setting_notice/$', 'setting_notice'),
    (r'^system/setting_p/$', 'setting_p'),
    (r'^notice/setting_p/$', 'setting_p_notice'),
    (r'^notice/setting/$', 'setting_notice'),
    (r'^system/get_config_log/$', 'get_config_log'),
    (r'^system/download_configs/$', 'download_configs'),

    (r'^system/test_setting/$', 'test_setting'),
    (r'^system/test_setting_p/$', 'test_setting_p'),
    (r'^system/change_take_config_type/$', 'change_take_config_type'),
    (r'^system/get_config_value/$', 'get_config_value'),
)

urlpatterns += patterns('admin.views.maintain',
    (r'^maintain/$', 'zone_maintain'),
    (r'^maintain/change_full_maintain_status/$', 'change_full_maintain_status'),
    (r'^maintain/change_zone_maintain_status/$', 'change_zone_maintain_status'),
    (r'^maintain/get_zone_maintain_status/$', 'get_zone_maintain_status'),
    (r'^maintain/push_zone_maintain_status/$', 'push_zone_maintain_status'),
    (r'^maintain/get_server_list/$', 'get_server_list'),
)

urlpatterns += patterns('admin.views.coin_record',
    (r'^coin_record/buy_record/$', 'buy_record'),
    (r'^coin_record/buy_record_day/([\d]{4})-([\d]{1,2})-([\d]{1,2})/$' , 'buy_record_day'),
    (r'^coin_record/export_buy_record_day/([\d]{4})-([\d]{1,2})-([\d]{1,2}).csv$' , 'export_buy_record_day'),
    (r'^coin_record/coin_ana_day/([\d]{4})-([\d]{1,2})-([\d]{1,2})/$' , 'coin_ana_day'),
    (r'^coin_record/user_record/$', 'user_record'),
)



urlpatterns += patterns('admin.views.pay_record',
    (r'^pay_record/pay_record/$', 'pay_record'),
    (r'^pay_record/pay_record_day/([\d]{4})-([\d]{1,2})-([\d]{1,2})/$' , 'pay_record_day'),
    (r'^pay_record/user_record/$', 'user_record'),
)

urlpatterns += patterns('admin.views.msg',
    (r'^msg/msg_view/$', 'msg_view'),
    (r'^msg/msg_view_user/$', 'msg_view_user'),
    (r'^msg/re_msg/$', 're_msg'),
    (r'^msg/pass_msg/$', 'pass_msg'),
    (r'^msg/mark_msg/$', 'mark_msg'),
    (r'^msg/country_notice_view/$', 'country_notice_view'),
)
urlpatterns += patterns('admin.views.freeze',
    (r'^freeze/freeze_view/$', 'freeze_view'),
    (r'^freeze/freeze_user/$', 'freeze_user'),
    (r'^freeze/freeze_do/$', 'freeze_do'),
)

urlpatterns += patterns('admin.views.casino_gift',
    (r'^casino_gift/casino_gift_records/$', 'casino_gift_records'),
    (r'^casino_gift/add_casino_gift/$', 'add_casino_gift'),
    (r'^casino_gift/add_casino_gift_p/$', 'add_casino_gift_p'),
    (r'^casino_gift/del_casino_gift/$', 'del_casino_gift'),
    (r'^casino_gift/send_casino_gift/$', 'send_casino_gift'),
    (r'^casino_gift/send_casino_gift_p/$', 'send_casino_gift_p'),
    (r'^casino_gift/casino_user_records/$', 'casino_user_records'),
    (r'^casino_gift/casino_user_records_day/([\d]{4})-([\d]{1,2})-([\d]{1,2})/$' , 'casino_user_records_day'),
    (r'^casino_gift/user_casino_user_records/$', 'user_casino_user_records'),
)


urlpatterns += patterns('admin.views.reward',
    (r'^reward/send_admin_gift_msg/$', 'send_admin_gift_msg'),
    (r'^reward/admin_gift_msg/$', 'admin_gift_msg'),
    (r'^reward/set_reward_to_user_p/$', 'set_reward_to_user_p'),
    (r'^reward/set_reward_to_user/$', 'set_reward_to_user'),
    (r'^reward/del_reward_to_user/$', 'del_reward_to_user'),
    (r'^reward/do_admin_gift_msg/$', 'do_admin_gift_msg'),

    (r'^reward/rewards/$', 'rewards'),
    (r'^reward/do_reward/$', 'do_reward'),
    (r'^reward/edit_reward/$', 'edit_reward'),
    (r'^reward/edit_reward_p/$', 'edit_reward_p'),

    (r'^reward/edit_reward_code/$', 'edit_reward_code'),
    (r'^reward/do_code_reward/$', 'do_code_reward'),
    (r'^reward/edit_reward_code_p/$', 'edit_reward_code_p'),
    (r'^reward/code_rewards/$', 'code_rewards'),
    (r'^reward/get_reward_code_list/$', 'get_reward_code_list'),
    (r'^reward/weixin_reward_code/$', 'weixin_reward_code'),

    (r'^reward/edit_push_notice/$', 'edit_push_notice'),
    (r'^reward/edit_push_notice_p/$', 'edit_push_notice_p'),
    (r'^reward/push_notice/$', 'push_notice'),
    (r'^reward/do_push_notice/$', 'do_push_notice'),

    (r'^reward/publish_reward/$', 'publish_reward'),
    (r'^reward/push_server_reward_config/$', 'push_server_reward_config'),
    (r'^reward/search_rewards/$', 'search_rewards'),
    (r'^reward/add_ucoin_reward/$', 'add_ucoin_reward'),
    (r'^reward/view_ucoin_reward/$', 'view_ucoin_reward'),
    (r'^reward/send_ucoin_reward/$', 'send_ucoin_reward'),
)


urlpatterns += patterns('admin.views.zone_manage',
    (r'^zone_manage/$', 'zone_manage'),
    (r'^zone_manage/batch_import/$', 'batch_import'),
    (r'^zone_manage/get_zone/$', 'get_zone'),
    (r'^zone_manage/edit_zone/$', 'edit_zone'),
    (r'^zone_manage/view_zone_op_logs/$', 'view_zone_op_logs'),
    (r'^zone_manage/modify_auto_open_config/$', 'modify_auto_open_config'),
    (r'^zone_manage/change_auto_active/$', 'change_auto_active'),
)

urlpatterns += patterns('admin.views.merge_zone',
    (r'^merge_zone/$', 'merge_zone'),
    (r'^merge_zone/calculate_merge_zone/$', 'calculate_merge_zone'),
    (r'^merge_zone/get_merge_config/$', 'get_merge_config'),
    (r'^merge_zone/modify_merge_config/$', 'modify_merge_config'),
    (r'^merge_zone/confirm_merge_config/$', 'confirm_merge_config'),
    (r'^merge_zone/view_merge_result/$', 'view_merge_result'),
    (r'^merge_zone/download_merge_config/$', 'download_merge_config'),
    (r'^merge_zone/recalculate_merge_zone/$', 'recalculate_merge_zone'),
    (r'^merge_zone/del_merge_record/$', 'del_merge_record'),
    (r'^merge_zone/start_merge/$', 'start_merge'),
    (r'^merge_zone/get_merge_records/$', 'get_merge_records'),
    (r'^merge_zone/get_maintain_zones/$', 'get_maintain_zones'),
    (r'^merge_zone/view_merge_progress/$', 'view_merge_progress'),
    (r'^merge_zone/manual_merge_zone/$', 'manual_merge_zone'),
)

urlpatterns += patterns('admin.views.js_test',
    (r'^js_test/$', 'js_api_test'),
    (r'^js_test/call_js_interface/$', 'call_js_interface'),
    (r'^js_test/save_call_interface/$', 'save_call_interface'),
    (r'^js_test/get_call_record_data/$', 'get_call_record_data'),
    (r'^js_test/download_js/$', 'download_js'),
)

urlpatterns += patterns('admin.views.gather_player',
    (r'^gather_player/$', 'index'),
    (r'^gather_player/get_player_template/$', 'get_player_template'),
    (r'^gather_player/get_player_data/$', 'get_player_data'),
    (r'^gather_player/get_player_hero_dict/$', 'get_player_hero_dict'),
    (r'^gather_player/save_player_data/$', 'save_player_data'),
    (r'^gather_player/view_zone_gather/$', 'view_zone_gather'),
    (r'^gather_player/get_zone_player_data/$', 'get_zone_player_data'),
    (r'^gather_player/get_zone_mod_data/$', 'get_zone_mod_data'),
    (r'^gather_player/modify_player_status/$', 'modify_player_status'),
    (r'^gather_player/change_player_user_zone_status/$', 'change_player_user_zone_status'),
    (r'^gather_player/view_player_data/$', 'view_player_data'),
    (r'^gather_player/player_command/$', 'player_command'),
)

