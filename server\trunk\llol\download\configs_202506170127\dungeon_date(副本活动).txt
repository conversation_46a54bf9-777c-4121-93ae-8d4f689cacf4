{
	'reset_city':{'-1':1,'-201':2},		#当今日有任意活动时，将数组内的城池归属重置,过偏移的时候执行
	'dungeon_default_map':'dungeon_default_map',        #没有副本玩法的情况下显示的地图素材名
	0:{   #未合服，此配置等同于功能的总开关，当配置产出后发布时，没有对应名称的功能将立即关闭
		'max_loop':[47,48],		#首次循环天数，后续循环天数
		'date_list':{   #开服/合服天数，在以下范围内，取当天活动

		},
		'cycle_list':{   #开服/合服天数，大于'date_list'，当前开服天数-'max_loop'第0位，模'max_loop'第1位，余数对应以下天数的活动

		}
	},
	1:{   #1次合服
		'date_list':{
			7:'xyz',
		},
		'cycle_list':{
			8:'xyz',
		}
	},
	2:{   #2次合服
		'date_list':{
			3:'xyz',

		},
		'cycle_list':{
			6:'xyz',

		}
	},
	'dungeons':{  #显示素材以及持续时间
		'xyz':{    #襄阳战，与配置同名
			'name':'dungeon_xyz_name',    #玩法名称
			'map':'dungeon_xyz_map',    #该副本玩法的显示的地图素材名
			'before_day':1800,         #比赛日前置显示时间，如果与前一个活动的后置时间重叠，优先显示后置状态，单位：分钟
			'after_day':600,          #比赛日后置保留显示时间，比前置更优先显示，因为可能与结算奖励相关，单位：分钟
		},
		'gdzz':{    #官渡之战
			'name':'dungeon_xyz_name',
			'map':'dungeon_xyz_map',
			'before_day':400,
			'after_day':400,
		},
	}
}