{
   'beastResonanceA4':{ #凤蝶 兽灵4共鸣    我方前、后军行动前，若自身有不良状态，则有20%×凤蝶标记数量的几率发动【凤蝶轻舞】，净化1个不良状态。发动时消耗2个凤蝶标记（不足时消耗所有）。战中每持有1个凤蝶标记，部队免伤+2%
      'type':9,       #兽灵技
      'infoArr':['%|act[0].round.energy','-|act[0].removeDebuff','-|act[0].multMax*act[0].cost','-|act[0].cost'],
      'energyArr':['-|act[1].energy','-|act[1].energyRnd'],
      'passive':[
         {
            'rslt':{'power':300,'powerRate':100},
         },
         {
            'cond':['skill.beastResonanceA4', '*', 1],
            'rslt':{'powerRate':50},
         },
      ],
      'lvPatch':{   #满足特定等级的补丁
         '2':{  'act[1].energy':0, 'act[1].energyRnd':1  }, 
         '3':{  'act[1].energy':1, 'act[1].energyRnd':0  },
         '4':{  'act[1].energy':1, 'act[1].energyRnd':1  },
         '5':{  'act[1].energy':2, 'act[1].energyRnd':0  }, 
      },
      'act':[
        {
          'priority':88800,
          'type': 10,     #10急救净化 预备期
          'src': -1,              #发出源 0前军，1后军，【2英雄】，-1全军
          'tgt':[0, -5],    
          'round':{'energy':200},
          'cond':[['checkBuff',0,-5,2,'>',0]],   #场中存在状态[1]敌我[2]部队[3]状态类型或指定状态[4]比较符[5]数量         
          'costKey':'beastTypeA',       #充能来源类型 
          'cost':1,                 #使用 消耗充能
          'multMax':2,              #一次使用的最大倍数
          'mult':{},                #多重施法时提升内容

          'nonSkill':1,    #非技能，不受傲气
          'noBuff':1,      #有此值时，不受dmgFinal、resFinal、战场dmgRealPer、resRealPer影响
          'noBfr':1,
          'noAft':1,
 
          'removeDebuff':1, 
          #'summonReal':1,
          #'summon':10, 

          'info':['_beastTypeA',4], 
          'eff':'effBeastTypeA',
          'lv':14,
        },
        {     #战斗开始获取能量
          'priority':8600001,
          'type': 13,               #每个大回合重置阶段（无视速度）
          'src': 2,                #发出源 0前军，1后军，【2英雄】，-1全军
          'srcFree': 1, 
          'tgt':[0, -7],        #不计死亡的英雄
          'round':{'0':1000},
          'nonSkill':1,    #非技能，不受傲气
          'noBuff':1,      #有此值时，不受dmgFinal、resFinal、战场dmgRealPer、resRealPer影响
          'noBfr':1,
          'noAft':1,

          'energyKey':'beastTypeA',       #充能标记类型 
          'energy':1,                 #获得充能标记数量 
          'energyRnd':1,              #获得充能标记随机数量 
          'info':['凤蝶·初始标记',0],
          'eff':'effNull',
          'time':0,	          #强制指定战报时间
        },
      ],
   },
   'beastResonanceA8':{ #凤蝶 兽灵8共鸣    每回合恢复标记
      'type':9,       #兽灵技
      'energyArr':['-|act[0].energy','-|act[0].energyRnd'],
      'passive':[
         {
            'rslt':{'power':500,'powerRate':150},
         },
         {
            'cond':['skill.beastResonanceA8', '*', 1],
            'rslt':{'powerRate':150},
         },
      ],
      'lvPatch':{   #满足特定等级的补丁
         '2':{  'act[0].energy':1, 'act[0].energyRnd':-1  },
         '3':{  'act[0].energy':1, 'act[0].energyRnd':0  },
         '4':{  'act[0].energy':2, 'act[0].energyRnd':-1  },
         '5':{  'act[0].energy':2, 'act[0].energyRnd':0  },
      },
      'act':[
        {     #大回合重置获取能量
          'priority':8600000,
          'type': 13,               #每个大回合重置阶段（无视速度）
          'src': 2,                #发出源 0前军，1后军，【2英雄】，-1全军
          'srcFree': 1, 
          'tgt':[0, -7],        #不计死亡的英雄 
          'round':{'all':1000},
          'nonSkill':1,    #非技能，不受傲气
          'noBuff':1,      #有此值时，不受dmgFinal、resFinal、战场dmgRealPer、resRealPer影响
          'noBfr':1,
          'noAft':1,

          'energyKey':'beastTypeA',       #充能标记类型 
          'energy':0,                 #获得充能标记数量 
          'energyRnd':1,              #获得充能标记随机数量 
          'info':['凤蝶·增补标记',0],
          'eff':'effNull',
          'time':0,	          #强制指定战报时间
        },
      ],
   },


   'beastResonanceB4':{ #凶狼 我方前、后军行动前，若敌方同位置部队有增益状态，则有20%×凶狼标记数量的几率发动【凶狼凝视】，驱散其1个增益状态。发动时消耗2个凶狼标记（不足时消耗所有）。战中每持有1个凶狼标记，部队伤害+2%
      'type':9,       #兽灵技
      'infoArr':['%|act[0].round.energy','-|act[0].removeDebuff','-|act[0].multMax*act[0].cost','-|act[0].cost'],
      'energyArr':['-|act[1].energy','-|act[1].energyRnd'],
      'passive':[
         {
            'rslt':{'power':300,'powerRate':100},
         },
         {
            'cond':['skill.beastResonanceB4', '*', 1],
            'rslt':{'powerRate':50},
         },
      ],
      'lvPatch':{   #满足特定等级的补丁
         '2':{  'act[1].energy':0, 'act[1].energyRnd':1  },
         '3':{  'act[1].energy':1, 'act[1].energyRnd':0  },
         '4':{  'act[1].energy':1, 'act[1].energyRnd':1  },
         '5':{  'act[1].energy':2, 'act[1].energyRnd':0  },
      },
      'act':[
        {
          'priority':88810,
          'type': 0,     
          'src': -1,              #发出源 0前军，1后军，【2英雄】，-1全军
          'tgt':[1, -5],    
          'round':{'energy':200},
          'cond':[['checkBuff',1, {'key':'alive', 'tgt1':-5} ,1,'>',0]],    #场中存在状态[1]敌我[2]部队[3]状态类型或指定状态[4]比较符[5]数量       
          'costKey':'beastTypeB',       #充能来源类型 
          'cost':1,                 #使用 消耗充能
          'multMax':2,              #一次使用的最大倍数
          'mult':{},                #多重施法时提升内容

          'nonSkill':1,    #非技能，不受傲气
          'noBuff':1,      #有此值时，不受dmgFinal、resFinal、战场dmgRealPer、resRealPer影响
          'noBfr':1,
          'noAft':1,

          'removeBuff':1,

          'info':['_beastTypeB',4],
          'eff':'effBeastTypeB',
          'lv':14,
        },
        {     #战斗开始获取能量
          'priority':8600004,
          'type': 13,               #每个大回合重置阶段（无视速度）
          'src': 2,                #发出源 0前军，1后军，【2英雄】，-1全军
          'srcFree': 1, 
          'tgt':[0, -7],        #不计死亡的英雄
          'round':{'0':1000},
          'nonSkill':1,    #非技能，不受傲气
          'noBuff':1,      #有此值时，不受dmgFinal、resFinal、战场dmgRealPer、resRealPer影响
          'noBfr':1,
          'noAft':1,

          'energyKey':'beastTypeB',       #充能标记类型 
          'energy':1,                 #获得充能标记数量 
          'energyRnd':1,              #获得充能标记随机数量 
          'info':['凶狼·初始标记',0],
          'eff':'effNull',
          'time':0,	          #强制指定战报时间
        },
      ],
   },
   'beastResonanceB8':{ #凶狼 兽灵8共鸣    每回合恢复标记
      'type':9,       #兽灵技
      'energyArr':['-|act[0].energy','-|act[0].energyRnd'],
      'passive':[
         {
            'rslt':{'power':500,'powerRate':150},
         },
         {
            'cond':['skill.beastResonanceB8', '*', 1],
            'rslt':{'powerRate':150},
         },
      ],
      'lvPatch':{   #满足特定等级的补丁
         '2':{  'act[0].energy':1, 'act[0].energyRnd':-1  },
         '3':{  'act[0].energy':1, 'act[0].energyRnd':0  },
         '4':{  'act[0].energy':2, 'act[0].energyRnd':-1  },
         '5':{  'act[0].energy':2, 'act[0].energyRnd':0  },
      },
      'act':[
        {     #大回合重置获取能量
          'priority':8600003,
          'type': 13,               #每个大回合重置阶段（无视速度）
          'src': 2,                #发出源 0前军，1后军，【2英雄】，-1全军
          'srcFree': 1, 
          'tgt':[0, -7],        #不计死亡的英雄 
          'round':{'all':1000},
          'nonSkill':1,    #非技能，不受傲气
          'noBuff':1,      #有此值时，不受dmgFinal、resFinal、战场dmgRealPer、resRealPer影响
          'noBfr':1,
          'noAft':1,

          'energyKey':'beastTypeB',       #充能标记类型 
          'energy':0,                 #获得充能标记数量 
          'energyRnd':1,              #获得充能标记随机数量 
          'info':['凶狼·增补标记',0],
          'eff':'effNull',
          'time':0,	          #强制指定战报时间
        },
      ],
   },


   'beastResonanceC4':{ #妖狐   若预判将对敌方施加的不良状态会因几率失败，则有15%×妖狐标记数量的几率提前发动【妖狐预言】，强制施加该不良状态。发动时消耗2个妖狐标记（不足时消耗所有）。战中每持有1个妖狐标记，部队暴击率+3%
      'type':9,       #兽灵技
      'infoArr':['%|act[0].round.energy','-|act[0].removeDebuff','-|act[0].multMax*act[0].cost','-|act[0].cost'],
      'energyArr':['-|act[1].energy','-|act[1].energyRnd'],
      'passive':[
         {
            'rslt':{'power':300,'powerRate':100},
         },
         {
            'cond':['skill.beastResonanceC4', '*', 1],
            'rslt':{'powerRate':50},
         },
      ],
      'lvPatch':{   #满足特定等级的补丁
         '2':{  'act[1].energy':0, 'act[1].energyRnd':1  },
         '3':{  'act[1].energy':1, 'act[1].energyRnd':0  },
         '4':{  'act[1].energy':1, 'act[1].energyRnd':1  },
         '5':{  'act[1].energy':2, 'act[1].energyRnd':0  },
      },
      'act':[
        {
          'priority':88820,
          'type': 11,     #施加debuff失败
          'src': -2,              #发出源 0前军，1后军，【2英雄】，-1全军
          'srcFree': 1,    
          'times': -1,    

          'tgt':[0, 2],    
          'round':{'energy':150},   
          'costKey':'beastTypeC',       #充能来源类型 
          'cost':1,                 #使用 消耗充能
          'multMax':2,              #一次使用的最大倍数
          'mult':{},                #多重施法时提升内容

          'nonSkill':1,    #非技能，不受傲气
          'noBuff':1,      #有此值时，不受dmgFinal、resFinal、战场dmgRealPer、resRealPer影响
          'noBfr':1,
          'noAft':1,

          #'dmgReal':1234,

          'force':1,      #强制施加debuff

          'info':['_beastTypeC',4],
          'eff':'effBeastTypeC',
          'lv':14,
        },
        {     #战斗开始获取能量
          'priority':8600006,
          'type': 13,               #每个大回合重置阶段（无视速度）
          'src': 2,                #发出源 0前军，1后军，【2英雄】，-1全军
          'srcFree': 1, 
          'tgt':[0, -7],        #不计死亡的英雄 
          'round':{'0':1000},
          'nonSkill':1,    #非技能，不受傲气
          'noBuff':1,      #有此值时，不受dmgFinal、resFinal、战场dmgRealPer、resRealPer影响
          'noBfr':1,
          'noAft':1,

          'energyKey':'beastTypeC',       #充能标记类型 
          'energy':1,                 #获得充能标记数量 
          'energyRnd':1,              #获得充能标记随机数量 
          'info':['妖狐·初始标记',0],
          'eff':'effNull',
          'time':0,	          #强制指定战报时间
        },
      ],
   },
   'beastResonanceC8':{ #妖狐   兽灵8共鸣    每回合恢复标记
      'type':9,       #兽灵技
      'energyArr':['-|act[0].energy','-|act[0].energyRnd'],
      'passive':[
         {
            'rslt':{'power':500,'powerRate':150},
         },
         {
            'cond':['skill.beastResonanceC8', '*', 1],
            'rslt':{'powerRate':150},
         },
      ],
      'lvPatch':{   #满足特定等级的补丁
         '2':{  'act[0].energy':1, 'act[0].energyRnd':-1  },
         '3':{  'act[0].energy':1, 'act[0].energyRnd':0  },
         '4':{  'act[0].energy':2, 'act[0].energyRnd':-1  },
         '5':{  'act[0].energy':2, 'act[0].energyRnd':0  },
      },
      'act':[
        {     #大回合重置获取能量
          'priority':8600005,
          'type': 13,               #每个大回合重置阶段（无视速度）
          'src': 2,                #发出源 0前军，1后军，【2英雄】，-1全军
          'srcFree': 1, 
          'tgt':[0, -7],        #不计死亡的英雄 
          'round':{'all':1000},
          'nonSkill':1,    #非技能，不受傲气
          'noBuff':1,      #有此值时，不受dmgFinal、resFinal、战场dmgRealPer、resRealPer影响
          'noBfr':1,
          'noAft':1,

          'energyKey':'beastTypeC',       #充能标记类型 
          'energy':0,                 #获得充能标记数量 
          'energyRnd':1,              #获得充能标记随机数量 
          'info':['妖狐·增补标记',0],
          'eff':'effNull',
          'time':0,	          #强制指定战报时间
        },
      ],
   },


   'beastResonanceD4':{ #灵蛇   若预判敌方即将发动任意触发型辅助技能，则有15%×灵蛇标记数量的几率提前发动【灵蛇禁锢】，阻止该辅助技能的触发。发动时消耗1个灵蛇标记。战中每持有1个灵蛇标记，部队格挡率+3%
      'type':9,       #兽灵技
      'infoArr':['%|act[0].round.energy','-|act[0].removeDebuff','-|act[0].multMax*act[0].cost','-|act[0].cost'],
      'energyArr':['-|act[1].energy','-|act[1].energyRnd'],
      'passive':[
         {
            'rslt':{'power':300,'powerRate':100},
         },
         {
            'cond':['skill.beastResonanceD4', '*', 1],
            'rslt':{'powerRate':50},
         },
      ],
      'lvPatch':{   #满足特定等级的补丁
         '2':{  'act[1].energy':0, 'act[1].energyRnd':1  },
         '3':{  'act[1].energy':1, 'act[1].energyRnd':0  },
         '4':{  'act[1].energy':1, 'act[1].energyRnd':1  },
         '5':{  'act[1].energy':2, 'act[1].energyRnd':0  },
      },
      'act':[
        {
          'priority':88830,
          'type': 12,     #对方触发型辅助技能
          'src': 2,              #发出源 0前军，1后军，【2英雄】，-1全军
          'srcFree': 1,    
          'times': -1,    

          'tgt':[1, 2],    
          'round':{'energy':150},   
          'costKey':'beastTypeD',       #充能来源类型 
          'cost':1,                 #使用 消耗充能
          'multMax':1,              #一次使用的最大倍数
          'mult':{},                #多重施法时提升内容

          'nonSkill':1,    #非技能，不受傲气
          'noBuff':1,      #有此值时，不受dmgFinal、resFinal、战场dmgRealPer、resRealPer影响
          'noBfr':1,
          'noAft':1,

          'stop':1,      #禁止继续发动

          'info':['_beastTypeD',4],
          'eff':'effBeastTypeD',
          'lv':14,
        },
        {     #战斗开始获取能量
          'priority':8600008,
          'type': 13,               #每个大回合重置阶段（无视速度）
          'src': 2,                #发出源 0前军，1后军，【2英雄】，-1全军
          'srcFree': 1, 
          'tgt':[0, -7],        #不计死亡的英雄 
          'round':{'0':1000},
          'nonSkill':1,    #非技能，不受傲气
          'noBuff':1,      #有此值时，不受dmgFinal、resFinal、战场dmgRealPer、resRealPer影响
          'noBfr':1,
          'noAft':1,

          'energyKey':'beastTypeD',       #充能标记类型 
          'energy':1,                 #获得充能标记数量 
          'energyRnd':1,              #获得充能标记随机数量 
          'info':['灵蛇·初始标记',0],
          'eff':'effNull',
          'time':0,	          #强制指定战报时间
        },
      ],
   },
   'beastResonanceD8':{ #灵蛇   兽灵8共鸣    每回合恢复标记
      'type':9,       #兽灵技
      'energyArr':['-|act[0].energy','-|act[0].energyRnd'],
      'passive':[
         {
            'rslt':{'power':500,'powerRate':150},
         },
         {
            'cond':['skill.beastResonanceD8', '*', 1],
            'rslt':{'powerRate':150},
         },
      ],
      'lvPatch':{   #满足特定等级的补丁
         '2':{  'act[0].energy':1, 'act[0].energyRnd':-1  },
         '3':{  'act[0].energy':1, 'act[0].energyRnd':0  },
         '4':{  'act[0].energy':2, 'act[0].energyRnd':-1  },
         '5':{  'act[0].energy':2, 'act[0].energyRnd':0  },
      },
      'act':[
        {     #大回合重置获取能量
          'priority':8600007,
          'type': 13,               #每个大回合重置阶段（无视速度）
          'src': 2,                #发出源 0前军，1后军，【2英雄】，-1全军
          'srcFree': 1, 
          'tgt':[0, -7],        #不计死亡的英雄 
          'round':{'all':1000},
          'nonSkill':1,    #非技能，不受傲气
          'noBuff':1,      #有此值时，不受dmgFinal、resFinal、战场dmgRealPer、resRealPer影响
          'noBfr':1,
          'noAft':1,

          'energyKey':'beastTypeD',       #充能标记类型 
          'energy':0,                 #获得充能标记数量 
          'energyRnd':1,              #获得充能标记随机数量 
          'info':['灵蛇·增补标记',0],
          'eff':'effNull',
          'time':0,	          #强制指定战报时间
        },
      ],
   },


   'beastResonanceE4':{ #烈鹰 兽灵4共鸣   从战斗开始每回合检测，有20%+15%×烈鹰标记数量的几率，英雄行动前发动【烈鹰突袭】，对敌方后军造成140%伤害。发动时若有烈鹰标记将全部消耗，每个标记提升40%伤害强度
      'type':9,       #兽灵技
      'infoArr':['%|act[0].round.energy','-|act[0].removeDebuff','-|act[0].multMax*act[0].cost','-|act[0].cost'],
      'energyArr':['-|act[1].energy','-|act[1].energyRnd'],
      'passive':[
         {
            'rslt':{'power':300,'powerRate':100},
         },
         {
            'cond':['skill.beastResonanceE4', '*', 1],
            'rslt':{'powerRate':50},
         },
      ],
      'lvPatch':{   #满足特定等级的补丁
         '2':{  'act[1].energy':0, 'act[1].energyRnd':1  },
         '3':{  'act[1].energy':1, 'act[1].energyRnd':0  },
         '4':{  'act[1].energy':1, 'act[1].energyRnd':1  },
         '5':{  'act[1].energy':2, 'act[1].energyRnd':0  },
      },
      'act':[
        {
          'priority':88840,
          'type': 0,  
          'src': 2,              #发出源 0前军，1后军，【2英雄】，-1全军
          'srcFree': 1,
          'tgt':[1, 1],    
          'round':{'any':200,'energy':150},    
          'costKey':'beastTypeE',       #充能来源类型 
          'cost':0,                 #使用 消耗充能
          'costMult':1,                 #多倍使用时 消耗充能
          'multMax':-1,              #一次使用的最大倍数
          'mult':{'dmgScale':300,'dmgLimit':60},                #多重施法时提升内容

          'nonSkill':1,    #非技能，不受傲气
          'noBfr':1,
          'noAft':1,

          'dmg':1200,							#技能伤害系数。文武、性别、兵种类型默认
          'dmgReal':130,						#真实伤害值
          'dmgLimit':150,
          'atk0': 480,    #攻击力源头(前军)
          'atk1': 500,    #攻击力源头(前军)

          'info':['_beastTypeE',4],
          'eff':'effBeastTypeE',
        },
        {     #战斗开始获取能量
          'priority':8600010,
          'type': 13,               #每个大回合重置阶段（无视速度）
          'src': 2,                #发出源 0前军，1后军，【2英雄】，-1全军
          'srcFree': 1, 
          'tgt':[0, -7],        #不计死亡的英雄    
          'round':{'0':1000},
          'nonSkill':1,    #非技能，不受傲气
          'noBuff':1,      #有此值时，不受dmgFinal、resFinal、战场dmgRealPer、resRealPer影响
          'noBfr':1,
          'noAft':1,

          'energyKey':'beastTypeE',       #充能标记类型 
          'energy':1,                 #获得充能标记数量 
          'energyRnd':1,              #获得充能标记随机数量 
          'info':['烈鹰·初始标记',0],
          'eff':'effNull',
          'time':0,	          #强制指定战报时间
        },
      ],
   },
   'beastResonanceE8':{ #烈鹰 兽灵8共鸣    每回合恢复标记
      'type':9,       #兽灵技
      'energyArr':['-|act[0].energy','-|act[0].energyRnd'],
      'passive':[
         {
            'rslt':{'power':500,'powerRate':150},
         },
         {
            'cond':['skill.beastResonanceE8', '*', 1],
            'rslt':{'powerRate':150},
         },
      ],
      'lvPatch':{   #满足特定等级的补丁
         '2':{  'act[0].energy':1, 'act[0].energyRnd':-1  },
         '3':{  'act[0].energy':1, 'act[0].energyRnd':0  },
         '4':{  'act[0].energy':2, 'act[0].energyRnd':-1  },
         '5':{  'act[0].energy':2, 'act[0].energyRnd':0  },
      },
      'act':[
        {     #大回合重置获取能量
          'priority':8600009,
          'type': 13,               #每个大回合重置阶段（无视速度）
          'src': 2,                #发出源 0前军，1后军，【2英雄】，-1全军
          'srcFree': 1, 
          'tgt':[0, -7],        #不计死亡的英雄   
          'round':{'all':1000},
          'nonSkill':1,    #非技能，不受傲气
          'noBuff':1,      #有此值时，不受dmgFinal、resFinal、战场dmgRealPer、resRealPer影响
          'noBfr':1,
          'noAft':1,

          'energyKey':'beastTypeE',       #充能标记类型 
          'energy':0,                 #获得充能标记数量 
          'energyRnd':1,              #获得充能标记随机数量 
          'info':['烈鹰·增补标记',0],
          'eff':'effNull',
          'time':0,	          #强制指定战报时间
        },
      ],
   },

   'beastResonanceF4':{ #蛮牛 兽灵4共鸣   从远战首回合起每4回合，英雄行动前发动【蛮牛冲击】，对敌方前军造成270%伤害。发动时若有蛮牛标记将全部消耗，每个标记提升60%伤害强度
      'type':9,       #兽灵技
      'infoArr':['%|act[0].round.energy','-|act[0].removeDebuff','-|act[0].multMax*act[0].cost','-|act[0].cost'],
      'energyArr':['-|act[1].energy','-|act[1].energyRnd'],
      'passive':[
         {
            'rslt':{'power':300,'powerRate':100},
         },
         {
            'cond':['skill.beastResonanceF4', '*', 1],
            'rslt':{'powerRate':50},
         },
      ],
      'lvPatch':{   #满足特定等级的补丁
         '2':{  'act[1].energy':0, 'act[1].energyRnd':1  },
         '3':{  'act[1].energy':1, 'act[1].energyRnd':0  },
         '4':{  'act[1].energy':1, 'act[1].energyRnd':1  },
         '5':{  'act[1].energy':2, 'act[1].energyRnd':0  },
      },
      'act':[
        {
          'priority':88850,
          'type': 0,  
          'src': 2,              #发出源 0前军，1后军，【2英雄】，-1全军
          'srcFree': 1,
          'tgt':[1, 0],    
          'round':{'1':1000,'5':1000,'9':1000,},    
          'costKey':'beastTypeF',       #充能来源类型 
          'cost':0,                 #使用 消耗充能
          'costMult':1,                 #多倍使用时 消耗充能
          'multMax':-1,              #一次使用的最大倍数
          'mult':{'dmgScale':400,'dmgLimit':100},                #多重施法时提升内容

          'nonSkill':1,    #非技能，不受傲气
          'noBfr':1,
          'noAft':1,

          'dmg':2600,							#技能伤害系数。文武、性别、兵种类型默认
          'dmgReal':260,						#真实伤害值
          'dmgLimit':320,
          'atk0': 520,    #攻击力源头(前军)
          'atk1': 500,    #攻击力源头(前军)

          'info':['_beastTypeF',4],
          'eff':'effBeastTypeF',
        },
        {     #战斗开始获取能量
          'priority':8600012,
          'type': 13,               #每个大回合重置阶段（无视速度）
          'src': 2,                #发出源 0前军，1后军，【2英雄】，-1全军
          'srcFree': 1, 
          'tgt':[0, -7],        #不计死亡的英雄    
          'round':{'0':1000},
          'nonSkill':1,    #非技能，不受傲气
          'noBuff':1,      #有此值时，不受dmgFinal、resFinal、战场dmgRealPer、resRealPer影响
          'noBfr':1,
          'noAft':1,

          'energyKey':'beastTypeF',       #充能标记类型 
          'energy':1,                 #获得充能标记数量 
          'energyRnd':1,              #获得充能标记随机数量 
          'info':['蛮牛·初始标记',0],
          'eff':'effNull',
          'time':0,	          #强制指定战报时间
        },
      ],
   },
   'beastResonanceF8':{ #蛮牛 兽灵8共鸣    每回合恢复标记
      'type':9,       #兽灵技
      'energyArr':['-|act[0].energy','-|act[0].energyRnd'],
      'passive':[
         {
            'rslt':{'power':500,'powerRate':150},
         },
         {
            'cond':['skill.beastResonanceF8', '*', 1],
            'rslt':{'powerRate':150},
         },
      ],
      'lvPatch':{   #满足特定等级的补丁
         '2':{  'act[0].energy':1, 'act[0].energyRnd':-1  },
         '3':{  'act[0].energy':1, 'act[0].energyRnd':0  },
         '4':{  'act[0].energy':2, 'act[0].energyRnd':-1  },
         '5':{  'act[0].energy':2, 'act[0].energyRnd':0  },
      },
      'act':[
        {     #大回合重置获取能量
          'priority':8600011,
          'type': 13,               #每个大回合重置阶段（无视速度）
          'src': 2,                #发出源 0前军，1后军，【2英雄】，-1全军
          'srcFree': 1, 
          'tgt':[0, -7],        #不计死亡的英雄   
          'round':{'all':1000},
          'nonSkill':1,    #非技能，不受傲气
          'noBuff':1,      #有此值时，不受dmgFinal、resFinal、战场dmgRealPer、resRealPer影响
          'noBfr':1,
          'noAft':1,

          'energyKey':'beastTypeF',       #充能标记类型 
          'energy':0,                 #获得充能标记数量 
          'energyRnd':1,              #获得充能标记随机数量 
          'info':['蛮牛·增补标记',0],
          'eff':'effNull',
          'time':0,	          #强制指定战报时间
        },
      ],
   },

   'beastResonanceG4':{ #玄龟 兽灵4共鸣   从远战首回合起每3回合，英雄行动前发动【玄龟守护】，为我方前军添加强度为200%的护盾。发动时若有玄龟标记将全部消耗，每个标记提升60%护盾强度
      'type':9,       #兽灵技
      'infoArr':['%|act[0].round.energy','-|act[0].removeDebuff','-|act[0].multMax*act[0].cost','-|act[0].cost'],
      'energyArr':['-|act[1].energy','-|act[1].energyRnd'],
      'passive':[
         {
            'rslt':{'power':300,'powerRate':100},
         },
         {
            'cond':['skill.beastResonanceG4', '*', 1],
            'rslt':{'powerRate':50},
         },
      ],
      'lvPatch':{   #满足特定等级的补丁
         '2':{  'act[1].energy':0, 'act[1].energyRnd':1  },
         '3':{  'act[1].energy':1, 'act[1].energyRnd':0  },
         '4':{  'act[1].energy':1, 'act[1].energyRnd':1  },
         '5':{  'act[1].energy':2, 'act[1].energyRnd':0  },
      },
      'act':[
        {
          'priority':88860,
          'type': 0,  
          'src': 2,              #发出源 0前军，1后军，【2英雄】，-1全军
          'srcFree': 1,
          'tgt':[0, 0],    
          'round':{'1':1000,'4':1000,'7':1000,'10':1000,},    
          'costKey':'beastTypeG',       #充能来源类型 
          'cost':0,                 #使用 消耗充能
          'costMult':1,              #多倍使用时 消耗充能
          'multMax':-1,              #一次使用的最大倍数
          'mult':{                   #多重施法时提升内容
            'buff.buffShield2.shield.value':80,
            'buff.buffShield2.shield.hpmRate':9,
          },               

          'nonSkill':1,    #非技能，不受傲气
          'noBuff':1,      #有此值时，不受dmgFinal、resFinal、战场dmgRealPer、resRealPer影响
          'noBfr':1,
          'noAft':1,

          'buff':{'buffShield2':{'shield':{'value':200,'hpmRate':32,'bearPoint':1000}}},

          'info':['_beastTypeG',4],
          'eff':'effBeastTypeG',
        },
        {     #战斗开始获取能量
          'priority':8600014,
          'type': 13,               #每个大回合重置阶段（无视速度）
          'src': 2,                #发出源 0前军，1后军，【2英雄】，-1全军
          'srcFree': 1, 
          'tgt':[0, -7],        #不计死亡的英雄   
          'round':{'0':1000},
          'nonSkill':1,    #非技能，不受傲气
          'noBuff':1,      #有此值时，不受dmgFinal、resFinal、战场dmgRealPer、resRealPer影响
          'noBfr':1,
          'noAft':1,

          'energyKey':'beastTypeG',       #充能标记类型 
          'energy':1,                 #获得充能标记数量 
          'energyRnd':1,              #获得充能标记随机数量 
          'info':['玄龟·初始标记',0],
          'eff':'effNull',
          'time':0,	          #强制指定战报时间
        },
      ],
   },
   'beastResonanceG8':{ #玄龟 兽灵8共鸣    每回合恢复标记
      'type':9,       #兽灵技
      'energyArr':['-|act[0].energy','-|act[0].energyRnd'],
      'passive':[
         {
            'rslt':{'power':500,'powerRate':150},
         },
         {
            'cond':['skill.beastResonanceG8', '*', 1],
            'rslt':{'powerRate':150},
         },
      ],
      'lvPatch':{   #满足特定等级的补丁
         '2':{  'act[0].energy':1, 'act[0].energyRnd':-1  },
         '3':{  'act[0].energy':1, 'act[0].energyRnd':0  },
         '4':{  'act[0].energy':2, 'act[0].energyRnd':-1  },
         '5':{  'act[0].energy':2, 'act[0].energyRnd':0  },
      },
      'act':[
        {     #大回合重置获取能量
          'priority':8600013,
          'type': 13,               #每个大回合重置阶段（无视速度）
          'src': 2,                #发出源 0前军，1后军，【2英雄】，-1全军
          'srcFree': 1, 
          'tgt':[0, -7],        #不计死亡的英雄  
          'round':{'all':1000},
          'nonSkill':1,    #非技能，不受傲气
          'noBuff':1,      #有此值时，不受dmgFinal、resFinal、战场dmgRealPer、resRealPer影响
          'noBfr':1,
          'noAft':1,

          'energyKey':'beastTypeG',       #充能标记类型 
          'energy':0,                 #获得充能标记数量 
          'energyRnd':1,              #获得充能标记随机数量 
          'info':['玄龟·增补标记',0],
          'eff':'effNull',
          'time':0,	          #强制指定战报时间
        },
      ],
   },

   'beastResonanceH4':{ #巨猿 兽灵4共鸣   从远战第二回合起每2回合，英雄行动前发动【巨猿憾地】，对敌方全体造成150%伤害。发动时若有巨猿标记将全部消耗，每个标记提升60%伤害强度
      'type':9,       #兽灵技
      'infoArr':['%|act[0].round.energy','-|act[0].removeDebuff','-|act[0].multMax*act[0].cost','-|act[0].cost'],
      'energyArr':['-|act[1].energy','-|act[1].energyRnd'],
      'passive':[
         {
            'rslt':{'power':300,'powerRate':100},
         },
         {
            'cond':['skill.beastResonanceH4', '*', 1],
            'rslt':{'powerRate':50},
         },
      ],
      'lvPatch':{   #满足特定等级的补丁
         '2':{  'act[1].energy':0, 'act[1].energyRnd':1  },
         '3':{  'act[1].energy':1, 'act[1].energyRnd':0  },
         '4':{  'act[1].energy':1, 'act[1].energyRnd':1  },
         '5':{  'act[1].energy':2, 'act[1].energyRnd':0  },
      },
      'act':[
        {
          'priority':88870,
          'type': 0,  
          'src': 2,              #发出源 0前军，1后军，【2英雄】，-1全军
          'srcFree': 1,
          'tgt':[1, -1],    
          'round':{'2':1000,'4':1000,'6':1000,'8':1000,'10':1000,'12':1000,},    
          'costKey':'beastTypeH',       #充能来源类型 
          'cost':0,                 #使用 消耗充能
          'costMult':1,                 #多倍使用时 消耗充能
          'multMax':-1,              #一次使用的最大倍数
          'mult':{'dmgScale':400,'dmgLimit':60},                #多重施法时提升内容

          'nonSkill':1,    #非技能，不受傲气
          'noBfr':1,
          'noAft':1,

          'dmg':1150,							#技能伤害系数。文武、性别、兵种类型默认
          'dmgReal':125,						#真实伤害值
          'dmgLimit':140,
          'atk0': 500,    #攻击力源头(前军)
          'atk1': 500,    #攻击力源头(前军)

          'info':['_beastTypeH',4],
          'eff':'effBeastTypeH',
        },
        {     #战斗开始获取能量
          'priority':8600016,
          'type': 13,               #每个大回合重置阶段（无视速度）
          'src': 2,                #发出源 0前军，1后军，【2英雄】，-1全军
          'srcFree': 1, 
          'tgt':[0, -7],        #不计死亡的英雄    
          'round':{'0':1000},
          'nonSkill':1,    #非技能，不受傲气
          'noBuff':1,      #有此值时，不受dmgFinal、resFinal、战场dmgRealPer、resRealPer影响
          'noBfr':1,
          'noAft':1,

          'energyKey':'beastTypeH',       #充能标记类型 
          'energy':1,                 #获得充能标记数量 
          'energyRnd':1,              #获得充能标记随机数量 
          'info':['巨猿·初始标记',0],
          'eff':'effNull',
          'time':0,	          #强制指定战报时间
        },
      ],
   },
   'beastResonanceH8':{ #巨猿 兽灵8共鸣    每回合恢复标记
      'type':9,       #兽灵技
      'energyArr':['-|act[0].energy','-|act[0].energyRnd'],
      'passive':[
         {
            'rslt':{'power':500,'powerRate':150},
         },
         {
            'cond':['skill.beastResonanceH8', '*', 1],
            'rslt':{'powerRate':150},
         },
      ],
      'lvPatch':{   #满足特定等级的补丁
         '2':{  'act[0].energy':1, 'act[0].energyRnd':-1  },
         '3':{  'act[0].energy':1, 'act[0].energyRnd':0  },
         '4':{  'act[0].energy':2, 'act[0].energyRnd':-1  },
         '5':{  'act[0].energy':2, 'act[0].energyRnd':0  },
      },
      'act':[
        {     #大回合重置获取能量
          'priority':8600015,
          'type': 13,               #每个大回合重置阶段（无视速度）
          'src': 2,                #发出源 0前军，1后军，【2英雄】，-1全军
          'srcFree': 1, 
          'tgt':[0, -7],        #不计死亡的英雄   
          'round':{'all':1000},
          'nonSkill':1,    #非技能，不受傲气
          'noBuff':1,      #有此值时，不受dmgFinal、resFinal、战场dmgRealPer、resRealPer影响
          'noBfr':1,
          'noAft':1,

          'energyKey':'beastTypeH',       #充能标记类型 
          'energy':0,                 #获得充能标记数量 
          'energyRnd':1,              #获得充能标记随机数量 
          'info':['巨猿·增补标记',0],
          'eff':'effNull',
          'time':0,	          #强制指定战报时间
        },
      ],
   },

   'beastResonanceI4':{ #猛虎 兽灵4共鸣   每个回合检测，若我方兵力比例不足10%+5%×当前猛虎标记数量，英雄行动前发动【猛虎掏心】，对敌方随机部队造成3次70%伤害。发动时若有猛虎标记将全部消耗，每个标记提升40%伤害强度
      'type':9,       #兽灵技
      'infoArr':['%|act[0].round.energy','-|act[0].removeDebuff','-|act[0].multMax*act[0].cost','-|act[0].cost'],
      'energyArr':['-|act[1].energy','-|act[1].energyRnd'],
      'passive':[
         {
            'rslt':{'power':300,'powerRate':100},
         },
         {
            'cond':['skill.beastResonanceI4', '*', 1],
            'rslt':{'powerRate':50},
         },
      ],
      'lvPatch':{   #满足特定等级的补丁
         '2':{  'act[1].energy':0, 'act[1].energyRnd':1  },
         '3':{  'act[1].energy':1, 'act[1].energyRnd':0  },
         '4':{  'act[1].energy':1, 'act[1].energyRnd':1  },
         '5':{  'act[1].energy':2, 'act[1].energyRnd':0  },
      },
      'act':[
        {
          'priority':88880,
          'type': 0,  
          'src': 2,              #发出源 0前军，1后军，【2英雄】，-1全军
          'srcFree': 1,
          'tgt':[1, -2],    
          'cond':[['hpPoint','<',100,'beastTypeI', 50]],
          'costKey':'beastTypeI',       #充能来源类型 
          'cost':0,                 #使用 消耗充能
          'costMult':1,                 #多倍使用时 消耗充能
          'multMax':-1,              #一次使用的最大倍数
          'mult':{'dmgScale':300,'dmgLimit':30},                #多重施法时提升内容

          'nonSkill':1,    #非技能，不受傲气
          'noBfr':1,
          'noAft':1,

          'dmg':600,							#技能伤害系数。文武、性别、兵种类型默认
          'dmgReal':70,						#真实伤害值
          'dmgLimit':70,	
          'atk0': 510,    #攻击力源头(前军)
          'atk1': 510,    #攻击力源头(前军)
          'combo':3,

          'info':['_beastTypeI',4],
          'eff':'effBeastTypeI',
        },
        {     #战斗开始获取能量
          'priority':8600018,
          'type': 13,               #每个大回合重置阶段（无视速度）
          'src': 2,                #发出源 0前军，1后军，【2英雄】，-1全军
          'srcFree': 1, 
          'tgt':[0, -7],        #不计死亡的英雄   
          'round':{'0':1000},
          'nonSkill':1,    #非技能，不受傲气
          'noBuff':1,      #有此值时，不受dmgFinal、resFinal、战场dmgRealPer、resRealPer影响
          'noBfr':1,
          'noAft':1,

          'energyKey':'beastTypeI',       #充能标记类型 
          'energy':1,                 #获得充能标记数量 
          'energyRnd':1,              #获得充能标记随机数量 
          'info':['猛虎·初始标记',0],
          'eff':'effNull',
          'time':0,	          #强制指定战报时间
        },
      ],
   },
   'beastResonanceI8':{ #猛虎 兽灵8共鸣    每回合恢复标记
      'type':9,       #兽灵技
      'energyArr':['-|act[0].energy','-|act[0].energyRnd'],
      'passive':[
         {
            'rslt':{'power':500,'powerRate':150},
         },
         {
            'cond':['skill.beastResonanceI8', '*', 1],
            'rslt':{'powerRate':150},
         },
      ],
      'lvPatch':{   #满足特定等级的补丁
         '2':{  'act[0].energy':1, 'act[0].energyRnd':-1  },
         '3':{  'act[0].energy':1, 'act[0].energyRnd':0  },
         '4':{  'act[0].energy':2, 'act[0].energyRnd':-1  },
         '5':{  'act[0].energy':2, 'act[0].energyRnd':0  },
      },
      'act':[
        {     #大回合重置获取能量
          'priority':8600017,
          'type': 13,               #每个大回合重置阶段（无视速度）
          'src': 2,                #发出源 0前军，1后军，【2英雄】，-1全军
          'srcFree': 1, 
          'tgt':[0, -7],        #不计死亡的英雄    
          'round':{'all':1000},
          'nonSkill':1,    #非技能，不受傲气
          'noBuff':1,      #有此值时，不受dmgFinal、resFinal、战场dmgRealPer、resRealPer影响
          'noBfr':1,
          'noAft':1,

          'energyKey':'beastTypeI',       #充能标记类型 
          'energy':0,                 #获得充能标记数量 
          'energyRnd':1,              #获得充能标记随机数量 
          'info':['猛虎·增补标记',0],
          'eff':'effNull',
          'time':0,	          #强制指定战报时间
        },
      ],
   },



   ################################兽灵共鸣技能结束， 副将技开始



   'adjutantH00':{ #前军武将副将 速战速决 近战第一回合，对敌方前军或后军造成50%的伤害。若敌方是全才，则改为远战第一回合释放
      'type':7,  #副将英雄技
      'cost_type':7,  #副将英雄技，满级125
      'infoArr':['r|act[0]','%|act[0].dmg','%|special[0].change.skill.$adjutantH00.act[0].dmg'],
      'nextArr':['%|act[0].dmg'],  
      'passive':[
         {
            'rslt':{'power':100,'powerRate':20},
         },
         {
            'cond':['skill.adjutantH00', '*', 1],
            'rslt':{'powerRate':4},
         },
      ],
      'up':{'act[0].dmg':10, 'act[0].dmgReal':2},
      'special':[
         {
            'cond':[['enemy','type',2]],  #对战全才
            'change':{
               'skill':{
                  'adjutantH00.act[0].round':{'1':1000},
                  'adjutantH00.act[0].dmg':500,
                  'adjutantH00.act[0].dmgReal':100,
                  'adjutantH00.act[0].isBurn':1,	                #文本燃烧
               },
            },
         },
      ],
      'act':[
         {
            'priority':-10,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 3,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, -2],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'3':1000},
            'cond':[['army',0]],
            'dmg':500,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':80,
            'dmgLimit':150,
            'noBfr':1,
            'element':'Adjutant',
            'atk0': 1000,
            'eff':'effH00',
            'info':['adjutantH00',2],	          #应该引用文字配置
         },
      ],
   },
   'adjutantH01':{ #前军文官副将 舞文弄墨 近战第一回合，对敌方前军或后军造成50%的伤害。若敌方是武将，则改为远战第一回合释放
      'type':7,  #副将英雄技
      'cost_type':7,  #副将英雄技，满级125
      'infoArr':['r|act[0]','%|act[0].dmg','%|special[0].change.skill.$adjutantH01.act[0].dmg'],
      'nextArr':['%|act[0].dmg'],  
      'passive':[
         {
            'rslt':{'power':100,'powerRate':20},
         },
         {
            'cond':['skill.adjutantH01', '*', 1],
            'rslt':{'powerRate':4},
         },
      ],
      'up':{'act[0].dmg':10, 'act[0].dmgReal':2},
      'special':[
         {
            'cond':[['enemy','type',0]],  #对战武将
            'change':{
               'skill':{
                  'adjutantH01.act[0].round':{'1':1000},
                  'adjutantH01.act[0].dmg':500,
                  'adjutantH01.act[0].dmgReal':100,
                  'adjutantH01.act[0].isBurn':1,	                #文本燃烧
               },
            },
         },
      ],
      'act':[
         {
            'priority':-10,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 3,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, -2],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'3':1000},
            'cond':[['army',0]],
            'dmg':500,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':80,
            'dmgLimit':150,
            'noBfr':1,
            'element':'Adjutant',
            'atk0': 1000,
            'eff':'effH01',
            'info':['adjutantH01',2],	          #应该引用文字配置
         },
      ],
   },
   'adjutantH02':{ #前军全才副将 犄角围攻 近战第一回合，对敌方前军或后军造成50%的伤害。若敌方是文官，则改为远战第一回合释放
      'type':7,  #副将英雄技
      'cost_type':7,  #副将英雄技，满级125
      'infoArr':['r|act[0]','%|act[0].dmg','%|special[0].change.skill.$adjutantH02.act[0].dmg'],
      'nextArr':['%|act[0].dmg'],  
      'passive':[
         {
            'rslt':{'power':100,'powerRate':20},
         },
         {
            'cond':['skill.adjutantH02', '*', 1],
            'rslt':{'powerRate':4},
         },
      ],
      'up':{'act[0].dmg':10, 'act[0].dmgReal':2},
      'special':[
         {
            'cond':[['enemy','type',1]],  #对战文官
            'change':{
               'skill':{
                  'adjutantH02.act[0].round':{'1':1000},
                  'adjutantH02.act[0].dmg':500,
                  'adjutantH02.act[0].dmgReal':100,
                  'adjutantH02.act[0].isBurn':1,	                #文本燃烧
               },
            },
         },
      ],
      'act':[
         {
            'priority':-10,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 3,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, -2],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'3':1000},
            'cond':[['army',0]],
            'dmg':500,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':80,
            'dmgLimit':150,
            'noBfr':1,
            'element':'Adjutant',
            'atk0': 1000,
            'eff':'effH02',
            'info':['adjutantH02',2],	          #应该引用文字配置
         },
      ],
   },
   'adjutantH10':{ #后军武将副将 穿云之箭 远战第二回合，对敌方前军或后军造成50%的伤害，并有20%几率对另一部队也能造成同等伤害
      'type':7,  #副将英雄技
      'cost_type':7,  #副将英雄技，满级125
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[1].round.all'],
      'nextArr':['%|act[0].dmg','%|act[1].round.all'],  
      'passive':[
         {
            'rslt':{'power':100,'powerRate':20},
         },
         {
            'cond':['skill.adjutantH10', '*', 1],
            'rslt':{'powerRate':4},
         },
      ],
      'up':{'act[0].dmg':10, 'act[0].dmgReal':2, 'act[1].round.all':4},
      'act':[
         {
            'priority':-10,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 4,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, -2],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'2':1000},
            'cond':[['army',1]],
            'dmg':500,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':80,
            'dmgLimit':150,
            'noBfr':1,
            'element':'Adjutant',
            'atk1': 1000,    #攻击力源头(后军)
            'eff':'effH10',
            'info':['adjutantH10',2],	          #应该引用文字配置
         },
         {
            'priority':9999,
            'type': 2,
            'src': 4,	               #发出源 0前军，1后军，【2英雄】，-1全军
            'round':{'all':300},
            'info':['穿云',0],	          #应该引用文字配置
            'follow':'adjutantH10',           #必须跟随特定行动触发
            'times':-1,  
            'nonSkill':1,    #非技能，不受傲气影响
            'binding':{ 'tgt':[1, -1],},
         },
      ],
   },
   'adjutantH11':{ #后军文官副将 锦囊妙计 远战第二回合，对敌方前军或后军造成50%的伤害。并提升我方全军攻防4%，持续2回合
      'type':7,  #副将英雄技
      'cost_type':7,  #副将英雄技，满级125
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[1].buff.buffPlan.prop.atkRate*2'],
      'nextArr':['%|act[0].dmg','%|act[1].buff.buffPlan.prop.atkRate*2'],  
      'passive':[
         {
            'rslt':{'power':100,'powerRate':20},
         },
         {
            'cond':['skill.adjutantH11', '*', 1],
            'rslt':{'powerRate':4},
         },
      ],
      'up':{'act[0].dmg':10, 'act[0].dmgReal':2, 'act[1].buff.buffPlan.prop.atkRate':0.3, 'act[1].buff.buffPlan.prop.defRate':0.3},
      'act':[
         {
            'priority':-10,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 4,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, -2],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'2':1000},
            'cond':[['army',1]],
            'dmg':500,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':80,
            'dmgLimit':150,
            'noBfr':1,
            'element':'Adjutant',
            'atk1': 1000,    #攻击力源头(后军)
            'eff':'effH11',
            'info':['adjutantH11',2],	          #应该引用文字配置
         },
         {   #锦囊
            'priority':960,
            'type': 3,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 4,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[0, -1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'buff':{'buffPlan':{'round':2,'prop':{'atkRate':10,'defRate':15}}},
            'info':['锦囊',0],	          #应该引用文字配置
            'time':100,	          #强制指定战报时间
            'times':-1,  
            'nonSkill':1,    #非技能，不受傲气影响
            'noBfr':1,
            'noAft':1,
            'eff':'effNull',
            'follow':'adjutantH11',            #必须跟随特定行动触发
         },
      ],
   },
   'adjutantH12':{ #后军全才副将 山崩地裂飞火流星 远战第二回合，对敌方前军或后军造成50%的伤害，并有15%几率造成混乱，持续1回合
      'type':7,  #副将英雄技
      'cost_type':7,  #副将英雄技，满级125
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[0].buff.buffStun.rnd'],
      'nextArr':['%|act[0].dmg','%|act[0].buff.buffStun.rnd'],  
      'passive':[
         {
            'rslt':{'power':100,'powerRate':20},
         },
         {
            'cond':['skill.adjutantH12', '*', 1],
            'rslt':{'powerRate':4},
         },
      ],
      'up':{'act[0].dmg':10, 'act[0].dmgReal':2, 'act[0].buff.buffStun.rnd':3},
      'act':[
         {
            'priority':-10,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 4,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, -2],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'2':1000},
            'cond':[['army',1]],
            'dmg':500,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':80,
            'dmgLimit':150,
            'noBfr':1,
            'element':'Adjutant',
            'atk1': 1000,    #攻击力源头(后军)
            'eff':'effH12',
            'buff':{'buffStun':{'rnd':150, 'round':1}},
            'info':['adjutantH12',2],	          #应该引用文字配置
         },
      ],
   },


   'adjutantH770':{ #神·汉献帝      作为前军副将时，副将主动技锁定为[【王者风范】]，远战第二回合发动【王者风范】，以我方前军最大兵力作为攻击，对敌方前军造成伤害，并为我方前军添加强力护盾。护盾存在期间，受到不良状态的几率减少20%
      'type':7,  #副将英雄技
      'cost_type':7,  #副将英雄技，满级125
      #'icon':'item433',
      'infoArr':['%|act[0].dmg','%|act[1].buff.buffShield3.shield.value*5'],
      'nextArr':['%|act[0].dmg','%|act[1].buff.buffShield3.shield.value*5'],  
      'passive':[
         {
            'rslt':{'power':100,'powerRate':40},
         },
         {
            'cond':['skill.adjutantH770', '*', 1],
            'rslt':{'powerRate':4.5},
         },
      ],
      'up':{'act[0].dmg':15, 'act[0].dmgReal':8, 'act[1].buff.buffShield3.shield.value':2, 'act[1].buff.buffShield3.shield.hpmRate':0.2},
      'act':[
         {
            'priority':9997700,
            'type': 1,
            'src': 3,
            'tgt':[1, 0],
            'round':{'2':1000},
            'allTimes': 1,
            'cond':[['army',0]],
           #'nonSkill':1,
            'noBfr':1,
            'element':'Adjutant',
            'dmg':750,
            'dmgReal':160,
            'dmgLimit':180,	  #限制对单目标的杀伤千分点，溢出衰减
            #'block':-100000,
            #'ignDef':200,
            'atk': {'armys[0].hpm':1.2,'armys[0].atk':0.2},  #类似special中change格式的取得部队数据v，与m相乘加到本次技能的攻击力中
            #'atk0': 750,
            #'atk1': 300,
            'eff':'eff770a',
            'info':['adjutantH770',2],
         },
         {
            'priority':9997701,
            'type': 3,
            'src': 3,
            'tgt':[0, 0],
            'cond':[['army',0]],
            'nonSkill':1,
            'noBfr':1,
            'noAft':1,
            'eff':'eff770a2',

            'buff':{'buffShield3':{'shield':{'value':200,'hpmRate':20,'bearPoint':1000},'prop':{'deBuffRate':-200}}},
            'follow':'adjutantH770',
            'info':['王者护盾',0],
         },
      ],
   },


   'adjutantH782':{  #马忠 作为弓兵副将时，副将主动技锁定为【暗箭难防】，当敌方兵力低于一半时可发动，对敌后军造成无视防御的伤害，并有30%几率斩杀敌方英雄
      'type':7,  #副将英雄技
      'cost_type':7,  #副将英雄技，满级125
      'infoArr':['%|act[0].dmg'],
      'nextArr':['%|act[0].dmg'],  
      'passive':[
         {
            'rslt':{'power':100,'powerRate':30},
         },
         {
            'cond':['skill.adjutantH782', '*', 1],
            'rslt':{'powerRate':4.2},
         },
      ],
      'special':[{  #判断马忠在主将还是哪个副将位，变更发起源
          'cond':[['adjutant', 'hero782', 0, 1]],
          'priority':-0.5,
          'change':{
              'skill':{
                  'adjutantH782.act[0].src':'$4',
                  'adjutantH782.act[1].src':'$4',
              },
          },
      }],
      'up':{'act[0].dmg':15, 'act[0].dmgReal':10},
      'act':[
         {
            'priority':-99997820,
            'type': 1,
            'tgt':[1, 1],
            'cond':[['enemyHpPoint','<',500],['army',1,2]],
            'allTimes': 1,
            #'nonSkill':1,
            'noBfr':1,
            'element':'Adjutant',
            'dmg':800,
            'dmgReal':150,
            'dmgLimit':270,	  #限制对单目标的杀伤千分点，溢出衰减
            'ignDef':1000,
            'atk1': 1000,
            'eff':'effE782',              
            'info':['adjutantH782',2],
         },
         {
            'priority':99997821,
            'type': 2,
            'round':{'any':300},
            'follow':'adjutantH782',
            'times':-1,  
            'nonSkill':1,    #非技能，不受傲气影响
            'binding':{'tgtHero':1,'dmgScale':100},
            'info':['暗箭斩落',0],
         },
      ],
   },
   'adjutantH785':{ #陈寿后军副将技  史策丹心  副将主动技能锁定为[【史策丹心】]，己方兵力低于一半时发动，以己方前后军防御总和作为攻击，对敌方全体造成大量伤害（史册）
      'type':7,  #副将英雄技
      'cost_type':7,  #副将英雄技，满级125
      #'icon':'item433',
      'infoArr':['%|act[0].dmg*1.2'],
      'nextArr':['%|act[0].dmg*1.2'],  
      'passive':[
         {
            'rslt':{'power':100,'powerRate':40},
         },
         {
            'cond':['skill.adjutantH785', '*', 1],
            'rslt':{'powerRate':4.5},
         },
      ],
      'up':{'act[0].dmg':10, 'act[0].dmgReal':6},
      'act':[
         {
            'priority':-10,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 4,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, -1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'any':1000},
            'cond':[['hpPoint','<',500]],
            'allTimes': 1,
            'times': 1,
            #'nonSkill':1,
            'noBfr':1,
            'element':'Adjutant',
            'dmg':1000,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':600,  
            'dmgLimit':200,	  #限制对单目标的杀伤千分点，溢出衰减
            'atk': {'armys[0].atk':0.1,'armys[1].atk':0.1,'armys[0].def':0.7,'armys[1].def':0.75},  #类似special中change格式的取得部队数据v，与m相乘加到本次技能的攻击力中
            'eff':'effH785',
            'info':['adjutantH785',2],	          #应该引用文字配置
         },
      ],
   },
   'adjutantH788':{ #黄承彦 作为方士副将时，副将主动技锁定为[【洪荒八阵】]，近战首回合发动，对敌方全军造成伤害，并有20%的几率使之【混乱】1回合。命中后若敌方有目标处于不良状态，可再额外发动1次【洪荒八阵】；
      'type':7,  #副将英雄技
      'cost_type':7,  #副将英雄技，满级125
      #'icon':'item433',
      'infoArr':['%|act[0].dmg'],
      'nextArr':['%|act[0].dmg'],  
      'passive':[
         {
            'rslt':{'power':100,'powerRate':40},
         },
         {
            'cond':['skill.adjutantH788', '*', 1],
            'rslt':{'powerRate':4.5},
         },
      ],
      'special':[{  #判断黄承彦在主将还是哪个副将位，变更发起源
          'cond':[['adjutant', 'hero788', 0, 1]],
          'priority':-0.5,
          'change':{
              'skill':{
                  'adjutantH788.act[0].src':'$4',
                  'adjutantH788.act[1].src':'$4',
                  'adjutantH788.act[2].src':'$4',
              },
          },
      }],
      'up':{'act[0].dmg':7, 'act[0].dmgReal':4,  'act[1].dmg':7, 'act[1].dmgReal':4},
      'act':[
         {
            'priority':9997880,
            'type': 1,
            'tgt':[1, -1],
            'round':{'3':1000},
            'allTimes': 2,
            'cond':[['army',1,3]],
            #'nonSkill':1,
            'noBfr':1,
            'element':'Adjutant',
            'buff':{'buffStun':{'rnd':200, 'round':1},'buffFire':{'rnd':0, 'round':1}},    #原值200
            'dmg':450,
            'dmgReal':100,
            'dmgLimit':90,	  #限制对单目标的杀伤千分点，溢出衰减
            'atk1': 1000,
            'eff':'effE788',                #特效暂时用的飞火流星
            'info':['adjutantH788',2],
         },
         {
            'priority':9997881,
            'type': 3,
            'tgt':[1, -1],
            'cond':[['checkBuff', 1, -1, 2, '>', 0]],  #敌方 全军 不良 大于0
            #'nonSkill':1,
            'noBfr':1,
            'element':'Adjutant',
            'buff':{'buffStun':{'rnd':200, 'round':1},'buffFire':{'rnd':0, 'round':1}},      #原值200
            'dmg':450,
            'dmgReal':100,
            'dmgLimit':90,	  #限制对单目标的杀伤千分点，溢出衰减
            'atk1': 1000,
            'eff':'effE788',                #特效暂时用的飞火流星
            'follow':'adjutantH788',
            'actId':'adjutantH788_1',
            'info':['adjutantH788',2],
         },
         {
            'priority':9997882,
            'type': 2,
            'cond':[['comp','sum']],
            'info':['洪荒八阵：四维胜出',0],	          #应该引用文字配置
            'times': -1,    #每回合最多使用次数
            'nonSkill':1,    #非技能，不受傲气
            'binding':{'buff.buffFire.rnd':500},    #火灾               #原值500
            'follow':{'adjutantH788':1,'adjutantH788_1':1},            #必须跟随特定行动触发
         },
      ],
   },


    'adjutantE788_1':{  #神·黄承彦     作为副将时，若敌方激活的阵法与我方相同，造成的伤害+20%；作为副将时，主将四维各+1；
        'special':[{
           'cond':[['compareTroop', 'formationType', '=']],
           'change':{
              'skill':{
                 'adjutantE788_1':{
                   'act':[{
                      'priority':1001,
                      'type':2,
                      'src':-2,
                      'nonSkill': 1, 
                      'binding':{
                         'dmg':200,
                         'dmgScale':150,
                      },
                      'info':['同阵增伤',0],
                   }],
                 },
              },
           },
        }],
    },






   'adjutantA0':{ #副将技能 步兵强击 每次攻击都有{0}的几率，额外造成{1}点伤害
      'type':8,  #副将兵种技
      'cost_type':8,  #副将兵种技，满级250
      'infoArr':['%|act[0].round.all','-|act[0].binding.dmgReal*2'],
      'nextArr':['%|act[0].round.all','-|act[0].binding.dmgReal*2'],
      'passive':[
         {
            'rslt':{'power':100,'powerRate':10},
         },
         {
            'cond':['skill.adjutantA0', '*', 1],
            'rslt':{'powerRate':2},
         },
      ],
      'up':{'act[0].round.all':1, 'act[0].binding.dmgReal':0.2},
      'act':[
         {
            'priority':9999,
            'type': 2,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 0,       #发出源 0前军，1后军，2英雄，-1全军，-2全军含英雄
            'times': -1,    #每回合最多使用次数
            'round':{'all':100},
            'cond':['not',[['srcTgtTeam',0],['srcTgtArmy',-5]]],     #攻击目标不为自己
            'follow':{'keys':{'dmg':1000,'dmgReal':1000}},
            'binding':{
               'dmgReal':20,
            },
            'info':['adjutantA',1],	          #[0]应该引用文字配置，[1]为显示模式，0只用于打印，1word，2banner，3fate。[2]由程序自动补足等级
         },
      ],
   },
   'adjutantA1':{ #副将技能 骑兵强击 每次攻击都有{0}的几率，额外造成{1}点伤害
      'type':8,  #副将兵种技
      'cost_type':8,  #副将兵种技，满级250
      'infoArr':['%|act[0].round.all','-|act[0].binding.dmgReal*2'],
      'nextArr':['%|act[0].round.all','-|act[0].binding.dmgReal*2'],
      'passive':[
         {
            'rslt':{'power':100,'powerRate':10},
         },
         {
            'cond':['skill.adjutantA1', '*', 1],
            'rslt':{'powerRate':2},
         },
      ],
      'up':{'act[0].round.all':1, 'act[0].binding.dmgReal':0.2},
      'act':[
         {
            'priority':9999,
            'type': 2,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 0,       #发出源 0前军，1后军，2英雄，-1全军，-2全军含英雄
            'times': -1,    #每回合最多使用次数
            'round':{'all':100},
            'cond':['not',[['srcTgtTeam',0],['srcTgtArmy',-5]]],     #攻击目标不为自己
            'follow':{'keys':{'dmg':1000,'dmgReal':1000}},
            'binding':{
               'dmgReal':20,
            },
            'info':['adjutantA',1],	          #[0]应该引用文字配置，[1]为显示模式，0只用于打印，1word，2banner，3fate。[2]由程序自动补足等级
         },
      ],
   },
   'adjutantA2':{ #副将技能 弓兵强击 每次攻击都有{0}的几率，额外造成{1}点伤害
      'type':8,  #副将兵种技
      'cost_type':8,  #副将兵种技，满级250
      'infoArr':['%|act[0].round.all','-|act[0].binding.dmgReal*2'],
      'nextArr':['%|act[0].round.all','-|act[0].binding.dmgReal*2'],
      'passive':[
         {
            'rslt':{'power':100,'powerRate':10},
         },
         {
            'cond':['skill.adjutantA2', '*', 1],
            'rslt':{'powerRate':2},
         },
      ],
      'up':{'act[0].round.all':1, 'act[0].binding.dmgReal':0.2},
      'act':[
         {
            'priority':9999,
            'type': 2,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 1,       #发出源 0前军，1后军，2英雄，-1全军，-2全军含英雄
            'times': -1,    #每回合最多使用次数
            'round':{'all':100},
            'cond':['not',[['srcTgtTeam',0],['srcTgtArmy',-5]]],     #攻击目标不为自己
            'follow':{'keys':{'dmg':1000,'dmgReal':1000}},
            'binding':{
               'dmgReal':20,
            },
            'info':['adjutantA',1],	          #[0]应该引用文字配置，[1]为显示模式，0只用于打印，1word，2banner，3fate。[2]由程序自动补足等级
         },
      ],
   },
   'adjutantA3':{ #副将技能 方士强击 每次攻击都有{0}的几率，额外造成{1}点伤害
      'type':8,  #副将兵种技
      'cost_type':8,  #副将兵种技，满级250
      'infoArr':['%|act[0].round.all','-|act[0].binding.dmgReal*2'],
      'nextArr':['%|act[0].round.all','-|act[0].binding.dmgReal*2'],
      'passive':[
         {
            'rslt':{'power':100,'powerRate':10},
         },
         {
            'cond':['skill.adjutantA3', '*', 1],
            'rslt':{'powerRate':2},
         },
      ],
      'up':{'act[0].round.all':1, 'act[0].binding.dmgReal':0.2},
      'act':[
         {
            'priority':9999,
            'type': 2,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 1,       #发出源 0前军，1后军，2英雄，-1全军，-2全军含英雄
            'times': -1,    #每回合最多使用次数
            'round':{'all':100},
            'cond':['not',[['srcTgtTeam',0],['srcTgtArmy',-5]]],     #攻击目标不为自己
            'follow':{'keys':{'dmg':1000,'dmgReal':1000}},
            'binding':{
               'dmgReal':20,
            },
            'info':['adjutantA',1],	          #[0]应该引用文字配置，[1]为显示模式，0只用于打印，1word，2banner，3fate。[2]由程序自动补足等级
         },
      ],
   },

   'adjutantE786':{ #紫虚上人额外技能  问卦  作为副将时，战斗开始时额外发动[【问卦】]，吉凶概率各50%。若为吉卦，敌方前军立即损失10%当前兵力。若为凶卦，己方前军立即损失5%当前兵力；若主将战力高于对手，【问卦】吉卦概率+25%；若主将四维总数高于对手，【问卦】吉卦概率+25%；自身作为主将时，也可发动【问卦】，且损兵效果提升至3倍
      'special':[
          {  #判断紫虚上人在主将还是哪个副将位，变更问卦主体
            'cond':[['adjutant', 'hero786', 0,0]],
            'priority':-0.5,
            'change':{
               'skill':{
                  'adjutantE786.act[0].src':'$3',
                  'adjutantE786.act[1].src':'$3',
                  'adjutantE786.act[2].src':'$3',
                  'adjutantE786.act[3].src':'$3',
               },
            },
          },
          {  #判断紫虚上人在主将还是哪个副将位，变更问卦主体
            'cond':[['adjutant', 'hero786', 0,1]],
            'priority':-0.5,
            'change':{
               'skill':{
                  'adjutantE786.act[0].src':'$4',
                  'adjutantE786.act[1].src':'$4',
                  'adjutantE786.act[2].src':'$4',
                  'adjutantE786.act[3].src':'$4',
               },
            },
          },
          {             #skillPatch中暂时未生效special 所以要加2遍
            'cond':[['compare','power','>']],
            'change':{
               'skill':{
                  'adjutantE786.act[1].round.0':250,
               },
            },
          },
          {  
            'cond':[['compare','sum','>']],
            'change':{
               'skill':{
                  'adjutantE786.act[1].round.0':250,
               },
            },
          },
      ],
      'act':[
         {   #问卦
            'priority': 10000001,
            'type': 0,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'tgt':[1, 2],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'0':1000},
            'nonSkill':1,
            'noBfr':1,  
            'noAft':1,  
            'noBuff':1,    
            'lv':15,
            'eff':'effE786',
            'info':['adjutantE786_',2],	          #应该引用文字配置
         },
         {   #问卦绑定标记
            'priority': 10000002,
            'type': 2,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'round':{'0':500},
            'nonSkill':1, 
            'noBfr':1,  
            'noAft':1,  
            'noBuff':1,
            'follow':'adjutantE786_',  
            'binding':{'buff': {'buff786':{}}}, 
            'info':['定卦',0],	          #应该引用文字配置
         },
         {     #吉卦
            'priority': 10000000,
            'type': 0,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'tgt':[1, 0],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'0':1000},
            'cond':[['checkBuff', 1, 2, 'buff786', '>', 0],],
            'nonSkill':1,
            'noBfr':1,  
            'noAft':1,  
            'noBuff':1,
            'lv':15,
            'dmgRealHp0':100,   
            'dmgRealHp1':100, 
            'eff':'effE786a',
            'info':['adjutantE786_0',2],	          #应该引用文字配置
         },
         {     #凶卦
            'priority': 9999999,
            'type': 0,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'tgt':[0, 0],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'0':1000},
            'cond':[['checkBuff', 1, 2, 'buff786', '=', 0],],
            'nonSkill':1,
            'noBfr':1,  
            'noAft':1,  
            'noBuff':1,
            'lv':1,  
            'dmgRealHp0':50,   
            'dmgRealHp1':50, 
            'eff':'effE786a',
            'info':['adjutantE786_1',2],	          #应该引用文字配置
         },
      ],
   },





      'adjutantE793':{  #刘邦副将   作为副将，且敌方主将为男性时，可额外发动【急脱鸿门】：【急脱鸿门】：我方受到伤害后若兵力低于25%立即发动，恢复我方全军5%的最大兵力，并移除全部不良状态（每场对决限一次）；若我方战力低于对手，发动【急脱鸿门】时可额外恢复我方全军10%的最大兵力；
      'special':[
          {  
             'priority':307933.1,
             'cond':[['compare','power','<']],
             'change':{
                'skill':{
                   'adjutantE793.act[0].order.summon':100,
                   'adjutantE793.act[0].order.summonReal':30,
               },
             },
           },
           {
              'priority':307933.5,
              'cond':[['adjutant', 'hero793', 0,0]],
              'change':{
                 'skill':{
                   'adjutantE793.act[0].order.src':'$3',
                },
              },
           },
           {
              'priority':307934.5,
              'cond':[['adjutant', 'hero793', 0,1]],
              'change':{
                 'skill':{
                   'adjutantE793.act[0].order.src':'$4',
                },
              },
           },
           {
              'priority':307936,
              'cond':[['enemy', 'sex', 0]],
              'change':{
                  'skill':{'adjutantE793':'del'},
              },
           },
      ],
      'act':[
        {
            'priority':307930,
            'type': 23,	            #触发类型，23兵力变化
            'src':-1,
            'condHpChange':[['<',0],None,['<',0.25]],  #兵力受损后全军兵力不足25%
            'order':{        
               'src': 2,
               'tgt':[0, -6],
               'nonSkill':1,
               'noBfr':1,
               'noAft':1,
               'summon':50,           #召唤伤兵千分点
               'summonReal':10,  
               'removeDebuff':100,     
               'info':['adjutantE793',2],
               'lv':30,
               'isBurn':1,
               'eff':'effH793',
               'time':100,  
            },
            'limitTimes': 1,
            'allTimes': 1,
            'noBfr': 1,
            'noAft': 1, 
            'nonSkill':1,
            'info':['急脱',0],
            'eff':'effNull',
            'time':0,     
        },
      ],
      },

      'adjutantE793a':{  #神·刘邦副将   作为副将时，主将前后军的攻击，可无视目标10%防御；【急脱鸿门】发动时，我方全军每存在1种不良状态，【急脱鸿门】的恢复效果+20%；
        'special':[
           {  
             'change':{
                'prop':{
                   'armys[0].ignDef':80,
                   'armys[1].ignDef':80,
               },
             },
           },
           {
              'priority':307937.5,
              'cond':[['adjutant', 'hero793', 0,0]],
              'change':{
                 'skill':{
                   'adjutantE793a.act[0].src':'$3',
                   'adjutantE793a.act[1].src':'$3',
                   'adjutantE793a.act[2].src':'$3',
                   'adjutantE793a.act[3].src':'$3',
                   'adjutantE793a.act[4].src':'$3',
                },
              },
           },
           {
              'priority':307938.5,
              'cond':[['adjutant', 'hero793', 0,1]],
              'change':{
                 'skill':{
                   'adjutantE793a.act[0].src':'$4',
                   'adjutantE793a.act[1].src':'$4',
                   'adjutantE793a.act[2].src':'$4',
                   'adjutantE793a.act[3].src':'$4',
                   'adjutantE793a.act[4].src':'$4',
                },
              },
           },
        ],
        'act':[
          {
            'priority':307931,
            'type': 2,	            #攻前
            'src':2,
            'cond':[['checkProp','selfTroop.buffStatistics.buffTypeSum.2', '=', 1]],
            'times': -1,
            'noBfr': 1,
            'noAft': 1, 
            'nonSkill':1,
            'binding':{'summonScale':200},
            'follow':'adjutantE793',
            'lv':1,
            'info':['急脱加成1',0],
          },
          {
            'priority':307932,
            'type': 2,	            #攻前
            'src':2,
            'cond':[['checkProp','selfTroop.buffStatistics.buffTypeSum.2', '=', 2]],
            'times': -1,
            'noBfr': 1,
            'noAft': 1, 
            'nonSkill':1,
            'binding':{'summonScale':400},
            'follow':'adjutantE793',
            'lv':50,
            'info':['急脱加成2',0],
          },
          {
            'priority':307933,
            'type': 2,	            #攻前
            'src':2,
            'cond':[['checkProp','selfTroop.buffStatistics.buffTypeSum.2', '=', 3]],
            'times': -1,
            'noBfr': 1,
            'noAft': 1, 
            'nonSkill':1,
            'binding':{'summonScale':600},
            'follow':'adjutantE793',
            'lv':100,
            'info':['急脱加成3',0],
          },
          {
            'priority':307934,
            'type': 2,	            #攻前
            'src':2,
            'cond':[['checkProp','selfTroop.buffStatistics.buffTypeSum.2', '=', 4]],
            'times': -1,
            'noBfr': 1,
            'noAft': 1, 
            'nonSkill':1,
            'binding':{'summonScale':800},
            'follow':'adjutantE793',
            'lv':150,
            'info':['急脱加成4',0],
          },
          {
            'priority':307935,
            'type': 2,	            #攻前
            'src':2,
            'cond':[['checkProp','selfTroop.buffStatistics.buffTypeSum.2', '>=', 5]],
            'times': -1,
            'noBfr': 1,
            'noAft': 1, 
            'nonSkill':1,
            'binding':{'summonScale':1000},
            'follow':'adjutantE793',
            'lv':250,
            'info':['急脱加成5',0],
          },
        ],
      },

      'adjutantE7001':{  #张仲景副将   作为前军副将时，前军可额外豁免1次会导致全灭的伤害，且首次豁免后发动【医圣在世】，治疗前军20%伤兵
      'special':[
           {
              'priority':307001,
              'cond':[['adjutant', 'hero7001', 0,0]],
              'change':{
                  'prop':{
                      'armys[0].stamina':1,
                   },
               },
           },
      ],
      'act':[
        {
            'priority':307001,
            'type': 28,	          
            'src':0,
            'allTimes': 1,
            'order':{        
               'src': 3,
               'tgt':[0, -9],
               'nonSkill':1,
               'noBfr':1,
               'noAft':1,
               'cure':200,           #召唤伤兵千分点
               'cureReal':10,  
               'info':['hero7001',2],
               'lv':30,
               'isBurn':1,
               'eff':'effH7001',
               'time':100,  
            },
            'nonSkill':1,
            'noBfr':1,
            'noAft':1,
            'eff':'effNull',
            'time': 0,
            'info':['首次硬抗',0],
        },
      ],
      },


    'adjutantE7001_1':{  #神·张仲景副将     作为前军副将时，我方治疗和增援兵力的效果+10%；
       'special':[{
          'change':{
             'prop':{
                 'armys[0].others.elementCure':100,  
                 'armys[1].others.elementCure':100,
                 'armys[0].others.elementSummon':100,
                 'armys[1].others.elementSummon':100,
             },
          },
       }],
    },


   'adjutantE7004':{  #邓艾副将   【屯田蓄资】：近战首回合开始时发动，治疗我方剩余兵力较少的部队33%伤兵，并提升此部队15%伤害和免伤，保持于本次对决且无法被驱散
      'special':[
          {  #变更主体
            'cond':[['adjutant', 'hero7004', 0,0]],
            'change':{
               'skill':{
                  'adjutantE7004.act[0].src':3,
               },
            },
          },
          {  #变更主体
            'cond':[['adjutant', 'hero7004', 0,1]],
            'change':{
               'skill':{
                  'adjutantE7004.act[0].src':4,
               },
            },
          },
      ],
      'act':[
        {
            'priority':4000020,
            'type': 13,
            'round':{'3':1000},	
            'tgt':[0, {'key':'hpPer'}], 
            'limitTimes': 1,
            'allTimes': 1,
            'cure':330,
            'cureReal':1,
            'element':'Adjutant',
            'noBfr': 1,
            'noAft': 1, 
            'nonSkill':1,
            'buff':{'buff7004':{'rnd':1000}, 'buff7004_1':{'rnd':0}},
            'info':['hero7004',2],
            'eff':'eff7004',
            'lv':17,
            'time':100,     
        },
      ],
   },


      'adjutantE7004a':{  #神·邓艾副将   作为副将时，主将近战回合前免伤+15%
        'special':[
           {  
             'change':{
                'prop':{
                      'armys[0].others.roundRes_0':150,
                      'armys[1].others.roundRes_0':150,
                      'armys[0].others.roundRes_1':150,
                      'armys[0].others.roundRes_2':150,
                      'armys[1].others.roundRes_1':150,
                      'armys[1].others.roundRes_2':150,
               },
             },
           },
        ],
      },

  #################宿命技 开始

   'fate7011':{  #马超 裸衣酣战 必杀
      'act':[
         {
            'priority': 300,
            'type': 0,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, 0],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'0':1000},
            'cond':[['fate', 'id', 'hero702']],
            'dmg':600,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':85,
            'atk0': 500,      #攻击力源头(前军)
            'atk1': 500,    #攻击力源头(后军)
            'noAft':1,
            'eff':'eff7011',
            'info':['fate7011',3],	          #应该引用文字配置
         },
      ],
   },
   'fate7021':{  #举火鏖战
      'special':[{
            'cond':[['fate', 'id', 'hero701']],
            'change':{
               'skill':{'skill289.act[0].dmg':200, 'skill289.act[0].dmgReal':15},
            },
      }],
   },
   'fate7031':{  #意乱情迷
      'special':[{
            'cond':[['fate', 'id', 'hero708']],
            'change':{
               'skill':{'skill230.act[0].ignDef':150, 'skill230.act[0].dmgReal':5},
            },
      }],
   },
   'fate7041':{  #伉俪情深
      'special':[{
            'cond':[['fate', 'id', 'hero716']],
            'change':{
               'skill':{'skill209.act[0].dmg':200, 'skill209.act[0].dmgReal':15},
            },
      }],
   },
   'fate7051':{  #黄月英 机巧大师 必杀
      'act':[
         {
            'priority': 300,
            'type': 0,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, -1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'0':1000},
            'cond':[['fate', 'id', 'hero714']],
            'dmg':500,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':180,
            'buff':{'buffFaction':{'rnd':250, 'round':2}},
            'atk0': 500,      #攻击力源头(前军)
            'atk1': 500,    #攻击力源头(后军)
            'noAft':1,
            'eff':'eff7051',
            'info':['fate7051',3],	          #应该引用文字配置
         },
      ],
   },
   'fate7061':{  #碧盔银甲
      'special':[{
            'cond':[['fate', 'id', 'hero716']],
            'change':{
               'skill':{'skill226.act[0].dmg':300, 'skill226.act[0].dmgReal':25},
            },
      }],
   },
   'fate7071':{  #司马懿 军师联盟 必杀
      'act':[
         {
            'priority': 400,
            'type': 0,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, 1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'0':1000},
            'cond':[['fate', 'type', 1]],   #['fate', 'ids', ['hero715','hero717','hero709']]
            'dmg':850,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':250,
            'buff':{'buffFaction':{'rnd':400, 'round':1}},
            'atk0': 500,      #攻击力源头(前军)
            'atk1': 500,    #攻击力源头(后军)
            'noAft':1,
            'eff':'eff7071',
            'info':['fate7071',3],	          #应该引用文字配置
         },
      ],
   },
   'fate7081':{  #吕布 情迷天下 必杀
      'act':[
         {
            'priority': 300,
            'type': 0,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, 0],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'0':1000},
            'cond':[['fate', 'id', 'hero703']],
            'dmg':950,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':280,
            'buff':{'buffFaction':{'rnd':200, 'round':1}},
            'atk0': 500,      #攻击力源头(前军)
            'atk1': 500,    #攻击力源头(后军)
            'noAft':1,
            'eff':'eff7081',
            'info':['fate7081',3],	          #应该引用文字配置
         },
      ],
   },
   'fate7091':{  #周瑜 水火无情 必杀
      'act':[
         {
            'priority': 300,
            'src':2,                   #设定子集默认值   发出源 0前军，1后军，2英雄，-1全军，-2全军含英雄
            'type':0,	            #设定子集默认值   触发类型，0起始 1主 2攻前 3攻后 4防前 5防后
            'tgt':[1, 0],             #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全军 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'0':1000},	    #设定子集默认值   本技能所有触发回合及机会
            'cond':[['fate', 'type', 0]],    #刨去自己，同时上阵任意武将英雄
            'dmg':950,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':260,
            'atk0': 500,      #攻击力源头(前军)
            'atk1': 500,    #攻击力源头(后军)
            'noAft':1,
            'eff':'eff7091',
            'info':['fate7091',3],	          #[0]应该引用文字配置，[1]为显示模式，0只用于打印，1word，2banner，3fate。[2]由程序自动补足等级
         },
         {
            'priority':9999,
            'type': 2,
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'cond':[['comp','agi']],
            'info':['水火无情：智力胜出',0],	          #应该引用文字配置
            'binding':{'dmg':300,   'dmgReal':60,},
            'times':-1,  
            'nonSkill':1,    #非技能，不受傲气影响
            'follow':'fate7091',            #必须跟随特定行动触发
         },
      ],
   },

   'fate7101':{  #孙策 业火燎原 必杀
      'act':[
         {
            'priority': 300,
            'type': 0,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, -2],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'0':1000},
            'cond':[['fate', 'id', 'hero722']],
            'dmg':220,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':65,
            #'element':'Fire',
            'buff':{'buffSlow':{'rnd':100,'round':2}},
            'atk0': 500,      #攻击力源头(前军)
            'atk1': 500,    #攻击力源头(后军)
            'combo':5,
            'noAft':1,
            'eff':'eff7101',
            'info':['fate7101',3],	          #应该引用文字配置
         },
      ],
   },
   'fate7111':{  #孟获 蛮荒之力
      'special':[{
            'cond':[['fate', 'id', 'hero720']],
            'change':{
               'skill':{'skill243.act[0].dmg':400, 'skill243.act[0].dmgReal':30},
            },
      }],
   },
   'fate7121':{  #张辽 神兵奇谋
      'special':[{
            'cond':[['fate', 'id', 'hero717']],
            'change':{
               'skill':{'skill210.act[0].dmg':300, 'skill210.act[0].dmgReal':20},
            },
      }],
   },
   'fate7131':{  #董卓 魔君降临 必杀
      'act':[
         {
            'priority': 300,
            'type': 0,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, 0],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'0':1000},
            'cond':[['fate', 'id', 'hero703']],
            'dmg':900,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':400,
            'buff':{'buffWeak':{'rnd':300, 'round':2}},
            'atk0': 500,      #攻击力源头(前军)
            'atk1': 500,    #攻击力源头(后军)
            'noAft':1,
            'eff':'eff7131',
            'info':['fate7131',3],	          #应该引用文字配置
         },
      ],
   },
   'fate7133':{   #董卓 一夫当关
      'act':[
        {
            'priority': 3333,
            'type': 0,             #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,             #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[0, -1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'3':1000},
            'buff':{'fate7133':{}},
            'time':0,           #强制指定战报时间
            'nonSkill':1,    #非技能，不受傲气和特殊加成影响
            'noBfr':1,
            'noAft':1,
            'eff':'effNull',
            'info':['fate7133',0],
         }
      ],
   },
   'fate7141':{  #诸葛亮 雷霆万钧 必杀
      'act':[
         {
            'priority': 300,
            'src':2,                   #设定子集默认值   发出源 0前军，1后军，2英雄，-1全军，-2全军含英雄
            'type':0,	            #设定子集默认值   触发类型，0起始 1主 2攻前 3攻后 4防前 5防后
            'tgt':[1, -1],             #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全军 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'0':1000},	    #设定子集默认值   本技能所有触发回合及机会
            'cond':[['fate', 'num', 1]],    #刨去自己，同时上阵n个英雄
            'dmg':130,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':95,
            'atk0': 500,      #攻击力源头(前军)
            'atk1': 500,    #攻击力源头(后军)
            'combo':1,
            'noAft':1,
            'eff':'eff7141',
            'info':['fate7141',3],	          #[0]应该引用文字配置，[1]为显示模式，0只用于打印，1word，2banner，3fate。[2]由程序自动补足等级
         },
         {
            'priority': 303,
            'src':2,                   #设定子集默认值   发出源 0前军，1后军，2英雄，-1全军，-2全军含英雄
            'type': 2,	            #设定子集默认值   触发类型，0起始 1主 2攻前 3攻后 4防前 5防后
            'follow':'fate7141',            #必须跟随特定行动触发
            'cond':[['fate', 'type', 0]],    #刨去自己，同时上阵任意武将英雄
            'binding':{'combo':1},
            'times':-1,  
            'nonSkill':1,    #非技能，不受傲气影响
            'info':['雷霆万钧武',0],	          #[0]应该引用文字配置，[1]为显示模式，0只用于打印，1word，2banner，3fate。[2]由程序自动补足等级
         },
         {
            'priority': 302,
            'src':2,                   #设定子集默认值   发出源 0前军，1后军，2英雄，-1全军，-2全军含英雄
            'type': 2,	            #设定子集默认值   触发类型，0起始 1主 2攻前 3攻后 4防前 5防后
            'follow':'fate7141',            #必须跟随特定行动触发
            'cond':[['fate', 'type', 1]],    #刨去自己，同时上阵任意文官英雄
            'binding':{'combo':1},
            'times':-1,  
            'nonSkill':1,    #非技能，不受傲气影响
            'info':['雷霆万钧文',0],	          #[0]应该引用文字配置，[1]为显示模式，0只用于打印，1word，2banner，3fate。[2]由程序自动补足等级
         },
         {
            'priority': 301,
            'src':2,                   #设定子集默认值   发出源 0前军，1后军，2英雄，-1全军，-2全军含英雄
            'type': 2,	            #设定子集默认值   触发类型，0起始 1主 2攻前 3攻后 4防前 5防后
            'follow':'fate7141',            #必须跟随特定行动触发
            'cond':[['fate', 'type', 2]],    #刨去自己，同时上阵任意全才英雄
            'binding':{'combo':1},
            'times':-1,  
            'nonSkill':1,    #非技能，不受傲气影响
            'info':['雷霆万钧全',0],	          #[0]应该引用文字配置，[1]为显示模式，0只用于打印，1word，2banner，3fate。[2]由程序自动补足等级
         },
      ],
   },

   'fate7151':{  #智勇之极
      'special':[{
            'cond':[['fate', 'id', 'hero708']],
            'change':{
               'skill':{'skill260.act[0].dmg':100, 'skill260.act[0].dmgReal':1, 'skill260.act[1].res':50},
            },
      }],
   },
   'fate7161':{  #赵云 飞云天龙 圣心龙胆 必杀
      'act':[
         {
            'priority': 300,
            'type': 0,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, 1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'0':1000},
            #'cond':[[['fate', 'id', 'hero706'],['fate', 'id', 'hero707']]],
            #'cond':[[['comp','cha'],['comp','str']],[['comp','agi'],['comp','lead']]],
            'cond':[['fate', 'id', 'hero706']],
            'dmg':850,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':220,
            'buff':{'buffStun':{'rnd':200, 'round':2}},
            'atk0': 500,      #攻击力源头(前军)
            'atk1': 500,    #攻击力源头(后军)
            'noAft':1,
            'eff':'eff7161',
            'info':['fate7161',3],	          #应该引用文字配置
         },
      ],
   },
   'fate7171':{    #郭嘉 魏武智勇 必杀
      'act':[
         {
            'priority': 300,
            'type': 0,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, -1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'0':1000},
            'cond':[['fate', 'id', 'hero712']],
            'dmg':550,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':110,
            'buff':{'buffWeak':{'rnd':200, 'round':2}},
            'atk0': 500,      #攻击力源头(前军)
            'atk1': 500,    #攻击力源头(后军)
            'noAft':1,
            'eff':'eff7171',
            'info':['fate7171',3],	          #应该引用文字配置
         },
      ],
   },
   'fate7181':{    #陆逊 火烧连营 必杀
      'act':[
         {
            'priority': 300,
            'type': 0,             #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,             #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, -1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'0':1000},
            'cond':[['fate', 'rarity', 2]],    #刨去自己，同时上阵任意国士英雄
            'dmg':300,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':80,
            #'element':'Fire',
            'buff':{'buffFire':{'rnd':500, 'round':2}},
            'atk0': 500,      #攻击力源头(前军)
            'atk1': 500,    #攻击力源头(后军)
            'noAft':1,
            'eff':'eff7181',
            'info':['fate7181',3],	          #应该引用文字配置
         },
      ],
   },
   'fate7191':{  #天妒红颜
      'special':[{
            'cond':[['fate', 'id', 'hero717']],
            'change':{
               'skill':{'skill221.act[0].dmg':200, 'skill221.act[0].dmgReal':2},
            },
      }],
   },
   'fate7201':{    #祝融 荆棘之花 必杀
      'act':[
         {
            'priority': 500,
            'type': 0,             #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,             #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, 0],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'cond':[['fate', 'rarity', 3]],    #刨去自己，同时上阵任意巾帼英雄
            'allTimes':1,
            'nonSkill':1,    #非技能，不受傲气
            'noBuff':1,    #有此值时，不受dmgFinal、resFinal、战场dmgRealPer、resRealPer影响
            'noBfr':1,
            'noAft':1,
            'eff':'eff7201',
            'dmgReal':1,
            'element':'Anti',
            'costKey':'deaded',       #充能来源类型 兵力损失
            'cost':1,         #使用 消耗充能
            'multMax':-1,         #一次使用的最大倍数
            'mult':{'dmgReal':1},        #多重施法时提升内容
            'info':['fate7201',3],
         },
      ],
   },
   'fate7211':{  #胡笳伴舞 蔡文姬
      'special':[{
            'cond':[['fate', 'id', 'hero723']],
            'change':{
                'prop':{'armys[0].resType0':500,'armys[1].resType0':500},
            },
      }],
   },
   'fate7221':{  #兄妹情深
      'special':[{
            'cond':[['fate', 'id', 'hero710']],
            'change':{
               'skill':{'skill225.act[0].dmg':100, 'skill225.act[0].dmgReal':1},
            },
      }],
   },
   'fate7231':{  #小乔 红袖添香 必杀
      'act':[
         {
            'priority': 300,
            'type': 0,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, 1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'0':1000},
            'cond':[['fate', 'id', 'hero709']],
            'dmg':900,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':320,
            'atk0': 500,      #攻击力源头(前军)
            'atk1': 500,    #攻击力源头(后军)
            'buff':{'buffStun':{'rnd':250, 'round':2}},
            'noAft':1,
            'eff':'eff7231',
            'info':['fate7231',3],	          #应该引用文字配置
            #'combo':2,
         },
      ],
   },
   'fate7241':{  #大乔 羡煞四方 必杀
      'act':[
         {
            'priority': 300,
            'type': 0,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, -1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'0':1000},
            'cond':[['fate', 'sex', 1]],
            'dmg':350,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':180,
            'atk0': 500,      #攻击力源头(前军)
            'atk1': 500,    #攻击力源头(后军)
            'combo':2,
            'noAft':1,
            'eff':'eff7241',
            'info':['fate7241',3],	          #应该引用文字配置
         },
      ],
   },
   'fate7251':{  #士别三日
      'special':[{
            'cond':[['fate', 'id', 'hero751']],
            'change':{
                'prop':{'armys[1].atkRate':100,'armys[1].spd':10},
            },
      }],
   },
   'fate7261':{  #兄弟情深
      'special':[{
            'cond':[['fate', 'id', 'hero727']],
            'change':{
                'prop':{'armys[0].atkRate':100,'armys[0].defRate':100},
            },
      }],
   },
   'fate7271':{  #虎踞西北
      'special':[{
            'cond':[['fate', 'id', 'hero730']],
            'change':{
                'prop':{'armys[0].atkRate':150,'armys[0].spd':10},
            },
      }],
   },
   'fate7281':{  #悍勇难当
      'special':[{
            'cond':[['fate', 'id', 'hero733']],
            'change':{
                'prop':{'armys[1].atkRate':150,'armys[1].defRate':150},
            },
      }],
   },
   'fate7291':{  #蜀中无将
      'special':[{
            'cond':[['fate', 'id', 'hero740']],
            'change':{
                'prop':{'armys[1].atkRate':250},
            },
      }],
   },
   'fate7301':{  #巧克强敌
      'special':[{
            'cond':[['fate', 'id', 'hero712']],
            'change':{
                'prop':{'armys[0].atkRate':150,'armys[0].defRate':150},
            },
      }],
   },
   'fate7311':{  #桃园之义
      'special':[{
            'cond':[['fate', 'id', 'hero706']],
            'change':{
                'prop':{'armys[0].atkRate':300},
            },
      }],
   },
   'fate7321':{  #野性难驯
      'special':[{
            'cond':[['fate', 'id', 'hero735']],
            'change':{
                'prop':{'armys[1].atkRate':150,'armys[1].defRate':150},
            },
      }],
   },
   'fate7331':{  #摒弃父仇
      'special':[{
            'cond':[['fate', 'id', 'hero753']],
            'change':{
                'prop':{'armys[0].atkRate':250,'armys[0].spd':10},
            },
      }],
   },
   'fate7341':{  #军政双全
      'special':[{
            'cond':[['fate', 'id', 'hero717']],
            'change':{
                'prop':{'armys[0].atkRate':150,'armys[0].defRate':150},
            },
      }],
   },
   'fate7351':{  #长沙举兵
      'special':[{
            'cond':[['fate', 'id', 'hero736']],
            'change':{
                'prop':{'armys[0].atkRate':300},
            },
      }],
   },
   'fate7361':{  #惺惺相惜
      'special':[{
            'cond':[['fate', 'id', 'hero716']],
            'change':{
                'prop':{'armys[1].atkRate':300},
            },
      }],
   },
   'fate7371':{  #不献一谋
      'special':[{
            'cond':[['fate', 'id', 'hero747']],
            'change':{
                'prop':{'armys[1].atkRate':150,'armys[1].spd':10},
            },
      }],
   },
   'fate7611':{  #庞统 巧施连环 必杀 合击技
      'act':[
         {
            'priority': 300,
            'src':2,                   #设定子集默认值   发出源 0前军，1后军，2英雄，-1全军，-2全军含英雄
            'type':0,	            #设定子集默认值   触发类型，0起始 1主 2攻前 3攻后 4防前 5防后
            'tgt':[1, -1],             #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全军 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'0':1000},	    #设定子集默认值   本技能所有触发回合及机会
            'cond':[['fate', 'type', 2]],    #刨去自己，同时上阵任意全才英雄
            'dmg':450,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':100,
            'atk0': 500,      #攻击力源头(前军)
            'atk1': 500,    #攻击力源头(后军)
            'buff':{'buffStun':{'rnd':250, 'round':2}},
            'noAft':1,
            'eff':'eff7611',
            'info':['fate7611',3],	          #[0]应该引用文字配置，[1]为显示模式，0只用于打印，1word，2banner，3fate。[2]由程序自动补足等级
         },
      ],
   },
   'fate7711':{  #吕玲绮 父女齐心
      'special':[{
            'cond':[['fate', 'id', 'hero708']],
            'change':{
                'prop':{
                   'armys[0].atkRate':100,'armys[0].spd':5,
                   'armys[1].atkRate':100,'armys[1].spd':5
                },
            },
      }],
   },

   'fate7731':{   #关银屏 武继云长 同时上阵，近战每个回合，有额外[30%]的几率发动【武圣】
      'special':[
         {
            'cond':[['fate', 'id', 'hero706']],
            'change':{
               'skill':{
                  'skill226.act[0].round.3':300,
                  'skill226.act[0].round.near':300,
               },
            },
         },
      ],
   },
   'fate7733':{   #关银屏 怪力豪勇 对战女性英雄时，己方前军可额外豁免一次会导致全灭的攻击伤害
      'special':[
         {
            'priority':11000,
            'cond':[['enemy','sex',0],], 
            'change':{
               'prop':{
                   'armys[0].stamina':1,
               }
            },
         }
      ],
   },


   'fate7761':{   #张星彩 万夫之勇
      'special':[
         {
            'cond':[['fate', 'id', 'hero731']],
            'change':{
               'skill':{
                  'skill289.act[0].isBurn':1,
                  'skill289.act[0].times':1,
                  'skill204.act[0].isBurn':1,
                  'skill204.act[0].times':1,
                  'skill206.act[0].isBurn':1,
                  'skill206.act[0].times':1,
                  'skill977.act[0].isBurn':1,
                  'skill977.act[0].times':1,
               },
            },
         },
      ],
   },
   'fate7763':{   #张星彩 不让须眉
      'special':[
         {
            'priority':11000,
            'cond':[['enemy','sex',1],],  #对战男性
            'change':{
               'prop':{
                   'armys[0].stamina':1,
               }
            },
         }
      ],
   },

   'fate7771':{   #张春华 甘为绿叶，受到英雄技伤害-15%
      'special':[
         {
            'cond':[['fate', 'id', 'hero705']],
            'change':{
               'prop':{
                   'armys[0].resHero':200,
                   'armys[1].resHero':200,
               }
            },
         }
      ],
   },
   #'fate7772':{   #张春华 绝情鸳鸯，对战武将时，敌方防御-25%
   #   'special':[
   #      {
   #         'cond':[['fate', 'id', 'hero707']],
   #         'changeEnemy':{
   #            'prop':{
   #                'armys[0].defRate':-1250,
   #                'armys[1].defRate':-1250,
   #            }
   #         },
   #      }
   #   ],
   #},

   'fate7781':{   #刘禅 亚父守护 同时上阵，福佑的护盾量提升[300%]
      'special':[
         {
            'cond':[['fate', 'id', 'hero714']],
            'change':{
               'skill':{
                  'skill240.act[0].isBurn':1,  #文本燃烧
                  'skill240.act[0].buff.buffShield.shield.value':500,
                  'skill240.act[0].buff.buffShield.shield.hpmRate':80,
               },
            },
         }
      ],
   },

   'fate7791':{   #文鸯 四海无敌 同时上阵，近战首回合可额外发动【冲阵】，并正常判定连发
      'special':[
         {
            'cond':[['fate', 'id', 'hero708']],
            'change':{
               'skill':{
                  'skill245.act[0].round.3':1000,
               },
            },
         }
      ],
   },

   'fate7553':{  #周泰 救命之恩 复生
      'act':[
         {
            'priority':9801,
            'type': 8,	    #触发类型，8战胜
            'src': 2,      #发出源 0前军，1后军，2英雄，-1全军，-2全军含英雄
            'tgt':[0, -6],           #大目标 0自己1敌方-1全体      ;小目标 -6不计死亡的前后军
            'lv': 5, 
            'noBfr': 1,
            'noAft': 1, 
            'nonSkill':1,    #非技能，不受傲气
            'cureReal':1,         #治疗伤兵数量
            'cure':50,           #治疗伤兵千分点
            'info':['hero755',1],	          #[0]应该引用文字配置，[1]为显示模式，0只用于打印，1word，2banner，3fate。[2]由程序自动补足等级
            'eff':'eff272',
            'time':100,           #强制战报时间
         },
      ],
   },



   'fate7801':{   #曹丕 养虎为患 同时上阵，我方每战损100兵力，便引导冤魂缠绕敌方，反噬敌军10兵力
      'special':[
         {
            'cond':[['fate', 'id', 'hero707']],
            'change':{
               'skill':{
                  'hero7801':{  #反噬
                     'act':[{
                        'priority': 500,
                        'type': 0,             #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
                        'src': 2,             #发出源 0前军，1后军，【2英雄】，-1全军
                        'tgt':[1, 0],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
                        'times':-1,
                        'nonSkill':1,    #非技能，不受傲气影响
                        'noBuff':1,    #有此值时，不受dmgFinal、resFinal、战场dmgRealPer、resRealPer影响
                        'noBfr':1,
                        'noAft':1,
                        'eff':'eff707',
                        'lv':14,
                        'dmgReal':100,
                        'element':'Anti',

                        'costKey':'deaded',       #充能来源类型 兵力损失
                        'cost':1000,         #使用 消耗充能
                        'multMax':-1,         #一次使用的最大倍数
                        'mult':{'dmgReal':100},        #多重施法时提升内容
                        'info':['hero707',2],
                     }],
                  },
               },
            },
         }
      ],
   },

   'fate7831':{  #王越 虎贲将军  同时上阵，【死斗】有50%的几率会提前到近战首回合发动
      'special':[{
            'cond':[['fate', 'id', 'hero770']],
            'change':{
               'skill':{'skill246.act[0].isBurn':1, 'skill246.act[0].round.3':500},
            },
      }],
   },

   'fate7841':{  #左慈 师承遁甲  同时上阵，【遁甲】可同时攻击敌方全体
      'special':[{
            'cond':[['fate', 'id', 'hero787']],
            'change':{
               'skill':{'skill246.act[0].isBurn':1, 'skill275.act[0].tgt':[1,-1]},
            },
      }],
   },

   'fate7871':{   #南华老仙 太平要术 同时上阵，任意方士主动技能，可连续发动[2]次
      'special':[
         {
            'cond':[['fate', 'id', 'hero769']],
            'change':{
               'skill':{
                  'skill222.act[0].isBurn':1,
                  'skill222.act[0].times':1,
                  'skill224.act[0].isBurn':1,
                  'skill224.act[0].times':1,
                  'skill292.act[0].isBurn':1,
                  'skill292.act[0].times':1,
                  'skill980.act[0].isBurn':1,
                  'skill980.act[0].times':1,
               },
            },
         }
      ],
   },

   'fate7721':{  #班育双姝 凄美丽人
      'special':[{
            'cond':[['fate', 'id', 'hero719']],
            'change':{
               'skill':{
                  'skill225.act[0].timesRnd':100,
                  'skill226.act[0].timesRnd':100,
                  'skill227.act[0].timesRnd':100,
                  'skill228.act[0].timesRnd':100,
                  'skill229.act[0].timesRnd':100,
                  'skill230.act[0].timesRnd':100,
                  'skill232.act[0].timesRnd':100,
                  'skill233.act[0].timesRnd':100,
                  'skill235.act[0].timesRnd':100,

                  'skill236.act[0].timesRnd':100,
                  'skill237.act[0].timesRnd':100,
                  'skill238.act[0].timesRnd':100,
                  'skill243.act[0].timesRnd':100,
                  'skill239.act[0].timesRnd':100,
                  'skill245.act[0].timesRnd':100,
                  'skill241.act[0].timesRnd':100,
                  'skill242.act[0].timesRnd':100,
                  'skill244.act[0].timesRnd':100,

                  'skill247.act[0].timesRnd':100,
                  'skill248.act[0].timesRnd':100,
               },
            },
      }],
   },
   'fate7722':{  #班育双姝 
      'special':[{
            'change':{
               'skill':{
                  'skill225.act[0].timesRnd':50,
                  'skill226.act[0].timesRnd':50,
                  'skill227.act[0].timesRnd':50,
                  'skill228.act[0].timesRnd':50,
                  'skill229.act[0].timesRnd':50,
                  'skill230.act[0].timesRnd':50,
                  'skill232.act[0].timesRnd':50,
                  'skill233.act[0].timesRnd':50,
                  'skill235.act[0].timesRnd':50,

                  'skill236.act[0].timesRnd':50,
                  'skill237.act[0].timesRnd':50,
                  'skill238.act[0].timesRnd':50,
                  'skill243.act[0].timesRnd':50,
                  'skill239.act[0].timesRnd':50,
                  'skill245.act[0].timesRnd':50,
                  'skill241.act[0].timesRnd':50,
                  'skill242.act[0].timesRnd':50,
                  'skill244.act[0].timesRnd':50,

                  'skill247.act[0].timesRnd':50,
                  'skill248.act[0].timesRnd':50,
               },
            },
      }],
   },
   'fate7723':{  #班育双姝 
      'special':[{
            'change':{
               'skill':{
                  'skill225.act[0].timesRnd':50,
                  'skill226.act[0].timesRnd':50,
                  'skill227.act[0].timesRnd':50,
                  'skill228.act[0].timesRnd':50,
                  'skill229.act[0].timesRnd':50,
                  'skill230.act[0].timesRnd':50,
                  'skill232.act[0].timesRnd':50,
                  'skill233.act[0].timesRnd':50,
                  'skill235.act[0].timesRnd':50,

                  'skill236.act[0].timesRnd':50,
                  'skill237.act[0].timesRnd':50,
                  'skill238.act[0].timesRnd':50,
                  'skill243.act[0].timesRnd':50,
                  'skill239.act[0].timesRnd':50,
                  'skill245.act[0].timesRnd':50,
                  'skill241.act[0].timesRnd':50,
                  'skill242.act[0].timesRnd':50,
                  'skill244.act[0].timesRnd':50,

                  'skill247.act[0].timesRnd':50,
                  'skill248.act[0].timesRnd':50,
               },
            },
      }],
   },
   'fate7724':{  #班育双姝 
      'special':[{
            'change':{
               'skill':{
                  'skill225.act[0].timesRnd':50,
                  'skill226.act[0].timesRnd':50,
                  'skill227.act[0].timesRnd':50,
                  'skill228.act[0].timesRnd':50,
                  'skill229.act[0].timesRnd':50,
                  'skill230.act[0].timesRnd':50,
                  'skill232.act[0].timesRnd':50,
                  'skill233.act[0].timesRnd':50,
                  'skill235.act[0].timesRnd':50,

                  'skill236.act[0].timesRnd':50,
                  'skill237.act[0].timesRnd':50,
                  'skill238.act[0].timesRnd':50,
                  'skill243.act[0].timesRnd':50,
                  'skill239.act[0].timesRnd':50,
                  'skill245.act[0].timesRnd':50,
                  'skill241.act[0].timesRnd':50,
                  'skill242.act[0].timesRnd':50,
                  'skill244.act[0].timesRnd':50,

                  'skill247.act[0].timesRnd':50,
                  'skill248.act[0].timesRnd':50,
               },
            },
      }],
   },

   'fate7891':{  #袁绍  挚友死敌  同时上阵，万箭随机增加1~3次乱射
      'special':[{
            'cond':[['fate', 'id', 'hero762']],
            'change':{
               'skill':{
                  'skill225.act[0].comboRnd':2,
                  'skill225.act[0].combo':1,
               },
            },
      }],
   },
   'fate7941':{  #项羽  西楚霸王  【冲阵】可攻击敌方全体
      'special':[{
            'change':{
               'skill':{
                  'skill245.act[0].tgt':[1, -1],
               },
            },
      }],
   },
   'fate7944':{  #项羽  西楚霸王   敌方全军免伤-15%
      'special':[{              
         'changeEnemy':{
             'prop':{
                  'armys[0].resFinal':-150,
                  'armys[1].resFinal':-150,
             },
         },
      }],
   },

   'fate7944':{  #虞姬  倾城之颜   远战回合免伤+20%
      'special':[{              
         'change':{
             'prop':{
                  'armys[0].others.roundRes_1':200,
                  'armys[1].others.roundRes_1':200,
                  'armys[0].others.roundRes_2':200,
                  'armys[1].others.roundRes_2':200,
             },
         },
      }],
   },
   'fate70001':{  #公孙瓒  白马将军   前后军受到不良状态的几率-5%
      'special':[{              
         'change':{
             'prop':{
                  'armys[0].deBuffRate':-50,
                  'armys[1].deBuffRate':-50,
             },
         },
      }],
   },
   'fate70011':{  #张仲景  医圣在世   敌方任意恢复兵力的效果-30%
       'special':[{
            'changeEnemy':{
                'prop':{
                     'armys[0].others.elementCure':-300,  
                     'armys[1].others.elementCure':-300,
                     'armys[0].others.elementSummon':-300,
                     'armys[1].others.elementSummon':-300,
                },
            },
       }],
   },
   'fate70024':{   #王昭君  前后军可分别豁免一次导致全灭的攻击伤害
      'special':[{
          'change':{
               'prop':{
                   'armys[0].stamina':1,
                   'armys[1].stamina':1,
               },
          },
      }],
   },
   'fate70034':{   #钟会  【遁甲】威力+15%
      'special':[{
            'change':{
               'skill':{
                  'skill275.act[0].dmgScale':150,
               },
            },
      }],
   },

   'fate77023':{   #何皇后   对战武将时，受到按比例损失兵力的效果减免40%
      'special':[
         {
            'cond':[['enemy','type',0],],  #对战武将
            'change':{
               'prop':{
                  'armys[0].resRealRate':400,
                  'armys[1].resRealRate':400,
               },
            },
         }
      ],
   },

   'fate70051':{   #潘凤   潘凤发动【邀斗】的当前回合，敌方尝试增援时会触发[【失信】]，停止该行动并全军【混乱】[1]回合
      'special':[
         {
            'priority':-2007801,
            'change':{
               'skill':{
                  'hero7005.act[0].buff.buffInvite.rnd':1000,
               },
            },
         }
      ],
   },
 
  #################宿命技 结束





#正式技能               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


  #################步兵技能


   'skill201':{   #盾墙
      'infoArr':['r|act[0]','%|act[0].binding.res'],
      'nextArr':['%|act[0].binding.res'],
      'highArr':['p|high.passive'],
      'index':101,
      'type':0,
      'cost_type':0,
      'max_level':25,
      'shogun_type':0,
      'ability_info':[],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':200,'powerRate':20},
         },
         {
            'cond':['skill.skill201', '*', 1],
            'rslt':{'powerRate':9},
         },
      ],
      'up':{ 'act[0].binding.res' : 10},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].binding.res' : -5  },
         '27':{  'act[0].binding.res' : -10  },
         '28':{  'act[0].binding.res' : -15  },
         '29':{  'act[0].binding.res' : -20  },
         '30':{  'act[0].binding.res' : -25  },
      },
      'high':{
         'lv':10,
         'passive':[
            {
               'cond':['army[0].type', '=', 0],
               'rslt':{'army[0].defBase':20},
            },
            {
               'rslt':{'power':200,'powerRate':5},
            },
         ],
      },
      'act':[
         {
            'priority':9400,
            'type': 4,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'round':{'all':1000},
            'cond':[['srcArmy',1]],
            'times': -1,    #每回合最多使用次数
            'binding':{
               'res':120,	    #减免本次伤害
            },
            'eff':'eff201',
            'info':['skill201',1],	          #应该引用文字配置
         },
      ],
   },
   'skill289':{   #激战
      'infoArr':['r|act[0]','-|act[0].combo','%|act[0].dmg'],
      'nextArr':['%|act[0].dmg'],
      'highArr':['p|high.passive'],
      'index':102,
      'type':0,
      'cost_type':0,
      'max_level':25,
      'shogun_type':0,
      'ability_info':[],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':200,'powerRate':20},
         },
         {
            'cond':['skill.skill289', '*', 1],
            'rslt':{'powerRate':9},
         },
      ],
      'up':{ 'act[0].dmg':15, 'act[0].dmgReal':2},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].dmg' : -5  },
         '27':{  'act[0].dmg' : -10  },
         '28':{  'act[0].dmg' : -15  },
         '29':{  'act[0].dmg' : -20  },
         '30':{  'act[0].dmg' : -25  },
      },
      'high':{
         'lv':10,
         'passive':[
            {
               'cond':['army[0].type', '=', 0],
               'rslt':{'army[0].atkBase':50},
            },
            {
               'rslt':{'power':200,'powerRate':5},
            },
         ],
      },
      'act':[
         {
            'priority':40,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, -2],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'3':1000},
            'dmg':620,	       #技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':15,
            'dmgLimit':70,                       #限制对单目标的杀伤千分点，溢出衰减
            'atk0': 1000,      #攻击力源头(前军)
            'times': 1,    #每回合最多使用次数
            'combo':4,
            'isMelee':1,
            'eff':'eff289',
            'info':['skill289',1],	          #应该引用文字配置
         },
      ],
   },

   'skill202':{   #坚守
      'infoArr':['r|act[0]','%|act[0].binding.res'],
      'nextArr':['%|act[0].binding.res'],
      'highArr':['p|high.passive'],
      'index':103,
      'type':0,
      'cost_type':1,
      'max_level':25,
      'shogun_type':1,
      'ability_info':[],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':300,'powerRate':25},
         },
         {
            'cond':['skill.skill202', '*', 1],
            'rslt':{'powerRate':11},
         },
      ],
      'up':{ 'act[0].binding.res':10},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].binding.res' : -5  },
         '27':{  'act[0].binding.res' : -10  },
         '28':{  'act[0].binding.res' : -15  },
         '29':{  'act[0].binding.res' : -20  },
         '30':{  'act[0].binding.res' : -25  },
      },
      'high':{
         'lv':13,
         'passive':[
            {
               'cond':['army[0].type', '=', 0],
               'rslt':{'army[0].spd':10,'army[0].hpm':50},
            },
            {
               'rslt':{'power':300,'powerRate':5},
            },
         ],
      },
      'act':[
         {
            'priority':9401,
            'type': 4,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'round':{'all':1000},
            'cond':[['srcArmy',0]],
            'times': -1,    #每回合最多使用次数
            'binding':{
               'res':130,	    #减免本次伤害
            },
            'eff':'eff202',
            'info':['skill202',1],	          #应该引用文字配置
         },
      ],
   },
   'skill204':{   #冲锋
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[1].binding.crit'],
      'nextArr':['%|act[0].dmg'],
      'highArr':['%|high.special[0].change.skill.skill204h.act[0].binding.dmg'],
      'index':104,
      'type':0,
      'cost_type':1,
      'max_level':25,
      'shogun_type':1,
      'ability_info':['cha','str'],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':300,'powerRate':25},
         },
         {
            'cond':['skill.skill204', '*', 1],
            'rslt':{'powerRate':12},
         },
      ],
      'up':{'act[0].dmg':25, 'act[0].dmgReal':3},
      'high':{
         'lv':13,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':10},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill204h':{
                     'act':[{
                        'priority':9001,
                        'type': 2,
                        'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'cond':[['comp','str']],
                        'binding':{
                           'dmg':200,
                           'dmgReal':5,
                        },
                        'times':-1,  
                        'nonSkill':1,    #非技能，不受傲气影响
                        'follow':'skill204',            #必须跟随特定行动触发
                        'info':['冲锋：武力胜出',0],	          #应该引用文字配置
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':60,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, 0],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'2':1000},
            'dmg':800,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':20,
            'dmgLimit':160,
            'atk0': 1050,      #攻击力源头(前军)
            'times': 1,    #每回合最多使用次数
            'isMelee':1,
            'eff':'eff204',
            'info':['skill204',1],	          #应该引用文字配置
         },
         {
            'priority':9002,
            'type': 2,
            'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'cond':[['comp','cha']],
            'times': -1,    #每回合最多使用次数
            'nonSkill':1,    #非技能，不受傲气
            'binding':{
               'crit':400,
            },
            'follow':'skill204',            #必须跟随特定行动触发
            'info':['冲锋：魅力胜出',0],	          #应该引用文字配置
         },
      ],
   },

   'skill203':{   #反击
      'infoArr':['r|act[0]','%|act[0].dmg'],
      'nextArr':['%|act[0].dmg'],
      'highArr':['%|high.special[0].change.skill.skill203h.act[0].binding.dmg'],
      'index':105,
      'type':0,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['lead'],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':400,'powerRate':30},
         },
         {
            'cond':['skill.skill203', '*', 1],
            'rslt':{'powerRate':14},
         },
      ],
      'up':{'act[0].dmg':25, 'act[0].dmgReal':2},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].dmg' : -5  },
         '27':{  'act[0].dmg' : -10  },
         '28':{  'act[0].dmg' : -15  },
         '29':{  'act[0].dmg' : -20  },
         '30':{  'act[0].dmg' : -25  },
      },
      'high':{
         'lv':13,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':10},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill203h':{
                     'act':[{
                        'priority':9000,
                        'type': 2,
                        'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'cond':[['comp','lead']],
                        'binding':{
                           'dmg':200,
                           'dmgReal':5,
                        },
                        'times':-1,  
                        'nonSkill':1,    #非技能，不受傲气影响
                        'follow':'skill203',            #必须跟随特定行动触发
                        'info':['反击：统帅胜出',0],	          #应该引用文字配置
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':9500,
            'type': 5,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 0,
            'round':{'near':1000},
            'tgt':[1, 0],          #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'cond':[['army',0],['srcArmy',0],['srcTeam',1]],
            'times': -1,    #每回合最多使用次数
            'noAft': 1,    #本次行动不能触发后置绑定效果
            'dmg':1100,	#技能伤害系数
            'dmgReal':30,
            'dmgLimit':120,
            'buff':{'buffFire':{'rnd':0,'round':2}},
            'atk0': 1000,      #攻击力源头(前军)
            'isMelee':1,
            'eff':'eff203',
            'info':['skill203',1],	          #应该引用文字配置
         },
      ],
   },

   'skill205':{   #裂甲
      'infoArr':['r|act[0]','%|act[0].binding.ignDef/0.7','%|act[1].binding.dmg'],
      'nextArr':['%|act[0].binding.ignDef/0.7'],
      'highArr':['%|high.special[0].change.skill.skill205h.act[0].binding.ignDef/0.7'],
      'index':106,
      'type':0,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['agi','lead'],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':400,'powerRate':30},
         },
         {
            'cond':['skill.skill205', '*', 1],
            'rslt':{'powerRate':15},
         },
      ],
      'up':{ 'act[0].binding.ignDef':7, 'act[0].binding.dmgReal':4},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].binding.ignDef' : -3.5  },
         '27':{  'act[0].binding.ignDef' : -7  },
         '28':{  'act[0].binding.ignDef' : -10.5  },
         '29':{  'act[0].binding.ignDef' : -14  },
         '30':{  'act[0].binding.ignDef' : -17.5  },
      },
      'high':{
         'lv':13,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':10},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill205h':{
                     'act':[{
                        'priority':9003,
                        'type': 2,
                        'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'cond':[['comp','lead']],
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能，不受傲气
                        'binding':{
                           'ignDef':105,	   
                           'dmgReal':40
                        },
                        'follow':{'attach':'skill205'},     #必须跟随附加技  
                        #'follow':'skill205',            #必须跟随特定行动触发
                        'info':['裂甲：统帅胜出',0],	          #应该引用文字配置
                     }],
                  },
               },
            },
         }]
      },

      'act':[
         {
            'priority':9005,
            'type': 2,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'round':{'all':1000},
            'times': -1,    #每回合最多使用次数
            'follow':{'keys':{'dmg':1000,'dmgReal':1000}},
            'binding':{
               'ignDef':84,
               'dmgReal':20
            },
            'eff':'eff205',
            'info':['skill205',1],	          #应该引用文字配置
         },
         {
            'priority':9004,
            'type': 2,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'cond':[['comp','agi']],
            'times': -1,    #每回合最多使用次数
            'nonSkill':1,    #非技能，不受傲气
            'binding':{
               'dmg':150,
               'dmgReal':5
            },
            'follow':{'attach':'skill205'},     #必须跟随附加技 
            #'follow':'skill205',            #必须跟随特定行动触发
            'info':['裂甲：智力胜出',0],	          #应该引用文字配置
         },
      ],
   },
   'skill206':{   #陷阵
      'infoArr':['r|act[0]','%|act[0].dmg+150','%|act[0].loss'],
      'nextArr':['%|act[0].dmg+150'],
      'highArr':[],
      'merge':1,   #几次合服后可见
      'index':107,
      'type':0,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['cha'],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':600,'powerRate':35},
         },
         {
            'cond':['skill.skill206', '*', 1],
            'rslt':{'powerRate':17},
         },
      ],
      'up':{ 'act[0].dmg':70, 'act[0].dmgReal':20},
      'high':{
         'lv':13,
         'passive':[
            {
               'rslt':{'power':600,'powerRate':15},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill206h':{
                     'act':[{
                        'priority':9006,
                        'type': 2,
                        'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'cond':[['comp','cha']],
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能，不受傲气
                        'binding':{
                           'crit':99999999,
                           'dmgReal':50
                        },
                        'follow':'skill206',            #必须跟随特定行动触发
                        'info':['陷阵：魅力胜出',0],	          #应该引用文字配置
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':80,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, -1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'1':1000},
            'dmg':1000,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':200,
            'dmgLimit':170,
            'atk0': 1000,      #攻击力源头(前军)
            'loss':50,
            'isMelee':1,
            'times': 1,    #每回合最多使用次数
            'eff':'eff206',
            'info':['skill206',1],	          #应该引用文字配置
         },
      ],
   },
   'skill293':{   #蓄势 三合
      'infoArr':['%|act[0].binding.dmg*1.5','%|act[1].round.3','-|act[1].removeDebuff'],
      'nextArr':['%|act[0].binding.dmg*1.5','%|act[1].round.3'],
      'highArr':[],
      'merge':3,   #几次合服后可见
      'index':108,
      'type':0,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['str','agi'],
      'passive':[
         {
            'rslt':{'power':600,'powerRate':100},
         },
         {
            'cond':['skill.skill293', '*', 1],
            'rslt':{'powerRate':17},
         },
      ],
      'up':{ 'act[0].binding.dmg':100, 'act[0].binding.dmgReal':60, 'act[1].round.3':30},
      'high':{
         'lv':15,
         'passive':[
            {
               'rslt':{'power':600,'powerRate':50},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill293h':{
                     'act':[{
                        'priority':9414,
                        'type': 4,
                        'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'round':{'0':1000,'1':1000,'2':1000},
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能，不受傲气
                        'binding':{
                           'banIgnDef':900,
                        },
                        'follow':{'keys':{'dmg':1000,'dmgReal':1000}},
                        'info':['蓄势不乱',0],	          #应该引用文字配置
                     }],
                  },
               },
            },
         }]
      },
      'act':[   #积聚战意，势如破竹。近战首回合，我方步兵在首次攻击时，对首个目标额外追加{0}的伤害。若武力或智力高于对手，近战首回合步兵行动前可随机移除{1}个不良状态
         {
            'priority':2931,
            'type': 16,	            #触发类型，16标后
            'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'round':{'3':1000},
            'free':1,    #有此值时，每回合检查cond等失败不会消耗检查次数（用于条件可变性较强的）
            'condTgt':[
                 ['tgtTeam',1],   #对敌方出手时才生效
                 #[['armyType',0],['armyType',1]],
                 #['hpPoint','<',500],
            ],
            'binding':{
                 #'ignDef':20,
                 'critRate':-300,
                 'dmgScale':100,
                 'dmgReal':600,
                 'dmg':2400,
            },
            'follow':{'keys':{'dmg':1000,'dmgReal':1000}},
            'unFollow':{'effFaction':1},
            'times': 1,    #每回合最多使用次数
            'eff':'eff293',
            'info':['skill293',1],	          #应该引用文字配置
         },
         {
            'priority':2930,
            'type': 10,     #10急救净化 预备期
            'src': 0,              #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[0, -5],    
            'round':{'3':130},
            'cond':[[['comp','str'],['comp','agi']],['checkBuff',0,-5,2,'>',0]],   #场中存在状态[1]敌我[2]部队[3]状态类型或指定状态[4]比较符[5]数量         

            'nonSkill':1,    #非技能，不受傲气
            'noBuff':1,      #有此值时，不受dmgFinal、resFinal、战场dmgRealPer、resRealPer影响
            'noBfr':1,
            'noAft':1,

            'removeDebuff':1,

            'eff':'eff293s',
            'info':['skill293s',1],
         },
#         {
#            'priority':2930,
#            'type': 16,	            #触发类型，16标后
#            'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
#            'cond':[[['comp','str'],['comp','agi']]],
#            'follow':{'attach':'skill293'},     #必须跟随附加技
#            'binding':{
#               'buff':{'buffStun':{'rnd':1000, 'round':1}},
#            },
#            'eff':'effNull',
#            'info':['蓄势混乱',1],	          #应该引用文字配置
#         },
      ],
   },


   'skill962':{   #逆刃
      'infoArr':[
         '%|act[0].dmg',
         '%|act[0].buff.buffStun.rnd',
      ],
      'nextArr':['%|act[0].dmg','%|act[0].buff.buffStun.rnd'],
      'highArr':['%|high.special[0].change.prop.$armys[0].deBuffRate'],
      'merge':5,
      'index':109,
      'type':0,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':[],
      'passive':[
         {
            'rslt':{'power':600,'powerRate':100},
         },
         {
            'cond':['skill.skill962', '*', 1],
            'rslt':{'powerRate':17},
         },
      ],
      'lvPatch':{   #满足特定等级的补丁
         '30':{  'act[0].buff.buffStun.rnd':5  },
      },
      'up':{'act[0].dmg':50, 'act[0].dmgReal':25, 'act[0].buff.buffStun.rnd':5},
      'high':{
         'lv':17,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':60},
            },
         ],
         'special':[{
            'change':{
               'prop':{
                  'armys[0].deBuffRate':-200,
               },
            },
         }],
      },
      'act':[   #受到8次伤害后，步兵立即发动一次无畏冲锋，对敌方全军造成240%伤害，并有10%几率导致敌方【混乱】，持续2回合；
         {
            'priority':88962,
            'type': 17,             #触发类型，17充能后
            'src': 0,
            'tgt':[1, -1],
            'allTimes': 1,
            'dmg':2400,
            'dmgReal':350,
            'atk0': 1000,
            'dmgLimit':240,
            'energyKeySrc': 'energy962',
            'costKey': 'energy962',
            'cost': 8,
            'buff':{'buffStun':{'rnd':100, 'round':2}},
            'isMelee':1,
            'eff':'eff962',
            'info':['skill962',1],	          #应该引用文字配置
         },
         {
            'priority':962.001,
            'type': 23,             #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 0,              #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[0, 0],
            'times': -1,
            'nonSkill':1,    #非技能，不受傲气和特殊加成影响
            'noBfr':1,    
            'noAft':1,  
            'condHpChange':[['<',0],None],  #兵力受损后
            'energyKey':'energy962',
            'energy':1,
            'time':0,
            'eff':'effNull',
            'info':['逆刃储能',0],           #应该引用文字配置
         },
      ],
   },

   'skill966':{   #警觉
      'infoArr':[
         '%|0-act[0].binding.res',
         '%|0-act[1].binding.res',
      ],
      'nextArr':['%|0-act[0].binding.res','%|0-act[1].binding.res'],
      'highArr':['*%|high.special[0].change.skill.$skill289.act[0].dmgScale'],
      'merge':5,
      'index':110,
      'type':0,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':[],
      'passive':[
         {
            'rslt':{'power':600,'powerRate':100},
         },
         {
            'cond':['skill.skill966', '*', 1],
            'rslt':{'powerRate':17},
         },
      ],
      'up':{'act[0].binding.res':3, 'act[1].binding.res':6},
      'high':{
         'lv':17,
         'passive':[
            {
               'rslt':{'power':500,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill289.act[0].dmgScale':100,
                  'skill204.act[0].dmgScale':100,
                  'skill203.act[0].dmgScale':100,
                  'skill206.act[0].dmgScale':100,
                  'skill962.act[0].dmgScale':100,
               },
            },
         }],
      },
      'act':[   #步兵受到兵种技伤害-5%，受到英雄技伤害-10%
         {
            'priority':9420.966,
            'type': 4,         #触发类型，防前
            'src': 0,
            'times': -1,
            'allTimes': -1,
            #'cond':[['srcArmy',-1],['srcTeam',1]],
            'binding':{'res':50},
            'unFollow':{'keys':{'allowSelf':1000}},
            'follow':{
               '_type_0_A':1,'_type_1_A':1,'_type_2_A':1,'_type_3_A':1,
            },
            'info':['skill966',1],
         },
         {
            'priority':-9421.966,
            'type': 4,             #触发类型，防前
            'src': 0,
            'times': -1,
            'allTimes': -1,
            'cond':[['srcArmy',2],['srcTeam',1]],
            'binding':{'res':100},
            'unFollow':{'keys':{'allowSelf':1000}},
            #'follow':{'useBase':1, 'keys':{'isHero':1000}},
            'follow':{'heroDmg':1},
            'info':['skill966',1],
         },
      ],
   },


   'skill977':{   #投戟
      'infoArr':['%0|act[0].dmg','%0|act[0].dmg+act[0].dmgRnd'],
      'nextArr':['%0|act[0].dmg','%0|act[0].dmg+act[0].dmgRnd'],
      'highArr':[],
      'unlockArr':[],
      'merge':7,   #几次合服后可见
      'index':111,
      'type':0,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['agi','str'],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':600,'powerRate':50},
         },
         {
            'cond':['skill.skill977', '*', 1],
            'rslt':{'powerRate':17},
         },
      ],
      'special':[{
           'priority':-888977,
           'cond':['enemy','army',2],
                'change':{
                     'skill':{
                          'skill977.act[0].tgt':[1, -1],
                     },
                },
           },
      ],
      'up':{ 'act[0].dmg':51.72, 'act[0].dmgRnd':51.72, 'act[0].dmgReal':13},
      'unlock':[
        {
         'lv':10,
         'passive':[
            {
               'cond':['army[0].type', '=', 0],
               'rslt':{'power':150,'powerRate':20,'agi':1,'str':1,'army[0].res':100},
            },
         ],
         'special':[{
           'change':{
              'skill':{
                 'skill977.act[0].dmgScale':250,
              },
           },
         }],
        },
        {
         'lv':17,
         'passive':[
            {
               'rslt':{'power':350,'powerRate':50},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill977h':{
                     'act':[{
                        'priority':888.977,
                        'type': 3,
                        'src': 0,
                        'tgt':[0, 0],
                        'cond':[[['comp','agi'],['comp','str']]],
                        'times': -1,
                        'nonSkill':1,    #非技能，不受傲气和特殊加成影响
                        'noBfr': 1,    #本次行动不能触发前置绑定效果
                        'noAft': 1,    #本次行动不能触发后置绑定效果  
                        'buff':{'buffEngage':{'round':2}},
                        'follow':'skill977',
                        'eff':'effNull',
                        'time':0,
                        'info':['投戟：武智胜出',0],
                     }],
                  },
               },
            },
         }]
        },
      ],
      'act':[
         {
            'priority':88977,
            'type': 1,
            'src': 0,
            'tgt':[1, -2],
            'round':{'2':1000},
            'dmg':1500,
            'dmgReal':190,
            'dmgRnd':1500,
            'dmgLimit':150,
            'atk0': 1050,
            #'enforceCrit':100,
            'allowSelf':1,
            'noBfr':-1,
            'noAft':-1,
            'times': 1,
            'isRemote':1,
            'eff':'eff977',
            'info':['skill977',1],
         },
      ],
   },


   'skill984':{   #战壕
      'infoArr':['%|act[0].round.0'],
      'nextArr':['%|act[0].round.0'],
      'highArr':[],
      'unlockArr':[],
      'merge':7,   #几次合服后可见
      'index':112,
      'type':0,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':[],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':600,'powerRate':50},
         },
         {
            'cond':['skill.skill984', '*', 1],
            'rslt':{'powerRate':17},
         },
      ],
      'up':{ 'act[0].round.0':3.44, 'act[1].round.far':3.44, 'act[1].lv':1},
      'unlock':[
        {
         'lv':10,
         'passive':[     #战斗开始和远战回合，步兵受到弓兵技攻击时，也有同等几率触发【战壕】
            {
               'rslt':{'power':150,'powerRate':20},
            },
         ],
         'special':[{
            'priority':-88984,
            'change':{
               'skill':{
                  'skill984.act[0].follow.skill216':1,
                  'skill984.act[0].follow.skill218':1,
                  'skill984.act[0].follow.skill964':1,
                  'skill984.act[0].follow.skill979':1,
                  'skill984.act[1].follow.skill216':1,
                  'skill984.act[1].follow.skill218':1,
                  'skill984.act[1].follow.skill964':1,
                  'skill984.act[1].follow.skill979':1,
               },
            },
         }]
        },
        {
         'lv':17,
         'passive':[   #远战首回合，步兵触发【战壕】的几率+25%
            {
               'rslt':{'power':350,'powerRate':50},
            },
         ],
         'special':[{
            'priority':-88984.1,
            'change':{
               'skill':{
                  'skill984.act[0].round.1':250,
               },
            },
         }]
        },
      ],
      'act':[   #战斗开始和远战回合，步兵受到英雄技攻击时，有10%（20%）的几率触发【战壕】，使本段攻击无法命中
         {
            'priority':889840,
            'type': 4,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'round':{'0':100}, 
            'times': -1,
            'binding':{
                'acc':-1000,
            },
            'follow':{'skill216':0,'skill218':0,'skill964':0,'skill979':0, 'useBase':1, 'keys':{'isHero':1000}},
            'unFollow':{'skill231':1,'skill234':1,'skill960':1,'skill971':1,'skill240':1},
            #'eff':'eff984',
            'info':['skill984',1],
         },
         {
            'priority':889841,
            'type': 4,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'round':{'far':100}, 
            'times': -1,
            'binding':{
                'acc':-1000,
            },
            'follow':{'skill216':0,'skill218':0,'skill964':0,'skill979':0, 'useBase':1, 'keys':{'isHero':1000}},
            'unFollow':{'skill231':1,'skill234':1,'skill960':1,'skill971':1,'skill240':1},
            'lv':1,
            'actId':'skill984_1',
            #'eff':'eff984',
            'info':['skill984',1],
         },
      ],
   },


#骑兵技


   'skill207':{   #铁蹄
      'infoArr':['r|act[0]','%|act[0].binding.dmg'],
      'nextArr':['%|act[0].binding.dmg'],
      'highArr':['p|high.passive'],
      'index':201,
      'type':1,
      'cost_type':0,
      'max_level':25,
      'shogun_type':0,
      'ability_info':[],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':200,'powerRate':20},
         },
         {
            'cond':['skill.skill207', '*', 1],
            'rslt':{'powerRate':9},
         },
      ],
      'up':{ 'act[0].binding.dmg':30, 'act[0].binding.dmgReal':2},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].binding.dmg' : -10  },
         '27':{  'act[0].binding.dmg' : -20  },
         '28':{  'act[0].binding.dmg' : -30  },
         '29':{  'act[0].binding.dmg' : -40  },
         '30':{  'act[0].binding.dmg' : -50  },
      },
      'high':{
         'lv':10,
         'passive':[
            {
               'cond':['army[0].type', '=', 1],
               'rslt':{'army[0].atkBase':50},
            },
            {
               'rslt':{'power':200,'powerRate':5},
            },
         ],
      },
      'act':[
         {
            'priority':9007,
            'type': 2,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'round':{'near':1000},
            'times': -1,    #每回合最多使用次数
            'binding':{
               'dmg':250,
               'dmgReal':15,
            },
            'follow':{'keys':{'dmg':1000,'dmgReal':1000}},
            'eff':'eff207',
            'info':['skill207',1],	          #应该引用文字配置
         },
      ],
   },
   'skill209':{   #奔袭
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[0].dmg+act[0].dmgRnd'],
      'nextArr':['%|act[0].dmg','%|act[0].dmg+act[0].dmgRnd'],
      'highArr':['p|high.passive'],
      'index':202,
      'type':1,
      'cost_type':0,
      'max_level':25,
      'shogun_type':0,
      'ability_info':[],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':200,'powerRate':20},
         },
         {
            'cond':['skill.skill209', '*', 1],
            'rslt':{'powerRate':9},
         },
      ],
      'up':{ 'act[0].dmg':25, 'act[0].dmgReal':3},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].dmg' : -5  },
         '27':{  'act[0].dmg' : -10  },
         '28':{  'act[0].dmg' : -15  },
         '29':{  'act[0].dmg' : -20  },
         '30':{  'act[0].dmg' : -25  },
      },
      'high':{
         'lv':13,
         'passive':[
            {
               'cond':['army[0].type', '=', 1],
               'rslt':{'army[0].spd':10,'army[0].hpm':50},
            },
            {
               'rslt':{'power':300,'powerRate':5},
            },
         ],
      },
      'act':[
         {
            'priority':81,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, 0],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'1':1000},
            'dmg':280,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgRnd':280,
            'dmgReal':13,
            'dmgLimit':110,
            'times':1,
            'atk0': 1050,      #攻击力源头(前军)
            'atk1': 0,    #攻击力源头(后军)
            'isMelee':1,
            'eff':'eff209',
            'info':['skill209',1],	          #应该引用文字配置
         },
      ],
   },

   'skill208':{   #不屈
      'infoArr':['r|act[0]','%|act[0].binding.res'],
      'nextArr':['%|act[0].binding.res'],
      'highArr':['p|high.passive'],
      'index':203,
      'type':1,
      'cost_type':1,
      'max_level':25,
      'shogun_type':1,
      'ability_info':[],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':300,'powerRate':25},
         },
         {
            'cond':['skill.skill208', '*', 1],
            'rslt':{'powerRate':11},
         },
      ],
      'up':{ 'act[0].binding.res' : 10},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].binding.res' : -5  },
         '27':{  'act[0].binding.res' : -10  },
         '28':{  'act[0].binding.res' : -15  },
         '29':{  'act[0].binding.res' : -20  },
         '30':{  'act[0].binding.res' : -25  },
      },
      'high':{
         'lv':10,
         'passive':[
            {
               'cond':['army[0].type', '=', 1],
               'rslt':{'army[0].defBase':20},
            },
            {
               'rslt':{'power':300,'powerRate':5},
            },
         ],
      },
      'act':[
         {
            'priority':9402,
            'type': 4,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后             'cond':[['srcArmy',1]],
            'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'round': {'all':1000},
            'times': -1,    #每回合最多使用次数
            'cond':[['srcArmy',1]],
            'binding':{
               'res':130,	    #减免本次伤害
            },
            'eff':'eff208',
            'info':['skill208',1],	          #应该引用文字配置
         },
      ],
   },

   'skill210':{   #突击
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[1].binding.dmg'],
      'nextArr':['%|act[0].dmg'],
      'highArr':['%|high.special[0].change.skill.skill210h.act[0].binding.ignDef'],
      'index':204,
      'type':1,
      'cost_type':1,
      'max_level':25,
      'shogun_type':1,
      'ability_info':['str','cha'],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':300,'powerRate':25},
         },
         {
            'cond':['skill.skill210', '*', 1],
            'rslt':{'powerRate':12},
         },
      ],
      'up':{'act[0].dmg':35, 'act[0].dmgReal':4},
      'high':{
         'lv':13,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':10},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill210h':{
                     'act':[{
                        'priority':9008,
                        'type': 2,
                        'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'cond':[['comp','cha']],
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能，不受傲气
                        'binding':{
                           'ignDef':300,
                           'dmgReal':5,
                        },
                        'follow':'skill210',            #必须跟随特定行动触发
                        'info':['突击：魅力胜出',0],	          #应该引用文字配置
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':61,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, 0],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'2':1000},
            'dmg':780,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':24,
            'dmgLimit':150,
            'atk0': 1050,      #攻击力源头(前军)
            'atk1': 0,    #攻击力源头(后军)
            'eff':'eff210',
            'isMelee':1,
            'buff':{'buffStun':{'rnd':0, 'round':1}},
            'info':['skill210',1],	          #应该引用文字配置
         },
         {
            'priority':9009,
            'type': 2,
            'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'cond':[['comp','str']],
            'times': -1,    #每回合最多使用次数
            'nonSkill':1,    #非技能，不受傲气
            'binding':{
               'dmg':200,
               'dmgReal':5,
            },
            'follow':'skill210',            #必须跟随特定行动触发
            'info':['突击：武力胜出',0],	          #应该引用文字配置
         },
      ],
   },
   'skill290':{   #舍身
      'infoArr':['r|act[0]','%|act[0].round.all','%|act[0].dmg','%|act[0].loss'],
      'nextArr':['%|act[0].round.all','%|act[0].dmg'],
      'highArr':[],
      'index':205,
      'type':1,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['agi'],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':400,'powerRate':30},
         },
         {
            'cond':['skill.skill290', '*', 1],
            'rslt':{'powerRate':14},
         },
      ],
      'up':{ 'act[0].dmg':45, 'act[0].dmgReal':4, 'act[0].round.all':10},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].dmg' : -5, 'act[0].round.all':-5  },
         '27':{  'act[0].dmg' : -10, 'act[0].round.all':-10  },
         '28':{  'act[0].dmg' : -15, 'act[0].round.all':-15  },
         '29':{  'act[0].dmg' : -20, 'act[0].round.all':-20  },
         '30':{  'act[0].dmg' : -25, 'act[0].round.all':-25  },
      },
      'high':{
         'lv':13,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':10},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill290h':{
                     'act':[{
                        'priority':9051,
                        'type': 2,
                        'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'cond':[['comp','agi']],
                        'info':['舍身：智力胜出',0],	          #应该引用文字配置
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能，不受傲气
                        'binding':{
                           'loss':-50,
                        },
                        'follow':'skill290',            #必须跟随特定行动触发
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':30,
            'type': 3,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src':0,
            'tgt':[1, 0],          #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'all':150},
            'dmg':950,	   #技能伤害系数
            'dmgReal':35,
            'dmgLimit':150,
            'buff':{'buffFire':{'rnd':0,'round':2}},
            'loss':50,     #自损（不会致死）
            'atk0': 1050,      #攻击力源头(前军)
            'eff':'eff290',
            'info':['skill290',1],
         },
      ],
   },

   'skill212':{   #刚毅
      'infoArr':['r|act[0]','%|act[0].binding.res','%|act[1].binding.res'],
      'nextArr':['%|act[0].binding.res'],
      'highArr':['%|high.special[0].change.skill.skill212h.act[0].binding.res'],
      'index':206,
      'type':1,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['lead','agi'],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':400,'powerRate':30},
         },
         {
            'cond':['skill.skill212', '*', 1],
            'rslt':{'powerRate':15},
         },
      ],
      'up':{ 'act[0].binding.res':15},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].binding.res' : -5  },
         '27':{  'act[0].binding.res' : -10  },
         '28':{  'act[0].binding.res' : -15  },
         '29':{  'act[0].binding.res' : -20  },
         '30':{  'act[0].binding.res' : -25  },
      },
      'high':{
         'lv':13,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':10},
            },
         ],
         'special':[{
            'priority':212,
            'change':{
               'skill':{
                  'skill212h':{
                     'act':[{
                        'priority':9403,
                        'type': 4,
                        'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'lv':13,
                        'round':{'all':1000},
                        'cond':[['comp','agi'],['checkBuff', 0, 0, 'buffBanArmyB', '=', 0]],      #额外添加了buffBanArmyB：被禁被动技时，壁垒不再生效
                        'times': -1,    #每回合最多使用次数
                        'binding':{
                           'res':150,	    #减免本次伤害
                        },
                        'info':['skill212_h',1],	          #应该引用文字配置
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':9404,
            'type': 4,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'round':{'all':1000},
            'cond':[['srcArmy',2]],
            'times': -1,    #每回合最多使用次数
            'binding':{
               'res':200,	    #减免本次伤害
            },
            'eff':'eff212',
            'info':['skill212',1],	          #应该引用文字配置
         },
         {
            'priority':9405,
            'type': 4,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'round':{'all':1000},
            'cond':[['srcArmy',0],['comp','lead']],
            'times': -1,    #每回合最多使用次数
            'binding':{
               'res':300,	    #减免本次伤害
            },
            'eff':'eff212',
            'info':['skill212',1],	          #应该引用文字配置
         },
      ],
   },

   'skill211':{   #合围   0单 1双
      'infoArr':['r|act[0]','%|act[0].dmg+300','%|act[1].binding.dmg+900'],
      'nextArr':['%|act[0].dmg+300','%|act[1].binding.dmg+900'],
      'highArr':[],
      'merge':1,   #几次合服后可见
      'index':207,
      'type':1,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['str'],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':600,'powerRate':35},
         },
         {
            'cond':['skill.skill211', '*', 1],
            'rslt':{'powerRate':17},
         },
      ],
      'up':{  'act[1].binding.dmg':180, 'act[1].binding.dmgReal':45, 'act[0].dmg':60, 'act[0].dmgReal':15},
      'high':{
         'lv':13,
         'passive':[
            {
               'rslt':{'power':600,'powerRate':15},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill211h':{
                     'act':[{
                        'priority':9010,
                        'type': 2,
                        'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'cond':[['comp','str']],
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能，不受傲气
                        'binding':{
                           'crit':99999999,
                           'dmgReal':50
                        },
                        'follow':'skill211',            #必须跟随特定行动触发
                        'info':['合围：武力胜出',0],	          #应该引用文字配置
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {  #合围主体需要放0位置，注意改相关位置
            'priority':41,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 0,
            'round':{'3':1000},
            'tgt':[1, -1],          #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'dmgScale':-50,
            'dmg':1200,
            'dmgReal':180,	
            'dmgLimit':180,
            'isMelee':1,
            'times':1,
            'eff':'eff211s',
            'info':['skill211',1],
            'atk0': 1050,      #攻击力源头(前军)
         },
         {   #合围单军攻前
            'priority':42,
            'type': 2,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 0,
            'cond':[['enemyArmy',-1]],  #本行为执行条件（必须全部满足），敌方前后排只剩其一
            'nonSkill':1,
            'times': -1,
            'binding':{
                'dmg':2400,
                'dmgReal':360,
                'dmgScale':-50,
                'dmgLimit':360,	
            },
            'eff':'eff211',
            'follow':'skill211',
            'info':['合围单前',0],
         },
      ],
   },


   'skill294':{   #切割 三合
      'infoArr':['%|act[0].binding.dmg','%|act[1].round.any','-|act[1].binding.removeBuff'],
      'nextArr':['%|act[0].binding.dmg','%|act[1].round.any'],
      'highArr':['%|high.special[0].change.skill.skill294h.act[0].binding.atk1'],
      'merge':3,   #几次合服后可见
      'index':208,
      'type':1,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['cha','lead'],
      'passive':[
         {
            'rslt':{'power':600,'powerRate':100},
         },
         {
            'cond':['skill.skill294', '*', 1],
            'rslt':{'powerRate':17},
         },
      ],
      'up':{  'act[0].binding.dmg':90, 'act[0].binding.dmgReal':18, 'act[0].binding.dmgScale':3, 'act[1].round.any':30},
      'high':{
         'lv':15,
         'passive':[
            {
               'rslt':{'power':600,'powerRate':50},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill294h':{   #远战回合，我方骑兵在攻击时，可额外附加{0}的后军攻击力
                     'act':[{
                        'priority':9065,
                        'type': 2,
                        'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'round':{'far':1000},
                        'times': -1,  
                        'nonSkill':1,    #非技能，不受傲气
                        'binding':{
                           'atk1':100,
                           'dmgReal':100,
                        },
                        'follow':{'keys':{'dmg':1000,'dmgReal':1000}},
                     }],
                  },
               },
            },
         }]
      },
      'act':[    # 分割敌阵，各个击破。每场对决中，我方骑兵攻击首个目标时可额外追加{0}的伤害。若魅力或统帅高于对手，还可随机移除该目标{1}个增益状态
         {
            'priority':2941,
            'type': 16,	            #触发类型，16标后
            'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'free':1,    #有此值时，每回合检查cond等失败不会消耗检查次数（用于条件可变性较强的）
            'condTgt':[
                 ['tgtTeam',1],   #对敌方出手时才生效
            ],
            'binding':{
                 'dmgScale':50,
                 'dmgReal':330,
                 'dmg':2200,
            },
            'follow':{'keys':{'dmg':1000,'dmgReal':1000}},
            'unFollow':{'effFaction':1},
            'allTimes': 1,  
            'eff':'eff294',
            'info':['skill294',1],	          #应该引用文字配置
         },
         {
            'priority':2940,
            'type': 16,	            #触发类型，16标后
            'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'round':{'any':130},
            'cond':[[['comp','cha'],['comp','lead']]],
            'follow':{'attach':'skill294'},     #必须跟随附加技
            'binding':{
               'removeBuff':1,
               #'dmg':300,
            },
         },
      ],
   },


   'skill963':{   #戮尘
      'infoArr':[
         '%|act[0].dmg',
         '%|act[0].buff.buffFaction.rnd',
      ],
      'nextArr':['%|act[0].dmg','%|act[0].buff.buffFaction.rnd'],
      'highArr':['%|high.special[0].change.prop.$armys[0].deBuffRate'],
      'merge':5,
      'index':209,
      'type':1,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':[],
      'passive':[
         {
            'rslt':{'power':600,'powerRate':100},
         },
         {
            'cond':['skill.skill963', '*', 1],
            'rslt':{'powerRate':17},
         },
      ],
      'lvPatch':{   #满足特定等级的补丁
         '30':{  'act[0].buff.buffFaction.rnd':5  },
      },
      'up':{'act[0].dmg':80, 'act[0].dmgReal':50, 'act[0].buff.buffFaction.rnd':5},
      'high':{
         'lv':17,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':60},
            },
         ],
         'special':[{
            'change':{
               'prop':{
                  'armys[0].deBuffRate':-200,
               },
            },
         }],
      },
      'act':[   #受到8次伤害后，骑兵扬尘策马入阵屠戮，对敌方前军或后军造成440%伤害，并有25%几率导致敌方【内讧】，持续2回合；
         {
            'priority':88963,
            'type': 17,             #触发类型，17充能后
            'src': 0,
            'tgt':[1, -2],
            'allTimes': 1,
            'dmg':4400,
            'dmgReal':550,
            'atk0': 1050,
            'dmgLimit':400,
            'energyKeySrc': 'energy963',
            'costKey': 'energy963',
            'cost': 8,
            'buff':{'buffFaction':{'rnd':250, 'round':2}},
            'isMelee':1,
            'eff':'eff963',
            'info':['skill963',1],	          #应该引用文字配置
         },
         {
            'priority':963.001,
            'type': 23,             #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 0,              #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[0, 0],
            'times': -1,
            'nonSkill':1,    #非技能，不受傲气和特殊加成影响
            'noBfr':1,    
            'noAft':1,  
            'condHpChange':[['<',0],None],  #兵力受损后
            'energyKey':'energy963',
            'energy':1,
            'time':0,
            'eff':'effNull',
            'info':['戮尘储能',0],           #应该引用文字配置
         },
      ],
   },

   'skill967':{   #骑胄
      'infoArr':[
         '%|0-act[0].binding.res',
         '%|0-act[1].binding.res',
      ],
      'nextArr':['%|0-act[0].binding.res','%|0-act[1].binding.res'],
      'highArr':['*%|high.special[0].change.skill.$skill209.act[0].dmgScale'],
      'merge':5,
      'index':210,
      'type':1,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':[],
      'passive':[
         {
            'rslt':{'power':600,'powerRate':100},
         },
         {
            'cond':['skill.skill967', '*', 1],
            'rslt':{'powerRate':17},
         },
      ],
      'up':{'act[0].binding.res':6, 'act[1].binding.res':3},
      'high':{
         'lv':17,
         'passive':[
            {
               'rslt':{'power':500,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill209.act[0].dmgScale':100,
                  'skill210.act[0].dmgScale':100,
                  'skill290.act[0].dmgScale':100,
                  'skill211.act[0].dmgScale':100,
                  'skill963.act[0].dmgScale':100,
               },
            },
         }],
      },
      'act':[   #骑兵受到兵种技伤害-10%，受到英雄技伤害-5%
         {
            'priority':9422.967,
            'type': 4,         #触发类型，防前
            'src': 0,
            'times': -1,
            'allTimes': -1,
            #'cond':[['srcArmy',-1],['srcTeam',1]],
            'binding':{'res':100},
            'unFollow':{'keys':{'allowSelf':1000}},
            'follow':{
               '_type_0_A':1,'_type_1_A':1,'_type_2_A':1,'_type_3_A':1,
            },
            'info':['skill967',1],
         },
         {
            'priority':-9423.967,
            'type': 4,             #触发类型，防前
            'src': 0,
            'times': -1,
            'allTimes': -1,
            'cond':[['srcArmy',2],['srcTeam',1]],
            'binding':{'res':50},
            'unFollow':{'keys':{'allowSelf':1000}},
            'follow':{'useBase':1, 'keys':{'isHero':1000}},
            'info':['skill967',1],
         },
      ],
   },


   'skill978':{   #掷矛
      'infoArr':['%0|act[0].dmg'],
      'nextArr':['%0|act[0].dmg'],
      'highArr':[],
      'unlockArr':[],
      'merge':7,   #几次合服后可见
      'index':211,
      'type':1,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['cha','str'],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':600,'powerRate':50},
         },
         {
            'cond':['skill.skill978', '*', 1],
            'rslt':{'powerRate':17},
         },
      ],
      'special':[{
           'priority':-888978,
           'cond':['enemy','army',0],
                'change':{
                     'skill':{
                          'skill978.act[0].combo':2,
                     },
                },
           },
      ],
      'up':{ 'act[0].dmg':68.96, 'act[0].dmgReal':9},
      'unlock':[
        {
         'lv':10,
         'passive':[
            {
               'cond':['army[0].type', '=', 1],
               'rslt':{'power':150,'powerRate':20,'cha':1,'str':1},
            },
         ],
         'special':[{
           'change':{
              'skill':{
                 'skill978.act[0].dmgScale':250,
              },
           },
         }],
        },
        {
         'lv':17,
         'passive':[
            {
               'rslt':{'power':350,'powerRate':50},
            },
         ],
         'special':[{  #若魅力或武力高于对手，【掷矛】每段伤害可额外消灭目标15%的当前兵力
            'change':{
               'skill':{
                  'skill978h':{
                     'act':[{
                        'priority':888.978,
                        'type': 2,
                        'src': 0,
                        'cond':[[['comp','cha'],['comp','str']]],
                        'times': -1,
                        'nonSkill':1,
                        'binding':{
                            'dmgRealRate':150,
                        },
                        'follow':'skill978',
                        'info':['掷矛：武魅胜出',0],
                     }],
                  },
               },
            },
         }]
        },
      ],
      'act':[
         {  #远战首回合发动，忽略敌方被动技能，对敌方前军造成200%（400%）伤害。若敌方前军为步兵，则额外造成1段伤害
            'priority':88978,
            'type': 1,
            'src': 0,
            'tgt':[1, 0],
            'round':{'1':1000},
            'dmg':2000,
            'dmgReal':250,
            'dmgLimit':200,
            'atk0': 1050,
            'enforceCrit':100,
            'allowSelf':1,
            'noBfr':-1,
            'noAft':-1,
            'times': 1,
            'isRemote':1,
            'eff':'eff978',
            'info':['skill978',1],
         },
      ],
   },


   'skill985':{   #逸迹
      'infoArr':['%|act[0].round.any'],
      'nextArr':['%|act[0].round.any'],
      'highArr':[],
      'unlockArr':[],
      'merge':7,   #几次合服后可见
      'index':212,
      'type':1,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':[],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':600,'powerRate':50},
         },
         {
            'cond':['skill.skill985', '*', 1],
            'rslt':{'powerRate':17},
         },
      ],
      'up':{ 'act[0].round.any':6.89, 'act[1].lv':1},
      'unlock':[
        {
         'lv':10,
         'passive':[     #骑兵自身按比例损失兵力时，也有同等几率触发【逸迹】
            {
               'rslt':{'power':150,'powerRate':20},
            },
         ],
         'special':[{
            'priority':-88985,
            'change':{
               'skill':{
                  'skill985.act[1].round.any':1000,
               },
            },
         }]
        },
        {
         'lv':17,
         'passive':[   #远战第二回合，骑兵触发【逸迹】的几率+25%
            {
               'rslt':{'power':350,'powerRate':50},
            },
         ],
         'special':[{
            'priority':-88985.1,
            'change':{
               'skill':{
                  'skill985.act[0].round.2':250,
               },
            },
         }]
        },
      ],
      'act':[   #骑兵受到攻击时，若攻击中包含以任意方兵力比例消灭部队的效果，则有20%（40%）的几率触发【逸迹】，使本段攻击无法命中
         {
            'priority':889850,
            'type': 4,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'round':{'any':200}, 
            'times': -1,
            'binding':{
                'acc':-1000,
            },
            'unFollow':{'equip113_4':1,'hero7702':1,'hero7702_1':1,'plan7005_1':1,'hero725r':1},
            'follow':{'useBase':1, 'keys':{'dmgRealHp1':1000,'dmgRealHp0':1000,'dmgRealRate':1000,'dmgRealMax':1000,'dmgRealLost':1000}},
            #'eff':'eff985',
            'info':['skill985',1],
         },
         {
            'priority':889851,
            'type': 2,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'round':{'any':0}, 
            'times': -1,
            'binding':{
                'loss':'del',
            },
            'follow':{'useBase':1, 'keys':{'loss':1000}},
            'lv':1,
            #'eff':'eff985',
            'info':['skill985',1],
         },
      ],
   },


#弓兵技

   'skill213':{   #强弩
      'infoArr':['r|act[0]','%|act[0].binding.ignDef/0.6'],
      'nextArr':['%|act[0].binding.ignDef/0.6'],
      'highArr':['p|high.passive'],
      'index':301,
      'type':2,
      'cost_type':0,
      'max_level':25,
      'shogun_type':0,
      'ability_info':[],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':200,'powerRate':20},
         },
         {
            'cond':['skill.skill213', '*', 1],
            'rslt':{'powerRate':9},
         },
      ],
      'up':{ 'act[0].binding.ignDef':6, 'act[0].binding.dmgReal':5},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].binding.ignDef' : -3  },
         '27':{  'act[0].binding.ignDef' : -6  },
         '28':{  'act[0].binding.ignDef' : -9  },
         '29':{  'act[0].binding.ignDef' : -12  },
         '30':{  'act[0].binding.ignDef' : -15  },
      },
      'high':{
         'lv':10,
         'passive':[
            {
               'cond':['army[1].type', '=', 2],
               'rslt':{'army[1].atkBase':50},
            },
            {
               'rslt':{'power':200,'powerRate':5},
            },
         ],
      },
      'act':[
         {
            'priority':9011,
            'type': 2,
            'src': 1,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'round':{'near':1000},
            'times': -1,    #每回合最多使用次数
            'eff':'eff213',
            'info':['skill213',1],	          #应该引用文字配置
            'binding':{
               'ignDef':90,     #忽视防御
               'dmgReal':20,
            },
            'follow':{'keys':{'dmg':1000,'dmgReal':1000}},
         },
      ],
   },
   'skill215':{   #闪避
      'infoArr':['r|act[0]','%|act[0].round.all','%|act[0].binding.res*1.25'],
      'nextArr':['%|act[0].round.all', '%|act[0].binding.res*1.25'],
      'highArr':['p|high.passive'],
      'index':302,
      'type':2,
      'cost_type':0,
      'max_level':25,
      'shogun_type':0,
      'ability_info':[],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':200,'powerRate':20},
         },
         {
            'cond':['skill.skill215', '*', 1],
            'rslt':{'powerRate':9},
         },
      ],
      'up':{ 'act[0].binding.res':8},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].binding.res' : -4  },
         '27':{  'act[0].binding.res' : -8  },
         '28':{  'act[0].binding.res' : -12  },
         '29':{  'act[0].binding.res' : -16  },
         '30':{  'act[0].binding.res' : -20  },
      },
      'high':{
         'lv':13,
         'passive':[
            {
               'cond':['army[1].type', '=', 2],
               'rslt':{'army[1].spd':10,'army[0].hpm':50},
            },
            {
               'rslt':{'power':200,'powerRate':5},
            },
         ],
      },
      'act':[
         {
            'priority':9406,
            'type': 4,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 1,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'round':{'all':400},
            'times': -1,    #每回合最多使用次数
            'binding':{
               'res':104,	    #减免本次伤害
            },
            'eff':'eff215',
            'info':['skill215',1],	          #应该引用文字配置
         },
      ],
   },

   'skill214':{   #回射
      'infoArr':['r|act[0]','%|act[0].dmg'],
      'nextArr':['%|act[0].dmg'],
      'highArr':['p|high.passive'],
      'index':303,
      'type':2,
      'cost_type':1,
      'max_level':25,
      'shogun_type':1,
      'ability_info':[],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':300,'powerRate':25},
         },
         {
            'cond':['skill.skill214', '*', 1],
            'rslt':{'powerRate':11},
         },
      ],
      'up':{ 'act[0].dmg':25, 'act[0].dmgReal':1},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].dmg' : -10  },
         '27':{  'act[0].dmg' : -20  },
         '28':{  'act[0].dmg' : -30  },
         '29':{  'act[0].dmg' : -40  },
         '30':{  'act[0].dmg' : -50  },
      },
      'high':{
         'lv':10,
         'passive':[
            {
               'cond':['army[1].type', '=', 2],
               'rslt':{'army[1].defBase':20},
            },
            {
               'rslt':{'power':300,'powerRate':5},
            },
         ],
      },
      'act':[
         {
            'priority':9501,
            'type': 5,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 1,
            'round':{'far':1000,},
            'tgt':[1, 1],          #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'times': -1,    #每回合最多使用次数
            'noAft': 1,    #本次行动不能触发后置绑定效果
            'dmg':700,	#技能伤害系数
            'dmgReal':15,
            'dmgLimit':100,
            'buff':{'buffStun':{'rnd':0, 'round':1},'buffFire':{'rnd':0, 'round':2}},
            'cond':[['army',1],['srcArmy',1],['srcTeam',1]],
            'atk1': 1000,    #攻击力源头(后军)
            'isRemote':1,
            'eff':'eff214',
            'info':['skill214',1],	          #应该引用文字配置
         },
      ],
   },

   'skill216':{   #火箭
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[1].binding.dmg'],
      'nextArr':['%|act[0].dmg'],
      'highArr':[
         '%|high.special[0].change.skill.skill216h.act[0].binding.$buff.buffFire.rnd',
         '-|act[0].buff.buffFire.round',
         '%|100'
      ],
      'index':304,
      'type':2,
      'cost_type':1,
      'max_level':25,
      'shogun_type':1,
      'ability_info':['lead','agi'],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':300,'powerRate':25},
         },
         {
            'cond':['skill.skill216', '*', 1],
            'rslt':{'powerRate':12},
         },
      ],
      'up':{ 'act[0].dmg':10, 'act[0].dmgReal':2},
      'high':{
         'lv':13,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':10},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill216h':{
                     'act':[{
                        'priority':9012,
                        'type': 2,
                        'src': 1,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'cond':[['comp','agi']],
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能，不受傲气
                        'binding':{
                             'buff.buffFire.rnd':400,
                        },
                        'follow':'skill216',            #必须跟随特定行动触发
                        'info':['火箭：智力胜出',0],	          #应该引用文字配置
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':82,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 1,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, -1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'1':1000},
            'times':1,
            'combo':1,
            'dmg':900,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':12,
            'dmgLimit':110,
            #'element':'Fire',
            'atk1': 1080,    #攻击力源头(后军)
            'buff':{'buffFire':{'rnd':0, 'round':1}},
            'isRemote':1,
            'eff':'eff216',
            'info':['skill216',1],	          #应该引用文字配置
         },
         {
            'priority':9013,
            'type': 2,
            'src': 1,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'cond':[['comp','lead']],
            'times': -1,    #每回合最多使用次数
            'nonSkill':1,    #非技能，不受傲气
            'binding':{
               'dmg':120,
               'dmgReal':2,
            },
            'follow':'skill216',            #必须跟随特定行动触发
            'info':['火箭：统帅胜出',0],	          #应该引用文字配置
         },
      ],
   },
   'skill217':{   #金汁
      'infoArr':['r|act[0]','%|act[0].round.all','%|act[0].binding.buff.buffPoison.prop.atkRate*2'],
      'nextArr':['%|act[0].round.all','%|act[0].binding.buff.buffPoison.prop.atkRate*2'],
      'highArr':['%|high.special[0].change.skill.$skill217.act[0].round.all'],
      'index':305,
      'type':2,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['str'],
      #'limit':{},
      #'fast_learn':500,
      'passive':[
         {
            'rslt':{'power':400,'powerRate':30},
         },
         {
            'cond':['skill.skill217', '*', 1],
            'rslt':{'powerRate':14},
         },
      ],
      'up':{ 'act[0].binding.buff.buffPoison.prop.atkRate':-2, 'act[0].round.all':5},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].binding.buff.buffPoison.prop.atkRate' : 1, 'act[0].round.all':-3  },
         '27':{  'act[0].binding.buff.buffPoison.prop.atkRate' : 2, 'act[0].round.all':-6  },
         '28':{  'act[0].binding.buff.buffPoison.prop.atkRate' : 3, 'act[0].round.all':-9  },
         '29':{  'act[0].binding.buff.buffPoison.prop.atkRate' : 4, 'act[0].round.all':-12  },
         '30':{  'act[0].binding.buff.buffPoison.prop.atkRate' : 5, 'act[0].round.all':-15  },
      },
      'high':{
         'lv':13,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':10},
            },
         ],
         'special':[{
            'cond':[['compare','str','>']],
            'change':{
               'skill':{
                  'skill217.act[0].round.all':200,
               },
            },
         }]
      },
      'act':[
         {
            'priority':9014,
            'type': 2,
            'src': 1,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'round':{'all':250},
            'eff':'eff217',
            'times': -1,    #每回合最多使用次数
            'binding':{
               'buff':{'buffPoison':{'round':1, 'prop':{'atkRate':-40 } }},
            },
            'info':['skill217',1],	          #应该引用文字配置
         },
      ],
   },
   'skill218':{   #齐射
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[1].binding.ignDef'],
      'nextArr':['%|act[0].dmg'],
      'highArr':[],
      'index':306,
      'type':2,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['cha','str'],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':400,'powerRate':30},
         },
         {
            'cond':['skill.skill218', '*', 1],
            'rslt':{'powerRate':15},
         },
      ],
      'up':{ 'act[0].dmg':20, 'act[0].dmgReal':2},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].dmg' : -5  },
         '27':{  'act[0].dmg' : -10  },
         '28':{  'act[0].dmg' : -15  },
         '29':{  'act[0].dmg' : -20  },
         '30':{  'act[0].dmg' : -25  },
      },
      'high':{
         'lv':13,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':10},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill218h':{
                     'act':[{
                        'priority':9015,
                        'type': 2,
                        'src': 1,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'cond':[['comp','str']],
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能，不受傲气
                        'binding':{
                           'combo':1,	   
                        },
                        'follow':'skill218',            #必须跟随特定行动触发
                        'info':['齐射：武力胜出',0],	          #应该引用文字配置
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':62,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 1,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, -2],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'2':1000},
            'dmg':1300,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':30,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgLimit':150,
            'atk1': 1100,    #攻击力源头(后军)
            'combo':2,
            'isRemote':1,
            'eff':'eff218',
            'info':['skill218',1],	          #应该引用文字配置
         },
         {
            'priority':9016,
            'type': 2,
            'src': 1,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'cond':[['comp','cha']],
            'times': -1,    #每回合最多使用次数
            'nonSkill':1,    #非技能，不受傲气
            'binding':{'ignDef':200,   'dmgReal':10   },
            'follow':'skill218',            #必须跟随特定行动触发
            'info':['齐射：魅力胜出',0],	          #应该引用文字配置
         },
      ],
   },
   'skill291':{   #轮射
      'infoArr':['r|act[0]','-|act[0].times','%|act[0].dmg'],
      'nextArr':['%|act[0].dmg'],
      'highArr':[
         '%|high.special[0].change.skill.skill291h.act[0].binding.$buff.buffStun.rnd',
         '-|act[0].buff.buffStun.round'
      ],
      'merge':1,   #几次合服后可见
      'index':307,
      'type':2,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['lead'],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':600,'powerRate':35},
         },
         {
            'cond':['skill.skill291', '*', 1],
            'rslt':{'powerRate':17},
         },
      ],
      'up':{  'act[0].dmg':25, 'act[0].dmgReal':6},
      'high':{
         'lv':13,
         'passive':[
            {
               'rslt':{'power':600,'powerRate':15},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill291h':{
                     'act':[{
                        'priority':9052,
                        'type': 2,
                        'src': 1,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'cond':[['comp','lead']],
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能，不受傲气
                        'binding':{
                           'dmg':30,	#技能伤害系数。文武、性别、兵种类型默认
                           'dmgReal':5,
                           'buff.buffStun.rnd':100,
                        },
                        'follow':'skill291',            #必须跟随特定行动触发
                        'info':['轮射：统帅胜出',0],	          #应该引用文字配置
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':52,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 1,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, -1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'3':1000},
            #'allTimes': 3,
            'times': 3,
            'dmg':520,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':75,   
            'dmgLimit':65, 
            'atk1': 1000,   
            'buff':{
                'buffStun':{ 'rnd':0, 'round':1,},
            },
            'isRemote':1,
            'eff':'eff291',
            'info':['skill291',1],	          #应该引用文字配置
         },
      ],
   },
   'skill291_':{   #前军轮射
      'up':{  'act[0].dmg':25, 'act[0].dmgReal':6},
      'high':{
         'lv':13,
         'passive':[
            {
               'rslt':{'power':600,'powerRate':15},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill291h_':{
                     'act':[{
                        'priority':9052,
                        'type': 2,
                        'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'cond':[['comp','lead']],
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能，不受傲气
                        'binding':{
                           'dmg':30,	#技能伤害系数。文武、性别、兵种类型默认
                           'dmgReal':5,
                           'buff.buffStun.rnd':100,
                        },
                        'follow':'skill291_',            #必须跟随特定行动触发
                        'info':['前轮射：统帅胜出',0],	          #应该引用文字配置
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':52,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, -1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'3':1000},
            #'allTimes': 3,
            'times': 3,
            'dmg':520,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':75,   
            'dmgLimit':65, 
            'atk0': 1000,   
            'buff':{
                'buffStun':{ 'rnd':0, 'round':1,},
            },
            'isRemote':1,
            'eff':'eff291',
            'actId':'skill291_',
            'info':['skill291',1],	          #应该引用文字配置
         },
      ],
   },

   'skill295':{   #飞羽破空   三合
      'infoArr':['%|act[0].binding.dmg','%|act[1].round.1'],
      'nextArr':['%|act[0].binding.dmg','%|act[1].round.1'],
      'highArr':['*|high.special[0].change.skill.skill295h.act[0].binding.dmg'],
      'merge':3,   #几次合服后可见
      'index':308,
      'type':2,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['agi','cha'],
      'passive':[
         {
            'rslt':{'power':600,'powerRate':100},
         },
         {
            'cond':['skill.skill295', '*', 1],
            'rslt':{'powerRate':17},
         },
      ],
      'up':{  'act[0].binding.dmg':50, 'act[0].binding.dmgReal':9,  'act[1].round.1':30},
      'high':{
         'lv':15,
         'passive':[
            {
               'rslt':{'power':600,'powerRate':50},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill295h':{    #所有回合，我方弓兵对后军伤害{0}
                     'act':[{
                        'priority':2952,
                        'type': 16,
                        'src': 1,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'condTgt':[
                             ['tgtTeam',1],  [['armyType',2],['armyType',3]]
                        ],
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能，不受傲气
                        'binding':{
                           'dmgScale':100,
                           'dmg':150,	
                           'dmgReal':100,
                        },
                        'follow':{'keys':{'dmg':1000,'dmgReal':1000}},
                        'info':['破空克后',0],	          #应该引用文字配置
                     }],
                  },
               },
            },
         }]
      },
      'act':[      #巧占先机，一发入魂。远战首回合，我方弓兵在首次主动攻击时，对所有目标追加{0}的伤害。若智力或魅力高于对手，该次攻击有{1}的几率忽略敌方被动技能
         {
            'priority':2951,
            'type': 2,	            #触发类型，攻前
            'src': 1,	            #发出源 0前军，1后军，【2英雄】，-1全军
            #'free':1,    #有此值时，每回合检查cond等失败不会消耗检查次数（用于条件可变性较强的）
            'round':{'1':1000},
            'binding':{
                 'dmgReal':140,
                 'dmg':1050,
            },
            'unFollow':{'skill214':1,'skill276':1,'effFaction':1},
            'follow':{'keys':{'dmg':1000,'dmgReal':1000}},
            #'times': 2,  
            'mergeEff':1,
            'eff':'eff295',
            'info':['skill295',1],	          #应该引用文字配置
         },
         {
            'priority':2950,
            'type': 2,	            #触发类型，攻前
            'src': 1,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'round':{'1':130},
            'cond':[[['comp','agi'],['comp','cha']]],
            'follow':{'attach':'skill295'},     #必须跟随附加技
            'times': -1,  
            'binding':{
                'banFollow':{'defBinding':1},
            },
            'info':['破空忽略',0],	          #应该引用文字配置
         },
      ],
   },

   'skill964':{   #焱雨
      'infoArr':[
         '%|act[0].dmg',
         '%|act[0].buff.buffFire.rnd',
      ],
      'nextArr':['%|act[0].dmg','%|act[0].buff.buffFire.rnd'],
      'highArr':['%|high.special[0].change.prop.$armys[1].deBuffRate'],
      'merge':5,
      'index':309,
      'type':2,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':[],
      'passive':[
         {
            'rslt':{'power':600,'powerRate':100},
         },
         {
            'cond':['skill.skill964', '*', 1],
            'rslt':{'powerRate':17},
         },
      ],
      'lvPatch':{   #满足特定等级的补丁
         '30':{  'act[0].buff.buffFire.rnd':5  },
      },
      'up':{'act[0].dmg':50, 'act[0].dmgReal':25, 'act[0].buff.buffFire.rnd':5},
      'high':{
         'lv':17,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':60},
            },
         ],
         'special':[{
            'change':{
               'prop':{
                  'armys[1].deBuffRate':-200,
               },
            },
         }],
      },
      'act':[   #对敌方造成6次伤害后，弓兵立即发动一次蓄力乱射，对敌方全军造成240%伤害，并有25%的几率导致敌方【火灾】，持续2回合
         {
            'priority':889641,
            'type': 17,
            'srcArmy': 2,
            'srcCanAction': 1,
            'tgt':[1, -1],
            'allTimes': 1,
            'dmg':2400,
            'dmgReal':350,
            'atk1': 1050,
            'dmgLimit':240,
            #'element':'Fire',
            'energyKeySrc': 'beHitSkill964_1',
            'costKey':'beHitSkill964_1',
            'cost': 6,
            'buff':{'buffFire':{'rnd':250, 'round':2}},
            'eff':'eff964',
            'info':['skill964',1],	          #应该引用文字配置
         },
         {
            'priority': 889640,	 
            'type': 2,             #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'srcArmy': 2,          #发出源 
            'nonSkill':1,
            'follow':{'keys':{'dmg':1000,'dmgReal':1000}},  
            'times':-1,  
            'binding':{
               'energys':{
                  'ESkill964':{     #充能包
                     'priority':889641,
                     'condE':['dmg','>',0],
                     'srcE':{
                        'beHitSkill964_1':{'num':1,'checkAct':1},
                     },
                  }
               }
            },
            'info':['攻前充能',0],
         },
      ],
   },



   'skill968':{   #掩蔽
      'infoArr':[
         '%|0-act[0].binding.res',
         '%|0-act[1].binding.res',
      ],
      'nextArr':['%|0-act[0].binding.res','%|0-act[1].binding.res'],
      'highArr':['*%|high.special[0].change.skill.$skill214.act[0].dmgScale'],
      'merge':5,
      'index':310,
      'type':2,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':[],
      'passive':[
         {
            'rslt':{'power':600,'powerRate':100},
         },
         {
            'cond':['skill.skill968', '*', 1],
            'rslt':{'powerRate':17},
         },
      ],
      'up':{'act[0].binding.res':6, 'act[1].binding.res':3},
      'high':{
         'lv':17,
         'passive':[
            {
               'rslt':{'power':500,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill214.act[0].dmgScale':100,
                  'skill216.act[0].dmgScale':100,
                  'skill218.act[0].dmgScale':100,
                  'skill291.act[0].dmgScale':100,
                  'skill964.act[0].dmgScale':100,
               },
            },
         }],
      },
      'act':[   #弓兵受到英雄技伤害-10%，受到副将技伤害-5%
         {
            'priority':9423,
            'type': 4,         #触发类型，防前
            'src': 1,
            'times': -1,
            'allTimes': -1,
            'cond':[['srcArmy',2],['srcTeam',1]],
            'binding':{'res':100},
            'unFollow':{'keys':{'allowSelf':1000}},
            'follow':{'useBase':1, 'keys':{'isHero':1000}},
            'info':['skill968',1],
         },
         {
            'priority':9424.968,
            'type': 4,             #触发类型，防前
            'src': 1,
            'times': -1,
            'allTimes': -1,
            #'cond':[['srcTeam',1]],
            'binding':{'res':50},
            'follow':{'element':'Adjutant'},
            'info':['skill968',1],
         },
      ],
   },


   'skill979':{   #落月
      'infoArr':['%0|act[0].dmg'],
      'nextArr':['%0|act[0].dmg'],
      'highArr':[],
      'unlockArr':[],
      'merge':7,   #几次合服后可见
      'index':311,
      'type':2,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['cha','lead'],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':600,'powerRate':50},
         },
         {
            'cond':['skill.skill979', '*', 1],
            'rslt':{'powerRate':17},
         },
      ],
      'special':[{
           'priority':-888979,
           'cond':['enemy','army',3],
                'change':{
                     'skill':{
                          'skill979.act[0].timesRnd':10000,
                     },
                },
           },
      ],
      'up':{ 'act[0].dmg':75.86, 'act[0].dmgReal':10},
      'unlock':[
        {
         'lv':10,
         'passive':[
            {
               'cond':['army[1].type', '=', 2],
               'rslt':{'power':150,'powerRate':20,'cha':1,'lead':1},
            },
         ],
         'special':[{
           'change':{
              'skill':{
                 'skill9797.act[0].dmgScale':250,
              },
           },
         }],
        },
        {
         'lv':17,
         'passive':[
            {
               'rslt':{'power':350,'powerRate':50},
            },
         ],
         'special':[{  #若魅力或统御高于对手，远战第二回合弓兵行动前，发动【皎洁】驱散自身2个不良状态
            'change':{
               'skill':{
                  'skill979h':{
                     'act':[{
                        'priority':888.979,
                        'type': 10,
                        'src': 1,
                        'tgt':[0, -5],    
                        'round':{'2':1000},
                        'cond':[[['comp','cha'],['comp','lead']]],  

                        'nonSkill':1,
                        'noBuff':1,
                        'noBfr':1,
                        'noAft':1,

                        'removeDebuff':2,

                        'eff':'eff979h',
                        'lv': 17,
                        'info':['skill979_1',1],
                     }],
                  },
               },
            },
         }]
        },
      ],
      'act':[
         {  #远战第二回合发动，忽略敌方被动技能，对敌方后军造成220%（440%）伤害。若敌方后军为方士，则可额外连发1次【落月】
            'priority':88979,
            'type': 1,
            'src': 1,
            'tgt':[1, 1],
            'round':{'2':1000},
            'dmg':2200,
            'dmgReal':275,
            'dmgLimit':220,
            'enforceCrit':100,
            'atk1': 1050,
            'allowSelf':1,
            'noBfr':-1,
            'noAft':-1,
            'times': 1,
            'isRemote':1,
            'eff':'eff979',
            'info':['skill979',1],
         },
      ],
   },


#方士技

   'skill219':{   #罡气
      'infoArr':['r|act[0]','%|act[0].binding.res'],
      'nextArr':['%|act[0].binding.res'],
      'highArr':['p|high.passive'],
      'index':401,
      'type':3,
      'cost_type':0,
      'max_level':25,
      'shogun_type':0,
      'ability_info':[],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':200,'powerRate':20},
         },
         {
            'cond':['skill.skill219', '*', 1],
            'rslt':{'powerRate':9},
         },
      ],
      'up':{ 'act[0].binding.res':10},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].binding.res' : -5  },
         '27':{  'act[0].binding.res' : -10  },
         '28':{  'act[0].binding.res' : -15  },
         '29':{  'act[0].binding.res' : -20  },
         '30':{  'act[0].binding.res' : -25  },
      },
      'high':{
         'lv':10,
         'passive':[
            {
               'cond':['army[1].type', '=', 3],
               'rslt':{'army[1].defBase':20},
            },
            {
               'rslt':{'power':300,'powerRate':5},
            },
         ],
      },
      'act':[
         {
            'priority':9407,
            'type': 4,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 1,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'round':{'all':1000},
            'cond':[['srcArmy',2]],
            'times': -1,    #每回合最多使用次数
            'binding':{
               'res':140,	    #减免本次伤害
            },
            'eff':'eff219',
            'info':['skill219',1],	          #应该引用文字配置
         },
      ],
   },
   'skill220':{   #蚀甲
      'infoArr':['r|act[0]','%|act[0].binding.ignDef'],
      'nextArr':['%|act[0].binding.ignDef'],
      'highArr':['p|high.passive'],
      'index':402,
      'type':3,
      'cost_type':0,
      'max_level':25,
      'shogun_type':0,
      'ability_info':[],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':200,'powerRate':20},
         },
         {
            'cond':['skill.skill220', '*', 1],
            'rslt':{'powerRate':9},
         },
      ],
      'up':{ 'act[0].binding.ignDef':10, 'act[0].binding.dmgReal':1},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].binding.ignDef' : -5  },
         '27':{  'act[0].binding.ignDef' : -10  },
         '28':{  'act[0].binding.ignDef' : -15  },
         '29':{  'act[0].binding.ignDef' : -20  },
         '30':{  'act[0].binding.ignDef' : -25  },
      },
      'high':{
         'lv':10,
         'passive':[
            {
               'cond':['army[1].type', '=', 3],
               'rslt':{'army[1].atkBase':50},
            },
            {
               'rslt':{'power':200,'powerRate':5},
            },
         ],
      },
      'act':[
         {
            'priority':9017,
            'type': 2,     #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 1,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'round':{'far':1000},
            'times': -1,    #每回合最多使用次数
            'binding':{'ignDef':120,'dmgReal':8},
            'follow':{'keys':{'dmg':1000,'dmgReal':1000}},
            'info':['skill220',1],	          #应该引用文字配置
            'eff':'eff220',
         },
      ],
   },
   'skill221':{   #疾风
      'infoArr':['r|act[0]','%|act[0].round.all','%|act[0].dmg'],
      'nextArr':['%|act[0].round.all', '%|act[0].dmg'],
      'highArr':['p|high.passive'],
      'index':403,
      'type':3,
      'cost_type':1,
      'max_level':25,
      'shogun_type':1,
      'ability_info':[],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':300,'powerRate':25},
         },
         {
            'cond':['skill.skill221', '*', 1],
            'rslt':{'powerRate':11},
         },
      ],
      'up':{ 'act[0].dmg':25, 'act[0].dmgReal':2},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].dmg' : -5  },
         '27':{  'act[0].dmg' : -10  },
         '28':{  'act[0].dmg' : -15  },
         '29':{  'act[0].dmg' : -20  },
         '30':{  'act[0].dmg' : -25  },
      },
      'high':{
         'lv':13,
         'passive':[
            {
               'cond':['army[1].type', '=', 3],
               'rslt':{'army[1].spd':10,'army[0].hpm':50},
            },
            {
               'rslt':{'power':300,'powerRate':5},
            },
         ],
      },
      'act':[
         {
            'priority':30,
            'type': 3,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src':1,
            'tgt':[1, 1],          #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'all':400},
            'times': 1,    #每回合最多使用次数
            #'noAft': 1,    #本次行动不能触发后置绑定效果
            'atk1': 1000,    #攻击力源头(后军)
            'dmg':420,	#技能伤害系数
            'dmgReal':15,
            'dmgLimit':80,
            'buff':{'buffFrozen':{'rnd':0,'round':2},'buffFire':{'rnd':0, 'round':2}},
            'eff':'eff221',
            'info':['skill221',1],
         },
      ]
   },
   'skill222':{   #地刺
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[1].binding.dmg'],
      'nextArr':['%|act[0].dmg'],
      'highArr':[
         '%|high.special[0].change.skill.skill222h.act[0].binding.dmg',
         '%|high.special[0].change.skill.skill222h.act[0].binding.$buff.buffStun.rnd',
         '-|act[0].buff.buffStun.round'
      ],
      'index':404,
      'type':3,
      'cost_type':1,
      'max_level':25,
      'shogun_type':1,
      'ability_info':['agi','lead'],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':300,'powerRate':25},
         },
         {
            'cond':['skill.skill222', '*', 1],
            'rslt':{'powerRate':12},
         },
      ],
      'up':{ 'act[0].dmg':15,'act[0].dmgReal':2},
      'high':{
         'lv':13,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':10},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill222h':{
                     'act':[{
                        'priority':9018,
                        'type': 2,
                        'src': 1,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'cond':[['comp','lead']],
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能，不受傲气
                        'binding':{
                           'dmg':150,	#技能伤害系数。文武、性别、兵种类型默认
                           'dmgReal':3,
                           'buff.buffStun.rnd':150,
                        },
                        'follow':'skill222',            #必须跟随特定行动触发
                        'info':['地刺：统帅胜出',0],	          #应该引用文字配置
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':63,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 1,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, -1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'2':1000},
            'times':1,
            'combo':1,
            'dmg':1200,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':20,
            'atk1': 1000,    #攻击力源头(后军)
            'dmgLimit':130,
            'buff':{'buffStun':{'rnd':0, 'round':1}},
            'isRemote':1,
            'eff':'eff222',
            'info':['skill222',1],	          #应该引用文字配置
         },
         {
            'priority':9019,
            'type': 2,
            'src': 1,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'cond':[['comp','agi']],
            'times': -1,    #每回合最多使用次数
            'nonSkill':1,    #非技能，不受傲气
            'binding':{'dmg':150,   'dmgReal':3,},
            'follow':'skill222',            #必须跟随特定行动触发
            'info':['地刺：智力胜出',0],	          #应该引用文字配置
         },
      ],
   },
   'skill223':{   #咒印
      'infoArr':['%|act[0].binding.dmg','r|act[1]','%|act[1].binding.res'],
      'nextArr':['%|act[0].binding.dmg','%|act[1].binding.res'],
      'highArr':[
         '%|high.special[0].change.skill.skill223h.act[0].binding.dmg',
         '%|high.special[0].change.skill.skill223h.act[0].binding.dmgDebuff',
      ],
      'index':405,
      'type':3,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['cha'],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':400,'powerRate':30},
         },
         {
            'cond':['skill.skill223', '*', 1],
            'rslt':{'powerRate':14},
         },
      ],
      'up':{ 
         'act[0].binding.dmg':20,
         'act[0].binding.dmgReal':1,
         'act[1].binding.res':10,
      },
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[1].binding.res' : -5  },
         '27':{  'act[1].binding.res' : -10  },
         '28':{  'act[1].binding.res' : -15  },
         '29':{  'act[1].binding.res' : -20  },
         '30':{  'act[1].binding.res' : -25  },
      },
      'high':{
         'lv':13,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':10},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill223h':{
                     'act':[{
                        'priority':9020,
                        'type': 2,
                        'src': 1,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'round':{'near':10000},
                        'cond':[['comp','cha']],
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能，不受傲气
                        'binding':{
                           'dmgDebuff':150,	#技能伤害系数。文武、性别、兵种类型默认
                           'dmg':100,     #
                           'dmgReal':10,
                        },
                        'follow':{'attach':'skill223'},     #必须跟随附加技
                        #'follow':'skill223',            #必须跟随特定行动触发
                        'info':['咒印：魅力胜出',0],	          #应该引用文字配置
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':9021,
            'type': 2,
            'src': 1,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'round':{'near':1000,'all':0},
            'times': -1,    #每回合最多使用次数
            'eff':'eff223',
            'info':['skill223',1],	          #应该引用文字配置
            'binding':{
               'dmg':160,     #
               'dmgReal':10,
            },
            'follow':{'keys':{'dmg':1000,'dmgReal':1000}},
         },
         {
            'priority':9408,
            'type': 4,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 1,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'round':{'far':1000},
            'cond':[['srcArmy',1]],
            'times': -1,    #每回合最多使用次数
            'binding':{
               'res':100,	    #减免本次伤害
            },
            'actId':'skill223_1',
            'info':['skill223',1],	          #应该引用文字配置
         },
      ],
   },
   'skill224':{   #鬼影
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[1].binding.dmg'],
      'nextArr':['%|act[0].dmg'],
      'highArr':['%|high.special[0].change.skill.skill224h.act[0].binding.crit'],
      'index':406,
      'type':3,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['str','cha'],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':400,'powerRate':30},
         },
         {
            'cond':['skill.skill224', '*', 1],
            'rslt':{'powerRate':15},
         },
      ],
      'up':{ 'act[0].dmg':50, 'act[0].dmgReal':10},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].dmg' : -10  },
         '27':{  'act[0].dmg' : -20  },
         '28':{  'act[0].dmg' : -30  },
         '29':{  'act[0].dmg' : -40  },
         '30':{  'act[0].dmg' : -50  },
      },
      'high':{
         'lv':13,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':10},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill224h':{
                     'act':[{
                        'priority':9022,
                        'type': 2,
                        'src': 1,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'cond':[['comp','cha']],
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能，不受傲气
                        'binding':{
                           'crit':300,	    #暴击率
                           'dmgReal':10
                        },
                        'follow':'skill224',            #必须跟随特定行动触发
                        'info':['鬼影：魅力胜出',0],	          #应该引用文字配置
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':83,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 1,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, 1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'1':1000},
            'times':1,
            'dmg':1750,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':60,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgLimit':280,
            'atk1': 1050,    #攻击力源头(后军)
            'isRemote':1,
            'eff':'eff224',
            'info':['skill224',1],	          #应该引用文字配置
         },
         {
            'priority':9023,
            'type': 2,
            'src': 1,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'cond':[['comp','str']],
            'times': -1,    #每回合最多使用次数
            'nonSkill':1,    #非技能，不受傲气
            'binding':{'dmg':250,   'dmgReal':20   },
            'follow':'skill224',            #必须跟随特定行动触发
            'info':['鬼影：武力胜出',0],	          #应该引用文字配置
         },
      ],
   },
   'skill292':{   #神光
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[0].buff.buffTrance.prop.atkRate*2','-|act[0].buff.buffTrance.round'],
      'nextArr':['%|act[0].dmg','%|act[0].buff.buffTrance.prop.atkRate*2','-|act[0].buff.buffTrance.round'],
      'highArr':[
         '%|high.special[0].change.skill.skill292h.act[0].round.all',
      ],
      'merge':1,   #几次合服后可见
      'index':407,
      'type':3,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['agi'],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':600,'powerRate':35},
         },
         {
            'cond':['skill.skill292', '*', 1],
            'rslt':{'powerRate':17},
         },
      ],
      'up':{'act[0].dmg':110, 'act[0].dmgReal':30  , 'act[0].buff.buffTrance.prop.atkRate':-1, 'act[0].buff.buffTrance.prop.defRate':-1},
      'high':{
         'lv':13,
         'passive':[
            {
               'rslt':{'power':600,'powerRate':15},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill292h':{
                     'act':[{
                        'priority':9053,
                        'type': 2,
                        'src': 1,	           #发出源 0前军，1后军，【2英雄】，-1全军
                        'round':{'all':300},
                        'cond':[['comp','agi']],
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能，不受傲气
                        'binding':{'tgt':[1, -1]},
                        'follow':'skill292',            #必须跟随特定行动触发
                        'info':['神光：智力胜出',0],	          #应该引用文字配置
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':53,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 1,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, 0],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'3':1000},
            'times':1,
            #'dmgScale':50,
            'dmg':2000,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':400,   
            'atk1': 1020,   
            'dmgLimit':300,
            'buff':{
                'buffTrance':{'round':2, 'prop':{'atkRate':-10,'defRate':-10 }},
            },
            'isRemote':1,
            'eff':'eff292',
            'info':['skill292',1],	          #应该引用文字配置
         },
      ],
   },
   'skill296':{   #空明血瞳  三合
      'infoArr':['%|act[0].binding.dmg','%|act[1].binding.dmgRealRate'],
      'nextArr':['%|act[0].binding.dmg','%|act[1].binding.dmgRealRate'],
      'highArr':['*|high.special[0].change.skill.skill296h.act[0].binding.dmg'],
      'merge':3,   #几次合服后可见
      'index':408,
      'type':3,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['lead','str'],
      'passive':[
         {
            'rslt':{'power':600,'powerRate':100},
         },
         {
            'cond':['skill.skill296', '*', 1],
            'rslt':{'powerRate':17},
         },
      ],
      'up':{  'act[0].binding.dmg':80, 'act[0].binding.dmgReal':16,  'act[1].binding.dmgRealRate':2},
      'high':{
         'lv':15,
         'passive':[
            {
               'rslt':{'power':600,'powerRate':50},
            },
         ],
         'special':[{      #近战回合，我方方士对前军伤害{0}
            'change':{
               'skill':{
                  'skill296h':{
                     'act':[{
                        'priority':2962,
                        'type': 16,
                        'src': 1,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'round':{'near':1000},
                        'condTgt':[
                             ['tgtTeam',1],  [['armyType',0],['armyType',1]]
                        ],
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能，不受傲气
                        'binding':{
                           'ignDef':100,
                           'dmgScale':250,
                           'dmg':500,	
                           'dmgReal':400,
                        },
                        'follow':{'keys':{'dmg':1000,'dmgReal':1000}},
                        'info':['血瞳克前',0],	          #应该引用文字配置
                     }],
                  },
               },
            },
         }]
      },
      'act':[    #摒弃杂念，掌控生死。远战每个回合，我方方士攻击首个目标时，额外追加{0}的伤害。若统帅或武力高于对手，还可额外消灭该目标{1}的当前兵力
         {
            'priority':2961,
            'type': 16,
            'src': 1,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'free':1,    #有此值时，每回合检查cond等失败不会消耗检查次数（用于条件可变性较强的）
            'round':{'far':1000},
            'binding':{
                 'dmgReal':180,
                 'dmg':1200,
            },
            'unFollow':{'skill276':1,'effFaction':1},
            'follow':{'keys':{'dmg':1000,'dmgReal':1000}},
            'times': 1,  
            'eff':'eff296',
            'info':['skill296',1],	          #应该引用文字配置
         },
         {
            'priority':2960,
            'type': 16,	            #触发类型，攻前
            'src': 1,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'cond':[[['comp','agi'],['comp','cha']]],
            'follow':{'attach':'skill296'},     #必须跟随附加技
            'times': -1,  
            'binding':{
                'dmgRealRate':12,
            },
            'info':['血瞳咒杀',0],	          #应该引用文字配置
         },
      ],
   },

   'skill965':{   #星陨
      'infoArr':[
         '%|act[0].dmg',
         '%|act[0].buff.buffFlee.rnd',
      ],
      'nextArr':['%|act[0].dmg','%|act[0].buff.buffFlee.rnd'],
      'highArr':['%|high.special[0].change.prop.$armys[1].deBuffRate'],
      'merge':5,
      'index':409,
      'type':3,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':[],
      'passive':[
         {
            'rslt':{'power':600,'powerRate':100},
         },
         {
            'cond':['skill.skill965', '*', 1],
            'rslt':{'powerRate':17},
         },
      ],
      'lvPatch':{   #满足特定等级的补丁
         '30':{  'act[0].buff.buffFlee.rnd':-3  },
      },
      'up':{'act[0].dmg':80, 'act[0].dmgReal':50, 'act[0].buff.buffFlee.rnd':7},
      'high':{
         'lv':17,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':60},
            },
         ],
         'special':[{
            'change':{
               'prop':{
                  'armys[1].deBuffRate':-200,  
               },
            },
         }],
      },
      'act':[   #对敌方造成3次伤害后，方士立即召唤流星砸向目标，对敌方前军或后军造成440%伤害，并有50%几率导致敌方【溃逃】，持续2回合
         {
            'priority':889651,
            'type': 17,
            'srcArmy': 3,
            'srcCanAction': 1,
            'tgt':[1, -2],
            'allTimes': 1,
            'dmg':4400,
            'dmgReal':550,
            'atk1': 1050,
            'dmgLimit':400,	  #限制对单目标的杀伤千分点，溢出衰减
            'energyKeySrc': 'beHitSkill965_1',
            'costKey':'beHitSkill965_1',
            'cost': 3,
            'buff':{'buffFlee':{'rnd':500, 'round':2}},
            'eff':'eff965',
            'info':['skill965',1],	          #应该引用文字配置
         },
         {
            'priority': 889650,	 
            'type': 2,             #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'srcArmy': 3,          #发出源 
            'nonSkill':1,
            'follow':{'keys':{'dmg':1000,'dmgReal':1000}},  
            'times':-1,  
            'binding':{
               'energys':{
                  'ESkill965_1':{     #充能包
                     'priority':889651,
                     'condE':['dmg','>',0],
                     'srcE':{
                        'beHitSkill965_1':{'num':1,'checkAct':1},
                     },
                  }
               }
            },
            'info':['攻前充能',0],
         },
      ],
   },

   'skill969':{   #石肤
      'infoArr':[
         '%|0-act[0].binding.res',
         '%|0-act[1].binding.res',
      ],
      'nextArr':['%|0-act[0].binding.res','%|0-act[1].binding.res'],
      'highArr':['*%|high.special[0].change.skill.$skill221.act[0].dmgScale'],
      'merge':5,
      'index':410,
      'type':3,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':[],
      'passive':[
         {
            'rslt':{'power':500,'powerRate':40},
         },
         {
            'cond':['skill.skill969', '*', 1],
            'rslt':{'powerRate':15},
         },
      ],
      'up':{'act[0].binding.res':6, 'act[1].binding.res':3},
      'high':{
         'lv':17,
         'passive':[
            {
               'rslt':{'power':500,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill221.act[0].dmgScale':100,
                  'skill222.act[0].dmgScale':100,
                  'skill224.act[0].dmgScale':100,
                  'skill292.act[0].dmgScale':100,
                  'skill966.act[0].dmgScale':100,
               },
            },
         }],
      },
      'act':[   #方士受到兵种技伤害-10%，受到副将技伤害-5%
         {
            'priority':-9690000,
            'type': 4,         #触发类型，防前
            'src': 1,
            'times': -1,
            'allTimes': -1,
            #'cond':[['srcArmy',2],['srcTeam',1]],
            'binding':{'res':100},
            'unFollow':{'keys':{'allowSelf':1000}},
            'follow':{
               '_type_0_A':1,'_type_1_A':1,'_type_2_A':1,'_type_3_A':1,
            },
            'info':['skill969',1],
         },
         {
            'priority':-9690001,
            'type': 4,             #触发类型，防前
            'src': 1,
            'times': -1,
            'allTimes': -1,
            'cond':[['srcTeam',1]],
            'binding':{'res':50},
            'follow':{'element':'Adjutant'},
            'info':['skill969',1],
         },
      ],
   },


   'skill980':{   #陷阱
      'infoArr':['%0|act[0].dmg','%0|act[0].dmg+act[0].dmgRnd'],
      'nextArr':['%0|act[0].dmg','%0|act[0].dmg+act[0].dmgRnd'],
      'highArr':[],
      'unlockArr':[],
      'merge':7,   #几次合服后可见
      'index':411,
      'type':3,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['agi','lead'],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':600,'powerRate':50},
         },
         {
            'cond':['skill.skill980', '*', 1],
            'rslt':{'powerRate':17},
         },
      ],
      'up':{
         'act[0].dmg':17.24,
         'act[0].dmgRnd':103.44,
         'act[0].dmgReal':3,
         'act[0].dmgRealRnd':18,
         'special[0].change.skill.skill980_1.act[0].binding.dmg':103.44,
         'special[0].change.skill.skill980_1.act[0].binding.dmgRnd':-103.44,
         'special[0].change.skill.skill980_1.act[0].binding.dmgReal':18,
         'special[0].change.skill.skill980_1.act[0].binding.dmgRealRnd':-18,
      },
      'unlock':[
        {
         'lv':10,
         'passive':[
            {
               'cond':['army[1].type', '=', 3],
               'rslt':{'power':150,'powerRate':20,'agi':1,'lead':1},
            },
         ],
         'special':[{
           'change':{
              'skill':{
                 'skill980.act[0].dmgScale':250,
              },
           },
         }],
        },
        {
         'lv':17,
         'passive':[
            {
               'rslt':{'power':350,'powerRate':50},
            },
         ],
         'special':[{  #若智力或统御高于对手，【陷阱】有80%的几率导致目标【封技】1回合，期间无法发动兵种主动技
            'change':{
               'skill':{
                  'skill980h':{
                     'act':[{
                        'priority': 888.9801,	
                        'type': 2,
                        'src': 1,
                        'times': -1,
                        'cond':[['rnd',800]],
                        'nonSkill':1,
                        'binding':{'buff.buffBanArmyA2':{'round':1}},
                        'follow':'skill980', 
                        'info':['陷阱封技',0],
                     }],
                  },
               },
            },
         }]
        },
      ],
      'act':[
         {  #远战首回合发动，忽略敌方被动技能，对敌方前军造成50%（100%）~350%（700%）伤害。若敌方前军为骑兵，则必定造成最大伤害
            'priority':88980,
            'type': 1,
            'src': 1,
            'tgt':[1, 0],
            'round':{'1':1000},
            'dmg':500,
            'dmgRnd':3000,
            'dmgReal':50,
            'dmgRealRnd':300,
            'dmgLimit':250,
            'atk1':1050,
            'enforceCrit':100,
            'allowSelf':1,
            'noBfr':-1,
            'noAft':-1,
            'times': 1,
            'isRemote':1,
            'eff':'eff980',
            'info':['skill980',1],
         },
      ],
      'special':[{
           'priority':-888980,
           'cond':['enemy','army',1],
           'change':{
              'skill':{
                 'skill980_1':{
                    'act':[{
                       'priority':888.9800,
                       'type': 2,
                       'src': 1,
                       'times': -1,
                       'nonSkill':1,
                       'binding':{
                          'dmg':3000,
                          'dmgRnd':-3000,
                          'dmgReal':300,
                          'dmgRealRnd':-300,
                       },
                       'follow':'skill980',
                       'info':['敌前骑兵',0],
                    }],
                 },
              },
           },
      }],
   },



#英雄技

   'skill225':{   #万箭
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[0].dmg+act[0].dmgRnd','%|act[1].binding.dmg'],
      'nextArr':['%|act[0].dmg','%|act[0].dmg+act[0].dmgRnd'],
      'highArr':[],
      'index':25,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['str'],
      'limit':{'str':85,'type':0},
      'passive':[
         {
            'rslt':{'power':400,'powerRate':30},
         },
         {
            'cond':['skill.skill225', '*', 1],
            'rslt':{'power':50,'powerRate':15},
         },
      ],
      'up':{'act[0].dmg':5, 'act[0].dmgReal':2, 'act[0].dmgRnd':15, 'act[0].atk1':2},
      'high':{
         'lv':13,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':10},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill225.act[0].combo':1,
               },
            },
         }]
      },
      'act':[
         {
            'priority':80,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, -2],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'1':1000},
            'cond':[['army',1]],
            'times': 1,
            'dmg':160,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgRnd':240,	#浮动技能伤害
            'dmgReal':10,
            'dmgLimit':45,
            #'element':'Fire',
            'atk1': 1000,    #攻击力源头(后军)
            'combo':2,
            'buff':{'buffStun':{'rnd':0, 'round':1,}},
            'eff':'eff225',
            'info':['skill225',2],	          #应该引用文字配置
         },
         {
            'priority':9024,
            'type': 2,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'cond':[['comp','str']],
            'times': -1,    #每回合最多使用次数
            'nonSkill':1,    #非技能，不受傲气
            'binding':{'dmg':80,'dmgReal':2},
            'follow':'skill225',            #必须跟随特定行动触发
            'info':['万箭：武力胜出',0],	          #应该引用文字配置
         },
      ],
   },
   'skill226':{   #武圣
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[1].binding.dmg'],
      'nextArr':['%|act[0].dmg'],
      'highArr':['%|high.special[0].change.skill.skill226h.act[0].binding.ignDef'],
      'index':26,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['cha','str'],
      'limit':{'cha':90,'type':0},
      'passive':[
         {
            'rslt':{'power':400,'powerRate':30},
         },
         {
            'cond':['skill.skill226', '*', 1],
            'rslt':{'power':50,'powerRate':15},
         },
      ],
      'up':{'act[0].dmg':60, 'act[0].dmgReal':8, 'act[0].atk0':2},
      'high':{
         'lv':10,
         'passive':[
            {
               'rslt':{'power':300,'powerRate':10},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill226h':{
                     'act':[{
                        'priority':9025,
                        'type': 2,
                        'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'cond':[['comp','str']],
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能，不受傲气
                        'info':['武圣：武力胜出',0],	          #应该引用文字配置
                        'binding':{
                           'ignDef':500,
                           'dmgReal':5,
                        },
                        'follow':'skill226',            #必须跟随特定行动触发
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':60,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, 0],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'cond':[['army',0]],
            'round':{'2':1000},
            'dmg':1150,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':35,
            'dmgLimit':170,
            'atk0': 900,      #攻击力源头(前军)
            'atk1': 100,    #攻击力源头(后军)
            'eff':'eff226',
            'info':['skill226',2],	          #应该引用文字配置
         },
         {
            'priority':9026,
            'type': 2,
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'cond':[['comp','cha']],
            'times': -1,    #每回合最多使用次数
            'nonSkill':1,    #非技能，不受傲气
            'info':['武圣：魅力胜出',0],	          #应该引用文字配置
            'binding':{'dmg':250,  'dmgReal':10},
            'follow':'skill226',            #必须跟随特定行动触发
         },
      ],
   },
   'skill227':{   #无双
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[0].dmg+act[0].dmgRnd','%|act[1].binding.dmg'],
      'nextArr':['%|act[0].dmg','%|act[0].dmg+act[0].dmgRnd'],
      'highArr':[],
      'index':27,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['str'],
      'limit':{'str':90,'type':0},
      'passive':[
         {
            'rslt':{'power':400,'powerRate':30},
         },
         {
            'cond':['skill.skill227', '*', 1],
            'rslt':{'power':50,'powerRate':15},
         },
      ],
      'up':{'act[0].dmg':20, 'act[0].dmgReal':4, 'act[0].dmgRnd':20, 'act[0].atk0':2},
      'high':{
         'lv':13,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':10},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill227.act[0].combo':1,
               },
            },
         }]
      },
      'act':[
         {
            'priority':40,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, -2],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'cond':[['army',0]],
            'round':{'3':1000},
            'dmg':550,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgRnd':250,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':25,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgLimit':110,
            'atk0': 1050,      #攻击力源头(前军)
            'eff':'eff227',
            'combo':2,
            'info':['skill227',2],	          #应该引用文字配置
         },
         {
            'priority':9027,
            'type': 2,
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'cond':[['comp','str']],
            'times': -1,    #每回合最多使用次数
            'nonSkill':1,    #非技能，不受傲气
            'info':['无双：武力胜出',0],	          #应该引用文字配置
            'binding':{'dmg':150,'dmgReal':5},
            'follow':'skill227',            #必须跟随特定行动触发
         },
      ],
   },
   'skill228':{   #离间
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[1].binding.dmg'],
      'nextArr':['%|act[0].dmg'],
      'highArr':['%|high.special[0].change.skill.skill228h.act[0].binding.$buff.buffFaction.rnd','-|act[0].buff.buffFaction.round'],
      'index':28,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['lead','agi'],
      'limit':{'lead':90,'type':1},
      'passive':[
         {
            'rslt':{'power':400,'powerRate':30},
         },
         {
            'cond':['skill.skill228', '*', 1],
            'rslt':{'power':50,'powerRate':15},
         },
      ],
      'up':{'act[0].dmg':55, 'act[0].dmgReal':4, 'act[0].atk1':2},
      'high':{
         'lv':10,
         'passive':[
            {
               'rslt':{'power':300,'powerRate':10},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill228h':{
                     'act':[{
                        'priority':9028,
                        'type': 2,
                        'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'cond':[['comp','agi']],
                        'info':['离间：智力胜出',0],	          #应该引用文字配置
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能，不受傲气
                        'follow':'skill228',            #必须跟随特定行动触发
                        'binding':{'buff.buffFaction.rnd':300}, #内讧
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':62,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, -2],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'2':1000},
            'dmg':1000,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':20,
            'dmgLimit':150,
            'atk0': 500,      #攻击力源头(前军)
            'atk1': 500,    #攻击力源头(后军)
            'eff':'eff228',
            'buff':{'buffFaction':{'rnd':0, 'round':1}},

            'info':['skill228',2],	          #应该引用文字配置
         },
         {
            'priority':9029,
            'type': 2,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'cond':[['comp','lead']],
            'info':['离间：统帅胜出',0],	          #应该引用文字配置
            'times': -1,    #每回合最多使用次数
            'nonSkill':1,    #非技能，不受傲气
            'binding':{'dmg':250,'dmgReal':10},
            'follow':'skill228',            #必须跟随特定行动触发
         },
      ],
   },
   'skill229':{   #落雷
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[0].dmg+act[0].dmgRnd','%|act[1].binding.dmg'],
      'nextArr':['%|act[0].dmg','%|act[0].dmg+act[0].dmgRnd'],
      'highArr':[],
      'index':29,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['agi'],
      'limit':{'agi':90,'type':1},
      'passive':[
         {
            'rslt':{'power':400,'powerRate':30},
         },
         {
            'cond':['skill.skill229', '*', 1],
            'rslt':{'power':50,'powerRate':15},
         },
      ],
      'up':{'act[0].dmgReal':2, 'act[0].dmgRnd':50, 'act[0].atk1':2},
      'high':{
         'lv':10,
         'passive':[
            {
               'rslt':{'power':300,'powerRate':10},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill229.act[0].combo':1,
               },
            },
         }]
      },
      'act':[
         {
            'priority':41,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, -2],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'cond':[['army',0]],
            'round':{'3':1000},
            'dmg':100,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgRnd':1100,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':10,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgLimit':75,
            'atk0': 900,      #攻击力源头(前军)
            'atk1': 100,    #攻击力源头(后军)
            'buff':{
                'buffStun':{'rnd':0, 'round':1},
                'buffBreak':{'rnd':0, 'round':2},
            },
            'eff':'eff229',
            'combo':3,
            'info':['skill229',2],	          #应该引用文字配置
         },
         {
            'priority':9030,
            'type': 2,
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'cond':[['comp','agi']],
            'info':['落雷：智力胜出',0],	          #应该引用文字配置
            'times': -1,    #每回合最多使用次数
            'nonSkill':1,    #非技能，不受傲气
            'binding':{'dmg':150,'dmgReal':3},
            'follow':'skill229',            #必须跟随特定行动触发
         },
      ],
   },
   'skill230':{   #倾国
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[0].ignDef'],
      'nextArr':['%|act[0].dmg'],
      'highArr':[
         '%|high.special[0].change.skill.$skill230.act[0].buff.buffWeak.rnd',
         '-|act[0].buff.buffWeak.round',
         '%|0-200',
      ],
      'index':30,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['cha'],
      'limit':{'sex':0},
      'passive':[
         {
            'rslt':{'power':400,'powerRate':30},
         },
         {
            'cond':['skill.skill230', '*', 1],
            'rslt':{'power':50,'powerRate':15},
         },
      ],
      'special':[{
         'cond':[['enemy', 'sex', 0]],
         'change':{
            'skill':{
               'skill230.act[0].round.1':-10000,
               'skill230.act[0].round.2':-10000,
            },
         },
      }],
      'up':{'act[0].dmg':55, 'act[0].dmgReal':7, 'act[0].atk0':2},
      'high':{
         'lv':13,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':10},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill230.act[0].buff.buffWeak.rnd':300,
               },
            },
         }],
      },
      'act':[
         {
            'priority':63,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, -1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'cond':[['comp','cha']],
            'round':{'2':1000},
            'dmg':900,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':40,
            'dmgLimit':140,
            'atk0': 500,      #攻击力源头(前军)
            'atk1': 500,    #攻击力源头(后军)
            'ignDef':100,     #忽视防御
            'buff':{'buffWeak':{'rnd':0, 'round':2}},
            'eff':'eff230',
            'info':['skill230',2],	          #应该引用文字配置
         },
      ],
   },
   'skill231':{   #鼓舞
      'infoArr':['r|act[0]','%|act[0].buff.buffMorale.prop.defRate*2','-|act[0].buff.buffMorale.round','%|act[1].binding.$buff.buffMorale.prop.defRate*2'],
      'nextArr':['%|act[0].buff.buffMorale.prop.defRate*2'],
      'highArr':[
         '-|high.special[0].change.skill.$skill231.act[0].buff.buffMorale.prop.spd',
      ],
      'index':31,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['cha'],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':400,'powerRate':30},
         },
         {
            'cond':['skill.skill231', '*', 1],
            'rslt':{'power':50,'powerRate':15},
         },
      ],
      'up':{'act[0].buff.buffMorale.prop.atkRate':3, 'act[0].buff.buffMorale.prop.atk':5, 'act[0].buff.buffMorale.prop.defRate':5},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].buff.buffMorale.prop.atkRate':-1.5, 'act[0].buff.buffMorale.prop.defRate':-2.5  },
         '27':{  'act[0].buff.buffMorale.prop.atkRate':-3, 'act[0].buff.buffMorale.prop.defRate':-5  },
         '28':{  'act[0].buff.buffMorale.prop.atkRate':-4.5, 'act[0].buff.buffMorale.prop.defRate':-7.5  },
         '29':{  'act[0].buff.buffMorale.prop.atkRate':-6, 'act[0].buff.buffMorale.prop.defRate':-10  },
         '30':{  'act[0].buff.buffMorale.prop.atkRate':-7.5, 'act[0].buff.buffMorale.prop.defRate':-12.5  },
      },
      'high':{
         'lv':13,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':10},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill231.act[0].buff.buffMorale.prop.spd':5,
               },
            },
         }],
      },
      'act':[
         {
            'priority':83,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[0, -1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'cond':[['army',1]],
            'round':{'1':1000},
            'noBfr': 1,    #本次行动不能触发前置绑定效果
            'noAft': 1,    #本次行动不能触发后置绑定效果
            'buff':{'buffMorale':{'round':2,'prop':{'atkRate':20, 'atk':30, 'defRate':30}}},
            'eff':'eff231',
            'info':['skill231',2],	          #应该引用文字配置
         },
         {
            'priority':9031,
            'type': 2,
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'cond':[['comp','cha']],
            'info':['鼓舞：魅力胜出',0],	          #应该引用文字配置
            'times': -1,    #每回合最多使用次数
            'nonSkill':1,    #非技能，不受傲气
            'binding':{'buff.buffMorale.prop.atkRate':20, 'buff.buffMorale.prop.atk':30, 'buff.buffMorale.prop.defRate':30},
            'follow':'skill231',            #必须跟随特定行动触发
         },
      ],
   },
   'skill232':{   #奇策
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[1].binding.dmg'],
      'nextArr':['%|act[0].dmg'],
      'highArr':[
         '%|high.special[0].change.skill.skill232h.act[0].binding.$buff.buffSlow.rnd',
         '-|act[0].buff.buffSlow.round',
         '-|0-30',
      ],
      'index':32,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['agi','cha'],
      'limit':{'agi':85,'type':1},
      'passive':[
         {
            'rslt':{'power':400,'powerRate':30},
         },
         {
            'cond':['skill.skill232', '*', 1],
            'rslt':{'power':50,'powerRate':15},
         },
      ],
      'up':{'act[0].dmg':40, 'act[0].dmgReal':8, 'act[0].atk1':2},
      'high':{
         'lv':13,
         'passive':[
            {
               'rslt':{'power':400,'powerRate':10},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill232h':{
                     'act':[{
                        'priority':9032,
                        'type': 2,
                        'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'cond':[['comp','cha']],
                        'info':['奇策：魅力胜出',0],	          #应该引用文字配置
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能，不受傲气
                        'follow':'skill232',            #必须跟随特定行动触发
                        'binding':{
                           'buff.buffSlow.rnd':500,
                        },
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':82,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, -2],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'cond':[['army',1]],
            'round':{'1':1000},
            'dmg':790,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':18,
            'dmgLimit':100,
            'atk0': 100,      #攻击力源头(前军)
            'atk1': 900,    #攻击力源头(后军)
            'buff':{'buffSlow':{'rnd':0,'round':3}},
            'eff':'eff232',
            'info':['skill232',2],	          #应该引用文字配置
         },
         {
            'priority':9033,
            'type': 2,
            'src': 2,	           #发出源 0前军，1后军，【2英雄】，-1全军
            'cond':[['comp','agi']],
            'info':['奇策：智力胜出',0],	          #应该引用文字配置
            'times': -1,    #每回合最多使用次数
            'nonSkill':1,    #非技能，不受傲气
            'binding':{'dmg':350, 'dmgReal':15,},
            'follow':'skill232',            #必须跟随特定行动触发
         },
      ],
   },
   'skill233':{   #乾坤
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[2].buff.buffAlert.prop.resRate','%|act[1].binding.dmg'],
      'nextArr':['%|act[2].buff.buffAlert.prop.resRate'],
      'highArr':[
         '%|high.special[0].change.skill.skill233h.act[0].binding.$buff.buffWeak.rnd',
         '-|act[0].buff.buffWeak.round',
         '%|0-200',
      ],
      'index':33,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['agi','lead'],
      'limit':{'agi':90},
      'passive':[
         {
            'rslt':{'power':600,'powerRate':40},
         },
         {
            'cond':['skill.skill233', '*', 1],
            'rslt':{'power':80,'powerRate':20},
         },
      ],
      'up':{
         'act[2].buff.buffAlert.prop.resRate':20,
         'act[2].buff.buffAlert.prop.defRate':1,
         'act[0].dmgReal':3,
         'act[0].atk0':2,
      },
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[2].buff.buffAlert.prop.resRate':-15  },
         '27':{  'act[2].buff.buffAlert.prop.resRate':-30  },
         '28':{  'act[2].buff.buffAlert.prop.resRate':-45  },
         '29':{  'act[2].buff.buffAlert.prop.resRate':-60  },
         '30':{  'act[2].buff.buffAlert.prop.resRate':-75  },
      },

      'high':{
         'lv':13,
         'passive':[
            {
               'rslt':{'power':600,'powerRate':15},
            },
         ],
         'special':[{

            'change':{
               'skill':{
                  'skill233h':{
                     'act':[{
                        'priority':9062,
                        'type': 2,
                        'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'cond':[['comp','lead']],
                        'info':['乾坤：统帅胜出',0],	          #应该引用文字配置
                        'binding':{
                           'buff.buffWeak.rnd':400,
                        },
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能，不受傲气
                        'follow':'skill233',            #必须跟随特定行动触发
                     }],
                  },
               },
            },
         }],
      },
      'act':[
         {
            'priority':84,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, -2],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'cond':[['army',0]],
            'round':{'1':1000},
            'dmg':750,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':20,
            'dmgLimit':80,
            'atk0': 900,      #攻击力源头(前军)
            'atk1': 100,    #攻击力源头(后军)
            'buff':{'buffWeak':{'rnd':0, 'round':2}},
            'eff':'eff233',
            'info':['skill233',2],	          #应该引用文字配置
         },
         {
            'priority':9034,
            'type': 2,
            'src': 2,	           #发出源 0前军，1后军，【2英雄】，-1全军
            'cond':[['comp','agi']],
            'info':['乾坤：智力胜出',0],	          #应该引用文字配置
            'times': -1,    #每回合最多使用次数
            'nonSkill':1,    #非技能，不受傲气
            'binding':{'dmg':200,   'dmgReal':10,   },
            'follow':'skill233',            #必须跟随特定行动触发
         },
         {   #乾坤对我军减伤
            'priority':970,
            'type': 3,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[0, -1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'buff':{'buffAlert':{'round':2, 'prop':{'resRate':200,'defRate':20}}},
            'info':['乾坤戒备',0],	          #应该引用文字配置
            #'unFollow':{'attach':{'hero795_5':1}},
            'time':100,	          #强制指定战报时间
            'times':-1, 
            'atOnce': -100,
            'isHero':0,      #不认为是独立英雄技
            'nonSkill':1,    #非技能，不受傲气影响
            'noBfr':1,
            'noAft':1,
            'eff':'effNull',
            'follow':'skill233',            #必须跟随特定行动触发
         },
      ],
   },
   'skill234':{   #金刚
      'infoArr':['r|act[0]','%|act[0].buff.buffArmor.prop.resArmy2/1.5'],
      'nextArr':['%|act[0].buff.buffArmor.prop.resArmy2/1.5'],
      'highArr':[
         '%|high.special[0].change.skill.$skill234.act[0].buff.buffArmor.prop.resHero*0.4',
      ],
      'index':34,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['str'],
      'limit':{'str':90,},
      'passive':[
         {
            'rslt':{'power':600,'powerRate':40},
         },
         {
            'cond':['skill.skill234', '*', 1],
            'rslt':{'power':80,'powerRate':20},
         },
      ],
      'up':{
         'act[0].buff.buffArmor.prop.resArmy2':30,
         'act[0].buff.buffArmor.prop.resArmy3':30,
      },
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].buff.buffArmor.prop.resArmy2':-15,   'act[0].buff.buffArmor.prop.resArmy3':-15,  },
         '27':{  'act[0].buff.buffArmor.prop.resArmy2':-30,   'act[0].buff.buffArmor.prop.resArmy3':-30,  },
         '28':{  'act[0].buff.buffArmor.prop.resArmy2':-45,   'act[0].buff.buffArmor.prop.resArmy3':-45,  },
         '29':{  'act[0].buff.buffArmor.prop.resArmy2':-60,   'act[0].buff.buffArmor.prop.resArmy3':-60,  },
         '30':{  'act[0].buff.buffArmor.prop.resArmy2':-75,   'act[0].buff.buffArmor.prop.resArmy3':-75,  },
      },
      'high':{
         'lv':13,
         'passive':[
            {
               'rslt':{'power':600,'powerRate':15},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill234.act[0].buff.buffArmor.prop.resHero':1250,
               },
            },
         }],
      },
      'act':[
         {
            'priority':10000,
            'type': 0,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】
            'tgt':[0, -1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'cond':[['army',0],], 
            'round':{'0':1000},
            'noBfr': 1,    #本次行动不能触发前置绑定效果
            'noAft': 1,    #本次行动不能触发后置绑定效果
            'buff':{'buffArmor':{'round':3, 'prop':{'resArmy2':450,'resArmy3':450,'resHero':0}}},
            'eff':'eff234',
            'info':['skill234',2],	          #应该引用文字配置
         },
      ],
   },
   'skill235':{   #悲歌
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[0].buff.buffSad.prop.atkRate*2'],
      'nextArr':['%|act[0].buff.buffSad.prop.atkRate*2'],
      'highArr':[
         '%|high.special[0].change.skill.$skill235.act[0].buff.buffSad.prop.defRate*2',
         '-|high.special[1].change.skill.$skill235.act[0].removeBuff',
      ],
      'index':35,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['cha'],
      'limit':{'cha':95,},
      'passive':[
         {
            'rslt':{'power':600,'powerRate':40},
         },
         {
            'cond':['skill.skill235', '*', 1],
            'rslt':{'power':80,'powerRate':20},
         },
      ],
      'up':{'act[0].buff.buffSad.prop.atkRate':-4, 'act[0].atk1':2},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].buff.buffSad.prop.atkRate':2  },
         '27':{  'act[0].buff.buffSad.prop.atkRate':4  },
         '28':{  'act[0].buff.buffSad.prop.atkRate':6  },
         '29':{  'act[0].buff.buffSad.prop.atkRate':8  },
         '30':{  'act[0].buff.buffSad.prop.atkRate':10  },
      },
      'high':{
         'lv':13,
         'passive':[
            {
               'rslt':{'power':600,'powerRate':15},
            },
         ],
         'special':[
           {
            'cond':[['compare','power','>']],
            'change':{
               'skill':{
                  'skill235.act[0].buff.buffSad.prop.defRate':-25,
               },
            },
           },
           {
            'cond':[['compare','power','<']],
            'change':{
               'skill':{
                  'skill235.act[0].removeBuff':1,
                  #'skill235.act[0].buff.buffSad.round':1,
               },
            },
           },
         ],
      },
      'act':[
         {
            'priority':79,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】
            'tgt':[1, -1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'cond':[['army',1]], 
            'round':{'1':1000},
            #'noBfr':1,
            #'noAft':1,
            'dmg':350,
            'dmgReal':20,
            'dmgLimit':40,
            'atk1': 1000,
            'buff':{'buffSad':{'round':1, 'prop':{'atkRate':-90,'defRate':0}}},
            'eff':'eff235',
            'info':['skill235',2],	          #应该引用文字配置
         },
         {
            'priority':9035,
            'type': 2,
            'src': 2,	           #发出源 0前军，1后军，【2英雄】，-1全军
            'cond':[['comp','cha']],
            'info':['悲歌：魅力胜出',0],	          #应该引用文字配置
            'times': -1,    #每回合最多使用次数
            'nonSkill':1,    #非技能，不受傲气
            'binding':{'dmg':350,  'dmgReal':20},
            'follow':'skill235',            #必须跟随特定行动触发
         },
      ],
   },
   'skill236':{   #乱舞
      'infoArr':['r|act[0]','%|act[0].dmg'],
      'nextArr':['%|act[0].dmg'],
      'highArr':[
         '%|high.special[0].change.skill.$skill236.act[0].buff.buffStun.rnd',
         '-|act[0].buff.buffStun.round',
      ],
      'index':36,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['str'],
      'limit':{'str':95,'type':0},
      'passive':[
         {
            'rslt':{'power':600,'powerRate':40},
         },
         {
            'cond':['skill.skill236', '*', 1],
            'rslt':{'power':80,'powerRate':20},
         },
      ],
      'up':{'act[0].dmg':35, 'act[0].dmgReal':3, 'act[0].atk0':2},
      'high':{
         'lv':15,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':15},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill236.act[0].buff.buffStun.rnd':300,
               },
            },
         }],
      },
      'act':[
         {
            'priority':85,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, 0],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'cond':[['army',0]],
            'round':{'1':1000},
            'times':1,
            'combo':1,
            'dmg':700,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':30,
            'dmgLimit':90,
            'atk0': 1100,      #攻击力源头(前军)
            'buff':{'buffStun':{'rnd':0, 'round':1}},
            'eff':'eff236',
            'info':['skill236',2],	          #应该引用文字配置
         },
         {
            'priority':9036,
            'type': 2,
            'src': 2,	           #发出源 0前军，1后军，【2英雄】，-1全军
            'cond':[['comp','str']],
            'info':['乱舞：武力胜出',0],	          #应该引用文字配置
            'times': -1,    #每回合最多使用次数
            'nonSkill':1,    #非技能，不受傲气
            'binding':{'tgt':[1, -1]},
            'follow':'skill236',            #必须跟随特定行动触发
         },
      ],
   },
   'skill237':{   #业火
      'infoArr':['r|act[0]','%|act[0].dmg'],
      'nextArr':['%|act[0].dmg'],
      'highArr':[
         '%|high.special[0].change.skill.skill237h.act[0].binding.$buff.buffFire.rnd',
         '-|act[0].buff.buffFire.round',
         '%|100'
      ],
      'index':37,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['lead','cha'],
      'limit':{'lead':95},
      'passive':[
         {
            'rslt':{'power':600,'powerRate':40},
         },
         {
            'cond':['skill.skill237', '*', 1],
            'rslt':{'power':80,'powerRate':20},
         },
      ],
      'up':{'act[0].dmg':55, 'act[0].dmgReal':6, 'act[0].atk1':2},
      'high':{
         'lv':13,
         'passive':[
            {
               'rslt':{'power':600,'powerRate':15},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill237h':{
                     'act':[{
                        'priority':9037,
                        'type': 2,
                        'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'cond':[['comp','lead']],
                        'info':['业火：统帅胜出',0],	          #应该引用文字配置
                        'binding':{
                           'buff.buffFire.rnd':350,                          #原值350
                        },
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能，不受傲气
                        'follow':'skill237',            #必须跟随特定行动触发
                     }],
                  },
               },
            },
         }]
      },

      'act':[
         {
            'priority':64,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, 1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'cond':[['army',1]],
            'round':{'2':1000},
            'dmg':850,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':30,
            'dmgLimit':130,
            #'element':'Fire',
            'combo':1,
            'atk0': 100,      #攻击力源头(前军)
            'atk1': 900,    #攻击力源头(后军)
            'eff':'eff237',
            'buff':{'buffFire':{'rnd':0, 'round':2}},
            'info':['skill237',2],	          #应该引用文字配置
         },
         {
            'priority':9038,
            'type': 2,
            'src': 2,	           #发出源 0前军，1后军，【2英雄】，-1全军
            'cond':[['comp','cha']],
            'info':['业火：魅力胜出',0],	          #应该引用文字配置
            'times': -1,    #每回合最多使用次数
            'nonSkill':1,    #非技能，不受傲气
            'follow':'skill237',            #必须跟随特定行动触发
            'binding':{ 'tgt':[1, -1],  },
         },
      ],
   },
   'skill238':{   #天命
      'infoArr':['r|act[0]','%|act[0].dmg'],
      'nextArr':['%|act[0].dmg'],
      'highArr':[
         '%|high.special[0].change.skill.$skill238.act[0].buff.buffStun.rnd',
         '-|act[0].buff.buffStun.round',
      ],
      'index':38,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['agi'],
      'limit':{'agi':95},
      'passive':[
         {
            'rslt':{'power':600,'powerRate':40},
         },
         {
            'cond':['skill.skill238', '*', 1],
            'rslt':{'power':80,'powerRate':20},
         },
      ],
      'up':{'act[0].dmg':35, 'act[0].dmgReal':3, 'act[0].atk1':2},
      'high':{
         'lv':17,
         'passive':[
            {
               'rslt':{'power':1000,'powerRate':15},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill238.act[0].buff.buffStun.rnd':300,
               },
            },
         }],
      },
      'act':[
         {
            'priority':86,
            'type': 1,	        #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	        #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, -2],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'cond':[['army',1]],
            'round':{'1':1000},
            'dmg':720,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':25,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgLimit':90,
            'atk1': 990,    #攻击力源头(后军)
            'buff':{
                'buffStun':{'rnd':0, 'round':1},
            },
            'eff':'eff238',
            'info':['skill238',2],	          #应该引用文字配置
         },
         {
            'priority':9039,
            'type': 2,
            'src': 2,	           #发出源 0前军，1后军，【2英雄】，-1全军
            'cond':[['comp','agi']],
            'info':['天命：智力胜出',0],	          #应该引用文字配置
            'times': -1,    #每回合最多使用次数
            'nonSkill':1,    #非技能，不受傲气
            'follow':'skill238',            #必须跟随特定行动触发
            'binding':{ 'tgt':[1, -1],  },
         },
      ],
   },
   'skill240':{   #福佑
      'infoArr':['r|act[0]','%|act[0].round.0','%|act[0].buff.buffShield.shield.value*5'],
      'nextArr':['%|act[0].round.0','%|act[0].buff.buffShield.shield.value*5'],
      'highArr':[
         '%|high.special[0].change.skill.$skill240.act[0].buff.buffShield.prop.resRealRate*0.5',
      ],
      'state':7,   #开服几天后显示  
      'index':40,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['cha'],
      'limit':{'cha':90,'type':1},
      'passive':[
         {
            'rslt':{'power':600,'powerRate':40},
         },
         {
            'cond':['skill.skill240', '*', 1],
            'rslt':{'power':80,'powerRate':20},
         },
      ],
      'up':{
         'act[0].round.0':5,
         'act[0].buff.buffShield.shield.value':10,
         'act[0].buff.buffShield.shield.hpmRate':1,
      },
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].round.0':-3,     'act[0].buff.buffShield.shield.value':-5  },
         '27':{  'act[0].round.0':-6,     'act[0].buff.buffShield.shield.value':-10  },
         '28':{  'act[0].round.0':-9,     'act[0].buff.buffShield.shield.value':-15  },
         '29':{  'act[0].round.0':-12,     'act[0].buff.buffShield.shield.value':-20  },
         '30':{  'act[0].round.0':-15,     'act[0].buff.buffShield.shield.value':-25  },
      },

      'high':{
         'lv':17,
         'passive':[
            {
               'rslt':{'power':1000,'powerRate':15},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill240.act[0].buff.buffShield.prop.resRealRate':1000,
               },
            },
         }],
      },
      'act':[
         {
            'priority':100001,
            'type': 0,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】
            'tgt':[0, -1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'0':400},
            'noBfr': 1,    #本次行动不能触发前置绑定效果
            'noAft': 1,    #本次行动不能触发后置绑定效果
            'buff':{'buffShield':{'shield':{'value':90,'hpmRate':16,'bearPoint':1000},'prop':{'resRealRate':0}}},
            'eff':'eff240',
            'info':['skill240',2],	          #应该引用文字配置
         },
      ],
   },
   'skill243':{   #巨象
      'infoArr':['r|act[0]','%|act[0].dmg','%|act[1].dmgRealRate','%|act[0].dmgRealMax'],
      'nextArr':['%|act[0].dmg','%|act[0].dmgRealMax'],
      'highArr':[
         '%|high.special[0].change.skill.skill243h.act[0].binding.$buff.buffStun.rnd',
         '-|act[0].buff.buffStun.round',
      ],
      'index':41,
      'state':7,   #开服几天后显示   
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      #'icon':'item084',
      'ability_info':['lead','str'],
      'limit':{'str':95,'lead':90},
      'passive':[
         {
            'rslt':{'power':600,'powerRate':40},
         },
         {
            'cond':['skill.skill243', '*', 1],
            'rslt':{'power':80,'powerRate':20},
         },
      ],
      'up':{'act[0].dmg':50, 'act[0].dmgReal': 8, 'act[0].dmgRealMax': 2, 'act[0].atk0':2},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].dmgRealMax':-1  },
         '27':{  'act[0].dmgRealMax':-2  },
         '28':{  'act[0].dmgRealMax':-3  },
         '29':{  'act[0].dmgRealMax':-4  },
         '30':{  'act[0].dmgRealMax':-5  },
      },

      'high':{
         'lv':17,
         'passive':[
            {
               'rslt':{'power':1000,'powerRate':15},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill243h':{
                     'act':[{
                        'priority':9041,
                        'type': 2,
                        'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'cond':[['comp','str']],
                        'info':['恐象',0],	          #应该引用文字配置
                        'binding':{
                           'buff.buffStun.rnd':400,
                        },
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能，不受傲气
                        'follow':'skill243',            #必须跟随特定行动触发
                     }],
                  },
               },
            },
         }],
      },
      'act':[
         {
            'priority':66,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, -1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'cond':[['army',0]],
            'round':{'2':1000},
            #'combo':1,
            'dmg':900,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':200,
            'dmgRealMax':10,
            'dmgLimit':130,
            #'ignDef':150,
            'atk0': 900,   
            'atk1': 100,   
            'times':1,
            'buff':{'buffStun':{'rnd':0, 'round':1}},
            'eff':'eff243',
            'info':['skill243',2],	          #应该引用文字配置
         },
         {
            'priority':971,  
            'type': 3,
            'src': 2,	           #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[0, 0],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'cond':[['army',0],['comp','lead','<']],
            'dmgRealRate':100,
            'times':-1,  
            'nonSkill':1,    #非技能，不受傲气影响
            'noBfr':1,
            'noAft':1,
            'isHero':0,
            'follow':'skill243',            #必须跟随特定行动触发
            'eff':'effElephant',
            'info':['巨象：统帅失败',0],	          #应该引用文字配置
         },
      ],
   },
   'skill239':{   #攻心
      'infoArr':[
         'r|act[0]',
         '-|act[0].combo',
         '-|act[0].combo+act[0].comboRnd',
         '%|act[0].dmg',
         '%|30'
      ],
      'nextArr':['-|act[0].combo','-|act[0].combo+act[0].comboRnd','%|act[0].dmg'],
      'highArr':[
         '-|act[0].buff.buffFlee.round',
         '%|50',
      ],
      'index':42,
      'merge':2,   #几次合服后可见
      'state':1,   #开服几天后显示 
      'type':4,  
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['lead','sum'],
      'limit':{'type':1},
      'passive':[
         {
            'rslt':{'power':600,'powerRate':40},
         },
         {
            'cond':['skill.skill239', '*', 1],
            'rslt':{'power':80,'powerRate':20},
         },
      ],
      'up':{'act[0].dmg':20, 'act[0].dmgReal':1 , 'act[0].comboRnd':0.111, 'act[0].atk1':1},
      'high':{
         'lv':17,
         'passive':[
            {
               'rslt':{'power':1000,'powerRate':15},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill239h':{
                     'act':[{
                        'priority':9055,
                        'type': 2,
                        'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'cond':[['comp','sum']],
                        'info':['攻心：四维胜出',0],	          #应该引用文字配置
                        'binding':{
                           'buff.buffFlee.rnd':1000
                        },
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能，不受傲气
                        'follow':'skill239',            #必须跟随特定行动触发
                     }],
                  },
               },
            },
         }],
      },
      'act':[
         {
            'priority':122,
            'type': 1,	        #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	        #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, 1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            #'cond':[['army',1]],
            'round':{'0':1000},
            'dmg':500,	
            'dmgReal':40,	
            'dmgLimit':50,
            'atk0': 300, 
            'atk1': 700, 
            'combo':1,
            'comboRnd':0.4,
            'buff':{
                'buffFaction':{'rnd':0, 'round':1},
                'buffFlee':{'rnd':0, 'round':3}
            },
            'eff':'eff239',
            'info':['skill239',2],	          #应该引用文字配置
         },
         {
            'priority':9058,
            'type': 2,
            'src': 2,	           #发出源 0前军，1后军，【2英雄】，-1全军
            'cond':[['comp','lead']],
            'info':['buff239',0],	          #应该引用文字配置
            'times': -1,    #每回合最多使用次数
            'nonSkill':1,    #非技能，不受傲气
            'follow':'skill239',            #必须跟随特定行动触发
            'binding':{ 'buff.buff239':{},  },
         },
      ],
   },
   'skill245':{   #冲阵
      'infoArr':[
         'r|act[0]',
         '%|act[0].dmg',
         '%|act[1].round.any',
      ],
      'nextArr':['%|act[0].dmg','%|act[1].round.any'],
      'highArr':['%|100'],
      'index':43,
      'merge':2,   #几次合服后可见
      'state':1,   #开服几天后显示   
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['str'],
      'limit':{'type':0},
      'passive':[
         {
            'rslt':{'power':600,'powerRate':40},
         },
         {
            'cond':['skill.skill245', '*', 1],
            'rslt':{'power':80,'powerRate':20},
         },
      ],
      'up':{'act[0].dmg':40, 'act[0].dmgReal':4, 'act[1].round.any':20, 'act[0].atk0':1},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[1].round.any':-10  },
         '27':{  'act[1].round.any':-20  },
         '28':{  'act[1].round.any':-30  },
         '29':{  'act[1].round.any':-40  },
         '30':{  'act[1].round.any':-50  },
      },
      'high':{
         'lv':17,
         'passive':[
            {
               'rslt':{'power':1000,'powerRate':15},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill245h':{     #缴械效果
                     'act':[{
                        'priority':9059,
                        'type': 2,
                        'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'allTimes': -1,
                        'times': -1,
                        'nonSkill':1,    #非技能，不受傲气

                        'info':['buff245',0],	          #应该引用文字配置
                        'binding':{
                           'buff.buff245.rnd':10000    
                        },

                        'follow':'skill245',            #必须跟随特定行动触发
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':121,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, -2],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'0':1000},
            'allTimes': -1,
            'times': 1,
            #'timesRnd': 2000,      #回合内额外发动1次的几率
            'dmg':680,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':70,
            'dmgLimit':90,
            'atk0': 750,      #攻击力源头(前军)
            'atk1': 300,      #攻击力源头(前军)
            'buff':{'buff245':{'rnd':0}, 'buffFlee':{'rnd':0, 'round':2}},
            'eff':'eff245',
            'info':['skill245',2],	          #应该引用文字配置
         },
         {
            'priority':9060,      #再次冲阵
            'type': 2,
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'cond':[['comp','str']],
            'round':{'any':300},
            'info':['冲阵：武力胜出',0],	          #应该引用文字配置
            'binding':{
                'refresh':1       #使用后刷新本次技能
            },
            'times': 1,    #每回合最多使用次数
            'nonSkill':1,    #非技能，不受傲气
            'follow':'skill245',            #必须跟随特定行动触发
         }
      ],
   },
   'skill246':{   #死斗
      'infoArr':[
         'r|act[0]',
         '-|act[0].buff.buff246.round',
         '*|act[0].buff.buff246.prop.resRate',
         '*|act[0].buff.buff246.prop.crit*0.8',
      ],
      'nextArr':['*|act[0].buff.buff246.prop.resRate',  '*|act[0].buff.buff246.prop.crit*0.8'],
      'highArr':['%|high.special[0].change.skill.skill246h.act[0].buff.buff246h.prop.resRate*0.8'],
      'index':44,
      'merge':2,   #几次合服后可见
      'state':1,   #开服几天后显示   
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['cha','lead'],
      'limit':{'cha':85,'lead':85},
      'passive':[
         {
            'rslt':{'power':600,'powerRate':40},
         },
         {
            'cond':['skill.skill246', '*', 1],
            'rslt':{'power':80,'powerRate':20},
         },
      ],
      'up':{'act[0].buff.buff246.prop.dmgRate':8, 'act[0].buff.buff246.prop.resRate':10, 'act[0].buff.buff246.prop.crit':10},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].buff.buff246.prop.dmgRate':-4, 'act[0].buff.buff246.prop.resRate':-5, 'act[0].buff.buff246.prop.crit':-5  },
         '27':{  'act[0].buff.buff246.prop.dmgRate':-8, 'act[0].buff.buff246.prop.resRate':-10, 'act[0].buff.buff246.prop.crit':-10  },
         '28':{  'act[0].buff.buff246.prop.dmgRate':-12, 'act[0].buff.buff246.prop.resRate':-15, 'act[0].buff.buff246.prop.crit':-15  },
         '29':{  'act[0].buff.buff246.prop.dmgRate':-16, 'act[0].buff.buff246.prop.resRate':-20, 'act[0].buff.buff246.prop.crit':-20  },
         '30':{  'act[0].buff.buff246.prop.dmgRate':-20, 'act[0].buff.buff246.prop.resRate':-25, 'act[0].buff.buff246.prop.crit':-25  },
      },
      'high':{
         'lv':17,
         'passive':[
            {
               'rslt':{'power':1000,'powerRate':15},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill246h':{   
                     'act':[{
                        'isHero':0,
                        'priority':9000020,
                        'type': 13,
                        'src': -1,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'tgt':[0, -1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
                        'round':{'3':1000},
                        'nonSkill':1,    #非技能，不受傲气
                        'noBfr': 1,    #本次行动不能触发前置绑定效果
                        'noAft': 1,    #本次行动不能触发后置绑定效果
                        'buff':{'buff246h':{'round':2,'prop':{'resRate':250, 'resRealPer':0.01}}},

                        'time': 0,	 
                        'eff':'effNull', 
                        'info':['死斗先御',0],	          #应该引用文字配置
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':100002,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[0, -1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'4':1000},
            'allTimes': 1,
            'noBfr': 1,    #本次行动不能触发前置绑定效果
            'noAft': 1,    #本次行动不能触发后置绑定效果
            'buff':{'buff246':{'round':3,'prop':{'dmgRealPer':0.01, 'resRealPer':0.01,'dmgRate':100,'resRate':150,'crit':200}}},
            'eff':'eff246',
            'info':['skill246',2],	          #应该引用文字配置
         },
      ],
   },



   'skill241':{   #枪王
      'infoArr':[
         'r|act[0]',
         '%|act[0].dmg',
         '%|act[0].buff.buffSlow.rnd',
         '-|act[0].buff.buffSlow.round',
         '-|0-30',
      ],
      'nextArr':['%|act[0].dmg','%|act[0].buff.buffSlow.rnd'],
      'highArr':['%|high.special[0].change.skill.$skill241.act[0].round.near'],
      'state':16,                                      #最早可见天数   
      #'open_date':datetime.datetime(2019,1,31,5,0),   #最早可见日期
      'index':50,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      #'icon':'item084',
      'ability_info':['str'],
      'limit':{'str':98,'type':0},
      'passive':[
         {
            'rslt':{'power':800,'powerRate':45},
         },
         {
            'cond':['skill.skill241', '*', 1],
            'rslt':{'power':100,'powerRate':22},
         },
      ],
      'up':{'act[0].dmg':35, 'act[0].dmgReal':8, 'act[0].buff.buffSlow.rnd':30, 'act[0].atk0':2},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].buff.buffSlow.rnd':-20  },
         '27':{  'act[0].buff.buffSlow.rnd':-40  },
         '28':{  'act[0].buff.buffSlow.rnd':-60  },
         '29':{  'act[0].buff.buffSlow.rnd':-80  },
         '30':{  'act[0].buff.buffSlow.rnd':-100  },
      },
      'high':{
         'lv':13,
         'passive':[
            {
               'rslt':{'power':1000,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill241.act[0].round.near':600,
               },
            },
         }]
      },
      'act':[
         {
            'priority':87,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, 1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'cond':[['army',0]],
            'round':{'1':1000},
            'allowSelf':1,
            'noBfr':-1,
            'noAft':-1,
            'dmg':810,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':75,
            'dmgLimit':120,
            'ignDef':20,
            'atk0': 1080,      #攻击力源头(前军)
            'combo':1,
            'buff':{'buffSlow':{'rnd':200,'round':3}},
            'eff':'eff241',
            'info':['skill241',2],	          #应该引用文字配置
         },
      ],
   },
   'skill242':{   #幻术
      'infoArr':[
         'r|act[0]',
         '%|act[0].dmg',
         '%|act[0].buff.buffUnreal.rnd',
         '-|act[0].buff.buffUnreal.round',
         '%|0-50',
         '%|500',
      ],
      'nextArr':['%|act[0].dmg','%|act[0].buff.buffUnreal.rnd'],
      'highArr':[
         '%|high.special[0].change.skill.skill242h.act[0].binding.dmgRealRate',
      ],
      'state':30,                                      #最早可见天数   
      #'open_date':datetime.datetime(2019,2,14,5,0),   #最早可见日期
      'index':51,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      #'icon':'item084',
      'ability_info':['agi'],
      'limit':{'agi':98,'type':1},
      'passive':[
         {
            'rslt':{'power':800,'powerRate':45},
         },
         {
            'cond':['skill.skill242', '*', 1],
            'rslt':{'power':100,'powerRate':22},
         },
      ],
      'up':{'act[0].dmg':35, 'act[0].dmgReal':3, 'act[0].buff.buffUnreal.rnd':5, 'act[0].atk1':2},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].buff.buffUnreal.rnd':-3  },
         '27':{  'act[0].buff.buffUnreal.rnd':-6  },
         '28':{  'act[0].buff.buffUnreal.rnd':-9  },
         '29':{  'act[0].buff.buffUnreal.rnd':-12  },
         '30':{  'act[0].buff.buffUnreal.rnd':-15  },
      },
      'high':{
         'lv':13,
         'passive':[
            {
               'rslt':{'power':1000,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                 'skill242h':{
                   'act':[{
                     'priority':9057,
                     'type': 2,   							#触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
                     'src':2,   							#发出源 0前军，1后军，2英雄，-1全军
                     'binding':{'dmgRealRate':70,   'dmgReal':5,},
                     'times': -1,    #每回合最多使用次数
                     'nonSkill':1,    #非技能，不受傲气
                     'follow':'skill242',            #必须跟随特定行动触发
                     'info':['幻术离魂',0],	          #应该引用文字配置
                   }],
                 },
               },
            },
         }],
      },
      'act':[
         {
            'priority':130,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, -1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'2':1000},
            'dmg':650,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':30,
            'dmgLimit':90,
            'atk0': 500,   
            'atk1': 500,   
            'buff':{'buffUnreal':{'rnd':200, 'round':3}},
            'eff':'eff242',
            'info':['skill242',2],	          #应该引用文字配置
         },
      ],
   },

   'skill244':{   #龙怒
      'infoArr':['%|act[0].dmg*2'],
      'nextArr':['%|act[0].dmg*2'],
      'highArr':[
         '%|high.special[0].change.skill.skill244h.act[0].binding.atk0',
         '%|high.special[0].change.skill.skill244h.act[1].binding.atk1',
      ],
      'merge':1,   #几次合服后可见
      'state':6,   #开服几天后显示   
      'index':52,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      'ability_info':['str','agi'],
      'limit':{'str':90,'agi':90},
      'passive':[
         {
            'rslt':{'power':800,'powerRate':45},
         },
         {
            'cond':['skill.skill244', '*', 1],
            'rslt':{'power':100,'powerRate':22},
         },
      ],
      'up':{'act[0].dmg':40, 'act[0].dmgReal':20, 'act[0].atk0':2},
      'high':{
         'lv':17,
         'passive':[
            {
               'rslt':{'power':1200,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill244h':{
                     'act':[
                      {
                        'priority':9042,
                        'type': 2,
                        'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'cond':[['comp','str']],
                        'info':['前攻',0],	          #应该引用文字配置
                        'binding':{
                           'dmg':100,
                           'dmgReal':60,
                           'atk0':300,
                        },
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能，不受傲气
                        'follow':'skill244',            #必须跟随特定行动触发
                      },
                      {
                        'priority':9043,
                        'type': 2,
                        'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'cond':[['comp','agi']],
                        'info':['后攻',0],	          #应该引用文字配置
                        'binding':{
                           'dmg':100,
                           'dmgReal':60,
                           'atk1':250,
                        },
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能，不受傲气
                        'follow':'skill244',            #必须跟随特定行动触发
                      },
                     ],
                  },
               },
            },
         }],
      },
      'act':[
         {
            'priority':120,
            'type': 1,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, 0],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'any':1000},
            'cond':[['hpPoint','<',500]],
            'allTimes': 1,
            'times': 1,
            'allowSelf':1,
            'noBfr':-1,
            'noAft':-1,
            'dmg':950,	#技能伤害系数。文武、性别、兵种类型默认
            'dmgReal':450,
            'dmgLimit':250,
            'ignDef':50,
            'atk0': 600,   
            'atk1': 550,   
            'buff':{
                'buffStun':{'rnd':0, 'round':3},
            },
            'eff':'eff244',
            'info':['skill244',2],	          #应该引用文字配置
         },
      ],
   },

   'skill247':{   #冰河
      'infoArr':[
         'r|act[0]',
         '%|act[0].dmg',
         '%|act[0].buff.buffFrozen.rnd',
         '-|act[0].buff.buffFrozen.round',
         '%|0-100',
         '%|0-500',
      ],
      'nextArr':['%|act[0].dmg','%|act[0].buff.buffFrozen.rnd'],
      'highArr':['%|high.special[0].change.skill.$skill247.act[0].round.4'],
      'merge':4,
      'state':1,                                      #最早可见天数   
      'open_date':datetime.datetime(2021,12,3,5,0),   #最早可见日期
      'index':53,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      #'icon':'item084',
      'ability_info':['agi','lead'],
      'limit':{'agi':95,'lead':95},
      'passive':[
         {
            'rslt':{'power':800,'powerRate':45},
         },
         {
            'cond':['skill.skill247', '*', 1],
            'rslt':{'power':150,'powerRate':22},
         },
         {
            'special':[{
               'change':{
                  'skill':{
                     'skill247h':{
                       'act':[{
                         'priority':2470,
                         'type': 1,
                         'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
                         'tgt':[0, 2],
                         'cond':[['comp','agi']],
                         'round':{'3':1000},
                         'info':['智力胜出',0],
                         'refreshSkill':{
                            'type':1,    #刷新行动的类型
                            'times':1,   #刷新行动的次数
                            'atOnce':1,  #不排队立即执行
                            'actId':'skill247'
                         },
                         'limitTimes': 1,
                         'nonSkill':1,
                         'noBfr':1,
                         'noAft':1,
                         'time': 0,
                         'eff':'effNull',
                       }],
                     },
                  },
               },
            }],
         },
      ],
      'up':{'act[0].dmg':30, 'act[0].dmgReal':5, 'act[0].buff.buffFrozen.rnd':8},
      'high':{
         'lv':17,
         'passive':[
            {
               'rslt':{'power':1200,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill247.act[0].round.4':1000,
               },
            },
         }],
      },
      'act':[
         {
            'priority':65,
            'type': 1,
            'src': 2,
            'tgt':[1, -1],
            'round':{'2':1000},
            'dmg':500,
            'dmgReal':80,
            'dmgLimit':70,
            'ignDef':50,
            'atk0': 500,
            'atk1': 500,
            'buff':{'buffFrozen':{'rnd':88, 'round':1}},    
            'eff':'eff247',
            'actId':'skill247',
            'info':['skill247',2],
         },
      ],
   },


   'skill248':{   #勾魂
      'infoArr':[
         'r|act[0]',
         '%|act[0].dmg*1.5',
         '%|act[1].summon*40',
      ],
      'nextArr':['%|act[0].dmg*1.5','%|act[1].summon*40'],
      'highArr':['%|600',],
      'merge':4,
      'state':1,                                      #最早可见天数   
      'open_date':datetime.datetime(2021,11,17,5,0),   #最早可见日期
      'index':54,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      #'icon':'item084',
      'ability_info':['cha','lead'],
      'limit':{'cha':98},
      'passive':[
         {
            'rslt':{'power':800,'powerRate':45},
         },
         {
            'cond':['skill.skill248', '*', 1],
            'rslt':{'power':150,'powerRate':22},
         },
      ],
      'lvPatch':{   #满足特定等级的补丁
         '28':{  'act[1].summon':0.1 },
         '29':{  'act[1].summon':0.2 },
         '30':{  'act[1].summon':0.22 },
      },
      'up':{'act[0].dmg':180/1.5, 'act[0].dmgReal':30, 'act[1].summon':0.82,'act[1].summonReal':8.2},       #伤害+15%，恢复兵力+0.1%
      'high':{
         'lv':17,
         'passive':[
            {
               'rslt':{'power':1200,'powerRate':20},
            },
         ],
         'special':[{
            'cond':[['compare','sex','!=']],
            'change':{
               'skill':{
                  'skill248.act[0].dmgScale':600,    #原值600
               },
            },
         }],
      },
      'act':[
         {
            'priority':42,
            'type': 1,
            'src': 2,
            'tgt':[1, -2],
            'round':{'3':1000},
            'dmg':3500/1.5,
            'dmgReal':500,
            'dmgLimit':300,
            'dmgScale':400,
            'ignDef':50,
            'atk0': 300,
            'atk1': 700,
            'eff':'eff248',        
            'info':['skill248',2],
         },
         {
            'priority':9301,
            'type': 3,
            'src': 2,
            'tgt':[0, -6],
            'cond':[[['comp','lead'],['comp','cha']]],
            'summonReal':160,         #治疗伤兵数量
            'summon':16,            #治疗伤兵千分点
            'eff':'eff272',         #临时
            'times':-1,    
            'nonSkill':1,    
            'noBfr':1,     
            'noAft':1,    
            'isHero':0,    
            'follow':'skill248',
            'info':['勾魂恢复',0],
         },
      ],
   },

   'skill960':{   #圣甲     #远战第二回合起始时发动，为前军或后军召唤相当于自身最大兵力12%(24%)的守护圣甲，每次受到伤害时，圣甲将吸收50%伤害直至碎裂。若武力或统帅高于对手，【圣甲】护盾分摊对全军生效，总护盾量+20%
      'infoArr':[
         'r|act[0]',
         '%|act[0].buff.buffShield4.shield.hpmRate',
      ],
      'nextArr':['%|act[0].buff.buffShield4.shield.hpmRate'],
      'highArr':['%|act[1].binding.buff.buff960h_0.prop.dmgRate'],
      'merge':5,
      'state':1,                                      #最早可见天数   
      'open_date':datetime.datetime(2021,11,17,5,0),   #最早可见日期
      'index':55,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      #'icon':'item084',
      'ability_info':['lead','str'],
      'limit':{'lead':100},
      'passive':[
         {
            'rslt':{'power':800,'powerRate':45},
         },
         {
            'cond':['skill.skill960', '*', 1],
            'rslt':{'power':150,'powerRate':22},
         },
      ],
      'lvPatch':{   #满足特定等级的补丁（面板显示值凑整）
         '30':{ 'act[0].buff.buffShield4.shield.hpmRate':4, 'act[1].binding.buffPatch.id.buffShield4.shield.hpmRate':-1.6 },
      },
      'up':{'act[0].buff.buffShield4.shield.hpmRate':4, 'act[1].binding.buffPatch.id.buffShield4.shield.hpmRate':-1.6, 'act[1].lv':1},
      'high':{
         'lv':17,
         'passive':[    #圣甲存在期间，每受到1次来自敌方的伤害，自身伤害和免伤各提升3%（最多叠加10次，前后军独立计算），该效果持续整场
            {
               'rslt':{'power':1200,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill960h':{
                     'act':[{
                        'priority': 9600,	
                        'type': 4,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
                        'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'cond':[['checkBuff', 0, 0, 'buffShield4', '>', 0]],
                        'times':-1,
                        'binding':{'buff.buff960h_0':{'prop':{'dmgRate':30,'resRate':30}}},
                        'nonSkill':1,
                        'info':['前：加BUFF',0],	          #应该引用文字配置
                     },
                     {
                        'priority': 9601,	
                        'type': 4,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
                        'src': 1,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'cond':[['checkBuff', 0, 1, 'buffShield4', '>', 0]],
                        'times':-1,
                        'binding':{'buff.buff960h_0':{'prop':{'dmgRate':30,'resRate':30}}},
                        'nonSkill':1,
                        'info':['后：加BUFF',0],	          #应该引用文字配置
                     }],
                  },
               },
            },
         }],
      },
      'act':[{   #远战第二回合起始时发动，为前军或后军召唤相当于自身最大兵力12%(24%)的守护圣甲，每次受到伤害时，圣甲将吸收50%伤害直至碎裂。若武力或统帅高于对手，【圣甲】护盾分摊对全军生效，总护盾量+20%
          'priority': 7000021,	 
          'type': 13,
          'src': 2,
          'tgt':[0, -2],
          'round':{'2':1000},
          'buff':{'buffShield4':{'shield':{'hpmRate':120,'bearPoint':500}}},
          'nonSkill':1,    #非技能，不受傲气影响
          'noBfr':1,
          'noAft':1,
          'eff':'eff960',
          'actId':'skill960',
          'info':['skill960',2],
      },
      {
          'priority': 100004,	 
          'type': 2,
          'src': 2,
          'follow':'skill960',
          'cond':[ 
            ['selfArmy',0],
            ['selfArmy',1],
            [['comp','str'],['comp','lead']],
          ],
          'binding':{ 
            'tgt':[0, -1],
            'buffPatch':{'id':{'buffShield4':{'shield':{'hpmRate':-48,'bearPoint':500 }} }},
          },
          'nonSkill':1,    #非技能，不受傲气影响
          'noBfr':1,
          'noAft':1,
          'times':-1,  
          'lv':1,
          'info':['分摊',0],
      },],
   },

   'skill961':{   #障毒
      'infoArr':[
         'r|act[0]',
         '%|act[0]._dmgRealMax',
         '%|act[0]._dmgRealMaxRnd*2',
      ],
      'nextArr':[
         '%|act[0]._dmgRealMax',
         '%|act[0]._dmgRealMaxRnd*2',
      ],
      'highArr':['%|0-300'],
      'merge':5,
      'state':1,                                       #最早可见天数   
      'open_date':datetime.datetime(2021,11,17,5,0),   #最早可见日期
      'index':56,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      #'icon':'item084',
      'ability_info':['cha','agi'],
      'limit':{'cha':100},
      'passive':[
         {
            'rslt':{'power':800,'powerRate':45},
         },
         {
            'cond':['skill.skill961', '*', 1],
            'rslt':{'power':150,'powerRate':22},
         },
      ],
      'lvPatch':{   #满足特定等级的补丁（面板显示值凑整）
         '30':{  
            'act[0]._dmgRealMaxRnd':1,
         },
      },
      'up':{
          'act[0].buff.buffPoisonousNew.lv':1,
          'act[0]._dmgRealMax':0.7,
          'act[0]._dmgRealMaxRnd':1,
      },
      'high':{
         'lv':17,
         'passive':[
            {
               'rslt':{'power':1200,'powerRate':20},
            },
         ],
      },
      'act':[   #远战首回合发动，使敌方全场陷入障毒之中，每回合结束时阵亡2~4%(4~10%)的最大兵力。若智力高于对手，近战回合障毒强度+40%（障毒效果持续整场，不算做状态，且不可驱散）
         {
            'priority':-100003,
            'type': 1,
            'src': 2,
            'tgt':[1, -6],
            'round':{'1':1000},
            'noBfr': 1,
            'noAft': 1,

            '_dmgRealMax':20,
            '_dmgRealMaxRnd':20,

            'buff':{'buffPoisonousNew':{'lv':1}},
            'eff':'eff961',
            'actId':'skill961',
            'info':['skill961',2],
         },
      ],
   },


   'skill970':{   #霸君
      'infoArr':[
         'r|act[0]',
         '%|act[0].dmgRealHp0',
      ],
      'nextArr':['%|act[0].dmgRealHp0'],
      'highArr':[],
      'merge':5,
      'state':1,                                       #最早可见天数   
      'open_date':datetime.datetime(2021,11,17,5,0),   #最早可见日期
      'index':57,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      #'icon':'item084',
      'ability_info':['sum'],
      'limit':{'sex':1,'sum':400},
      'passive':[
         {
            'rslt':{'power':800,'powerRate':45},
         },
         {
            'cond':['skill.skill970', '*', 1],
            'rslt':{'power':150,'powerRate':22},
         },
      ],
      'lvPatch':{   #满足特定等级的补丁
         '30':{ 'act[0].dmgRealHp0':5 },
      },
      'up':{ 'act[0].dmgRealHp0':5 },
      'high':{      #发动【霸君】后若敌方前军兵力低于一半，则敌方全体进入【战栗】状态，持续2回合。【战栗】期间，部队暴击率-100%
         'lv':15,
         'passive':[
            {
               'rslt':{'power':1200,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill970_h':{
                     'act':[
                     {
                         'priority':9701,
                         'type': 3,
                         'src': 2,
                         'srcFree': 1,
                         'times': -1,
                         'tgt':[1, -1],
                         'buff':{'buffTremble':{'round':2}},
                         'isHero':0,      #不认为是独立英雄技
                         'nonSkill':1,
                         'noBfr':1,
                         'noAft':1,
                         'cond':[['enemyHpPoint','<',500,0]],
                         'atOnce':-1,
                         'follow':'skill970',
                         'time':0,  
                         'actId':'skill970_h',
                         'eff':'effNull',
                         'info':['半血战栗',0],
                     }],
                  },
               },
            },
         }],
      },
      'act':[   #远战第二回合发动，忽略双方触发型辅助技能，按我方前军当前兵力的15%(30%)，消灭敌方前军兵力。若战力高于对手，【霸君】可额外按我方后军当前兵力的20%，消灭目标兵力
         {
            'priority':100005,
            'type': 1,
            'src': 2,
            'tgt':[1, 0],
            'round':{'2':1000},
            'noBfr': 1,
            'noAft': 1,
            'noBuff':1,
            'dmgRealHp0':150,
            #'dmgReal':1,
            'banFollow':{'useBase':1, 'keys':{'isAssist':1000}},
            'eff':'eff970',
            'actId':'skill970',
            'info':['skill970',2],
         },
      ],
      'special':[{
         'cond':[['compare','power','>']],
         'change':{
            'skill':{
               'skill970_1':{
                  'act':[{
                     'priority':10000.970,
                     'type': 2,
                     'src': 2,
                     'noBfr': 1,
                     'noAft': 1,
                     'binding':{'dmgRealHp1':200,},  #'dmgReal':1
                     'follow':'skill970',
                     #'eff':'effNull',
                     #'time':0,
                     'info':['按后增伤',0],
                  }],
               },
            },
         },
      }],
   },

   'skill971':{   #仙音
      'infoArr':[
         'r|act[0]',
         '%|special[0].change.skill.skill971_1.act[0].summon',
         '%|special[0].change.skill.skill971_1.act[1].order.dmgRealRate',
      ],
      'nextArr':['%|special[0].change.skill.skill971_1.act[0].summon','%|special[0].change.skill.skill971_1.act[1].order.dmgRealRate'],
      'highArr':[],
      'merge':5,
      'state':1,                                       #最早可见天数   
      'open_date':datetime.datetime(2021,11,17,5,0),   #最早可见日期
      'index':58,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      #'icon':'item084',
      'ability_info':['sum'],
      'limit':{'sex':0,'sum':400},
      'passive':[
         {
            'rslt':{'power':800,'powerRate':45},
         },
         {
            'cond':['skill.skill971', '*', 1],
            'rslt':{'power':150,'powerRate':22},
         },
      ],
      'lvPatch':{   #满足特定等级的补丁
         '30':{ 
            'special[0].change.skill.skill971_1.act[0].summon':0.85, 'special[0].change.skill.skill971_1.act[2].summon':0.85,
            'special[0].change.skill.skill971_1.act[1].order.dmgRealRate':5, '%|special[0].change.skill.skill971_1.act[3].order.dmgRealRate':5,
         },
      },
      'up':{ 
          'special[0].change.skill.skill971_1.act[0].summon':1.35, 'special[0].change.skill.skill971_1.act[2].summon':1.35,
          'special[0].change.skill.skill971_1.act[1].order.dmgRealRate':5, 'special[0].change.skill.skill971_1.act[3].order.dmgRealRate':5, 
          'special[0].change.skill.skill971_1.act[1].order.lv':1, 'special[0].change.skill.skill971_1.act[3].order.lv':1, 
      },
      'high':{      #发动【仙音】后，敌方此后获得的护盾类效果-30%
         'lv':15,
         'passive':[
            {
               'rslt':{'power':1200,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill971_h':{
                     'act':[{
                         'priority':10000.971,
                         'type': 3,
                         'src': 2,
                         'tgt':[1, -1],
                         'allTimes': 1,
                         'nonSkill':1,
                         'noBfr':1,
                         'noAft':1,
                         'isHero':0,
                         'buff':{'buff971_2':{'round':99}}, 
                         'follow':'skill971',
                         'time':0,  
                         'eff':'effNull',
                         'info':['降敌护盾',0],
                     }],
                  },
               },
            },
         }],
      },
      'act':[   #远战首回合发动，忽略双方触发型辅助技能，为我方前军或后军附加【仙音】状态，持续3回合。【仙音】期间，每回合结束时恢复自身4%(8%)的最大兵力。【仙音】被驱散或自然移除时，触发【音爆】消灭敌方全军15%(30%)的当前兵力
         {
            'priority':100005,
            'type': 1,
            'src': 2,
            'tgt':[0, -2],
            'round':{'1':1000},
            'noBfr': 1,
            'noAft': 1,
            'buff':{'buff971':{'round':3}},
            'banFollow':{'useBase':1, 'keys':{'isAssist':1000}},
            'eff':'eff971', 
            'info':['skill971',2],
         },
      ],
      'special':[{
         'change':{
            'skill':{
               'skill971_1':{
                  'act':[{    #前军
                     'priority':4000040.97101,
                     'type': 27,
                     'src': 0,
                     'round':{'any':1000},
                     'tgt':[0, -9],
                     'times': 1,
                     'noBfr': 1,
                     'noAft': 1,
                     'nonSkill':1,
                     'cond':[['checkBuff', 0, 0, 'buff971', '>', 0]],   #场中存在状态[1]敌我[2]部队[3]状态类型或指定状态[4]比较符[5]数量 
                     'summonReal':10,
                     'summon':40,
                     'eff':'eff971_0',
                     'info':['仙音恢复0',0],
                  },
                  {
                     'priority':10000.97102,
                     'type': 21,
                     'src': 0,
                     'round':{'any':1000},
                     'order':{
                         'src': 2,
                         'tgt':[1, -1],
                         'nonSkill':1,
                         'noBfr':1,
                         'noAft':1,
                         'noBuff':1,
                         'dmgRealRate':150,
                         'lv':1,
                         'info':['skill971_1',2],  #音爆
                         'eff':'eff971_1',
                     },
                     'noBfr': 1,
                     'noAft': 1,
                     'nonSkill':1,
                     'times': 1,
                     'condBuffChange':'buff971',
                     'eff':'effNull',
                     'time':0,
                     'info':['仙音消失0',0],
                  },

                  {    #后军
                     'priority':4000041.97103,
                     'type': 27,
                     'src': 1,
                     'round':{'any':1000},
                     'tgt':[0, -10],
                     'times': 1,
                     'noBfr': 1,
                     'noAft': 1,
                     'nonSkill':1,
                     'cond':[['checkBuff', 0, 1, 'buff971', '>', 0]],   #场中存在状态[1]敌我[2]部队[3]状态类型或指定状态[4]比较符[5]数量 
                     'summonReal':10,
                     'summon':40,
                     'eff':'eff971_0',
                     'info':['仙音恢复1',0],
                  },
                  {
                     'priority':10000.97104,
                     'type': 21,
                     'src': 1,
                     'round':{'any':1000},
                     'order':{
                         'src': 2,
                         'tgt':[1, -1],
                         'nonSkill':1,
                         'noBfr':1,
                         'noAft':1,
                         'noBuff':1,
                         'dmgRealRate':150,
                         'lv':1,
                         'info':['skill971_1',2],  #音爆
                         'eff':'eff971_1',
                     },
                     'noBfr': 1,
                     'noAft': 1,
                     'nonSkill':1,
                     'times': 1,
                     'condBuffChange':'buff971',
                     'eff':'effNull',
                     'time':0,
                     'info':['仙音消失1',0],
                  }],
               },
            },
         },
      }],
   },

   'skill972':{   #两仪  6合
      'infoArr':[
         'r|act[0]',
         '%|act[0].dmg',
      ],
      'nextArr':['%|act[0].dmg'],
      'highArr':[],
      'merge':6,
      'state':1,                                       #最早可见天数   
      'open_date':datetime.datetime(2021,11,17,5,0),   #最早可见日期
      'index':59,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      #'icon':'item084',
      'ability_info':['str','agi'],
      'limit':{'str':100,'agi':100},
      'passive':[
         {
            'rslt':{'power':800,'powerRate':45},
         },
         {
            'cond':['skill.skill972', '*', 1],
            'rslt':{'power':150,'powerRate':22},
         },
      ],
      'lvPatch':{   #满足特定等级的补丁（面板显示值凑整）
         '30':{ 'act[0].dmg':-5 },
      },
      'up':{ 'act[0].dmg':115,'act[0].dmgReal':23 },
      'high':{      #首次发动【两仪】后，可将造成伤害的50%转化为生命能量，用于增援我方战损较高部队的兵力
         'lv':17,
         'passive':[
            {
               'rslt':{'power':1200,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill972_h':{
                     'act':[{
                         'priority':1003,
                         'type': 3,
                         'src': 2,	               #发出源 0前军，1后军，【2英雄】，-1全军
                         'tgt':[0, {'key':'hpPer'}],   #剩余兵力较少的
                         'times': 1,
                         'allTimes': 1,

                         'nonSkill':1,    #非技能，不受傲气和特殊加成影响
                         'noBfr': 1,      #本次行动不能触发前置绑定效果
                         'noAft': 1,      #本次行动不能触发后置绑定效果 
                         'noBuff': 1,     #不受增援系效果影响
                         'isHero':0,

                         'summonReal':1,
                         'mult':{'summonReal':1},
                         'costKey': 'energys972',
                         'cost':1,
                         'costMult':1,
                         'multMax':-1,
                         'eff':'eff972h',
                         'follow':'skill972',
                         'info':['skill972_h',0],
                     }],
                  },
               },
            },
         }],
      },
      'act':[   #远战第二回合发动，对敌方前军造成222%（555%）伤害。且武力或智力高于对手时，【两仪】有[50%]的几率再次连发。若敌我双方性别不同，【两仪】必定暴击
         {
            'priority':100006,
            'type': 1,
            'src': 2,
            'tgt':[1, 0],
            'round':{'2':1000},
            'dmg':2220,
            'dmgReal':400,
            'dmgLimit':220,
            'atk0': 650, 
            'atk1': 400, 
            'eff':'eff972',
            'info':['skill972',2],
         },
         {
            'priority': 100005,	 
            'type': 2,
            'src': 2,
            'times':1,
            'nonSkill':1,
            'binding':{
               'energys':{
                  'eSkill792':{
                     'priority':97200,
                     'condE':['dmg','*',0],
                     'srcE':{
                        'energys972':{'num':0.5},
                     },
                  },
               },
            },
            'follow':'skill972',
            'info':['两仪充能',0],
         },
      ],
      'special':[
      {
         'priority':-555.9720,
         'cond':[[['compare','agi', '>'],['compare','str', '>']]],
         'change':{
            'skill':{
               'skill972.act[0].timesRnd':500,
            },
         },
      },
      {
         'cond':[['compare','sex','!=']],
         'change':{
            'skill':{
               'skill972.act[0].crit':99999999,
            },
         },
      }],
   },


   'skill973':{   #四象  6合
      'infoArr':[
         'r|act[0]',
         '%|act[0].dmgRealMax',
      ],
      'nextArr':['%|act[0].dmgRealMax'],
      'highArr':[],
      'merge':6,
      'state':1,                                       #最早可见天数   
      'open_date':datetime.datetime(2021,11,17,5,0),   #最早可见日期
      'index':60,
      'type':4,
      'cost_type':3,
      'max_level':25,
      'shogun_type':3,
      #'icon':'item084',
      'ability_info':['sum'],
      'limit':{'sum':400},
      'passive':[
         {
            'rslt':{'power':800,'powerRate':45},
         },
         {
            'cond':['skill.skill973', '*', 1],
            'rslt':{'power':150,'powerRate':22},
         },
      ],
      'lvPatch':{   #满足特定等级的补丁
         '30':{ 'act[0].dmgRealMax':1 },
      },
      'up':{ 'act[0].dmgRealMax':11 },
      'high':{      #近战回合，我方即将按比例损失兵力时，减免25%的效果。若战场中存在任意气象，发动【四象】可使目标【混乱】1回合
         'lv':17,
         'passive':[
            {
               'rslt':{'power':1200,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill973_h':{
                     'act':[
         {
            'priority':9730, 
            'type': 4,             #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': -1,             #发出源 0前军，1后军，【2英雄】，-1全军
            'round':{'near':1000},
            'follow':'all',        
            'times':-1,  
            'nonSkill':1,    #非技能，不受傲气和特殊加成影响
            'binding':{
               'resRealRate':250,      #减免本次伤害
            },
            'info':['近战减免',0],           #应该引用文字配置
         },
         {
            'priority':9731, 
            'type': 2,             #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': -1,             #发出源 0前军，1后军，【2英雄】，-1全军
            'round':{'near':1000},
            'follow':'all',        
            'times':-1,    
            'nonSkill':1,    #非技能，不受傲气和特殊加成影响
            'binding':{
               'resRealRate':250,      #减免本次伤害
            },
            'info':['近战减免',0],           #应该引用文字配置
         },
         {
            'priority':9732, 
            'type': 2,             #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,             #发出源 0前军，1后军，【2英雄】，-1全军
            'cond':[['weather','!=',0]],  
            'follow':'skill973',        
            'times':-1,    
            'nonSkill':1,    #非技能，不受傲气和特殊加成影响
            'binding':{'buff.buffStun':{'round':1}},
            'info':['四象混乱',0],           #应该引用文字配置
         }],
                  },
               },
            },
         }],
      },
      'act':[   #近战首回合发动，直接消灭敌方后军12%（44%）的最大兵力（非伤害）。四维总数每比对手高10，【四象】额外消灭目标4%的最大兵力（最多额外消灭20%的最大兵力）
         {
            'priority':100007,
            'type': 1,
            'src': 2,
            'tgt':[1, 1],
            'round':{'3':1000},
            'noBfr': 1,
            'noAft': 1,
            'noBuff':1,
            'dmgRealMax':120,
            'dmgReal':1,
            'eff':'eff973',
            'actId':'skill973',
            'info':['skill973',2],
         },
      ],
      'special':[
        {
          'priority':-555.9730,
          'cond':[['compare','sum','>=',0,0,10]],
          'change':{
            'skill':{
               'skill973_1':{
                  'act':[{
                     'priority':10000.973,
                     'type': 2,
                     'src': 2,
                     'nonSkill':1,
                     'binding':{},
                     'follow':'skill973',
                     'info':['四维增伤',0],
                  }],
               },
            },
          },
        },
        {
          'priority':-555.9731,
          'cond':[['compare','sum','*',5,10,0]],
          'change':{
            'skill':{
               'skill973_1.act[0].binding.dmgRealMax':40,
               'skill973_1.act[0].binding.dmgReal':10,
            },
          },
        },
      ],
   },



#辅助技

   'skill260':{   #策谋
      'infoArr':['%|act[0].binding.dmg*2','%|act[1].binding.res*2'],
      'nextArr':['%|act[0].binding.dmg*2','%|act[1].binding.res*2'],
      'highArr':['%|high.special[0].change.skill.skill260h.act[0].binding.block'],
      'index':541,
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['agi','cha'],
      'limit':{'agi':90,'type':1},
      'isAssist':1,
      'passive':[
         {
            'rslt':{'power':150},
         },
         {
            'cond':['skill.skill260', '*', 0],
            'rslt':{'powerRate':14},
         },
      ],
      'up':{ 'act[0].binding.dmg':5, 'act[1].binding.res':5},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].binding.dmg':-2.5, 'act[1].binding.res':-2.5  },
         '27':{  'act[0].binding.dmg':-5, 'act[1].binding.res':-5  },
         '28':{  'act[0].binding.dmg':-7.5, 'act[1].binding.res':-7.5  },
         '29':{  'act[0].binding.dmg':-10, 'act[1].binding.res':-10  },
         '30':{  'act[0].binding.dmg':-12.5, 'act[1].binding.res':-12.5  },
      },
      'high':{
         'lv':15,
         'passive':[
            {
               'rslt':{'power':600,'powerRate':15},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill260h':{
                     'act':[{
                        'priority':9409,
                        'type': 4,
                        'src': -1,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'cond':[['army',2],['comp','cha']],
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能，不受傲气 
                        'binding':{
                           'block':150,
                        },
                        'info':['策谋格挡',0],	  
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':9044,
            'type': 2,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': -1,      #发出源 0前军，1后军，2英雄，-1全军，-2全军含英雄
            'follow':{'keys':{'dmg':1000,'dmgReal':1000}},
            'unFollow': 'effFaction',      #不能跟随特定行动的info触发
            'cond':[['army',2],['comp','agi']],  #己方英雄存活
            'times': -1,    #每回合最多使用次数
            'binding':{
               'dmg':30,     #技能伤害系数
               'dmgReal':3,
            },
            'info':['skill260',1],	          #[0]应该引用文字配置，[1]为显示模式，0只用于打印，1word，2banner，3fate。[2]由程序自动补足等级
         },
         {
            'priority':9410,
            'type': 4,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src':-1, 
            'cond':[['army',2],['comp','agi'],['checkBuff',0,-5,'buffStun','=',0]],  #场中存在状态[1]敌我[2]部队[3]状态类型或指定状态[4]比较符[5]数量       
            'times': -1,    #每回合最多使用次数
            'binding':{
               'res':30,
            },
            'actId':'skill260_1',
            'info':['skill260',1],	          #策谋防御
         },
      ],

   },
   'skill261':{   #暴烈
      'infoArr':['%|act[0].binding.dmg','%|act[1].binding.ignDef'],
      'nextArr':['%|act[0].binding.dmg'],
      'highArr':['%|high.special[0].change.skill.skill261h.act[0].binding.crit'],
      'index':543,
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['str','lead'],
      'limit':{'str':90,'type':0},
      'isAssist':1,
      'passive':[
         {
            'rslt':{'power':150},
         },
         {
            'cond':['skill.skill261', '*', 0],
            'rslt':{'powerRate':14},
         },
      ],
      'up':{ 'act[0].binding.dmg':20},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].binding.dmg':-10  },
         '27':{  'act[0].binding.dmg':-20  },
         '28':{  'act[0].binding.dmg':-30  },
         '29':{  'act[0].binding.dmg':-40  },
         '30':{  'act[0].binding.dmg':-50  },
      },
      'high':{
         'lv':15,
         'passive':[
            {
               'rslt':{'power':600,'powerRate':15},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill261h':{
                     'act':[{
                        'priority':9045,
                        'type': 2,
                        'src': 0,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'cond':[['not',[['srcTgtTeam',0],['srcTgtArmy',-5]]],['army',2],['comp','lead']],
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能，不受傲气 
                        'binding':{
                           'crit':300,
                        },
                        'info':['暴烈暴击',0],
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':9046,
            'type': 2,	    #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 0,      #发出源 0前军，1后军，2英雄，-1全军，-2全军含英雄
            'cond':[['army',2]],  #己方英雄存活
            'times': -1,    #每回合最多使用次数
            'nonSkill':1,    #非技能，不受傲气 
            'cond':['not',[['srcTgtTeam',0],['srcTgtArmy',-5]]],     #攻击目标不为自己
            'follow':{'keys':{'dmg':1000,'dmgReal':1000}},
            'binding':{
               'dmg':150,
               'dmgReal':3,    #真实伤害点数
            },
            'info':['skill261',1],	          #[0]应该引用文字配置，[1]为显示模式，0只用于打印，1word，2banner，3fate。[2]由程序自动补足等级
         },
         {
            'priority':9047,
            'type': 2,	    #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 0,      #发出源 0前军，1后军，2英雄，-1全军，-2全军含英雄
            'cond':[['not',[['srcTgtTeam',0],['srcTgtArmy',-5]]],['army',2],['comp','str']],  #攻击目标不为自己，己方英雄存活
            'times': -1,    #每回合最多使用次数
            'nonSkill':1,    #非技能，不受傲气	 
            'isAssist':0, 
            'binding':{
               'ignDef':150,	#忽视防御率
            },
            'info':['skill261',0],	          #[0]应该引用文字配置，[1]为显示模式，0只用于打印，1word，2banner，3fate。[2]由程序自动补足等级
         },
      ],
   },

   'skill270':{   #军略
      'infoArr':['%|special[0].change.skill.skill270_0.act[0].binding.dmg*2','%|special[1].change.skill.skill270_1.act[0].binding.res*2'],
      'nextArr':['%|special[0].change.skill.skill270_0.act[0].binding.dmg*2','%|special[1].change.skill.skill270_1.act[0].binding.res*2'],
      'highArr':['%|high.special[0].change.skill.skill270h.act[0].buff.buff270h.prop.defRate*2'],
      'index':544,
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['lead'],
      'limit':{'lead':85},
      'isAssist':1,
      'actIds':['战备','skill270_1'],
      'passive':[
         {
            'rslt':{'power':150},
         },
         {
            'cond':['skill.skill270', '*', 0], 
            'rslt':{'powerRate':14},
         },
      ],
      'up':{ 
         'special[0].change.skill.skill270_0.act[0].binding.dmg':5,
         'special[0].change.skill.skill270_0.lv':1,
         'special[1].change.skill.skill270_1.act[0].binding.res':5,
         'special[1].change.skill.skill270_1.lv':1,
      },
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'special[0].change.skill.skill270_0.act[0].binding.dmg':-2.5,    'special[1].change.skill.skill270_1.act[0].binding.res':-2.5 },
         '27':{  'special[0].change.skill.skill270_0.act[0].binding.dmg':-5,    'special[1].change.skill.skill270_1.act[0].binding.res':-5 },
         '28':{  'special[0].change.skill.skill270_0.act[0].binding.dmg':-7.5,    'special[1].change.skill.skill270_1.act[0].binding.res':-7.5 },
         '29':{  'special[0].change.skill.skill270_0.act[0].binding.dmg':-10,    'special[1].change.skill.skill270_1.act[0].binding.res':-10 },
         '30':{  'special[0].change.skill.skill270_0.act[0].binding.dmg':-12.5,    'special[1].change.skill.skill270_1.act[0].binding.res':-12.5 },
      },
      'high':{
         'lv':17,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':15},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill270h':{
                     'act':[{
                        'priority':10001,
                        'type': 0,
                        'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'tgt':[0, -1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
                        'round':{'0':1000},
                        'buff':{'buff270h':{'round':3, 'prop':{'defRate':100}}},
                        'time':0,	          #强制指定战报时间
                        'nonSkill':1,    #非技能，不受傲气
                        'noBfr':1,
                        'noAft':1,
                        'eff':'effNull',
                        'info':['战备',0],
                     }],
                  },
               },
            },
         }]
      },
      'special':[
         {
            'cond':[['compare', 'hp', '>']],   #修正特性，战前总兵力大于对方
            'change':{
               'skill':{
                  'skill270_0':{   #战前已分割放置到各个主体中，战中回合行动时加入到战斗action列表
                     'lv':1,
                     'act':[
                        {
                           'priority':9056,
                           'isAssist':1,
                           'type': 2,	            #设定子集默认值   触发类型，0起始 1主 2攻前 3攻后 4防前 5防后
                           'src':-2,                   #设定子集默认值   发出源 0前军，1后军，2英雄，-1全军，-2全军含英雄
                           'info':['skill270',1],	          #[0]应该引用文字配置，[1]为显示模式，0只用于打印，1word，2banner，3fate。[2]由程序自动补足等级
                           'times': -1,    #每回合最多使用次数
                           'binding':{'dmg':30},	          #绑定的效果
                        },
                     ],
                  }
               },
            },
         },
         {
            'cond':[['compare', 'hp', '<']],   #修正特性，战前总兵力小于对方
            'change':{
               'skill':{
                  'skill270_1':{   #战前已分割放置到各个主体中，战中回合行动时加入到战斗action列表
                     'lv':1,
                     'act':[
                        {
                           'priority':9413,
                           'isAssist':1,
                           'type': 4,	            #设定子集默认值   触发类型，0起始 1主 2攻前 3攻后 4防前 5防后
                           'src':-1,                   #设定子集默认值   发出源 0前军，1后军，2英雄，-1全军，-2全军含英雄
                           'info':['skill270',1],	          #[0]应该引用文字配置，[1]为显示模式，0只用于打印，1word，2banner，3fate。[2]由程序自动补足等级
                           'times': -1,    #每回合最多使用次数
                           'actId':'skill270_1',
                           'binding':{'res':30},	          #绑定的效果
                        },
                     ],
                  }
               },
            },
         },
      ],


   },
   'skill271':{   #诱敌
      'infoArr':['%|act[0].round.1','%|act[0].binding.res','%|special[0].change.skill.$skill271.act[0].round.1'],
      'nextArr':['%|act[0].round.1'],
      'highArr':['%|high.special[0].change.skill.skill271h.act[0].binding.crit','%|high.special[0].change.skill.skill271h.act[1].binding.crit'],
      'index':545,
      'state':7,   #开服几天后显示   
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['cha','lead'],
      'limit':{'cha':90,'lead':90},
      'isAssist':1,     #有值表示为触发类  1发动时机被动触发  2发动时机特殊触发
      'passive':[
         {
            'rslt':{'power':200},
         },
         {
            'cond':['skill.skill271', '*', 0],
            'rslt':{'powerRate':18},
         },
      ],
      'special':[
           {
            'cond':[['compare','power','<']],
            'change':{
               'skill':{
                  'skill271.act[0].round.1':150,
               },
            },
           },
      ],
      'up':{ 'act[0].round.1':15},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].round.1':-5 },
         '27':{  'act[0].round.1':-10 },
         '28':{  'act[0].round.1':-15 },
         '29':{  'act[0].round.1':-20 },
         '30':{  'act[0].round.1':-25 },
      },
      'high':{
         'lv':17,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill271h':{
                     'act':[
                      {
                        'priority':9048,
                        'type': 2,
                        'src': -1,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'round':{'2':1000},
                        'cond':[['army',2],['comp','cha']],
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能，不受傲气 
                        'binding':{
                           'crit':400,
                        },
                        'info':['诱敌比魅力',0],
                      },
                      {
                        'priority':9049,
                        'type': 2,
                        'src': -1,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'round':{'near':1000},
                        'cond':[['army',2],['comp','lead']],
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能，不受傲气 
                        'binding':{
                           'crit':200,
                        },
                        'info':['诱敌比统帅',0],
                      }
                    ],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':9411,
            'type': 4,	    #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': -1,      #发出源 0前军，1后军，2英雄，-1全军，-2全军含英雄
            'round':{'1':100},
            'cond':[['army',2]],  #己方英雄存活
            'times': -1,    #每回合最多使用次数
            'binding':{
               'res':990,
            },
            'info':['skill271',1],	          #[0]应该引用文字配置，[1]为显示模式，0只用于打印，1word，2banner，3fate。[2]由程序自动补足等级
         },
      ],
   },
   'skill266':{   #增员
      'infoArr':['l|passive[1].rslt.$army[0].hpmBase'],
      'nextArr':['l|passive[1].rslt.$army[0].hpmBase'],
      'breakArr':['b|passive[2].rslt.$army[0].hpmBase+260'],
      'index':546,
      'type':5,
      'cost_type':5,
      'max_level':20,
      'shogun_type':5,
      'ability_info':[],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':150},
         },
         {
            'cond':['skill.skill266', '*', 0, 20],
            'rslt':{'army[0].hpmBase':13,'power':30},
         },
         {
            'cond':['skill.skill266', '*', 20],
            'rslt':{'army[0].hpmBase':8},
         },
      ],
   },
   'skill267':{   #扩编
      'infoArr':['l|passive[1].rslt.$army[1].hpmBase'],
      'nextArr':['l|passive[1].rslt.$army[1].hpmBase'],
      'breakArr':['b|passive[2].rslt.$army[1].hpmBase+260'],
      'index':547,
      'type':5,
      'cost_type':5,
      'max_level':20,
      'shogun_type':5,
      'ability_info':[],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':150},
         },
         {
            'cond':['skill.skill267', '*', 0, 20],
            'rslt':{'army[1].hpmBase':13,'power':30},
         },
         {
            'cond':['skill.skill267', '*', 20],
            'rslt':{'army[1].hpmBase':8},
         },
      ],
   },
   'skill268':{   #刚体
      'infoArr':['l|passive[1].rslt.$army[0].defBase'],
      'nextArr':['l|passive[1].rslt.$army[0].defBase'],
      'breakArr':['b|passive[2].rslt.$army[0].defBase+60'],
      'index':548,
      'type':5,
      'cost_type':5,
      'max_level':20,
      'shogun_type':5,
      'ability_info':[],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':150},
         },
         {
            'cond':['skill.skill268', '*', 0, 20],
            'rslt':{'army[0].defBase':3,'power':70},
         },
         {
            'cond':['skill.skill268', '*', 20],
            'rslt':{'army[0].defBase':2},
         },
      ],
   },
   'skill269':{   #灵敏
      'infoArr':['l|passive[1].rslt.$army[1].defBase'],
      'nextArr':['l|passive[1].rslt.$army[1].defBase'],
      'breakArr':['b|passive[2].rslt.$army[1].defBase+60'],
      'index':549,
      'type':5,
      'cost_type':5,
      'max_level':20,
      'shogun_type':5,
      'ability_info':[],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':150},
         },
         {
            'cond':['skill.skill269', '*', 0, 20],
            'rslt':{'army[1].defBase':3,'power':70},
         },
         {
            'cond':['skill.skill269', '*', 20],
            'rslt':{'army[1].defBase':2},
         },
      ],
   },

   'skill262':{   #勇武
      'infoArr':['l|passive[1].rslt.str'],
      'nextArr':['l|passive[1].rslt.str'],
      'breakArr':['-|6','b|passive[2].rslt.$army[0].atkBase'],
      'index':550,
      'type':5,
      'cost_type':6,
      'max_level':6,
      'shogun_type':6,
      'ability_info':['str'],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':300},
         },
         {
            'cond':['skill.skill262', '*', 0, 6],
            'rslt':{'str':1,'power':300,'powerRate':10},
         },
         {
            'cond':['skill.skill262', '*', 6],
            'rslt':{'army[0].atkBase':4},
         },
      ],
   },
   'skill263':{   #才学
      'infoArr':['l|passive[1].rslt.agi'],
      'nextArr':['l|passive[1].rslt.agi'],
      'breakArr':['-|6','b|passive[2].rslt.$army[1].atkBase'],
      'index':551,
      'type':5,
      'cost_type':6,
      'max_level':6,
      'shogun_type':6,
      'ability_info':['agi'],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':300},
         },
         {
            'cond':['skill.skill263', '*', 0, 6],
            'rslt':{'agi':1,'power':300,'powerRate':10},
         },
         {
            'cond':['skill.skill263', '*', 6],
            'rslt':{'army[1].atkBase':4},
         },
      ],
   },
   'skill264':{   #修养
      'infoArr':['l|passive[1].rslt.cha'],
      'nextArr':['l|passive[1].rslt.cha'],
      'breakArr':['-|6','b|passive[2].rslt.defBase'],
      'index':552,
      'type':5,
      'cost_type':6,
      'max_level':6,
      'shogun_type':6,
      'ability_info':['cha'],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':300},
         },
         {
            'cond':['skill.skill264', '*', 0, 6],
            'rslt':{'cha':1,'power':300,'powerRate':10},
         },
         {
            'cond':['skill.skill264', '*', 6],
            'rslt':{'defBase':2},
         },
      ],
   },
   'skill265':{   #兵法
      'infoArr':['l|passive[1].rslt.lead'],
      'nextArr':['l|passive[1].rslt.lead'],
      'breakArr':['-|6','b|passive[2].rslt.hpmBase'],
      'index':553,
      'type':5,
      'cost_type':6,
      'max_level':6,
      'shogun_type':6,
      'ability_info':['lead'],
      #'limit':{},
      'passive':[
         {
            'rslt':{'power':300},
         },
         {
            'cond':['skill.skill265', '*', 0, 6],
            'rslt':{'lead':1,'power':300,'powerRate':10},
         },
         {
            'cond':['skill.skill265', '*', 6],
            'rslt':{'hpmBase':5},
         },
      ],
   },


   'skill250':{   #狂骨
      'infoArr':['*|act[0].binding.dmgScale*1.25','*|act[1].binding.res'],
      'nextArr':['*|act[0].binding.dmgScale*1.25','*|act[1].binding.res'],
      'highArr':['p|high.passive'],
      'index':554,
      'merge':4,
      'state':1,   #开服几天后显示
      'open_date':datetime.datetime(2021,12,3,5,0),   #最早可见日期
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['str'],
      'limit':{},
      'passive':[
         {
            'rslt':{'power':200},
         },
         {
            'cond':['skill.skill250', '*', 0],
            'rslt':{'powerRate':18},
         },
      ],
      'up':{ 'act[0].binding.dmgScale':16, 'act[0].binding.dmgReal':2, 'act[1].binding.res':10,},
      'high':{
         'lv':17,
         'passive':[
            {
               'rslt':{
                  'str':2,
                  'army[0].spd':5,
                  'power':650,
                  'powerRate':30,
               },
            },
         ],
      },
      'act':[
         {
            'priority':9217.250,
            'type': 2,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,
            'times': -1,
            'nonSkill':1,
            'follow':{'skill225':1,'skill226':1,'skill227':1,'skill236':1,'skill243':1,'skill245':1,'skill241':1,'skill244':1,'skill239':1,'skill972':1},   
            'binding':{
               'dmgScale':136,    #原值170
               'dmgReal':20,
            },
            'info':['skill250',0],
         },
         {
            'priority':9418.250,
            'type': 4,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': -1,
            'times': -1,
            'nonSkill':1,
            'follow':{'skill225':1,'skill226':1,'skill227':1,'skill236':1,'skill243':1,'skill245':1,'skill241':1,'skill244':1,'skill239':1,'skill972':1},   
            'binding':{
               'res':110,   #原值110   
            },
            'info':['狂骨免伤',0],
         },
      ],
   },

   'skill251':{   #博闻
      'infoArr':['*|act[0].binding.dmgScale*1.25','*|act[1].binding.res'],
      'nextArr':['*|act[0].binding.dmgScale*1.25','*|act[1].binding.res'],
      'highArr':['p|high.passive'],
      'index':555,
      'merge':4,
      'state':1,   #开服几天后显示
      'open_date':datetime.datetime(2021,12,3,5,0),   #最早可见日期
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['agi'],
      'limit':{},
      'passive':[
         {
            'rslt':{'power':200},
         },
         {
            'cond':['skill.skill251', '*', 0],
            'rslt':{'powerRate':18},
         },
      ],
      'up':{ 'act[0].binding.dmgScale':16, 'act[0].binding.dmgReal':2, 'act[1].binding.res':10,},
      'high':{
         'lv':17,
         'passive':[
            {
               'rslt':{
                  'agi':2,
                  'army[1].spd':5,
                  'power':650,
                  'powerRate':30,
               },
            },
         ],
      },
      'act':[
         {
            'priority':9219.251,
            'type': 2,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,
            'times': -1,
            'nonSkill':1,
            'follow':{'skill228':1,'skill229':1,'skill232':1,'skill233':1,'skill238':1,'skill242':1,'skill244':1,'skill247':1,'skill239':1,'skill972':1},   
            'binding':{
               'dmgScale':136,
               'dmgReal':20,
            },
            'info':['skill251',0],
         },
         {
            'priority':9420.251,
            'type': 4,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': -1,
            'times': -1,
            'nonSkill':1,
            'follow':{'skill228':1,'skill229':1,'skill232':1,'skill233':1,'skill238':1,'skill242':1,'skill244':1,'skill247':1,'skill239':1,'skill972':1},    
            'binding':{
               'res':110,
            },
            'info':['博闻免伤',0],
         },
      ],
   },


   'skill252':{   #仁义
      'infoArr':['*|act[0].binding.dmgScale*1.25','*|act[1].binding.res'],
      'nextArr':['*|act[0].binding.dmgScale*1.25','*|act[1].binding.res'],
      'highArr':['p|high.passive'],
      'index':556,
      'merge':4,
      'state':1,   #开服几天后显示
      'open_date':datetime.datetime(2021,12,3,5,0),   #最早可见日期
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['cha'],
      'limit':{},
      'passive':[
         {
            'rslt':{'power':200},
         },
         {
            'cond':['skill.skill252', '*', 0],
            'rslt':{'powerRate':18},
         },
      ],
      'up':{ 'act[0].binding.dmgScale':16, 'act[0].binding.dmgReal':2, 'act[1].binding.res':10,},
      'high':{
         'lv':17,
         'passive':[
            {
               'rslt':{
                  'cha':2,
                  'hpmBase':40,
                  'power':650,
                  'powerRate':30,
               },
            },
         ],
      },
      'act':[
         {
            'priority':9221.252,
            'type': 2,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,
            'times': -1,
            'nonSkill':1,
            'follow':{'skill226':1,'skill230':1,'skill232':1,'skill235':1,'skill237':1,'skill248':1,'skill239':1},
            'binding':{
               'dmgScale':136,
               'dmgReal':20,
            },
            'info':['skill252',0],
         },
         {
            'priority':9422.252,
            'type': 4,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': -1,
            'times': -1,
            'nonSkill':1,
            'follow':{'skill226':1,'skill230':1,'skill232':1,'skill235':1,'skill237':1,'skill248':1,'skill239':1},
            'binding':{
               'res':110,
            },
            'info':['仁义免伤',0],
         },
      ],
   },

   'skill253':{   #推演
      'infoArr':['*|act[0].binding.dmgScale*1.25','*|act[1].binding.res'],
      'nextArr':['*|act[0].binding.dmgScale*1.25','*|act[1].binding.res'],
      'highArr':['p|high.passive'],
      'index':557,
      'merge':4,
      'state':1,   #开服几天后显示
      'open_date':datetime.datetime(2021,12,3,5,0),   #最早可见日期
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['lead'],
      'limit':{},
      'passive':[
         {
            'rslt':{'power':200},
         },
         {
            'cond':['skill.skill253', '*', 0],
            'rslt':{'powerRate':18},
         },
      ],
      'up':{ 'act[0].binding.dmgScale':16, 'act[0].binding.dmgReal':2, 'act[1].binding.res':10,},
      'high':{
         'lv':17,
         'passive':[
            {
               'rslt':{
                  'lead':2,
                  'defBase':10,
                  'power':650,
                  'powerRate':30,
               },
            },
         ],
      },
      'act':[
         {
            'priority':9223.253,
            'type': 2,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,
            'times': -1,
            'nonSkill':1,
            'follow':{'skill228':1,'skill233':1,'skill237':1,'skill243':1,'skill239':1,'skill247':1,'skill248':1},
            'binding':{
               'dmgScale':136,
               'dmgReal':20,
            },
            'info':['skill253',0],
         },
         {
            'priority':9424.253,
            'type': 4,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': -1,
            'times': -1,
            'nonSkill':1,
            'follow':{'skill228':1,'skill233':1,'skill237':1,'skill243':1,'skill239':1,'skill247':1,'skill248':1},
            'binding':{
               'res':110,
            },
            'info':['推演免伤',0],
         },
      ],
   },



   'skill274':{   #忘忧
      'infoArr':['%|special[0].change.prop.$heroLogic.deBuffRate+act[0].buff.skill274_0.prop.deBuffRate','*|special[0].changeEnemy.prop.$heroLogic.deBuffRate+act[1].buff.skill274_1.prop.deBuffRate'],
      'nextArr':['%|special[0].change.prop.$heroLogic.deBuffRate+act[0].buff.skill274_0.prop.deBuffRate','*|special[0].changeEnemy.prop.$heroLogic.deBuffRate+act[1].buff.skill274_1.prop.deBuffRate'],
      'highArr':['-|high.special[0].change.prop.$armys[1].stamina'],
      'index':560,
      'state':7,   #开服几天后显示   
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['cha','agi'],
      'limit':{'agi':85,'cha':90},
      'passive':[
         {
            'rslt':{'power':200},
         },
         {
            'cond':['skill.skill274', '*', 0],
            'rslt':{'powerRate':18},
         },
      ],
      'up':{ 
         'act[0].buff.skill274_0.prop.deBuffRate':-4,
         'act[1].buff.skill274_1.prop.deBuffRate':4,
      },
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].buff.skill274_0.prop.deBuffRate':2,  'act[1].buff.skill274_1.prop.deBuffRate':-2, },
         '27':{  'act[0].buff.skill274_0.prop.deBuffRate':4,  'act[1].buff.skill274_1.prop.deBuffRate':-4, },
         '28':{  'act[0].buff.skill274_0.prop.deBuffRate':6,  'act[1].buff.skill274_1.prop.deBuffRate':-6, },
         '29':{  'act[0].buff.skill274_0.prop.deBuffRate':8,  'act[1].buff.skill274_1.prop.deBuffRate':-8, },
         '30':{  'act[0].buff.skill274_0.prop.deBuffRate':10,  'act[1].buff.skill274_1.prop.deBuffRate':-10, },
      },
      'high':{
         'lv':15,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'prop':{
                  #'armys[0].stamina':1,
                  'armys[1].stamina':1,
               },
            },
         }]
      },
      'special':[
         {
            'priority':-150,
            'change':{
               'prop':{
                  'heroLogic.deBuffRate':-40,
                  'armys[0].deBuffRate':-10,
                  'armys[1].deBuffRate':-10,
               },
            },
            'changeEnemy':{
               'prop':{
                  'heroLogic.deBuffRate':40,
                  'armys[0].deBuffRate':10,
                  'armys[1].deBuffRate':10,
               },
            },
         },
      ],
      'act':[
         {
            'priority':2001,
            'type': 0,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[0, -1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'0':1000},
            'nonSkill':1,    #非技能，不受傲气
            'noBfr': 1,    #本次行动不能触发前置绑定效果
            'noAft': 1,    #本次行动不能触发后置绑定效果
            'buff':{'skill274_0':{'prop':{'deBuffRate':-20}}},
            'time':0,	          #强制指定战报时间
            'eff':'effNull',
            'info':['skill274',0],	          #应该引用文字配置
         },
         {
            'priority':2002,
            'type': 0,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, -1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'0':1000},
            'nonSkill':1,    #非技能，不受傲气
            'noBfr': 1,    #本次行动不能触发前置绑定效果
            'noAft': 1,    #本次行动不能触发后置绑定效果
            'buff':{'skill274_1':{'prop':{'deBuffRate':20}}},
            'time':0,	          #强制指定战报时间
            'eff':'effNull',
            'info':['skill274',0],	          #应该引用文字配置
         },
      ],

   },
   'skill280':{   #杀意
      'infoArr':['l|passive[1].rslt.$army[0].atkBase','l|passive[1].rslt.$army[1].atkBase'],
      'nextArr':['l|passive[1].rslt.$army[0].atkBase','l|passive[1].rslt.$army[1].atkBase'],
      'highArr':['%|high.special[0].change.skill.skill280h.act[0].buff.buff280h.prop.atkRate*4'],
      'breakArr':['b|passive[2].rslt.$army[0].atkBase+50','b|passive[2].rslt.$army[1].atkBase+50'],
      'index':561,
      'state':7,   #开服几天后显示   
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['str','lead'],
      'limit':{'str':85,'lead':90},
      'passive':[
         {
            'rslt':{'power':200},
         },
         {
            'cond':['skill.skill280', '*', 0, 25],
            'rslt':{'power':30, 'powerRate':9, 'army[0].atkBase':2, 'army[1].atkBase':2},
         },
         {
            'cond':['skill.skill280', '*', 25],
            'rslt':{'army[0].atkBase':1, 'army[1].atkBase':1},
         },
      ],
      'high':{
         'lv':17,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill280h':{
                     'act':[{
                        'priority':10002,
                        'type': 0,
                        'src': 2,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'tgt':[0, -1],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
                        'round':{'0':1000},
                        'buff':{'buff280h':{'round':3, 'prop':{'atkRate':50}}},
                        'time':0,	          #强制指定战报时间
                        'nonSkill':1,    #非技能，不受傲气
                        'noBfr':1,
                        'noAft':1,
                        'eff':'effNull',
                        'info':['速杀',0],
                     }],
                  },
               },
            },
         }]
      },
   },
   'skill276':{   #株连     睚眦必报，暗藏杀机。我方后军若受到英雄技攻击，有50%（满74%）的几率立即发动【株连】反击，对敌前军或后军造成58%（满130%）的伤害。此刻双方的兵种被动技能、触发型辅助技能不会生效
      'infoArr':['%|act[0].round.any','%|act[0].dmg'],
      'nextArr':['%|act[0].round.any','%|act[0].dmg'],
      'highArr':['%|high.special[0].change.skill.skill276h.act[0].binding.dmgScale'],
      'index':562,
      'state':7,   #开服几天后显示   
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':[],
      'limit':{'type':2},
      'isAssist':1,
      'passive':[
         {
            'rslt':{'power':200},
         },
         {
            'cond':['skill.skill276', '*', 0],
            'rslt':{'powerRate':18},
         },
      ],
      'up':{ 'act[0].round.any':10, 'act[0].dmg':30, 'act[0].dmgReal':3},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].round.any':-5, 'act[0].dmg':-10, },
         '27':{  'act[0].round.any':-10, 'act[0].dmg':-20 },
         '28':{  'act[0].round.any':-15, 'act[0].dmg':-30 },
         '29':{  'act[0].round.any':-20, 'act[0].dmg':-40 },
         '30':{  'act[0].round.any':-25, 'act[0].dmg':-50 },
      },

      'high':{
         'lv':10,
         'passive':[
            {
               'rslt':{'power':600,'powerRate':20},
            },
         ],
         'special':[{
            'cond':[['compare','sex','=']],
            'change':{
               'skill':{
                  'skill276h':{
                     'act':[{
                        'priority':9061,
                        'type': 2,
                        'src': 1,	           #发出源 0前军，1后军，【2英雄】，-1全军
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能，不受傲气
                        'binding':{'tgt':[1, -1],  'dmgScale':500,},
                        'follow':'skill276',            #必须跟随特定行动触发
                        'info':['株连全军',0],	          #应该引用文字配置
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':9502,
            'type': 5,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 1,
            'round':{'any':500,},
            'tgt':[1, -2],          #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'times': -1,    #每回合最多使用次数
            #'nonSkill':1,    #非技能，不受傲气
            'noBfr': 1,
            'noAft': 1,
            'unFollow':{'skill961':1},
            'follow':{'useBase':1, 'keys':{'isHero':1000}},
            'dmg':580,	#技能伤害系数
            'dmgReal':50,
            'dmgLimit':90,
            'cond':[['army',1],['srcArmy',2],['srcTeam',1]],
            'atk0': 100,    
            'atk1': 900,   
            'isRemote':1,
            'eff':'eff276',
            'info':['skill276',1],	          #应该引用文字配置
         },
      ],
   },
   'skill277':{   #集智
      'infoArr':['*|act[0].binding.dmgScale*1.25'],
      'nextArr':['*|act[0].binding.dmgScale*1.25'],
      'highArr':['%|high.special[0].change.skill.skill277h.act[0].binding.ignDef*1.5'],
      'index':563,
      'state':7,   #开服几天后显示   
      #'open_date':datetime.datetime(2020,6,4,5,0),   #最早可见日期
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['agi','lead'],
      'limit':{'agi':90,'lead':90},
      'isAssist':1,
      'passive':[
         {
            'rslt':{'power':200},
         },
         {
            'cond':['skill.skill277', '*', 0],
            'rslt':{'powerRate':18},
         },
      ],
      'up':{ 'act[0].binding.dmgScale':12, 'act[0].binding.dmgReal':1},
      'high':{
         'lv':13,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':20},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill277h':{
                     'act':[{
                        'priority':9063,
                        'type': 2,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
                        'src':2, 
                        'cond':[[['comp','agi'],['comp','lead']]], 
                        'follow':{'attach':'skill277'},      #必须跟随附加技
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能，不受傲气	
                        'binding':{
                           'ignDef':100,	#忽视防御率
                        },
                        'info':['集智比智力统帅',0],	          #策谋防御
                     }],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':9064,
            'type': 2,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,      #发出源 0前军，1后军，2英雄，-1全军，-2全军含英雄
            'times': -1,    #每回合最多使用次数
            'nonSkill':1,    #非技能，不受傲气	
            #'unFollow':{'group000':1,'group001':1,'group002':1,'group003':1,'group004':1,'group006':1,'group007':1,'group008':1,'skill275':1,},  
            #'follow':{'group000':1,'keys':{'isHero':9119}},   
            'follow':{'useBase':1, 'keys':{'dmg':200,'dmgReal':200,'isHero':800,'isFate':800,}},
            'binding':{
               'dmgScale':100,     
               'dmgReal':12,
            },
            'info':['skill277',1],	          #[0]应该引用文字配置，[1]为显示模式，0只用于打印，1word，2banner，3fate。[2]由程序自动补足等级
         },
      ],
   },

   'skill272':{   #急救
      'infoArr':['r|act[0]','-|act[0].cureReal','%|act[1].cure'],
      'nextArr':['-|act[0].cureReal','%|act[1].cure'],
      'highArr':['%|200'],
      'state':9,                                      #最早可见天数   
      #'open_date':datetime.datetime(2019,1,24,5,0),   #最早可见日期
      'index':570,
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      #'icon':'item084',
      'ability_info':['cha'],
      'limit':{'cha':98},
      'isAssist':1,
      'passive':[
         {
            'rslt':{'power':250},
         },
         {
            'cond':['skill.skill272', '*', 0],
            'rslt':{'powerRate':20},
         },
      ],
      'up':{ 'act[0].cureReal':3,'act[1].cure':10,'act[1].cureReal':1},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].cureReal':3,'act[1].cure':-5, },
         '27':{  'act[0].cureReal':6,'act[1].cure':-10, },
         '28':{  'act[0].cureReal':9,'act[1].cure':-15, },
         '29':{  'act[0].cureReal':12,'act[1].cure':-20, },
         '30':{  'act[0].cureReal':15,'act[1].cure':-25, },
      },

      'high':{
         'lv':13,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':25},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill272h':{
                     'act':[    #净化
                      {
                        'priority':9050,
                        'type': 2,
                        'src': -1,	            #发出源 0前军，1后军，【2英雄】，-1全军
                        'cond':[['army',2],['checkBuff',0,-5,2,'>',0],['rnd',200]],
                        'binding':{
                           'removeDebuff':1,
                        },
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能，不受傲气
                        'follow':'skill272',            #必须跟随特定行动触发
                        'lv':13,
                        'info':['skill272h',1],
                      },
                    ],
                  },
               },
            },
         }]
      },
      'act':[
         {
            'priority':5500,
            'type': 10,	    #正式版配10     
            'src': -1,      #发出源 0前军，1后军，2英雄，-1全军，-2全军含英雄
            'tgt':[0, -5],           #大目标 0自己1敌方-1全体      ;小目标 -5同一军
            'round':{'far':1000},
            'noBfr': 1,
            'noAft': 1, 
            'nonSkill':1,    #非技能，不受傲气
            'cureReal':15,         #治疗伤兵数量
            'info':['skill272',1],	          #[0]应该引用文字配置，[1]为显示模式，0只用于打印，1word，2banner，3fate。[2]由程序自动补足等级
            'eff':'eff272',
            'time':100,           #强制战报时间
         },
         {
            'priority':9800,
            'type': 8,	    #触发类型，8战胜
            'src': 2,      #发出源 0前军，1后军，2英雄，-1全军，-2全军含英雄
            'tgt':[0, -6],           #大目标 0自己1敌方-1全体      ;小目标 -6不计死亡的前后军
            'noBfr': 1,
            'noAft': 1, 
            'nonSkill':1,    #非技能，不受傲气
            'cureReal':1,         #治疗伤兵数量
            'cure':100,           #治疗伤兵千分点
            'info':['skill272',1],	          #[0]应该引用文字配置，[1]为显示模式，0只用于打印，1word，2banner，3fate。[2]由程序自动补足等级
            'eff':'eff272',
            'time':100,           #强制战报时间
         },
      ],
   },
   'skill273':{   #无懈
      'infoArr':['%|act[0].round.any','%|act[0].round.lv>'],
      'nextArr':['%|act[0].round.any'],
      'highArr':['%|500'],
      'state':23,                                      #最早可见天数   
      #'open_date':datetime.datetime(2019,2,7,5,0),   #最早可见日期
      'index':571,
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      #'icon':'item084',
      'ability_info':['lead'],
      'limit':{'lead':98},
      'isAssist':1,
      'passive':[
         {
            'rslt':{'power':250},
         },
         {
            'cond':['skill.skill273', '*', 0],
            'rslt':{'powerRate':20},
         },
      ],
      'up':{ 'act[0].round.any':15},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].round.any':-10 },
         '27':{  'act[0].round.any':-20 },
         '28':{  'act[0].round.any':-30 },
         '29':{  'act[0].round.any':-40 },
         '30':{  'act[0].round.any':-50 },
      },
      'high':{
         'lv':13,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':25},
            },
         ],
         'special':[{
            'priority':-9700.273,
            'cond':[['rnd', 500]],
            'change':{
               'skill':{
                  'skill273.act[0].allTimes':1,
               },
            },
         }]
      },
      'act':[
         {
            'priority':9700,
            'type': 7,	    #对方英雄技
            'src': 2,      #发出源 0前军，1后军，2英雄，-1全军，-2全军含英雄
            'tgt':[0, 2],    
            'round':{'any':110, 'lv>':200},  
            'times': -1,    #每回合最多使用次数
            'allTimes': 1,
            #'srcStop': 0,   #源技能未被阻止时，本技能才能触发
            'stop':1,
            #'follow':'all',
            'unFollow':{'attach':{'hero778r':1,'hero798':1}},
            'nonSkill':1,
            'noBfr':1,
            'noAft':1,
            'eff':'eff273',
            'info':['skill273',2],	          #[0]应该引用文字配置，[1]为显示模式，0只用于打印，1word，2banner，3fate。[2]由程序自动补足等级
         },
      ],
   },


   'skill275':{   #遁甲   三奇六仪，分置九宫。每次英雄技攻击后，有35%（满59%）概率发动【遁甲】，对敌前军或后军造成无视防御的随机伤害，伤害率10%~100%（满34%~220%）。每回合最多发动1次
      'infoArr':[
         '%|act[0].round.any',
         '%|act[0].dmg',
         '%|act[0].dmg+act[0].dmgRnd',
      ],
      'nextArr':[
         '%|act[0].round.any',
         '%|act[0].dmg',
         '%|act[0].dmg+act[0].dmgRnd',
      ],
      'highArr':['%|high.special[0].change.skill.$skill275.act[0].buff.buffFaction.rnd','-|act[0].buff.buffFaction.round'],
      'index':572,
      'merge':2,   #几次合服后可见
      'state':1,   #开服几天后显示   
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['agi'],
      'limit':{'agi':98},
      'isAssist':1,
      'passive':[
         {
            'rslt':{'power':250},
         },
         {
            'cond':['skill.skill275', '*', 0],
            'rslt':{'powerRate':20},
         },
      ],
      'up':{'act[0].round.any':10, 'act[0].dmg':10, 'act[0].dmgRnd':40, 'act[0].dmgReal':1, 'act[0].dmgRealRnd':4},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].round.any':-5 },
         '27':{  'act[0].round.any':-10 },
         '28':{  'act[0].round.any':-15 },
         '29':{  'act[0].round.any':-25 },
         '30':{  'act[0].round.any':-30 },
      },

      'high':{
         'lv':17,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':25},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill275.act[0].buff.buffFaction.rnd':200,   #内讧
               },
            },
         }]
      },
      'act':[
         {
            'priority':4100,
            'type': 3,             #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 2,             #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[1, -2],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
            'round':{'any':350},
            'times': 1,
            'nonSkill':1,    #非技能，不受傲气和特殊加成影响
            'noBfr': 1,    #本次行动不能触发前置绑定效果
            'noAft': 1,    #本次行动不能触发后置绑定效果
            'dmg':100,	  
            'dmgRnd':900,
            'dmgReal':20,
            'dmgRealRnd':200,
            'dmgLimit':120,
            'ignDef':1000,

            'atk0': 400,     
            'atk1': 450,     
            'buff':{'buffFaction':{'rnd':0, 'round':1}},

            'eff':'eff275',
            'info':['skill275',2],           #应该引用文字配置
         },
      ],
   },

   'skill278':{   #洞鉴   三合
      'infoArr':[
         '%|act[0].buff.buff278.shield.value*5',
         '*|act[0].buff.buff278.prop.dmgRate',
         '-|act[0].cost',
      ],
      'nextArr':[
         '%|act[0].buff.buff278.shield.value*5',
         '*|act[0].buff.buff278.prop.dmgRate',
      ],
      'highArr':[
         '*|high.special[0].change.skill.$skill278.act[0].buff.buff278.prop.resRate',
         '*|high.special[0].change.skill.$skill278.act[0].buff.buff278.prop.block*0.5',
      ],
      'index':573,
      'merge':3,   #几次合服后可见
      'state':1,   #开服几天后显示   
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['str','agi','sum'],
      'limit':{'str':95,'agi':95},
      'isAssist':1,
      'passive':[
         {
            'rslt':{'power':250},
         },
         {
            'cond':['skill.skill278', '*', 0],
            'rslt':{'powerRate':20},
         },
      ],
      'up':{
          'act[0].buff.buff278.shield.value':5,
          'act[0].buff.buff278.shield.hpmRate':0.5,
          'act[0].buff.buff278.prop.dmgRate':10,
          'act[0].buff.buff278.prop.defRate':-1,
      },
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].buff.buff278.prop.dmgRate':-2, },
         '27':{  'act[0].buff.buff278.prop.dmgRate':-4, },
         '28':{  'act[0].buff.buff278.prop.dmgRate':-6, },
         '29':{  'act[0].buff.buff278.prop.dmgRate':-8, },
         '30':{  'act[0].buff.buff278.prop.dmgRate':-10, },
      },

      'high':{   #若四维总数高于对手，【洞鉴】护盾存在期间，我军免伤{0}，格挡率{1}
         'lv':17,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':100},
            },
         ],
         'special':[{
            'cond':[['compare','sum','>']],
            'change':{
               'skill':{
                  'skill278.act[0].buff.buff278.prop.resRate':100,
                  'skill278.act[0].buff.buff278.prop.block':800,
               },
            },
         }]
      },
      'act':[   #静观其变，稳操胜券。我方每累计受到{2}次伤害，立即发动一次【洞鉴】，使我军全军获得强度为{0}的护盾，且护盾存在期间我军伤害{1}
         {
            'priority':88278,
            'type': 17,             #触发类型，17充能后
            'src': -1,
            'srcFree': 1, 
            'tgt':[0, -1],         
            #'follow':{'attach':'skill278_0'},            #跟随附加类行动触发
            'times': -1,    #每回合最多使用次数
            'nonSkill':1,    #非技能，不受傲气
            'noBfr': 1,
            'noAft': 1,

            'cond':[[['selfArmy',0],['selfArmy',1]]],      #需前军或后军存活

            'energyKeySrc': 'energy278',
            'costKey': 'energy278',
            #'cost': 2,
            'cost': 9,
            #'buff':{'buff279':{'shield':{'value':2,'hpLimitPoint':10,'bearPoint':1000,'info':['skill279',1]}}},  #无法造成超过1%的伤害
            'buff':{'buff278':{'shield':{'value':60,'hpmRate':10,'bearPoint':1000},   'prop':{'dmgRate':300,'defRate':-30}}},
            'eff':'eff278',
            'info':['skill278',2],	          #应该引用文字配置
         },
         #{
           #'priority':9416,
           #'type': 4,             #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            #'src': -1,             #发出源 0前军，1后军，【2英雄】，-1全军
            #'times': -1,
            #'nonSkill':1,    #非技能，不受傲气和特殊加成影响
            #'binding':{
            #     'energyKey':'energy278',
            #     'energy':1,
            #},
            #'follow':{'keys':{'dmg':1000,'dmgReal':1000,'dmgRealMax':1000,'dmgRealRate':1000}},
            #'actId':'skill278_0',  
            #'info':['洞鉴储能',0],           #应该引用文字配置
         #},
         {
            'priority':9416,
            'type': 23,            #兵力变化
            'src': -1,             #发出源 0前军，1后军，【2英雄】，-1全军
            'tgt':[0, -7],         #不计死亡的英雄
            'times': -1,
            'nonSkill':1,
            'noBfr':1,    
            'noAft':1,  
            'condHpChange':[['<',0],None],   #兵力受损后
            'energyKey':'energy278',
            'energy':1,
            'time':0,
            'eff':'effNull',
            'info':['洞鉴储能：新',0],           #应该引用文字配置     #应该引用文字配置
         },
      ],
   },


   'skill254':{   #凶煞
      'infoArr':['*|act[0].binding.crit/2','*|act[0].binding.critRate*1.5'],
      'nextArr':['*|act[0].binding.crit/2','*|act[0].binding.critRate*1.5'],
      'highArr':['%|0-300'],
      'index':574,
      'merge':4,
      'state':1,   #开服几天后显示
      'open_date':datetime.datetime(2021,12,3,5,0),   #最早可见日期
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['str'],
      'limit':{'str':98},
      #'isAssist':1,
      'passive':[
         {
            'rslt':{'power':250},
         },
         {
            'cond':['skill.skill254', '*', 0],
            'rslt':{'powerRate':20},
         },
      ],
      'up':{ 'act[0].binding.crit':10, 'act[0].binding.critRate':10, 'act[0].binding.critAdd':20},
      'high':{
         'lv':17,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':100},
            },
         ],
         'special':[{
            'changeEnemy':{
               'prop':{
                  'armys[0].others.elementShield':-300,  #原值-300
                  'armys[1].others.elementShield':-300,
                  #'armys[0].others.elementCure':-300,
                  #'armys[1].others.elementCure':-300,
                  'armys[0].others.elementSummon':-300,
                  'armys[1].others.elementSummon':-300,
               },
            },
         }],
      },
      'act':[
         {
            'priority':9425,
            'type': 2,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': -2,
            'times': -1,
            'nonSkill':1,
            'cond':[['enemyHpPoint','<',750]],
            'binding':{
               'crit':200,   
               'critRate':50,    
               'critAdd':100,    
            },
            'info':['skill254',0],
         },
      ],
   },



   'skill279':{   #退避   三合
      'infoArr':[
         '%|act[0].buff.buff279.shield.bearPoint+50',
         '%|act[0].buff.buff279.shield.hpLimitPoint',
      ],
      'nextArr':[
         '%|act[0].buff.buff279.shield.bearPoint+50',
      ],
      'highArr':[
         '%|high.special[0].change.skill.skill279h.act[0].buff.buff279.shield.bearPoint+50',
         '%|high.special[0].change.skill.skill279h.act[0].buff.buff279.shield.hpLimitPoint',
      ],
      'index':564,
      'merge':3,   #几次合服后可见
      'state':1,   #开服几天后显示   
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['cha'],
      'limit':{'cha':95},
      #'isAssist':1,
      'passive':[
         {
            'rslt':{'power':200},
         },
         {
            'cond':['skill.skill279', '*', 0],
            'rslt':{'powerRate':18},
         },
      ],
      'up':{'act[0].buff.buff279.shield.bearPoint':15,   'act[0].buff.buff279.shield.info.2':1},
      'lvPatch':{   #满足特定等级的补丁
         '26':{  'act[0].buff.buff279.shield.bearPoint':-5 },
         '27':{  'act[0].buff.buff279.shield.bearPoint':-10 },
         '28':{  'act[0].buff.buff279.shield.bearPoint':-15 },
         '29':{  'act[0].buff.buff279.shield.bearPoint':-20 },
         '30':{  'act[0].buff.buff279.shield.bearPoint':-25 },
      },

      'high':{   #近战回合前，若我方后军单次承受的伤害，超出其最大兵力的5%，超出部分将被减免50%
         'lv':15,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':80},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill279h':{
                     'act':[
                      {
                        'priority':9000022,
                        'type': 13,             #触发类型，13大回合重置
                        'src': 1,
                        'tgt':[0, 1],   
                        'round':{'0':1000},      
                        'nonSkill':1,    #非技能，不受傲气
                        'noBfr': 1,
                        'noAft': 1,
                        #造成超出10%的伤害时，超出部分被缩减
                        'buff':{'buff279':{'round':3, 'shield':{'value':999,'hpLimitPoint':100,'bearPoint':300,'info':['skill279',1,15]}}},
                        'eff':'effNull',
                        'time': 0,
                        'info':['skill279',0],	          #应该引用文字配置
                      },
                    ],
                  },
               },
            },
         }]
      },
      'act':[   #暂避其峰，挫敌锐气。近战回合前，若我方前军单次承受的伤害，超出其最大兵力的5%，超出部分将被减免50%
         {
            'priority':9000023,
            'type': 13,             #触发类型，13大回合重置
            'src': 0,
            'tgt':[0, 0],   
            'round':{'0':1000},      
            'nonSkill':1,    #非技能，不受傲气
            'noBfr': 1,
            'noAft': 1,
            'isAssist':0,
            #造成超出10%的伤害时，超出部分被缩减
            'buff':{'buff279':{'round':3, 'shield':{'value':999,'hpLimitPoint':100,'bearPoint':140,'info':['skill279',1,1]}}},  
            'eff':'effNull',
            'time': 0,
            'info':['skill279',0],	          #应该引用文字配置
         },
      ],
   },

   'skill974':{   #折冲   6合
      'infoArr':[
         '%|act[0].round.any',
      ],
      'nextArr':[
         '%|act[0].round.any',
      ],
      'highArr':[],
      'index':575,
      'merge':6,   #几次合服后可见
      'state':1,   #开服几天后显示   
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':[],
      'limit':{},
      'isAssist':1,
      'passive':[
         {
            'rslt':{'power':200},
         },
         {
            'cond':['skill.skill974', '*', 0],
            'rslt':{'powerRate':18},
         },
      ],
      'lvPatch':{   #满足特定等级的补丁
         '28':{ 'act[0].round.any':-5 },
         '29':{ 'act[0].round.any':-5 },
         '30':{ 'act[0].round.any':-6 },
      },
      'up':{'act[0].round.any':14},
      'high':{   #近战回合兵种攻击+5%，伤害+5%
         'lv':17,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':80},
            },
         ],
         'special':[{
            'change':{
              'skill':{
                 'skill974h':{
                   'act':[{
                      'priority': 9000024,
                      'type': 13,             #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
                      'src': -1,             #发出源 0前军，1后军，【2英雄】，-1全军
                      'tgt':[0, -5],           #大目标 0自己1敌方-1全体      ;小目标 0前军1后军2英雄 -1全体 -2任一军 -3另一军（执行时自动按情况变化）
                      'round':{'3':1000},
                      'limitTimes': 1,
                      'buff':{'skill974h':{}},
                      'time':0,           #强制指定战报时间
                      'nonSkill':1,    #非技能，不受傲气和特殊加成影响
                      'noBfr':1,
                      'noAft':1,
                      'eff':'effNull',
                      'info':['折冲近攻',0],
                   }],
                 },
              },
            },
         }],
      },
      'act':[   #我方任意兵种主动技首次发动时，都有{0}的几率触发【折冲】，使此兵种技于[2]回合后再次发动
         {
            'priority':88279,
            'type': 2,
            'src': -1, 

            'round':{'any':200},
            'follow':{
               'limitTimes':{    #以下行动中，仅已发动次数少于设定的，才可被follow
                 'skill289':1,'skill204':1,'skill206':1,'skill977':1,
                 'skill209':1,'skill210':1,'skill211':1,'skill978':1,
                 'skill216':1,'skill218':1,'skill291':1,'skill979':1, 
                 'skill222':1,'skill224':1,'skill292':1,'skill980':1,
               },
            },     
            'times': -1,
            'nonSkill':1,
            'binding':{
                'refreshSkill':{
                   'srcStop':0,           #0仅未阻止时可触发，1仅被阻止时可触发，默认-1无论阻止与否都可触发
                   'actId':'selfAct',     #刷新自身的本行动
                   'times':1,       #刷新行动的次数
                   'delayRound':2   #延迟2回合
                }
            },
            'info':['skill974',1],
         },
      ],
   },


   'skill975':{   #重生   6合
      'infoArr':[
         '%|act[0].round.any',
         '%|act[0].summon',
      ],
      'nextArr':[
         '%|act[0].round.any',
      ],
      'highArr':[
         '%|act[0].buff.buff975.prop.atkRate*2',
      ],
      'index':576,
      'merge':6,   #几次合服后可见
      'state':1,   #开服几天后显示   
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':[],
      'limit':{},
      'isAssist':1,
      'passive':[
         {
            'rslt':{'power':200},
         },
         {
            'cond':['skill.skill975', '*', 0],
            'rslt':{'powerRate':18},
         },
      ],
      'lvPatch':{   #满足特定等级的补丁
         '30':{  'act[0].round.any':7,  'act[1].round.any':7 },
      },
      'up':{'act[0].round.any':17,  'act[1].round.any':17},
      'high':{   #部队复活后，自身额外提升20%的攻击
         'lv':17,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':80},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill975.act[0].buff.buff975.rnd':1000,
                  'skill975.act[1].buff.buff975.rnd':1000,
               },
            },
         }]
      },
      'act':[   #我方任意部队受到伤害导致全灭时，有25%（75%）几率复活，恢复自身30%的最大兵力，并驱散所有增益和不良状态（每场对决限成功发动一次重生）
         {
            'priority':88279,
            'type': 23,
            'src': 0,
            'srcFree': 1,
            'tgt':[0, -9],    
            'round':{'any':250},
            'condHpChange':[['<',0], ['=',0]],
            'noBfr': 1,
            'noAft': 1,
            'removeBuff':100,
            'removeDebuff':100,
            'summonReal':10,
            'summon':300, 
            'buff':{'buff975':{'rnd':0, 'prop':{'atkRate':100}}},
            'unFollow':{'hero725r':1},
            'limitTimes': 1,
            'actId':'skill975',
            'eff':'eff975',
            'info':['skill975',1],
         },
         {
            'priority':88280,
            'type': 23,
            'src': 1,
            'srcFree': 1,
            'tgt':[0, -10],    
            'round':{'any':250},
            'condHpChange':[['<',0], ['=',0]],
            'noBfr': 1,
            'noAft': 1,
            'removeBuff':100,
            'removeDebuff':100,
            'summonReal':10,
            'summon':300, 
            'buff':{'buff975':{'rnd':0, 'prop':{'atkRate':100}}},
            'unFollow':{'hero725r':1},
            'limitTimes': 1,
            'actId':'skill975',
            'eff':'eff975',
            'info':['skill975',1],
         },
      ],
   },

   'skill976':{   #鹰视   6合
      'infoArr':[
         '*|act[0].buff.buff976.prop.{others.skillPoint__type_army_A}',
         '%|act[1].buff.buff976_1.prop.{others.skillPoint__type_army_A}',
      ],
      'nextArr':[
         '*|act[0].buff.buff976.prop.{others.skillPoint__type_army_A}',
         '%|act[1].buff.buff976_1.prop.{others.skillPoint__type_army_A}',
      ],
      'highArr':[
         '%|high.special[0].change.skill.skill976h.act[0].buff.buff976_2.prop.{others.skillPoint__type_army_A}',
         '%|high.special[0].change.skill.skill976h.act[1].buff.buff976_3.prop.{others.skillPoint__type_army_A}',
      ],
      'index':577,
      'merge':6,   #几次合服后可见
      'state':1,   #开服几天后显示   
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':[],
      'limit':{},
      #'isAssist':1,
      'passive':[
         {
            'rslt':{'power':200},
         },
         {
            'cond':['skill.skill976', '*', 0],
            'rslt':{'powerRate':18},
         },
      ],
      'lvPatch':{   #满足特定等级的补丁
         '28':{  'act[0].buff.buff976.prop.{others.skillPoint__type_army_A}':4, 'act[1].buff.buff976_1.prop.{others.skillPoint__type_army_A}':-6  },
         '29':{  'act[0].buff.buff976.prop.{others.skillPoint__type_army_A}':4, 'act[1].buff.buff976_1.prop.{others.skillPoint__type_army_A}':-6  },
         '30':{  'act[0].buff.buff976.prop.{others.skillPoint__type_army_A}':4, 'act[1].buff.buff976_1.prop.{others.skillPoint__type_army_A}':-6  },
      },
      'up':{'act[0].buff.buff976.prop.{others.skillPoint__type_army_A}':4,  'act[1].buff.buff976_1.prop.{others.skillPoint__type_army_A}':-6},
      'high':{   #远战第二回合，敌方兵种主动技发动率额外-6%，  近战回合，敌方兵种主动技发动率额外-12%
         'lv':17,
         'passive':[
            {
               'rslt':{'power':800,'powerRate':80},
            },
         ],
         'special':[{
            'change':{
               'skill':{
                  'skill976h':{
                     'act':[
                      {
                        'priority':9000027,
                        'type': 13,
                        'src': 2,
                        'tgt':[1, -1],   
                        'round':{'2':1000},
                        'noBfr': 1,
                        'noAft': 1,
                        'nonSkill':1,
                        'allTimes': 1,
                        'buff':{'buff976_2':{'round':1,'prop':{'others.skillPoint__type_army_A':-60}}},
                        'eff':'effNull',
                        'time':0,
                        'info':['上3',0],
                      },
                      {
                        'priority':9000028,
                        'type': 13,
                        'src': 2,
                        'tgt':[1, -1],   
                        'round':{'3':1000},
                        'noBfr': 1,
                        'noAft': 1,
                        'nonSkill':1,
                        'allTimes': 1,
                        'buff':{'buff976_3':{'round':1,'prop':{'others.skillPoint__type_army_A':-120}}},
                        'eff':'effNull',
                        'time':0,
                        'info':['上4',0],
                      },
                    ],
                  },
               },
            },
         }]
      },
      'act':[   #我方兵种主动技发动率+6%（18%），敌方兵种主动技发动率-9%（27%）
         {
            'priority':9000025,
            'type': 13,
            'src': 2,
            'tgt':[0, -1],   
            'round':{'0':1000},
            'noBfr': 1,
            'noAft': 1,
            'nonSkill':1,
            'allTimes': 1,
            'buff':{'buff976':{'prop':{'others.skillPoint__type_army_A':60}}},
            'eff':'effNull',
            'time':0,
            'info':['上1',0],
         },
         {
            'priority':9000026,
            'type': 13,
            'src': 2,
            'tgt':[1, -1],   
            'round':{'0':1000},
            'noBfr': 1,
            'noAft': 1,
            'nonSkill':1,
            'allTimes': 1,
            'buff':{'buff976_1':{'prop':{'others.skillPoint__type_army_A':-90}}},
            'eff':'effNull',
            'time':0,
            'info':['上2',0],
         },
      ],
   },


   'skill981':{   #戒备   7合（弃用）
      'infoArr':[
         '%1|act[0].round.any',
      ],
      'nextArr':[
         '%1|act[0].round.any',
      ],
      'highArr':[],
      'unlockArr':[],
      'index':1578,
      'merge':10,   #几次合服后可见
      'state':1,   #开服几天后显示   
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['cha','lead'],
      'limit':{},
      #'isAssist':1,
      'passive':[
         {
            'rslt':{'power':200},
         },
         {
            'cond':['skill.skill981', '*', 0],
            'rslt':{'powerRate':18},
         },
      ],
      'up':{'act[0].round.any':17.24,  'act[1].round.any':17.24},
      'unlock':[
        {
         'lv':10,
         'passive':[   #若魅力高于对手，我方触发【戒备】时额外减免20%效果
            {
                'special':[{   
                    'priority':-888.9810,   
                    'cond':[['compare','cha','>']],
                    'change':{  
                        'skill':{
                             'skill981.act[0].binding.resRealRate':200,
                             'skill981.act[1].binding.resRealRate':200,
                             'skill981.act[2].binding.resRealRate':200,
                             'skill981.act[3].binding.resRealRate':200,
                          },
                     },
               }],
            },
         ],
        },
        {
         'lv':17,
         'passive':[
            {
               'rslt':{'power':500,'powerRate':80},
            },
         ],
         'special':[{  #若统御高于对手，我方后军受到按比例损失兵力的效果时，也有50%几率触发【戒备】
            'priority':-888.9810,   
            'cond':[['compare','lead','>']],
            'change':{
               'skill':{
                   'skill981.act[2].round.any':500,
                   'skill981.act[3].round.any':500,
               },
            },
         }]
        },
      ],
      'act':[   #我方前军受到按比例损失兵力的效果时，有25%（75%）几率触发【戒备】，减免40%效果
         {
            'priority':88981.101, 
            'type': 4,             #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 0,             #发出源 0前军，1后军，【2英雄】，-1全军
            'follow':{'keys':{'dmgRealMax':1000,'dmgRealRate':1000,'dmgRealLostRnd':1000,'dmgRealHpm0':1000,'dmgRealHp0':1000}},           
            'times':-1,  
            'round':{'any':250},
            'nonSkill':1,    #非技能，不受傲气和特殊加成影响
            'binding':{
               'resRealRate':400,      #减免本次伤害
            },
            'info':['skill981',1],           #应该引用文字配置
         },
         {
            'priority':88981.100, 
            'type': 2,             #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 0,             #发出源 0前军，1后军，【2英雄】，-1全军
            'follow':{'keys':{'dmgRealMax':1000,'dmgRealRate':1000,'dmgRealLostRnd':1000,'dmgRealHpm0':1000,'dmgRealHp0':1000}},        
            'times':-1,    
            'round':{'any':250},
            'nonSkill':1,    #非技能，不受傲气和特殊加成影响
            'binding':{
               'resRealRate':400,      #减免本次伤害
            },
            'info':['skill981',1],           #应该引用文字配置
         },

         {
            'priority':88981.103, 
            'type': 4,             #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 1,             #发出源 0前军，1后军，【2英雄】，-1全军
            'follow':{'keys':{'dmgRealMax':1000,'dmgRealRate':1000,'dmgRealLostRnd':1000,'dmgRealHpm1':1000,'dmgRealHp1':1000}},           
            'times':-1,  
            'round':{'any':0},
            'nonSkill':1,    #非技能，不受傲气和特殊加成影响
            'binding':{
               'resRealRate':400,      #减免本次伤害
            },
            'info':['skill981',1],           #应该引用文字配置
         },
         {
            'priority':88981.102, 
            'type': 2,             #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 1,             #发出源 0前军，1后军，【2英雄】，-1全军
            'follow':{'keys':{'dmgRealMax':1000,'dmgRealRate':1000,'dmgRealLostRnd':1000,'dmgRealHpm1':1000,'dmgRealHp1':1000}},           
            'round':{'any':0},
            'times':-1,    
            'nonSkill':1,    #非技能，不受傲气和特殊加成影响
            'binding':{
               'resRealRate':400,      #减免本次伤害
            },
            'info':['skill981',1],           #应该引用文字配置
         },
      ],
   },


   'skill982':{   #复仇   7合
      'infoArr':[
         '%|act[1].mult.dmgReal*1000',
         '%|act[1].mult.dmgReal*2*1000',
      ],
      'nextArr':[
         '%|act[1].mult.dmgReal*1000',
         '%|act[1].mult.dmgReal*2*1000',
      ],
      'highArr':[],
      'unlockArr':[],
      'index':579,
      'merge':7,   #几次合服后可见
      'state':1,   #开服几天后显示   
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['str'],
      'limit':{},
      'isAssist':1,
      'passive':[
         {
            'rslt':{'power':200},
         },
         {
            'cond':['skill.skill982', '*', 0],
            'rslt':{'powerRate':18},
         },
      ],
      'up':{'act[1].mult.dmgReal':0.00413},
      'unlock':[
        {
         'lv':10,
         'passive':[   #若武力高于对手，【复仇】有50%的几率将目标改为敌方全军
            {
                'special':[{    
                    'change':{  
                       'skill':{
                         'skill982h_0':{
                           'act':[{
                             'priority': -8889820, 
                             'type': 2,
                             'src': 2,
                             'cond':[['rnd',500],['comp','str']],
                             'binding':{'tgt':[1, -1]},
                             'times': -1,
                             'nonSkill':1,
                             'follow':'skill982',
                             'info':['改全体',0],
                           }],
                         },
                       },
                    },
               }],
            },
         ],
        },
        {
         'lv':17,
         'passive':[
            {
               'rslt':{'power':500,'powerRate':80},
            },
         ],
         'special':[{  #远战回合，敌方暴击率-50%
            'changeEnemy':{
               'skill':{
                  'skill982h':{
                    'act':[{
                        'priority': 8889202,	
                        'type': 2,
                        'src': -4,
                        'round':{'far':1000},  
                        'binding':{'crit':-650},
                        'times': -1,    #每回合最多使用次数
                        'nonSkill':1,    #非技能
                        'follow':{'keys':{'dmg':1000,'dmgReal':1000,'dmgRealMax':1000}},
                        'info':['skill982h',0],
                    }],
                  },
               },
            },
         }]
        },
      ],
      'act':[   #近战每个回合结束时发动【复仇】，将本回合我方战损兵力的6%（18%）~12%（36%）反噬给敌方前军
       {
            'priority':-88888.9821,
            'type': 4,               #防前            
            'src': -1,
            'round':{'near':1000},  
            'times':-1,
            'nonSkill':1,   
            'isAssist':0, 
            'binding':{
               'energys':{
                  'S982':{     #部队每次伤亡
                     'condE':['casualty','*',0],
                     'tgtE':{
                        'dmg982':{'num':1,'numRnd':1,'checkAct':1},
                     },
                  },
               },
            },
            'follow':{'keys':{'dmg':1000,'dmgReal':1000,'dmgRealRate':1000,'dmgRealMax':1000,'dmgRealLost':1000,'dmgGod':1000,'dmgPuppet':1000,'dmgRealPower':1000}},
            'info':['受击充能',0],
       },
       {
            'priority':-88888888.9820,
            'type': 27,
            'src': 2,
            'tgt':[1, 0],
            'round':{'near':1000},  

            'nonSkill':1,
            'noBfr': 1,
            'noAft': 1, 
            'noBuff':1,
            'times':1,
            'allTimes':-1,

            'costKey':'dmg982',
            'cost':1,
            'costMult':1,
            'multMax':-1,
            'mult':{'dmgReal':0.06},     #多重施法时提升内容
            'element':'Anti',
            'dmgReal':0.06,

            'lv':25,
            'eff':'eff982',
            'info':['skill982',2],
       },
      ],
   },



   'skill983':{   #细作   7合
      'infoArr':[
         '%|act[0].buff.buffSpy.prop.defRate*2',
      ],
      'nextArr':[
         '%|act[0].buff.buffSpy.prop.defRate*2',
      ],
      'highArr':[],
      'unlockArr':[],
      'index':580,
      'merge':7,   #几次合服后可见
      'state':1,   #开服几天后显示   
      'type':5,
      'cost_type':2,
      'max_level':25,
      'shogun_type':2,
      'ability_info':['agi'],
      'limit':{},
      #'isAssist':1,
      'passive':[
         {
            'rslt':{'power':200},
         },
         {
            'cond':['skill.skill983', '*', 0],
            'rslt':{'powerRate':18},
         },
      ],
      'up':{'act[0].buff.buffSpy.prop.defRate':-1.03,'act[1].buff.buffSpy.prop.defRate':-1.03},
      'unlock':[
        {
         'lv':10,
         'passive':[   #若智力高于对手，敌方部队的每层【细作】额外使攻防-3%
            {
                'special':[{   
                    'priority':-888.9830,   
                    'cond':[['compare','agi','>']],
                    'change':{  
                        'skill':{
                             'skill983.act[0].buff.buffSpy.prop.atkRate':-15,
                             'skill983.act[0].buff.buffSpy.prop.defRate':-15,
                             'skill983.act[1].buff.buffSpy.prop.atkRate':-15,
                             'skill983.act[1].buff.buffSpy.prop.defRate':-15,
                          },
                     },
               }],
            },
         ],
        },
        {
         'lv':17,
         'passive':[
            {
               'rslt':{'power':500,'powerRate':80},
            },
         ],
         'special':[{  #敌方部队的【细作】效果叠加至5层和10层时，可触发【谣变】，导致自身【内讧】1回合
            'priority':-8888.9831,   
            'change':{
               'skill':{
                  'skill983.act[2].round.any':1000,
                  'skill983.act[3].round.any':1000,
               },
            },
         }]
        },
      ],
      'act':[   #敌方部队每获得1次护盾、治疗、增援兵力时，自身叠加1层【细作】的特殊效果，每层防御-3%（满级-9%）
       {
            'priority':-888.9831,
            'enemy': 1,    
            'type': 20,    
            'src': -1,
            'tgt':[0, -5],
            'condBuffChange':{
                'shield':{'C':1,'A':1},    #对护盾类型生效  A抵次B滤超C抵伤
            },
            'atOnce': -1,
            'times': -1,
            'buff':{'buffSpy':{'stack':1,'prop':{'defRate':-15}}}, 
            'nonSkill':1,
            'noBfr':1,
            'noAft':1,
            'lv':-100,
            'eff':'eff983',
            #'time':0,
            'info':['skill983',1],
       },
       {
            'priority':-888.9832,
            'enemy': 1,    
            'type': 23,             #兵力变化
            'src': -1,
            'tgt':[0, -5],
            'atOnce': -1,
            'times': -1,
            'nonSkill':1,
            'noBfr': 1,
            'noAft': 1,
            'buff':{'buffSpy':{'stack':1,'prop':{'defRate':-15}}}, 
            'condHpChange':[['>',0],None],
            'eff':'eff983',
            'lv':-100,
            #'time':100,
            'info':['skill983',1],
       },
       {
            'priority':-888.9833,
            'enemy': 1,
            'type': 20,       
            'src': 0,   
            'tgt':[0, 0],   
            'round':{'any':0},
            'cond':[[['checkBuff', 0, 0, 'buffSpy', '=', 5], ['checkBuff', 0, 0, 'buffSpy', '=', 10]]],
            'condBuffChange':{
                'buffSpy':1,      
            },
            'nonSkill':1,
            'noBfr':1,
            'noAft':1,
            'times':-1,
            'end':1,      #谣变时，若为自身轮次中，结束自身轮次
            'buff':{'buffFaction':{'round':1}},  
            'atOnce':-100,
            'eff':'eff983_1',
            #'time':0,
            'info':['skill983_1',1],
       },
       {
            'priority':-888.9834,
            'enemy': 1,
            'type': 20,       
            'src': 1,   
            'tgt':[0, 1],  
            'round':{'any':0}, 
            'cond':[[['checkBuff', 0, 1, 'buffSpy', '=', 5], ['checkBuff', 0, 1, 'buffSpy', '=', 10]]],
            'condBuffChange':{
                'buffSpy':1,      
            },
            'nonSkill':1,
            'noBfr':1,
            'noAft':1,
            'times':-1,
            'end':1,      #谣变时，若为自身轮次中，结束自身轮次
            'buff':{'buffFaction':{'round':1}},  
            'atOnce':-100,
            'eff':'eff983_1',
            #'time':0,
            'info':['skill983_1',1],
       },
],
   },



   'skill281':{   #筑城
      'infoArr':['e|city_build'],
      'nextArr':['e|city_build'],
      'index':653,
      'type':6,
      'cost_type':4,
      'max_level':20,
      'shogun_type':4,
      'ability_info':[],
      #'limit':{},
      'city_build':[0.05,0.20],#增加（ax+b）%的速度，x为技能等级
   },
   'skill282':{   #行军
      'infoArr':['e|army_go'],
      'nextArr':['e|army_go'],
      'index':654,
      'type':6,
      'cost_type':4,
      'max_level':20,
      'shogun_type':4,
      'ability_info':[],
      #'limit':{},
      'army_go':[0.01,0.05],#增加（ax+b）%的速度，x为技能等级
   },
   'skill283':{   #富豪
      'infoArr':['e|estate_active[2]'],
      'nextArr':['e|estate_active[2]'],
      'index':655,
      'type':6,
      'cost_type':4,
      'max_level':20,
      'shogun_type':4,
      'ability_info':[],
      #'limit':{},
      'estate_active':['gold',['1','2'],[0.02,0.05]],
      #数据结构 所有相同的百分比倍数先相加，再+1乘以基础值
      #如果为[a]则直接增加a%的倍数
      #如果为[a,b]则公式为(ax+b)%的倍数，x为技能等级或者科技等级
      #如果为[a,b,c,d] 则公式为ax+b的几率在c到d中随机一个数量获得，x为技能等级
   },
   'skill284':{   #农耕
      'infoArr':['e|estate_active[2]'],
      'nextArr':['e|estate_active[2]'],
      'index':656,
      'type':6,
      'cost_type':4,
      'max_level':20,
      'shogun_type':4,
      'ability_info':[],
      #'limit':{},
      'estate_active':['food',['3'],[0.02,0.05]],
   },
   'skill285':{   #寻矿
      'infoArr':['e|estate_active[2]'],
      'nextArr':['e|estate_active[2]'],
      'index':657,
      'type':6,
      'cost_type':4,
      'max_level':20,
      'shogun_type':4,
      'ability_info':[],
      #'limit':{},
      'estate_active':['iron',['5'],[0.02,0.05]],
   },
   'skill286':{   #育林
      'infoArr':['e|estate_active[2]'],
      'nextArr':['e|estate_active[2]'],
      'index':658,
      'type':6,
      'cost_type':4,
      'max_level':20,
      'shogun_type':4,
      'ability_info':[],
      #'limit':{},
      'estate_active':['wood',['4'],[0.02,0.05]],
   },
   'skill287':{   #口才
      'infoArr':['e|hero_visit','-|hero_visit[2]','-|hero_visit[3]'],
      'nextArr':['e|hero_visit'],
      'index':659,
      'type':6,
      'cost_type':4,
      'max_level':20,
      'shogun_type':4,
      'ability_info':[],
      #'limit':{},
      'up':'hero_visit',
      'hero_visit':[0.04,0.20,1,3],  #[a,b,c,d] ax+b 计算几率x为技能等级 在c-d中随机
   },
   'skill288':{   #豪杰
      'infoArr':['e|estate_active[2]', '-|1', '-|3'],
      'nextArr':['e|estate_active[2]'],
      'index':660,
      'type':6,
      'cost_type':4,
      'max_level':20,
      'shogun_type':4,
      'ability_info':[],
      #'limit':{},
      'estate_active':['hero',['6'],[0.02,0.18,1,3]],
   },
   'skill297':{   #商贾
      'infoArr':['e|estate_active[2]'],
      'nextArr':['e|estate_active[2]'],
      'merge':4,   #几次合服后可见
      'state':1,   #开服几天后显示 
      'index':661,
      'type':6,
      'cost_type':4,
      'max_level':20,
      'shogun_type':4,
      'ability_info':[],
      #'limit':{},
      'estate_active':['trade',['2'],[0.01,0.04]],
   },








   'star03':{ #金色耐久星辰  格挡强度
      'infoArr':['-|special.change.prop.blockAdd'],
      'up':{ 'special.change.prop.blockAdd':3},
      'special':
      {
            'change':{
               'prop':{
                  'blockAdd':25,
               },
            },
      },
   },
   'star04':{ #金色痛击星辰  暴击强度
      'infoArr':['-|special.change.prop.critAdd'],
      'up':{ 'special.change.prop.critAdd':3},
      'special':
      {
            'change':{
               'prop':{
                  'critAdd':25,
               },
            },
      },
   },
   'star16':{ #金色英魂星辰
      'up':{ 'act[0].binding.dmg':2,'act[0].binding.dmgReal':1},
      'act':[{
                     'priority':9054,
                     'type': 2,             #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
                     'src': 2,             #发出源 0前军，1后军，【2英雄】，-1全军        
                     'times':-1,  
                     'follow':{'useBase':1, 'keys':{'isHero':800,'dmg':200,'dmgReal':200}},
                     'nonSkill':1,    #非技能，不受傲气影响
                     'binding':{
                        'dmg':10,     
                        'dmgReal':2,   
                     },
                     'info':['英魂',0],           #应该引用文字配置
      }],
   },
   'star17':{ #金色坚定星辰
      'infoArr':['%|act[0].binding.resRealRate'],
      'up':{ 'act[0].binding.resRealRate':10},
      'act':[{
                     'priority':9412,
                     'type': 4,             #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
                     'src': -1,             #发出源 0前军，1后军，【2英雄】，-1全军
                     'follow':'all',        
                     'times':-1,  
                     'nonSkill':1,    #非技能，不受傲气影响
                     'binding':{
                        'resRealRate':80,      #减免本次伤害
                     },
                     'info':['坚定',0],           #应该引用文字配置
      }],
   },

   ####################### 精英大将等 #######################


   'npc831h':{   #护国军规避（100级时，必定防止攻击中以任意方兵力比例消灭部队的效果）
      'up':{ 'act[0].round.any':10},
      'act':[   #全军受到攻击时，若攻击中包含以任意方兵力比例消灭部队的效果，则有几率触发【规避】，使本段攻击无法命中
         {
            'priority':888310,
            'type': 4,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': -1,	            #发出源 0前军，1后军，【2英雄】，-1全军
            'round':{'any':0}, 
            'times': -1,
            'nonSkill':1,    #非技能
            'binding':{
                'acc':-1000,
            },
            'unFollow':{'equip113_4':1,'hero725r':1},
            'follow':{'useBase':1, 'keys':{'dmgRealHp1':1000,'dmgRealHp0':1000,'dmgRealRate':1000,'dmgRealMax':1000,'dmgRealLost':1000}},
            'info':['hero831h',1],
            'lv':10,
         },
      ],
   },

   'npc1':{ #精英之威
      'passive':[
         {
            'rslt':{'powerRate':300},
            'special':{
               'change':{
                   'prop':{   #增加比例抗性
                      'armys[0].resRealRate':250,
                      'armys[1].resRealRate':250,

                      'heroLogic.deBuffRate':-250,
                      'armys[0].deBuffRate':-250,
                      'armys[1].deBuffRate':-250,
                   },
               },
           },
         },
         {
            'cond':['lv', '*', 60],
            'rslt':{
               'dmgFinalLevel':20,
               'resSex0':10,
               'resSex1':10,
               'hpmBase':10,
               'hpmRate':10,
               'atkBase':10,
               'atkRate':10,
               'defBase':5,
               'defRate':5,
               'powerRate':50,
            },
            'special':{
               'changeEnemy':{
                  'prop':{   #敌方的护盾、治疗、增援弱化
                     'armys[0].others.elementShield':-5,
                     'armys[1].others.elementShield':-5,
                     'armys[0].others.elementCure':-5,
                     'armys[1].others.elementCure':-5,
                     'armys[0].others.elementSummon':-5,
                     'armys[1].others.elementSummon':-5,
                  },
               },
               'change':{
                   'prop':{   #增加比例抗性
                      'armys[0].resRealRate':10,
                      'armys[1].resRealRate':10,

                      'heroLogic.deBuffRate':-5,
                      'armys[0].deBuffRate':-5,
                      'armys[1].deBuffRate':-5,
                   },
                   'skill':{   
                      'npc1.act[0].dmgRealRate':0.25,
                      'npc1.act[1].dmgRealRate':0.5,
                   },
               },
           },
         },
      ],
      'act':[
          {
            'priority':2000020,
            'type': 13,	            
            'src':2, 
            'round':{'0':1000},	
            'tgt':[1, -1], 	
            'dmgRealRate':20,	
            'nonSkill':1,   
            'noBfr':1,	
            'noAft':1,
            'noBuff': 1,
            'noRealRate': 1,
            'noKill':1,	

            'lv':4,
            'isBurn':5,	              
            'eff':'effNpc1',
            'info':['effNpc1',2],	       
          },
          {
            'priority':-9999,
            'type': 9,	            
            'src':2, 
            'srcFree':1, 
            'tgt':[1, -1], 		
            'dmgRealRate':40,	
            'nonSkill':1,   
            'noBfr':1,	
            'noAft':1,
            'noBuff': 1,
            'noRealRate': 1,
            'noKill':1,	

            'lv':4,
            'isBurn':5,	       
            'eff':'effNpc1',
            'info':['effNpcLoser1',2],	       
          },
      ],
   },
   'npc2':{ #大将之威
      'passive':[
         {
            'rslt':{'powerRate':600},
            'special':{
               'change':{
                   'prop':{   #增加比例抗性
                      'armys[0].resRealRate':500,
                      'armys[1].resRealRate':500,

                      'heroLogic.deBuffRate':-500,
                      'armys[0].deBuffRate':-500,
                      'armys[1].deBuffRate':-500,
                   },
               },
           },
         },
         {
            'cond':['lv', '*', 60],
            'rslt':{
               'dmgFinalLevel':40,
               'resSex0':20,
               'resSex1':20,
               'hpmBase':20,
               'hpmRate':20,
               'atkBase':20,
               'atkRate':20,
               'defBase':10,
               'defRate':10,
               'powerRate':100,
            },
            'special':{
               'changeEnemy':{
                  'prop':{   #敌方的护盾、治疗、增援弱化
                     'armys[0].others.elementShield':-10,
                     'armys[1].others.elementShield':-10,
                     'armys[0].others.elementCure':-10,
                     'armys[1].others.elementCure':-10,
                     'armys[0].others.elementSummon':-10,
                     'armys[1].others.elementSummon':-10,
                  },
               },
               'change':{
                   'prop':{   #增加比例抗性
                      'armys[0].resRealRate':20,
                      'armys[1].resRealRate':20,

                      'heroLogic.deBuffRate':-10,
                      'armys[0].deBuffRate':-10,
                      'armys[1].deBuffRate':-10,
                   },
                   'skill':{   
                      'npc1.act[0].dmgRealRate':0.5,
                      'npc1.act[1].dmgRealRate':1,
                   },
               },
           },
         },
      ],
      'act':[
          {
            'priority':2000021,
            'type': 13,	            
            'src':2, 
            'round':{'0':1000},	
            'tgt':[1, -1], 	
            'dmgRealRate':40,	
            'nonSkill':1,   
            'noBfr':1,	
            'noAft':1,
            'noBuff': 1,
            'noRealRate': 1,
            'noKill':1,	

            'lv':7,
            'isBurn':5,	              
            'eff':'effNpc2',
            'info':['effNpc2',2],	       
          },
          {
            'priority':-9999,
            'type': 9,	            
            'src':2, 
            'srcFree':1, 
            'tgt':[1, -1], 		
            'dmgRealRate':80,	
            'nonSkill':1,   
            'noBfr':1,	
            'noAft':1,
            'noBuff': 1,
            'noRealRate': 1,
            'noKill':1,	

            'lv':7,
            'isBurn':5,	       
            'eff':'effNpc2',
            'info':['effNpcLoser2',2],	       
          },
      ],
   },


   ####################### 神灵天赋 #######################


   'godDmgAnger':{   #我方攻击每造成1次战损，我方怒气+  ok
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'priority': 33333000,	
            'type': 31,               #造成兵力变化
            'src': -6,          
            'srcPuppet': 1,  
            'condHpChange':[['<',0],None,None,1,[0,1]],   #31攻击敌方部队造成伤害后      
            'tgt':[0, 5],        #神灵    
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,
            'atOnce':-33,    #立即插入排队排头
            'times': -1,

            'energyKey':'anger',       #充能标记类型 
            'energy':1,                 #获得充能标记数量 
            'info':['造损加怒',0],
            'eff':'effNull',
            'time':0,	          #强制指定战报时间         
         },
      ],
   },
   'godHurtAnger':{       #我方每受到1次来自敌方的战损，我方怒气+   ok
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'priority': 33333001,	 
            'type': 23,               #自身兵力变化
            'cond':[['srcTeam',1]],
            'condHpChange':[['<',0]],  
            'src': -1,    
            #'srcPuppet': 1,  
            'srcFree': -1,
            'tgt':[0, 5],    
            'nonSkill':1,    #非技能，不受傲气
            'noBfr':1,
            'noAft':1,
            'atOnce':-33,
            'times': -1,

            'energyKey':'anger',      
            'energy':1,                
            'info':['自损加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godRecoveryAnger':{   #我方任意部队每次有效恢复兵力后，我方怒气+  ok
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'priority': 33333010,	
            'type': 23,               #自身兵力变化
            'src': -1,          
            'condHpChange':[['>',0]],   
            'tgt':[0, 5],        #神灵    
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,
            'atOnce':-33,    #立即插入排队排头
            'times': -1,

            'energyKey':'anger',       #充能标记类型 
            'energy':1,                 #获得充能标记数量 
            'info':['恢复加怒',0],
            'eff':'effNull',
            'time':0,	          #强制指定战报时间         
         },
      ],
   },



   'godDmg0Anger':{   #我方步兵攻击每造成1次战损，我方怒气+  ok
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'priority': 33333040,	
            'type': 31,               #造成兵力变化
            'srcArmy': 0,          
            'condHpChange':[['<',0],None,None,1],   #31攻击敌方造成伤害后      
            'tgt':[0, 5],        #神灵    
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,
            'atOnce':-33,    #立即插入排队排头
            'times': -1,

            'energyKey':'anger',       #充能标记类型 
            'energy':1,                 #获得充能标记数量 
            'info':['步攻加怒',0],
            'eff':'effNull',
            'time':0,	          #强制指定战报时间         
         },
      ],
   },
   'godHurt0Anger':{       #我方步兵每受到1次来自敌方的战损，我方怒气+   ok
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'priority': 33333041,	 
            'type': 23,               #自身兵力变化
            'cond':[['srcTeam',1]],
            'condHpChange':[['<',0]],  
            'srcArmy': 0,   
            'srcFree': -1,             
            'tgt':[0, 5],    
            'nonSkill':1,    #非技能，不受傲气
            'noBfr':1,
            'noAft':1,
            'atOnce':-33,
            'times': -1,

            'energyKey':'anger',      
            'energy':1,                
            'info':['步损加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godDmg1Anger':{   #我方骑兵攻击每造成1次战损，我方怒气+  ok
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'priority': 33333042,	
            'type': 31,               #造成兵力变化
            'srcArmy': 1,          
            'condHpChange':[['<',0],None,None,1],   #31攻击敌方造成伤害后      
            'tgt':[0, 5],        #神灵    
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,
            'atOnce':-33,    #立即插入排队排头
            'times': -1,

            'energyKey':'anger',       #充能标记类型 
            'energy':1,                 #获得充能标记数量 
            'info':['骑攻加怒',0],
            'eff':'effNull',
            'time':0,	          #强制指定战报时间         
         },
      ],
   },
   'godHurt1Anger':{       #我方骑兵每受到1次来自敌方的战损，我方怒气+   ok
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'priority': 33333043,	 
            'type': 23,               #自身兵力变化
            'cond':[['srcTeam',1]],
            'condHpChange':[['<',0]],  
            'srcArmy': 1, 
            'srcFree': -1,               
            'tgt':[0, 5],    
            'nonSkill':1,    #非技能，不受傲气
            'noBfr':1,
            'noAft':1,
            'atOnce':-33,
            'times': -1,

            'energyKey':'anger',      
            'energy':1,                
            'info':['骑损加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godDmg2Anger':{   #我方弓兵攻击每造成1次战损，我方怒气+  ok
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'priority': 33333044,	
            'type': 31,               #造成兵力变化
            'srcArmy': 2,          
            'condHpChange':[['<',0],None,None,1],   #31攻击敌方造成伤害后      
            'tgt':[0, 5],        #神灵    
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,
            'atOnce':-33,    #立即插入排队排头
            'times': -1,

            'energyKey':'anger',       #充能标记类型 
            'energy':1,                 #获得充能标记数量 
            'info':['弓攻加怒',0],
            'eff':'effNull',
            'time':0,	          #强制指定战报时间         
         },
      ],
   },
   'godHurt2Anger':{       #我方弓兵每受到1次来自敌方的战损，我方怒气+   ok
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'priority': 33333045,	 
            'type': 23,               #自身兵力变化
            'cond':[['srcTeam',1]],
            'condHpChange':[['<',0]],  
            'srcArmy': 2,        
            'srcFree': -1,        
            'tgt':[0, 5],    
            'nonSkill':1,    #非技能，不受傲气
            'noBfr':1,
            'noAft':1,
            'atOnce':-33,
            'times': -1,

            'energyKey':'anger',      
            'energy':1,                
            'info':['弓损加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godDmg3Anger':{   #我方方士攻击每造成1次战损，我方怒气+  ok
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'priority': 33333046,	
            'type': 31,               #造成兵力变化
            'srcArmy': 3,          
            'condHpChange':[['<',0],None,None,1],   #31攻击敌方造成伤害后      
            'tgt':[0, 5],        #神灵    
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,
            'atOnce':-33,    #立即插入排队排头
            'times': -1,

            'energyKey':'anger',       #充能标记类型 
            'energy':1,                 #获得充能标记数量 
            'info':['方攻加怒',0],
            'eff':'effNull',
            'time':0,	          #强制指定战报时间         
         },
      ],
   },
   'godHurt3Anger':{       #我方方士每受到1次来自敌方的战损，我方怒气+   ok
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'priority': 33333047,	 
            'type': 23,               #自身兵力变化
            'cond':[['srcTeam',1]],
            'condHpChange':[['<',0]],  
            'srcArmy': 3,    
            'srcFree': -1,            
            'tgt':[0, 5],    
            'nonSkill':1,    #非技能，不受傲气
            'noBfr':1,
            'noAft':1,
            'atOnce':-33,
            'times': -1,

            'energyKey':'anger',      
            'energy':1,                
            'info':['方损加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },





   'godBuffAnger':{   #我方每获得1次增益状态，我方怒气+  ok
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'priority': 33333100,	
            'type': 20,            
            'src': -1,                
            'tgt':[0, 5],     
            'condBuffChange':{
                1:1,   #任意增益状态可触发
            },
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,
            'atOnce':-33,    #立即插入排队排头
            'times': -1,
            'energyKey':'anger',      
            'energy':1,                
            'info':['我增加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godBuffEnemyAnger':{   #敌方每获得1次增益状态，我方怒气+  ok
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'enemy': 1,             #以敌方src为主体的行动
            'priority': 33333101,	
            'type': 20,            
            'src': -1,                
            'tgt':[1, 5],     
            'condBuffChange':{
                1:1,   #任意增益状态可触发
            },
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,
            'atOnce':-33,    #立即插入排队排头
            'times': -1,
            'energyKey':'anger',      
            'energy':1,                
            'info':['敌增加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },

   'godDebuffAnger':{      #我方每获得1次不良状态，我方怒气+  ok
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'priority': 33333120,	
            'type': 20,            
            'src': -1,                
            'tgt':[0, 5],     
            'condBuffChange':{
                2:1,   #任意不良状态可触发
            },
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,
            'atOnce':-33,    #立即插入排队排头
            'times': -1,
            'energyKey':'anger',      
            'energy':1,                
            'info':['我增加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godDebuffEnemyAnger':{    #敌方每获得1次不良状态，我方怒气+  ok
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'enemy': 1,             #以敌方src为主体的行动
            'priority': 33333121,	
            'type': 20,            
            'src': -1,                
            'tgt':[1, 5],     
            'condBuffChange':{
                2:1,   #任意不良状态可触发
            },
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,
            'atOnce':-33,    #立即插入排队排头
            'times': -1,
            'energyKey':'anger',      
            'energy':1,                
            'info':['敌增加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },

   'godRemoveBuffAnger':{   #我方每有部队被驱散增益状态时，我方怒气+
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'priority': 33333130,	
            'type': 21,            
            'src': -1,                
            'tgt':[0, 5],     
            'condBuffChange':{
                1:1,          #任意增益状态可触发
                'dispel':1,   #仅适用21状态消失。填1必须被驱散的才触发，-1必须是自然消亡的才触发
            },
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,
            'atOnce':-33,    #立即插入排队排头
            'times': -1,
            'energyKey':'anger',      
            'energy':1,                
            'info':['我被驱增益加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godRemoveDebuffEnemyAnger':{    #敌方每有部队被驱散不良状态时，我方怒气+
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'enemy': 1,             #以敌方src为主体的行动
            'priority': 33333131,	
            'type': 21,            
            'src': -1,                
            'tgt':[1, 5],     
            'condBuffChange':{
                2:1,   #任意不良状态可触发
                'dispel':1,   #仅适用21状态消失。填1必须被驱散的才触发，-1必须是自然消亡的才触发
            },
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,
            'atOnce':-33,    #立即插入排队排头
            'times': -1,
            'energyKey':'anger',      
            'energy':1,                
            'info':['敌被驱不良加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },



   'godBuffFireAnger':{      #我方每获得1次火灾状态，我方怒气+  ok
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'priority': 33333160,	
            'type': 20,            
            'src': -1,                
            'tgt':[0, 5],     
            'condBuffChange':{
                'buffFire':1,   #火灾状态可触发
            },
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,
            'atOnce':-33,    #立即插入排队排头
            'times': -1,
            'energyKey':'anger',      
            'energy':1,                
            'info':['我火加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godBuffFireEnemyAnger':{    #敌方每获得1次火灾状态，我方怒气+  ok
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'enemy': 1,             #以敌方src为主体的行动
            'priority': 33333161,	
            'type': 20,            
            'src': -1,                
            'tgt':[1, 5],     
            'condBuffChange':{
                'buffFire':1,   #火灾状态可触发
            },
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,
            'atOnce':-33,    #立即插入排队排头
            'times': -1,
            'energyKey':'anger',      
            'energy':1,                
            'info':['敌火加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },

   'godFireEnemyAnger':{    #敌方每次受到【火灾】的燃烧伤害，我方怒气+ ok
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'enemy': 1,             #以敌方src为主体的行动
            'priority': 33333171,	
            'type': 3,            
            'src': -1,  
            #'srcPuppet': 1,                
            'tgt':[1, 5],     
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,
            'atOnce':-33,    #立即插入排队排头
            'times': -1,
            'energyKey':'anger',      
            'energy':1,         
            'follow':'effFire',       
            'info':['敌燃加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },


   'godDeadAnger':{    #我方单部队首次全灭时，我方怒气+  ok
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'priority': 33333200,	
            'type': 19,            
            'src': -1,                
            'tgt':[0, 5],     
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,
            'atOnce':-33,    #立即插入排队排头
            'limitTimes': 1,
            'energyKey':'anger',      
            'energy':1,                
            'info':['我亡加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godDeadEnemyAnger':{    #敌方单部队首次全灭时，我方怒气+  ok
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'enemy': 1,             #以敌方src为主体的行动
            'priority': 33333201,	
            'type': 19,            
            'src': -1,                
            'tgt':[1, 5],     
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,
            'atOnce':-33,    #立即插入排队排头
            'limitTimes': 1,
            'energyKey':'anger',      
            'energy':1,                
            'info':['敌亡加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },

   'godHeroSkillAnger':{   #我方每发动1次英雄技，怒气+
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'priority': 33333220,	
            'type': 3,              
            'src': 2,          
            'tgt':[0, 5],        #神灵    
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,
            'atOnce':-33,    #立即插入排队排头
            'times': -1,
            'follow':{'useBase':1, 'keys':{'isHero':1000}},

            'energyKey':'anger',       #充能标记类型 
            'energy':1,                 #获得充能标记数量 
            'info':['英技加怒',0],
            'eff':'effNull',
            'time':0,	          #强制指定战报时间         
         },
      ],
   },
   'godHeroSkillFirstAnger':{   #我方每回合首次发动英雄技后，怒气++
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'priority': 33333221,	
            'type': 3,              
            'src': 2,          
            'tgt':[0, 5],        #神灵    
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,
            'atOnce':-33,    #立即插入排队排头
            'follow':{'useBase':1, 'keys':{'isHero':1000}},

            'energyKey':'anger',       #充能标记类型 
            'energy':1,                 #获得充能标记数量 
            'info':['英首加怒',0],
            'eff':'effNull',
            'time':0,	          #强制指定战报时间         
         },
      ],
   },


   'godRoundHpLowerAnger':{   #远战首回合起，每回合结束时若敌方兵力高于我方，我方怒气+
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'priority': 8800040,	
            'type': 27,            
            'src': 5,                
            'tgt':[0, 5],   
            'cond':[['hp','<']],  
            'round':{'all':1000},  
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,

            'energyKey':'anger',      
            'energy':1,                
            'info':['我寡加怒',0],
            'eff':'effNull',
            'time':0,	       
         },
      ],
   },

   'godRoundHpLowAnger':{   #回合结束时若我方兵力少于50%，我方怒气+
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'priority': 8800041,	
            'type': 27,            
            'src': 5,                
            'tgt':[0, 5],   
            'cond':[['hpPoint','<',500]],  
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,

            'energyKey':'anger',      
            'energy':1,                
            'info':['我残加怒',0],
            'eff':'effNull',
            'time':0,	       
         },
      ],
   },
   'godRoundHpLowEnemyAnger':{   #回合结束时若敌方兵力少于50%，我方怒气+
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'enemy': 1,             #以敌方src为主体的行动
            'priority': 8800042,	
            'type': 27,            
            'src': 2,  
            'srcFree': 1, 
            'tgt':[1, 5],   
            'cond':[['hpPoint','<',500]],  
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,

            'energyKey':'anger',      
            'energy':1,                
            'info':['敌残加怒',0],
            'eff':'effNull',
            'time':0,	       
         },
      ],
   },

   'godRound1Anger':{   #远1回合，我方神灵怒气+
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'priority': 2999999.2000000,	
            'type': 13,            
            'src': 5,                
            'tgt':[0, 5],     
            'round':{'1':1000},	   
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,

            'energyKey':'anger',      
            'energy':1,                
            'info':['远1加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godRound2Anger':{   #远2回合，我方神灵怒气+
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'priority': 2999999.33333401,	
            'type': 13,            
            'src': 5,                
            'tgt':[0, 5],     
            'round':{'2':1000},	   
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,

            'energyKey':'anger',      
            'energy':1,                
            'info':['远2加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godRound3Anger':{   #近1回合，我方神灵怒气+
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'priority': 2999999.33333402,	
            'type': 13,            
            'src': 5,                
            'tgt':[0, 5],     
            'round':{'3':1000},	   
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,

            'energyKey':'anger',      
            'energy':1,                
            'info':['近1加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godWeather0Anger':{   #无气象，远战首回合起，每回合我方怒气+
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'priority': 2999999.33333500,	
            'type': 13,            
            'src': 5,                
            'tgt':[0, 5],     
            'round':{'all':1000},
            'cond':[['weather','=',0]],	   
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,

            'energyKey':'anger',      
            'energy':1,                
            'info':['无气加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },

 ########################################敌方怒气-的优先   33339

   'godRecoveryReduceAnger':{   #我方任意部队每次有效恢复兵力后，敌方怒气-  ok
      'up':{  'act[0].energy':-1},
      'act':[
         {   
            'priority': 33339015,	
            'type': 23,               #自身兵力变化
            'src': -1,          
            'condHpChange':[['>',0]],   
            'tgt':[1, 5],        #神灵    
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,
            'atOnce':-33,    #立即插入排队排头
            'times': -1,

            'energyKey':'anger',       #充能标记类型 
            'energy':-1,                 #获得充能标记数量 
            'info':['恢复减怒',0],
            'eff':'effNull',
            'time':0,	          #强制指定战报时间       
         },
      ],
   },

   'godRound1ReduceAnger':{   #远1回合，敌方神灵怒气-
      'up':{  'act[0].energy':-1},
      'act':[
         {   
            'priority': 8899999.33339400,	 #降怒优先于攒怒
            'type': 13,            
            'src': 5,                
            'tgt':[1, 5],     
            'round':{'1':1000},	   
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,

            'energyKey':'anger',      
            'energy':-1,                
            'info':['远1降怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godRound2ReduceAnger':{   #远2回合，敌方神灵怒气-
      'up':{  'act[0].energy':-1},
      'act':[
         {   
            'priority': 8899999.33339401,	 #降怒优先于攒怒
            'type': 13,            
            'src': 5,                
            'tgt':[1, 5],     
            'round':{'2':1000},	   
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,

            'energyKey':'anger',      
            'energy':-1,                
            'info':['远2降怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godRound3ReduceAnger':{   #近1回合，敌方神灵怒气-
      'up':{  'act[0].energy':-1},
      'act':[
         {   
            'priority': 8899999.33339402,	 #降怒优先于攒怒
            'type': 13,            
            'src': 5,                
            'tgt':[1, 5],     
            'round':{'3':1000},	   
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,

            'energyKey':'anger',      
            'energy':-1,                
            'info':['近1降怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },


   'godWeather1ReduceAnger':{   #风沙气象下，远战首回合起，每回合敌方怒气-
      'up':{  'act[0].energy':-1},
      'act':[
         {   
            'priority': 8899999.33339501,	
            'type': 13,            
            'src': 5,                
            'tgt':[1, 5],     
            'round':{'all':1000},
            'cond':[['weather','=',1]],	   
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,

            'energyKey':'anger',      
            'energy':-1,                
            'info':['风沙降怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godWeather2ReduceAnger':{   #暴雨气象下，远战首回合起，每回合敌方怒气-
      'up':{  'act[0].energy':-1},
      'act':[
         {   
            'priority': 8899999.33339502,	
            'type': 13,            
            'src': 5,                
            'tgt':[1, 5],     
            'round':{'all':1000},
            'cond':[['weather','=',2]],	   
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,

            'energyKey':'anger',      
            'energy':-1,                
            'info':['暴雨降怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godWeather3ReduceAnger':{   #浓雾气象下，远战首回合起，每回合敌方怒气-
      'up':{  'act[0].energy':-1},
      'act':[
         {   
            'priority': 8899999.33339503,	
            'type': 13,            
            'src': 5,                
            'tgt':[1, 5],     
            'round':{'all':1000},
            'cond':[['weather','=',3]],	   
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,

            'energyKey':'anger',      
            'energy':-1,                
            'info':['浓雾降怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'godWeather4ReduceAnger':{   #大雪气象下，远战首回合起，每回合敌方怒气-
      'up':{  'act[0].energy':-1},
      'act':[
         {   
            'priority': 8899999.33339504,	
            'type': 13,            
            'src': 5,                
            'tgt':[1, 5],     
            'round':{'all':1000},
            'cond':[['weather','=',4]],	   
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,

            'energyKey':'anger',      
            'energy':-1,                
            'info':['大雪降怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },


   ####################### 神灵功法 #######################


   'gs201':{   #躁动<怒气>  远战每回合，我方神灵怒气{0}
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'priority': 8899999.33332201,	
            'type': 13,            
            'src': 5,                
            'tgt':[0, 5],     
            'round':{'far':1000},	   
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,

            'energyKey':'anger',      
            'energy':1,                
            'info':['远战加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'gs202':{   #燃灯<怒气>  近战每回合，我方神灵怒气{0}
      'up':{  'act[0].energy':1},
      'act':[
         {   
            'priority': 8899999.33332202,	
            'type': 13,            
            'src': 5,                
            'tgt':[0, 5],     
            'round':{'near':1000},	   
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,

            'energyKey':'anger',      
            'energy':1,                
            'info':['近战加怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'gs204':{   #宁静<怒气>  远战每回合，敌方神灵怒气{0}
      'up':{  'act[0].energy':-1},
      'act':[
         {   
            'priority': 8899999.33339204,	 #降怒优先于攒怒
            'type': 13,            
            'src': 5,                
            'tgt':[1, 5],     
            'round':{'far':1000},	   
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,

            'energyKey':'anger',      
            'energy':-1,                
            'info':['远战降怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },
   'gs205':{   #安魂<怒气>  近战每回合，敌方神灵怒气{0}
      'up':{  'act[0].energy':-1},
      'act':[
         {   
            'priority': 8899999.33339205,	 #降怒优先于攒怒
            'type': 13,            
            'src': 5,                
            'tgt':[1, 5],     
            'round':{'near':1000},	   
            'nonSkill':1,  
            'noBfr':1,
            'noAft':1,

            'energyKey':'anger',      
            'energy':-1,                
            'info':['近战降怒',0],
            'eff':'effNull',
            'time':0,	         
         },
      ],
   },


   ####################### 傀儡效果（含精炼） #######################

   'puppetAtkMeleeDmg':{   #傀儡近身攻击时增伤
      'up':{  'act[0].binding.dmgPuppetScale':1, 'act[0].binding.dmgFixed':1},
      'act':[
         {   
            'priority': 44444000,	
            'type': 2,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 6,	      
            'times': -1,    #每回合最多使用次数
            'nonSkill':1,    #非技能，不受傲气
            'binding':{
               'dmgPuppetScale':1,
               'dmgFixed':1,
            },
            'follow':{'useBase':1,'keys':{'isMelee':1000}},
            'info':['近攻增伤',0],      
         },
      ],
   },
   'puppetAtkRemoteDmg':{   #傀儡远程攻击时增伤
      'up':{  'act[0].binding.dmgPuppetScale':1, 'act[0].binding.dmgFixed':1},
      'act':[
         {   
            'priority': 44444001,	
            'type': 2,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 6,	      
            'times': -1,    #每回合最多使用次数
            'nonSkill':1,    #非技能，不受傲气
            'binding':{
               'dmgPuppetScale':1,
               'dmgFixed':1,
            },
            'follow':{'useBase':1,'keys':{'isRemote':1000}},
            'info':['远攻增伤',0],      
         },
      ],
   },

   'puppetDefMeleeDmg':{   #傀儡受到近身攻击时增伤
      'up':{  'act[0].binding.resPuppetScale':-1, 'act[0].binding.dmgFixed':1},
      'act':[
         {   
            'priority': 44444040,	
            'type': 4,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 6,	      
            'times': -1,    #每回合最多使用次数
            'nonSkill':1,    #非技能，不受傲气
            'binding':{
               'resPuppetScale':-1,
               'dmgFixed':1,
            },
            'follow':{'useBase':1,'keys':{'isMelee':1000}},
            'info':['受近增伤',0],      
         },
      ],
   },
   'puppetDefRemoteDmg':{   #傀儡受到远程攻击时增伤
      'up':{  'act[0].binding.resPuppetScale':-1, 'act[0].binding.dmgFixed':1},
      'act':[
         {   
            'priority': 44444041,	
            'type': 4,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 6,	      
            'times': -1,    #每回合最多使用次数
            'nonSkill':1,    #非技能，不受傲气
            'binding':{
               'resPuppetScale':-1,
               'dmgFixed':1,
            },
            'follow':{'useBase':1,'keys':{'isRemote':1000}},
            'info':['受远增伤',0],      
         },
      ],
   },



   'puppet_pr11':{   #傀儡首次攻击时伤害
      'up':{  'act[0].binding.dmgPuppetScale':1, 'act[0].binding.dmgFixed':1},
      'act':[
         {   
            'priority': 44444411,	   
            'type': 2,   #连贯攻前
            'src': 6,
            'follow':{'useBase':1,'keys':{'isPuppet':1000}},  
            'allTimes':1,
            'nonSkill':1,  

            'binding':{
               'dmgPuppetScale':1,
               'dmgFixed':1,
            },
            'info':['首创',0],
            'actId':'puppet_pr11',
         },
      ],
   },
   'puppet_pr12':{   #傀儡耐久高于一半时伤害
      'up':{  'act[0].binding.dmgPuppetScale':1, 'act[0].binding.dmgFixed':1},
      'act':[
         {   
            'priority': 44444412,	  
            'type': 16,	            #标后
            'src': 6,
            'cond':['hpPoint','>',500,6],      #我方傀儡耐久高于一半
            'follow':{'useBase':1,'keys':{'isPuppet':1000}},
            'nonSkill':1,  
            'times':-1,  

            'binding':{
               'dmgPuppetScale':1,
               'dmgFixed':1,
            },
            'info':['高创',0],
         },
      ],
   },
   'puppet_pr13':{   #傀儡耐久低于一半时伤害
      'up':{  'act[0].binding.dmgPuppetScale':1, 'act[0].binding.dmgFixed':1},
      'act':[
         {   
            'priority': 44444412,	  
            'type': 16,	            #标后 
            'src': 6,
            'cond':['hpPoint','<',500,6],      #我方傀儡耐久高于一半
            'follow':{'useBase':1,'keys':{'isPuppet':1000}},
            'nonSkill':1,  
            'times':-1,  

            'binding':{
               'dmgPuppetScale':1,
               'dmgFixed':1,
            },
            'info':['低创',0],
         },
      ],
   },
   'puppet_pr15':{   #傀儡暴击伤害
      'up':{  'special[0].change.prop.$puppetLogic.others.critRate':1, 'special[0].change.prop.$puppetLogic.others.critAdd':2},
      'special':[
       {  
         'change':{
            'prop':{
               'puppetLogic.others.critRate':1,
               'puppetLogic.others.critAdd':2,
            },
         }
       },
      ],
   },
   'puppet_pr16':{   #傀儡攻击敌方部队时伤害
      'up':{  'act[0].binding.dmgPuppetScale':1, 'act[0].binding.dmgFixed':1},
      'act':[
         {   
            'priority': 44444416,	
            'type': 16,	            #标后
            'src': 6,	      
            'condTgt':[[['armyIndex',0],['armyIndex',1]]],    #目标为部队时
            'follow':{'useBase':1,'keys':{'isPuppet':1000}},
            'times': -1,   
            'nonSkill':1, 
            'binding':{
               'dmgPuppetScale':1,
               'dmgFixed':1,
            },
            'info':['创军',0],      
         },
      ],
   },
   'puppet_pr17':{   #傀儡攻击敌方傀儡时伤害
      'up':{  'act[0].binding.dmgPuppetScale':1, 'act[0].binding.dmgFixed':1},
      'act':[
         {   
            'priority': 44444417,	
            'type': 16,	            #标后
            'src': 6,	      
            'condTgt':[['armyIndex',6]],    #目标为傀儡时
            'follow':{'useBase':1,'keys':{'isPuppet':1000}},
            'times': -1,   
            'nonSkill':1, 
            'binding':{
               'dmgPuppetScale':1,
               'dmgFixed':1,
            },
            'info':['创傀',0],      
         },
      ],
   },

   'puppet_pr1x_pp100':{   #巨斧之魂   我方傀儡前两次攻击时触发【猛突】，暴击率和暴击创伤{0}
      'up':{
         'act[0].binding.enforceCrit':1,
         'act[0].binding.critRate':1, 
         'act[0].binding.critAdd':2,
      },
      'act':[
         {   
            'priority': 444444100,	
            'type': 2,	            
            'src': 6,	  
            'allTimes':2,
            'times':-1,    
            'follow':{'useBase':1,'keys':{'isPuppet':1000}},   
            'nonSkill':1, 
            'binding':{
               'enforceCrit':1,
               'critRate':1,    
               'critAdd':2,    
            },
            'lv':25,  
            'info':['pp100x',1],      
         },
      ],
   },

   'puppet_pr1x_pp101':{   #火炮之魂   我方傀儡存活时，我方攻击处于【火灾】状态中的目标时，造成伤害和创伤{0}
      'up':{
         'act[0].binding.dmgScale':1,
         'act[0].binding.dmgPuppetScale':1, 
         'act[0].binding.dmgFixed':1,
      },
      'act':[
         {   
            'priority': 444444101,	
            'type': 16,	            
            'src': -4,	   #全军含副将+英雄
            'srcPuppet': 1,
            'times':-1,    
            'follow':{'useBase':1,'keys':{'isPuppet':1000,'dmg':1000,'dmgReal':1000}},
            'cond':['selfArmy', 6],             #我方傀儡存活
            'condTgt':[
              ['buff','buffFire','>',0]
            ], 
            'nonSkill':1, 
            'binding':{
               'dmgScale':1,
               'dmgPuppetScale':1,
               'dmgFixed':1,  
            },
            'lv':25,  
            'info':['pp101x',1],      
         },
      ],
   },

   'puppet_pr1x_pp102':{   #爆锤之魂   奇数回合，我方傀儡速度和创伤{0}
      'up':{
         'act[0].buff.buffPP102x.prop.$others.spdRate':1, 
         'act[0].buff.buffPP102x.prop.$others.dmgPuppetScale':1, 
      },
      'act':[
         {   
            'priority': 444444102,	
            'type': 13,	            
            'src': 6,	
            'tgt':[0, 6],      
            'round':{'1':1000,'3':1000,'5':1000,'7':1000,'9':1000,'11':1000},
            
            'nonSkill':1, 
            'buff':{'buffPP102x':{'round':1, 'prop':{'others.spdRate':1, 'others.dmgPuppetScale':1.5}}}, 
            'time':0,   
            'eff':'effNull',   
            'info':['奇行',0],      
         },
      ],
   },
   'puppet_pr1x_pp103':{   #刺盾之魂   远战首回合起，每回合【回刺】的创伤强度累计{0}
      'up':{
         'act[0].buff.buffPP103x1.prop.$others.dmgOut_pp103_1':1.5, 
      },
      'act':[
         {   
            'priority': 444444103,	
            'type': 13,	            
            'src': 6,	
            'tgt':[0, 6],      
            'round':{'all':1000},
            
            'nonSkill':1, 
            'buff':{'buffPP103x1':{'prop':{'others.dmgOut_pp103_1':1.5}}}, 
            'time':0,   
            'eff':'effNull',   
            'info':['回刺增长',0],      
         },
      ],
   },
   'puppet_pr1x_pp104':{   #重弩之魂   我方傀儡行动结束后若无不良状态，触发【快速装填】的几率{0}
      'up':{
         'special[0].change.skill.$pp104.act[1].round.all':1, 
      },
      'special':[{
         'change':{
            'skill':{
               'pp104.act[1].round.all':1,
            },
         },
      }],
   },
   'puppet_pr1x_pp105':{   #拳刃之魂   远战回合，我方傀儡触发【瞬步】的几率{0}
      'up':{
         'special[0].change.skill.$pr1x_pp105.act[0].round.far':1, 
      },
      'special':[{
         'change':{
            'skill':{
               'pr1x_pp105.act[0].round.far':1,
            },
         },
      }],
   },
   'puppet_pr1x_pp106':{   #雷鼓之魂   我方傀儡的远程攻击命中时，使目标获得【磁锁】的几率{0}
      'up':{
         'special[0].change.skill.$pp106.act[1].binding.buff.buffMagnetic.rnd':1, 
      },
      'special':[{
         'change':{
            'skill':{
               'pp106.act[1].binding.buff.buffMagnetic.rnd':1,
            },
         },
      }],
   },

   'puppet_pr2x_pp200':{   #暴戾之魂   我方傀儡触发暴击后，自身获得【亢奋】的特殊效果2回合，攻防{0}
      'up':{
         'act[1].buff.buffExcited.prop.atkRate':1,  
         'act[1].buff.buffExcited.prop.defRate':1,  
      },
      'act':[
         {
            'priority': 444444200,
            'type': 2,            
            'src': 6,                        
            'follow':{'useBase':1,'keys':{'isPuppet':1000}},
            'times':-1,
            'nonSkill':1,    
            'binding':{
                'energys':{
                   'Epr2x_pp200':{     #每次暴击充能
                      'condE':['crit','>',0],   #目标被暴击过
                      'srcE':{
                         'energy_pp200':{'num':1,'checkAct':1},
                      },
                   },
                }
            },
            'info':['暴击充戾',0],	         
         },
         {
            'priority':444444200,
            'type': 17,             #触发类型，17充能后  自身获得【亢奋】的特殊效果2回合，攻防{0}
            'src': 6,
            'tgt':[0, 6],
            'nonSkill':1,  
            'times': -1,  

            'energyKeySrc': 'energy_pp200',
            'costKey': 'energy_pp200',
            'cost': 1,
            'multMax':-1,          
            'mult':{},           

            'buff':{'buffExcited':{'round':2,'prop':{'atkRate':1, 'defRate':1}}},
            'eff':'effNull', 
            'lv':25,
            'info':['buffExcited',1],	        
         },
      ],
   },


   'puppet_pr2x_pp201':{   #玲珑之魂   每回合被我方傀儡攻击的首个目标，获得【压制】的几率{0}
      'up':{
         'act[0].binding.buffPatch.id.buffRepress.rnd':1,  
      },
      'act':[
         {   
            'priority': 444444201,	 
            'type': 16,	           
            'src': 6,	            #发出源 傀儡
            'unFollow':{'pp103_1':1},  
            'follow':{'useBase':1,'keys':{'isPuppet':1000}},  
  
            'times': 1,    
            'nonSkill':1,   
            'binding':{
                'buffPatch':{
                   'id':{'buffRepress':{'rnd':1}},     
                },
            },
            'info':['首压',0],  
         },
      ],
   },

   'puppet_pr2x_pp202':{   #果决之魂   我方傀儡每回合对首个目标造成创伤时，可使其叠加1层【标靶】的特殊效果（最大3层），被暴击率{0}
      'up':{
         'act[0].binding.buff.buffMark.prop.$others.beEnforceCrit':0.7,  
      },
      'act':[
         {   
            'priority': 444444202,	 
            'type': 16,	           
            'src': 6,	            #发出源 傀儡
            'follow':{'useBase':1,'keys':{'isPuppet':1000}},
  
            'times': 1,    
            'nonSkill':1,   
            'binding':{
                'buff':{'buffMark':{ 'prop':{'others.beEnforceCrit':0.7}}},  #'round':1,
            },
            'info':['叠标靶',0],  
         },
      ],
   },

   'puppet_pr2x_pp203':{   #坚韧之魂   远战第二回合和近战首回合结束时，我方发动【时光追忆】的几率{0}
      'up':{
         'special[0].change.skill.$pr2x_pp203.act[0].round.2':1, 
         'special[0].change.skill.$pr2x_pp203.act[0].round.3':1, 
      },
      'special':[{
         'change':{
            'skill':{
               'pr2x_pp203.act[0].round.2':50,
               'pr2x_pp203.act[0].round.3':50,
            },
         },
      }],
   },

   'puppet_pr2x_pp204':{   #冷峻之魂   我方傀儡远程攻击触发【寒袭】时，造成创伤和按比例消灭效果{0}
      'up':{
         'special[0].change.skill.$pp204.act[2].binding.dmgRealMax':0.04, 
         'special[0].change.skill.$pp204.act[2].binding.dmgPuppetScale':1, 
         'special[0].change.skill.$pp204.act[2].binding.dmgFixed':1, 
      },
      'special':[{
         'change':{
            'skill':{
               'pp204.act[2].binding.dmgRealMax':0.04,
               'pp204.act[2].binding.dmgPuppetScale':1,
               'pp204.act[2].binding.dmgFixed':1,
            },
         },
      }],
   },

   'puppet_pr2x_pp205':{   #伏忍之魂   我方傀儡攻击存在不良状态的目标时，暴击率、暴击创伤{0}
      'up':{
         'act[0].binding.enforceCrit':1, 
         'act[0].binding.critRate':1, 
         'act[0].binding.critAdd':2, 
      },
      'act':[
         {   
            'priority': 444444205,	
            'type': 16,	            
            'src': 6,	
            'condTgt':[
               ['buff', 2,'>',0]
            ],
            'follow':{'useBase':1,'keys':{'isPuppet':1000}},
            'times': -1,  
            'nonSkill':1, 

            'binding':{
               'enforceCrit':1,
               'critRate':1,    
               'critAdd':2, 
            },

            'info':['忍暴',0],	          #应该引用文字配置    
         },
      ],
   },
   'puppet_pr2x_pp206':{   #骄狂之魂   我方傀儡发动【电涌再生】时，触发【狂能】的几率{0}
      'up':{
         'special[0].change.skill.$pr2x_pp206.act[1].round.any':1, 
      },
      'special':[{
         'change':{
            'skill':{
               'pr2x_pp206.act[1].round.any':1,
            },
         },
      }],
   },

   'puppet_pr21':{   #傀儡首次受到攻击时免伤
      'up':{  'act[0].binding.resPuppetScale':1, 'act[0].binding.dmgFixed':-1},
      'act':[
         {   
            'priority': 44444421,	  
            'type': 38,   #连贯防前
            'src': 6,
            'allTimes':1,
            'times':-1,
            'nonSkill':1,  
            'follow':{'useBase':1,'keys':{'isPuppet':1000}},  

            'binding':{
               'resPuppetScale':1,
               'dmgFixed':-1,
            },
            'info':['首免',0],
            'actId':'puppet_pr21',
         },
      ],
   },
   'puppet_pr22':{   #傀儡耐久高于一半时免伤
      'up':{  'act[0].binding.resPuppetScale':1, 'act[0].binding.dmgFixed':-1},
      'act':[
         {   
            'priority': 44444422,	  
            'type': 4, 
            'src': 6,
            'cond':['hpPoint','>',500,6],      #我方傀儡耐久高于一半
            'follow':{'useBase':1,'keys':{'isPuppet':1000}},
            'nonSkill':1,  
            'times':-1,  

            'binding':{
               'resPuppetScale':1,
               'dmgFixed':-1,
            },
            'info':['高免',0],
         },
      ],
   },
   'puppet_pr23':{   #傀儡耐久低于一半时免伤
      'up':{  'act[0].binding.resPuppetScale':1, 'act[0].binding.dmgFixed':-1},
      'act':[
         {   
            'priority': 44444423,	
            'type': 4, 
            'src': 6,
            'cond':['hpPoint','<',500,6],      #我方傀儡耐久不足一半
            'follow':{'useBase':1,'keys':{'isPuppet':1000}},
            'nonSkill':1,  
            'times':-1,  

            'binding':{
               'resPuppetScale':1,
               'dmgFixed':-1,
            },
            'info':['低免',0],
         },
      ],
   },
   'puppet_pr24':{   #傀儡暴击抗性
      'up':{  'special[0].change.prop.$puppetLogic.others.resCrit':1},
      'special':[
       {  
         'change':{
            'prop':{
               'puppetLogic.others.resCrit':1,
            },
         }
       },
      ],
   },
   'puppet_pr25':{   #傀儡未受到暴击时免伤
      'up':{  'special[0].change.prop.$puppetLogic.others.noCritRate':1},
      'special':[
       {  
         'change':{
            'prop':{
               'puppetLogic.others.noCritRate':1,
            },
         }
       },
      ],
   },
   'puppet_pr26':{   #傀儡受到近身攻击时免伤
      'up':{  'act[0].binding.resPuppetScale':1, 'act[0].binding.dmgFixed':-1},
      'act':[
         {   
            'priority': 44444426,	
            'type': 4,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 6,	      
            'times': -1,    #每回合最多使用次数
            'nonSkill':1,    #非技能，不受傲气
            'binding':{
               'resPuppetScale':1,
               'dmgFixed':-1,
            },
            'follow':{'useBase':1,'keys':{'isMelee':1000}},
            'info':['近免',0],      
         },
      ],
   },
   'puppet_pr27':{   #傀儡受到远程攻击时免伤
      'up':{  'act[0].binding.resPuppetScale':1, 'act[0].binding.dmgFixed':-1},
      'act':[
         {   
            'priority': 44444427,	
            'type': 4,	            #触发类型，0起始 【1主】 2攻前 3攻后 4防前 5防后
            'src': 6,	      
            'times': -1,    #每回合最多使用次数
            'nonSkill':1,    #非技能，不受傲气
            'binding':{
               'resPuppetScale':1,
               'dmgFixed':-1,
            },
            'follow':{'useBase':1,'keys':{'isRemote':1000}},
            'info':['远免',0],      
         },
      ],
   },
}