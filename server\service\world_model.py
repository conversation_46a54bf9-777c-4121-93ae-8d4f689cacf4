#-*- coding: utf-8 -*-
import os
log_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "app.log")
@classmethod
@gen.coroutine
def login(cls, params):
    if User.season_num <0:
        raise gen.Return([None, None, None, None])
    uid = params.get('uid')
    sessionid = params.get('sessionid')
    if not uid or not sessionid:
        raise gen.Return([None, None, None, None])



    user_code = params.get('user_code')
    pf_data = params.get('pf_data')
    md5_str,tstr,pf = sessionid.split('|')
    if md5_str != md5('%s%s%s' % (uid,tstr,settings.PWD)).hexdigest():
        raise gen.Return([None, None, None, None])
    uid = cls.get_new_uid(uid,params['zone'])

    
    try:
        uid = int(uid)
    except:
        raise gen.Return([None, None, None, None])
    
    is_new = False
    if User.users.has_key(uid):
        self = User.users[uid]
        self.check_self()
        #self.check_home()
        self.get_reward()
    else:
        if game_config.zone[options.zone][5]:
            raise gen.Return([None, None, None, None])
        self = cls(uid)
        yield self.init_user()
        self.pf = pf
        User.users[uid] = self
        is_new = True
    print '111111111111'
    now = datetime.datetime.now()
    self.now = now
    self.refresh_task()
    print '2222222222222'
    self.refresh_honour_task()
    print '33333333333333'
    self.refresh_new_task()
    print '444444444444444'
    self.check_pay_ploy()
    print '555555555555555555555'
    self.check_dial()
    print '66666666666666666666'
    self.check_treasure()
    print '7777777777777777777777777'
    self.check_sale_shop()
    print '88888888888888'
    self.check_limit_free()
    print '999999999999999'
    self.check_happy_login()
    print '10000000000000000'
    self.check_coin_consume()
    print '110000000000000000'
    self.check_exchange_shop()
    print '120000000000000000'
    self.check_pay_choose()
    print '130000000000000000'
    self.check_rool_pay()
    print '14000000000000000'
    self.check_festival()
    print '150000000000000000'
    self.check_legend()
    print '16000000000000000'
    self.check_equip_box()
    self.check_sp_army_box()
    self.check_duplicate_shop()
    self.check_user_arena()
    self.check_legend_awaken()
    self.check_credit_dict(0)
    if self.country>=0:
        self.get_hero_catch(None)
    self.get_ftask(None)
    if self.pf == 'h5_qqdt':
        yield self.update_qqgame_blue()


    self.add_credit(0)
    if user_code and not self.birthday:
        t = time.strptime(user_code[6:14], '%Y%m%d')
        birthday = datetime.date(*t[0:3])
        self.birthday = birthday
    User.add_backup_uid(self.uid)
    if self.records['auto_mk_army']:
        self=self.building_auto_make_army_all()

    # 修复损坏的战功相关数据
    if not hasattr(self, 'credit_get_gifts') or self.credit_get_gifts is None:
        self.credit_get_gifts = []
        with open(log_file, "a") as f:
            f.write("修复玩家credit_get_gifts数据\n")
            f.write(str(self.uid))
            f.write("\n")

    # 调试和修复credit_lv数据
    with open(log_file, "a") as f:
        f.write("=== 调试玩家credit_lv ===\n")
        f.write(str(self.uid))
        f.write("\n")
        f.write("hasattr credit_lv: ")
        f.write(str(hasattr(self, 'credit_lv')))
        f.write("\n")

        if hasattr(self, 'credit_lv'):
            f.write("credit_lv值: ")
            f.write(str(self.credit_lv))
            f.write("\n")
            f.write("credit_lv类型: ")
            f.write(str(type(self.credit_lv)))
            f.write("\n")
        else:
            f.write("玩家没有credit_lv属性\n")

    # 获取当前合服配置中的最大等级
    # 检查可能的合服次数属性名
    merge_num = 0
    if hasattr(self, 'merge_num'):
        merge_num = self.merge_num
    elif hasattr(self, 'mergeNum'):
        merge_num = self.mergeNum
    else:
        # 从zone配置中获取合服次数
        zone = getattr(self, 'zone', None)
        if zone and zone in game_config.zone:
            merge_num = game_config.zone[zone][8] if len(game_config.zone[zone]) > 8 else 0

    merge_key = 'merge_%d' % merge_num

    with open(log_file, "a") as f:
        f.write("玩家合服次数: ")
        f.write(str(merge_num))
        f.write("\n")
        f.write("合服配置key: ")
        f.write(str(merge_key))
        f.write("\n")

    # 根据合服次数选择配置
    if merge_key in game_config.credit and 'clv_first_reward' in game_config.credit[merge_key]:
        clv_first_reward = game_config.credit[merge_key]['clv_first_reward']
        with open(log_file, "a") as f:
            f.write("使用合服配置\n")
    else:
        clv_first_reward = game_config.credit.get('clv_first_reward', [])
        with open(log_file, "a") as f:
            f.write("使用默认配置\n")

    max_credit_lv = len(clv_first_reward) - 1 if clv_first_reward else 0

    with open(log_file, "a") as f:
        f.write("配置中最大credit_lv: ")
        f.write(str(max_credit_lv))
        f.write("\n")

    # 修复异常的credit_lv数据
    original_lv = getattr(self, 'credit_lv', None)
    if not hasattr(self, 'credit_lv') or self.credit_lv is None or self.credit_lv < 0:
        self.credit_lv = 0
        with open(log_file, "a") as f:
            f.write("修复credit_lv数据: ")
            f.write(str(self.uid))
            f.write(" ")
            f.write(str(original_lv))
            f.write(" -> 0\n")
    elif self.credit_lv > max_credit_lv:
        with open(log_file, "a") as f:
            f.write("修复credit_lv超出合服配置范围: ")
            f.write(str(self.uid))
            f.write(" ")
            f.write(str(self.credit_lv))
            f.write(" -> ")
            f.write(str(max_credit_lv))
            f.write("\n")
        self.credit_lv = max_credit_lv
    else:
        with open(log_file, "a") as f:
            f.write("credit_lv正常: ")
            f.write(str(self.uid))
            f.write(" ")
            f.write(str(self.credit_lv))
            f.write("\n")

    user_dict = self.dumps(shallow=True)
    if self.country >= 0:
        user_dict['task'] = self.get_full_task()
        country_club = CountryClub.country_clubs[self.country]
        alien_log = country_club.get_alien_log()
        country_club_dict = country_club.dumps()
        country_club_dict['alien_log'] = alien_log

        #user_dict['country_club'] = CountryClub.country_clubs[self.country].dumps(shallow=True)
        user_dict['country_club'] = country_club_dict
    champion_uid = User.pk_yard.get('champion_uid', None)
    if champion_uid is not None:
        if champion_uid < 0:
            user_dict['champion_user'] = [champion_uid, User.pk_robot[abs(champion_uid)]['uname']]
        else:
            user_dict['champion_user'] = [champion_uid, User.users[champion_uid].uname]
    self.check_auction()
    self.pf_key = params['pf_key']
    user_dict['auction'] = User.auction
    User.last_login_uid = self.uid
    if not User.has_user_login:
        User.has_user_login = 1



    hero_rank_list = sorted(User.hero_power_rank.values(), reverse=True)[:game_config.bless_hero['secondary_lv']['range']]
    if hero_rank_list:
        power = sum(hero_rank_list)/len(hero_rank_list)
    else:
        power = 1
    lv = 0
    for item in game_config.bless_hero['secondary_lv']['convert']:
        if power>=item[0]:
            lv = item[1:]
            break
    user_dict['secondary_lv'] = lv
    if user_dict.has_key('coin_records'):
        del user_dict['coin_records']
    ################################
    user_dict['honour_num'] = User.honour.get('num',0)
    user_dict['honour_if_open'] = User.honour.get('if_open',False)
    user_dict['honour_exp_multiple'] = User.honour.get('exp_multiple',1)
    user_dict['honour_strength'] = User.honour.get('strength',0)

    raise gen.Return([pf, is_new, pf_data, user_dict])


def user_init(self, uid=None):
    now = datetime.datetime.now()
    self.uid = uid
    self.pf_key = None
    self.pf = 'local'
    self.uname = None
    self.head = None
    self.is_online = False
    self.online_log = {'login_time': None, 'logout_time': None, 'on_time': 0, 'off_time': 0}

    self.sessionid = None
    self.gold = 0
    self.coin = 0

    self.food = 0
    self.wood = 0
    self.iron = 0
    self.merit = 0
    self.office = 0
    self.add_time = now
    self.login_time = now
    self.country = None
    self.building_cd = {}
    self.prop = {}
    self.hero = {}
    self.home = {}
    self.credit_settle = None
    self.quota_gift = None
    self.year_kill_num = 0
    self.year_kill_troop = 0
    self.year_dead_num = 0
    self.year_build = 0
    self.banned_users = {}
    self.is_notify = False
    self.mayor_get_time = None
    self.impeach_time = None
    self.xyz = None
    self.bless_hero = {}
    self.legend = {
        'refresh_time': now,
        'buy_times': 0,
        'combat_times': game_config.legend['initial_times'],
        'hero_dict': {
            # hid:[kill_num,reward_status]
        },
    }

    self.mining = {'res': [None, None, None],
                   'next_grab_user': None, 'magic_id': None,
                   'grab_num': 0, 'last_grab_time': None,
                   'free_magic_num': 0, 'last_magic_time': None,
                   'magic_lucky': None}
    # res/item; [start_time, hids, magic, grabbed_num, fail_time, succ_num, fail_num]

    self.records = {
        'tel': None,
        'guide': [],
        'reward_ids': [],
        'code_reward_ids': [],
        'title': [],
        'worship_time': None,
        'free_buy': {
            'gold': None,
            'food': None,
            'wood': None,
            'iron': None,
        },
        'equip_wash_val': 0,
        'star_up_time': now,
        'star_up_hids': [],
        'science_cd': [],
        'science_day_get_time': now,
        'science_day_get_ids': [],
        'up_power_hids': None,
        'push_task': None,
        'push_new_task': None,
        'push_effort': None,
        'chat': {
            '0': {'time': None, 'num': 0},
            '1': {'time': None, 'num': 0},
            '2': {'time': None, 'num': 0},
        },
        'online_reward': [0, now],
        'pay_ids': [],
        'pay_money': 0,
        'pay_coin': 0,
        'pay_pid_log': [],
        'pay_money_daily': 0,
        'pay_coin_daily': 0,
        'pay_time': None,
        'login_days': 1,
        'login_reward_day': 0,
        'wish_reward': -1,
        'office_reward': [],
        'pay_reward': [0, 0],
        'pay_ploy': {},
        'surprise_gift': {},
        'pay_skincoin': {},
        'day_buy_weapon': {},
        'lvup_reward': {},
        'city_build_event': {'time': None, 'num': 0},
        'week_card': {'time': None, 'receive_num': 0, 'last_receive_time': None},
        'fund': {'time': None, 'is_finish': False, 'receive_index': []},
        'wx_share': {'time': None, 'num': 0},
        'sale_shop': [0, 0, now, now, {}],
        'estate_6_hids': [now, []],  # 英雄围猎每次1次
        'happy_buy': {
            'login': [0, 0, []],
            'addup': [0, 0, []],
            'purchase': [0, 0, 0],
            'once': [0, 0, 0, [], 0],
            'sparta': {},
        },
        'coin_consume': ['0_0', 0, 0, []],
        'limit_free': [0, 0, 0, {}, [], []],
        'freeze': [0, now, '', '', ''],
        'dial': [0, 0, 0, [], []],
        'treasure': [0, 0, 0, 0, {}],
        'redbag_num': 0,
        'pay_gtask_reward': [None, now, 0, 0],
        'exchange_shop': [],
        'pay_choose': [],
        'rool_pay': [0, 0, None, 0, 0],
        'festival': {},
        'auction': [None, []],
        'pay_rank_gift': {'open_day': None, 'point_reward': [], 'rank_reward': False},
        'troop_add': {'season_num': None, 'food': 0},
        'equip_box': [0, 0, 0, None, 0, 0, 0, 0, {}],
        'sp_army_box': [0, 0, 0, None, 0, 0, 0, 0, []],
        'auto_mk_army': 0,
        'buy_weapon': {},
        'office_credit': 0,
        'estate_coin_get_time': now,  # 黄金产业时间
        'estate_coin_get_num': 0,  # 黄金产业每日数量
        'science_day_get_ids': [],
        'costly': [None, 0],
        'pay_again': [],
        'duplicate_shop': [],
        'collection_battles': {},  # 收藏战报
        'face': [],  # 表情背包
    }
    self.pub_records = {
        'draw_time': now,
        'draw_times': 0,
        'free_time': now,
        'free_times': 0,
        'extra_times': 0,
    }
    self.baggage = {
        'refresh_time': now,  # 下次付费次数刷新时间
        'free_times': 0,
        'refresh_free_time': now,
        'material': [[0, 0], [0, 0], [0, 0], [0, 0]],  # food,food,wood,icon([付费次数,活动次数])
    }
    self.shop = {
    }
    self.equip = {
    }
    self.equip_cd = []
    self.star = {
    }
    self.star_records = {
        'lucky_val': 0,  # 幸运值
        'index': 0,  # star_id|index
        'get_times': 0,
        'get_time': now,
    }

    self.application_log = {}  # 公会申请记录
    self.total_records = {
        'cost_gold': 0,  # 累计消耗gold
        'cost_coin': 0,  # 累计消耗coin
        'army_0': 0,  # 累计训练步兵数
        'army_1': 0,  # 累计训练骑兵数
        'army_2': 0,  # 累计训练弓兵数
        'army_3': 0,  # 累计训练方兵数
        'merit': 0,  # 累计功勋值
        'finish_gtask': 0,  # 累计完成政务数
        'rat_gtask': 0,  # 累计完成政务评级最高次数
        'resolve': 0,  # 累计问道次数
        'catch_times': 0,  # 累计名将切磋次数
        'pk_rank_times': 0,  # 累计pk_user次数
        'build_count': 0,  # 累计建设值
        'kill_num': 0,  # 累计杀敌数
        'runaway_num': 0,  # 累计逃兵数量
        'die_num': 0,  # 累计战损
        'visit_num': 0,  # 累计拜访次数
        'make_equip': 0,  # 累计宝物制作次数
        'office_gtask': 0,  # 当前爵位等级完成政务数
        'climb_times': 0,  # 累计过关斩将次数
        'estate_1': 0,  # 累计产业村落次数
        'estate_2': 0,  # 累计产业港口次数
        'estate_3': 0,  # 累计产业农田次数
        'estate_4': 0,  # 累计产业林场次数
        'estate_5': 0,  # 累计产业矿场次数
        'estate_6': 0,  # 累计产业牧场次数
        'country_fight': 0,  # 累计国战次数

        'build_count_merge': 0,  # 合区累计建设值
        'kill_num_merge': 0,  # 合区累计杀敌数
        'die_num_merge': 0,  # 合区累计战损
        'pk_yard_times': 0,  # 累计报名比武大会
    }  # 各种累计获得数量
    self.office_right = []
    self.alien_reward = []
    self.pk_records = {
        'log': [],
        'best_rank': game_config.pk['pk_enemy'][3],
        'pk_times': game_config.pk['pk_count'][0],
        'pk_time': now,
        'buy_times': 0,
        'buy_time': now,
    }
    self.pve_records = {
        'combat_times': game_config.pve['combat_times'],
        'combat_time': now,
        'buy_times': 0,
        'buy_time': now,
        'battle_id': 'battle000',
        'chapter': {},
    }
    self.climb_records = {
        'combat_times': game_config.climb['configure']['frequency'],  # 每日战斗次数
        'combat_time': now,
        'buy_times': 0,
        'buy_time': now,
        'combat_num': 0,  # 当年(夏季更新)击杀数
        'kill_wave': 0,  # 当年(夏季更新)最高波数
        'season_num': 0,
        'reward': {},
        'end_time': None,
        'pk_data': None,
        'pk_result': None,
    }
    self.msg = {
        'sys': [],
        'usr': [],
    }
    self.shogun = [
        {'lv': 1, 'hids': ['block', 'block', 'block', 'block', 'block']},
        {'lv': 1, 'hids': ['block', 'block', 'block', 'block', 'block']},
        {'lv': 1, 'hids': ['block', 'block', 'block', 'block', 'block']},
        {'lv': 1, 'hids': ['block', 'block', 'block', 'block', 'block']},
        {'lv': 1, 'hids': ['block', 'block', 'block', 'block', 'block']},
        {'lv': 1, 'hids': ['block', 'block', 'block', 'block', 'block']},
        {'lv': 1, 'hids': ['block', 'block', 'block', 'block', 'block']},
    ]
    self.shogun_value = None  # [0,0,0,0,0,0,0]
    self.hero_catch = {}

    self.milepost_reward = []
    self.milepost_fight_reward = []
    self.estate = []
    self.visit = {}
    self.science = {}

    self.credit_year = User.season_num / 4
    self.credit = 0
    self.year_credit = 0
    self.credit_lv = 0
    self.is_credit_lv_up = 0
    self.credit_get_gifts = []
    self.credit_rool_gifts_num = 0
    self.gtask = {}
    self.ftask = {}
    self.pk_npc = {}
    self.city_build = {}
    self.birthday = None
    self.power = 0
    self.best_hero_power = 0
    self.task = {
        'daily_time': now,
        'daily': {},
        'main': [1, 0],
        'chapter': ['chapter_1', {}],
        'common': {},
        'country': [],  # [coutry_x,0]
    }
    self.effort = {
    }
    self.effort_records = {
        'daily_time': now,
        'daily': {
            'hero_box1': 0,  # 今日酒馆次数
            'hero_box2': 0,
            'hero_box3': 0,
        },
        'hero_box1': 0,  # 累计酒馆次数
        'hero_box2': 0,
        'hero_box3': 0,
        'army_make_seconds': 0,  # 累计造兵用时(秒)
        'best_personal': 0,  # 累计国战中杀敌最多次数
        'kill_captial': 0,  # 累计国战中击杀大将次数
        'buy_gtask_times': 0,  # 累计购买政务次数
        'forget_skill': 0,  # 累计遗忘技能数量
        'build_cost_minutes': 0,  # 累计建设消费加速分钟
        'hero_catch_lose': 0,  # 累计名将切磋失败次数
        'buff_corps': 0,  # 累计发布太守专令
        'appoint_mayor': 0,  # 累计被任命太守次数
        'appoint_minister': 0,  # 累计被任命丞相次数
        'gtask_cost_times': 0,  # 累计花钱政务评级次数
        'no_fight_victory': 0,  # 累计国战中未参战获胜次数
        'duplicate_kill_monster': 0,  # 跨服战击杀monster数量
        'duplicate_kill_user': {},  # 跨服战击杀user段位数量
        'duplicate_win': {},  # 跨服战胜利段位次数
        'duplicate_floor': {},  # 跨服战累计占领地块次数
        'duplicate_level': {},  # 跨服战累计占参加段位次数
        'duplicate_list': {},  # 跨服战累段位排行榜第一名次数
        'ts_alien': 0,  # 累计alien次数
        'ts_mining': 0,  # 累计mining次数
        'ts_thief': 0,  # 累计thief次数
    }
    self.coin_records = []  # 消费记录
    self.sale_pay = []  # 打折卷
    self.member = [0, None]  # 永久会员卡
    self.arena = {}  # 擂台赛数据
    self.beast = {}  # 兽灵仓库
    self.beast_index = 0  # 兽灵id
    self.beast_times = 0  # 兽灵格子购买次数
    self.beast_lock_ids = []  # 兽灵锁id列表
    self.records_360 = []  # 360 h5渠道关注认证奖励
    self.legend_awaken = {}  # 传奇觉醒
    self.new_task = {}  # 朝廷秘旨
    self.credit_dict = {}  # 每日战功增加
    self.qqgame_records = {  # qq蓝钻相关数据
        'blue_vip': {  # 用户蓝钻会员信息
            'is_blue_vip': 0,
            'blue_vip_level': 0,
            'is_blue_year_vip': 0,
            'is_super_blue_vip': 0
        },
        'daily': {  # 每日礼包领取奖励记录
            'blue_time': None,  # 蓝钻每日礼包
            'year_time': None,  # 年费会员每日礼包
            'super_time': None  # 豪华会员每日礼包
        },
        'lvup_records': [],  # 升级礼包领取记录
        'new_reward': False,  # 新手礼包是否领取
    }
    self.qqdt_records = {  # qq大厅相关数据
        'daily': None,  # 每日礼包领取奖励记录
        'lvup_records': [],  # 升级礼包领取记录
        'new_reward': False,  # 新手礼包是否领取
    }
    self.honour_hero = {}  # 赛季英雄
    self.honour_task = {}  # 赛季任务
    self.honour_log = []  # 赛季历史
    self.counter_free_drop_days = 1  # 能免费放弃英雄counter的开服天数
    self.extra_hero_army = {}  # 额外战功干扰
    self.use_ctask = 0
    self.skin_list = []
    self.duplicate_records = {}
    self.duplicate_prestige = []  # 近10次信誉积分记录
    self.duplicate_ban_time = None  # 跨服战禁赛结束时间
    self.sp_army = {}
    self.soul = {}
    self.optional_ones = [0, 0, 0, None, None, {}]
    self.big_shot = {}
    self.tomb = {}
    self.levelup_gift = {}
    self.surprise_gift_new = {}
    self.surprise_gift_day = {}
    self.surprise_gift_ever = {}
    self.loop_card = {}
    self.independ_addup = []
    self.used_sale_pay = []
    self.skin_stars = None  # 拥有皮肤的星级总和


def give_gifts(self, gift_dict, gtype='give_gifts'):
    return_res = {}
    for k, v in gift_dict.items():
        if k == 'gold':
            self.gold += int(v)
            return_res['gold'] = self.gold

        elif k == 'food':
            self.food += int(v)
            return_res['food'] = self.food

        elif k == 'wood':
            self.wood += int(v)
            return_res['wood'] = self.wood

        elif k == 'merit':
            self.merit += int(v)
            self.total_records['merit'] += v
            return_res['merit'] = self.merit
            return_res['total_records'] = self.total_records

        elif k == 'coin':
            self.coin += int(v)
            return_res['coin'] = self.coin
            if int(v) != 0:
                building_lv_limit, coin_records_limit = game_config.system_simple['coin_records_limit']
                if self.home['building001']['lv'] >= building_lv_limit:
                    self.coin_records.insert(0, [gtype, int(v), self.coin, int(time.time())])
                    if len(self.coin_records) > coin_records_limit:
                        self.coin_records = self.coin_records[:coin_records_limit]

        elif k == 'iron':
            self.iron += int(v)
            return_res['iron'] = self.iron

        elif k == 'equip':
            for equip_id in v:
                self.add_equip(equip_id)
            return_res['equip'] = self.equip

        elif k == 'awaken':
            for hid in v:
                if self.hero.has_key(hid):
                    if self.hero[hid].get('awaken', 0):
                        continue
                    if game_config.hero[hid]['rarity'] == 4:
                        item_num = game_config.system_simple['hero_buy'][1]
                    else:
                        item_num = game_config.system_simple['hero_buy'][0]
                    item_id = hid.replace('hero', 'item')
                    msg_gift_dict = {item_id: item_num}
                    name, info = game_config.system_simple['msg_awaken_repair']
                    msg_name = self.get_return_msg(name)
                    msg_info = self.get_return_msg(info)
                    self.add_gift_msg(msg_name, msg_info, msg_gift_dict, push_user=True)
                else:
                    self.add_hero(hid)
                self.hero[hid]['awaken'] = 1
                if self.head and self.head.split('_')[0] == hid:
                    self.check_head(hid)
            return_res['hero'] = self.hero
            return_res['head'] = self.head

        elif k.startswith('item'):
            if self.prop.has_key(k):
                self.prop[k] += v
            else:
                self.prop[k] = v
            return_res['prop'] = self.prop

        elif k.startswith('star'):
            for i in range(v):
                self.add_star(k)
            return_res['star'] = self.star
        elif k.startswith('soul'):
            self.add_soul(k, v)
            return_res['soul'] = self.soul
        elif k == 'title':
            for tv in v:
                title_time = datetime.datetime.now() + datetime.timedelta(minutes=game_config.title[tv]['time'])
                self.records['title'].append([tv, title_time])
            return_res['records'] = self.records

        elif k == 'credit':
            self.add_credit(int(v))

        elif k.startswith('saledepot'):
            sale_id, cd_minutes, out_pf = game_config.system_simple['sale_depot'][k]
            if self.pf not in out_pf:
                now = datetime.datetime.now()
                end_time = now + datetime.timedelta(minutes=cd_minutes)
                for ii in range(v):
                    self.sale_pay.append([sale_id, now, end_time])
            self.check_sale_shop()
            return_res['sale_pay'] = self.sale_pay
        elif k.startswith('beast'):
            for i in range(v):
                self.add_beast(k)
            return_res['beast'] = self.beast
            return_res['beast_lock_ids'] = self.beast_lock_ids
            return_res['beast_index'] = self.beast_index
        elif k == 'face':
            for i in v:
                self.records['face'].append(i)
            return_res['records'] = self.records

    return return_res


def get_festival_exchange_reward(self, params):
    self.check_festival()
    if not self.records['festival']:
        raise Model_Error(10201, u'活动已过期')
    goods_id = params['goods_id']
    goods_limit = self.records['festival']['exchange']['goods_limit'].get(goods_id, 0)
    f_key = self.records['festival']['f_key']
    goods_config = game_config.festival[f_key]['act']['exchange']['goods']
    if goods_config[goods_id].has_key('merge'):
        merge_times = game_config.zone[options.zone][8]
        if merge_times not in goods_config[goods_id]['merge']:
            raise Model_Error(10201, u'活动已过期')
    if goods_config[goods_id]['limit'] != -1 and goods_limit >= goods_config[goods_id]['limit']:
        raise Model_Error(10202, u'超出兑换数量上限')
    for item in goods_config[goods_id]['need']:
        self.spend_cost(item, 'get_festival_exchange_reward')
    goods_limit += 1
    goods_limit = self.records['festival']['exchange']['goods_limit'][goods_id] = goods_limit
    gift_dict = goods_config[goods_id]['reward']
    if gift_dict.has_key('awaken'):
        if self.hero.get(gift_dict['awaken'][0], {}).get('awaken', 0) == 1:
            raise Model_Error(10310, u'此英雄已觉醒')
    return_res = self.give_gifts(gift_dict, 'get_festival_exchange_reward')
    return_res['records'] = self.records
    return_res['coin'] = self.coin
    return_res['prop'] = self.prop
    self.save()
    return {
        'user': return_res,
        'gift_dict': gift_dict,
    }


@gen.coroutine
def get_pk_npc(self, params):
    pk_npc_config = game_config.pk_npc
    captain = self.pk_npc.get('captain',[])
    if captain:
        init_captain = False
        end_time = captain[0][0]
        if end_time is None:
            init_captain = True
        else:
            if self.now >= end_time:
                captain.pop(0)
                init_captain = True
        if init_captain:
            if len(captain)>0:
                end_time,power,rate,blv,seed,captain_hid,random_gift,pk_res = captain[0]
                end_time = self.now + datetime.timedelta(minutes=pk_npc_config['captain_lasttime'])
                captain_troop = self.get_pk_npc_captain_troop(seed,blv,captain_hid)
                power = yield RequestCollection.FightServerRequest('getAllPower',captain_troop)
                power = power/rate
                captain[0][0] = end_time
                captain[0][1] = power
                self.pk_npc['captain'] = captain
            self.save()

    building_lv_exceed = self.home['building001']['lv'] - pk_npc_config['building_unlock_lv']
    building_lv = self.home['building001']['lv']
    if building_lv_exceed < 0:
        raise gen.Return({'user': {'pk_npc': self.pk_npc}})
    npc_limit = pk_npc_config['alien_limit'] - len(self.pk_npc.get('npc_dict',{}))
    if npc_limit <=0:
        raise gen.Return({'user': {'pk_npc': self.pk_npc}})
    city_ids = []
    for item in pk_npc_config['alien_flush_city'][self.country]:
        if world.cities[item].country != self.country:
            continue
        if self.pk_npc.get('npc_dict',{}).has_key(item):
            continue
        if self.ftask.get(item,[0])[0] != -1:
            continue
        city_ids.append(item)
    if not city_ids:
        raise gen.Return({'user': {'pk_npc': self.pk_npc}})
    refresh_time = self.pk_npc.get('refresh_time',None)
    #if not self.pk_npc:
    if not refresh_time:
        if len(city_ids) <2:
            raise gen.Return({'user': {'pk_npc': self.pk_npc}})
        self.pk_npc['refresh_time'] = self.now
        self.pk_npc['npc_dict'] = {}
        #self.pk_npc['captain'] = []
        #self.pk_npc['captain_hids'] = []


        alien_first = pk_npc_config['alien_first']
        city_num = min(alien_first[0], len(city_ids))
        city_ids = random.sample(city_ids, city_num)
        city_list = []
        for item in city_ids[:-1]:
            city_list.append([item, 0, alien_first[1],self.now])
        if city_num == alien_first[0]:
            city_list.append([city_ids[-1],1,alien_first[2], self.now])
        else:
            city_list.append([city_ids[-1], 0, alien_first[1], self.now])

    else:
        refresh_time = self.pk_npc['refresh_time']
        refresh_time_list = []
        while True:
            time_list = list(refresh_time.timetuple()[:3])
            for item in pk_npc_config['alien_time']:
                r_time = datetime.datetime(*(time_list+item))
                if r_time < refresh_time:
                    continue
                if r_time > self.now:
                    break
                refresh_time_list.append(r_time)
            refresh_time = datetime.datetime(*(time_list+[0,0]))
            refresh_time += datetime.timedelta(days=1)
            if r_time > self.now:
                break
        if not refresh_time_list:
            raise gen.Return({'user': {'pk_npc': self.pk_npc}})
        capital_num = 0
        for k,v in game_config.city.items():
            if v['cityType'] not in  [4,5]:
                continue
            if world.cities[k].country == self.country:
                capital_num += 1
        random_alien_num = []
        for item in pk_npc_config['alien_num']:
            if capital_num >= item[0][0]:
                random_alien_num.append([item[0][1], item[1]])
        city_time_list = []
        for item in refresh_time_list:
            city_num = utils.random_choice2(random_alien_num)
            city_time_list += [item for i in range(city_num)]
        city_num = min(len(city_time_list),len(city_ids),npc_limit)
        city_ids = random.sample(city_ids, city_num)
        city_list = []
        for i in range(len(city_ids)):
            city_id = city_ids[i]
            city_time = city_time_list[i]
            alien_diffup = []
            for k,v in pk_npc_config['alien_diffup'].items():
                diffup_time = self.pk_npc.get(str(k),None)
                if diffup_time and diffup_time+datetime.timedelta(minutes=v[0]) > city_time:
                    continue
                alien_diffup.append([k,v[1]])
            if not alien_diffup:
                alien_diffup.append([0,pk_npc_config['alien_diffup'][0][1]])

            diffup = utils.random_choice2(alien_diffup)
            try:
                difflv = pk_npc_config['alien_difflv'][building_lv_exceed]
            except:
                difflv = pk_npc_config['alien_difflv'][-1]
            difflv += pk_npc_config['alien_difflv_gap'][int(diffup)]
            city_list.append([city_id, diffup, difflv,city_time])
            self.pk_npc[str(diffup)] = city_time
    #计算随即掉落奖励
    alien_arm_pro = pk_npc_config['alien_arm_pro']
    random_item = pk_npc_config['alien_arm'][0][1]
    for item in pk_npc_config['alien_arm'][1:]:
        chapter_id = item[0]
        chapter = self.pve_records['chapter'].get(chapter_id,{})
        if len(chapter.get('star',{})) <4:
            break
        random_item = item[1]

    for item in city_list:
        city_id,diffup,difflv,city_time = item
        self.pk_npc['npc_dict'][city_id] = [diffup, difflv,city_time,0,0,{},[],building_lv] #难度系数，英雄等级，刷新时间，剩余波数,平均战力, 随即掉落奖励,[pk_data,pk_result,time],官邸等级
        bot_troop = self.get_pk_npc_troop(city_id)
        random_gift = {}
        for n in range(len(bot_troop)):
            if random.random()<=alien_arm_pro:
                item_id,item_num = utils.random_choice2(random_item)
                random_gift[n] = [item_id,item_num]

        #power = yield RequestCollection.FightServerRequest('getAllPower',bot_troop)
        try:
            power = pk_npc_config['alien_lv_power'][building_lv_exceed]
        except:
            power = pk_npc_config['alien_lv_power'][-1]
        power = int(power*pk_npc_config['alien_diff_power'][diffup])

        self.pk_npc['npc_dict'][city_id][3] = len(bot_troop)
        self.pk_npc['npc_dict'][city_id][4] = power
        self.pk_npc['npc_dict'][city_id][5] = random_gift
    self.pk_npc['refresh_time'] = self.now
    self.save()
    raise gen.Return({'user': {'pk_npc': self.pk_npc}})

def refresh_pk_npc_captain(self, building_lv):
    pk_npc_config = game_config.pk_npc
    if building_lv not in pk_npc_config['captain_strike_open'].keys():
        return False
    captain = self.pk_npc.get('captain',[])
    #if not self.pk_npc:
    if not captain:
        self.pk_npc['captain'] = []
        self.pk_npc['captain_hids'] = []
    seed = int(time.time())
    random_hids = []
    for hid in pk_npc_config['captain_nameandrew'].keys():
        if hid not in self.pk_npc['captain_hids']:
            random_hids.append(hid)
    if not random_hids:
        return False
    captain_hid = random.choice(random_hids)


    hero_num = pk_npc_config['captain_armyopnum']
    #计算随即掉落奖励
    alien_arm_pro = pk_npc_config['alien_arm_pro']

    random_item = pk_npc_config['alien_arm'][0][1]
    for item in pk_npc_config['alien_arm'][1:]:
        chapter_id = item[0]
        chapter = self.pve_records['chapter'].get(chapter_id,{})
        if len(chapter.get('star',{})) <4:
            break
        random_item = item[1]
    random_gift = {}
    for n in range(hero_num):
        if random.random()<=alien_arm_pro:
            item_id,item_num = utils.random_choice2(random_item)
            random_gift[n] = [item_id,item_num]

    captain_list = [None,None,hero_num,building_lv, seed, captain_hid,random_gift,[]] #有效期，战力，剩余波数,官邸等级，种子，随即出的名将id,随即掉落的奖励,[pk_data,pk_result,time]
    self.pk_npc['captain'].append(captain_list)
    self.pk_npc['captain_hids'].append(captain_hid)
    self.save()
    return True

@gen.coroutine
def run_fight(self,now_d):
    now = int(time.mktime(now_d.timetuple()))
    
    xyz = self.check_xyz(now_d=now_d,if_settle=True)

    if self.refresh_npc_time['season_num']!=User.season_num:
        self.refresh_npc_time = {'season_num': User.season_num, 'data': []}
    
    add_npc = []

    now_minutes = now_d.hour*60+now_d.minute
    now_seconds = (now_minutes)*60+now_d.second
    if not xyz:
        for key in game_config.attack_city_npc:
            gc = game_config.attack_city_npc[key]
            if gc['arise_sec']!=-1 and utils.total_seconds(now_d-game_config.zone[options.zone][2])<=gc['arise_sec']:
                continue

            if (User.season_num+1)%gc['cycle']==0:
                for h, m in gc['arise_time']:
                    if (key, h, m) in self.refresh_npc_time['data']:
                        continue
                    if now_minutes<h*60+m:
                        continue

                    if now_minutes-(h*60+m)>5:
                        self.refresh_npc_time['data'].append((key, h, m))
                        continue

                    for i, country in self.countries.items():
                        if gc.has_key('safe_city'):
                            cids = list(set([cid for cid in self.cities.keys() if self.cities[cid].country==i])-set(gc['safe_city']))
                        else:
                            cids = list(set([cid for cid in self.cities.keys() if self.cities[cid].country==i]).intersection(set(gc['target_city'])))
                        num = min(gc['enemy_unm'], len(cids))
                        if num:
                            cids = random.sample(cids, num)
                            hids = [gc['enemy_hero'][0]]
                            if num-1>0:
                                hids.extend(random.sample(gc['enemy_hero'][1], num-1))
                            for j in range(len(cids)):
                                dd = {'type': key, 'hid': hids[j], 'cid': cids[j], 'start_time': now, 'country': int(gc['country'])}
                                self.attack_npc.append(dd)
                                add_npc.append(dd)
                    self.refresh_npc_time['data'].append((key, h, m))
    else:
        key = 'thief_three'
        gc = game_config.country_pvp[key]

        for h, m in gc['arise_time']:
            if (key, h, m) in self.refresh_npc_time['data']:
                continue
            if now_minutes<h*60+m:
                continue

            if now_minutes-(h*60+m)>5:
                self.refresh_npc_time['data'].append((key, h, m))
                continue

            for cid in gc['target_city']:
                cid = str(cid)
                hid = random.choice(gc['enemy_hero'])
                dd = {'type': key, 'hid': hid, 'cid': cid, 'start_time': now, 'country': int(gc['country'])}
                self.attack_npc.append(dd)
                add_npc.append(dd)
            self.refresh_npc_time['data'].append((key, h, m))

    if add_npc:
        for uid in User.users:
            User.users[uid].write('w.sync_attack_npc', add_npc)
    
    def get_ready_ncp():
        for i, npc in enumerate(self.attack_npc):
            if npc['type']=='thief_three':
                gc = game_config.country_pvp[npc['type']]
            else:
                gc = game_config.attack_city_npc[npc['type']]
            if now>=npc['start_time']+gc['speed']:
                return i, npc
        return None
    
    nn = get_ready_ncp()
    while nn:
        index, npc = nn
        yield self.check_npc_fire(now, npc)
        self.attack_npc.pop(index)
        nn = get_ready_ncp()

    gc = game_config.country_army
    if (game_config.zone[options.zone][8] and User.season_num+1>=gc['expand_day'][1]) or (not game_config.zone[options.zone][8] and User.season_num+1>=gc['expand_day'][0]):
        if self.country_army['season_num']!=User.season_num:
            self.country_army = {'season_num': User.season_num, 'data': []}
        # 生成护国军
        for time_list in gc['arise_time']:
            if now_minutes < time_list[0][0]*60+time_list[0][1]:
                continue
            for h,m in time_list:
                if (h, m) in self.country_army['data']:
                    continue
                if now_minutes < h*60+m:
                    continue

                if self.check_xyz(now_d=now_d) and now_minutes > gc['armistice'][0]*60+gc['armistice'][1]:
                    continue

                merge_times = game_config.zone[options.zone][8]
                #hlv = int(max(self.world_lv, gc['lv_%s' % merge_times][0])*gc['lv_%s' % merge_times][1])
                hlv = world.pve_lv

                big_c = {}
                small_c = {}
                for country in [0, 1, 2]:
                    for i, city_list in enumerate(gc[str(country)]['target_city']):
                        lost_city = set(self.countries[country].land_lost).intersection(set(city_list))
                        # 检测是否有失城
                        if not lost_city:
                            continue

                        # 计算大小国
                        city_hold_country = self.cities[city_list[-1]].country
                        big_c.setdefault(city_hold_country,[])
                        if country not in big_c[city_hold_country]:
                            big_c[city_hold_country].append(country)
                        small_c.setdefault(country,[])
                        if city_hold_country not in small_c[country]:
                            small_c[country].append(city_hold_country)

                enemy_num = gc['enemy_num_%s' % merge_times]
                troop_num_a = enemy_num[0]
                User.check_country_power()
                country_act = User.country_act
                for country in [0, 1, 2]:
                    for i, city_list in enumerate(gc[str(country)]['target_city']):
                        lost_city = set(self.countries[country].land_lost).intersection(set(city_list))
                        # 检测是否有失城
                        if not lost_city:
                            continue
                        hold_countries = set()
                        for _cid in lost_city:
                            city_country = self.cities[_cid].country
                            if city_country not in [0, 1, 2]:
                                continue
                            hold_countries.add(self.cities[_cid].country)
                        # 国家活跃值，多个国家取最大值
                        _country_act = max([country_act[_c] for _c in hold_countries])
                        troop_num_b = _country_act + len(lost_city)*enemy_num[3]

                        ## 大小国系数
                        city_hold_country = self.cities[city_list[-1]].country
                        if len(big_c.get(city_hold_country,[])) >1:
                            troop_num_b += enemy_num[1]
                        if len(small_c.get(country,[])) >1:
                            troop_num_b += enemy_num[2]
                        troop_num = int(troop_num_a*troop_num_b)
                        hid = gc['enemy_hero']

                        fake_uid = -200001
                        while fake_uid in self.troops:
                            fake_uid -= 1

                        troop = {'xtype': 'country_army', 'car_type': i, 'country': country, 'uid': fake_uid, 'hid': hid,
                                'hlv': hlv, 'city': city_list[0], 'status': 1, 'army': [-1, -1], 'data': None, 'num': troop_num}
                        troop['uname'] = '$' + gc[str(country)]['name']
                        troop['data'] = {
                                'city_list': [(c, game_config.world['marchSpeedBase']*gc['speed'] if k<len(city_list)-1 else 0) for k, c in enumerate(city_list)],
                                'have_dis': 0,
                                'start_time': now,
                                'speedup': 1,
                                'index': 0,
                                'last_time': now,
                                'dismiss_time': None,
                                'title': None,
                                'type': 0
                                }
                        self.troops.setdefault(fake_uid, {})
                        self.troops[fake_uid][hid] = troop
                        for uid in User.users:
                            u = User.users[uid]
                            u.write('w.troop_move_push', self.troops[fake_uid][hid])

                self.country_army['data'].append((h, m))
        # 检测失地数据
        change_countries = set()
        for time_list in gc['arise_time'][::-1]:
            if now_minutes < time_list[0][0]*60+time_list[0][1]:
                continue
            check_seconds = (time_list[0][0]*60+time_list[0][1] + gc['reaction_time'])*60
            if now_seconds != check_seconds:
                continue
            for country in [0, 1, 2]:
                for i, city_list in enumerate(gc[str(country)]['target_city']):
                    lost_city = set(self.countries[country].land_lost).intersection(set(city_list))
                    for _cid in lost_city:
                        if self.cities[_cid].country != self.cities[_cid].faith[0]:
                            continue
                        self.countries[country].land_lost.remove(_cid)
                        change_countries.add(country)
            break
        if change_countries:
            for _user in User.users.values():
                if _user.country not in list(change_countries):
                    continue
                _user.write('w.push_land_lost', {'land_lost': self.countries[_user.country].land_lost})
        # 护国军衰减
        for item in gc['decay']:
            if now_seconds != (item[0][0]*60+item[0][1])*60:
                continue
            del_country_army = []
            for country in [0, 1, 2]:
                for city_list in gc[str(country)]['target_city']:
                    _cid = city_list[-1]
                    if self.cities[_cid].fight:
                        continue
                    for _uid in self.troops.keys():
                        for k,v in self.troops[_uid].items():
                            if v['city'] != _cid:
                                continue
                            if v.get('xtype') != 'country_army':
                                continue
                            self.troops[_uid][k]['num'] = int(math.floor(v['num']*(1-item[1])))
                            if self.troops[_uid][k]['num'] == 0:
                                del_country_army.append(_uid)
            for _uid in del_country_army:
                del self.troops[_uid]


    now_s = self.fix_minute_time(now_minutes)
    latest_s = self.fix_minute_time(game_config.country_pvp['latest_time'][0]*60+game_config.country_pvp['latest_time'][1])
    diff_minute = latest_s - now_s
    #判断战斗
    for cid in self.cities:
        if int(cid) < 0:
            ## 襄阳战城市战斗
            settle_rate = None
            for k,v in game_config.country_pvp['quick_fight']:
                if diff_minute <= k:
                    settle_rate = v
                    break
            if settle_rate is None:
                loop_num = 1
            else:
                if self.cities[cid].fight and now >= self.cities[cid].fight['fight_time']:
                    teams = len(self.cities[cid].fight['team'][0]['troop']) + len(self.cities[cid].fight['team'][1]['troop'])
                    loop_num = int(math.ceil(teams*settle_rate))
                else:
                    loop_num = 1
            if loop_num > 10:
                loop_num = 10
            # if self.cities[cid].fight and now>=self.cities[cid].fight['fight_time'] and loop_num > 1 and now%10 == 0:
            #     print "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<"
            #     print "xyz_fight - cid: %s, diff_minute: %s, teams: %s, settle_rate: %s, loop_num: %s" % (cid, diff_minute, teams, settle_rate, loop_num)
            for i in xrange(loop_num):
                res = yield self.run_city_fight(cid, now, now_d)
                if loop_num > 1:
                    yield self.run(now_d)
        else:
            yield self.run_city_fight(cid, now, now_d)


@gen.coroutine
def get_battle_result(self, city, t1, t2, now, now_d, last_battle_result):
    try:
        initJS = last_battle_result['initJS']
        troop1, troop2 = initJS['troop']
        npc_reward = initJS['npc_reward']
        battle_id = initJS['battle_id']
        fight_result = last_battle_result['result']
    except:
        troop1, troop2, fight_result = last_battle_result
        initJS = None
        npc_reward = None
        battle_id = None
    app_log.info("fight_result>>>: %s", fight_result)
    copyJS = copy(initJS)
    win = None
    #计算战功
    #杀人战功=击杀部队数*双方战力倍数*旗子*爵位加成*玩家或者npc系数*杀人系数
    #牺牲战功=牺牲部队数量*部队等级系数*爵位*旗子*牺牲系数
    city.fight['fight_count'] += 1
    city.fight['rnd'] = fight_result['rnd']

    def get_credit(troop, kill_num, dead_num_1, dead_num_2, my_power, other_power, other_is_npc, is_attack, other_official):
        if troop['uid'] > 0:
            dead_num_1,dead_num_2 = User.users[troop['uid']].change_extra_hero_army(troop['hid'], dead_num_1, dead_num_2)
        kill_num = max(0 , kill_num)
        dead_num_1 = max(0 , dead_num_1)
        dead_num_2 = max(0 , dead_num_2)

        power_radio = max(min(other_power*1.0/my_power, game_config.credit['power_by'][1]), game_config.credit['power_by'][0])

        if other_is_npc:
            if other_official==-2:
                npc_radio = game_config.credit['play_npc'][1]*game_config.credit['guard_lv'][2]
            elif other_official==-1:
                npc_radio = game_config.credit['play_npc'][1]*game_config.credit['guard_lv'][1]
            else:
                npc_radio = game_config.credit['play_npc'][1]*game_config.credit['guard_lv'][0]
        else:
            npc_radio = game_config.credit['play_npc'][0]

        if troop['uid']>0:
            army_rank = [item['rank'] for item in User.users[troop['uid']].get_fight_prepare_data(troop['hid'])['army']]
        else:
            army_rank = [item['rank'] for item in troop['army']]

        buff = 0
        if troop['country'] in [0, 1, 2]:
            buffs = self.countries[troop['country']].buffs
        else:
            buffs = {}

        if is_attack:
            gc = game_config.country['buff_country3']
            if buffs.has_key('buff_country3') and now_d<=buffs['buff_country3'][2]+datetime.timedelta(minutes=gc['time']) and buffs['buff_country3'][0]==city.cid:
                buff = game_config.country['buff_country3']['effect_consume'][1]
        else:
            gc = game_config.country['buff_country4']
            if buffs.has_key('buff_country4') and now_d<=buffs['buff_country4'][2]+datetime.timedelta(minutes=gc['time']) and buffs['buff_country4'][0]==city.cid:
                buff = game_config.country['buff_country4']['effect_consume'][1]

        u = User.users[troop['uid']] if troop['uid']>0 else None
        kill_office = 0
        if User.use_function.get('use_office37', 0) == 0:
            office_config = game_config.office
        else:
            office_config = game_config.office37
        for office in office_config['righttype']['killmexp']:
            if u and office in u._get_office_right_list():
                kill_office += office_config['right'][office]['para'][0]
        x = [office_config['right'][office]['para'][0] for office in
                office_config['righttype']['mexp_compensate'] if u and office in u._get_office_right_list()]
        if x:
            dead_office = max(x)
        else:
            dead_office = 1
        c1 = kill_num*power_radio*(1+buff)*(1+kill_office)*npc_radio*game_config.credit['kill_ratio']
        c2 = dead_num_1*game_config.credit['army_ratio'][army_rank[0]-1]*(1+buff)*dead_office*game_config.credit['dead_ratio']
        c3 = dead_num_2*game_config.credit['army_ratio'][army_rank[1]-1]*(1+buff)*dead_office*game_config.credit['dead_ratio']
        credit_effect = 1
        if troop['country'] in [0,1,2]:
            expedition_config = game_config.country['expedition']
            if expedition_config['switch']:
                if self.countries[troop['country']].expedition_cid == city.cid:
                    credit_effect = expedition_config['effect']
        return int((c1+c2+c3)*credit_effect)

    if fight_result['winner']==0:

        troop1_credit = get_credit(troop1,
                troop2['army'][0]['hp']+troop2['army'][1]['hp'],
                troop1['army'][0]['hp']-fight_result['winnerHp'][0],
                troop1['army'][1]['hp']-fight_result['winnerHp'][1],
                troop1['power'], troop2['power'], troop2['uid']<0, True, troop2['official']
                )

        troop2_credit = get_credit(troop2,
                troop1['army'][0]['hp']-fight_result['winnerHp'][0]+troop1['army'][1]['hp']-fight_result['winnerHp'][1],
                troop2['army'][0]['hp'],
                troop2['army'][1]['hp'],
                troop2['power'], troop1['power'], troop1['uid']<0, False, troop1['official']
                )

        if troop1['uid']>0:
            if int(city.cid)>=0:
                User.users[troop1['uid']].add_kill_num(troop2['army'][0]['hp']+troop2['army'][1]['hp'],
                    max(0, troop1['army'][0]['hp']-fight_result['winnerHp'][0])+max(0, troop1['army'][1]['hp']-fight_result['winnerHp'][1]),if_country=True, is_win=True)
            else:
                self.add_xyz_kill(User.users[troop1['uid']], troop2['army'][0]['hp']+troop2['army'][1]['hp'])
            city.fight['user_logs'][troop1['uid']]['kill'] += (troop2['army'][0]['hp']+troop2['army'][1]['hp'])
            city.fight['user_logs'][troop1['uid']]['dead'] += (max(0, troop1['army'][0]['hp']-fight_result['winnerHp'][0])+max(0, troop1['army'][1]['hp']-fight_result['winnerHp'][1]))
        if troop2['uid']>0:
            if int(city.cid)>=0:
                User.users[troop2['uid']].add_kill_num(max(0, troop1['army'][0]['hp']-fight_result['winnerHp'][0])+max(0, troop1['army'][1]['hp']-fight_result['winnerHp'][1]),
                        troop2['army'][0]['hp']+ troop1['army'][1]['hp'],if_country=True, is_win=False)
            else:
                self.add_xyz_kill(User.users[troop2['uid']], max(0, troop1['army'][0]['hp']-fight_result['winnerHp'][0])+max(0, troop1['army'][1]['hp']-fight_result['winnerHp'][1]))
            city.fight['user_logs'][troop2['uid']]['kill'] += (max(0, troop1['army'][0]['hp']-fight_result['winnerHp'][0])+max(0, troop1['army'][1]['hp']-fight_result['winnerHp'][1]))
            city.fight['user_logs'][troop2['uid']]['dead'] += (troop2['army'][0]['hp']+ troop2['army'][1]['hp'])
        troop1_kill_count = (troop2['army'][0]['hp']+troop2['army'][1]['hp'])
        troop2_kill_count = (max(0, troop1['army'][0]['hp']-fight_result['winnerHp'][0])+max(0, troop1['army'][1]['hp']-fight_result['winnerHp'][1]))
        troop1['army'][0]['hp'] = fight_result['winnerHp'][0]
        troop1['army'][1]['hp'] = fight_result['winnerHp'][1]
        if fight_result.get('winnerPuppetHp', None) is not None:
            troop1['puppet']['hp'] = fight_result['winnerPuppetHp']

        if troop1['uid']>0:

            x = 0
            for i in range(len(t1)-1, -1, -1):
                if t1[i]['uid']>0:
                    x = i+1
                    break
            index = max(x, min(len(t1), game_config.fight['troopInsertIndex']))
        else:
            if troop1['proud']>=game_config.world['npcBackProud']-1:
                index = len(t1)
            else:
                index = 0
        t1.insert(index, troop1)

        if troop2['uid']>0 or troop2.get('xtype')=='car':
            if troop2['uid'] in self.troops and self.troops[troop2['uid']].has_key(troop2['hid']):
                del self.troops[troop2['uid']][troop2['hid']]
        elif troop2.get('xtype') in ['country_army', 'patriot_army']:
            if troop2['uid'] in self.troops and self.troops[troop2['uid']].has_key(troop2['hid']):
                self.troops[troop2['uid']][troop2['hid']]['num'] -= 1
                if self.troops[troop2['uid']][troop2['hid']]['num']<=0:
                    del self.troops[troop2['uid']][troop2['hid']]

                    for uid in AppSocket.user_socket_dict:
                        u = User.users[uid]
                        if u.country==troop2['country']:
                            u.write('w.country_army_dead', {'uid': troop2['uid'], 'hid': troop2['hid']})
        else:
            city.troop -= 1

        if troop1['uid']>0 or troop1.get('xtype')=='car':
            if troop1['uid'] in self.troops and self.troops[troop1['uid']].has_key(troop1['hid']):
                self.troops[troop1['uid']][troop1['hid']]['army'][0] = troop1['army'][0]['hp']
                self.troops[troop1['uid']][troop1['hid']]['army'][1] = troop1['army'][1]['hp']

        win = 0

    else:
        troop2_credit = get_credit(troop2,
                troop1['army'][0]['hp']+troop1['army'][1]['hp'],
                troop2['army'][0]['hp']-fight_result['winnerHp'][0],
                troop2['army'][1]['hp']-fight_result['winnerHp'][1],
                troop2['power'], troop1['power'], troop1['uid']<0, False, troop1['official']
                )

        troop1_credit = get_credit(troop1,
                troop2['army'][0]['hp']-fight_result['winnerHp'][0]+troop2['army'][1]['hp']-fight_result['winnerHp'][1],
                troop1['army'][0]['hp'],
                troop1['army'][1]['hp'],
                troop1['power'], troop2['power'], troop2['uid']<0, True, troop2['official']
                )

        if troop1['uid']>0:
            if int(city.cid)>=0:
                User.users[troop1['uid']].add_kill_num(max(0, troop2['army'][0]['hp']-fight_result['winnerHp'][0])+max(0, troop2['army'][1]['hp']-fight_result['winnerHp'][1]),
                        troop1['army'][0]['hp']+ troop1['army'][1]['hp'],if_country=True, is_win=False)
            else:
                self.add_xyz_kill(User.users[troop1['uid']], max(0, troop2['army'][0]['hp']-fight_result['winnerHp'][0])+max(0, troop2['army'][1]['hp']-fight_result['winnerHp'][1]))
            city.fight['user_logs'][troop1['uid']]['kill'] += (max(0, troop2['army'][0]['hp']-fight_result['winnerHp'][0])+max(0, troop2['army'][1]['hp']-fight_result['winnerHp'][1]))
            city.fight['user_logs'][troop1['uid']]['dead'] += (troop1['army'][0]['hp']+ troop1['army'][1]['hp'])
        if troop2['uid']>0:
            if int(city.cid)>=0:
                User.users[troop2['uid']].add_kill_num(troop1['army'][0]['hp']+troop1['army'][1]['hp'],
                        max(0, troop2['army'][0]['hp']-fight_result['winnerHp'][0])+ max(0, troop2['army'][1]['hp']-fight_result['winnerHp'][1]),if_country=True, is_win=True)
            else:
                self.add_xyz_kill(User.users[troop2['uid']], troop1['army'][0]['hp']+troop1['army'][1]['hp'])
            city.fight['user_logs'][troop2['uid']]['kill'] += (troop1['army'][0]['hp']+troop1['army'][1]['hp'])
            city.fight['user_logs'][troop2['uid']]['dead'] += (max(0, troop2['army'][0]['hp']-fight_result['winnerHp'][0])+ max(0, troop2['army'][1]['hp']-fight_result['winnerHp'][1]))


        troop1_kill_count = (max(0, troop2['army'][0]['hp']-fight_result['winnerHp'][0])+max(0, troop2['army'][1]['hp']-fight_result['winnerHp'][1]))
        troop2_kill_count = (troop1['army'][0]['hp']+troop1['army'][1]['hp'])
        troop2['army'][0]['hp'] = fight_result['winnerHp'][0]
        troop2['army'][1]['hp'] = fight_result['winnerHp'][1]
        if fight_result.get('winnerPuppetHp', None) is not None:
            troop2['puppet']['hp'] = fight_result['winnerPuppetHp']

        if troop2['uid']>0:

            x = 0
            for i in range(len(t2)-1, -1, -1):
                if t2[i]['uid']>0:
                    x = i+1
                    break
            index = max(x, min(len(t2), game_config.fight['troopInsertIndex']))
        else:
            if troop2['proud']>=game_config.world['npcBackProud']-1:
                index = len(t2)
            else:
                index = 0
        t2.insert(index, troop2)

        if troop1['uid']>0 or troop1.get('xtype')=='car':
            if troop1['uid'] in self.troops and self.troops[troop1['uid']].has_key(troop1['hid']):
                del self.troops[troop1['uid']][troop1['hid']]
        elif troop1.get('xtype') in ['country_army', 'patriot_army']:
            if troop1['uid'] in self.troops and self.troops[troop1['uid']].has_key(troop1['hid']):
                self.troops[troop1['uid']][troop1['hid']]['num'] -= 1
                if self.troops[troop1['uid']][troop1['hid']]['num']<=0:
                    del self.troops[troop1['uid']][troop1['hid']]

                    for uid in User.users:
                        u = User.users[uid]
                        if u.country==troop1['country']:
                            u.write('w.country_army_dead', {'uid': troop1['uid'], 'hid': troop1['hid']})

        if troop2['uid']>0 or troop2.get('xtype')=='car':
            if troop2['uid'] in self.troops and self.troops[troop2['uid']].has_key(troop2['hid']):
                self.troops[troop2['uid']][troop2['hid']]['army'][0] = troop2['army'][0]['hp']
                self.troops[troop2['uid']][troop2['hid']]['army'][1] = troop2['army'][1]['hp']

        win = 1

    ## 新天下大势数据记录
    if troop1['uid'] > 0:
        self.add_new_milepost_records('player_kill', troop1['country'], troop1_kill_count)
        if troop2['country'] in [6, 17]:
            ## 击杀黄巾军
            self.add_new_milepost_records('player_kill_thief', troop1['country'], troop1_kill_count)
        self.add_new_milepost_records('player_credit', troop1['country'], troop1_credit)
    if troop2['uid'] > 0:
        self.add_new_milepost_records('player_kill', troop2['country'], troop2_kill_count)
        if troop1['country'] in [6, 17]:
            ## 击杀黄巾军
            self.add_new_milepost_records('player_kill_thief', troop2['country'], troop2_kill_count)
        self.add_new_milepost_records('player_credit', troop2['country'], troop2_credit)

    if city.fight.get('fight_data'):
        city.fight['fight_data'][0]['kill_count'] += troop1_kill_count
        city.fight['fight_data'][1]['kill_count'] += troop2_kill_count
        if troop1['uid'] > 0:
            troop1_kill = city.fight['user_logs'][troop1['uid']]['kill']
            if not city.fight['fight_data'][0]['top_kill'] or city.fight['fight_data'][0]['top_kill']['kill'] < troop1_kill:
                city.fight['fight_data'][0]['top_kill'] = {'uid': troop1['uid'], 'head': troop1['head'], 'kill': troop1_kill}
        if troop2['uid'] > 0:
            troop2_kill = city.fight['user_logs'][troop2['uid']]['kill']
            if not city.fight['fight_data'][1]['top_kill'] or city.fight['fight_data'][1]['top_kill']['kill'] < troop2_kill:
                city.fight['fight_data'][1]['top_kill'] = {'uid': troop2['uid'], 'head': troop2['head'], 'kill': troop2_kill}

    if troop1['uid']>0:
        User.users[troop1['uid']].add_credit(troop1_credit,hid=troop1['hid'])
        if int(city.cid)<0:
            self.add_xyz_credit(User.users[troop1['uid']], troop1_credit)
        city.fight['user_logs'][troop1['uid']]['credit'] += troop1_credit
    if troop2['uid']>0:
        User.users[troop2['uid']].add_credit(troop2_credit,hid=troop2['hid'])
        if int(city.cid)<0:
            self.add_xyz_credit(User.users[troop2['uid']], troop2_credit)
        city.fight['user_logs'][troop2['uid']]['credit'] += troop2_credit

    ## 战斗记录
    if battle_id is not None and (troop1['uid'] > 0 and troop2['uid'] > 0):
        city.fight['fight_logs'].append({
            'cid': city.cid,
            'winner': fight_result['winner'],
            'fight_id': city.fight['fight_id'],
            'zone': options.zone,
            'troop1': {
                'uid': troop1['uid'],
                'uname': troop1['uname'],
                'country': troop1['country'],
                'power': troop1['power'],
                'hid': troop1['hid'],
                'hpm': sum([t['hpm'] for t in copyJS['troop'][0]['army']]),  # 总血量
                'bhp': sum([t['hp'] for t in copyJS['troop'][0]['army']]),  # 开战前的血量
                'ahp': fight_result['troop'][0]['hp']   # 战斗结束后的血量
            },
            'troop2': {
                'uid': troop2['uid'],
                'uname': troop2['uname'],
                'country': troop2['country'],
                'power': troop2['power'],
                'hid': troop2['hid'],
                'hpm': sum([t['hpm'] for t in copyJS['troop'][1]['army']]),
                'bhp': sum([t['hp'] for t in copyJS['troop'][1]['army']]),
                'ahp': fight_result['troop'][1]['hp']
            },
            'battle_id': battle_id,
            'fight_index': city.fight['fight_count'],   #战斗场次
            'fight_time': now
        })
        if game_config.system_simple['war_report']['save_log']:
            params = {
                'zone': options.zone,
                'time': now_d,
                'initJS': copyJS,
                'fight_id': city.fight['fight_id'],
                'fight_result': fight_result
            }
            fapi.insert_fight_log(pickle.dumps(params, -1))

    if win==0:
        win_troop = troop1
        dead_troop = troop2
    else:
        win_troop = troop2
        dead_troop = troop1
    try:
        fight_result_troop = fight_result['troop']
        if troop1['uid'] > 0:
            User.users[troop1['uid']].check_new_task('hero_kill', fight_result_troop[1]['dead'], [troop1['hid']])
            #User.users[troop1['uid']].check_new_task('kill_country', 1, [troop2['country']])
        if troop2['uid'] > 0:
            User.users[troop2['uid']].check_new_task('hero_kill', fight_result_troop[0]['dead'], [troop2['hid']])
            #User.users[troop2['uid']].check_new_task('kill_country', 1, [troop1['country']])
        if win_troop['uid'] > 0:
            User.users[win_troop['uid']].check_new_task('kill_country', 1, [dead_troop['country']])
    except Exception as e:
        pass

    win_troop['proud'] += 1
    dead_troop['proud'] += 1
    dead_troop['army'][0]['hp'] = 0
    dead_troop['army'][1]['hp'] = 0

    #战斗胜利或失败推送
    if win_troop['uid']>0:
        try:
            if win == 0:
                User.users[win_troop['uid']].check_new_task('kill_army', 1, [])
            if dead_troop['uid'] > 0:
                User.users[win_troop['uid']].check_new_task('kill_player', 1, [])

        except:
            pass
        city.fight['user_logs'][win_troop['uid']]['attends'][win_troop['hid']] = self.get_troop_hp(win_troop)

        if dead_troop['official']==-2:
            city.fight['user_logs'][win_troop['uid']]['kill_gen'] += 1

        city.fight['user_logs'][win_troop['uid']]['kill_troop'] += 1

        u = User.users[win_troop['uid']]
        # npc 掉落宝物
        """
        npc_type = dead_troop.get('npc_type')
        reward = None
        if npc_type and npc_type in game_config.attack_city_npc:
            reward = game_config.attack_city_npc[npc_type]['reward_'+dead_troop['hid']]
            gift_dict = dict(reward)
            if npc_type == 'thief_one':
                gift_dict = u.festival_reward_interface('attack_city_npc', gift_dict)
        """
        if npc_reward:
            u.give_gifts(npc_reward, 'npc_reward')
            User.add_backup_uid(u.uid)

        try:
            army = self.troops[win_troop['uid']][win_troop['hid']]['army']
        except:
            army = None
        if npc_reward:
            u.write('w.finish_fight', {'city': city.cid, 'hid': win_troop['hid'], 'index': index, 'army': army, 'user':{'hero': u.get_return_hero_dict(win_troop['hid']), 'credit_year': u.credit_year, 'credit': u.credit, 'year_credit': u.year_credit, 'credit_lv': u.credit_lv, 'credit_gift': u.credit_get_gifts, 'coin': u.coin, 'gold': u.gold, 'prop': u.prop, 'records': {'office_credit': u.records['office_credit']}}, 'npc_reward': npc_reward })
        else:
            u.write('w.finish_fight', {'city': city.cid, 'hid': win_troop['hid'], 'index': index, 'army': army, 'user':{'hero': u.get_return_hero_dict(win_troop['hid']), 'credit_year': u.credit_year, 'credit': u.credit, 'year_credit': u.year_credit, 'credit_lv': u.credit_lv, 'credit_gift': u.credit_get_gifts, 'coin': u.coin, 'gold': u.gold, 'records': {'office_credit': u.records['office_credit']}}, 'npc_reward': npc_reward })
    if dead_troop['uid']>0:
        city.fight['user_logs'][dead_troop['uid']]['attends'][dead_troop['hid']] = self.get_troop_hp(dead_troop)

        u = User.users[dead_troop['uid']]
        u.reduce_hero_morale(dead_troop['hid'])
        if npc_reward:
            u.write('w.finish_fight', {'city': city.cid, 'hid': dead_troop['hid'], 'index': -1, 'army': None, 'user':{'hero': u.get_return_hero_dict(dead_troop['hid']), 'credit_year': u.credit_year, 'credit': u.credit, 'year_credit': u.year_credit, 'credit_lv': u.credit_lv, 'credit_gift': u.credit_get_gifts, 'coin': u.coin, 'gold': u.gold, 'prop': u.prop, 'npc_reward': None, 'records': {'office_credit': u.records['office_credit']}} })
        else:
            u.write('w.finish_fight', {'city': city.cid, 'hid': dead_troop['hid'], 'index': -1, 'army': None, 'user':{'hero': u.get_return_hero_dict(dead_troop['hid']), 'credit_year': u.credit_year, 'credit': u.credit, 'year_credit': u.year_credit, 'credit_lv': u.credit_lv, 'credit_gift': u.credit_get_gifts, 'coin': u.coin, 'gold': u.gold, 'npc_reward': None, 'records': {'office_credit': u.records['office_credit']}} })

    if dead_troop.get('xtype')=='car':
        u = User(-1)
        u.country = win_troop['country']
        u.check_chat('kill_ballista', [win_troop['country'], win_troop['official'], win_troop['uname'], dead_troop['country'], dead_troop['car_type']])

    if not (yield self.check_fight_result(city, t1, t2, now, now_d)):
        # 我的部队即将上场
        for i, troop in enumerate(t1):
            if i <= 2 or ((i+1)%10==0 and i!=0):
                if troop['uid']>0:
                    u = User.users[troop['uid']]
                    u.write('w.fight_ready', {'city': city.cid, 'hid': troop['hid'], 'index': i})
        for i, troop in enumerate(t2):
            if i <= 2 or ((i+1)%10==0 and i!=0):
                if troop['uid']>0:
                    u = User.users[troop['uid']]
                    u.write('w.fight_ready', {'city': city.cid, 'hid': troop['hid'], 'index': i})

@gen.coroutine
def troop_move(self, user, params_list, runaway=False, _break=False):
    now = int(time.time())
    hids = []
    t0 = time.time()
    for params in params_list:
        t1 = time.time()
        hid = params['hid']
        hids.append(hid)
        city_list = params['city_list']

        city = self.cities[str(city_list[-1])]
        if not(hid in self.troops[user.uid]):
            raise Model_Error(10436, "不存在的英雄id")
        troop = self.troops[user.uid][hid]

        my_army = yield self.get_troop_fight_init_data([troop])
        app_log.info("##################t1_%s >> %s", user.uid, (time.time()-t1)*1000)
        my_army = my_army[0]

        xyz = self.check_xyz()
        app_log.info("##################t2_%s >> %s", user.uid, (time.time()-t1)*1000)
        if user.country!=city.country:

            if not(my_army['army'][0]['hp']+my_army['army'][1]['hp'] >= (my_army['army'][0]['hpm']+my_army['army'][1]['hpm'])*game_config.world['troop_atk_per']):
                raise Model_Error(10437, "血量不够")

            if not(city.limit!=-1 and user.hero[hid]['lv']>=max(int(world.world_lv*game_config.world['countryFightLvRate']), city.limit)):
                raise Model_Error(10438, "城市不能被攻击或英雄等级不够")

            x = game_config.world['cityType'][str(city.cityType)]['fightTime']
            if x==-1:
                if not(xyz and xyz['status']==1):
                    raise Model_Error(10439, "该时间不能攻击")
            else:
                if not(x!=0 and (x==1 or any([a*60+b<=user.now.hour*60+user.now.minute<=m*60+n for a, b, m, n in x]))):
                    raise Model_Error(10440, "该时间不能攻击")


            if game_config.zone[options.zone][8]:
                if not(User.season_num+1>=city.mergeDay):
                    raise Model_Error(10441, '刚开服，暂时不能攻打')
            else:
                if not(User.season_num+1>=city.day):
                    raise Model_Error(10442, '刚开服，暂时不能攻打')

            if int(city.cid)<0:
                if not(xyz and xyz['status']==1):
                    raise Model_Error(10443, "没有襄阳战或者不能操作")
            else:
                if not(not xyz or xyz['status']!=1):
                    raise Model_Error(10444, "没有襄阳战或者不能操作")
        else:
            if int(city.cid)<0:
                if not(xyz and xyz['status']==1):
                    raise Model_Error(10445, "没有襄阳战或者不能操作")

        if not (runaway or _break):
            if not(troop['status']==0 and self.cities[troop['city']].fight is None):
                raise Model_Error(10446, "英雄部队非待命状态")
        if not(len(city_list)>=2):
            raise Model_Error(10447, "need >=2")
        if not(city_list[0]!=city_list[-1]):
            raise Model_Error(10448, "出发地是目的地")
        if not(str(city_list[0])==troop['city']):
            raise Model_Error(10449, "出发城市不是当前城市")
        for i, cid in enumerate(city_list):
            cid = str(cid)
            if not(cid in self.cities):
                raise Model_Error(10450, "不存在的城市id")
            if i<len(city_list)-1:
                if i!=0:
                    if not(self.cities[cid].country==user.country):
                        raise Model_Error(10451, "途径敌国城市")
                if not(self.get_path_str(cid, str(city_list[i+1])) in game_config.map['path'] or str(city_list[i+1]) in self.get_city_midao(user.country, self.cities[cid])):
                    raise Model_Error(10452, "不存在的城市连接")
        x = my_army['army'][0]['hp']*game_config.army['army_ability'][str(my_army['army'][0]['rank'])][my_army['army'][0]['type']][4]
        y = my_army['army'][1]['hp']*game_config.army['army_ability'][str(my_army['army'][1]['rank'])][my_army['army'][1]['type']][4]
        z = (x+y)*game_config.army['army_food_num']


        z /= (1+user.get_science_gain('army_food'))



        all_food = 0
        for i, c in enumerate(city_list):
            if i<len(city_list)-1:

                p = 1+self.get_two_cities_speed2(user.country, str(c), str(city_list[i+1]))
                s = self.get_two_cities_dis(user.country, c, city_list[i+1])
                res = z*s/p


                all_food += res

        all_food = int(all_food)
        if not(all_food<=user.food):
            raise Model_Error(10453, "移动所需粮草不足: %d" % all_food)
        user.spend_cost(['food', all_food], 'troop_move')
        user.save()

        user_speedup = 0

        hero = user.hero[hid]
        for eid in hero['equip']:
            item = [eid, user.equip[eid]['lv'], user.equip[eid]['wash']]
            gc = game_config.equip[eid]
            for i in range(item[1]+1):
                user_speedup += gc['upgrade'][str(i)].get('army_go', 0)
            for wash in item[2]:
                if not wash or wash not in game_config.equip_wash:
                    continue
                user_speedup += game_config.equip_wash[wash].get('army_go', 0)


        user_speedup += user.get_science_gain('army_go')
        gc = game_config.system_simple['building_army_go']
        user_speedup += gc[1]+gc[2]*user.home[gc[0]]['lv']


        cap = self.cities[self.countries[user.country].capital]
        bid = game_config.city_build['army_go']
        cap_b_lv = cap.build.get(bid, [0, 0])[0]
        if cap_b_lv>0:
            user_speedup += game_config.city_build['buildall'][bid]['effect'][cap_b_lv-1][0]

        for sid, lv in hero['skill'].items():
            a, b = game_config.skill[sid].get('army_go', [0, 0])
            user_speedup += a*lv+b


        if runaway and sum(item['hp'] for item in my_army['army'])>sum(item['hpm'] for item in my_army['army'])*game_config.world['troopRunAway'][0]:
            troop['army'][0] -= int(troop['army'][0]*utils.random_range(game_config.world['troopRunAway'][1], game_config.world['troopRunAway'][2]))
            troop['army'][1] -= int(troop['army'][1]*utils.random_range(game_config.world['troopRunAway'][1], game_config.world['troopRunAway'][2]))

        if runaway:
            hp_p = sum(item['hp'] for item in my_army['army'])*1.0/ sum(item['hpm'] for item in my_army['army'])
            user.check_effort('cum_withdrawal',cond=[hp_p])

        type = 0
        if _break:
            type = 1
        elif runaway:
            type = 2

        troop['data'] = {
                'city_list': [(c, game_config.world['marchSpeedBase']*(1+user_speedup)*(1+self.get_two_cities_speed(user.country, str(c), str(city_list[i+1])))
                    if i<len(city_list)-1 else 0) for i, c in enumerate(city_list)],
                'have_dis': 0,
                'start_time': now,
                'speedup': 1,
                'index': 0,
                'last_time': now,
                'dismiss_time': None,
                'title': User.users[user.uid].hero[hid]['title'],
                'type': type
                }
        group = my_army.get('group', None)
        if group:
            troop['data']['group'] = group
        if user.hero[hid].get('awaken'):
            troop['data']['awaken']=user.hero[hid].get('awaken')
        if user.hero[hid].get('skin_id'):
            troop['data']['skin_id']=user.hero[hid].get('skin_id')
        troop['status'] = 1

        if self.cities[str(city_list[-1])].limit==-1 and self.cities[str(city_list[0])].limit==-1:
            troop['hidden'] = True
        else:
            troop['hidden'] = False

        for uid in User.users:
            u = User.users[uid]
            if not troop.get('hidden') or u.country==User.users[troop['uid']].country:
                if AppSocket.user_socket_dict.get(u.uid):
                    u.write('w.troop_move_push', self.troops[user.uid][hid])

        if type!=0:
            for uid in self.follow_data.get(str(city_list[0]), set()):
                u = User.users[uid]
                u.write('w.exit_fight_follow', {'city': str(city_list[0]), 'side': 1 if self.cities[str(city_list[0])].country==troop['country'] else 0, 'army': {'uid': user.uid, 'hid': hid}})
        app_log.info("##################t3_%s >> %s", user.uid, (time.time()-t1)*1000)

    app_log.info("##################t0_%s >> %s", user.uid, (time.time()-t0)*1000)
    #user.check_task('hero_mobile',1,[hids])
    raise gen.Return({'user': {'food': user.food}})

@classmethod
@gen.coroutine
def FightServerRequest(cls, method, data):
    now = datetime.datetime.now()
    hour = str(now.hour)
    cls.fight_request_ana.setdefault(hour, {})
    cls.fight_request_ana[hour].setdefault(method, {})
    cls.fight_request_ana[hour][method].setdefault('times', 0)
    cls.fight_request_ana[hour][method]['times'] += 1
    http_client = AsyncHTTPClient()
    if method in ('doBattle', ):
        # data['zone'] = options.zone
        data['merge_times'] = game_config.zone[options.zone][8]
    t1 = time.time()
    json_data = json.dumps(data, default=json_default)
    t2 = (time.time()-t1) * 1000
    cls.fight_request_ana[hour][method].setdefault('dumps', 0)
    cls.fight_request_ana[hour][method]['dumps'] += t2
    for item in range(4):
        try:
            res = yield http_client.fetch(settings.BATTLE_URL+"/%s/"%method, method='POST', body=json_data, request_timeout=5)
            break
        except Exception,e:
            print e
            continue
    t3 = time.time()
    resp = json.loads(res.body)
    t3 = (time.time()-t3) * 1000
    cls.fight_request_ana[hour][method].setdefault('loads', 0)
    cls.fight_request_ana[hour][method]['loads'] += t3
    #raise gen.Return(json.loads(res.body))
    raise gen.Return(resp)

def report_invitation_data(self):
    """
    被邀请人上报数据
    """
    try:
	if self.invitation['master']:
	    master_uid = self.invitation['master']
	    if game_config.zone[options.zone][8]:
		master_user = User.users.get(master_uid)
	    else:
		old_maste_zone = str(int(master_uid / settings.UIDADD))
		if old_maste_zone != options.zone:
		    master_user = None
		else:
		    old_master_uid = master_uid % settings.UIDADD
		    master_user = User.users.get(old_master_uid)
	    old_uid, old_zone = User.get_old_uid_zone(self.uid, options.zone)
	    data = {
		'uid': old_uid,
		'zone': old_zone,
		'buildinglv': self.home['building001']['lv'],
	    }
	    pay_money, admin_money, sale_money = self.get_user_pay()
	    data['money'] = pay_money + admin_money
	    if master_user:
		master_user.update_invitation_data(data)
	    else:
		params = {
		    'master_uid': master_uid,
		    'data': data,
		}
		wapi.report_invitation_data(pickle.dumps(params, -1))
    except Exception as e:
	app_log.exception(e)

def check_accept_sys_gift_msg(self):
    """收取邮件
    """
    for item in game_config.player_robot['accept_sys_gift_msg']:
        _check_time = datetime.datetime(*list(self.now.timetuple())[:3]+item)
        if self.now != _check_time:
            continue
        user = User.users[self.uid]
        lost_time = self.now - datetime.timedelta(minutes=game_config.system_simple['losttime_mail'])
        gift_dict_list = []
        for item in user.msg['sys']:
            if item[4] != 0:
                continue
            if item[3] < lost_time:
                continue
            if item[2]:
                gift_dict_list.append(item[2])
            item[4] = 1
        for gift_dict in gift_dict_list:
            user.give_gifts(gift_dict, 'accept_sys_gift_msg')
        app_log.info('AiPlayerRobot accept_sys_gift_msg: %s', self.uid)
        break

def check_self(self):
    #重构
    self_demo = PlayerRobot(None)

    for item in ['records','plan_time']:
        self_attr = getattr(self, item)
        for key, val in getattr(self_demo, item).items():
            if not self_attr.has_key(key):
                self_attr[key] = val
                c = True
        setattr(self, item, self_attr)

@classmethod
@gen.coroutine
def player_robot_run(cls, now_d):
    """陪玩逻辑

    :param now_d: _description_
    """
    player_config = game_config.player_robot
    if player_config.get('ai_switch', 0) != 1:
        raise gen.Return()
    if game_config.zone[options.zone][8]:
        raise gen.Return()
    if options.zone in player_config['ai_zone_blacklist']:
        raise gen.Return()
    open_days = User.season_num + 1
    if open_days < player_config['ai_zone_day'][0] or open_days > player_config['ai_zone_day'][1]:
        raise gen.Return()
    cls.check_user(now_d)
    for uid, player_robot in world.player_robots.items():
        player_robot.now = now_d
        if not player_robot.is_alive:
            player_robot.logout()
            continue
        if cls.is_maintain():
            player_robot.logout()
            continue
        player_robot.check_self()
        player_robot.check_span_day()
        player_robot.check_online()
        if not User.users[uid].is_online:
            continue
        yield player_robot.update_player_data()
        if not player_robot.player_did:
            continue
        player_robot.check_troop_create()
        player_robot.check_troop_move_aim()
        player_robot.check_troop_move()
        player_robot.check_troop_move_speedup()
        player_robot.check_troop_add()
        player_robot.check_climb()
        player_robot.check_pk_user()
        player_robot.check_join_pk_yard()
        player_robot.check_pk_yard_gamble()
        player_robot.check_join_pk_arena()
        player_robot.check_redbag_reward()
        player_robot.check_build_city_build()
        player_robot.check_country_task()
        player_robot.check_club_alien_join_v1()
        player_robot.check_cost_auction()
        player_robot.check_pk_bless()
        player_robot.check_club_alien_chat()
        player_robot.check_accept_sys_gift_msg()
        player_robot.check_hero_install_title()

def check_hero_install_title(self):
    """佩戴称号

    :return: _description_
    """
    if self.plan_time['hero_install_title'] is None:
        return
    if self.now < self.plan_time['hero_install_title']:
        return
    if not utils.random_occur(self.calculation(game_config.player_robot['ai_hero_install_title_rate'])):
        self.update_plan_time('hero_install_title')
        return
    free_title = []
    for k,v in enumerate(self.records['title']):
        if v[1] <= self.now:
            continue
        free_title.append([k, v])
    if not free_title:
        self.update_plan_time('hero_install_title')
        return
    free_hid = None
    user = User.users[self.uid]
    for k,v in sorted(user.hero.items(),key=lambda x:x[1]['power'],reverse=True):
        if v.get('title') and v['title'][1] > self.now:
            continue
        free_hid = k
        break
    if not free_hid:
        self.update_plan_time('hero_install_title')
        return
    title_index, title = random.choice(free_title)
    user.records['title'].pop(title_index)
    user.hero[free_hid]['title'] = title
    self.update_plan_time('hero_install_title')
    app_log.info('AiPlayerRobot hero_install_title: %s, %s, %s', self.uid, free_hid, title)


def check_troop_create(self):
    if self.plan_time['troop_create'] is None:
        return
    if self.now < self.plan_time['troop_create']:
        return
    for k,v in game_config.player_robot['ai_troop_create_bag_num'].items():
        if k < self.now.hour:
            continue
        if k in self.records['troop_create_bag']:
            continue
        self.records['create_troop_num'] += int(self.calculation(v))
    user = User.users[self.uid]
    world.troops.setdefault(self.uid, {})
    hids = []
    for hid,v in sorted(user.hero.items(),key=lambda x:x[1]['power'],reverse=True):
        if self.records['create_troop_num'] <= 0:
            break
        if user.hero_is_adjutant(hid):
            continue
        if hid in world.troops[self.uid]:
            continue
        if game_config.system_simple['troop_que']+user.get_office_right_gain('addtroop') <= len(world.troops[user.uid]):
            break
        morale_config = game_config.system_simple.get('morale',None)
        if morale_config:
            user.reduce_hero_morale(hid, if_reduce=False)
            _morale_time = self.now - datetime.timedelta(seconds=morale_config['consume']*morale_config['recovery'])
            if _morale_time < user.hero[hid]['morale_time']:
                continue
            ## 副将士气
            adjutant_morale = False
            for adjutant_hid in user.hero[hid].get('adjutant', [None, None]):
                if not adjutant_hid:
                    continue
                if _morale_time >= user.hero[adjutant_hid]['morale_time']:
                    continue
                adjutant_morale = True
                break
            if adjutant_morale:
                continue
        self.troop_create(user, hid)
        self.records['create_troop_num'] -= 1
        hids.append(hid)
        app_log.info('AiPlayerRobot troop_create: %s, %s', self.uid, hid)
    self.update_plan_time('troop_create')

def check_span_day(self):
    """跨天数据更新

    :return: _description_
    """
    if self.season_num == User.season_num:
        return
    self._warlike[1] = random.randint(0, game_config.player_robot['ai_warlike_range'][2])
    self._synergy[1] = random.randint(0, game_config.player_robot['ai_synergy_range'][2])
    self._activity[1] = random.randint(0, game_config.player_robot['ai_active_range'][2])
    self.records['join_pk_yard'] = False
    self.records['pk_yard_gamble'] = None
    self.records['create_troop_num'] = int(self.calculation(game_config.player_robot['ai_troop_create_num']))
    self.records['redbag_reward_num'] = int(self.calculation(game_config.player_robot['ai_redbag_reward_num']))
    self.records['auction_index'] = None
    self.records['bless_team_id'] = None
    self.records['troop_create_bag'] = []
    self.season_num = User.season_num

def _get_need_item_num(self, item_id, gift_dict=None, print_log=False):
    """
    获取宝物升级所需的碎片数量
    """
    equip_info = game_config.prop[item_id].get('equip_info')
    if not equip_info:
        return None
    need_num = 0
    # 打印日志用，无实际意义
    eid_lvs = {}

    for eid in equip_info:
        eid_lvs[eid] = self.equip.get(eid, {}).get('lv', 0)
        if eid not in self.equip:
            need_num += game_config.equip[eid].get('make_item', {}).get(item_id, 0)
        for k,v in game_config.equip[eid]['upgrade'].items():
            if eid in self.equip and self.equip[eid]['lv'] >= int(k):
                continue
            need_num += v.get('cost', {}).get(item_id, 0)
    need_num -= self.prop.get(item_id, 0)
    if gift_dict:
        need_num -= gift_dict.get(item_id, 0)
    if need_num < 0:
        need_num = 0
    if print_log:
        app_log.info(">>>peach_draw_need_num<<< uid:%s, item_id:%s, pray_id:%s, eid_lvs:%s, prop:%s, gift_num:%s, need_num:%s", self.uid, item_id, self.peach['pray'], eid_lvs, self.prop.get(item_id, 0), gift_dict.get(item_id, 0) if gift_dict else 0, need_num)
    return need_num

def peach_draw(self, params):
    """
    瑶池抽奖
    """
    self.check_peach()
    if not self.peach:
        raise Model_Error(10704, u'瑶池盛宴未开启')
    open_days = User.season_num + 1
    peach_config = game_config.kunlun['peach']
    merge_times = game_config.zone[options.zone][8]
    buy_times = params['buy_times']
    need_item, need_num = peach_config['use'][0]
    need_num *= buy_times
    need_coin = 0
    if self.prop.get(need_item, 0) < need_num:
        need_coin += peach_config['use'][1] * (need_num - self.prop.get(need_item, 0))
        need_num = self.prop.get(need_item, 0)
    if need_coin:
        self.spend_cost(['coin', need_coin], 'peach_draw')
    if need_num > 0:
        self.spend_cost([need_item, need_num], 'peach_draw')
    well = peach_config['merge_%s' % merge_times]['well'][self.peach['wid']]
    gift_dict_list = []
    gift_dict = {}
    for i in xrange(buy_times):
        _gift_dict = {}
        well_box = None
        well_box_config = peach_config['merge_%s' % merge_times]['well_box'][well[0]]
        for item in well_box_config:
            if self.peach['lucky'] > item[0]:
                continue
            well_box = item
            break
        if well_box is None:
            well_box = well_box_config[-1]
        item_id, add_lucky, item_num = utils.random_choice2(well_box[1])
        # 转换祈福碎片
        if self.peach['pray'] and item_id != self.peach['pray'] and item_id in peach_config['merge_%s' % merge_times]['pray_box'][well[1]]:
            pray_item_need_num = self._get_need_item_num(self.peach['pray'], gift_dict=gift_dict)
            if pray_item_need_num:
                if utils.random_occur(peach_config['pray'][2]):
                    item_id = self.peach['pray']
            else:
                self.peach['pray'] = None
        # 超过宝物所需最大碎片数量以后转换
        need_max_num = self._get_need_item_num(item_id, gift_dict=gift_dict, print_log=True)
        if need_max_num is not None and item_num > need_max_num:
            change_item, change_num = peach_config['change']
            for _ in xrange(item_num - need_max_num):
                _gift_dict.setdefault(change_item, 0)
                _gift_dict[change_item] += change_num
            #print "######", self.prop.get(item_id, 0), item_id, change_item, item_num - need_max_num
            item_num = need_max_num
        if item_num > 0:
            _gift_dict.setdefault(item_id, 0)
            _gift_dict[item_id] += item_num
        self.peach['lucky'] += add_lucky
        gift_dict_list.append(_gift_dict)
        for k,v in _gift_dict.items():
            gift_dict.setdefault(k, 0)
            gift_dict[k] += v
    return_res = self.give_gifts(gift_dict, 'peach_draw')
    return_res['peach'] = self.peach
    return_res['coin'] = self.coin
    return {
            'user': return_res,
            'gift_dict_list': gift_dict_list
            }

def set_guide_condition(self, params):
    key = params['key']
    value = params['value']
    if key not in game_config.guide:
        raise Model_Error(10391, '参数错误')
    if len(value) != 2:
        raise Model_Error(10391, '参数错误')
    for item in value:
        if isinstance(item, (unicode, str)):
            if len(item) > 30:
                raise Model_Error(10391, '参数错误')
        elif isinstance(item, int):
            if len(str(item)) > 10:
                raise Model_Error(10391, '参数错误')
        else:
            raise Model_Error(10391, '参数错误')
    self.records.setdefault('guide_condition', {})
    self.records['guide_condition'][key] = value
    return {'user': {'records': {'guide_condition': self.records['guide_condition']}}}

@gen.coroutine
def run_city_fight(self, cid, now, now_d):
    city = self.cities[cid]

    while city.timeout:
        tt = city.timeout[0]
        if now>=tt[0]:
            getattr(self, tt[1])(*tt[2:])
            city.timeout.pop(0)
        else:
            break

    if city.fight and now>=city.fight['fight_time']:
        #进攻
        t1 = city.fight['team'][0]['troop']
        #防守
        t2 = city.fight['team'][1]['troop']

        xx = city.fight.get('last_battle_result')
        if xx:
            try:
                yield self.get_battle_result(city, t1, t2, now, now_d, xx)
            except:
                app_log.error('get_battle_result Error', exc_info=True)
                troop1, troop2 = xx['initJS']['troop']
                for uid in (troop1['uid'], troop2['uid']):
                    if uid>0:
                        user = User.users[uid]
                        user.write('push_msg', {'code': 1, 'msg': 'battle error, cid: %s' % (city.cid, )})

            if city.fight and 'last_battle_result' in city.fight:
                del city.fight['last_battle_result']
            else:
                raise gen.Return(True)

        if (yield self.check_fight_result(city, t1, t2, now, now_d)):
            raise gen.Return(True)

        #start fight==========================
        troop1 = t1[0]
        troop2 = t2[0]
        #-------------------------------------
        country_log = city.fight['country_logs'][troop1['country']]
        max_official = -100
        level = 0
        for uid, item in city.fight['user_logs'].items():
            if item['country']==troop1['country']:
                if item['official']!=-100:
                    if game_config.country['Official']['minister%s'%item['official']]['level']>level:
                        level = game_config.country['Official']['minister%s'%item['official']]['level']
                        max_official = item['official']
        troop1['others'] = {'attends': copy(city.fight['user_logs'][troop1['uid']]['attends']) if troop1['uid']>0 else {}, 'official': max_official,
                'milepost': country_log['milepost'], 'buff': country_log['buff']}
        if city.cid==game_config.country_pvp['xyz_cityid'][0] and troop1['country'] in (0, 1, 2):
            door = 0
            for _cid in game_config.country_pvp['xyz_cityid'][1]:
                if self.cities[_cid].country==troop1['country']:
                    door += game_config.country_pvp['effect_door'][_cid]
            if door:
                troop1['others']['door'] = door

        if troop1['country'] in (0, 1, 2):
            buffs = User.get_fight_task_buff(troop1['country'])
            if buffs:
                troop1['others']['door'] = game_config.system_simple['fight_task']['task_buff'][0]*buffs

        if troop1['uid']<0:
            if troop1['hid'] in game_config.fight['legendTalentFight']:
                troop1['others']['spirit'] = [[troop1['hid'], troop1.get('hero_star', 0)]]
        else:
            spirit = []
            for troop in t1:
                if troop['uid']==troop1['uid'] and troop['hid'] in game_config.fight['legendTalentFight']:
                    spirit.append([troop['hid'], troop.get('hero_star', 0)])
            if spirit:
                troop1['others']['spirit'] = spirit


        #-------------------------------------
        country_log = city.fight['country_logs'][troop2['country']]
        max_official = -100
        level = 0
        for uid, item in city.fight['user_logs'].items():
            if item['country']==troop2['country']:
                if item['official']!=-100:
                    if game_config.country['Official']['minister%s'%item['official']]['level']>level:
                        level = game_config.country['Official']['minister%s'%item['official']]['level']
                        max_official = item['official']
        troop2['others'] = {'attends': copy(city.fight['user_logs'][troop2['uid']]['attends']) if troop2['uid']>0 else {}, 'official': max_official,
                'milepost': country_log['milepost'], 'buff': country_log['buff'], 'tower': city.fight['tower']}
        if city.cid==game_config.country_pvp['xyz_cityid'][0] and troop2['country'] in (0, 1, 2):
            door = 0
            for _cid in game_config.country_pvp['xyz_cityid'][1]:
                if self.cities[_cid].country==troop2['country']:
                    door += game_config.country_pvp['effect_door'][_cid]
            if door:
                troop2['others']['door'] = door

        if troop2['country'] in (0, 1, 2):
            buffs = User.get_fight_task_buff(troop2['country'])
            if buffs:
                troop2['others']['door'] = game_config.system_simple['fight_task']['task_buff'][0]*buffs

        if troop2['uid']<0:
            if troop2['hid'] in game_config.fight['legendTalentFight']:
                troop2['others']['spirit'] = [[troop2['hid'], troop2.get('hero_star', 0)]]
        else:
            spirit = []
            for troop in t2:
                if troop['uid']==troop2['uid'] and troop['hid'] in game_config.fight['legendTalentFight']:
                    spirit.append([troop['hid'], troop.get('hero_star', 0)])
            if spirit:
                troop2['others']['spirit'] = spirit

        initJS = {'battle_id': self._get_battle_id(cid), 'mode':0,'cid': int(cid), 'rnd': city.fight['rnd'], 'speedUp': city.fight.get('speedUp', 0), 'troop': [troop1, troop2], 'fight_count': city.fight['fight_count']}

        if int(city.cid)<0:
            now_s = now_d.hour*60+now_d.minute
            sunrise_s = game_config.country_pvp['sunrise'][0]*60+game_config.country_pvp['sunrise'][1]
            diff_time = now_s-sunrise_s
            timeScale = None
            for t, rate in game_config.country_pvp['special_time']['fight']:
                if diff_time<=t:
                    timeScale = rate
                    break
            if timeScale:
                initJS['timeScale'] = timeScale
        if city.weather:
            initJS['weather'] = city.weather
        app_log.info("doFight>>: %s <<>> %s", initJS['troop'][0].get('puppet'), initJS['troop'][1].get('puppet'))

        fight_result = yield RequestCollection.FightServerRequest('doFight', initJS)

        #计算宝物掉落
        if fight_result['winner']==0:
            win_troop = troop1
            dead_troop = troop2
        else:
            win_troop = troop2
            dead_troop = troop1
        npc_type = dead_troop.get('npc_type')
        npc_reward = None
        if npc_type and npc_type in game_config.attack_city_npc:
            reward = game_config.attack_city_npc[npc_type]['reward_'+dead_troop['hid']]
            npc_reward = dict(reward)
            if npc_type == 'thief_one':
                npc_reward = User.festival_reward_interface('attack_city_npc', npc_reward)
        initJS['npc_reward'] = npc_reward



        if city.fight.get('speedUp', 0)!=0:
            city.fight['speedUp'] -= 1

        t1.pop(0)
        t2.pop(0)

        if int(cid) < 0:
            now_s = self.fix_minute_time(now_d.hour * 60 + now_d.minute)
            latest_s = self.fix_minute_time(game_config.country_pvp['latest_time'][0] * 60 + game_config.country_pvp['latest_time'][1])
            diff_minute = latest_s - now_s
            quick_minute = max([item[0] for item in game_config.country_pvp['quick_fight']])
            if diff_minute <= quick_minute:
                _add_time = 0
            else:
                _add_time = fight_result['time']
        else:
            _add_time = fight_result['time']
        city.fight['fight_time'] += _add_time
        city.fight['last_battle_result'] = {'city': city.cid, 'result': fight_result, 'initJS': initJS}

        # 国战一场结束
        for uid in self.follow_data.get(city.cid, set()):
            u = User.users[uid]
            u.write('w.finish_fight_follow', {'city': city.cid, 'result': fight_result, 'initJS': initJS})

        #end fight==========================

@gen.coroutine
def on_message(self, data):
    start_time = time.time()

    data = json.loads(data)
    pid = data['pid']
    phone_id = data['phone_id']
    method = data['method']
    params = data['params']
    guide = data.get('guide', None)


    data = None
    now = datetime.datetime.now()
    if_service = False
    # service_config = game_config.system_simple['service_info']
    # service_zone_list = game_config.system_simple['service_zone_list']
    # if service_config[0] == 0 or options.zone in service_zone_list or now < game_config.zone[options.zone][2]:
    #     user_ip = self.request.remote_ip
    #     if method == 'login':
    #         uid = params.get('uid')
    #     else:
    #         uid = self.uid
    #     if not uid:
    #         uid = ''
    #     if user_ip not in service_config[3] and str(uid) not in service_config[3]:
    #         if_service = True
    if method == 'login':
        uid = params.get('uid')
    else:
        uid = self.uid
    if uid and uid in AppSocket.black_user:
        if AppSocket.black_user[uid] > start_time:
            self.close()
            return

    service_maintain = AppSocket.service_maintain
    if service_maintain['full_maintain'] == 0 or service_maintain['zone_maintain'] == 0 or now < game_config.zone[options.zone][2]:
        user_ip = self.request.remote_ip
        if method == 'login':
            uid = params.get('uid')
        else:
            uid = self.uid
        if not uid:
            uid = ''
        if not uid:
            old_uid = ''
        else:
            if game_config.zone[options.zone][8]:
                old_uid = uid%settings.UIDADD
            else:
                old_uid = uid
        if user_ip not in service_maintain['white_list'] and str(uid) not in service_maintain['white_list'] and str(old_uid) not in service_maintain['white_list']:
            if_service = True
    if if_service:
        if service_maintain['full_maintain'] == 0 or service_maintain['zone_maintain'] == 0:
            service_msg = service_maintain['info_msg']['maintain_msg']
        else:
            service_msg = service_maintain['info_msg']['future_msg']
        data = {'msg': service_msg}
        code = 2000

    elif method=='login':
        try:
            pf, is_new, pf_data, data = yield User.login(params)
            code = 0
            if data is None:
                self.close()
                return

            freeze_list = data['records']['freeze']
            if freeze_list[0] == 1 and freeze_list[1] > now:
                data = {'msg': User.users[data['uid']].get_return_msg('freeze_user')}
                code = 18888
            else:
                self.uid = data['uid']
                yield User.users[self.uid].on_login(self)
                ana_data = {}
                if params.get('platform_add_time'):
                    # 多后台同一入口的情况下，传平台注册时间(多个后台最早注册时间)
                    ana_data['platform_add_time'] = datetime.datetime(*time.localtime(params['platform_add_time'])[:6])

                # 充值相关数据不返回或者修改数值
                if game_config.help_msg.get('records_show_max') or game_config.help_msg.get('records_hide'):
                    _records = copy(data['records'])
                    if game_config.help_msg.get('records_show_max'):
                        for k,v in game_config.help_msg['records_show_max'].items():
                            if _records.get(k) > v:
                                _records[k] = v
                    if game_config.help_msg.get('records_hide'):
                        for k in game_config.help_msg['records_hide']:
                            if k in _records:
                                del _records[k]
                    data['records'] = _records
                RequestCollection.AnaServerRequest('ana_index',User.users[self.uid],ana_data)
                data['online_time'] = User.users[self.uid].get_online_time()
                data['world_lv'] = world.world_lv
                data['chat_cache'] = User.users[self.uid].get_chat_cache()

                u = User.users[self.uid]
                u.phone_id = phone_id

                RequestCollection.AdjustRequest(u, 1)

                if pf_data:
                    pf_from = params['pf']
                    pf_data['ip'] = self.request.remote_ip
                    RequestCollection.report_data_37(pf_from, pf_data, 'enter', user=u)

                # if pf=='h5_37' and pf_data:
                #     url = 'http://apigameh5.37.com/index.php?c=enter&a=callback&'
                #     req = {'appid': pf_data['appid'], 'game_id': pf_data['game_id'], 'uid': pf_data['uid'], 'sid': get_37_sid(),
                #             'enter_time': int(time.time()), 'ip': self.request.remote_ip, 'time': int(time.time()), 'guid': pf_data['guid']}
                #     sign_str = '&'.join(k+'='+str(req[k]) for k in sorted(req.keys()) if k!='sign')+'&'+'MS(3_7d@Gv.)28y*.62hW77Zh(qQHr@#'
                #     req['sign']=md5(sign_str).hexdigest()
                #     url += '&'.join([('%s=%s'%(k, v)) for k, v in req.items()])
                #     AsyncHTTPClient().fetch(url, method='GET')
        except Exception as e:
            app_log.error( 'Error', exc_info=True)

    else:
        if self.uid:

            if method in ['w.troop_create', 'w.troop_break', 'w.troop_runaway', 'w.troop_move', 'w.troop_add',
                          'grab_mining', 'hero_catch_pk', 'do_ftask', 'do_gtask', 'occupy_estate', 'do_guide',
                          'jump_guide', 'get_telbind_reward', 'get_reward_by_code', 'hero_install_adjutant',
                          'hero_skill_lv_up', 'climb', 'pve_combat', 'get_pk_npc', 'pk_npc_fight',
                          'pk_npc_captain_fight', 'pk_user',
                          'get_club_alien', 'club_alien_join',
                          'get_club_alien_v1', 'club_alien_join_v1',
                          'club_alien_quit_v1', 'get_alien_reward_v1',
                          'drop_alien_reward_v1', 'building_lvup',
                          'legend_combat', 'w.modify_country_notice', 'w.follow_fight', 'w.unfollow_fight',
                          'w.troop_move_speedup', 'update_user', 'w.build_ballista', 'user_info', 'get_fight_log',
                          'get_user_fight_log', 'get_battle_data', 'w.get_city_info', 'w.get_my_troops',
                          'w.get_info', 'w.get_user_list', 'get_credit_rank', 'get_pk_user', 'get_pk_yard_log',
                          'kill_army_cd', 'build_city_build', 'get_grab_user', 'get_pk_user', 'w.get_hero_count',
                          'w.get_new_milepost_reward', 'w.get_new_milepost_record', 'reset_red_dot_attr',
                          'get_random_uname', 'w.speed_up_fight',
                          'notice_expired_title', 'get_city_fight_log', 'receive_receipt', 'get_year_attr',
                          'w.get_hero', 'put_homeland_building', 'reput_homeland_building',
                          'recovery_homeland_building', 'copy_building_plan', 'use_building_plan',
                          'get_building_plan', 'build_homeland_building',
                          'save_building_plan', 'get_idol_login_reward',
                          'choice_gwent_dungeon', 'reset_gwent_dungeon',
                          'wind_up_spot_fight', 'get_user_homeland',
                          'choice_gwent_heros',
                          'reset_gwent_dungeon', 'choice_gwent_spot',
                          'choice_gwent_secret_spot',
                          'settle_gwent_spot_event', 'view_gwent_spot',
                          'settle_end_boss',
                          'get_gwent_task_reward', 'add_karma_exp',
                          'revived_hero_suffering_credit_on',
                          'revived_assign_attr', 'revived_reset_suffering',
                          'add_doom_type_exp', 'get_day_food_reward',
                          'buy_gold_food', 'get_doom_tower_task_reward',
                          'get_invitation_reward', 'get_msg', 'hero_sp_army_install', 'get_city_visit',
                          'get_my_pk_yard_hids', 'get_fight_task',
                          'get_building_material', 'get_online_reward',
                          'get_science', 'hero_install_god', 'hero_uninstall_god',
                          'god_uninstall_equip', 'god_skill_forget', 'god_recovery', 'god_change_lock',
                          'wilderness_settle', 'wilderness_hook_reward',
                          'get_country_boss', 'attack_country_boss',
                          'get_wilderness', 'cost_god_sale', 'get_god_sale',
                          'get_country_boss_life_reward', 'peach_get',
                          'peach_pray', 'peach_draw', 'peach_buy',
                          'get_credit_gift_v1',  'set_guide_condition',
                          'pk_yard_new_join', 'pk_yard_new_gamble',
                          'pk_yard_new_worship',
                          'get_pk_yard_new_group_enemy',
                          'get_pk_yard_new_group_rank', 'get_pk_yard_new_finals',
                          'get_pk_yard_new_finals_rank',
                          'get_pk_yard_new_log', 'get_pk_yard_new_hids',
                          'city_control_start', 'city_control_end',
            'get_dawanka_info', 'get_dawanka_reward', 'get_ng_task',
            'receive_ng_task', 'refresh_ng_task', 'get_ng_task_reward',
            'get_ng_task_smf_reward', 'quick_ng_task_reward',
            'buy_limit_free', 'get_dog_reward', 'elephant_combat',
            'hero_star_install', 'hero_star_uninstall',
            'cabinet_install_hero', 'build_pk_npc', 'get_drink', 'drink_pk',
            'drink_answer', 'drink_hero_like_reward', 'buy_ng_task_times',
            'drink_buy_pk_times', 'office37_class_lvup',
            'office37_right_active', 'touhu_free_reward', 'touhu_draw',
            'touhu_reward', 'touhu_refresh', 'puppet_activate',
            'hero_install_puppet', 'hero_uninstall_puppet',
            'puppet_install_arm', 'puppet_uninstall_arm',
            'puppet_arm_recovery', 'puppet_arm_lock',
            'puppet_building_get_arm', 'puppet_building_lvup',
            'puppet_gather_get', 'puppet_building_gather_get',
            'puppet_arm_recast', 'puppet_indent_submit',
            'puppet_rep_lvup_gift',
            'puppet_building_make_speed', 'puppet_arm_refine_strengthen',
            'puppet_arm_refine_raising', 'puppet_arm_refine_raising_use',
            'puppet_arm_refine_raising_cancel',
            'storeroom_open_reward_get', 'storeroom_country_indent_get',
            'storeroom_country_indent_submit', 'story_dungeon_begin',
            'story_dungeon_reward_get', 'storeroom_year_reward_get',
            'story_goal_reward_get', 'god_police_roll', 'god_police_send',
            'god_police_get', 'god_police_task_get', 'god_police_shop_buy',
            'estate_event_reward', 'estate_active_harvest',
            'god_police_consolation_get', 'get_rank_by_type', 'get_shop',
            'user_refresh_shop', 'buy_shop', 'hero_resolve', 'dig_tomb_map',
            'get_tomb_mattock', 'buy_tomb_mattock', 'get_tomb_map_reward',
            'get_records', 'get_member_reward', 'get_equip_cd',
            'get_champion_user', 'get_ftask_city_open_reward',
            'get_club_redbag_reward', 'get_club_redbag', 'hero_lv_up',
            'del_credit_settle', 'del_quota_gift',
            'get_festival_login_reward', 'get_gtask', 'receive_gtask',
            'get_pk_user', 'w.get_users', 'estate_harvest',
            'get_festival_awaken_shop_reward', 'get_hero_catch',
            'hero_catch_refresh', 'buy_hero_catch_pk_times',
            'refresh_gtask', 'do_gtask', 'get_gtask_reward',
            'get_festival_pitup_reward', 'get_festival_addup_reward',
            'get_festival_login_reward', 'get_festival_once_pay_reward',
            'city_build_reward', 'estate_build_visit_reward',
            'kill_city_build_cd', 'get_building_army', 'club_alien_join',
            'get_honour_rank', 'get_pk_user', 'get_climb_rank',
            'get_alien_reward', 'get_grab_log', 'power_lv_up'
            ]:
                user = User.users[self.uid]
            else:
                user = copy(User.users[self.uid])
                #app_log.info('Copy User: %s %s', self.uid, int((time.time()-start_time)*1000))
                #user = User.users[self.uid].copy_self()

            freeze_list = user.records['freeze']
            if freeze_list[0] == 1 and freeze_list[1] > now:
                self.close()
                return
            user.now = now
            user.phone_id = phone_id

            is_w = False

            try:
                if method.startswith('w.'):
                    m = getattr(world, method[2:])
                    is_w = True
                else:
                    m = getattr(user, method)

                if isinstance(params, dict) and isinstance(params.get('pf_data'), dict):
                    params['pf_data']['ip'] = self.request.remote_ip

                if method=='choose_country':
                    params['ip'] = self.request.remote_ip
                elif method=='chat':
                    params['ip'] = self.request.remote_ip
                elif method=='send_user_msg':
                    params['ip'] = self.request.remote_ip
                if is_w:
                    data = m(user, params)
                else:
                    data = m(params)

                if type(data)==gen.Future:
                    data = yield data
                # 充值相关数据不返回或者修改数值
                if game_config.help_msg.get('records_show_max') or game_config.help_msg.get('records_hide'):
                    if isinstance(data, dict) and data.get('user') and data['user'].get('records'):
                        records = copy(data['user']['records'])
                        if game_config.help_msg.get('records_show_max'):
                            for k,v in game_config.help_msg['records_show_max'].items():
                                if records.get(k) > v:
                                    records[k] = v
                        if game_config.help_msg.get('records_hide'):
                            for k in game_config.help_msg['records_hide']:
                                if k in records:
                                    del records[k]
                        data['user']['records'] = records
                user = User.users[self.uid]

                if user.records['up_power_hids'] is not None:
                    yield user.update_hero_power()
                if user.records['push_task'] is not None:
                    user.write('push_task', {'user':{'task': user.get_full_task()}})
                    user.records['push_task'] = None
                if user.records['push_new_task'] is not None:
                    user.write('push_new_task', {'user':{'new_task': user.new_task}})
                    user.records['push_new_task'] = None
                if user.records['push_effort'] is not None:
                    user.write('push_effort', {'user':{'effort': user.effort}})
                    user.records['push_effort'] = None

                if guide:
                    #user = User.users[self.uid]
                    user.do_guide(guide)
                code = 0
            except Model_Error as e:
                try:
                    server_raise_pf = game_config.system_simple['server_raise_pf'].get(user.pf,None)
                    if server_raise_pf:
                        server_raise_msg = getattr(game_config, 'server_raise_msg_%s' % server_raise_pf)
                    else:
                        server_raise_msg = game_config.server_raise_msg
                    code = e.code
                    err_msg = server_raise_msg.get(str(code), e.msg)
                    data = {'msg': err_msg}
                except Exception as e:
                    app_log.error( 'Error', exc_info=True)
                    data = {'msg': e.message}
                    code = 500
            except AssertionError as e:
                data = {'msg': e.message}
                code = 110
            except Exception as e:
                app_log.error( 'Error', exc_info=True)
                data = {'msg': e.message}
                code = 500

        else:
            self.close()
            return

    return_res = {'method': method, 'code': code, 'pid': pid, 'data': data, 'time': datetime.datetime.now()}
    try:
        return_json_res = json.dumps(return_res, default=json_default)
        self.write_message(return_json_res)
        r_len = len(return_json_res)
    except:
        app_log.error( 'Error', exc_info=True)
        r_len = 0

    app_log.info('Message: %s, %s, %s, %s, %s, %s, %s, %s', int((time.time()-start_time)*1000), r_len, self.uid, method, self.request.remote_ip, options.zone, params, guide)

    # 接口防刷处理
    if self.uid and game_config.help_msg.get('api_malice_req_switch'):
        now_time = int(start_time)
        duration = game_config.help_msg['api_malice_req_duration']
        start_t = now_time - (duration + 1)
        stop_t = now_time - 1
        limit = game_config.help_msg['api_malice_req_limit']
        duration_second_unsafe = []
        AppSocket.req_times.setdefault(self.uid, {})
        for i in xrange(start_t, stop_t):
            if len(AppSocket.req_times[self.uid].get(str(i), [])) <= limit:
                duration_second_unsafe.append(False)
            else:
                duration_second_unsafe.append(True)
        if all(duration_second_unsafe):
            AppSocket.black_user[self.uid] = now_time + game_config.help_msg.get('api_malice_freeze_seconds', 3600)
        else:
            # 删除超时数据
            for k in AppSocket.req_times[self.uid].keys():
                if int(k) < start_t:
                    del AppSocket.req_times[self.uid][k]
            AppSocket.req_times[self.uid].setdefault(str(now_time), [])
            AppSocket.req_times[self.uid][str(now_time)].append(method)

def _get_power_lv_gain(self, pg_type, need=[]):
    """
    获取战阶等级对应的增益效果
    """
    gain = 0
    power_lv_config = game_config.system_simple['power_level']
    merge_times = game_config.zone[options.zone][8]
    if merge_times < power_lv_config['switch']:
        return gain
    self.records.setdefault('power_lv', 0)
    power_lv = self.records['power_lv']
    if power_lv <= 0:
        return gain
    gain_config = power_lv_config['power_level_data'][power_lv-1]['effect'].get(pg_type, None)
    if not gain_config:
        return gain
    if need:
        if gain_config[0] != 'all' and gain_config[0] != need[0]:
            return gain
    return gain_config[-1]


def power_lv_up(self, params):
    """
    战阶升级
    """
    power_lv_config = game_config.system_simple['power_level']
    merge_times = game_config.zone[options.zone][8]
    if merge_times < power_lv_config['switch']:
        raise Model_Error(10613, u'功能未开启')
    try:
        power_lv_limit = power_lv_config['power_level_limit'][merge_times]
    except IndexError:
        power_lv_limit = power_lv_config['power_level_limit'][-1]
    self.records.setdefault('power_lv', 0)
    power_lv = self.records['power_lv']
    if power_lv >= power_lv_limit:
        raise Model_Error(10024, u'已到达等级上限')
    need_power = power_lv_config['power_level_data'][power_lv]['power']
    if self.power < need_power:
        raise Model_Error(10159, u'升级条件不满足')
    gift_dict = power_lv_config['power_level_data'][power_lv]['reward']
    return_res = self.give_gifts(gift_dict, 'power_lv_up')
    self.records['power_lv'] += 1
    return_res['records'] = {'power_lv': self.records['power_lv']}
    return {'user': return_res, 'gift_dict': gift_dict}

@gen.coroutine
def troop_create(self, user, params):
    hid = params['hid']
    is_pay = params.get('is_pay', False)
    is_xyz = params.get('is_xyz', False)
    if is_xyz:
        xyz = self.check_xyz()
        if not(xyz and xyz['status']==1):
            raise Model_Error(10419, "没有襄阳战或者不能操作")
    if not(hid in user.hero):
        raise Model_Error(10420, "不存在的英雄id")
    self.troops.setdefault(user.uid, {})
    if self.troops[user.uid].has_key(hid):
        raise Model_Error(10421, "英雄部队非未编状态")
    if user.hero_is_adjutant(hid):
        raise Model_Error(10422, "副将英雄不可编队")
    if game_config.system_simple['troop_que']+user.get_office_right_gain('addtroop') <= len(self.troops[user.uid]):
        raise Model_Error(10423, '部队数已达上限')

    morale_config = game_config.system_simple.get('morale',None)
    if morale_config:
        user.reduce_hero_morale(hid,if_reduce=False)
        now = datetime.datetime.now()
        _morale_time = now - datetime.timedelta(seconds=morale_config['consume']*morale_config['recovery'])
        if _morale_time < user.hero[hid]['morale_time']:
            raise Model_Error(10632, '士气不足')
        ## 副将士气
        for adjutant_hid in user.hero[hid].get('adjutant', [None, None]):
            if not adjutant_hid:
                continue
            if _morale_time < user.hero[adjutant_hid]['morale_time']:
                raise Model_Error(10632, '士气不足')
    #aa, bb = [item['hpm'] for item in init_data[0]['army']]
    aa = user.hero[hid]['hpm0']
    bb = user.hero[hid]['hpm1']
    i, j = game_config.hero[hid]['army']

    if is_pay:
        new_food = new_wood = new_iron = 0
        for bid, army_mk_num in [('building%03d'% (9+i), aa), ('building%03d' % (9+j), bb)]:
            assert user.home[bid]['lv']>=1
            barracks_config = game_config.system_simple['barracks'][bid][user.home[bid]['lv']-1]
            y = game_config.system_simple['fast_num'][user.home[bid]['lv']-1]
            army_ability = game_config.army['army_ability'][str(barracks_config[3])][Army_type[bid]]

            multiple = user.get_office_right_gain('traincost')
            multiple += user.get_science_gain('army_consume')
            multiple += user._get_power_lv_gain('army_consume')
            new_food += int(army_ability[5]*army_mk_num*y*1.0/10*(1-multiple))
            new_wood += int(army_ability[6]*army_mk_num*y*1.0/10*(1-multiple))
            new_iron += int(army_ability[7]*army_mk_num*y*1.0/10*(1-multiple))

        new_wood = int(new_wood)
        new_food = int(new_food)
        new_iron = int(new_iron)

        o, p, q = game_config.system_simple['fast_train_cost']
        food = int(new_food*1.0/o+new_wood*1.0/p+new_iron*1.0/q+1)
        if user.records['troop_add']['season_num']!=User.season_num:
            user.records['troop_add'] = {'season_num': User.season_num, 'food': 0}
        for v, r in game_config.system_simple['fast_level'][::-1]:
            if user.records['troop_add']['food']>=v:
                food  = int(food*r)
                break
        user.records['troop_add']['food'] += food

        user.spend_cost([game_config.system_simple['fast_train_type'], food], 'troop_add')
        army = [aa, bb]
    else:
        a, b = user.home['building%03d'% (9+i)]['army_num'], user.home['building%03d' % (9+j)]['army_num']
        if not(a+b>0):
            raise Model_Error(10424, '可用兵力为0')
        user.home['building%03d' % (9+i)]['army_num'] -= min(a, aa)
        user.home['building%03d' % (9+j)]['army_num'] -= min(b, bb)
        army = [min(a, aa), min(b, bb)]

    if not is_xyz:
        cid = self.countries[user.country].capital
    else:
        cid = game_config.country_pvp['xyz_cityid'][2][user.country]
    self.troops[user.uid][hid] = {'country': user.country, 'uid': user.uid, 'hid': hid, 'city': cid, 'status': 0, 'army': army, 'data': None}
    #状态： 0 待命/备战/战斗, 1, 行军 2，撤回 3， 等待行军
    user.check_task('have_force',0,[])
    user.check_effort('have_force')
    user.save()

    raise gen.Return({'user': {'home': user.home, game_config.system_simple['fast_train_type']: getattr(user, game_config.system_simple['fast_train_type']), 'records': user.records}, 'hp': army})

@gen.coroutine
def troop_add(self, user, params):
    '''
    该城市无法补兵；
    部队兵力已满；
    封地无兵可补；
    '''
    hid = params['hid']
    is_pay = params.get('is_pay', False)

    if not(hid in self.troops[user.uid]):
        raise Model_Error(10454, "不存在的英雄id")
    troop = self.troops[user.uid][hid]
    city = self.cities[troop['city']]

    if not(city.fight is None and troop['status']==0):
        raise Model_Error(10455, "英雄部队非待命状态")


    city_config = game_config.city[city.cid]
    if city_config.get('pctask_id',None) and user.ftask.get(city.cid,[0])[0] != -1:
        raise Model_Error(10456, u'民情未完成')

    if city.cityType!=7:
        bid, lv = game_config.city_build['troop1']
        if not(city.build.get(bid, [0, 0])[0]>=lv):
            raise Model_Error(10457, "无法补兵")

    xyz = self.check_xyz()
    if int(city.cid)<0:
        if not(xyz and xyz['status']==1):
            raise Model_Error(10458, "没有襄阳战或者不能操作")

    gc = game_config.country['buff_country5']
    if not('buff_country5' not in city.buffs or user.now>city.buffs['buff_country5'][2]+datetime.timedelta(minutes=gc['time'])):
        raise Model_Error(10459, '禁兵令无法补兵')

    prepare_data = user.get_fight_prepare_data(hid)
    i, j = game_config.hero[hid]['army']
    aa = user.hero[hid]['hpm0']
    bb = user.hero[hid]['hpm1']
    #init_data = yield RequestCollection.FightServerRequest('data',[prepare_data])
    #aa, bb = [item['hpm'] for item in init_data[0]['army']]
    _a, _b = troop['army']

    if is_pay:
        new_food = new_wood = new_iron = 0
        for bid, army_mk_num in [('building%03d'% (9+i), aa-_a), ('building%03d' % (9+j), bb-_b)]:
            assert user.home[bid]['lv']>=1
            barracks_config = game_config.system_simple['barracks'][bid][user.home[bid]['lv']-1]
            x = game_config.system_simple['fast_num'][user.home[bid]['lv']-1]

            army_ability = game_config.army['army_ability'][str(barracks_config[3])][Army_type[bid]]
            multiple = user.get_office_right_gain('traincost')
            multiple += user.get_science_gain('army_consume')
            multiple += user._get_power_lv_gain('army_consume')
            new_food += int(army_ability[5]*army_mk_num*x*1.0/10*(1-multiple))
            new_wood += int(army_ability[6]*army_mk_num*x*1.0/10*(1-multiple))
            new_iron += int(army_ability[7]*army_mk_num*x*1.0/10*(1-multiple))

        new_wood = int(new_wood)
        new_food = int(new_food)
        new_iron = int(new_iron)

        o, p, q = game_config.system_simple['fast_train_cost']
        food = int(new_food*1.0/o+new_wood*1.0/p+new_iron*1.0/q+1)

        if user.records['troop_add']['season_num']!=User.season_num:
            user.records['troop_add'] = {'season_num': User.season_num, 'food': 0}
        for v, r in game_config.system_simple['fast_level'][::-1]:
            if user.records['troop_add']['food']>=v:
                food  = int(food*r)
                break
        user.records['troop_add']['food'] += food
        troop['army'] = [aa, bb]
        user.spend_cost([game_config.system_simple['fast_train_type'], food], 'troop_add')
    else:
        a, b = user.home['building%03d'% (9+i)]['army_num'], user.home['building%03d' % (9+j)]['army_num']
        troop['army'] = [min(aa, a+troop['army'][0]), min(bb, b+troop['army'][1])]
        a, b = troop['army']
        user.home['building%03d'%(9+i)]['army_num'] -= max(a-_a, 0)
        user.home['building%03d'%(9+j)]['army_num'] -= max(b-_b, 0)

    user.save()
    raise gen.Return({'user': {'home': user.home, game_config.system_simple['fast_train_type']: getattr(user, game_config.system_simple['fast_train_type']), 'records': user.records}, 'hp': troop['army']})

def get_building_material(self, params):
    material_key = ['gold', 'food', 'wood', 'iron']
    building_config = game_config.home

    material_gift_cd = game_config.system_simple['material_gift_cd'][1]
    if params['produce'] == -1:
        produce_list = [0,1,2,3]
    else:
        produce_list = [params['produce']]
    gift_dict = {}
    cabinet_add = [0, 0, 0, 0] # 内政府额外产量
    for k,v in building_config.items():
        if not v.has_key('produce'):
            continue
        if v['produce'] not in produce_list:
            continue
        produce = v['produce']

        if self.home[k]['lv'] < 1:
            continue
        material_gift = game_config.system_simple['material_gift'][self.home[k]['lv']-1]
        cd_num = 0
        while 1:
            if (self.home[k]['material_time']+datetime.timedelta(minutes=material_gift_cd)) > self.now:
                break
            self.home[k]['material_time'] += datetime.timedelta(minutes=material_gift_cd)
            cd_num += 1
        if cd_num >= game_config.system_simple['material_gift_limit'][1]:
            cd_num = game_config.system_simple['material_gift_limit'][1]
        self.home[k]['material_time'] = self.now
        material_scale = game_config.system_simple['material_scale'][produce]
        gift_dict.setdefault(material_key[produce],0)
        if game_config.system_simple.get('use_cabinet', 0) == 1:
            # 内阁产量加成
            capacity_config = []
            for item in game_config.system_simple['cabinet']['cabinet']['capacity']:
                if item[0] != produce:
                    continue
                capacity_config = item[1]
                break
            for hid in self.cabinet[produce]:
                if not hid:
                    continue
                pol = self.hero[hid].get('attrs', {}).get('pol', 0)
                for i in capacity_config[::-1]:
                    if pol < i[0]:
                        continue
                    cabinet_add[produce] += i[1]*cd_num
                    break
        gift_dict[material_key[produce]] += material_gift*cd_num*material_scale


    for produce,gift_key in enumerate(material_key):
        if not gift_dict.has_key(gift_key):
            continue
        multiple = self.get_office_right_gain('home%s' % material_key[produce])
        multiple += self.get_science_gain('fief_produce',need=[material_key[produce]])
        multiple += self._get_power_lv_gain('fief_produce',need=[material_key[produce]])
        gift_dict[gift_key]= int(math.ceil(gift_dict[gift_key]+gift_dict[gift_key]*multiple+cabinet_add[produce]))
    self.give_gifts(gift_dict,'get_building_material')
    return {
            'user': {
                'home': self.home,
                'gold': self.gold,
                'food': self.food,
                'wood': self.wood,
                'iron': self.iron,
                },
            'gift_dict': gift_dict,
            }

def estate_harvest(self, params):
    estate_config = game_config.estate
    hv_time  = game_config.system_simple['material_gift_cd'][0]
    hv = game_config.system_simple['material_gift_limit'][0]
    gift_dict = {}
    #爵位特权加成
    multiple = self.get_office_right_gain('syield')
    #############
    city_config = game_config.city
    for estate in self.estate:
        estate_id,estate_lv  = city_config[estate['city_id']]['estate'][estate['estate_index']][:2]
        if not estate['harvest_time']:
            continue
        harvest_num = utils.total_seconds(self.now-estate['harvest_time'])/(60*hv_time)
        if harvest_num >= hv:
            harvest_num = hv
            estate['harvest_time'] = self.now
        else:
            estate['harvest_time'] += datetime.timedelta(minutes=harvest_num*hv_time)
        if harvest_num == 0:
            continue

        gift_key = estate_config['estate'][estate_id]['produce']
        ratio = estate_config['estate'][estate_id]['ratio']
        passive = estate_config['passive'][estate_lv-1]
        gift_num = int(harvest_num*ratio*passive)
        ###科技加成
        science_multiple = self.get_science_gain('estate_produce',need=[estate_id])
        ###战阶加成
        power_lv_multiple = self._get_power_lv_gain('estate_produce',need=[estate_id])
        gift_num = gift_num+int(gift_num*(multiple+science_multiple+power_lv_multiple))
        if gift_dict.has_key(gift_key):
            gift_dict[gift_key] += gift_num
        else:
            gift_dict[gift_key] = gift_num

    return_res = self.give_gifts(gift_dict,'estate_harvest')
    self.save()
    return_res['estate'] = self.estate
    return {
            'user': return_res,
            'gift_dict': gift_dict,
            }

def building_make_army(self, params, auto=False):
    """
    不要放到copy黑名单中
    """
    self = copy(self)
    bid = params['bid']
    if_cost = params['if_cost']
    army_mk_num = params['army_mk_num']
    if not auto and (army_mk_num % 10 != 0 or army_mk_num < 100):
        raise Model_Error(10051, u'数量错误')
    self.check_building_cd()
    if self.building_cd.has_key(bid):
        raise Model_Error(10052, u'兵营繁忙')
    if self.home[bid]['army_time'] > self.now:
        raise Model_Error(10053, u'兵营繁忙')
    if self.home[bid]['army_mk_num']:
        raise Model_Error(10054, u'兵营繁忙')
    barracks_config = game_config.system_simple['barracks'][bid][self.home[bid]['lv']-1]
    army_num_limit = barracks_config[2]
    army_stock = self.get_science_gain('army_stock',need=[bid])
    army_stock += self._get_power_lv_gain('army_stock',need=[bid])
    army_stock += self.get_office_right_gain('addarmylimit', need=[bid])
    army_num_limit += int(army_num_limit *army_stock)
    if self.home[bid]['army_num'] >= army_num_limit:
        raise Model_Error(10055, u'兵营已满')
    army_mk_num_limit = barracks_config[1]
    army_add = self.get_science_gain('army_add',need=[bid])
    army_add += self._get_power_lv_gain('army_add',need=[bid])
    army_mk_num_limit += int(army_mk_num_limit*army_add)
    if army_mk_num > army_mk_num_limit:
        raise Model_Error(10056, u'超出训练兵数上限')
    self.home[bid]['army_mk_num'] = army_mk_num
    need_seconds = army_mk_num/10*barracks_config[0]
    time_multiple = self.get_science_gain('army_rate',need=[bid])
    time_multiple += self._get_power_lv_gain('army_rate',need=[bid])
    time_multiple += self.get_buff_effect()
    need_seconds = int(math.ceil(float(need_seconds)/(1*(time_multiple+1))))
    if if_cost:
        cd_cost = game_config.system_simple['cd_cost'][3]
        cost_coin = int(math.ceil(need_seconds/60.0/float(cd_cost)))
        self.spend_cost(['coin',cost_coin], 'building_make_army')
        self.home[bid]['army_time'] = self.now
    else:
        self.home[bid]['army_time'] = self.now + datetime.timedelta(seconds=need_seconds)
    army_ability = game_config.army['army_ability'][str(barracks_config[3])][Army_type[bid]]
    multiple = self.get_office_right_gain('traincost')
    multiple += self.get_science_gain('army_consume')
    multiple += self._get_power_lv_gain('army_consume')

    need_food = int(army_ability[5]*army_mk_num/10*(1-multiple))
    need_wood = int(army_ability[6]*army_mk_num/10*(1-multiple))
    need_iron = int(army_ability[7]*army_mk_num/10*(1-multiple))

    self.spend_cost(['food', need_food], 'building_make_army')
    self.spend_cost(['wood', need_wood], 'building_make_army')
    self.spend_cost(['iron', need_iron], 'building_make_army')
    self.total_records['army_%s' % Army_type[bid]] += army_mk_num

    self.check_task('cum_train_soldier', 0, [Army_type[bid]])
    self.check_effort('cum_train_soldier', cond=[Army_type[bid]])
    self.check_task('train_soldier', army_mk_num, [Army_type[bid]])
    ## 新天下大势数据记录
    world.add_new_milepost_records('player_train', self.country, army_mk_num)

    world.countries[self.country].total_records['army_%s' % Army_type[bid]] += army_mk_num
    self.check_task('cum_country_trainedsoldiers', 0, [Army_type[bid]])
    self.check_effort('train_time', cond=[need_seconds])
    self.check_effort('army_num')
    self.check_happy_sparta('training',cond=[army_mk_num, barracks_config[3]])
    if auto==-1:
        self.home[bid]['army_num'] += self.home[bid]['army_mk_num']
        self.home[bid]['army_mk_num'] = 0
        self.home[bid]['army_time'] = self.now
        self.save()
        return self
    if auto and auto >0:
        self.home[bid]['army_time'] -= datetime.timedelta(seconds=auto)
        self.save()
        return self

    self.save()
    return {
            'user': {
                'coin': self.coin,
                'food': self.food,
                'wood': self.wood,
                'iron': self.iron,
                'home': self.home,
                'total_records': self.total_records,
                },
            }

def building_auto_make_army(self, bid):
    now = datetime.datetime.now()
    self.now = now
    building_time = self.home[bid]['army_time']
    if self.home[bid]['army_time'] <= self.now:
        self.home[bid]['army_num'] += self.home[bid]['army_mk_num']
        self.home[bid]['army_mk_num'] = 0
    if self.building_cd.has_key(bid):
        if self.building_cd[bid] > building_time:
            building_time = self.building_cd[bid]
    if now > building_time:
        barracks_config = game_config.system_simple['barracks'][bid][self.home[bid]['lv']-1]
        total_seconds = utils.total_seconds(now-building_time)
        army_mk_num_limit = barracks_config[1]
        army_add = self.get_science_gain('army_add',need=[bid])
        army_add += self._get_power_lv_gain('army_add',need=[bid])
        army_mk_num_limit += int(army_mk_num_limit*army_add)
        need_seconds = army_mk_num_limit/10*barracks_config[0]
        time_multiple = self.get_science_gain('army_rate',need=[bid])
        time_multiple += self._get_power_lv_gain('army_rate',need=[bid])
        time_multiple += self.get_buff_effect()
        need_seconds = int(math.ceil(float(need_seconds)/(1*(time_multiple+1))))
        make_times = int(total_seconds)/need_seconds
        for i in range(make_times):
            try:
                self=self.building_make_army({'bid': bid, 'army_mk_num': army_mk_num_limit,'if_cost':0},auto=-1)
            except Exception as e:
                break
        else:
            try:
                auto_seconds = int(total_seconds)%need_seconds
                if auto_seconds >0:
                    self=self.building_make_army({'bid': bid, 'army_mk_num': army_mk_num_limit,'if_cost':0},auto=auto_seconds)
            except:
                pass
    return self

def god_police_check(self):
    god_police_config = game_config.ploy.get('gods', None)
    if not god_police_config:
        return False
    firstday = god_police_config['firstday']
    now_date = datetime.datetime.now()
    if now_date < firstday:
        return False
    deviation = game_config.system_simple['deviation']
    deviation_minutes = datetime.timedelta(minutes = deviation)
    dev_open_date = firstday - deviation_minutes
    dev_now_date = now_date - deviation_minutes
    season_num = (now_date.date() - dev_open_date.date()).days
    open_days = season_num + 1
    play_id = self.god_police.get('play_id')
    if play_id:
        start_day, end_day = god_police_config['open_data'][play_id]['open_day']
        if open_days > end_day:
            # 如果有未领取的巡游奖励，通过邮件发放
            city_show_key = god_police_config['open_data'][play_id]['citys']
            task_key = god_police_config['open_data'][play_id]['task']
            currency = god_police_config['currency']
            gift_dict = {}
            for c_index, c_data in enumerate(self.god_police['cities']):
                city_config = god_police_config[city_show_key][c_index]
                consolation_times = self.god_police['consolation'].get(str(c_index), 0)
                if consolation_times > 0 and len(c_data) >= len(city_config['reward']) and c_data[-1]['get']:
                    rcount = int(city_config['count']*god_police_config['consolation_reward'])*consolation_times
                    gift_dict.setdefault(currency, 0)
                    gift_dict[currency] += rcount
                for k,v in enumerate(c_data):
                    if v['get']:
                        continue
                    if not v['god']:
                        continue
                    for _k,_v in v['extra'].items():
                        gift_dict.setdefault(_k, 0)
                        gift_dict[_k] += _v
                    for _k,_v in city_config['reward'][k].items():
                        gift_dict.setdefault(_k, 0)
                        gift_dict[_k] += _v
                    gift_dict.setdefault(currency, 0)
                    gift_dict[currency] += god_police_config[city_show_key][c_index]['count']
                #app_log.info("god_police_end: %s, %s, %s", c_index, consolation_times, gift_dict)
            for task_id, task_data in self.god_police['tasks'].items():
                if task_id not in god_police_config[task_key]:
                    continue
                for index, item in enumerate(god_police_config[task_key][task_id]['task']):
                    if task_data[0] > index:
                        continue
                    if task_data[1] < item['need'][0]:
                        continue
                    for k,v in item['reward'].items():
                        gift_dict.setdefault(k, 0)
                        gift_dict[k] += v
                    #app_log.info("god_police_task: %s, %s, %s", task_id, item, gift_dict)
            if gift_dict:
                name, info = god_police_config['msg_ramble']
                msg_name = self.get_return_msg(name)
                msg_info = self.get_return_msg(info)
                self.add_gift_msg(msg_name, msg_info, gift_dict, push_user=True, name_id=name)
            self.god_police = {}
        else:
            if self.god_police['season_num'] != season_num:
                self.god_police['god_count'] = {}
                self.god_police['free_times'] = 0
                self.god_police['season_num'] = season_num
    if not self.god_police:
        for k, v in god_police_config['open_data'].items():
            start_day, end_day = v['open_day']
            if open_days < start_day or open_days > end_day:
                continue
            self.god_police['season_num'] = season_num
            self.god_police['play_id'] = k
            self.god_police['city_index'] = 0
            self.god_police['free_times'] = 0
            self.god_police['magic_add'] = 0
            self.god_police['magic_times'] = 0
            self.god_police['cities'] = []
            self.god_police['event_text'] = None
            city_show_key = v['citys']
            task_key = v['task']
            for i in god_police_config[city_show_key]:
                self.god_police['cities'].append([])
            self.god_police['god_count'] = {}
            self.god_police['tasks'] = {}
            for task_id in god_police_config[task_key].keys():
                self.god_police['tasks'][task_id] = [0, 0] # [task.index，rate]
            self.god_police['shop'] = {}
            self.god_police['consolation'] = {}
            break

@gen.coroutine
def get_battle_result(self, city, t1, t2, now, now_d, last_battle_result):
    try:
        initJS = last_battle_result['initJS']
        troop1, troop2 = initJS['troop']
        npc_reward = initJS['npc_reward']
        battle_id = initJS['battle_id']
        fight_result = last_battle_result['result']
    except:
        troop1, troop2, fight_result = last_battle_result
        initJS = None
        npc_reward = None
        battle_id = None
    copyJS = copy(initJS)
    win = None
    #计算战功
    #杀人战功=击杀部队数*双方战力倍数*旗子*爵位加成*玩家或者npc系数*杀人系数
    #牺牲战功=牺牲部队数量*部队等级系数*爵位*旗子*牺牲系数
    city.fight['fight_count'] += 1
    city.fight['rnd'] = fight_result['rnd']

    def get_credit(troop, kill_num, dead_num_1, dead_num_2, my_power, other_power, other_is_npc, is_attack, other_official):
        if troop['uid'] > 0:
            dead_num_1,dead_num_2 = User.users[troop['uid']].change_extra_hero_army(troop['hid'], dead_num_1, dead_num_2)
        kill_num = max(0 , kill_num)
        dead_num_1 = max(0 , dead_num_1)
        dead_num_2 = max(0 , dead_num_2)

        power_radio = max(min(other_power*1.0/my_power, game_config.credit['power_by'][1]), game_config.credit['power_by'][0])

        if other_is_npc:
            if other_official==-2:
                npc_radio = game_config.credit['play_npc'][1]*game_config.credit['guard_lv'][2]
            elif other_official==-1:
                npc_radio = game_config.credit['play_npc'][1]*game_config.credit['guard_lv'][1]
            else:
                npc_radio = game_config.credit['play_npc'][1]*game_config.credit['guard_lv'][0]
        else:
            npc_radio = game_config.credit['play_npc'][0]

        if troop['uid']>0:
            army_rank = [item['rank'] for item in User.users[troop['uid']].get_fight_prepare_data(troop['hid'])['army']]
        else:
            army_rank = [item['rank'] for item in troop['army']]

        buff = 0
        if troop['country'] in [0, 1, 2]:
            buffs = self.countries[troop['country']].buffs
        else:
            buffs = {}

        if is_attack:
            gc = game_config.country['buff_country3']
            if buffs.has_key('buff_country3') and now_d<=buffs['buff_country3'][2]+datetime.timedelta(minutes=gc['time']) and buffs['buff_country3'][0]==city.cid:
                buff = game_config.country['buff_country3']['effect_consume'][1]
        else:
            gc = game_config.country['buff_country4']
            if buffs.has_key('buff_country4') and now_d<=buffs['buff_country4'][2]+datetime.timedelta(minutes=gc['time']) and buffs['buff_country4'][0]==city.cid:
                buff = game_config.country['buff_country4']['effect_consume'][1]

        u = User.users[troop['uid']] if troop['uid']>0 else None
        kill_office = 0
        if User.use_function.get('use_office37', 0) == 0:
            office_config = game_config.office
        else:
            office_config = game_config.office37
        for office in office_config['righttype']['killmexp']:
            if u and office in u._get_office_right_list():
                kill_office += office_config['right'][office]['para'][0]
        x = [office_config['right'][office]['para'][0] for office in
                office_config['righttype']['mexp_compensate'] if u and office in u._get_office_right_list()]
        if x:
            dead_office = max(x)
        else:
            dead_office = 1
        c1 = kill_num*power_radio*(1+buff)*(1+kill_office)*npc_radio*game_config.credit['kill_ratio']
        c2 = dead_num_1*game_config.credit['army_ratio'][army_rank[0]-1]*(1+buff)*dead_office*game_config.credit['dead_ratio']
        c3 = dead_num_2*game_config.credit['army_ratio'][army_rank[1]-1]*(1+buff)*dead_office*game_config.credit['dead_ratio']
        credit_effect = 1
        if troop['country'] in [0,1,2]:
            expedition_config = game_config.country['expedition']
            if expedition_config['switch']:
                if self.countries[troop['country']].expedition_cid == city.cid:
                    credit_effect = expedition_config['effect']
        return int((c1+c2+c3)*credit_effect)

    if fight_result['winner']==0:

        troop1_credit = get_credit(troop1,
                troop2['army'][0]['hp']+troop2['army'][1]['hp'],
                troop1['army'][0]['hp']-fight_result['winnerHp'][0],
                troop1['army'][1]['hp']-fight_result['winnerHp'][1],
                troop1['power'], troop2['power'], troop2['uid']<0, True, troop2['official']
                )

        troop2_credit = get_credit(troop2,
                troop1['army'][0]['hp']-fight_result['winnerHp'][0]+troop1['army'][1]['hp']-fight_result['winnerHp'][1],
                troop2['army'][0]['hp'],
                troop2['army'][1]['hp'],
                troop2['power'], troop1['power'], troop1['uid']<0, False, troop1['official']
                )

        if troop1['uid']>0:
            if int(city.cid)>=0:
                User.users[troop1['uid']].add_kill_num(troop2['army'][0]['hp']+troop2['army'][1]['hp'],
                    max(0, troop1['army'][0]['hp']-fight_result['winnerHp'][0])+max(0, troop1['army'][1]['hp']-fight_result['winnerHp'][1]),if_country=True, is_win=True)
            else:
                self.add_xyz_kill(User.users[troop1['uid']], troop2['army'][0]['hp']+troop2['army'][1]['hp'])
            city.fight['user_logs'][troop1['uid']]['kill'] += (troop2['army'][0]['hp']+troop2['army'][1]['hp'])
            city.fight['user_logs'][troop1['uid']]['dead'] += (max(0, troop1['army'][0]['hp']-fight_result['winnerHp'][0])+max(0, troop1['army'][1]['hp']-fight_result['winnerHp'][1]))
            troop1_extra_army = User.users[troop1['uid']].extra_hero_army.get(troop1['hid'], None)
            if troop1_extra_army is not None:
                troop1['extraHp'] = troop1_extra_army
        if troop2['uid']>0:
            if int(city.cid)>=0:
                User.users[troop2['uid']].add_kill_num(max(0, troop1['army'][0]['hp']-fight_result['winnerHp'][0])+max(0, troop1['army'][1]['hp']-fight_result['winnerHp'][1]),
                        troop2['army'][0]['hp']+ troop1['army'][1]['hp'],if_country=True, is_win=False)
            else:
                self.add_xyz_kill(User.users[troop2['uid']], max(0, troop1['army'][0]['hp']-fight_result['winnerHp'][0])+max(0, troop1['army'][1]['hp']-fight_result['winnerHp'][1]))
            city.fight['user_logs'][troop2['uid']]['kill'] += (max(0, troop1['army'][0]['hp']-fight_result['winnerHp'][0])+max(0, troop1['army'][1]['hp']-fight_result['winnerHp'][1]))
            city.fight['user_logs'][troop2['uid']]['dead'] += (troop2['army'][0]['hp']+ troop2['army'][1]['hp'])
        troop1_kill_count = (troop2['army'][0]['hp']+troop2['army'][1]['hp'])
        troop2_kill_count = (max(0, troop1['army'][0]['hp']-fight_result['winnerHp'][0])+max(0, troop1['army'][1]['hp']-fight_result['winnerHp'][1]))
        troop1['army'][0]['hp'] = fight_result['winnerHp'][0]
        troop1['army'][1]['hp'] = fight_result['winnerHp'][1]
        if fight_result.get('winnerPuppetHp', None) is not None:
            troop1['puppet']['hp'] = fight_result['winnerPuppetHp']

        if troop1['uid']>0:

            x = 0
            for i in range(len(t1)-1, -1, -1):
                if t1[i]['uid']>0:
                    x = i+1
                    break
            index = max(x, min(len(t1), game_config.fight['troopInsertIndex']))
        else:
            if troop1['proud']>=game_config.world['npcBackProud']-1:
                index = len(t1)
            else:
                index = 0
        t1.insert(index, troop1)

        if troop2['uid']>0 or troop2.get('xtype')=='car':
            if troop2['uid'] in self.troops and self.troops[troop2['uid']].has_key(troop2['hid']):
                del self.troops[troop2['uid']][troop2['hid']]
        elif troop2.get('xtype') in ['country_army', 'patriot_army']:
            if troop2['uid'] in self.troops and self.troops[troop2['uid']].has_key(troop2['hid']):
                self.troops[troop2['uid']][troop2['hid']]['num'] -= 1
                if self.troops[troop2['uid']][troop2['hid']]['num']<=0:
                    del self.troops[troop2['uid']][troop2['hid']]

                    for uid in AppSocket.user_socket_dict:
                        u = User.users[uid]
                        if u.country==troop2['country']:
                            u.write('w.country_army_dead', {'uid': troop2['uid'], 'hid': troop2['hid']})
        else:
            city.troop -= 1

        if troop1['uid']>0 or troop1.get('xtype')=='car':
            if troop1['uid'] in self.troops and self.troops[troop1['uid']].has_key(troop1['hid']):
                self.troops[troop1['uid']][troop1['hid']]['army'][0] = troop1['army'][0]['hp']
                self.troops[troop1['uid']][troop1['hid']]['army'][1] = troop1['army'][1]['hp']

        win = 0

    else:
        troop2_credit = get_credit(troop2,
                troop1['army'][0]['hp']+troop1['army'][1]['hp'],
                troop2['army'][0]['hp']-fight_result['winnerHp'][0],
                troop2['army'][1]['hp']-fight_result['winnerHp'][1],
                troop2['power'], troop1['power'], troop1['uid']<0, False, troop1['official']
                )

        troop1_credit = get_credit(troop1,
                troop2['army'][0]['hp']-fight_result['winnerHp'][0]+troop2['army'][1]['hp']-fight_result['winnerHp'][1],
                troop1['army'][0]['hp'],
                troop1['army'][1]['hp'],
                troop1['power'], troop2['power'], troop2['uid']<0, True, troop2['official']
                )

        if troop1['uid']>0:
            if int(city.cid)>=0:
                User.users[troop1['uid']].add_kill_num(max(0, troop2['army'][0]['hp']-fight_result['winnerHp'][0])+max(0, troop2['army'][1]['hp']-fight_result['winnerHp'][1]),
                        troop1['army'][0]['hp']+ troop1['army'][1]['hp'],if_country=True, is_win=False)
            else:
                self.add_xyz_kill(User.users[troop1['uid']], max(0, troop2['army'][0]['hp']-fight_result['winnerHp'][0])+max(0, troop2['army'][1]['hp']-fight_result['winnerHp'][1]))
            city.fight['user_logs'][troop1['uid']]['kill'] += (max(0, troop2['army'][0]['hp']-fight_result['winnerHp'][0])+max(0, troop2['army'][1]['hp']-fight_result['winnerHp'][1]))
            city.fight['user_logs'][troop1['uid']]['dead'] += (troop1['army'][0]['hp']+ troop1['army'][1]['hp'])
        if troop2['uid']>0:
            if int(city.cid)>=0:
                User.users[troop2['uid']].add_kill_num(troop1['army'][0]['hp']+troop1['army'][1]['hp'],
                        max(0, troop2['army'][0]['hp']-fight_result['winnerHp'][0])+ max(0, troop2['army'][1]['hp']-fight_result['winnerHp'][1]),if_country=True, is_win=True)
            else:
                self.add_xyz_kill(User.users[troop2['uid']], troop1['army'][0]['hp']+troop1['army'][1]['hp'])
            city.fight['user_logs'][troop2['uid']]['kill'] += (troop1['army'][0]['hp']+troop1['army'][1]['hp'])
            city.fight['user_logs'][troop2['uid']]['dead'] += (max(0, troop2['army'][0]['hp']-fight_result['winnerHp'][0])+ max(0, troop2['army'][1]['hp']-fight_result['winnerHp'][1]))
            troop2_extra_army = User.users[troop2['uid']].extra_hero_army.get(troop2['hid'], None)
            if troop2_extra_army is not None:
                troop2['extraHp'] = troop2_extra_army


        troop1_kill_count = (max(0, troop2['army'][0]['hp']-fight_result['winnerHp'][0])+max(0, troop2['army'][1]['hp']-fight_result['winnerHp'][1]))
        troop2_kill_count = (troop1['army'][0]['hp']+troop1['army'][1]['hp'])
        troop2['army'][0]['hp'] = fight_result['winnerHp'][0]
        troop2['army'][1]['hp'] = fight_result['winnerHp'][1]
        if fight_result.get('winnerPuppetHp', None) is not None:
            troop2['puppet']['hp'] = fight_result['winnerPuppetHp']

        if troop2['uid']>0:

            x = 0
            for i in range(len(t2)-1, -1, -1):
                if t2[i]['uid']>0:
                    x = i+1
                    break
            index = max(x, min(len(t2), game_config.fight['troopInsertIndex']))
        else:
            if troop2['proud']>=game_config.world['npcBackProud']-1:
                index = len(t2)
            else:
                index = 0
        t2.insert(index, troop2)

        if troop1['uid']>0 or troop1.get('xtype')=='car':
            if troop1['uid'] in self.troops and self.troops[troop1['uid']].has_key(troop1['hid']):
                del self.troops[troop1['uid']][troop1['hid']]
        elif troop1.get('xtype') in ['country_army', 'patriot_army']:
            if troop1['uid'] in self.troops and self.troops[troop1['uid']].has_key(troop1['hid']):
                self.troops[troop1['uid']][troop1['hid']]['num'] -= 1
                if self.troops[troop1['uid']][troop1['hid']]['num']<=0:
                    del self.troops[troop1['uid']][troop1['hid']]

                    for uid in User.users:
                        u = User.users[uid]
                        if u.country==troop1['country']:
                            u.write('w.country_army_dead', {'uid': troop1['uid'], 'hid': troop1['hid']})

        if troop2['uid']>0 or troop2.get('xtype')=='car':
            if troop2['uid'] in self.troops and self.troops[troop2['uid']].has_key(troop2['hid']):
                self.troops[troop2['uid']][troop2['hid']]['army'][0] = troop2['army'][0]['hp']
                self.troops[troop2['uid']][troop2['hid']]['army'][1] = troop2['army'][1]['hp']

        win = 1

    ## 新天下大势数据记录
    if troop1['uid'] > 0:
        self.add_new_milepost_records('player_kill', troop1['country'], troop1_kill_count)
        if troop2['country'] in [6, 17]:
            ## 击杀黄巾军
            self.add_new_milepost_records('player_kill_thief', troop1['country'], troop1_kill_count)
        self.add_new_milepost_records('player_credit', troop1['country'], troop1_credit)
    if troop2['uid'] > 0:
        self.add_new_milepost_records('player_kill', troop2['country'], troop2_kill_count)
        if troop1['country'] in [6, 17]:
            ## 击杀黄巾军
            self.add_new_milepost_records('player_kill_thief', troop2['country'], troop2_kill_count)
        self.add_new_milepost_records('player_credit', troop2['country'], troop2_credit)

    if city.fight.get('fight_data'):
        city.fight['fight_data'][0]['kill_count'] += troop1_kill_count
        city.fight['fight_data'][1]['kill_count'] += troop2_kill_count
        if troop1['uid'] > 0:
            troop1_kill = city.fight['user_logs'][troop1['uid']]['kill']
            if not city.fight['fight_data'][0]['top_kill'] or city.fight['fight_data'][0]['top_kill']['kill'] < troop1_kill:
                city.fight['fight_data'][0]['top_kill'] = {'uid': troop1['uid'], 'head': troop1['head'], 'kill': troop1_kill}
        if troop2['uid'] > 0:
            troop2_kill = city.fight['user_logs'][troop2['uid']]['kill']
            if not city.fight['fight_data'][1]['top_kill'] or city.fight['fight_data'][1]['top_kill']['kill'] < troop2_kill:
                city.fight['fight_data'][1]['top_kill'] = {'uid': troop2['uid'], 'head': troop2['head'], 'kill': troop2_kill}

    if troop1['uid']>0:
        User.users[troop1['uid']].add_credit(troop1_credit,hid=troop1['hid'])
        if int(city.cid)<0:
            self.add_xyz_credit(User.users[troop1['uid']], troop1_credit)
        city.fight['user_logs'][troop1['uid']]['credit'] += troop1_credit
    if troop2['uid']>0:
        User.users[troop2['uid']].add_credit(troop2_credit,hid=troop2['hid'])
        if int(city.cid)<0:
            self.add_xyz_credit(User.users[troop2['uid']], troop2_credit)
        city.fight['user_logs'][troop2['uid']]['credit'] += troop2_credit

    ## 战斗记录
    if battle_id is not None and (troop1['uid'] > 0 and troop2['uid'] > 0):
        city.fight['fight_logs'].append({
            'cid': city.cid,
            'winner': fight_result['winner'],
            'fight_id': city.fight['fight_id'],
            'zone': options.zone,
            'troop1': {
                'uid': troop1['uid'],
                'uname': troop1['uname'],
                'country': troop1['country'],
                'power': troop1['power'],
                'hid': troop1['hid'],
                'hpm': sum([t['hpm'] for t in copyJS['troop'][0]['army']]),  # 总血量
                'bhp': sum([t['hp'] for t in copyJS['troop'][0]['army']]),  # 开战前的血量
                'ahp': fight_result['troop'][0]['hp']   # 战斗结束后的血量
            },
            'troop2': {
                'uid': troop2['uid'],
                'uname': troop2['uname'],
                'country': troop2['country'],
                'power': troop2['power'],
                'hid': troop2['hid'],
                'hpm': sum([t['hpm'] for t in copyJS['troop'][1]['army']]),
                'bhp': sum([t['hp'] for t in copyJS['troop'][1]['army']]),
                'ahp': fight_result['troop'][1]['hp']
            },
            'battle_id': battle_id,
            'fight_index': city.fight['fight_count'],   #战斗场次
            'fight_time': now
        })
        if game_config.system_simple['war_report']['save_log']:
            params = {
                'zone': options.zone,
                'time': now_d,
                'initJS': copyJS,
                'fight_id': city.fight['fight_id'],
                'fight_result': fight_result
            }
            fapi.insert_fight_log(pickle.dumps(params, -1))

    if win==0:
        win_troop = troop1
        dead_troop = troop2
    else:
        win_troop = troop2
        dead_troop = troop1
    try:
        fight_result_troop = fight_result['troop']
        if troop1['uid'] > 0:
            User.users[troop1['uid']].check_new_task('hero_kill', fight_result_troop[1]['dead'], [troop1['hid']])
            #User.users[troop1['uid']].check_new_task('kill_country', 1, [troop2['country']])
        if troop2['uid'] > 0:
            User.users[troop2['uid']].check_new_task('hero_kill', fight_result_troop[0]['dead'], [troop2['hid']])
            #User.users[troop2['uid']].check_new_task('kill_country', 1, [troop1['country']])
        if win_troop['uid'] > 0:
            User.users[win_troop['uid']].check_new_task('kill_country', 1, [dead_troop['country']])
    except Exception as e:
        pass

    win_troop['proud'] += 1
    dead_troop['proud'] += 1
    dead_troop['army'][0]['hp'] = 0
    dead_troop['army'][1]['hp'] = 0

    #战斗胜利或失败推送
    if win_troop['uid']>0:
        try:
            if win == 0:
                User.users[win_troop['uid']].check_new_task('kill_army', 1, [])
            if dead_troop['uid'] > 0:
                User.users[win_troop['uid']].check_new_task('kill_player', 1, [])

        except:
            pass
        city.fight['user_logs'][win_troop['uid']]['attends'][win_troop['hid']] = self.get_troop_hp(win_troop)

        if dead_troop['official']==-2:
            city.fight['user_logs'][win_troop['uid']]['kill_gen'] += 1

        city.fight['user_logs'][win_troop['uid']]['kill_troop'] += 1

        u = User.users[win_troop['uid']]
        # npc 掉落宝物
        """
        npc_type = dead_troop.get('npc_type')
        reward = None
        if npc_type and npc_type in game_config.attack_city_npc:
            reward = game_config.attack_city_npc[npc_type]['reward_'+dead_troop['hid']]
            gift_dict = dict(reward)
            if npc_type == 'thief_one':
                gift_dict = u.festival_reward_interface('attack_city_npc', gift_dict)
        """
        if npc_reward:
            u.give_gifts(npc_reward, 'npc_reward')
            User.add_backup_uid(u.uid)

        try:
            army = self.troops[win_troop['uid']][win_troop['hid']]['army']
        except:
            army = None
        if npc_reward:
            u.write('w.finish_fight', {'city': city.cid, 'hid': win_troop['hid'], 'index': index, 'army': army, 'user':{'hero': u.get_return_hero_dict(win_troop['hid']), 'credit_year': u.credit_year, 'credit': u.credit, 'year_credit': u.year_credit, 'credit_lv': u.credit_lv, 'credit_gift': u.credit_get_gifts, 'coin': u.coin, 'gold': u.gold, 'prop': u.prop, 'records': {'office_credit': u.records['office_credit']}}, 'npc_reward': npc_reward })
        else:
            u.write('w.finish_fight', {'city': city.cid, 'hid': win_troop['hid'], 'index': index, 'army': army, 'user':{'hero': u.get_return_hero_dict(win_troop['hid']), 'credit_year': u.credit_year, 'credit': u.credit, 'year_credit': u.year_credit, 'credit_lv': u.credit_lv, 'credit_gift': u.credit_get_gifts, 'coin': u.coin, 'gold': u.gold, 'records': {'office_credit': u.records['office_credit']}}, 'npc_reward': npc_reward })
    if dead_troop['uid']>0:
        city.fight['user_logs'][dead_troop['uid']]['attends'][dead_troop['hid']] = self.get_troop_hp(dead_troop)

        u = User.users[dead_troop['uid']]
        u.reduce_hero_morale(dead_troop['hid'])
        if npc_reward:
            u.write('w.finish_fight', {'city': city.cid, 'hid': dead_troop['hid'], 'index': -1, 'army': None, 'user':{'hero': u.get_return_hero_dict(dead_troop['hid']), 'credit_year': u.credit_year, 'credit': u.credit, 'year_credit': u.year_credit, 'credit_lv': u.credit_lv, 'credit_gift': u.credit_get_gifts, 'coin': u.coin, 'gold': u.gold, 'prop': u.prop, 'npc_reward': None, 'records': {'office_credit': u.records['office_credit']}} })
        else:
            u.write('w.finish_fight', {'city': city.cid, 'hid': dead_troop['hid'], 'index': -1, 'army': None, 'user':{'hero': u.get_return_hero_dict(dead_troop['hid']), 'credit_year': u.credit_year, 'credit': u.credit, 'year_credit': u.year_credit, 'credit_lv': u.credit_lv, 'credit_gift': u.credit_get_gifts, 'coin': u.coin, 'gold': u.gold, 'npc_reward': None, 'records': {'office_credit': u.records['office_credit']}} })

    if dead_troop.get('xtype')=='car':
        u = User(-1)
        u.country = win_troop['country']
        u.check_chat('kill_ballista', [win_troop['country'], win_troop['official'], win_troop['uname'], dead_troop['country'], dead_troop['car_type']])

    if not (yield self.check_fight_result(city, t1, t2, now, now_d)):
        # 我的部队即将上场
        for i, troop in enumerate(t1):
            if i <= 2 or ((i+1)%10==0 and i!=0):
                if troop['uid']>0:
                    u = User.users[troop['uid']]
                    u.write('w.fight_ready', {'city': city.cid, 'hid': troop['hid'], 'index': i})
        for i, troop in enumerate(t2):
            if i <= 2 or ((i+1)%10==0 and i!=0):
                if troop['uid']>0:
                    u = User.users[troop['uid']]
                    u.write('w.fight_ready', {'city': city.cid, 'hid': troop['hid'], 'index': i})

def get_fight_prepare_data(self, hid, duplicate=False):
    hero_config = game_config.hero
    barracks_config = game_config.system_simple['barracks']
    shogun_val = self.get_shogun_value_list(if_check=False)

    hero = self.hero[hid]
    army = [{},{}]
    hero_type = hero_config[hid]['type']
    if shogun_val:
        shogun = [shogun_val[hero_type]]

    for i in [0,1]:
        army_type = hero_config[hid]['army'][i]
        if shogun_val:
            shogun.append(shogun_val[army_type+3])
        army_lv = hero['army'][i]
        bid = Army_type_2[army_type]
        blv = self.home[bid]['lv']-1
        if blv < 0:
            blv = 0
        rank = barracks_config[bid][blv][3]
        army[i]['type'] = army_type
        army[i]['rank'] = rank
        army[i]['lv'] = army_lv
        army[i]['add'] = [self.home[bid]['science'][0], self.home[bid]['science'][6]]
    hero_equip = []
    for eid in hero['equip']:
        item = [eid, self.equip[eid]['lv'], self.equip[eid]['wash'], self.equip[eid].get('enhance', 0)]
        hero_equip.append(item)
    star = {}
    for star_id in hero['star'].values():
        try:
            star_lv = self.star[star_id]['lv']
            star_id = star_id.split('|')[0]
            if star.has_key(star_id):
                star[star_id] += star_lv
            else:
                star[star_id] = star_lv
        except KeyError as e:
            app_log.exception(e)
            app_log.info("UserHeroStarError: %s %s %s", self.uid, hid, star_id)
            continue


    build78_lv = [self.home['building007']['lv'],self.home['building008']['lv']]


    hero_dict = {
        'uid': self.uid,
        'hid': hid,
        'lv': hero['lv'],
        'skill': hero['skill'],
        'fate': hero['fate'],
        'army': army,
        'hero_star': hero['hero_star'],
        'star': star,
        'legend': {},
        'equip': hero_equip,
        'science_passive': self.science.get('passive',{}),
        'building': build78_lv,
        'skin_lv': self.skin_stars,
        }
    if hero.has_key('revived'):
        hero_dict['revived'] = hero['revived']
    if hero.has_key('assign'):
        hero_dict['assign'] = hero['assign']
    doom_tower_lv = self._get_doom_tower_lv()
    if doom_tower_lv > 0:
        hero_dict['doom_tower_lv'] = doom_tower_lv
    doom_type_lv = self.doom_type_exp[hero_type]
    if doom_type_lv > 0:
        hero_dict['doom_type_lv'] = doom_type_lv

    #非跨服战属性
    if not duplicate:
        official = -100
        hero_dict['title'] = self.hero[hid]['title'][0] if self.hero[hid]['title'] and self.hero[hid]['title'][1]>datetime.datetime.now() else None
        if self.honour_hero.has_key(hid):
            hero_dict['honour_lv'] = self.honour_hero[hid]['lv']
            hero_dict['honour_strength'] = User.honour.get('strength',0)
        for i,v in enumerate(world.countries[self.country].official):
            if v is not None and v[0] == self.uid:
                official = i
                break
        hero_dict['official'] = official
        if official >=0 and official <=4:
            milepost = world.countries[self.country].get_current_milepost()
            hero_dict['milepost'] = milepost

        # 阴兵
        extra_army = self.extra_hero_army.get(hid, None)
        if extra_army is not None:
            hero_dict['extraHp'] = extra_army
    #战计
    if self.scheme:
        hero_dict['scheme'] = self.scheme
    scheme_box = None
    for item in self.scheme_box:
        if item['hid'] != hid:
            continue
        scheme_box = item['scheme']
        break
    if scheme_box is not None:
        hero_dict['scheme_box'] = scheme_box
    #魂玉
    if hero.has_key('soul'):
        hero_dict['soul'] = hero['soul'][:9]
        if hero['soul'][9]:
            eid = hero['soul'][9]
            soul_equip= [eid, self.equip[eid]['lv'], self.equip[eid]['wash'], self.equip[eid].get('enhance', 0)]
            hero_dict['soul'].append(soul_equip)
        else:
            hero_dict['soul'].append(None)
    #############################
    if hero.has_key('sp_id'):
        if hero['sp_id'] != [None,None]:
            hero_dict['sp_army'] = []
            for item in hero['sp_id']:
                if item:
                    hero_dict['sp_army'].append([item,self.sp_army[item]])
                else:
                    hero_dict['sp_army'].append(None)


    if hero.has_key('counter'):
        counter_dict = {}
        for item in hero['counter'].values():
            counter_dict[item[0]] = item[1]
        hero_dict['counter'] = counter_dict
    if hero.has_key('beast_ids'):
        beast = []
        for item in hero['beast_ids']:
            if item:
                beast.append(self.beast[item])
            else:
                beast.append(None)

        hero_dict['beast'] = beast
    if hero.get('skin_id',None):
        hero_dict['skin_id'] = hero['skin_id']
    if hero.has_key('awaken'):
        hero_dict['awaken'] = hero['awaken']
    if hero.has_key('formation'):
        hero_dict['formation_arr'] = hero['formation']
    if hero.has_key('formation_index'):
        hero_dict['formation_index'] = hero['formation_index']
    if hero.get('god', None):
        hero_dict['god'] = self.get_god_data(hero['god'])
    if hero.get('ppid') is not None:
        puppet = {'lv': self.puppet[hero['ppid']]['lv'], 'arms': []}
        for paid in self.puppet[hero['ppid']]['arm']:
            if paid is None:
                puppet['arms'].append(None)
            else:
                parm = self.puppet_arm[paid]
                arm = [
                        parm['type'],
                        parm['rank'],
                        [i[0] for i in parm['value']],
                        [[i[0], i[2] if not i[3] else i[3][1]] for i in parm['refine']]
                        ]
                puppet['arms'].append(arm)
        hero_dict['puppet'] = puppet

    for legend_hid in game_config.fight['legendTalent'].keys():
        if self.hero.has_key(legend_hid):
            hero_dict['legend'][legend_hid] = self.hero[legend_hid]['hero_star']
    if shogun_val:
        hero_dict['shogun'] = shogun
    hero_adjutant = hero.get('adjutant', None)
    adj_hero_skill_lv_max = game_config.system_simple['skill_cost_max_lv']['7']
    adj_hero_army_lv_max = game_config.system_simple['skill_cost_max_lv']['8']

    if game_config.system_simple.get('skill_cost_max_lv_science'):
        for science_id, science_add_lv in game_config.system_simple['skill_cost_max_lv_science']['7']:
            stype = game_config.science[science_id]['type']
            slv = self.science.get(stype,{}).get(science_id,0)
            adj_hero_skill_lv_max += science_add_lv*slv

        for science_id, science_add_lv in game_config.system_simple['skill_cost_max_lv_science']['8']:
            stype = game_config.science[science_id]['type']
            slv = self.science.get(stype,{}).get(science_id,0)
            adj_hero_army_lv_max += science_add_lv*slv

    equip_config = game_config.equip
    adjutant_enhance = game_config.system_simple['adjutant_enhance']
    adjutant_wash = game_config.system_simple['adjutant_wash']
    wash_config = game_config.equip_wash
    if hero_adjutant:
        adjutant = []
        skill_config = game_config.skill
        for item in hero_adjutant:
            if not item:
                adjutant.append(None)
                continue
            adj_hero_skill_lv = 0
            adj_hero_army_lv = 0
            adj_hero_hero_star = self.hero[item]['hero_star']
            for k,v in self.hero[item]['skill'].items():
                skill_type = skill_config[k].get('type',888)
                if skill_type > 4:
                    continue
                if skill_type == 4:
                    adj_hero_skill_lv += v
                else:
                    adj_hero_army_lv += v
            adj_hero_skill_lv = min(adj_hero_skill_lv,adj_hero_skill_lv_max)
            adj_hero_army_lv = min(adj_hero_army_lv,adj_hero_army_lv_max)
            equip_score = 0

            # 副将魂玉连接装备
            if self.hero[item].has_key('soul') and self.hero[item]['soul'][9]:
                adj_soul_equip = self.hero[item]['soul'][9]
                soul_id = self.hero[item]['soul'][-2]
                soul_lv = int(soul_id[6:8])
                soul_equip_limit = game_config.fight['soulEquipLimitArr'][soul_lv-1]
                adj_soul_equip_lv = min(self.equip[adj_soul_equip]['lv'], soul_equip_limit[0])
                adj_soul_equip_enhance = min(self.equip[adj_soul_equip].get('enhance',0), soul_equip_limit[2])
                adj_soul_equip_wash = self.equip[adj_soul_equip]['wash'][:soul_equip_limit[1]]
                try:
                    equip_score += equip_config[adj_soul_equip]['adjutant_equip'][adj_soul_equip_lv]
                    equip_score += adjutant_enhance[adj_soul_equip_enhance]
                    for wash_id in adj_soul_equip_wash:
                        if not wash_id:
                            continue
                        wash_rarity = wash_config[wash_id]['rarity']
                        equip_score += adjutant_wash[wash_rarity]
                except:
                    if settings.WHERE == 'local':
                        raise
            for equip_id in self.hero[item]['equip']:
                equip_lv = self.equip[equip_id]['lv']
                equip_enhance = self.equip[equip_id].get('enhance',0)
                try:
                    equip_score += equip_config[equip_id]['adjutant_equip'][equip_lv]
                    equip_score += adjutant_enhance[equip_enhance]
                    for wash_id in self.equip[equip_id]['wash']:
                        if not wash_id:
                            continue
                        wash_rarity = wash_config[wash_id]['rarity']
                        equip_score += adjutant_wash[wash_rarity]
                except:
                    if settings.WHERE == 'local':
                        raise
            # 副将神灵封印装备
            if self.hero[item].get('god'):
                god_data = self.get_god_data(self.hero[item]['god'])
                for equip in god_data.get('equip', []):
                    if not equip:
                        continue
                    eid, attr = equip
                    adjutant_equip = game_config.equip[eid]['adjutant_equip']
                    if attr == 0:
                        equip_score += adjutant_equip[attr]
                    else:
                        equip_score += (adjutant_equip[attr] - adjutant_equip[attr-1])

            adj_awaken = self.hero[item].get('awaken',0)
            adj_skin_id = self.hero[item].get('skin_id',None)

            adjutant.append([item,adj_hero_skill_lv,adj_hero_army_lv,adj_hero_hero_star,equip_score,adj_awaken,adj_skin_id])
        if adjutant != [None,None]:
            hero_dict['adjutant'] = adjutant

    return hero_dict

def god_police_limit_unlock(self, params):
    """
    解锁无需派遣限制
    """
    god_police_config = game_config.ploy.get('gods', None)
    if not god_police_config:
        raise Model_Error(10613, u'功能未开启')
    if not self.god_police:
        raise Model_Error(10613, u'功能未开启')
    limit_index = params['limit_index']
    open_days = User.season_num + 1
    unlock_city_types, limit, limit_days = god_police_config['limit'][limit_index]
    if not limit:
        raise Model_Error(10080, u'无效的操作')
    if limit_days != -1 and limit_days > open_days:
        raise Model_Error(10551, u'未到开启时间')
    element_limit = []
    for k,v in limit.items():
        has_c = 0
        for god_id, god_data in self.god.items():
            element = game_config.god['god'][god_data['type']]['element']
            if int(element) != int(k):
                continue
            score = self._get_god_score2(god_id)
            if score >= v[1]:
                has_c += 1
        element_limit.append(has_c >= v[0])
    if not all(element_limit):
        raise Model_Error(10105, u'不满足开启条件')
    play_id = self.god_police['play_id']
    city_show_key = god_police_config['open_data'][play_id]['citys']
    self.records['god_police_limit'].extend(unlock_city_types)
    currency = god_police_config['currency']
    gift_dict = {}
    gift_list = []
    for cid,data in enumerate(self.god_police['cities']):
        if not data:
            continue
        city_config = god_police_config[city_show_key][cid]
        city_type = game_config.city[str(city_config['cid'])]['cityType']
        if str(city_type) not in unlock_city_types:
            continue
        for si,sd in enumerate(data):
            if sd['get']:
                continue
            _gift_dict = {}
            for k,v in city_config['reward'][si].items():
                _gift_dict.setdefault(k, 0)
                _gift_dict[k] += v
            _gift_dict.setdefault(currency, 0)
            _gift_dict[currency] += city_config['count']
            if sd['god']:
                for k,v in sd['extra'].items():
                    _gift_dict.setdefault(k, 0)
                    _gift_dict[k] += v
                self.god_police['god_count'][sd['god']][1] = None
                self.god_police['god_count'][sd['god']][2] = None
            else:
                god_score2 = god_police_config['god_score_limit'][str(city_type)]
                factor_add = god_police_config[city_show_key][cid]['factor']
                extra_num = max([int(int(god_score2*factor_add)/10*10), 10])# 后1位数字取整, 永远向下取整，然后如果小于10了，就按10算
                _gift_dict.setdefault(currency, 0)
                _gift_dict[currency] += extra_num
                sd['extra'] = {currency: extra_num}
            sd['get'] = True
            gift_list.append([cid, _gift_dict])
            self.god_police_task_check('gp_ramble', 1, [])
            self.god_police_task_check('gp_ramble_city', 1, [cid])
            self.god_police_task_check('gp_ramble_city_type', 1, [cid])
            for k,v in _gift_dict.items():
                gift_dict.setdefault(k, 0)
                gift_dict[k] +=  v
    return_res = self.give_gifts(gift_dict, 'god_police_limit_unlock')
    return_res['records'] = {'god_police_limit': self.records['god_police_limit']}
    return_res['god_police'] = self.god_police
    self.save()
    return {'user': return_res, 'gift_list': gift_list}

def _get_god_police_unlock_city_types(self):
    unlock_types = []
    god_police_config = game_config.ploy.get('gods', None)
    open_days = User.season_num + 1
    for item in god_police_config['limit']:
        if item[2] != -1 and item[2] > open_days:
            continue
        if item[1]:
            continue
        unlock_types.extend(item[0])
    unlock_types.extend(self.records['god_police_limit'])
    return unlock_types


def god_police_roll(self, params):
    """
    神灵巡游 掷骰子
    """
    god_police_config = game_config.ploy.get('gods', None)
    if not god_police_config:
        raise Model_Error(10613, u'功能未开启')
    if not self.god_police:
        raise Model_Error(10613, u'功能未开启')

    open_days = self._get_god_police_open_days()

    roll_index = params['roll_index']
    need_cost = god_police_config['ramble_pay'][roll_index][1]
    if roll_index == 0 and self.god_police['free_times'] < god_police_config['free']:
        self.god_police['free_times'] += 1
    else:
        self.spend_cost(god_police_config['ramble_pay'][roll_index][1], 'god_police_roll')
    gift_dict = {}
    # 奇遇
    magic_percent = god_police_config['magic_percent'][roll_index]
    event = []
    if self.god_police.get('magic_times', 0) < god_police_config['magic_max']:
        event_hit = utils.random_occur(magic_percent[0]+self.god_police['magic_add'])
        if event_hit:
            event_text = random.choice([i for i in god_police_config['magic_text'] if i != self.god_police['event_text']])
            event.append(event_text)
            event_gift = self.get_award_by_id(god_police_config['magic_reward'])
            event.append(event_gift)
            self.god_police['event_text'] = event_text
            for k,v in event_gift.items():
                gift_dict.setdefault(k, 0)
                gift_dict[k] += v
            self.god_police_task_check('gp_event', 1, [])
            self.god_police['magic_add'] = 0
            self.god_police.setdefault('magic_times', 0)
            self.god_police['magic_times'] += 1
        else:
            self.god_police['magic_add'] += magic_percent[1]
    round_gift = {}
    play_id = self.god_police['play_id']
    city_show_key = god_police_config['open_data'][play_id]['citys']
    currency = god_police_config['currency']
    roll_nums = []
    for i in xrange(god_police_config['ramble_pay'][roll_index][0]):
        roll_num = utils.random_randrange(*god_police_config['random_range'])
        roll_item = [roll_num, None]
        new_index = self.god_police['city_index'] + roll_num
        if new_index >= len(god_police_config[city_show_key]):
            # 完成了一圈的奖励
            new_index -= len(god_police_config[city_show_key])
            rvalue = god_police_config['complete_reward_num']
            round_gift.setdefault(currency, 0)
            round_gift[currency] += rvalue
            gift_dict.setdefault(currency, 0)
            gift_dict[currency] += rvalue
            self.god_police_task_check('gp_ramble_round', 1, [])
        self.god_police['city_index'] = new_index
        city_config = god_police_config[city_show_key][new_index]
        if len(self.god_police['cities'][new_index]) >= len(city_config['reward']):
            # 迅游次数已满的奖励
            count = city_config['count']
            rcount = int(count*god_police_config['consolation_reward'])
            gift_dict.setdefault(currency, 0)
            gift_dict[currency] += rcount
            _get_reward = {currency: rcount}
            roll_item[1] = _get_reward
        else:
            city_type = game_config.city[str(city_config['cid'])]['cityType']
            # 检查是否可跳过派遣直接发奖
            unlock_city_types = self._get_god_police_unlock_city_types()
            send_god = True
            if str(city_type) in unlock_city_types:
                send_god = False

            if not send_god:
                # 直接发奖
                _get_reward = {}
                for k,v in city_config['reward'][len(self.god_police['cities'][new_index])].items():
                    _get_reward.setdefault(k, 0)
                    _get_reward[k] += v
                    gift_dict.setdefault(k, 0)
                    gift_dict[k] += v
                _get_reward.setdefault(currency, 0)
                _get_reward[currency] += city_config['count']
                gift_dict.setdefault(currency, 0)
                gift_dict[currency] += city_config['count']
                god_score2 = god_police_config['god_score_limit'][str(city_type)]
                factor_add = god_police_config[city_show_key][new_index]['factor']
                extra_num = max([int(int(god_score2*factor_add)/10*10), 10])# 后1位数字取整, 永远向下取整，然后如果小于10了，就按10算
                _get_reward.setdefault(currency, 0)
                _get_reward[currency] += extra_num
                gift_dict.setdefault(currency, 0)
                gift_dict[currency] += extra_num
                roll_item[1] = _get_reward
                self.god_police_task_check('gp_ramble', 1, [])
                self.god_police_task_check('gp_ramble_city', 1, [new_index])
                self.god_police_task_check('gp_ramble_city_type', 1, [new_index])
                data = {'get': True, 'extra': {currency: extra_num}}
            else:
                # 需派遣
                data = {
                    'god': None,
                    't': None,
                    'extra': {},
                    'get': False
                    }
                if city_config['threshold1'] != 0:
                    data['need'] = random.sample(xrange(5), city_config['threshold1'])
            self.god_police['cities'][new_index].append(data)
        roll_nums.append(roll_item)
    return_res = self.give_gifts(gift_dict, 'god_police_roll')
    return_res['god_police'] = self.god_police
    return_res['coin'] = self.coin
    return_res['gold'] = self.gold
    self.save()
    return {
            'user': return_res,
            'round_gift': round_gift if round_gift else None,
            'event': event if event else None,
            'roll_nums': roll_nums,
            }

##>>>>>战阶功能更新
AppSocket.on_message = on_message

User._get_power_lv_gain = _get_power_lv_gain
User.power_lv_up = power_lv_up
User.get_building_material = get_building_material
User.estate_harvest = estate_harvest
User.building_make_army = building_make_army
User.building_auto_make_army = building_auto_make_army
User.god_police_check = god_police_check
User._get_god_police_unlock_city_types = _get_god_police_unlock_city_types
User.god_police_roll = god_police_roll
User.god_police_limit_unlock = god_police_limit_unlock

World.troop_create = troop_create
World.troop_add = troop_add
##>>>>>阴兵
User.get_fight_prepare_data = get_fight_prepare_data
World.get_battle_result = get_battle_result
##<<<<<

#User.set_guide_condition = set_guide_condition
#User._get_need_item_num = _get_need_item_num
#User.peach_draw = peach_draw
#User.login = login
#World.run_fight = run_fight
#World.get_battle_result = get_battle_result
#World.run_city_fight = run_city_fight
#World.troop_move = troop_move

##>>>>>>>>
#RequestCollection.fight_request_ana = {}
#RequestCollection.FightServerRequest = FightServerRequest

#User.get_pk_npc = get_pk_npc
#User.refresh_pk_npc_captain = refresh_pk_npc_captain
##>>> 表情背包/节日活动 相关更新
# User.__init__ = user_init
# User.give_gifts = give_gifts
# User.get_festival_exchange_reward = get_festival_exchange_reward
##<<<
