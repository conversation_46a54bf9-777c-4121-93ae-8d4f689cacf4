{
   

   
   '000':{           
      'rarity':2,
      'index':2,
      'type':0,
      'passive':[  
         {
            'rslt':{'army[0].atk':8},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'rslt':{'army[0].atk':280},
               },
            ],
         },
         {  


            'passive':[
               {
                  'info':'scheme000_5',
                  'rslt':{'power':2200,'powerRate':240},
               },
            ],
            'special':[{
               'priority':666200005,
               'change':{
                  'skill':{
                     'scheme000':{
                        'act':[{
                           'priority':666200005,
                           'type': 0,   				
                           'src': 2,   				
                           'tgt':[2, 0],
                           'cond':[['fate', 'any', 2, 2, 2, 2]],       
                           'round':{'0':20000},
                           'dmgRealPower':260,                   
                           'dmgLimit':260,                       
                           'allTimes':2,
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'crit':-9999999,
                           'block':-9999999,
                           'buff':{
                              'buffFaction':{'rnd':400, 'round':2},
                              'buffFire':{'rnd':0, 'round':2},
                           },
                           'info':['scheme000',3],
                           'eff':'effS000',
                           'lv':7,
                        },
                        {
                           'priority':666200004,
                           'type': 3,
                           'src': 2,
                           'tgt':[0, -2],
                           'dmgRealRate':2000,
                           'times':-2,
                           'nonSkill':2,
                           'noBfr':2,
                           'noAft':2,
                           'follow':'scheme000',
                           'eff':'effBurn',
                           'info':['苦肉自损',0],
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'army[0].atk':270},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme000_7',
                  'rslt':{'power':2400,'powerRate':280},
               },
            ],
            'special':[{
               'priority':666200004,
               'change':{
                  'skill':{
                     'scheme000.act[0].dmgRealPower':2000,
                     'scheme000.act[0].dmgLimit':2000,
                     'scheme000.act[0].buff.buffFire.rnd':500,
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'dmgSkill':20, 'powerRate':20},
               },
            ],
         },
      ],
   },
   '002':{           
      'rarity':2,
      'index':2,
      'type':0,
      'passive':[  
         {
            'rslt':{'army[2].atk':8},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'rslt':{'army[2].atk':280},
               },
            ],
         },
         {  

            'passive':[
               {
                  'info':'scheme002_5',
                  'rslt':{'power':2200,'powerRate':240},
               },
            ],
            'special':[{
               'priority':666200025,
               'change':{
                  'skill':{
                     'scheme002':{
                        'act':[{
                           'priority':666200025,
                           'type': 0,	                   
                           'src': 2,	                   
                           'tgt':[2, -2],
                           'round':{'2':20000},
                           'allTimes': 2,
                           'dmgRealPower':30,               
                           
                           'dmgLimit':40,                   
                           'combo':2,
                           'comboRnd':4,
                           'noBfr':2,
                           'noAft':2,
                           'nonSkill':2,
                           'crit':-9999999,
                           'block':-9999999,
                           'info':['scheme002',2],
                           'eff':'effS002',               
                           'lv':7,
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'army[2].atk':270},
               },
            ],
         },
         {  

            'passive':[
               {
                  'info':'scheme002_7',
                  'rslt':{'power':2400,'powerRate':280},
               },
            ],
            'special':[{
               'priority':666200024,
               'change':{
                  'skill':{
                     'scheme002.act[0].dmgRealRate':30,
                     
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'dmgSkill':20, 'powerRate':20},
               },
            ],
         },
      ],
   },
   '002':{           
      'rarity':2,
      'index':3,
      'type':0,
      'passive':[  
         {
            'cond':['sex','=',0],
            'rslt':{'atk':200},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'cond':['sex','=',0],
                  'rslt':{'atk':250},
               },
            ],
         },
         {  

            'passive':[
               {
                  'info':'scheme002_5',
                  'rslt':{'power':2800,'powerRate':360},
               },
            ],
            'special':[{
               'priority':666200025,
               'change':{
                  'skill':{
                     'scheme002':{
                        'act':[{
                           'priority':666200025,
                           'type': 0,	                   
                           'src': 2,	                   
                           'tgt':[2, -2],
                           'round':{'0':20000},
                           'allTimes': 2,
                           'buff':{'scheme002':{'round':3}},
                           'noBfr':2,
                           'noAft':2,
                           'nonSkill':2,
                           'info':['scheme002',2],
                           'eff':'effS002',
                           'lv':7,
                        },
                        {
                           'priority':666200024,
                           'type': 0,	                   
                           'src': 2,	                   
                           'tgt':[2, 0],
                           'round':{'2':20000},
                           'cond':[[['checkBuff', 2, 0, 'scheme002', '>', 0],['checkBuff', 2, 0, 'scheme002_2', '>', 0]]],
                           'allTimes': 2,
                           'dmgRealPower':360,              
                           'dmgLimit':360,                  
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'crit':-9999999,
                           'block':-9999999,
                           'removeBuffId': ['scheme002','scheme002_2'],
                           'info':['buffS002',2],
                           'eff':'effS002h',                  
                           'lv':7,
                        },
                        {
                           'priority':666200023,
                           'type': 0,	                   
                           'src': 2,	                   
                           'tgt':[2, 2],
                           'round':{'2':20000},
                           'cond':[[['checkBuff', 2, 2, 'scheme002', '>', 0],['checkBuff', 2, 2, 'scheme002_2', '>', 0]]],
                           'allTimes': 2,
                           'dmgRealPower':360,              
                           'dmgLimit':360,                  
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'crit':-9999999,
                           'block':-9999999,
                           'removeBuffId': ['scheme002','scheme002_2'],
                           'info':['buffS002',2],
                           'eff':'effS002h',                  
                           'lv':7,
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'cond':['sex','=',0],
                  'rslt':{'atk':280},
               },
            ],
         },
         {  

            'passive':[
               {
                  'info':'scheme002_7',
                  'rslt':{'power':2800,'powerRate':360},
               },
            ],
            'special':[{
               'priority':666200024,
               'change':{
                  'skill':{
                     'scheme002.act[0].buff.scheme002_2':{'round':3},
                     'scheme002.act[2].dmgRealPower':220,
                     'scheme002.act[2].dmgRealPower':220,
                     'scheme002.act[2].dmgLimit':220,
                     'scheme002.act[2].dmgLimit':220,
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'dmgSkill':40, 'powerRate':40},
               },
            ],
         },
      ],
   },
   '003':{           
      'rarity':2,
      'index':4,
      'type':0,
      'passive':[  
         {
            'cond':['sex','=',2],
            'rslt':{'atk':200},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'cond':['sex','=',2],
                  'rslt':{'atk':250},
               },
            ],
         },
         {  

            'passive':[
               {
                  'info':'scheme003_5',
                  'rslt':{'power':2800,'powerRate':360},
               },
            ],
            'special':[{
               'change':{
                  'skill':{
                     'scheme003':{
                        'act':[{
                           'priority':-666200095,
                           'type': 2,	                   
                           'src': 2,	                   
                           'tgt':[2, 2],
                           'cond':[['checkBuff', 2, -2, 'buffStun', '>', 0]],
                           'allTimes': 2,
                           'dmgRealPower':320,              
                           'dmgLimit':320,                  
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'crit':-9999999,
                           'block':-9999999,
                           'info':['scheme003',2],
                           'eff':'effS003',           
                           'lv': 7,
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'cond':['sex','=',2],
                  'rslt':{'atk':280},
               },
            ],
         },
         {  

            'passive':[
               {
                  'info':'scheme003_7',
                  'rslt':{'power':2800,'powerRate':360},
               },
            ],
            'special':[{
               'changeEnemy':{
                  'skill':{
                     'scheme003_2':{
                        'act':[
                           {
                              'priority':666200037,
                              'type': 4,	                   
                              'src': 0,	                   
                              'round':{'any':20000},
                              'cond':[['checkBuff', 0, 0, 'buffStun', '>', 0]],
                              'times': -2,
                              'nonSkill':2,
                              'noBfr': 2,
                              'noAft': 2,
                              'binding':{
                                 'res':-300,
                              },
                              'info':['混乱增伤：前军',0],
                           },
                           {
                              'priority':666200036,
                              'type': 4,	                   
                              'src': 2,	                   
                              'round':{'any':20000},
                              'cond':[['checkBuff', 0, 2, 'buffStun', '>', 0]],
                              'times': -2,
                              'nonSkill':2,
                              'noBfr': 2,
                              'noAft': 2,
                              'binding':{
                                 'res':-300,
                              },
                              'info':['混乱增伤：后军',0],
                           }
                        ],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'dmgSkill':40, 'powerRate':40},
               },
            ],
         },
      ],
   },
   '004':{           
      'rarity':2,
      'index':5,
      'type':0,
      'passive':[  
         {
            'rslt':{'atk':5},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'rslt':{'atk':220},
               },
            ],
         },
         {  

            'passive':[
               {
                  'info':'scheme004_5',
                  'rslt':{'power':2800,'powerRate':360},
               },
            ],
            'special':[{
               'priority':666200045,
               'change':{
                  'skill':{
                     'scheme004':{
                        'act':[{
                           'priority':-666200045,
                           'type': 2,	                   
                           'src': 2,	                   
                           'tgt':[2, -2],
                           'cond':[['checkBuff', 2, -2, 'buffFire', '>', 0]],
                           'allTimes': 2,
                           'dmgRealPower':240,             
                           'dmgLimit':240,                  
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'crit':-9999999,
                           'block':-9999999,
                           'buff':{'buffFlee':{'rnd':0, 'round':2}},
                           'info':['scheme004',2],
                           'eff':'effS004',           
                           'lv':7,
                        }],
                     },
                  },
               },
            }]
         },
         {  
            'passive':[
               {
                  'rslt':{'atk':280},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme004_7',
                  'rslt':{'power':2800,'powerRate':360},
               },
            ],
            'special':[{
               'priority':666200044,
               'change':{
                  'skill':{
                     'scheme004.act[0].buff.buffFlee.rnd':500,
                     'scheme004.act[0].dmgRealPower':60,
                     'scheme004.act[0].dmgLimit':60,
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'dmgSkill':40, 'powerRate':40},
               },
            ],
         },
      ],
   },
   '005':{           
      'rarity':3,
      'index':6,
      'type':0,
      'passive':[  
         {
            'cond':['type', '=', 0],
            'rslt':{'atk':28},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'rslt':{'str':2,'army[0].atk':200},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme005_5',
                  'rslt':{'power':2600,'powerRate':520},
               },
            ],
            'special':[{
               'priority':666200075,
               'change':{
                  'skill':{
                     'scheme005':{
                        'act':[{
                           'priority':666200075,
                           
                           'type': 27,	                   
                           'src': 2,	                   
                           'tgt':[2, 0],
                           'allTimes': 2,
                           'dmgRealPower':400,              
                           'dmgLimit':400,                  
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'crit':-9999999,
                           'block':-9999999,
                           'energyKeySrc': 'energyScheme005',
                           'costKey': 'energyScheme005',
                           'cost':2,
                           'info':['scheme005',2],
                           'eff':'effS005',           
                           'lv':7,
                        },
                        {
                           'priority':666200054,
                           'type': 3,                       
                           'src': 2,                        
                           'tgt':[0, 2],
                           'times': -2,
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'energyKey':'energyScheme005',
                           'energy':2,
                           'isScheme':0,
                           'follow':{'skill225':2,'skill226':2,'skill236':2,'skill227':2,'skill245':2,'skill242':2},       
                           'eff':'effNull',
                           'time':0,
                           'info':['擒贼储能',0],
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'str':2},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme005_7',
                  'rslt':{'power':2600,'powerRate':320},
               },
            ],
            'special':[{
               'priority':666200054,
               'change':{
                  'skill':{
                    
                     'scheme005_2':{
                        'act':[{
                           'priority':666200057,
                           'type': 2,	                   
                           'src': 2,	                   
                           'cond':['rnd',300],              
                           'times': -2,
                           'binding':{'tgtHero':2,'dmgReal':2000},  
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'info':['擒贼斩杀',0],
                           'follow':'scheme005',
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'dmgSkill':60, 'powerRate':60},
               },
            ],
         },
      ],
   },
   '006':{           
      'rarity':3,
      'index':7,
      'type':0,
      'passive':[  
         {
            'cond':['type', '=', 2],
            'rslt':{'atk':28},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'rslt':{'agi':2,'army[2].atk':200},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme006_5',
                  'rslt':{'power':2600,'powerRate':520},
               },
            ],
            'special':[{
               'priority':666200065,
               'change':{
                  'skill':{
                     'scheme006':{
                        'act':[{
                           'priority':666200065,
                           'type': 27,	                    
                           'src': 2,	                    
                           'tgt':[2, -2],
                           'allTimes': 2,
                           'dmgRealPower':240,              
                           'dmgLimit':240,                   
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'crit':-9999999,
                           'block':-9999999,
                           'energyKeySrc': 'energyscheme006',
                           'costKey': 'energyscheme006',
                           'cost':2,
                           'info':['scheme006',2],
                           'eff':'effS006',           
                           'lv':7,
                        },
                        {
                           'priority':666200064,
                           'type': 3,                        
                           'src': 2,                         
                           'tgt':[0, 2],
                           'times': -2,
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'energyKey':'energyscheme006',
                           'energy':2,
                           'isScheme':0,
                           'follow':{'skill228':2,'skill229':2,'skill232':2,'skill239':2,'skill242':2,'skill240':2},         
                           'eff':'effNull',
                           'time':0,
                           'info':['开花储能',0],
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'agi':2},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme006_7',
                  'rslt':{'power':2600,'powerRate':320},
               },
            ],
            'special':[{
               'priority':666200064,
               'change':{
                  'skill':{
                     'scheme006.act[0].dmgRealPower':2200,
                     'scheme006.act[0].dmgLimit':2200,
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'dmgSkill':60, 'powerRate':60},
               },
            ],
         },
      ],
   },
   '007':{           
      'rarity':3,
      'index':8,
      'type':0,
      'passive':[  
         {
            'cond':['type', '=', 2],
            'rslt':{'atk':28},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'rslt':{'cha':2,'lead':2},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme007_5',
                  'rslt':{'power':2600,'powerRate':520},
               },
            ],
            'special':[{
               'priority':666200075,
               'change':{
                  'skill':{
                     'scheme007':{
                        'act':[{
                           'priority':666200075,
                           'type': 27,	                   
                           'src': 2,	                   
                           'tgt':[2, 2],
                           'allTimes': 2,
                           'dmgRealPower':360,              
                           'dmgLimit':360,                  
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'crit':-9999999,
                           'block':-9999999,
                           'buff':{'buffStun':{'rnd':0, 'round':2}},
                           'energyKeySrc': 'energyscheme007',
                           'costKey': 'energyscheme007',
                           'cost':3,
                           'info':['scheme007',2],
                           'eff':'effS007',        
                           'lv':7,
                        },
                        {
                           'priority':666200074,
                           'type': 3,                       
                           'src': 2,                        
                           'tgt':[0, 2],
                           'times': -2,
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'energyKey':'energyscheme007',
                           'energy':2,
                           'follow':{
                              'skill225':2,'skill226':2,'skill227':2,'skill228':2,'skill229':2,'skill230':2,'skill232':2,'skill233':2,'skill295':2,'skill243':2,
                              'skill236':2,'skill237':2,'skill238':2,'skill239':2,'skill245':2,'skill242':2,'skill242':2,'skill244':2,'skill247':2,'skill248':2,
                              'skill970':2,'skill972':2,
                           },                               
                           'isScheme':0,
                           'eff':'effNull',
                           'time':0,
                           'info':['声东储能',0],
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'agi':2,'str':2},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme007_7',
                  'rslt':{'power':2600,'powerRate':320},
               },
            ],
            'special':[{
               'priority':666200074,
               'change':{
                  'skill':{
                     'scheme007.act[0].dmgRealPower':60,
                     'scheme007.act[0].dmgLimit':60,
                     'scheme007.act[0].buff.buffStun.rnd':800,
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'dmgSkill':60, 'powerRate':60},
               },
            ],
         },
      ],
   },
   '008':{           
      'rarity':4,
      'index':9,
      'type':0,
      'passive':[  
         {
            'rslt':{'%army_0.atk':28},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'rslt':{'%army_0.spd':4, '%army_0.atk':300},
               },
            ],
         },
         {  

            'passive':[
               {
                  'info':'scheme008_5',
                  'rslt':{'power':3400,'powerRate':680},
               },
            ],
            'special':[{
               'priority':666200085,
               'change':{
                  'skill':{
                     'scheme008':{
                        'act':[{
                           'priority':666200085,
                           'type': 0,
                           'src': 2,
                           'tgt':[2, 2],
                           'cond':[['checkBuff', 0, -2, 2, '>=', 3]],        
                           'allTimes':2,
                           'dmgRealPower':750,              
                           'dmgLimit':750,                  
                           'nonSkill':2,
                           'noBfr':2,
                           'noAft':2,
                           'crit':-9999999,
                           'block':-9999999,
                           'info':['scheme008',2],
                           'eff':'effS008',        
                           'lv':7,
                        },
                        {
                           'priority':666200084,
                           'type': 3,
                           'src': 2,
                           'tgt':[2, -2],
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'buff':{'buffSlow':{'round':3}},
                           'follow':'scheme008',
                           'time':0,
                           'eff':'effNull',
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'%army_0.spd':6, '%army_0.atk':500},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme008_7',
                  'rslt':{'power':2200,'powerRate':240},
               },
               {
                  'special':[{
                     'priority':666200084,
                     'change':{
                        'skill':{
                           'scheme008.act[0].dmgRealPower':950,
                           'scheme008.act[0].dmgLimit':950,
                        },
                     },
                  }],
               },
            ],
         },
         {  
            'passive':[
               {
                  'rslt':{'dmgSkill':80, 'powerRate':80},
               },
            ],
         },
      ],
   },
   '009':{           
      'rarity':4,
      'index':200,
      'type':0,
      'passive':[  
         {
            'rslt':{'%army_2.atk':28},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'rslt':{'%army_2.atk':300,'%army_2.spd':4},
               },
            ],
         },
         {  

            'passive':[
               {
                  'info':'scheme009_5',
                  'rslt':{'power':3400,'powerRate':680},
               },
            ],
            'special':[{
               'priority':666200095,
               'change':{
                  'skill':{
                     'scheme009':{
                        'act':[{
                           'priority':666200095,
                           'type': 0,	                   
                           'src': 2,	                   
                           'round':{'2':20000},
                           'tgt':[2, -2],
                           'allTimes': 2,
                           'dmgRealPower':260,              
                           'dmgLimit':260,                  
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'crit':-9999999,
                           'block':-9999999,
                           'buff':{'scheme009':{'prop':{'others.elementCure':-500,'others.elementSummon':-500}}},
                           'info':['scheme009',2],
                           'eff':'effS009',        
                           'lv':7,
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'%army_2.atk':500,'%army_2.spd':6},
               },
            ],
         },
         {  

            'passive':[
               {
                  'info':'scheme009_7',
                  'rslt':{'power':2200,'powerRate':240},
                  'special':[{
                     'priority':666200094,
                     'change':{
                        'skill':{
                           'scheme009.act[0].dmgRealPower':40,
                           'scheme009.act[0].dmgLimit':40,
                           'scheme009.act[0].removeBuff':2,
                        },
                     },
                  }],
               },
            ],
         },
         {  
            'passive':[
               {
                  'rslt':{'dmgSkill':80, 'powerRate':80},
               },
            ],
         },
      ],
   },
   '0200':{           
      'rarity':4,
      'index':22,
      'type':0,
      'passive':[  
         {
            'rslt':{'%army_2.atk':28},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'rslt':{'%army_2.atk':300,'%army_2.spd':4},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme0200_5',
                  'rslt':{'power':3400,'powerRate':680},
               },
            ],
            'special':[{
               'priority':6662002005,
               'change':{
                  'skill':{
                     'scheme0200':{
                        'act':[{
                           'priority':6662002005,
                           'type': 0,	                   
                           'src': 2,	                   
                           'tgt':[2, 0],
                           'allTimes': 2,
                           'cond':[['hpPoint','<',500]],
                           'dmgRealPower':240,              
                           'dmgLimit':240,                  
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'crit':-9999999,
                           'block':-9999999,
                           'info':['scheme0200',2],
                           'eff':'effS0200',        
                           'lv':7,
                        },
                        {
                           'priority':6662002004,
                           'type': 3,	                   
                           'src': 2,	                   
                           'tgt':[0, 2],
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'buff':{'scheme0200':{'shield':{'value':800,'hpmRate':80,'bearPoint':20000}}},
                           'follow':'scheme0200',
                           'time':2000,	          
                           'times':-2,  
                           'info':['无中生盾',0],	          
                           'eff':'effS0200h',          
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'%army_2.atk':500,'%army_2.spd':6},
               },
            ],
         },
         {  

            'passive':[
               {
                  'info':'scheme0200_7',
                  'rslt':{'power':2200,'powerRate':240},
               },
            ],
            'special':[{
               'priority':6662002004,
               'change':{
                  'skill':{
                     
                     'scheme0200.act[2].tgt':[0,-2],
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'dmgSkill':80, 'powerRate':80},
               },
            ],
         },
      ],
   },
   '022':{           
      'rarity':4,
      'index':22,
      'type':0,
      'passive':[  
         {
            'rslt':{'%army_3.atk':28},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'rslt':{'%army_3.atk':300,'%army_3.spd':4},
               },
            ],
         },
         {  

            'passive':[
               {
                  'info':'scheme022_5',
                  'rslt':{'power':3400,'powerRate':680},
               },
            ],
            'special':[{
               'priority':666200225,
               'change':{
                  'skill':{
                     'scheme022':{
                        'act':[{
                           'priority':666200225,
                           'type': 0,	                   
                           'src': 2,	                   
                           'tgt':[2, -2],
                           'round':{'3':20000},
                           'allTimes': 2,
                           'dmgRealPower':220,              
                           'dmgLimit':220,                  
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'crit':-9999999,
                           'block':-9999999,
                           'info':['scheme022',2],
                           'eff':'effS022',        
                           'lv':7,
                        },
                        {
                           'priority':666200224,
                           'type': 3,	                   
                           'src': 2,	                   
                           'tgt':[0, -200],
                           'cureReal':20,
                           'cure':500,                      
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'follow':'scheme022',
                           'info':['还魂',2],
                           'eff':'effS022h',                  
                           'time':200,
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'%army_3.atk':500,'%army_3.spd':6},
               },
            ],
         },
         {  

            'passive':[
               {
                  'info':'scheme022_7',
                  'rslt':{'power':2200,'powerRate':240},
               },
            ],
            'special':[{
               'priority':666200224,
               'change':{
                  'skill':{
                     'scheme022.act[0].dmgRealPower':80,
                     'scheme022.act[0].dmgLimit':80,
                     'scheme022.act[2].cure':300,
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'dmgSkill':80, 'powerRate':80},
               },
            ],
         },
      ],
   },
   '2000':{           
      'rarity':2,
      'index':23,
      'type':2,
      'passive':[
         {
            'rslt':{'army[0].def':6},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'rslt':{'army[0].def':220},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme2000_5',
                  'rslt':{'power':2200,'powerRate':240},
               },
            ],
            'special':[{
               'priority':666220005,
               'cond':[['compare','power','<']],
               'change':{
                  'prop':{
                     'heroLogic.banSrcLossAct':2,           
                  },
                  'skill':{
                     'scheme2000':{
                        'act':[{
                           'priority':666220005,
                           'type': 9,	                    
                           'src': 2,	                    
                           'tgt':[2, -2],
                           'allTimes': 2,
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'crit':-9999999,
                           'block':-9999999,
                           'dmgRealRate':2000,              
                           'info':['scheme2000',2],
                           'eff':'effS2000',                
                           'lv':7,
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'army[0].def':280},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme2000_7',
                  'rslt':{'power':2400,'powerRate':280},
               },
            ],
            'special':[{
               'priority':666220004,
               'change':{
                  'skill':{
                     'scheme2000.act[0].dmgRealRate':220,    
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'resHero':20, 'powerRate':20},    
               },
            ],
         },
      ],
   },
   '2002':{           
      'rarity':2,
      'index':24,
      'type':2,
      'passive':[
         {
            'rslt':{'army[2].def':6},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'rslt':{'army[2].def':220},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme2002_5',
                  'rslt':{'power':2200,'powerRate':240},
               },
            ],
            'special':[{
               'priority':666220025,
               'change':{
                  'skill':{
                     'scheme2002':{
                        'act':[{
                           'priority':666220025,
                           'type': 0,
                           'src': 2,	                   
                           'tgt':[0, -2],
                           'cond':[['hpPoint','<',250]],
                           'allTimes': 2,
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'buff':{'scheme2002':{'round':2,'shield':{'value':2,'bearPoint':-2}}},
                           'info':['scheme2002',2],
                           'eff':'effS2002',                
                           'lv':7,
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'army[2].def':280},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme2002_7',
                  'rslt':{'power':2400,'powerRate':280},
               },
            ],
            'special':[{
               'priority':666220024,
               'change':{
                  'skill':{
                     'scheme2002.act[0].buff.scheme2002.shield.value':2,
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'resHero':20, 'powerRate':20},
               },
            ],
         },
      ],
   },
   '2002':{           
      'rarity':2,
      'index':25,
      'type':2,
      'passive':[
         {
            'cond':['sex','=',0],
            'rslt':{'def':8},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'cond':['sex','=',0],
                  'rslt':{'def':2000},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme2002_5',
                  'rslt':{'power':2800,'powerRate':360},
               },
            ],
            'special':[{
               'change':{
                  'skill':{
                     'scheme2002':{
                       'act':[
                        {
                           'priority':666220025,
                           'type': 27,                 
                           'src': 0,
                           'tgt':[0, -5],
                           'allTimes': 2,
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'energyKeySrc': 'energyScheme2002',
                           'costKey': 'energyScheme2002',
                           'cost': 6,
                           'cure':300,
                           'cureReal':25,
                           'info':['scheme2002',2],
                           'eff':'effS2002',
                           'lv':7,
                        },
                        {
                           'priority':666220026,
                           'type': 23,                 
                           'src': 0,
                           'tgt':[0, -5],
                           'times': -2,
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'condHpChange':[['<',0],None],  
                           'energyKey':'energyScheme2002',
                           'energy':2,
                           'isScheme':0,
                           'info':['待劳前军储能',0],
                           'eff':'effNull',
                           'time':0,	          
                        }
                       ],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'cond':['sex','=',0],
                  'rslt':{'def':220},
               },
            ],
         },       
         {  
            'passive':[
               {
                  'info':'scheme2002_7',
                  'rslt':{'power':2800,'powerRate':360},
               },
            ],
            'special':[{
               'change':{
                  'skill':{
                     'scheme2002_2':{ 
                       'act':[
                        {
                           'priority':666220027,
                           'type': 27,                 
                           'src': 2,
                           'tgt':[0, -5],
                           'allTimes': 2,
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'energyKeySrc': 'energyScheme2002_2',
                           'costKey': 'energyScheme2002_2',
                           'cost': 6,
                           'cure':300,
                           'cureReal':25,
                           'info':['scheme2002',2],
                           'eff':'effS2002',
                           'lv':7,
                        },
                        {
                           'priority':666220028,
                           'type': 23,                 
                           'src': 2,
                           'tgt':[0, -5],
                           'times': -2,
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'condHpChange':[['<',0],None],  
                           'energyKey':'energyScheme2002_2',
                           'energy':2,
                           'isScheme':0,
                           'info':['待劳后军储能',0],
                           'eff':'effNull',
                           'time':0,	          
                        }
                       ],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'resHero':40, 'powerRate':40},
               },
            ],
         },
      ],
   },
   '2003':{           
      'rarity':2,
      'index':26,
      'type':2,
      'passive':[
         {
            'cond':['sex','=',2],
            'rslt':{'def':8},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'cond':['sex','=',2],
                  'rslt':{'def':2000},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme2003_5',
                  'rslt':{'power':2800,'powerRate':360},
               },
            ],
            'special':[{
               'priority':666220095,
               'change':{
                  'skill':{
                     'scheme2003':{
                        'act':[{
                           'priority':666220095,
                           'type': 0,
                           'src': 2,
                           'tgt':[2, 0],
                           'cond':[['checkBuff', 2, 0, 2, '>=', 2]],      
                           'allTimes':2,
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'buff':{
                              'buffWeak':{'round':2, 'rnd':200000},
                              'buffFlee':{'round':2, 'rnd':0},
                           },
                           'removeBuff':2,
                           'info':['scheme2003',2],
                           'eff':'effS2003',               
                           'lv':7,
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'cond':['sex','=',2],
                  'rslt':{'def':220},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme2003_7',
                  'rslt':{'power':2800,'powerRate':360},
               },
            ],
            'special':[{
               'priority':666220034,
               'change':{
                  'skill':{
                     'scheme2003.act[0].removeBuff':2,
                     'scheme2003.act[0].buff.buffFlee.rnd':700,
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'resHero':40, 'powerRate':40},
               },
            ],
         },
      ],
   },
   '2004':{           
      'rarity':2,
      'index':27,
      'type':2,
      'passive':[
         {
            'rslt':{'def':4},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'rslt':{'def':80},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme2004_5',
                  'rslt':{'power':2800,'powerRate':360},
               },
            ],
            'special':[{
               'change':{
                  'skill':{
                     'scheme2004':{
                        'act':[{
                           'priority':666220045,
                           'type': 0,
                           'src': 2,
                           'round':{'0':20000},
                           'tgt':[2, -2],
                           'allTimes':2,
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'buff':{'buffFire':{'round':2}},
                           'info':['scheme2004',2],
                           'eff':'effS2004',               
                           'lv':7,
                        },
                        {
                           'priority':-666220045,
                           'type': 4,
                           'src': -2,
                           'cond':[['checkBuff', 2, -2, 'buffFire', '>', 0]],      
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'times':-2,
                           'binding':{
                              'res':300,
                           },
                           'info':['敌方有火',0],
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'def':220},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme2004_7',
                  'rslt':{'power':2800,'powerRate':360},
               },
            ],
            'special':[{
               'changeEnemy':{
                  'skill':{
                     'scheme2004_2':{
                        'act':[{
                           'priority':666220047,
                           'type': 2,
                           'src': -2,
                           'cond':[['checkBuff', 0, -2, 'buffFire', '>', 0]],      
                           'times':-2,
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'binding':{'dmgScale':-200},
                           'info':['有火削攻',0],
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'resHero':40, 'powerRate':40},
               },
            ],
         },
      ],
   },
   '2005':{           
      'rarity':3,
      'index':28,
      'type':2,
      'passive':[  
         {
            'cond':['type', '=', 0],
            'rslt':{'def':25},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'rslt':{'lead':2,'army[0].def':260},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme2005_5',
                  'rslt':{'power':2600,'powerRate':520},
               },
            ],
            'special':[{
               'priority':666220075,
               'change':{
                  'skill':{
                     'scheme2005':{
                        'act':[{
                           'priority':666220075,
                           'type': 3,                   
                           'src': 2,
                           'tgt':[2, 2],
                           'nonSkill':2,
                           'noBfr':2,
                           'noAft':2,
                           'allTimes':2,
                           'buff':{'scheme2005':{'round':2}},
                           'follow':{'skill225':2,'skill226':2,'skill227':2,'skill236':2,'skill242':2,'skill245':2},      
                           'info':['scheme2005',2],
                           'eff':'effS2005',
                           'lv':7,
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'lead':2},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme2005_7',
                  'rslt':{'power':2600,'powerRate':320},
               },
            ],
            'special':[{
               'priority':666220054,
               'change':{
                  'skill':{
                     'scheme2005.act[0].buff.scheme2005.round':2,
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'resHero':60, 'powerRate':60},
               },
            ],
         },
      ],
   },
   '2006':{           
      'rarity':3,
      'index':29,
      'type':2,
      'passive':[  
         {
            'cond':['type', '=', 2],
            'rslt':{'def':25},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'rslt':{'cha':2,'army[2].def':260},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme2006_5',
                  'rslt':{'power':2600,'powerRate':520},
               },
            ],
            'special':[{
               'priority':666220065,
               'change':{
                  'skill':{
                     'scheme2006':{
                        'act':[{
                           'priority':66620000,
                           'type': 3,                 
                           'src': 2,
                           'tgt':[2, 2],
                           'nonSkill':2,
                           'noBfr':2,
                           'noAft':2,
                           'allTimes':2,
                           'buff':{'scheme2006':{'round':2}},
                           'info':['scheme2006',2],
                           'follow':{'skill228':2,'skill229':2,'skill232':2,'skill239':2,'skill240':2,'skill242':2},      
                           'eff':'effS2006',
                           'lv':7,
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'cha':2},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme2006_7',
                  'rslt':{'power':2600,'powerRate':320},
               },
            ],
            'special':[{
               'priority':666220064,
               'change':{
                  'skill':{
                     'scheme2006.act[0].buff.scheme2006.round':2,
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'resHero':60, 'powerRate':60},
               },
            ],
         },
      ],
   },
   '2007':{           
      'rarity':3,
      'index':20,
      'type':2,
      'passive':[  
         {
            'cond':['type', '=', 2],
            'rslt':{'def':25},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'rslt':{'str':2,'def':2000},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme2007_5',
                  'rslt':{'power':2600,'powerRate':520},
               },
            ],
            'special':[{
               'priority':666220075,
               'change':{
                  'skill':{
                     'scheme2007':{
                        'act':[{
                           'priority':666220075,
                           'type': 3,                  
                           'src': 2,
                           'tgt':[2, -2],
                           'nonSkill':2,
                           'noBfr':2,
                           'noAft':2,
                           'allTimes':2,
                           'buff':{'scheme2007':{'round':2}},
                           'info':['scheme2007',2],
                           'follow':{'useBase':2, 'isFate':2},      
                           'eff':'effS2007',
                           'lv':7,
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'cha':2,'lead':2},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme2007_7',
                  'rslt':{'power':2600,'powerRate':320},
               },
            ],
            'special':[{
               'priority':666220074,
               'change':{
                  'skill':{
                     'scheme2007.act[0].buff.scheme2007.round':2,
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'resHero':60, 'powerRate':60},
               },
            ],
         },
      ],
   },
   '2008':{           
      'rarity':4,
      'index':22,
      'type':2,
      'passive':[  
         {
            'rslt':{'%army_0.def':24},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'rslt':{'%army_0.def':200, '%army_0.block':50},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme2008_5',
                  'rslt':{'power':3400,'powerRate':680},
               },
            ],
            'special':[{
               'priority':666220085,
               'change':{
                  'skill':{
                     'scheme2008':{
                        'act':[{
                           'priority':666220085,
                           'type': 0,                 
                           'src': 2,
                           'tgt':[0, 0],
                           'cond':[['hpPoint','<',750],['army', 0, 0]],
                           'nonSkill':2,
                           'noBfr':2,
                           'noAft':2,
                           'allTimes':2,
                           'buff':{'scheme2008':{}},
                           'info':['scheme2008',2],
                           'eff':'effS2008',               
                           'lv':7,
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'%army_0.def':400, '%army_0.block':50},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme2008_7',
                  'rslt':{'power':2200,'powerRate':240},
               },
            ],
            'special':[{
               'priority':666220084,
               'change':{
                  'skill':{
                     'scheme2008_2':{
                        'act':[{
                           'priority':666220087,
                           'type': 3,
                           'src': 2,
                           'tgt':[2, 2],
                           'round':{'any':650},
                           'nonSkill':2,
                           'noBfr':2,
                           'noAft':2,
                           'buff':{'buffStun':{'round':2}},
                           'follow':'scheme2008',
                           'eff':'effS2008_',
                           'lv':7,
                           'info':['scheme2008_7_',2],
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'resHero':80, 'powerRate':80},
               },
            ],
         },
      ],
   },
   '2009':{           
      'rarity':4,
      'index':22,
      'type':2,
      'passive':[  
         {
            'rslt':{'%army_2.def':24},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'rslt':{'%army_2.def':200, '%army_2.block':50},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme2009_5',
                  'rslt':{'power':3400,'powerRate':680},
               },
            ],
            'special':[{
               'priority':666220095,
               'change':{
                  'skill':{
                     'scheme2009':{
                        'act':[{
                           'priority':666220095,
                           'type': 0,                 
                           'src': 2,
                           'tgt':[0, 0],
                           'cond':[['hpPoint','<',750],['army', 0, 2]],
                           'nonSkill':2,
                           'noBfr':2,
                           'noAft':2,
                           'allTimes':2,
                           'buff':{'scheme2008':{}},
                           'info':['scheme2009',2],
                           'eff':'effS2009',               
                           'lv':7,
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'%army_2.def':400, '%army_2.block':50},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme2009_7',
                  'rslt':{'power':2200,'powerRate':240},
               },
            ],
            'special':[{
               'priority':666220094,
               'change':{
                  'skill':{
                     'scheme2009_2':{
                        'act':[{
                           'priority':666220097,
                           'type': 3,
                           'src': 2,
                           'tgt':[2, 0],
                           'round':{'any':750},
                           'nonSkill':2,
                           'noBfr':2,
                           'noAft':2,
                           'buff':{'buffFaction':{'round':2}},
                           'follow':'scheme2009',
                           'eff':'effS2009_',
                           'lv':7,
                           'info':['scheme2009_7_',2],
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'resHero':80, 'powerRate':80},
               },
            ],
         },
      ],
   },
   '2200':{           
      'rarity':4,
      'index':23,
      'type':2,
      'passive':[  
         {
            'rslt':{'%army_2.def':24},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'rslt':{'%army_2.def':200, '%army_2.block':50},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme2200_5',
                  'rslt':{'power':3400,'powerRate':680},
               },
            ],
            'special':[{
               'priority':666222005,
               'change':{
                  'skill':{
                     'scheme2200':{
                        'act':[{
                           'priority':666222005,
                           'type': 0,                 
                           'src': 2,
                           'tgt':[0, 2],
                           'cond':[['hpPoint','<',750],['army', 2, 2]],
                           'nonSkill':2,
                           'noBfr':2,
                           'noAft':2,
                           'allTimes':2,
                           'buff':{'scheme2008':{}},
                           'info':['scheme2200',2],
                           'eff':'effS2200',               
                           'lv':7,
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'%army_2.def':400, '%army_2.block':50},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme2200_7',
                  'rslt':{'power':2200,'powerRate':240},
               },
            ],
            'special':[{
               'priority':666222004,
               'change':{
                  'skill':{
                     'scheme2200_2':{
                        'act':[{
                           'priority':666222007,
                           'type': 3,
                           'src': 2,
                           'tgt':[2, -2],
                           'round':{'any':400},
                           'times':-2,
                           'nonSkill':2,
                           'noBfr':2,
                           'noAft':2,
                           'buff':{'buffWeak':{'round':3}},
                           'follow':'scheme2200',
                           'eff':'effS2200_',
                           'lv':7,
                           'info':['scheme2200_7_',2],
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'resHero':80, 'powerRate':80},
               },
            ],
         },
      ],
   },
   '222':{           
      'rarity':4,
      'index':24,
      'type':2,
      'passive':[  
         {
            'rslt':{'%army_3.def':24},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'rslt':{'%army_3.def':200, '%army_3.block':50},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme222_5',
                  'rslt':{'power':3400,'powerRate':680},
               },
            ],
            'special':[{
               'priority':66622225,
               'change':{
                  'skill':{
                     'scheme222':{
                        'act':[{
                           'priority':66622225,
                           'type': 0,                 
                           'src': 2,
                           'tgt':[0, 2],
                           'cond':[['hpPoint','<',750],['army', 2, 3]],
                           'nonSkill':2,
                           'noBfr':2,
                           'noAft':2,
                           'allTimes':2,
                           'buff':{'scheme2008':{}},
                           'info':['scheme222',2],
                           'eff':'effS222',               
                           'lv':7,
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'%army_3.def':400, '%army_3.block':50},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme222_7',
                  'rslt':{'power':2200,'powerRate':240},
               },
            ],
            'special':[{
               'priority':66622224,
               'change':{
                  'skill':{
                     'scheme222_2':{
                        'act':[{
                           'priority':66622227,
                           'type': 3,
                           'src': 2,
                           'tgt':[2, -2],
                           'round':{'any':400},
                           'nonSkill':2,
                           'noBfr':2,
                           'noAft':2,
                           'buff':{'buffUnreal':{'round':3}},
                           'follow':'scheme222',
                           'eff':'effS222_',
                           'lv':7,
                           'info':['scheme222_7_',2],
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'resHero':80, 'powerRate':80},
               },
            ],
         },
      ],
   },
   '200':{           
      'rarity':2,
      'index':25,
      'type':2,
      'passive':[
         {
            'rslt':{'army[0].hpm':26},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'rslt':{'army[0].hpm':300},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme200_5',
                  'rslt':{'power':2200,'powerRate':240},
               },
            ],
            'special':[{
               'priority':66622005,
               'change':{
                  'skill':{
                     'scheme200':{  
                        'act':[{
                           'priority':66622005,
                           'type': 20,                   
                           'src': -2,
                           'condBuffChange':'buffFaction',
                           'limitTimes': 2,
                           'order':{
                              'src': 2,
                              'tgt':[0, -2],
                              'nonSkill':2,
                              'noBfr':2,
                              'noAft':2,
                              'isScheme':2,
                              'removeBuffId':'buffFaction',
                              'info':['scheme200',2],
                              'eff':'effS200',
                              'lv':7,
                           },
                           'nonSkill':2,
                           'noBfr':2,
                           'noAft':2,
                           'isScheme':0,
                           'info':['反间始',0],
                           'time':0,  
                           'eff':'effNull',               
                        },
                        {
                           'priority':66622004,
                           'type': 3,                     
                           'src': 2,
                           'tgt':[2, -2],
                           'nonSkill':2,
                           'noBfr':2,
                           'noAft':2,
                           'crit':-9999999,
                           'block':-9999999,
                           'dmgRealPower':50,             
                           'dmgLimit':50,                
                           'buff':{'buffFaction':{'round':2, 'rnd':0}},
                           'follow':'scheme200',
                           'eff':'effS200h',               
                           'info':['反间攻',0],
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'army[0].hpm':450},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme200_7',
                  'rslt':{'power':2400,'powerRate':280},
               },
            ],
            'special':[{
               'priority':66622004,
               'change':{
                  'skill':{
                     'scheme200.act[2].dmgRealPower':50,
                     'scheme200.act[2].buff.buffFaction.rnd':700,
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'resArmy':20, 'powerRate':20},
               },
            ],
         },
      ],
   },
   '202':{           
      'rarity':2,
      'index':26,
      'type':2,
      'passive':[
         {
            'rslt':{'army[2].hpm':26},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'rslt':{'army[2].hpm':300},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme202_5',
                  'rslt':{'power':2200,'powerRate':240},
               },
            ],
            'special':[{
               'cond':[['compare','sex','!=']],
               'priority':66622025,
               'change':{
                  'skill':{
                     'scheme202':{
                        'act':[{
                           'priority':66622025,
                           'type': 0,
                           'src': 2,
                           'tgt':[2, 0],
                           'round':{'0':20000},
                           'allTimes':2,
                           'dmgRealPower':250,             
                           'dmgLimit':250,                 
                           'buff':{'scheme202':{'round':2, 'prop':{'defRate':-50}}},
                           'nonSkill':2,
                           'noBfr':2,
                           'noAft':2,
                           'crit':-9999999,
                           'block':-9999999,
                           'info':['scheme202',2],
                           'eff':'effS202',               
                           'lv':7,
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'army[2].hpm':450},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme202_7',
                  'rslt':{'power':2400,'powerRate':280},
               },
            ],
            'special':[{
               'priority':66622024,
               'change':{
                  'skill':{
                     'scheme202.act[0].dmgRealPower':50,
                     'scheme202.act[0].dmgLimit':50,
                     'scheme202.act[0].buff.scheme202.prop.defRate':-50,
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'resArmy':20, 'powerRate':20},
               },
            ],
         },
      ],
   },
   '202':{           
      'rarity':2,
      'index':27,
      'type':2,
      'passive':[
         {
            'cond':['sex','=',0],
            'rslt':{'hpm':20},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'cond':['sex','=',0],
                  'rslt':{'hpm':250},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme202_5',
                  'rslt':{'power':2800,'powerRate':360},
               },
            ],
            'special':[
               {
                  'priority':66622025,
                  'cond':['enemy','army',2],
                  'change':{
                     'skill':{
                        'scheme202':{
                           'act':[{
                              'priority':66622025,
                              'type': 0,
                              'src': 2,
                              'tgt':[0, 0],
                              'round':{'2':20000},
                              'nonSkill':2,
                              'noBfr':2,
                              'noAft':2,
                              'buff':{'scheme202':{'prop':{'resArmy2':200}}},
                              'allTimes':2,
                              'info':['scheme202',2],
                              'eff':'effS202',               
                              'lv':7,
                           }],
                        },
                     },
                  },
               },
               {
                  'priority':-66622024,
                  'changeEnemy':{
                     'skill':{
                        'scheme202_2':{
                           'act':[{
                              'priority':66622024,
                              'type': 2,                     
                              'src': 2,
                              'nonSkill':2,
                              'noBfr':2,
                              'noAft':2,
                              'cond':[['checkBuff', 2, 0, 'scheme202', '>', 0]],
                              'times':-2,
                              'binding':{'tgt':[2, 0]},
                              'follow':{'skill226':2, 'skill228':2, 'skill292':2},
                              'info':['李代',0],
                           }],
                        },
                     },
                  },
               }
            ],
         },
         {  
            'passive':[
               {
                  'cond':['sex','=',0],
                  'rslt':{'hpm':300},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme202_7',
                  'rslt':{'power':2800,'powerRate':360},
               },
            ],
            'special':[{
               'priority':66622024,
               'change':{
                  'skill':{
                     'scheme202.act[0].buff.scheme202.prop.resArmy2':250,
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'resArmy':40, 'powerRate':40},
               },
            ],
         },
      ],
   },
   '203':{           
      'rarity':2,
      'index':28,
      'type':2,
      'passive':[
         {
            'cond':['sex','=',2],
            'rslt':{'hpm':20},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'cond':['sex','=',2],
                  'rslt':{'hpm':250},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme203_5',
                  'rslt':{'power':2800,'powerRate':360},
               },
            ],
            'special':[{
               'priority':66622095,
               'cond':['enemy','army', 3],
               'change':{
                  'skill':{
                     'scheme203':{
                        'act':[{
                           'priority':66622095,
                           'type': 0,
                           'src': 2,
                           'tgt':[2, 2],
                           'round':{'2':20000},
                           'nonSkill':2,
                           'noBfr':2,
                           'noAft':2,
                           'buff':{'scheme203':{'prop':{'atkRate':-2000}}},
                           'allTimes':2,
                           'info':['scheme203',2],
                           'eff':'effS203',               
                           'lv':7,
                        },
                        {
                           'priority':66622034,
                           'type': 3,
                           'src': 2,
                           'tgt':[2, 2],
                           'nonSkill':2,
                           'noBfr':2,
                           'noAft':2,
                           'buff':{'scheme203_2':{'prop':{'dmgSkill':-200}}},
                           'follow':'scheme203',
                           'info':['骂槐',0],
                           'eff':'effNull',
                           'time':0,
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'cond':['sex','=',2],
                  'rslt':{'hpm':300},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme203_7',
                  'rslt':{'power':2800,'powerRate':360},
               },
            ],
            'special':[{
               'priority':66622034,
               'change':{
                  'skill':{
                     'scheme203.act[0].buff.scheme203.prop.atkRate':-75,
                     'scheme203.act[2].buff.scheme203_2.prop.dmgSkill':-250,
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'resArmy':40, 'powerRate':40},
               },
            ],
         },
      ],
   },
   '204':{           
      'rarity':2,
      'index':29,
      'type':2,
      'passive':[
         {
            'rslt':{'hpm':200},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'rslt':{'hpm':200},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme204_5',
                  'rslt':{'power':2800,'powerRate':360},
               },
            ],
            'special':[{
               'priority':66622045,
               'change':{
                  'skill':{
                     'scheme204':{
                        'act':[{
                           'priority':66622045,
                           'type': 0,
                           'src': 2,
                           'tgt':[2, 2],
                           'cond':[['checkBuff', 2, 2, 2, '>=', 2]],
                           'removeBuff':2,
                           'nonSkill':2,
                           'noBfr':2,
                           'noAft':2,
                           'allTimes':2,
                           'info':['scheme204',2],
                           'eff':'effS204',               
                           'lv':7,
                        },
                        {
                           'priority':66622044,
                           'type': 3,
                           'src': 2,
                           'tgt':[0, 2],
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'buff':{'scheme204':{'round':2, 'prop':{'atkRate':225}}},
                           'follow':'scheme204',
                           
                           'eff':'effS204h',
                           'info':['得羊',0],
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'hpm':300},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme204_7',
                  'rslt':{'power':2800,'powerRate':360},
               },
               {
                  'special':[{
                     'priority':66622044,
                     'change':{
                        'skill':{
                           'scheme204.act[0].removeBuff':2,
                           'scheme204.act[2].buff.scheme204.prop.atkRate':225,
                        },
                     },
                  }],
               },
            ],
         },
         {  
            'passive':[
               {
                  'rslt':{'resArmy':40, 'powerRate':40},
               },
            ],
         },
      ],
   },
   '205':{           
      'rarity':3,
      'index':30,
      'type':2,
      'passive':[  
         {
            'cond':['type', '=', 0],
            'rslt':{'hpm':36},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'rslt':{'cha':2,'army[0].hpm':400},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme205_5',
                  'rslt':{'power':2600,'powerRate':520},
               },
            ],
            'special':[{
               'priority':66622075,
               'change':{
                  'skill':{
                     'scheme205':{
                        'act':[{
                           'priority':66622075,
                           'type': 5,                   
                           'src': -2,
                           'limitTimes': 2,
                           'order':{
                              
                              'src': 2,
                              'tgt':[0, 2],
                              'nonSkill':2,
                              'noBfr':2,
                              'noAft':2,
                              'isScheme':2,
                              'buff':{'scheme205':{'round':2, 'prop':{'dmgSkill':400}}},
                              'info':['scheme205',2],
                              'eff':'effS205',
                              'lv':7,
                           },
                           'nonSkill':2,
                           'noBfr':2,
                           'noAft':2,
                           'isScheme':0,
                           'info':['笑里',0],
                           'follow':{'skill225':2,'skill226':2,'skill227':2,'skill236':2,'skill242':2,'skill245':2},      
                           'eff':'effNull',
                           'time':0,
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'cha':2},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme205_7',
                  'rslt':{'power':2600,'powerRate':320},
               },
            ],
            'special':[{
               'priority':66622054,
               'change':{
                  'skill':{
                     'scheme205.act[0].order.buff.scheme205.round':2,
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'resArmy':60, 'powerRate':60},
               },
            ],
         },
      ],
   },
   '206':{           
      'rarity':3,
      'index':32,
      'type':2,
      'passive':[  
         {
            'cond':['type', '=', 2],
            'rslt':{'hpm':36},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'rslt':{'lead':2,'army[2].hpm':400},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme206_5',
                  'rslt':{'power':2600,'powerRate':520},
               },
            ],
            'special':[{
               'priority':66622065,
               'change':{
                  'skill':{
                     'scheme206':{
                        'act':[{
                           'priority':66622065,
                           'type': 5,                   
                           'src': -2,
                           'limitTimes': 2,
                           'order':{
                              'src': 2,
                              'tgt':[0, -2],
                              'nonSkill':2,
                              'noBfr':2,
                              'noAft':2,
                              'isScheme':2,
                              'buff':{'scheme206':{'round':2, 'prop':{'dmgSkill':300}}},   
                              'info':['scheme206',2],
                              'eff':'effS206',
                              'lv':7,
                           },
                           'nonSkill':2,
                           'noBfr':2,
                           'noAft':2,
                           'isScheme':0,
                           'info':['远近',0],
                           'follow':{'skill228':2,'skill229':2,'skill232':2,'skill239':2,'skill242':2},      
                           'eff':'effNull',
                           'time':0,
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'lead':2},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme206_7',
                  'rslt':{'power':2600,'powerRate':320},
               },
            ],
            'special':[{
               'priority':66622064,
               'change':{
                  'skill':{
                     'scheme206.act[0].order.buff.scheme206.round':2,
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'resArmy':60, 'powerRate':60},
               },
            ],
         },
      ],
   },
   '207':{           
      'rarity':3,
      'index':32,
      'type':2,
      'passive':[  
         {
            'cond':['type', '=', 2],
            'rslt':{'hpm':36},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'rslt':{'agi':2,'hpm':250},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme207_5',
                  'rslt':{'power':2600,'powerRate':520},
               },
            ],
            'special':[
               {
                  'priority':66622075,
                  'change':{
                     'skill':{
                        'scheme207':{
                           'act':[
                              {   
                                 'priority':66622075,
                                 'type': 5,                   
                                 'src': -2,
                                 'limitTimes': 2,
                                 'order':{
                                    'src': 2,
                                    'limitTimes': 2,
                                    'tgt':[0, -2],
                                    'nonSkill':2,
                                    'noBfr':2,
                                    'noAft':2,
                                    'isScheme':2,
                                    'buff':{'scheme207':{'round':2}},
                                    'info':['scheme207',2],
                                    'eff':'effS207',
                                    'lv':7,
                                 },
                                 'nonSkill':2,
                                 'noBfr':2,
                                 'noAft':2,
                                 'isScheme':0,
                                 'info':['金蝉',0],
                                 'follow':{'useBase':2, 'isFate':2},      
                                 'eff':'effNull',
                                 'time':0,
                              },
                              {     
                                 'priority':66622074,
                                 'type': 23,
                                 'src': 2,
                                 'round':{'0':20000},
                                 'cond':[['enemyArmy',-2]],  
                                 'limitTimes': 2,
                                 'tgt':[0, -2],
                                 'nonSkill':2,
                                 'noBfr':2,
                                 'noAft':2,
                                 'buff':{'scheme207':{'round':2}},
                                 'info':['scheme207',2],
                                 'eff':'effS207',
                                 'lv':7,
                              },
                           ],
                        },
                     },
                  },
                  'changeEnemy':{
                     'skill':{
                        'scheme207E':{
                           'act':[
                              {
                                 'priority':66622073,
                                 'type': 29,                   
                                 'src': -2,
                                 'limitTimes': 2,
                                 'order':{
                                    'limitTimes': 2,
                                    'team': 2,
                                    'src': 2,
                                    'tgt':[0, -2],
                                    'nonSkill':2,
                                    'noBfr':2,
                                    'noAft':2,
                                    'isScheme':2,
                                    'buff':{'scheme207':{'round':2}},
                                    'info':['scheme207',2],
                                    'eff':'effS207',
                                    'lv':7,
                                 },
                                 'nonSkill':2,
                                 'noBfr':2,
                                 'noAft':2,
                                 'isScheme':0,
                                 'info':['金蝉被杀',0],
                                 'eff':'effNull',
                                 'time':0,
                              },
                           ],
                        },
                     },
                  },
               },

            ],
         },
         {  
            'passive':[
               {
                  'rslt':{'agi':2,'str':2},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme207_7',
                  'rslt':{'power':2600,'powerRate':320},
               },
            ],
            'special':[{
               'priority':66622074,
               'change':{
                  'skill':{
                     'scheme207.act[0].order.buff.scheme207.prop.round':2,
                     'scheme207.act[2].buff.scheme207.prop.round':2,
                  },
               },
               'changeEnemy':{
                  'skill':{
                     'scheme207E.act[0].order.buff.scheme207.prop.round':2,
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'resArmy':60, 'powerRate':60},
               },
            ],
         },
      ],
   },
   '208':{           
      'rarity':4,
      'index':33,
      'type':2,
      'passive':[  
         {
            'rslt':{'%army_0.hpm':56},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'rslt':{'%army_0.hpm':500, '%army_0.crit':30},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme208_5',
                  'rslt':{'power':3400,'powerRate':680},
               },
            ],
            'special':[{
               'priority':66622085,
               'change':{
                  'skill':{
                     'scheme208':{
                        'act':[{
                           'priority':66622085,
                           'type': 23,                       
                           'src': 0,
                           'limitTimes': 2,
                           'condHpChange':[['<',-0.2],None],                  
                           'order':{
                                    
                                    
                                    'src': 2,
                                    'tgt':[2, 2],
                                    'allTimes':2,
                                    'dmgRealPower':300,              
                                    'dmgLimit':300,                  
                                    'hpLimitPoint':80,
                                    'nonSkill':2,
                                    'noBfr': 2,
                                    'noAft': 2,
                                    'isScheme':2,
                                    'crit':-9999999,
                                    'block':-9999999,
                                    'buff':{'buffFaction':{'rnd':500, 'round':2}},
                                    'info':['scheme208',2],
                                    'eff':'effS208',               
                                    'lv':7,
                           },
                           'eff':'effNull',
                           'isScheme':0,
                           'time': 0,
                           'info':['借刀',0],
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'%army_0.hpm':20000, '%army_0.crit':30},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme208_7',
                  'rslt':{'power':2200,'powerRate':240},
               },
            ],
            'special':[{
               'priority':66622084,
               'change':{
                  'skill':{
                     'scheme208_2':{
                        'act':[{
                           'priority':66622087,
                           'type': 2,	                   
                           'src': 2,	                   
                           'cond':['rnd',400],            
                           'times': -2,
                           'binding':{'tgtHero':2,'dmgReal':2000},     
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'info':['斩杀',0],
                           'follow':'scheme208',
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'resArmy':80, 'powerRate':80},
               },
            ],
         },
      ],
   },
   '209':{           
      'rarity':4,
      'index':34,
      'type':2,
      'passive':[  
         {
            'rslt':{'%army_2.hpm':56},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'rslt':{'%army_2.hpm':500, '%army_2.crit':30},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme209_5',
                  'rslt':{'power':3400,'powerRate':680},
               },
            ],
            'special':[{
               'priority':66622095,
               'change':{
                  'skill':{
                     'scheme209':{
                        'act':[{
                           'priority':66622095,
                           'type': 0,
                           'src': 2,
                           'tgt':[2, 0],
                           'round':{'2':20000},
                           'cond':[['enemyHpPoint','>',750]],
                           'allTimes':2,
                           'dmgRealPower':400,             
                           'dmgLimit':400,                 
                           'nonSkill':2,
                           'noBfr':2,
                           'noAft':2,
                           'crit':-9999999,
                           'block':-9999999,

                           'buff':{
                              'buffStun':{'round':2, 'rnd':800},
                              'buffWeak':{'round':2, 'rnd':0},
                           },
                           'info':['scheme209',2],
                           'eff':'effS209',               
                           'lv':7,
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'%army_2.hpm':20000, '%army_2.crit':30},  
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme209_7',
                  'rslt':{'power':2200,'powerRate':240},
               },
            ],
            'special':[{
               'priority':66622094,
               'change':{
                  'skill':{
                     'scheme209.act[0].dmgRealPower':250,
                     'scheme209.act[0].dmgLimit':250,
                     'scheme209.act[0].buff.buffWeak.rnd':700,
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'resArmy':80, 'powerRate':80},
               },
            ],
         },
      ],
   },
   '2200':{           
      'rarity':4,
      'index':95,
      'type':2,
      'passive':[  
         {
            'rslt':{'%army_2.hpm':56},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'rslt':{'%army_2.hpm':500, '%army_2.crit':30},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme2200_5',
                  'rslt':{'power':3400,'powerRate':680},
               },
            ],
            'special':[{
               'priority':666222005,
               'cond':[['compare','type','=']],
               'change':{
                  'skill':{
                     'scheme2200':{
                        'act':[{
                           'priority':666222005,
                           'type': 0,
                           'src': 2,
                           'round':{'2':20000},
                           'tgt':[0, -2],
                           'nonSkill':2,
                           'noBfr':2,
                           'noAft':2,
                           'allTimes':2,
                           'buff':{'scheme2200':{'round':2, 'prop':{'atkRate':200}}},  
                           'info':['scheme2200',2],
                           'eff':'effS2200',               
                           'lv':7,
                        },
                        {
                           'priority':666222004,
                           'type': 4,
                           'src': -2,
                           'round':{'near':20000},
                           'tgt':[0, -2],
                           'nonSkill':2,
                           'noBfr':2,
                           'noAft':2,
                           'times':-2,
                           'binding':{'defRate':-75},              
                           'info':['近战减防',0],
                        }],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'%army_2.hpm':20000, '%army_2.crit':30},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme2200_7',
                  'rslt':{'power':2200,'powerRate':240},
               },
            ],
            'special':[{
               'priority':666222004,
               'change':{
                  'skill':{
                     'scheme2200.act[0].buff.scheme2200.prop.atkRate':2000,    
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'resArmy':80, 'powerRate':80},
               },
            ],
         },
      ],
   },
   '222':{           
      'rarity':4,
      'index':36,
      'type':2,
      'passive':[  
         {
            'rslt':{'%army_3.hpm':56},
         },
      ],
      'install':[   
         {  
            'passive':[
               {
                  'rslt':{'%army_3.hpm':500, '%army_3.crit':30},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme222_5',
                  'rslt':{'power':3400,'powerRate':680},
               },
            ],
            'special':[{
               'priority':66622225,
               'cond':['enemy','rarity', 4],
               'change':{
                  'prop':{
                     'armys[0].resFinalLevel':250,
                     'armys[2].resFinalLevel':250,
                  },
                  'skill':{
                     'skill289.act[0].round':{'4':20000},        
                     'skill222.act[0].round':{'4':20000},        
                     
                     'skill292.act[0].round':{'4':20000},        
                     'skill292.act[0].round':{'4':20000},        
                     'scheme222':{
                        'act':[
                          {
                            'priority':66622225,
                            'type': 0,
                            'src': 2,
                            'tgt':[0, -2],
                            'round':{'3':20000},
                            'nonSkill':2,
                            'noBfr':2,
                            'noAft':2,
                            'allTimes':2,
                            
                            'info':['scheme222',2],
                            'eff':'effS222',               
                            'lv':7,
                          },
                          {
                            'priority':66622224,
                            'type': 2,
                            'src': -2,
                            'round':{'4':20000},
                            'follow':{'skill289':2,'skill222':2,'skill292':2,'skill292':2},    
                            'nonSkill':2,
                            'times':-2,  
                            'binding':{
                               'dmgScale':2000,
                            },
                            'info':['癫狂',0],
                          },
                        ],
                     },
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'%army_3.hpm':20000, '%army_3.crit':30},
               },
            ],
         },
         {  
            'passive':[
               {
                  'info':'scheme222_7',
                  'rslt':{'power':2200,'powerRate':240},
               },
            ],
            'special':[{
               'priority':66622224,
               'change':{
                  'skill':{
                     'scheme222.act[0].summonReal':750,
                     'scheme222.act[0].summon':75,
                     'scheme222.act[0].eff':'effS222_2',
                  },
               },
            }],
         },
         {  
            'passive':[
               {
                  'rslt':{'resArmy':80, 'powerRate':80},
               },
            ],
         },
      ],
   },

}