{

    'test_publish': 1, ## 无意义，发布用


##########################################################################################

################################################################################
###################官邸升级奖励########################################
################################################################################
################################################################################
	'levelup':{
'reward':{




9:[6,{'item104':1,'gold':100000,'iron':200000,'wood':200000,'food':200000,}],
10:[30,{'item055':100,'gold':100000,'iron':200000,'wood':200000,'food':200000,}],
11:[30,{'item034':20000,'gold':150000,'iron':300000,'wood':300000,'food':300000,}],
12:[30,{'item059':100,'gold':150000,'iron':300000,'wood':300000,'food':300000,}],
13:[30,{'item057':100,'gold':200000,'iron':400000,'wood':400000,'food':400000,}],
14:[30,{'item036':10,'item079':200,'item080':200,'item081':200,'item082':200,}],
15:[30,{'item075':100,'gold':200000,'iron':400000,'wood':400000,'food':400000,}],
16:[30,{'item036':10,'item107':200,'item113':200,'item124':200,'item130':400,}],
17:[30,{'item070':30,'gold':200000,'iron':400000,'wood':400000,'food':400000,}],
18:[30,{'item071':30,'gold':200000,'iron':400000,'wood':400000,'food':400000,}],
19:[30,{'item059':200,'gold':200000,'iron':400000,'wood':400000,'food':400000,}],
20:[30,{'item057':200,'gold':200000,'iron':400000,'wood':400000,'food':400000,}],
21:[30,{'item061':50,'gold':200000,'iron':400000,'wood':400000,'food':400000,}],
22:[30,{'item036':20,'item406':200,'item407':200,'item408':200,'item409':200,}],
23:[30,{'item036':20,'item107':500,'item113':500,'item124':500,'item130':1000,}],
24:[30,{'item036':20,'item212':100,'item218':100,'item205':100,'item224':100,}],
25:[68,{'item036':20,'item191':10,'item189':50,'item030':20,'item187':50,}],
26:[68,{'item036':30,'item236':100,'item237':100,'item234':100,'item243':100,}],
27:[68,{'item036':30,'item189':100,'item603':5,'item271':100,'item238':100,}],
28:[128,{'item036':60,'item1030':20,'item292':200,'item291':200,'item280':200,}],
29:[128,{'item036':60,'item191':50,'item206':200,'item211':200,'item276':200,}],
30:[328,{'item036':120,'item1031':20,'item246':200,'item245':200,'item016':10,}],






},


		'time':10800,		#持续时间，秒
		'character':'hero704',	#立绘
		'title':'502003',		#标题
		'tips':'502004',		#提示文本
		'tab_name':'502009',		#提示文本
	},



################################################################################
###################官邸升级礼包########################################
################################################################################
################################################################################
	'levelup_gift':{
		'reward':{





31:[ 'gd0603',{'item016':5,'item1208':40000,'item1189':1,'item295':200,'item296':200,}],
32:[ 'gd0604',{'item016':5,'item1209':40000,'item1190':1,'item293':200,'item294':200,}],
33:[ 'gd0605',{'item016':5,'item1210':40000,'item1191':1,'item279':200,'item278':200,}],

		
		},


		'time':21600,		#持续时间，秒
		'character':'hero704',	#立绘
		'title':'502085',		#标题
		'tips':'502086',		#提示文本
	},




################################################################################
###################私人订制########################################
################################################################################
################################################################################
 'optional_ones':{  #私人订制（不能用抵扣券，不参与其他充值活
  'switch':1,   #功能总开关，0表示关闭，1表示开启
  'new_date':datetime.datetime(2020,8,7,10,0), #功能上线日期，当本服开服日期早于这个日期时，本服的此活动以这个日期作为起始日期
  'show_time':[[10,0],[22,0]],  #开启日10点 至 结束日22点显示入口
  'warehouse_name':['optional_ones_01','optional_ones_02','optional_ones_03'],  #自选库名称（对应0，1，2）
  'open_days':[  #开启日期
   [1,2,'reward1','items1'],
   [6,8,'reward2','items1'],
   [24,26,'reward2','items1'],
   [30,32,'reward2','items1'],
   [56,58,'reward2','items1'],
   [62,64,'reward2','items1'],
   [90,92,'reward2','items1'],
   [96,98,'reward2','items1'],
   [124,126,'reward2','items1'],
   [130,132,'reward2','items1'],
  ],

  'reward1':{  #关联payid：【黄金奖励（可空），数量+目标，限购次数（-1=无限）】
   'gd0201':[['coin',300],[0,1,],3],
   'gd0202':[['coin',680],[0,1,1,],3],
   'gd0203':[['coin',1280],[0,1,1,1,1,],3],
   'gd0204':[['coin',3280],[0,1,2,],3],
   'gd0205':[['coin',6480],[0,1,1,2,2,],2],
  },
  'reward2':{  #关联payid：【黄金奖励（可空），数量+目标，限购次数（-1=无限）】
   'gd0206':[['coin',300],[0,1,],3],
   'gd0207':[['coin',680],[0,1,1,],3],
   'gd0208':[['coin',1280],[0,1,1,1,1,],3],
   'gd0209':[['coin',3280],[0,1,2,],3],
   'gd0210':[['coin',6480],[0,1,1,2,2,],2],
   'gd0211':[['coin',9980],[1,1,2,2,2,],2],
  },
  'items1':{  #自选库（单个库里10个奖励）（同一个奖励库下，每个奖励只能选择一次）
   0:{'item003':3,'item013':4,'item021':7,'item036':8,'item056':1,'item058':50,'item059':40,},
   1:{'item004':8,'item014':16,'item191':7,'item1008':4,'item190':35,},
   2:{'item084':3,'item035':3,'item015':70,'item016':35,'item192':40,'item1043':1,'item1044':2,},
  },
 },

###############################################################
##################################################################################
#####################                                 ############################  
#####################         老活动                  ############################
#####################                                 ############################  
##################################################################################

	'free_buy':{
		'show_info':{
			'pay_gold':['pay_gold_name','pay_gold_info','hero404'],	
			'pay_food':['pay_food_name','pay_food_info','hero403'],	
			'pay_wood':['pay_wood_name','pay_wood_info','hero401'],	
			'pay_iron':['pay_iron_name','pay_iron_info','hero405'],	
			'buy_gold':['buy_gold_name','buy_gold_info','hero408'],	
			'buy_food':['buy_food_name','buy_food_info','hero404'],	
			'buy_wood':['buy_wood_name','buy_wood_info','hero401'],	
			'buy_iron':['buy_iron_name','buy_iron_info','hero405'],	
			'baggage_gold':['baggage_gold_name','baggage_gold_info','hero403'],	
			'baggage_food':['baggage_food_name','baggage_food_info','hero401'],	
			'baggage_wood':['baggage_wood_name','baggage_wood_info','hero402'],	
			'baggage_iron':['baggage_iron_name','baggage_iron_info','hero405'],	
		},





















 











    '6':{
            'gold':{
                     'limit':[[20000,0.3],[50000,0.1]],
                     'event':[
                             [['pay',120,60,36000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,189000],1500],
                             [['buy',120,50,21000],1000],
                             [['baggage',60,20,2],1000],
                                   ],
                       },
              'food':{
                        'limit':[[20000,0.3],[50000,0.1]],
                        'event':[
                                    [['pay',120,60,72000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,378000],1500],
                                    [['buy',120,50,42000],1000],
                                     [['baggage',60,20,2],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[20000,0.3],[50000,0.1]],
                         'event':[
                                  [['pay',120,60,72000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,378000],1500],
                                  [['buy',120,50,42000],1000],
                                   [['baggage',60,20,2],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[20000,0.3],[50000,0.1]],
                           'event':[
                                    [['pay',120,60,72000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,378000],1500],
                                    [['buy',120,50,42000],1000],
                                   [['baggage',60,20,2],1000],
                                        ],
                        },
                         },

    '7':{
            'gold':{
                     'limit':[[20000,0.3],[50000,0.1]],
                     'event':[
                             [['pay',120,60,36000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,189000],1500],
                             [['buy',120,50,21000],1000],
                             [['baggage',60,40,3],1000],
                                   ],
                       },
              'food':{
                        'limit':[[20000,0.3],[50000,0.1]],
                        'event':[
                                    [['pay',120,60,72000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,378000],1500],
                                    [['buy',120,50,42000],1000],
                                     [['baggage',60,40,3],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[20000,0.3],[50000,0.1]],
                         'event':[
                                  [['pay',120,60,72000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,378000],1500],
                                  [['buy',120,50,42000],1000],
                                   [['baggage',60,40,3],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[20000,0.3],[50000,0.1]],
                           'event':[
                                    [['pay',120,60,72000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,378000],1500],
                                    [['buy',120,50,42000],1000],
                                   [['baggage',60,40,3],1000],
                                        ],
                        },
                         },

    '8':{
            'gold':{
                     'limit':[[20000,0.3],[50000,0.1]],
                     'event':[
                             [['pay',120,60,36000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,189000],1500],
                             [['buy',120,50,21000],1000],
                             [['baggage',60,40,3],1000],
                                   ],
                       },
              'food':{
                        'limit':[[20000,0.3],[50000,0.1]],
                        'event':[
                                    [['pay',120,60,72000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,378000],1500],
                                    [['buy',120,50,42000],1000],
                                     [['baggage',60,40,3],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[20000,0.3],[50000,0.1]],
                         'event':[
                                  [['pay',120,60,72000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,378000],1500],
                                  [['buy',120,50,42000],1000],
                                   [['baggage',60,40,3],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[20000,0.3],[50000,0.1]],
                           'event':[
                                    [['pay',120,60,72000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,378000],1500],
                                    [['buy',120,50,42000],1000],
                                   [['baggage',60,40,3],1000],
                                        ],
                        },
                         },

    '9':{
            'gold':{
                     'limit':[[20000,0.3],[50000,0.1]],
                     'event':[
                             [['pay',120,60,39000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,204750],1500],
                             [['buy',120,60,27300],1000],
                             [['baggage',60,40,3],1000],
                                   ],
                       },
              'food':{
                        'limit':[[20000,0.3],[50000,0.1]],
                        'event':[
                                    [['pay',120,60,78000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,409500],1500],
                                    [['buy',120,60,54600],1000],
                                     [['baggage',60,40,3],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[20000,0.3],[50000,0.1]],
                         'event':[
                                  [['pay',120,60,78000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,409500],1500],
                                  [['buy',120,60,54600],1000],
                                   [['baggage',60,40,3],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[20000,0.3],[50000,0.1]],
                           'event':[
                                    [['pay',120,60,78000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,409500],1500],
                                    [['buy',120,60,54600],1000],
                                   [['baggage',60,40,3],1000],
                                        ],
                        },
                         },

    '10':{
            'gold':{
                     'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                     'event':[
                             [['pay',120,60,39000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,204750],1500],
                             [['buy',120,60,27300],1000],
                             [['baggage',60,40,3],1000],
                                   ],
                       },
              'food':{
                        'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                        'event':[
                                    [['pay',120,60,78000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,409500],1500],
                                    [['buy',120,60,54600],1000],
                                     [['baggage',60,40,3],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                         'event':[
                                  [['pay',120,60,78000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,409500],1500],
                                  [['buy',120,60,54600],1000],
                                   [['baggage',60,40,3],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                           'event':[
                                    [['pay',120,60,78000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,409500],1500],
                                    [['buy',120,60,54600],1000],
                                   [['baggage',60,40,3],1000],
                                        ],
                        },
                         },

    '11':{
            'gold':{
                     'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                     'event':[
                             [['pay',120,60,39000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,204750],1500],
                             [['buy',120,60,27300],1000],
                             [['baggage',60,40,3],1000],
                                   ],
                       },
              'food':{
                        'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                        'event':[
                                    [['pay',120,60,78000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,409500],1500],
                                    [['buy',120,60,54600],1000],
                                     [['baggage',60,40,3],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                         'event':[
                                  [['pay',120,60,78000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,409500],1500],
                                  [['buy',120,60,54600],1000],
                                   [['baggage',60,40,3],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                           'event':[
                                    [['pay',120,60,78000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,409500],1500],
                                    [['buy',120,60,54600],1000],
                                   [['baggage',60,40,3],1000],
                                        ],
                        },
                         },

    '12':{
            'gold':{
                     'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                     'event':[
                             [['pay',120,60,42000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,220500],1500],
                             [['buy',120,70,34300],1000],
                             [['baggage',60,40,3],1000],
                                   ],
                       },
              'food':{
                        'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                        'event':[
                                    [['pay',120,60,84000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,441000],1500],
                                    [['buy',120,70,68600],1000],
                                     [['baggage',60,40,3],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                         'event':[
                                  [['pay',120,60,84000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,441000],1500],
                                  [['buy',120,70,68600],1000],
                                   [['baggage',60,40,3],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                           'event':[
                                    [['pay',120,60,84000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,441000],1500],
                                    [['buy',120,70,68600],1000],
                                   [['baggage',60,40,3],1000],
                                        ],
                        },
                         },

    '13':{
            'gold':{
                     'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                     'event':[
                             [['pay',120,60,42000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,220500],1500],
                             [['buy',120,70,34300],1000],
                             [['baggage',60,60,4],1000],
                                   ],
                       },
              'food':{
                        'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                        'event':[
                                    [['pay',120,60,84000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,441000],1500],
                                    [['buy',120,70,68600],1000],
                                     [['baggage',60,60,4],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                         'event':[
                                  [['pay',120,60,84000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,441000],1500],
                                  [['buy',120,70,68600],1000],
                                   [['baggage',60,60,4],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                           'event':[
                                    [['pay',120,60,84000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,441000],1500],
                                    [['buy',120,70,68600],1000],
                                   [['baggage',60,60,4],1000],
                                        ],
                        },
                         },

    '14':{
            'gold':{
                     'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                     'event':[
                             [['pay',120,60,42000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,220500],1500],
                             [['buy',120,70,34300],1000],
                             [['baggage',60,60,4],1000],
                                   ],
                       },
              'food':{
                        'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                        'event':[
                                    [['pay',120,60,84000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,441000],1500],
                                    [['buy',120,70,68600],1000],
                                     [['baggage',60,60,4],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                         'event':[
                                  [['pay',120,60,84000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,441000],1500],
                                  [['buy',120,70,68600],1000],
                                   [['baggage',60,60,4],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                           'event':[
                                    [['pay',120,60,84000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,441000],1500],
                                    [['buy',120,70,68600],1000],
                                   [['baggage',60,60,4],1000],
                                        ],
                        },
                         },

    '15':{
            'gold':{
                     'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                     'event':[
                             [['pay',120,60,45000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,236250],1500],
                             [['buy',120,80,42000],1000],
                             [['baggage',60,60,4],1000],
                                   ],
                       },
              'food':{
                        'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                        'event':[
                                    [['pay',120,60,90000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,472500],1500],
                                    [['buy',120,80,84000],1000],
                                     [['baggage',60,60,4],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                         'event':[
                                  [['pay',120,60,90000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,472500],1500],
                                  [['buy',120,80,84000],1000],
                                   [['baggage',60,60,4],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                           'event':[
                                    [['pay',120,60,90000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,472500],1500],
                                    [['buy',120,80,84000],1000],
                                   [['baggage',60,60,4],1000],
                                        ],
                        },
                         },

    '16':{
            'gold':{
                     'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                     'event':[
                             [['pay',120,60,45000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,236250],1500],
                             [['buy',120,80,42000],1000],
                             [['baggage',60,60,4],1000],
                                   ],
                       },
              'food':{
                        'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                        'event':[
                                    [['pay',120,60,90000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,472500],1500],
                                    [['buy',120,80,84000],1000],
                                     [['baggage',60,60,4],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                         'event':[
                                  [['pay',120,60,90000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,472500],1500],
                                  [['buy',120,80,84000],1000],
                                   [['baggage',60,60,4],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                           'event':[
                                    [['pay',120,60,90000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,472500],1500],
                                    [['buy',120,80,84000],1000],
                                   [['baggage',60,60,4],1000],
                                        ],
                        },
                         },

    '17':{
            'gold':{
                     'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                     'event':[
                             [['pay',120,60,45000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,236250],1500],
                             [['buy',120,80,42000],1000],
                             [['baggage',60,60,4],1000],
                                   ],
                       },
              'food':{
                        'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                        'event':[
                                    [['pay',120,60,90000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,472500],1500],
                                    [['buy',120,80,84000],1000],
                                     [['baggage',60,60,4],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                         'event':[
                                  [['pay',120,60,90000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,472500],1500],
                                  [['buy',120,80,84000],1000],
                                   [['baggage',60,60,4],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                           'event':[
                                    [['pay',120,60,90000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,472500],1500],
                                    [['buy',120,80,84000],1000],
                                   [['baggage',60,60,4],1000],
                                        ],
                        },
                         },

    '18':{
            'gold':{
                     'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                     'event':[
                             [['pay',120,60,48000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,252000],1500],
                             [['buy',120,90,50400],500],
                             [['baggage',60,60,4],1000],
                                   ],
                       },
              'food':{
                        'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                        'event':[
                                    [['pay',120,60,96000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,504000],1500],
                                    [['buy',120,90,100800],500],
                                     [['baggage',60,60,4],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                         'event':[
                                  [['pay',120,60,96000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,504000],1500],
                                  [['buy',120,90,100800],500],
                                   [['baggage',60,60,4],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                           'event':[
                                    [['pay',120,60,96000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,504000],1500],
                                    [['buy',120,90,100800],500],
                                   [['baggage',60,60,4],1000],
                                        ],
                        },
                         },

    '19':{
            'gold':{
                     'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                     'event':[
                             [['pay',120,60,48000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,252000],1500],
                             [['buy',120,90,50400],1000],
                             [['baggage',60,100,5],1000],
                                   ],
                       },
              'food':{
                        'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                        'event':[
                                    [['pay',120,60,96000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,504000],1500],
                                    [['buy',120,90,100800],1000],
                                     [['baggage',60,100,5],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                         'event':[
                                  [['pay',120,60,96000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,504000],1500],
                                  [['buy',120,90,100800],1000],
                                   [['baggage',60,100,5],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                           'event':[
                                    [['pay',120,60,96000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,504000],1500],
                                    [['buy',120,90,100800],1000],
                                   [['baggage',60,100,5],1000],
                                        ],
                        },
                         },

    '20':{
            'gold':{
                     'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                     'event':[
                             [['pay',120,60,48000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,252000],1500],
                             [['buy',120,90,50400],1000],
                             [['baggage',60,100,5],1000],
                                   ],
                       },
              'food':{
                        'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                        'event':[
                                    [['pay',120,60,96000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,504000],1500],
                                    [['buy',120,90,100800],1000],
                                     [['baggage',60,100,5],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                         'event':[
                                  [['pay',120,60,96000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,504000],1500],
                                  [['buy',120,90,100800],1000],
                                   [['baggage',60,100,5],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                           'event':[
                                    [['pay',120,60,96000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,504000],1500],
                                    [['buy',120,90,100800],1000],
                                   [['baggage',60,100,5],1000],
                                        ],
                        },
                         },

    '21':{
            'gold':{
                     'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                     'event':[
                             [['pay',120,60,51000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,267750],1500],
                             [['buy',120,100,59500],1000],
                             [['baggage',60,100,5],1000],
                                   ],
                       },
              'food':{
                        'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                        'event':[
                                    [['pay',120,60,102000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,535500],1500],
                                    [['buy',120,100,119000],1000],
                                     [['baggage',60,100,5],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                         'event':[
                                  [['pay',120,60,102000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,535500],1500],
                                  [['buy',120,100,119000],1000],
                                   [['baggage',60,100,5],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                           'event':[
                                    [['pay',120,60,102000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,535500],1500],
                                    [['buy',120,100,119000],1000],
                                   [['baggage',60,100,5],1000],
                                        ],
                        },
                         },

    '22':{
            'gold':{
                     'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                     'event':[
                             [['pay',120,60,51000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,267750],1500],
                             [['buy',120,100,59500],1000],
                             [['baggage',60,100,5],1000],
                                   ],
                       },
              'food':{
                        'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                        'event':[
                                    [['pay',120,60,102000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,535500],1500],
                                    [['buy',120,100,119000],1000],
                                     [['baggage',60,100,5],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                         'event':[
                                  [['pay',120,60,102000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,535500],1500],
                                  [['buy',120,100,119000],1000],
                                   [['baggage',60,100,5],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                           'event':[
                                    [['pay',120,60,102000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,535500],1500],
                                    [['buy',120,100,119000],1000],
                                   [['baggage',60,100,5],1000],
                                        ],
                        },
                         },

    '23':{
            'gold':{
                     'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                     'event':[
                             [['pay',120,60,51000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,267750],1500],
                             [['buy',120,100,59500],1000],
                             [['baggage',60,100,5],1000],
                                   ],
                       },
              'food':{
                        'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                        'event':[
                                    [['pay',120,60,102000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,535500],1500],
                                    [['buy',120,100,119000],1000],
                                     [['baggage',60,100,5],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                         'event':[
                                  [['pay',120,60,102000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,535500],1500],
                                  [['buy',120,100,119000],1000],
                                   [['baggage',60,100,5],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                           'event':[
                                    [['pay',120,60,102000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,535500],1500],
                                    [['buy',120,100,119000],1000],
                                   [['baggage',60,100,5],1000],
                                        ],
                        },
                         },

    '24':{
            'gold':{
                     'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                     'event':[
                             [['pay',120,60,54000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,283500],1000],
                             [['buy',120,110,69300],1000],
                             [['baggage',60,100,5],1000],
                             [['pay',120,680,765000],1500],
                             [['pay',120,1280,1728000],1500],
                                   ],
                       },
              'food':{
                        'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                        'event':[
                                    [['pay',120,60,108000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,567000],1000],
                                    [['buy',120,110,138600],1000],
                                     [['baggage',60,100,5],1000],
                             [['pay',120,680,1530000],1500],
                             [['pay',120,1280,3456000],1500],
                                   ],
                            },
                'wood':{
                         'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                         'event':[
                                  [['pay',120,60,108000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,567000],1000],
                                  [['buy',120,110,138600],1000],
                                   [['baggage',60,100,5],1000],
                             [['pay',120,680,1530000],1500],
                             [['pay',120,1280,3456000],1500],
                                      ],
                          },
                 'iron':{
                           'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                           'event':[
                                    [['pay',120,60,108000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,567000],1000],
                                    [['buy',120,110,138600],1000],
                                   [['baggage',60,100,5],1000],
                             [['pay',120,680,1530000],1500],
                             [['pay',120,1280,3456000],1500],
                                        ],
                        },
                         },

    '25':{
            'gold':{
                     'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                     'event':[
                             [['pay',120,60,54000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,283500],1000],
                             [['buy',120,110,69300],1000],
                             [['baggage',60,150,6],1000],
                             [['pay',120,680,765000],1500],
                             [['pay',120,1280,1728000],1500],
                                   ],
                       },
              'food':{
                        'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                        'event':[
                                    [['pay',120,60,108000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,567000],1000],
                                    [['buy',120,110,138600],1000],
                                     [['baggage',60,150,6],1000],
                             [['pay',120,680,1530000],1500],
                             [['pay',120,1280,3456000],1500],
                                   ],
                            },
                'wood':{
                         'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                         'event':[
                                  [['pay',120,60,108000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,567000],1000],
                                  [['buy',120,110,138600],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1530000],1500],
                             [['pay',120,1280,3456000],1500],
                                      ],
                          },
                 'iron':{
                           'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                           'event':[
                                    [['pay',120,60,108000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,567000],1000],
                                    [['buy',120,110,138600],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1530000],1500],
                             [['pay',120,1280,3456000],1500],
                                        ],
                        },
                         },

    '26':{
            'gold':{
                     'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                     'event':[
                             [['pay',120,60,54000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,283500],1000],
                             [['buy',120,110,69300],1000],
                             [['baggage',60,150,6],1000],
                             [['pay',120,680,765000],1500],
                             [['pay',120,1280,1728000],1500],
                                   ],
                       },
              'food':{
                        'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                        'event':[
                                    [['pay',120,60,108000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,567000],1000],
                                    [['buy',120,110,138600],1000],
                                     [['baggage',60,150,6],1000],
                             [['pay',120,680,1530000],1500],
                             [['pay',120,1280,3456000],1500],
                                   ],
                            },
                'wood':{
                         'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                         'event':[
                                  [['pay',120,60,108000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,567000],1000],
                                  [['buy',120,110,138600],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1530000],1500],
                             [['pay',120,1280,3456000],1500],
                                      ],
                          },
                 'iron':{
                           'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                           'event':[
                                    [['pay',120,60,108000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,567000],1000],
                                    [['buy',120,110,138600],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1530000],1500],
                             [['pay',120,1280,3456000],1500],
                                        ],
                        },
                         },

    '27':{
            'gold':{
                     'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                     'event':[
                             [['pay',120,60,57000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,299250],1000],
                             [['buy',120,120,79800],1000],
                             [['baggage',60,150,6],1000],
                             [['pay',120,680,807500],1500],
                             [['pay',120,1280,1824000],1500],
                                   ],
                       },
              'food':{
                        'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                        'event':[
                                    [['pay',120,60,114000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,598500],1000],
                                    [['buy',120,120,159600],1000],
                                     [['baggage',60,150,6],1000],
                             [['pay',120,680,1615000],1500],
                             [['pay',120,1280,3648000],1500],
                                   ],
                            },
                'wood':{
                         'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                         'event':[
                                  [['pay',120,60,114000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,598500],1000],
                                  [['buy',120,120,159600],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1615000],1500],
                             [['pay',120,1280,3648000],1500],
                                      ],
                          },
                 'iron':{
                           'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                           'event':[
                                    [['pay',120,60,114000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,598500],1000],
                                    [['buy',120,120,159600],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1615000],1500],
                             [['pay',120,1280,3648000],1500],
                                        ],
                        },
                         },

    '28':{
            'gold':{
                     'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                     'event':[
                             [['pay',120,60,57000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,299250],1000],
                             [['buy',120,120,79800],1000],
                             [['baggage',60,150,6],1000],
                             [['pay',120,680,807500],1500],
                             [['pay',120,1280,1824000],1500],
                                   ],
                       },
              'food':{
                        'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                        'event':[
                                    [['pay',120,60,114000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,598500],1000],
                                    [['buy',120,120,159600],1000],
                                     [['baggage',60,150,6],1000],
                             [['pay',120,680,1615000],1500],
                             [['pay',120,1280,3648000],1500],
                                   ],
                            },
                'wood':{
                         'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                         'event':[
                                  [['pay',120,60,114000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,598500],1000],
                                  [['buy',120,120,159600],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1615000],1500],
                             [['pay',120,1280,3648000],1500],
                                      ],
                          },
                 'iron':{
                           'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                           'event':[
                                    [['pay',120,60,114000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,598500],1000],
                                    [['buy',120,120,159600],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1615000],1500],
                             [['pay',120,1280,3648000],1500],
                                        ],
                        },
                         },

    '29':{
            'gold':{
                     'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                     'event':[
                             [['pay',120,60,57000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,299250],1000],
                             [['buy',120,120,79800],1000],
                             [['baggage',60,150,6],1000],
                             [['pay',120,680,807500],1500],
                             [['pay',120,1280,1824000],1500],
                                   ],
                       },
              'food':{
                        'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                        'event':[
                                    [['pay',120,60,114000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,598500],1000],
                                    [['buy',120,120,159600],1000],
                                     [['baggage',60,150,6],1000],
                             [['pay',120,680,1615000],1500],
                             [['pay',120,1280,3648000],1500],
                                   ],
                            },
                'wood':{
                         'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                         'event':[
                                  [['pay',120,60,114000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,598500],1000],
                                  [['buy',120,120,159600],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1615000],1500],
                             [['pay',120,1280,3648000],1500],
                                      ],
                          },
                 'iron':{
                           'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                           'event':[
                                    [['pay',120,60,114000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,598500],1000],
                                    [['buy',120,120,159600],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1615000],1500],
                             [['pay',120,1280,3648000],1500],
                                        ],
                        },
                         },

    '30':{
            'gold':{
                     'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                     'event':[
                             [['pay',120,60,60000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,315000],1000],
                             [['buy',120,130,91000],1000],
                             [['baggage',60,150,6],1000],
                             [['pay',120,680,850000],1500],
                             [['pay',120,1280,1920000],1500],
                                   ],
                       },
              'food':{
                        'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                        'event':[
                                    [['pay',120,60,120000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,630000],1000],
                                    [['buy',120,130,182000],1000],
                                     [['baggage',60,150,6],1000],
                             [['pay',120,680,1700000],1500],
                             [['pay',120,1280,3840000],1500],
                                   ],
                            },
                'wood':{
                         'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                         'event':[
                                  [['pay',120,60,120000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,630000],1000],
                                  [['buy',120,130,182000],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1700000],1500],
                             [['pay',120,1280,3840000],1500],
                                      ],
                          },
                 'iron':{
                           'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                           'event':[
                                    [['pay',120,60,120000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,630000],1000],
                                    [['buy',120,130,182000],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1700000],1500],
                             [['pay',120,1280,3840000],1500],
                                        ],
                        },
                         },

    '31':{
            'gold':{
                     'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                     'event':[
                             [['pay',120,60,60000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,315000],1000],
                             [['buy',120,130,91000],1000],
                             [['baggage',60,150,6],1000],
                             [['pay',120,680,850000],1500],
                             [['pay',120,1280,1920000],1500],
                                   ],
                       },
              'food':{
                        'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                        'event':[
                                    [['pay',120,60,120000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,630000],1000],
                                    [['buy',120,130,182000],1000],
                                     [['baggage',60,150,6],1000],
                             [['pay',120,680,1700000],1500],
                             [['pay',120,1280,3840000],1500],
                                   ],
                            },
                'wood':{
                         'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                         'event':[
                                  [['pay',120,60,120000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,630000],1000],
                                  [['buy',120,130,182000],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1700000],1500],
                             [['pay',120,1280,3840000],1500],
                                      ],
                          },
                 'iron':{
                           'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                           'event':[
                                    [['pay',120,60,120000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,630000],1000],
                                    [['buy',120,130,182000],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1700000],1500],
                             [['pay',120,1280,3840000],1500],
                                        ],
                        },
                         },

    '32':{
            'gold':{
                     'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                     'event':[
                             [['pay',120,60,60000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,315000],1000],
                             [['buy',120,130,91000],1000],
                             [['baggage',60,150,6],1000],
                             [['pay',120,680,850000],1500],
                             [['pay',120,1280,1920000],1500],
                                   ],
                       },
              'food':{
                        'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                        'event':[
                                    [['pay',120,60,120000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,630000],1000],
                                    [['buy',120,130,182000],1000],
                                     [['baggage',60,150,6],1000],
                             [['pay',120,680,1700000],1500],
                             [['pay',120,1280,3840000],1500],
                                   ],
                            },
                'wood':{
                         'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                         'event':[
                                  [['pay',120,60,120000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,630000],1000],
                                  [['buy',120,130,182000],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1700000],1500],
                             [['pay',120,1280,3840000],1500],
                                      ],
                          },
                 'iron':{
                           'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                           'event':[
                                    [['pay',120,60,120000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,630000],1000],
                                    [['buy',120,130,182000],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1700000],1500],
                             [['pay',120,1280,3840000],1500],
                                        ],
                        },
                         },

    '33':{
            'gold':{
                     'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                     'event':[
                             [['pay',120,60,63000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,330750],1000],
                             [['buy',120,140,102900],1000],
                             [['baggage',60,150,6],1000],
                             [['pay',120,680,892500],1500],
                             [['pay',120,1280,2016000],1500],
                                   ],
                       },
              'food':{
                        'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                        'event':[
                                    [['pay',120,60,126000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,661500],1000],
                                    [['buy',120,140,205800],1000],
                                     [['baggage',60,150,6],1000],
                             [['pay',120,680,1785000],1500],
                             [['pay',120,1280,4032000],1500],
                                   ],
                            },
                'wood':{
                         'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                         'event':[
                                  [['pay',120,60,126000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,661500],1000],
                                  [['buy',120,140,205800],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1785000],1500],
                             [['pay',120,1280,4032000],1500],
                                      ],
                          },
                 'iron':{
                           'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                           'event':[
                                    [['pay',120,60,126000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,661500],1000],
                                    [['buy',120,140,205800],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1785000],1500],
                             [['pay',120,1280,4032000],1500],
                                        ],
                        },
                         },




	},

##########################购买政务########################
	'buy_gtask':{
		'name':'buy_gtask_name',
		'info':'buy_gtask_info',
		'hero':'hero761',
		'time':180,	#分钟
		'pay_money':30,	#需要充值的money
		'mulit':[1,3],	#居功至伟(reward_mulit)，3次
		'reward_key':'item021',	#黄金沙漏
		'para':[5,270,10],	#a为玩家完成第5次政务触发，if(玩家已完成政务数 < b && 全服最高政务数-玩家已完成政务数>c && 今日未触发过本事件)

	},


##########################在线奖励########################
'act_online':{

	'pay_money':6,    #充值额度，大于等于此数量后，玩家可以领取充值奖励；		

	'online': [
		{
			'cd':10,    #分钟
			'reward':{'gold':10000,'item002':2},
			'pay_reward':{'item037':10,'item032':2},
		},
		{
			'cd':30,    #分钟
			'reward':{'gold':10000,'food':10000},
			'pay_reward':{'item037':10,'item032':3},
		},
		{
			'cd':60,    #分钟
			'reward':{'gold':10000,'wood':10000},
			'pay_reward':{'item037':10,'item032':5},
		},
		{
			'cd':120,    #分钟
			'reward':{'gold':10000,'iron':10000},
			'pay_reward':{'item037':10,'item032':10},
		},
	],
},


########################每日许愿############################
	'act_wish':{
		'wish_rewchoo_num':4,   #许愿奖励数量
		'wish_initrew':['coin',100],    #许愿初始奖励
		'wish_multiple':2,    #充值后奖励翻倍
		'hero_icon':'hero407',   #使用英雄图片
		'wish_rewpond':[
			['gold',5000],['gold',5000],['food',10000],['food',10000],['wood',10000],['wood',10000],['iron',10000],['iron',10000],['item032',5],['item037',5],['item021',1],['item024',1],['item027',2],['item057',10],
		],   ####许愿奖池
		'wish_days':7,    #许愿获得额外奖励的累计天数
		'wish_daysrew':[
                        {'item071':4,'item070':4},        #首次7天领取
                        {'item071':3,'item070':3,'item073':3,'item069':3,'item074':3},  
		],#按照数组顺序轮换领取，最后一组循环
	},
		
########################永久单笔充值############################
	'independ_pay':[
			{
				'pay_money':6,
				'reward':{'item087':1,'gold':50000,'item032':10,'item037':5,'item021':5,'equip':['equip055']},	
				'picture':['actPay1_1.png','actPay2_4.png', 'actPay3_4.png', 'icon_03.png'],
			},
			{
				'pay_money':30,
				'reward':{'item088':1,'gold':50000,'item032':10,'item037':5,'item021':5,'item402':20},
				'picture':['actPay1_2.png','actPay2_6.png', 'actPay3_6.png'],
			},
			{
				'pay_money':68,
				'reward':{'item087':1,'gold':50000,'item032':10,'item037':5,'item021':5,'item402':40},	
				'picture':['actPay1_1.png','actPay2_6.png', 'actPay3_7.png'],
			},
			{
				'pay_money':128,
				'reward':{'item088':1,'gold':50000,'item032':10,'item037':5,'item021':5,'item402':80},	
				'picture':['actPay1_2.png','actPay2_6.png', 'actPay3_6.png'],
			},
#			{
#				'delay_day':7,        #过7天以上才能见到入口
#				'pay_money':100,
#				'reward':{'item001':1,'item002':1,'item003':1,'item004':1,'item005':1,'item006':1},	
#				'picture':['act01','single_act01'],
#			},
#			{
#				'pay_money':100,
#				'reward':{'item001':1,'item002':1,'item003':1,'item004':1,'item005':1,'item006':1},	
#				'picture':['act01','single_act01'],
#			},

	],

######################加官进爵####################
	'act_officeup':{
		'pay_money':88,			#需要玩家累积充值的金额
		'officelv_reward':{
			1:{'coin':100},     #office等级,奖励
			2:{'coin':600}, 
			3:{'coin':1500}, 
			4:{'coin':2800}, 
			5:{'coin':3888}, 
		},
	},



	
##########################精彩活动###############
	#单笔充值，活动时间内记录单笔充值的rmb
	#累计充值，活动时间内累计充值获得的黄金

	'pay_ploy':{
		'msg_name': '502001',
		'msg_info': '502002',
		#'XX':{ o开头代表单笔充值，a开头代表累计充值，字母后面的数字向下延续，尽量不重复用10数内的id
			#'date':[[2019,3,27],[2019,10,1]],		#以年月日作为起始/结束日期,（eg：9月1号5点开始，10月1号5点结束）
			#'days':[a,b],		#a&b=开服第几天，a=开启天数，b=结束天数 （eg：偏移值5点，1号6点开服, 'days':[1, 2]   活动入口会在3号5点关闭）
			#'type':'once',		#活动类型：once单笔充值，addup累计充值，today每日累充，choice充值自选
			#'reward':{		#奖励库，根据活动类型写法不同
				#1:{'item003':1,'item004':2,'item005':10,'item020':25,'item073':10},		#单笔充值写法；金额：奖励
				#1000:{'item003':1,'item004':2,'item005':10,'item020':25,'item073':10},	#累计充值写法；金额：奖励
			#},
			#'character':'hero703',	#面板中的立绘
			#'title':'502005',		#标题，入口以及面板内
			#'tips':'502006',		#面板内的提示文本
		#},


     'a1':{         #伏羲
                        
                         'days':[1,9],'type':'addup',
                         'show':[ 'equip108','equip109','equip110','equip111','equip112'],
                         'name':'502081',
                         'info':'502082',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'equip':['equip108'],'item490':50,'item406':200,'item036':20,'item021':20,},
                                      200:{'equip':['equip109'],'item490':50,'item409':400,'item036':30,'item021':30,},
                                      500:{'equip':['equip110'],'item490':100,'item407':600,'item036':40,'item021':40,},
                                      1000:{'equip':['equip111'],'item490':100,'item408':800,'item036':50,'item021':50,},
                                      2000:{'equip':['equip112'],'item490':200,'item410':1000,'item036':60,'item021':60,},
                                      4000:{'item013':100,'item490':300,'item408':1200,'item036':70,'item021':70,},
                                      6000:{'item013':200,'item490':400,'item407':1400,'item036':80,'item021':80,},
                                      8000:{'item013':300,'item490':500,'item409':1600,'item036':100,'item021':100,},
                                      11000:{'item014':100,'item490':600,'item406':1800,'item036':120,'item021':120,},
                                      15000:{'item014':200,'item490':800,'item084':5,'item036':160,'item015':100,},
                                      20000:{'item014':300,'item490':1100,'item084':10,'item036':200,'item016':100,},
                                      25000:{'item013':320,'item014':160,'item084':15,'item015':120,'item016':100,},

                                     },
                            'character':'skin716_0_1',
                            'reward_show':{     '25000':15000, '30000':20000,},
                            'title':'502007',
                            'tips':'502008',
                            },


















     'a2':{         #蓄势+切割
                        
                         'days':[10,16],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item293':20,'item294':20,'item187':5,'item205':20,'item212':20,},
                                      200:{'item293':40,'item294':40,'item187':10,'item205':40,'item212':40,},
                                      500:{'item293':70,'item294':70,'item187':15,'item205':70,'item212':70,},
                                      1000:{'item293':120,'item294':120,'item187':20,'item205':120,'item212':120,},
                                      2000:{'item293':200,'item294':200,'item187':40,'item205':200,'item212':200,},
                                      3000:{'item293':320,'item294':320,'item035':1,'item205':320,'item212':320,},
                                      4500:{'item293':480,'item294':480,'item035':2,'item205':480,'item212':480,},
                                      6000:{'item293':680,'item294':680,'item035':3,'item205':680,'item212':680,},
                                      8000:{'item293':920,'item294':920,'item035':4,'item205':920,'item212':920,},
                                      10000:{'item293':1160,'item294':1160,'item036':100,'item205':1160,'item212':1160,},
                                      13000:{'item293':1400,'item294':1400,'item036':120,'item205':1400,'item212':1400,},
                                      16000:{'item206':1640,'item211':1640,'item036':140,'item292':1640,'item291':1640,},

                                     },
                            'character':'hero724',
                            'reward_show':{},
                            'title':'502007',
                            'tips':'502008',
                            },
















     'a3':{         #宝物强化
                        
                         'days':[17,23],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item278':20,'item279':20,'item187':5,'item274':20,'item280':20,},
                                      200:{'item278':40,'item279':40,'item187':10,'item274':40,'item280':40,},
                                      500:{'item278':70,'item279':70,'item187':15,'item274':60,'item280':60,},
                                      1000:{'item278':120,'item279':120,'item187':20,'item274':100,'item280':100,},
                                      2000:{'item278':200,'item279':200,'item187':40,'item274':200,'item280':200,},
                                      3000:{'item278':320,'item279':320,'item035':1,'item274':320,'item280':320,},
                                      4500:{'item278':480,'item279':480,'item035':2,'item274':480,'item280':480,},
                                      6000:{'item278':680,'item279':680,'item035':3,'item274':640,'item280':640,},
                                      8000:{'item278':920,'item279':920,'item035':4,'item274':820,'item280':820,},
                                      10000:{'item278':1160,'item279':1160,'item036':100,'item274':1020,'item280':1020,},
                                      13000:{'item278':1400,'item279':1400,'item036':120,'item274':1320,'item280':1320,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },


















     'a4':{         #左慈
                        
                         'days':[24,30],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item295':20,'item296':20,'item187':5,'item218':20,'item224':20,},
                                      200:{'item295':40,'item296':40,'item187':10,'item218':40,'item224':40,},
                                      500:{'item295':70,'item296':70,'item187':15,'item218':70,'item224':70,},
                                      1000:{'item295':120,'item296':120,'item187':20,'item218':120,'item224':120,},
                                      2000:{'item295':200,'item296':200,'item187':40,'item218':200,'item224':200,},
                                      3000:{'item295':320,'item296':320,'item035':1,'item218':320,'item224':320,},
                                      4500:{'item295':480,'item296':480,'item035':2,'item218':480,'item224':480,},
                                      6000:{'item295':680,'item296':680,'item035':3,'item218':680,'item224':680,},
                                      8000:{'item295':920,'item296':920,'item035':4,'item218':920,'item224':920,},
                                      10000:{'item295':1160,'item296':1160,'item036':100,'item218':1160,'item224':1160,},
                                      13000:{'item295':1400,'item296':1400,'item036':120,'item218':1400,'item224':1400,},
                                      16000:{'item291':1640,'item292':1640,'item036':140,'item206':1640,'item211':1640,},
                                     },
                            'character':'hero784',
                            'title':'502007',
                            'tips':'502008',
                            },




















     'a5':{         #遁甲
                        
                         'days':[31,37],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item013':8,'item014':4,'item015':3,'item016':1,'item036':5,},
                                      200:{'item013':16,'item014':8,'item015':6,'item016':3,'item036':10,},
                                      500:{'item013':24,'item014':12,'item015':9,'item016':5,'item036':20,},
                                      1000:{'item013':32,'item014':16,'item015':12,'item016':7,'item036':30,},
                                      2000:{'item013':48,'item014':24,'item015':18,'item016':10,'item036':40,},
                                      3000:{'item013':64,'item014':32,'item015':24,'item016':15,'item036':50,},
                                      4500:{'item013':96,'item014':48,'item015':36,'item016':20,'item036':60,},
                                      6000:{'item013':128,'item014':64,'item015':48,'item016':25,'item036':70,},
                                      8000:{'item013':160,'item014':80,'item015':60,'item016':30,'item036':90,},
                                      10000:{'item013':200,'item014':100,'item015':76,'item016':36,'item036':100,},
                                      13000:{'item013':240,'item014':120,'item015':92,'item016':42,'item036':120,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },


















     'a6':{         #英雄技能
                        
                         'days':[38,44],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item275':20,'item244':20,'item187':5,'item246':20,'item240':20,},
                                      200:{'item275':40,'item244':40,'item187':10,'item246':40,'item240':40,},
                                      500:{'item275':70,'item244':70,'item187':15,'item246':70,'item240':70,},
                                      1000:{'item275':120,'item244':120,'item187':20,'item246':120,'item240':120,},
                                      2000:{'item275':200,'item244':200,'item187':40,'item246':200,'item240':200,},
                                      3000:{'item275':320,'item244':320,'item035':1,'item246':320,'item240':320,},
                                      4500:{'item275':480,'item244':480,'item035':2,'item246':480,'item240':480,},
                                      6000:{'item275':680,'item244':680,'item035':3,'item246':680,'item240':680,},
                                      8000:{'item275':920,'item244':920,'item035':4,'item246':920,'item240':920,},
                                      10000:{'item275':1160,'item244':1160,'item036':100,'item246':1160,'item240':1160,},
                                      13000:{'item275':1400,'item244':1400,'item036':120,'item246':1400,'item240':1400,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },


















     'a7':{         #宝物强化
                        
                         'days':[45,51],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item293':20,'item294':20,'item187':5,'item205':20,'item212':20,},
                                      200:{'item293':40,'item294':40,'item187':10,'item205':40,'item212':40,},
                                      500:{'item293':70,'item294':70,'item187':15,'item205':70,'item212':70,},
                                      1000:{'item293':120,'item294':120,'item187':20,'item205':120,'item212':120,},
                                      2000:{'item293':200,'item294':200,'item187':40,'item205':200,'item212':200,},
                                      3000:{'item293':320,'item294':320,'item035':1,'item205':320,'item212':320,},
                                      4500:{'item293':480,'item294':480,'item035':2,'item205':480,'item212':480,},
                                      6000:{'item293':680,'item294':680,'item035':3,'item205':680,'item212':680,},
                                      8000:{'item293':920,'item294':920,'item035':4,'item205':920,'item212':920,},
                                      10000:{'item293':1160,'item294':1160,'item036':100,'item205':1160,'item212':1160,},
                                      13000:{'item293':1400,'item294':1400,'item036':120,'item205':1400,'item212':1400,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },
















     'a8':{         #大师兵种技能1
                        
                         'days':[52,58],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item278':20,'item279':20,'item187':5,'item274':20,'item280':20,},
                                      200:{'item278':40,'item279':40,'item187':10,'item274':40,'item280':40,},
                                      500:{'item278':70,'item279':70,'item187':15,'item274':60,'item280':60,},
                                      1000:{'item278':120,'item279':120,'item187':20,'item274':100,'item280':100,},
                                      2000:{'item278':200,'item279':200,'item187':40,'item274':200,'item280':200,},
                                      3000:{'item278':320,'item279':320,'item035':1,'item274':320,'item280':320,},
                                      4500:{'item278':480,'item279':480,'item035':2,'item274':480,'item280':480,},
                                      6000:{'item278':680,'item279':680,'item035':3,'item274':640,'item280':640,},
                                      8000:{'item278':920,'item279':920,'item035':4,'item274':820,'item280':820,},
                                      10000:{'item278':1160,'item279':1160,'item036':100,'item274':1020,'item280':1020,},
                                      13000:{'item278':1400,'item279':1400,'item036':120,'item274':1320,'item280':1320,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },

















     'a9':{         #新国士技能2
                        
                         'days':[59,65],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item295':20,'item296':20,'item187':5,'item218':20,'item224':20,},
                                      200:{'item295':40,'item296':40,'item187':10,'item218':40,'item224':40,},
                                      500:{'item295':70,'item296':70,'item187':15,'item218':70,'item224':70,},
                                      1000:{'item295':120,'item296':120,'item187':20,'item218':120,'item224':120,},
                                      2000:{'item295':200,'item296':200,'item187':40,'item218':200,'item224':200,},
                                      3000:{'item295':320,'item296':320,'item035':1,'item218':320,'item224':320,},
                                      4500:{'item295':480,'item296':480,'item035':2,'item218':480,'item224':480,},
                                      6000:{'item295':680,'item296':680,'item035':3,'item218':680,'item224':680,},
                                      8000:{'item295':920,'item296':920,'item035':4,'item218':920,'item224':920,},
                                      10000:{'item295':1160,'item296':1160,'item036':100,'item218':1160,'item224':1160,},
                                      13000:{'item295':1400,'item296':1400,'item036':120,'item218':1400,'item224':1400,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },




     'a10':{         #遁甲
                        
                         'days':[66,72],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item013':8,'item014':4,'item015':3,'item016':1,'item036':5,},
                                      200:{'item013':16,'item014':8,'item015':6,'item016':3,'item036':10,},
                                      500:{'item013':24,'item014':12,'item015':9,'item016':5,'item036':20,},
                                      1000:{'item013':32,'item014':16,'item015':12,'item016':7,'item036':30,},
                                      2000:{'item013':48,'item014':24,'item015':18,'item016':10,'item036':40,},
                                      3000:{'item013':64,'item014':32,'item015':24,'item016':15,'item036':50,},
                                      4500:{'item013':96,'item014':48,'item015':36,'item016':20,'item036':60,},
                                      6000:{'item013':128,'item014':64,'item015':48,'item016':25,'item036':70,},
                                      8000:{'item013':160,'item014':80,'item015':60,'item016':30,'item036':90,},
                                      10000:{'item013':200,'item014':100,'item015':76,'item016':36,'item036':100,},
                                      13000:{'item013':240,'item014':120,'item015':92,'item016':42,'item036':120,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },


















     'a11':{         #英雄技能
                        
                         'days':[73,79],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item275':20,'item244':20,'item187':5,'item246':20,'item240':20,},
                                      200:{'item275':40,'item244':40,'item187':10,'item246':40,'item240':40,},
                                      500:{'item275':70,'item244':70,'item187':15,'item246':70,'item240':70,},
                                      1000:{'item275':120,'item244':120,'item187':20,'item246':120,'item240':120,},
                                      2000:{'item275':200,'item244':200,'item187':40,'item246':200,'item240':200,},
                                      3000:{'item275':320,'item244':320,'item035':1,'item246':320,'item240':320,},
                                      4500:{'item275':480,'item244':480,'item035':2,'item246':480,'item240':480,},
                                      6000:{'item275':680,'item244':680,'item035':3,'item246':680,'item240':680,},
                                      8000:{'item275':920,'item244':920,'item035':4,'item246':920,'item240':920,},
                                      10000:{'item275':1160,'item244':1160,'item036':100,'item246':1160,'item240':1160,},
                                      13000:{'item275':1400,'item244':1400,'item036':120,'item246':1400,'item240':1400,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },


















     'a12':{         #宝物强化
                        
                         'days':[80,86],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item293':20,'item294':20,'item187':5,'item205':20,'item212':20,},
                                      200:{'item293':40,'item294':40,'item187':10,'item205':40,'item212':40,},
                                      500:{'item293':70,'item294':70,'item187':15,'item205':70,'item212':70,},
                                      1000:{'item293':120,'item294':120,'item187':20,'item205':120,'item212':120,},
                                      2000:{'item293':200,'item294':200,'item187':40,'item205':200,'item212':200,},
                                      3000:{'item293':320,'item294':320,'item035':1,'item205':320,'item212':320,},
                                      4500:{'item293':480,'item294':480,'item035':2,'item205':480,'item212':480,},
                                      6000:{'item293':680,'item294':680,'item035':3,'item205':680,'item212':680,},
                                      8000:{'item293':920,'item294':920,'item035':4,'item205':920,'item212':920,},
                                      10000:{'item293':1160,'item294':1160,'item036':100,'item205':1160,'item212':1160,},
                                      13000:{'item293':1400,'item294':1400,'item036':120,'item205':1400,'item212':1400,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },

















     'a13':{         #大师兵种技能1
                        
                         'days':[87,93],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item278':20,'item279':20,'item187':5,'item274':20,'item280':20,},
                                      200:{'item278':40,'item279':40,'item187':10,'item274':40,'item280':40,},
                                      500:{'item278':70,'item279':70,'item187':15,'item274':60,'item280':60,},
                                      1000:{'item278':120,'item279':120,'item187':20,'item274':100,'item280':100,},
                                      2000:{'item278':200,'item279':200,'item187':40,'item274':200,'item280':200,},
                                      3000:{'item278':320,'item279':320,'item035':1,'item274':320,'item280':320,},
                                      4500:{'item278':480,'item279':480,'item035':2,'item274':480,'item280':480,},
                                      6000:{'item278':680,'item279':680,'item035':3,'item274':640,'item280':640,},
                                      8000:{'item278':920,'item279':920,'item035':4,'item274':820,'item280':820,},
                                      10000:{'item278':1160,'item279':1160,'item036':100,'item274':1020,'item280':1020,},
                                      13000:{'item278':1400,'item279':1400,'item036':120,'item274':1320,'item280':1320,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },

















     'a14':{         #新国士技能2
                        
                         'days':[94,100],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item295':20,'item296':20,'item187':5,'item218':20,'item224':20,},
                                      200:{'item295':40,'item296':40,'item187':10,'item218':40,'item224':40,},
                                      500:{'item295':70,'item296':70,'item187':15,'item218':70,'item224':70,},
                                      1000:{'item295':120,'item296':120,'item187':20,'item218':120,'item224':120,},
                                      2000:{'item295':200,'item296':200,'item187':40,'item218':200,'item224':200,},
                                      3000:{'item295':320,'item296':320,'item035':1,'item218':320,'item224':320,},
                                      4500:{'item295':480,'item296':480,'item035':2,'item218':480,'item224':480,},
                                      6000:{'item295':680,'item296':680,'item035':3,'item218':680,'item224':680,},
                                      8000:{'item295':920,'item296':920,'item035':4,'item218':920,'item224':920,},
                                      10000:{'item295':1160,'item296':1160,'item036':100,'item218':1160,'item224':1160,},
                                      13000:{'item295':1400,'item296':1400,'item036':120,'item218':1400,'item224':1400,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },


















     'a15':{         #遁甲
                        
                         'days':[101,107],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item013':8,'item014':4,'item015':3,'item016':1,'item036':5,},
                                      200:{'item013':16,'item014':8,'item015':6,'item016':3,'item036':10,},
                                      500:{'item013':24,'item014':12,'item015':9,'item016':5,'item036':20,},
                                      1000:{'item013':32,'item014':16,'item015':12,'item016':7,'item036':30,},
                                      2000:{'item013':48,'item014':24,'item015':18,'item016':10,'item036':40,},
                                      3000:{'item013':64,'item014':32,'item015':24,'item016':15,'item036':50,},
                                      4500:{'item013':96,'item014':48,'item015':36,'item016':20,'item036':60,},
                                      6000:{'item013':128,'item014':64,'item015':48,'item016':25,'item036':70,},
                                      8000:{'item013':160,'item014':80,'item015':60,'item016':30,'item036':90,},
                                      10000:{'item013':200,'item014':100,'item015':76,'item016':36,'item036':100,},
                                      13000:{'item013':240,'item014':120,'item015':92,'item016':42,'item036':120,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },


















     'a16':{         #英雄技能
                        
                         'days':[108,114],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item275':20,'item244':20,'item187':5,'item246':20,'item240':20,},
                                      200:{'item275':40,'item244':40,'item187':10,'item246':40,'item240':40,},
                                      500:{'item275':70,'item244':70,'item187':15,'item246':70,'item240':70,},
                                      1000:{'item275':120,'item244':120,'item187':20,'item246':120,'item240':120,},
                                      2000:{'item275':200,'item244':200,'item187':40,'item246':200,'item240':200,},
                                      3000:{'item275':320,'item244':320,'item035':1,'item246':320,'item240':320,},
                                      4500:{'item275':480,'item244':480,'item035':2,'item246':480,'item240':480,},
                                      6000:{'item275':680,'item244':680,'item035':3,'item246':680,'item240':680,},
                                      8000:{'item275':920,'item244':920,'item035':4,'item246':920,'item240':920,},
                                      10000:{'item275':1160,'item244':1160,'item036':100,'item246':1160,'item240':1160,},
                                      13000:{'item275':1400,'item244':1400,'item036':120,'item246':1400,'item240':1400,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },


















     'a17':{         #宝物强化
                        
                         'days':[115,121],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item293':20,'item294':20,'item187':5,'item205':20,'item212':20,},
                                      200:{'item293':40,'item294':40,'item187':10,'item205':40,'item212':40,},
                                      500:{'item293':70,'item294':70,'item187':15,'item205':70,'item212':70,},
                                      1000:{'item293':120,'item294':120,'item187':20,'item205':120,'item212':120,},
                                      2000:{'item293':200,'item294':200,'item187':40,'item205':200,'item212':200,},
                                      3000:{'item293':320,'item294':320,'item035':1,'item205':320,'item212':320,},
                                      4500:{'item293':480,'item294':480,'item035':2,'item205':480,'item212':480,},
                                      6000:{'item293':680,'item294':680,'item035':3,'item205':680,'item212':680,},
                                      8000:{'item293':920,'item294':920,'item035':4,'item205':920,'item212':920,},
                                      10000:{'item293':1160,'item294':1160,'item036':100,'item205':1160,'item212':1160,},
                                      13000:{'item293':1400,'item294':1400,'item036':120,'item205':1400,'item212':1400,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },

















     'a18':{         #大师兵种技能1
                        
                         'days':[122,128],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item278':20,'item279':20,'item187':5,'item274':20,'item280':20,},
                                      200:{'item278':40,'item279':40,'item187':10,'item274':40,'item280':40,},
                                      500:{'item278':70,'item279':70,'item187':15,'item274':60,'item280':60,},
                                      1000:{'item278':120,'item279':120,'item187':20,'item274':100,'item280':100,},
                                      2000:{'item278':200,'item279':200,'item187':40,'item274':200,'item280':200,},
                                      3000:{'item278':320,'item279':320,'item035':1,'item274':320,'item280':320,},
                                      4500:{'item278':480,'item279':480,'item035':2,'item274':480,'item280':480,},
                                      6000:{'item278':680,'item279':680,'item035':3,'item274':640,'item280':640,},
                                      8000:{'item278':920,'item279':920,'item035':4,'item274':820,'item280':820,},
                                      10000:{'item278':1160,'item279':1160,'item036':100,'item274':1020,'item280':1020,},
                                      13000:{'item278':1400,'item279':1400,'item036':120,'item274':1320,'item280':1320,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },

















     'a19':{         #新国士技能2
                        
                         'days':[129,135],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item295':20,'item296':20,'item187':5,'item218':20,'item224':20,},
                                      200:{'item295':40,'item296':40,'item187':10,'item218':40,'item224':40,},
                                      500:{'item295':70,'item296':70,'item187':15,'item218':70,'item224':70,},
                                      1000:{'item295':120,'item296':120,'item187':20,'item218':120,'item224':120,},
                                      2000:{'item295':200,'item296':200,'item187':40,'item218':200,'item224':200,},
                                      3000:{'item295':320,'item296':320,'item035':1,'item218':320,'item224':320,},
                                      4500:{'item295':480,'item296':480,'item035':2,'item218':480,'item224':480,},
                                      6000:{'item295':680,'item296':680,'item035':3,'item218':680,'item224':680,},
                                      8000:{'item295':920,'item296':920,'item035':4,'item218':920,'item224':920,},
                                      10000:{'item295':1160,'item296':1160,'item036':100,'item218':1160,'item224':1160,},
                                      13000:{'item295':1400,'item296':1400,'item036':120,'item218':1400,'item224':1400,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },


















     'a20':{         #遁甲
                        
                         'days':[136,142],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item013':8,'item014':4,'item015':3,'item016':1,'item036':5,},
                                      200:{'item013':16,'item014':8,'item015':6,'item016':3,'item036':10,},
                                      500:{'item013':24,'item014':12,'item015':9,'item016':5,'item036':20,},
                                      1000:{'item013':32,'item014':16,'item015':12,'item016':7,'item036':30,},
                                      2000:{'item013':48,'item014':24,'item015':18,'item016':10,'item036':40,},
                                      3000:{'item013':64,'item014':32,'item015':24,'item016':15,'item036':50,},
                                      4500:{'item013':96,'item014':48,'item015':36,'item016':20,'item036':60,},
                                      6000:{'item013':128,'item014':64,'item015':48,'item016':25,'item036':70,},
                                      8000:{'item013':160,'item014':80,'item015':60,'item016':30,'item036':90,},
                                      10000:{'item013':200,'item014':100,'item015':76,'item016':36,'item036':100,},
                                      13000:{'item013':240,'item014':120,'item015':92,'item016':42,'item036':120,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },


















     'a21':{         #英雄技能
                        
                         'days':[143,149],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item275':20,'item244':20,'item187':5,'item246':20,'item240':20,},
                                      200:{'item275':40,'item244':40,'item187':10,'item246':40,'item240':40,},
                                      500:{'item275':70,'item244':70,'item187':15,'item246':70,'item240':70,},
                                      1000:{'item275':120,'item244':120,'item187':20,'item246':120,'item240':120,},
                                      2000:{'item275':200,'item244':200,'item187':40,'item246':200,'item240':200,},
                                      3000:{'item275':320,'item244':320,'item035':1,'item246':320,'item240':320,},
                                      4500:{'item275':480,'item244':480,'item035':2,'item246':480,'item240':480,},
                                      6000:{'item275':680,'item244':680,'item035':3,'item246':680,'item240':680,},
                                      8000:{'item275':920,'item244':920,'item035':4,'item246':920,'item240':920,},
                                      10000:{'item275':1160,'item244':1160,'item036':100,'item246':1160,'item240':1160,},
                                      13000:{'item275':1400,'item244':1400,'item036':120,'item246':1400,'item240':1400,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },


















     'a22':{         #宝物强化
                        
                         'days':[150,156],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item293':20,'item294':20,'item187':5,'item205':20,'item212':20,},
                                      200:{'item293':40,'item294':40,'item187':10,'item205':40,'item212':40,},
                                      500:{'item293':70,'item294':70,'item187':15,'item205':70,'item212':70,},
                                      1000:{'item293':120,'item294':120,'item187':20,'item205':120,'item212':120,},
                                      2000:{'item293':200,'item294':200,'item187':40,'item205':200,'item212':200,},
                                      3000:{'item293':320,'item294':320,'item035':1,'item205':320,'item212':320,},
                                      4500:{'item293':480,'item294':480,'item035':2,'item205':480,'item212':480,},
                                      6000:{'item293':680,'item294':680,'item035':3,'item205':680,'item212':680,},
                                      8000:{'item293':920,'item294':920,'item035':4,'item205':920,'item212':920,},
                                      10000:{'item293':1160,'item294':1160,'item036':100,'item205':1160,'item212':1160,},
                                      13000:{'item293':1400,'item294':1400,'item036':120,'item205':1400,'item212':1400,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },

















     'a23':{         #大师兵种技能1
                        
                         'days':[157,163],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item278':20,'item279':20,'item187':5,'item274':20,'item280':20,},
                                      200:{'item278':40,'item279':40,'item187':10,'item274':40,'item280':40,},
                                      500:{'item278':70,'item279':70,'item187':15,'item274':60,'item280':60,},
                                      1000:{'item278':120,'item279':120,'item187':20,'item274':100,'item280':100,},
                                      2000:{'item278':200,'item279':200,'item187':40,'item274':200,'item280':200,},
                                      3000:{'item278':320,'item279':320,'item035':1,'item274':320,'item280':320,},
                                      4500:{'item278':480,'item279':480,'item035':2,'item274':480,'item280':480,},
                                      6000:{'item278':680,'item279':680,'item035':3,'item274':640,'item280':640,},
                                      8000:{'item278':920,'item279':920,'item035':4,'item274':820,'item280':820,},
                                      10000:{'item278':1160,'item279':1160,'item036':100,'item274':1020,'item280':1020,},
                                      13000:{'item278':1400,'item279':1400,'item036':120,'item274':1320,'item280':1320,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },

















     'a24':{         #新国士技能2
                        
                         'days':[164,170],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item295':20,'item296':20,'item187':5,'item218':20,'item224':20,},
                                      200:{'item295':40,'item296':40,'item187':10,'item218':40,'item224':40,},
                                      500:{'item295':70,'item296':70,'item187':15,'item218':70,'item224':70,},
                                      1000:{'item295':120,'item296':120,'item187':20,'item218':120,'item224':120,},
                                      2000:{'item295':200,'item296':200,'item187':40,'item218':200,'item224':200,},
                                      3000:{'item295':320,'item296':320,'item035':1,'item218':320,'item224':320,},
                                      4500:{'item295':480,'item296':480,'item035':2,'item218':480,'item224':480,},
                                      6000:{'item295':680,'item296':680,'item035':3,'item218':680,'item224':680,},
                                      8000:{'item295':920,'item296':920,'item035':4,'item218':920,'item224':920,},
                                      10000:{'item295':1160,'item296':1160,'item036':100,'item218':1160,'item224':1160,},
                                      13000:{'item295':1400,'item296':1400,'item036':120,'item218':1400,'item224':1400,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },



     'a25':{         #遁甲
                        
                         'days':[171,177],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item013':8,'item014':4,'item015':3,'item016':1,'item036':5,},
                                      200:{'item013':16,'item014':8,'item015':6,'item016':3,'item036':10,},
                                      500:{'item013':24,'item014':12,'item015':9,'item016':5,'item036':20,},
                                      1000:{'item013':32,'item014':16,'item015':12,'item016':7,'item036':30,},
                                      2000:{'item013':48,'item014':24,'item015':18,'item016':10,'item036':40,},
                                      3000:{'item013':64,'item014':32,'item015':24,'item016':15,'item036':50,},
                                      4500:{'item013':96,'item014':48,'item015':36,'item016':20,'item036':60,},
                                      6000:{'item013':128,'item014':64,'item015':48,'item016':25,'item036':70,},
                                      8000:{'item013':160,'item014':80,'item015':60,'item016':30,'item036':90,},
                                      10000:{'item013':200,'item014':100,'item015':76,'item016':36,'item036':100,},
                                      13000:{'item013':240,'item014':120,'item015':92,'item016':42,'item036':120,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },


















     'a26':{         #英雄技能
                        
                         'days':[178,184],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item275':20,'item244':20,'item187':5,'item246':20,'item240':20,},
                                      200:{'item275':40,'item244':40,'item187':10,'item246':40,'item240':40,},
                                      500:{'item275':70,'item244':70,'item187':15,'item246':70,'item240':70,},
                                      1000:{'item275':120,'item244':120,'item187':20,'item246':120,'item240':120,},
                                      2000:{'item275':200,'item244':200,'item187':40,'item246':200,'item240':200,},
                                      3000:{'item275':320,'item244':320,'item035':1,'item246':320,'item240':320,},
                                      4500:{'item275':480,'item244':480,'item035':2,'item246':480,'item240':480,},
                                      6000:{'item275':680,'item244':680,'item035':3,'item246':680,'item240':680,},
                                      8000:{'item275':920,'item244':920,'item035':4,'item246':920,'item240':920,},
                                      10000:{'item275':1160,'item244':1160,'item036':100,'item246':1160,'item240':1160,},
                                      13000:{'item275':1400,'item244':1400,'item036':120,'item246':1400,'item240':1400,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },


















     'a27':{         #宝物强化
                        
                         'days':[185,191],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item293':20,'item294':20,'item187':5,'item205':20,'item212':20,},
                                      200:{'item293':40,'item294':40,'item187':10,'item205':40,'item212':40,},
                                      500:{'item293':70,'item294':70,'item187':15,'item205':70,'item212':70,},
                                      1000:{'item293':120,'item294':120,'item187':20,'item205':120,'item212':120,},
                                      2000:{'item293':200,'item294':200,'item187':40,'item205':200,'item212':200,},
                                      3000:{'item293':320,'item294':320,'item035':1,'item205':320,'item212':320,},
                                      4500:{'item293':480,'item294':480,'item035':2,'item205':480,'item212':480,},
                                      6000:{'item293':680,'item294':680,'item035':3,'item205':680,'item212':680,},
                                      8000:{'item293':920,'item294':920,'item035':4,'item205':920,'item212':920,},
                                      10000:{'item293':1160,'item294':1160,'item036':100,'item205':1160,'item212':1160,},
                                      13000:{'item293':1400,'item294':1400,'item036':120,'item205':1400,'item212':1400,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },

















     'a28':{         #大师兵种技能1
                        
                         'days':[192,198],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item278':20,'item279':20,'item187':5,'item274':20,'item280':20,},
                                      200:{'item278':40,'item279':40,'item187':10,'item274':40,'item280':40,},
                                      500:{'item278':70,'item279':70,'item187':15,'item274':60,'item280':60,},
                                      1000:{'item278':120,'item279':120,'item187':20,'item274':100,'item280':100,},
                                      2000:{'item278':200,'item279':200,'item187':40,'item274':200,'item280':200,},
                                      3000:{'item278':320,'item279':320,'item035':1,'item274':320,'item280':320,},
                                      4500:{'item278':480,'item279':480,'item035':2,'item274':480,'item280':480,},
                                      6000:{'item278':680,'item279':680,'item035':3,'item274':640,'item280':640,},
                                      8000:{'item278':920,'item279':920,'item035':4,'item274':820,'item280':820,},
                                      10000:{'item278':1160,'item279':1160,'item036':100,'item274':1020,'item280':1020,},
                                      13000:{'item278':1400,'item279':1400,'item036':120,'item274':1320,'item280':1320,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },

















     'a29':{         #新国士技能2
                        
                         'days':[199,205],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item295':20,'item296':20,'item187':5,'item218':20,'item224':20,},
                                      200:{'item295':40,'item296':40,'item187':10,'item218':40,'item224':40,},
                                      500:{'item295':70,'item296':70,'item187':15,'item218':70,'item224':70,},
                                      1000:{'item295':120,'item296':120,'item187':20,'item218':120,'item224':120,},
                                      2000:{'item295':200,'item296':200,'item187':40,'item218':200,'item224':200,},
                                      3000:{'item295':320,'item296':320,'item035':1,'item218':320,'item224':320,},
                                      4500:{'item295':480,'item296':480,'item035':2,'item218':480,'item224':480,},
                                      6000:{'item295':680,'item296':680,'item035':3,'item218':680,'item224':680,},
                                      8000:{'item295':920,'item296':920,'item035':4,'item218':920,'item224':920,},
                                      10000:{'item295':1160,'item296':1160,'item036':100,'item218':1160,'item224':1160,},
                                      13000:{'item295':1400,'item296':1400,'item036':120,'item218':1400,'item224':1400,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },


















     'a30':{         #遁甲
                        
                         'days':[206,212],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item013':8,'item014':4,'item015':3,'item016':1,'item036':5,},
                                      200:{'item013':16,'item014':8,'item015':6,'item016':3,'item036':10,},
                                      500:{'item013':24,'item014':12,'item015':9,'item016':5,'item036':20,},
                                      1000:{'item013':32,'item014':16,'item015':12,'item016':7,'item036':30,},
                                      2000:{'item013':48,'item014':24,'item015':18,'item016':10,'item036':40,},
                                      3000:{'item013':64,'item014':32,'item015':24,'item016':15,'item036':50,},
                                      4500:{'item013':96,'item014':48,'item015':36,'item016':20,'item036':60,},
                                      6000:{'item013':128,'item014':64,'item015':48,'item016':25,'item036':70,},
                                      8000:{'item013':160,'item014':80,'item015':60,'item016':30,'item036':90,},
                                      10000:{'item013':200,'item014':100,'item015':76,'item016':36,'item036':100,},
                                      13000:{'item013':240,'item014':120,'item015':92,'item016':42,'item036':120,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },


















     'a31':{         #英雄技能
                        
                         'days':[213,219],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item275':20,'item244':20,'item187':5,'item246':20,'item240':20,},
                                      200:{'item275':40,'item244':40,'item187':10,'item246':40,'item240':40,},
                                      500:{'item275':70,'item244':70,'item187':15,'item246':70,'item240':70,},
                                      1000:{'item275':120,'item244':120,'item187':20,'item246':120,'item240':120,},
                                      2000:{'item275':200,'item244':200,'item187':40,'item246':200,'item240':200,},
                                      3000:{'item275':320,'item244':320,'item035':1,'item246':320,'item240':320,},
                                      4500:{'item275':480,'item244':480,'item035':2,'item246':480,'item240':480,},
                                      6000:{'item275':680,'item244':680,'item035':3,'item246':680,'item240':680,},
                                      8000:{'item275':920,'item244':920,'item035':4,'item246':920,'item240':920,},
                                      10000:{'item275':1160,'item244':1160,'item036':100,'item246':1160,'item240':1160,},
                                      13000:{'item275':1400,'item244':1400,'item036':120,'item246':1400,'item240':1400,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },


















     'a32':{         #宝物强化
                        
                         'days':[220,226],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item293':20,'item294':20,'item187':5,'item205':20,'item212':20,},
                                      200:{'item293':40,'item294':40,'item187':10,'item205':40,'item212':40,},
                                      500:{'item293':70,'item294':70,'item187':15,'item205':70,'item212':70,},
                                      1000:{'item293':120,'item294':120,'item187':20,'item205':120,'item212':120,},
                                      2000:{'item293':200,'item294':200,'item187':40,'item205':200,'item212':200,},
                                      3000:{'item293':320,'item294':320,'item035':1,'item205':320,'item212':320,},
                                      4500:{'item293':480,'item294':480,'item035':2,'item205':480,'item212':480,},
                                      6000:{'item293':680,'item294':680,'item035':3,'item205':680,'item212':680,},
                                      8000:{'item293':920,'item294':920,'item035':4,'item205':920,'item212':920,},
                                      10000:{'item293':1160,'item294':1160,'item036':100,'item205':1160,'item212':1160,},
                                      13000:{'item293':1400,'item294':1400,'item036':120,'item205':1400,'item212':1400,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },

















     'a33':{         #大师兵种技能1
                        
                         'days':[227,233],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item278':20,'item279':20,'item187':5,'item274':20,'item280':20,},
                                      200:{'item278':40,'item279':40,'item187':10,'item274':40,'item280':40,},
                                      500:{'item278':70,'item279':70,'item187':15,'item274':60,'item280':60,},
                                      1000:{'item278':120,'item279':120,'item187':20,'item274':100,'item280':100,},
                                      2000:{'item278':200,'item279':200,'item187':40,'item274':200,'item280':200,},
                                      3000:{'item278':320,'item279':320,'item035':1,'item274':320,'item280':320,},
                                      4500:{'item278':480,'item279':480,'item035':2,'item274':480,'item280':480,},
                                      6000:{'item278':680,'item279':680,'item035':3,'item274':640,'item280':640,},
                                      8000:{'item278':920,'item279':920,'item035':4,'item274':820,'item280':820,},
                                      10000:{'item278':1160,'item279':1160,'item036':100,'item274':1020,'item280':1020,},
                                      13000:{'item278':1400,'item279':1400,'item036':120,'item274':1320,'item280':1320,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },

















     'a34':{         #新国士技能2
                        
                         'days':[234,240],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item295':20,'item296':20,'item187':5,'item218':20,'item224':20,},
                                      200:{'item295':40,'item296':40,'item187':10,'item218':40,'item224':40,},
                                      500:{'item295':70,'item296':70,'item187':15,'item218':70,'item224':70,},
                                      1000:{'item295':120,'item296':120,'item187':20,'item218':120,'item224':120,},
                                      2000:{'item295':200,'item296':200,'item187':40,'item218':200,'item224':200,},
                                      3000:{'item295':320,'item296':320,'item035':1,'item218':320,'item224':320,},
                                      4500:{'item295':480,'item296':480,'item035':2,'item218':480,'item224':480,},
                                      6000:{'item295':680,'item296':680,'item035':3,'item218':680,'item224':680,},
                                      8000:{'item295':920,'item296':920,'item035':4,'item218':920,'item224':920,},
                                      10000:{'item295':1160,'item296':1160,'item036':100,'item218':1160,'item224':1160,},
                                      13000:{'item295':1400,'item296':1400,'item036':120,'item218':1400,'item224':1400,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },



















     'a35':{         #遁甲
                        
                         'days':[241,247],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item013':8,'item014':4,'item015':3,'item016':1,'item036':5,},
                                      200:{'item013':16,'item014':8,'item015':6,'item016':3,'item036':10,},
                                      500:{'item013':24,'item014':12,'item015':9,'item016':5,'item036':20,},
                                      1000:{'item013':32,'item014':16,'item015':12,'item016':7,'item036':30,},
                                      2000:{'item013':48,'item014':24,'item015':18,'item016':10,'item036':40,},
                                      3000:{'item013':64,'item014':32,'item015':24,'item016':15,'item036':50,},
                                      4500:{'item013':96,'item014':48,'item015':36,'item016':20,'item036':60,},
                                      6000:{'item013':128,'item014':64,'item015':48,'item016':25,'item036':70,},
                                      8000:{'item013':160,'item014':80,'item015':60,'item016':30,'item036':90,},
                                      10000:{'item013':200,'item014':100,'item015':76,'item016':36,'item036':100,},
                                      13000:{'item013':240,'item014':120,'item015':92,'item016':42,'item036':120,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },


















     'a36':{         #英雄技能
                        
                         'days':[248,254],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item275':20,'item244':20,'item187':5,'item246':20,'item240':20,},
                                      200:{'item275':40,'item244':40,'item187':10,'item246':40,'item240':40,},
                                      500:{'item275':70,'item244':70,'item187':15,'item246':70,'item240':70,},
                                      1000:{'item275':120,'item244':120,'item187':20,'item246':120,'item240':120,},
                                      2000:{'item275':200,'item244':200,'item187':40,'item246':200,'item240':200,},
                                      3000:{'item275':320,'item244':320,'item035':1,'item246':320,'item240':320,},
                                      4500:{'item275':480,'item244':480,'item035':2,'item246':480,'item240':480,},
                                      6000:{'item275':680,'item244':680,'item035':3,'item246':680,'item240':680,},
                                      8000:{'item275':920,'item244':920,'item035':4,'item246':920,'item240':920,},
                                      10000:{'item275':1160,'item244':1160,'item036':100,'item246':1160,'item240':1160,},
                                      13000:{'item275':1400,'item244':1400,'item036':120,'item246':1400,'item240':1400,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },


















     'a37':{         #宝物强化
                        
                         'days':[255,261],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item293':20,'item294':20,'item187':5,'item205':20,'item212':20,},
                                      200:{'item293':40,'item294':40,'item187':10,'item205':40,'item212':40,},
                                      500:{'item293':70,'item294':70,'item187':15,'item205':70,'item212':70,},
                                      1000:{'item293':120,'item294':120,'item187':20,'item205':120,'item212':120,},
                                      2000:{'item293':200,'item294':200,'item187':40,'item205':200,'item212':200,},
                                      3000:{'item293':320,'item294':320,'item035':1,'item205':320,'item212':320,},
                                      4500:{'item293':480,'item294':480,'item035':2,'item205':480,'item212':480,},
                                      6000:{'item293':680,'item294':680,'item035':3,'item205':680,'item212':680,},
                                      8000:{'item293':920,'item294':920,'item035':4,'item205':920,'item212':920,},
                                      10000:{'item293':1160,'item294':1160,'item036':100,'item205':1160,'item212':1160,},
                                      13000:{'item293':1400,'item294':1400,'item036':120,'item205':1400,'item212':1400,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },

















     'a38':{         #大师兵种技能1
                        
                         'days':[262,268],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item278':20,'item279':20,'item187':5,'item274':20,'item280':20,},
                                      200:{'item278':40,'item279':40,'item187':10,'item274':40,'item280':40,},
                                      500:{'item278':70,'item279':70,'item187':15,'item274':60,'item280':60,},
                                      1000:{'item278':120,'item279':120,'item187':20,'item274':100,'item280':100,},
                                      2000:{'item278':200,'item279':200,'item187':40,'item274':200,'item280':200,},
                                      3000:{'item278':320,'item279':320,'item035':1,'item274':320,'item280':320,},
                                      4500:{'item278':480,'item279':480,'item035':2,'item274':480,'item280':480,},
                                      6000:{'item278':680,'item279':680,'item035':3,'item274':640,'item280':640,},
                                      8000:{'item278':920,'item279':920,'item035':4,'item274':820,'item280':820,},
                                      10000:{'item278':1160,'item279':1160,'item036':100,'item274':1020,'item280':1020,},
                                      13000:{'item278':1400,'item279':1400,'item036':120,'item274':1320,'item280':1320,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },

















     'a39':{         #新国士技能2
                        
                         'days':[269,275],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item295':20,'item296':20,'item187':5,'item218':20,'item224':20,},
                                      200:{'item295':40,'item296':40,'item187':10,'item218':40,'item224':40,},
                                      500:{'item295':70,'item296':70,'item187':15,'item218':70,'item224':70,},
                                      1000:{'item295':120,'item296':120,'item187':20,'item218':120,'item224':120,},
                                      2000:{'item295':200,'item296':200,'item187':40,'item218':200,'item224':200,},
                                      3000:{'item295':320,'item296':320,'item035':1,'item218':320,'item224':320,},
                                      4500:{'item295':480,'item296':480,'item035':2,'item218':480,'item224':480,},
                                      6000:{'item295':680,'item296':680,'item035':3,'item218':680,'item224':680,},
                                      8000:{'item295':920,'item296':920,'item035':4,'item218':920,'item224':920,},
                                      10000:{'item295':1160,'item296':1160,'item036':100,'item218':1160,'item224':1160,},
                                      13000:{'item295':1400,'item296':1400,'item036':120,'item218':1400,'item224':1400,},
                                     },
                            'character':'hero724',
                            'title':'502007',
                            'tips':'502008',
                            },























 




     'o1':{      #步兵技能
                        
                         'days':[1,2],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item206':20,'item293':20,'item205':20,'item203':20,'coin':680,},
                                      128:{'item206':50,'item293':50,'item205':50,'item203':50,'coin':1280,},
                                      328:{'item206':120,'item293':120,'item205':120,'item203':120,'coin':3280,},
                                      648:{'item206':300,'item293':300,'item205':300,'item203':300,'coin':6480,},
                                      998:{'item206':450,'item293':450,'item205':450,'item203':450,'coin':10000,},
                                      1998:{'item206':900,'item293':900,'item205':900,'item203':900,'coin':20000,},
                                     },
                            'character':'hero761',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o2':{      #骑兵技能
                        
                         'days':[3,4],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item294':20,'item211':20,'item212':20,'item290':20,'coin':680,},
                                      128:{'item294':50,'item211':50,'item212':50,'item290':50,'coin':1280,},
                                      328:{'item294':120,'item211':120,'item212':120,'item290':120,'coin':3280,},
                                      648:{'item294':300,'item211':300,'item212':300,'item290':300,'coin':6480,},
                                      998:{'item294':450,'item211':450,'item212':450,'item290':450,'coin':10000,},
                                      1998:{'item294':900,'item211':900,'item212':900,'item290':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },
























     'o3':{      #方士技能
                        
                         'days':[5,6],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item296':20,'item292':20,'item223':20,'item224':20,'coin':680,},
                                      128:{'item296':50,'item292':50,'item223':50,'item224':50,'coin':1280,},
                                      328:{'item296':120,'item292':120,'item223':120,'item224':120,'coin':3280,},
                                      648:{'item296':300,'item292':300,'item223':300,'item224':300,'coin':6480,},
                                      998:{'item296':450,'item292':450,'item223':450,'item224':450,'coin':10000,},
                                      1998:{'item296':900,'item292':900,'item223':900,'item224':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o4':{      #弓兵技能
                        
                         'days':[7,8],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item291':20,'item295':20,'item218':20,'item217':20,'coin':680,},
                                      128:{'item291':50,'item295':50,'item218':50,'item217':50,'coin':1280,},
                                      328:{'item291':120,'item295':120,'item218':120,'item217':120,'coin':3280,},
                                      648:{'item291':300,'item295':300,'item218':300,'item217':300,'coin':6480,},
                                      998:{'item291':450,'item295':450,'item218':450,'item217':450,'coin':10000,},
                                      1998:{'item291':900,'item295':900,'item218':900,'item217':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o5':{      #洞鉴
                        
                         'days':[9,10],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item278':20,'item273':20,'item271':20,'item280':20,'coin':680,},
                                      128:{'item278':50,'item273':50,'item271':50,'item280':50,'coin':1280,},
                                      328:{'item278':120,'item273':120,'item271':120,'item280':120,'coin':3280,},
                                      648:{'item278':300,'item273':300,'item271':300,'item280':300,'coin':6480,},
                                      998:{'item278':450,'item273':450,'item271':450,'item280':450,'coin':10000,},
                                      1998:{'item278':900,'item273':900,'item271':900,'item280':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o6':{      #退避
                        
                         'days':[11,12],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item279':20,'item272':20,'item276':20,'item274':20,'coin':680,},
                                      128:{'item279':50,'item272':50,'item276':50,'item274':50,'coin':1280,},
                                      328:{'item279':120,'item272':120,'item276':120,'item274':120,'coin':3280,},
                                      648:{'item279':300,'item272':300,'item276':300,'item274':300,'coin':6480,},
                                      998:{'item279':450,'item272':450,'item276':450,'item274':450,'coin':10000,},
                                      1998:{'item279':900,'item272':900,'item276':900,'item274':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o7':{      #阵法
                        
                         'days':[13,14],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item190':20,'item192':4,'item189':20,'item191':4,'coin':680,},
                                      128:{'item190':40,'item192':8,'item189':40,'item191':8,'coin':1280,},
                                      328:{'item190':80,'item192':12,'item189':80,'item191':12,'coin':3280,},
                                      648:{'item190':180,'item192':26,'item189':180,'item191':26,'coin':6480,},
                                      998:{'item190':300,'item192':40,'item189':300,'item191':40,'coin':10000,},
                                      1998:{'item190':640,'item192':85,'item189':640,'item191':85,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o8':{      #冲阵攻心
                        
                         'days':[15,16],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item245':20,'item239':20,'item240':20,'item243':20,'coin':680,},
                                      128:{'item245':50,'item239':50,'item240':50,'item243':50,'coin':1280,},
                                      328:{'item245':120,'item239':120,'item240':120,'item243':120,'coin':3280,},
                                      648:{'item245':300,'item239':300,'item240':300,'item243':300,'coin':6480,},
                                      998:{'item245':450,'item239':450,'item240':450,'item243':450,'coin':10000,},
                                      1998:{'item245':900,'item239':900,'item240':900,'item243':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o9':{      #死斗龙怒
                        
                         'days':[17,18],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item246':20,'item244':20,'item241':20,'item242':20,'coin':680,},
                                      128:{'item246':50,'item244':50,'item241':50,'item242':50,'coin':1280,},
                                      328:{'item246':120,'item244':120,'item241':120,'item242':120,'coin':3280,},
                                      648:{'item246':300,'item244':300,'item241':300,'item242':300,'coin':6480,},
                                      998:{'item246':450,'item244':450,'item241':450,'item242':450,'coin':10000,},
                                      1998:{'item246':900,'item244':900,'item241':900,'item242':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o10':{      #急智遁甲
                        
                         'days':[19,20],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item277':20,'item275':20,'item276':20,'item271':20,'coin':680,},
                                      128:{'item277':50,'item275':50,'item276':50,'item271':50,'coin':1280,},
                                      328:{'item277':120,'item275':120,'item276':120,'item271':120,'coin':3280,},
                                      648:{'item277':300,'item275':300,'item276':300,'item271':300,'coin':6480,},
                                      998:{'item277':450,'item275':450,'item276':450,'item271':450,'coin':10000,},
                                      1998:{'item277':900,'item275':900,'item276':900,'item271':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },















     'o11':{      #步兵技能
                        
                         'days':[21,22],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item206':20,'item293':20,'item205':20,'item203':20,'coin':680,},
                                      128:{'item206':50,'item293':50,'item205':50,'item203':50,'coin':1280,},
                                      328:{'item206':120,'item293':120,'item205':120,'item203':120,'coin':3280,},
                                      648:{'item206':300,'item293':300,'item205':300,'item203':300,'coin':6480,},
                                      998:{'item206':450,'item293':450,'item205':450,'item203':450,'coin':10000,},
                                      1998:{'item206':900,'item293':900,'item205':900,'item203':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o12':{      #骑兵技能
                        
                         'days':[23,24],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item294':20,'item211':20,'item212':20,'item290':20,'coin':680,},
                                      128:{'item294':50,'item211':50,'item212':50,'item290':50,'coin':1280,},
                                      328:{'item294':120,'item211':120,'item212':120,'item290':120,'coin':3280,},
                                      648:{'item294':300,'item211':300,'item212':300,'item290':300,'coin':6480,},
                                      998:{'item294':450,'item211':450,'item212':450,'item290':450,'coin':10000,},
                                      1998:{'item294':900,'item211':900,'item212':900,'item290':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o13':{      #方士技能
                        
                         'days':[25,26],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item296':20,'item292':20,'item223':20,'item224':20,'coin':680,},
                                      128:{'item296':50,'item292':50,'item223':50,'item224':50,'coin':1280,},
                                      328:{'item296':120,'item292':120,'item223':120,'item224':120,'coin':3280,},
                                      648:{'item296':300,'item292':300,'item223':300,'item224':300,'coin':6480,},
                                      998:{'item296':450,'item292':450,'item223':450,'item224':450,'coin':10000,},
                                      1998:{'item296':900,'item292':900,'item223':900,'item224':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o14':{      #弓兵技能
                        
                         'days':[27,28],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item291':20,'item295':20,'item218':20,'item217':20,'coin':680,},
                                      128:{'item291':50,'item295':50,'item218':50,'item217':50,'coin':1280,},
                                      328:{'item291':120,'item295':120,'item218':120,'item217':120,'coin':3280,},
                                      648:{'item291':300,'item295':300,'item218':300,'item217':300,'coin':6480,},
                                      998:{'item291':450,'item295':450,'item218':450,'item217':450,'coin':10000,},
                                      1998:{'item291':900,'item295':900,'item218':900,'item217':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o15':{      #洞鉴
                        
                         'days':[29,30],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item278':20,'item273':20,'item271':20,'item280':20,'coin':680,},
                                      128:{'item278':50,'item273':50,'item271':50,'item280':50,'coin':1280,},
                                      328:{'item278':120,'item273':120,'item271':120,'item280':120,'coin':3280,},
                                      648:{'item278':300,'item273':300,'item271':300,'item280':300,'coin':6480,},
                                      998:{'item278':450,'item273':450,'item271':450,'item280':450,'coin':10000,},
                                      1998:{'item278':900,'item273':900,'item271':900,'item280':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o16':{      #退避
                        
                         'days':[31,32],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item279':20,'item272':20,'item276':20,'item274':20,'coin':680,},
                                      128:{'item279':50,'item272':50,'item276':50,'item274':50,'coin':1280,},
                                      328:{'item279':120,'item272':120,'item276':120,'item274':120,'coin':3280,},
                                      648:{'item279':300,'item272':300,'item276':300,'item274':300,'coin':6480,},
                                      998:{'item279':450,'item272':450,'item276':450,'item274':450,'coin':10000,},
                                      1998:{'item279':900,'item272':900,'item276':900,'item274':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o17':{      #阵法
                        
                         'days':[33,34],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item190':20,'item192':4,'item189':20,'item191':4,'coin':680,},
                                      128:{'item190':40,'item192':8,'item189':40,'item191':8,'coin':1280,},
                                      328:{'item190':80,'item192':12,'item189':80,'item191':12,'coin':3280,},
                                      648:{'item190':180,'item192':26,'item189':180,'item191':26,'coin':6480,},
                                      998:{'item190':300,'item192':40,'item189':300,'item191':40,'coin':10000,},
                                      1998:{'item190':640,'item192':85,'item189':640,'item191':85,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o18':{      #冲阵攻心
                        
                         'days':[35,36],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item245':20,'item239':20,'item240':20,'item243':20,'coin':680,},
                                      128:{'item245':50,'item239':50,'item240':50,'item243':50,'coin':1280,},
                                      328:{'item245':120,'item239':120,'item240':120,'item243':120,'coin':3280,},
                                      648:{'item245':300,'item239':300,'item240':300,'item243':300,'coin':6480,},
                                      998:{'item245':450,'item239':450,'item240':450,'item243':450,'coin':10000,},
                                      1998:{'item245':900,'item239':900,'item240':900,'item243':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o19':{      #死斗龙怒
                        
                         'days':[37,38],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item246':20,'item244':20,'item241':20,'item242':20,'coin':680,},
                                      128:{'item246':50,'item244':50,'item241':50,'item242':50,'coin':1280,},
                                      328:{'item246':120,'item244':120,'item241':120,'item242':120,'coin':3280,},
                                      648:{'item246':300,'item244':300,'item241':300,'item242':300,'coin':6480,},
                                      998:{'item246':450,'item244':450,'item241':450,'item242':450,'coin':10000,},
                                      1998:{'item246':900,'item244':900,'item241':900,'item242':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o20':{      #急智遁甲
                        
                         'days':[39,40],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item277':20,'item275':20,'item276':20,'item271':20,'coin':680,},
                                      128:{'item277':50,'item275':50,'item276':50,'item271':50,'coin':1280,},
                                      328:{'item277':120,'item275':120,'item276':120,'item271':120,'coin':3280,},
                                      648:{'item277':300,'item275':300,'item276':300,'item271':300,'coin':6480,},
                                      998:{'item277':450,'item275':450,'item276':450,'item271':450,'coin':10000,},
                                      1998:{'item277':900,'item275':900,'item276':900,'item271':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o21':{      #步兵技能
                        
                         'days':[41,42],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item206':20,'item293':20,'item205':20,'item203':20,'coin':680,},
                                      128:{'item206':50,'item293':50,'item205':50,'item203':50,'coin':1280,},
                                      328:{'item206':120,'item293':120,'item205':120,'item203':120,'coin':3280,},
                                      648:{'item206':300,'item293':300,'item205':300,'item203':300,'coin':6480,},
                                      998:{'item206':450,'item293':450,'item205':450,'item203':450,'coin':10000,},
                                      1998:{'item206':900,'item293':900,'item205':900,'item203':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o22':{      #骑兵技能
                        
                         'days':[43,44],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item294':20,'item211':20,'item212':20,'item290':20,'coin':680,},
                                      128:{'item294':50,'item211':50,'item212':50,'item290':50,'coin':1280,},
                                      328:{'item294':120,'item211':120,'item212':120,'item290':120,'coin':3280,},
                                      648:{'item294':300,'item211':300,'item212':300,'item290':300,'coin':6480,},
                                      998:{'item294':450,'item211':450,'item212':450,'item290':450,'coin':10000,},
                                      1998:{'item294':900,'item211':900,'item212':900,'item290':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o23':{      #方士技能
                        
                         'days':[45,46],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item296':20,'item292':20,'item223':20,'item224':20,'coin':680,},
                                      128:{'item296':50,'item292':50,'item223':50,'item224':50,'coin':1280,},
                                      328:{'item296':120,'item292':120,'item223':120,'item224':120,'coin':3280,},
                                      648:{'item296':300,'item292':300,'item223':300,'item224':300,'coin':6480,},
                                      998:{'item296':450,'item292':450,'item223':450,'item224':450,'coin':10000,},
                                      1998:{'item296':900,'item292':900,'item223':900,'item224':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o24':{      #弓兵技能
                        
                         'days':[47,48],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item291':20,'item295':20,'item218':20,'item217':20,'coin':680,},
                                      128:{'item291':50,'item295':50,'item218':50,'item217':50,'coin':1280,},
                                      328:{'item291':120,'item295':120,'item218':120,'item217':120,'coin':3280,},
                                      648:{'item291':300,'item295':300,'item218':300,'item217':300,'coin':6480,},
                                      998:{'item291':450,'item295':450,'item218':450,'item217':450,'coin':10000,},
                                      1998:{'item291':900,'item295':900,'item218':900,'item217':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o25':{      #洞鉴
                        
                         'days':[49,50],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item278':20,'item273':20,'item271':20,'item280':20,'coin':680,},
                                      128:{'item278':50,'item273':50,'item271':50,'item280':50,'coin':1280,},
                                      328:{'item278':120,'item273':120,'item271':120,'item280':120,'coin':3280,},
                                      648:{'item278':300,'item273':300,'item271':300,'item280':300,'coin':6480,},
                                      998:{'item278':450,'item273':450,'item271':450,'item280':450,'coin':10000,},
                                      1998:{'item278':900,'item273':900,'item271':900,'item280':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o26':{      #退避+
                        
                         'days':[51,52],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item279':20,'item272':20,'item276':20,'item274':20,'coin':680,},
                                      128:{'item279':50,'item272':50,'item276':50,'item274':50,'coin':1280,},
                                      328:{'item279':120,'item272':120,'item276':120,'item274':120,'coin':3280,},
                                      648:{'item279':300,'item272':300,'item276':300,'item274':300,'coin':6480,},
                                      998:{'item279':450,'item272':450,'item276':450,'item274':450,'coin':10000,},
                                      1998:{'item279':900,'item272':900,'item276':900,'item274':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o27':{      #阵法
                        
                         'days':[53,54],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item190':20,'item192':4,'item189':20,'item191':4,'coin':680,},
                                      128:{'item190':40,'item192':8,'item189':40,'item191':8,'coin':1280,},
                                      328:{'item190':80,'item192':12,'item189':80,'item191':12,'coin':3280,},
                                      648:{'item190':180,'item192':26,'item189':180,'item191':26,'coin':6480,},
                                      998:{'item190':300,'item192':40,'item189':300,'item191':40,'coin':10000,},
                                      1998:{'item190':640,'item192':85,'item189':640,'item191':85,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o28':{      #冲阵攻心
                        
                         'days':[55,56],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item245':20,'item239':20,'item240':20,'item243':20,'coin':680,},
                                      128:{'item245':50,'item239':50,'item240':50,'item243':50,'coin':1280,},
                                      328:{'item245':120,'item239':120,'item240':120,'item243':120,'coin':3280,},
                                      648:{'item245':300,'item239':300,'item240':300,'item243':300,'coin':6480,},
                                      998:{'item245':450,'item239':450,'item240':450,'item243':450,'coin':10000,},
                                      1998:{'item245':900,'item239':900,'item240':900,'item243':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o29':{      #死斗龙怒
                        
                         'days':[57,58],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item246':20,'item244':20,'item241':20,'item242':20,'coin':680,},
                                      128:{'item246':50,'item244':50,'item241':50,'item242':50,'coin':1280,},
                                      328:{'item246':120,'item244':120,'item241':120,'item242':120,'coin':3280,},
                                      648:{'item246':300,'item244':300,'item241':300,'item242':300,'coin':6480,},
                                      998:{'item246':450,'item244':450,'item241':450,'item242':450,'coin':10000,},
                                      1998:{'item246':900,'item244':900,'item241':900,'item242':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o30':{      #急智遁甲
                        
                         'days':[59,60],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item277':20,'item275':20,'item276':20,'item271':20,'coin':680,},
                                      128:{'item277':50,'item275':50,'item276':50,'item271':50,'coin':1280,},
                                      328:{'item277':120,'item275':120,'item276':120,'item271':120,'coin':3280,},
                                      648:{'item277':300,'item275':300,'item276':300,'item271':300,'coin':6480,},
                                      998:{'item277':450,'item275':450,'item276':450,'item271':450,'coin':10000,},
                                      1998:{'item277':900,'item275':900,'item276':900,'item271':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o31':{      #步兵技能
                        
                         'days':[61,62],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item206':20,'item293':20,'item205':20,'item203':20,'coin':680,},
                                      128:{'item206':50,'item293':50,'item205':50,'item203':50,'coin':1280,},
                                      328:{'item206':120,'item293':120,'item205':120,'item203':120,'coin':3280,},
                                      648:{'item206':300,'item293':300,'item205':300,'item203':300,'coin':6480,},
                                      998:{'item206':450,'item293':450,'item205':450,'item203':450,'coin':10000,},
                                      1998:{'item206':900,'item293':900,'item205':900,'item203':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o32':{      #骑兵技能
                        
                         'days':[63,64],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item294':20,'item211':20,'item212':20,'item290':20,'coin':680,},
                                      128:{'item294':50,'item211':50,'item212':50,'item290':50,'coin':1280,},
                                      328:{'item294':120,'item211':120,'item212':120,'item290':120,'coin':3280,},
                                      648:{'item294':300,'item211':300,'item212':300,'item290':300,'coin':6480,},
                                      998:{'item294':450,'item211':450,'item212':450,'item290':450,'coin':10000,},
                                      1998:{'item294':900,'item211':900,'item212':900,'item290':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o33':{      #方士技能
                        
                         'days':[65,66],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item296':20,'item292':20,'item223':20,'item224':20,'coin':680,},
                                      128:{'item296':50,'item292':50,'item223':50,'item224':50,'coin':1280,},
                                      328:{'item296':120,'item292':120,'item223':120,'item224':120,'coin':3280,},
                                      648:{'item296':300,'item292':300,'item223':300,'item224':300,'coin':6480,},
                                      998:{'item296':450,'item292':450,'item223':450,'item224':450,'coin':10000,},
                                      1998:{'item296':900,'item292':900,'item223':900,'item224':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },


    'o34':{      #阵法
                        
                         'days':[67,68],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item190':20,'item192':4,'item189':20,'item191':4,'coin':680,},
                                      128:{'item190':40,'item192':8,'item189':40,'item191':8,'coin':1280,},
                                      328:{'item190':80,'item192':12,'item189':80,'item191':12,'coin':3280,},
                                      648:{'item190':180,'item192':26,'item189':180,'item191':26,'coin':6480,},
                                      998:{'item190':300,'item192':40,'item189':300,'item191':40,'coin':10000,},
                                      1998:{'item190':640,'item192':85,'item189':640,'item191':85,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o35':{      #冲阵攻心
                        
                         'days':[69,70],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item245':20,'item239':20,'item240':20,'item243':20,'coin':680,},
                                      128:{'item245':50,'item239':50,'item240':50,'item243':50,'coin':1280,},
                                      328:{'item245':120,'item239':120,'item240':120,'item243':120,'coin':3280,},
                                      648:{'item245':300,'item239':300,'item240':300,'item243':300,'coin':6480,},
                                      998:{'item245':450,'item239':450,'item240':450,'item243':450,'coin':10000,},
                                      1998:{'item245':900,'item239':900,'item240':900,'item243':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o36':{      #死斗龙怒
                        
                         'days':[71,72],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item246':20,'item244':20,'item241':20,'item242':20,'coin':680,},
                                      128:{'item246':50,'item244':50,'item241':50,'item242':50,'coin':1280,},
                                      328:{'item246':120,'item244':120,'item241':120,'item242':120,'coin':3280,},
                                      648:{'item246':300,'item244':300,'item241':300,'item242':300,'coin':6480,},
                                      998:{'item246':450,'item244':450,'item241':450,'item242':450,'coin':10000,},
                                      1998:{'item246':900,'item244':900,'item241':900,'item242':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o37':{      #急智遁甲
                        
                         'days':[73,74],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item277':20,'item275':20,'item276':20,'item271':20,'coin':680,},
                                      128:{'item277':50,'item275':50,'item276':50,'item271':50,'coin':1280,},
                                      328:{'item277':120,'item275':120,'item276':120,'item271':120,'coin':3280,},
                                      648:{'item277':300,'item275':300,'item276':300,'item271':300,'coin':6480,},
                                      998:{'item277':450,'item275':450,'item276':450,'item271':450,'coin':10000,},
                                      1998:{'item277':900,'item275':900,'item276':900,'item271':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o38':{      #步兵技能
                        
                         'days':[75,76],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item206':20,'item293':20,'item205':20,'item203':20,'coin':680,},
                                      128:{'item206':50,'item293':50,'item205':50,'item203':50,'coin':1280,},
                                      328:{'item206':120,'item293':120,'item205':120,'item203':120,'coin':3280,},
                                      648:{'item206':300,'item293':300,'item205':300,'item203':300,'coin':6480,},
                                      998:{'item206':450,'item293':450,'item205':450,'item203':450,'coin':10000,},
                                      1998:{'item206':900,'item293':900,'item205':900,'item203':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o39':{      #骑兵技能
                        
                         'days':[77,78],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item294':20,'item211':20,'item212':20,'item290':20,'coin':680,},
                                      128:{'item294':50,'item211':50,'item212':50,'item290':50,'coin':1280,},
                                      328:{'item294':120,'item211':120,'item212':120,'item290':120,'coin':3280,},
                                      648:{'item294':300,'item211':300,'item212':300,'item290':300,'coin':6480,},
                                      998:{'item294':450,'item211':450,'item212':450,'item290':450,'coin':10000,},
                                      1998:{'item294':900,'item211':900,'item212':900,'item290':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o40':{      #方士技能
                        
                         'days':[79,80],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item296':20,'item292':20,'item223':20,'item224':20,'coin':680,},
                                      128:{'item296':50,'item292':50,'item223':50,'item224':50,'coin':1280,},
                                      328:{'item296':120,'item292':120,'item223':120,'item224':120,'coin':3280,},
                                      648:{'item296':300,'item292':300,'item223':300,'item224':300,'coin':6480,},
                                      998:{'item296':450,'item292':450,'item223':450,'item224':450,'coin':10000,},
                                      1998:{'item296':900,'item292':900,'item223':900,'item224':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o41':{      #弓兵技能
                        
                         'days':[81,82],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item291':20,'item295':20,'item218':20,'item217':20,'coin':680,},
                                      128:{'item291':50,'item295':50,'item218':50,'item217':50,'coin':1280,},
                                      328:{'item291':120,'item295':120,'item218':120,'item217':120,'coin':3280,},
                                      648:{'item291':300,'item295':300,'item218':300,'item217':300,'coin':6480,},
                                      998:{'item291':450,'item295':450,'item218':450,'item217':450,'coin':10000,},
                                      1998:{'item291':900,'item295':900,'item218':900,'item217':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o42':{      #洞鉴
                        
                         'days':[83,84],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item278':20,'item273':20,'item271':20,'item280':20,'coin':680,},
                                      128:{'item278':50,'item273':50,'item271':50,'item280':50,'coin':1280,},
                                      328:{'item278':120,'item273':120,'item271':120,'item280':120,'coin':3280,},
                                      648:{'item278':300,'item273':300,'item271':300,'item280':300,'coin':6480,},
                                      998:{'item278':450,'item273':450,'item271':450,'item280':450,'coin':10000,},
                                      1998:{'item278':900,'item273':900,'item271':900,'item280':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o43':{      #退避+
                        
                         'days':[85,86],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item279':20,'item272':20,'item276':20,'item274':20,'coin':680,},
                                      128:{'item279':50,'item272':50,'item276':50,'item274':50,'coin':1280,},
                                      328:{'item279':120,'item272':120,'item276':120,'item274':120,'coin':3280,},
                                      648:{'item279':300,'item272':300,'item276':300,'item274':300,'coin':6480,},
                                      998:{'item279':450,'item272':450,'item276':450,'item274':450,'coin':10000,},
                                      1998:{'item279':900,'item272':900,'item276':900,'item274':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o44':{      #阵法
                        
                         'days':[87,88],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item190':20,'item192':4,'item189':20,'item191':4,'coin':680,},
                                      128:{'item190':40,'item192':8,'item189':40,'item191':8,'coin':1280,},
                                      328:{'item190':80,'item192':12,'item189':80,'item191':12,'coin':3280,},
                                      648:{'item190':180,'item192':26,'item189':180,'item191':26,'coin':6480,},
                                      998:{'item190':300,'item192':40,'item189':300,'item191':40,'coin':10000,},
                                      1998:{'item190':640,'item192':85,'item189':640,'item191':85,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o45':{      #冲阵攻心
                        
                         'days':[89,90],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item245':20,'item239':20,'item240':20,'item243':20,'coin':680,},
                                      128:{'item245':50,'item239':50,'item240':50,'item243':50,'coin':1280,},
                                      328:{'item245':120,'item239':120,'item240':120,'item243':120,'coin':3280,},
                                      648:{'item245':300,'item239':300,'item240':300,'item243':300,'coin':6480,},
                                      998:{'item245':450,'item239':450,'item240':450,'item243':450,'coin':10000,},
                                      1998:{'item245':900,'item239':900,'item240':900,'item243':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o46':{      #死斗龙怒
                        
                         'days':[91,92],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item246':20,'item244':20,'item241':20,'item242':20,'coin':680,},
                                      128:{'item246':50,'item244':50,'item241':50,'item242':50,'coin':1280,},
                                      328:{'item246':120,'item244':120,'item241':120,'item242':120,'coin':3280,},
                                      648:{'item246':300,'item244':300,'item241':300,'item242':300,'coin':6480,},
                                      998:{'item246':450,'item244':450,'item241':450,'item242':450,'coin':10000,},
                                      1998:{'item246':900,'item244':900,'item241':900,'item242':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o47':{      #急智遁甲
                        
                         'days':[93,94],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item277':20,'item275':20,'item276':20,'item271':20,'coin':680,},
                                      128:{'item277':50,'item275':50,'item276':50,'item271':50,'coin':1280,},
                                      328:{'item277':120,'item275':120,'item276':120,'item271':120,'coin':3280,},
                                      648:{'item277':300,'item275':300,'item276':300,'item271':300,'coin':6480,},
                                      998:{'item277':450,'item275':450,'item276':450,'item271':450,'coin':10000,},
                                      1998:{'item277':900,'item275':900,'item276':900,'item271':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o48':{      #步兵技能
                        
                         'days':[95,96],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item206':20,'item293':20,'item205':20,'item203':20,'coin':680,},
                                      128:{'item206':50,'item293':50,'item205':50,'item203':50,'coin':1280,},
                                      328:{'item206':120,'item293':120,'item205':120,'item203':120,'coin':3280,},
                                      648:{'item206':300,'item293':300,'item205':300,'item203':300,'coin':6480,},
                                      998:{'item206':450,'item293':450,'item205':450,'item203':450,'coin':10000,},
                                      1998:{'item206':900,'item293':900,'item205':900,'item203':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o49':{      #骑兵技能
                        
                         'days':[97,98],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item294':20,'item211':20,'item212':20,'item290':20,'coin':680,},
                                      128:{'item294':50,'item211':50,'item212':50,'item290':50,'coin':1280,},
                                      328:{'item294':120,'item211':120,'item212':120,'item290':120,'coin':3280,},
                                      648:{'item294':300,'item211':300,'item212':300,'item290':300,'coin':6480,},
                                      998:{'item294':450,'item211':450,'item212':450,'item290':450,'coin':10000,},
                                      1998:{'item294':900,'item211':900,'item212':900,'item290':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o50':{      #方士技能
                        
                         'days':[99,100],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item296':20,'item292':20,'item223':20,'item224':20,'coin':680,},
                                      128:{'item296':50,'item292':50,'item223':50,'item224':50,'coin':1280,},
                                      328:{'item296':120,'item292':120,'item223':120,'item224':120,'coin':3280,},
                                      648:{'item296':300,'item292':300,'item223':300,'item224':300,'coin':6480,},
                                      998:{'item296':450,'item292':450,'item223':450,'item224':450,'coin':10000,},
                                      1998:{'item296':900,'item292':900,'item223':900,'item224':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o51':{      #阵法
                        
                         'days':[101,102],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item190':20,'item192':4,'item189':20,'item191':4,'coin':680,},
                                      128:{'item190':40,'item192':8,'item189':40,'item191':8,'coin':1280,},
                                      328:{'item190':80,'item192':12,'item189':80,'item191':12,'coin':3280,},
                                      648:{'item190':180,'item192':26,'item189':180,'item191':26,'coin':6480,},
                                      998:{'item190':300,'item192':40,'item189':300,'item191':40,'coin':10000,},
                                      1998:{'item190':640,'item192':85,'item189':640,'item191':85,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o52':{      #冲阵攻心
                        
                         'days':[103,104],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item245':20,'item239':20,'item240':20,'item243':20,'coin':680,},
                                      128:{'item245':50,'item239':50,'item240':50,'item243':50,'coin':1280,},
                                      328:{'item245':120,'item239':120,'item240':120,'item243':120,'coin':3280,},
                                      648:{'item245':300,'item239':300,'item240':300,'item243':300,'coin':6480,},
                                      998:{'item245':450,'item239':450,'item240':450,'item243':450,'coin':10000,},
                                      1998:{'item245':900,'item239':900,'item240':900,'item243':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o53':{      #死斗龙怒
                        
                         'days':[105,106],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item246':20,'item244':20,'item241':20,'item242':20,'coin':680,},
                                      128:{'item246':50,'item244':50,'item241':50,'item242':50,'coin':1280,},
                                      328:{'item246':120,'item244':120,'item241':120,'item242':120,'coin':3280,},
                                      648:{'item246':300,'item244':300,'item241':300,'item242':300,'coin':6480,},
                                      998:{'item246':450,'item244':450,'item241':450,'item242':450,'coin':10000,},
                                      1998:{'item246':900,'item244':900,'item241':900,'item242':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o54':{      #急智遁甲
                        
                         'days':[107,108],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item277':20,'item275':20,'item276':20,'item271':20,'coin':680,},
                                      128:{'item277':50,'item275':50,'item276':50,'item271':50,'coin':1280,},
                                      328:{'item277':120,'item275':120,'item276':120,'item271':120,'coin':3280,},
                                      648:{'item277':300,'item275':300,'item276':300,'item271':300,'coin':6480,},
                                      998:{'item277':450,'item275':450,'item276':450,'item271':450,'coin':10000,},
                                      1998:{'item277':900,'item275':900,'item276':900,'item271':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o55':{      #步兵技能
                        
                         'days':[109,110],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item206':20,'item293':20,'item205':20,'item203':20,'coin':680,},
                                      128:{'item206':50,'item293':50,'item205':50,'item203':50,'coin':1280,},
                                      328:{'item206':120,'item293':120,'item205':120,'item203':120,'coin':3280,},
                                      648:{'item206':300,'item293':300,'item205':300,'item203':300,'coin':6480,},
                                      998:{'item206':450,'item293':450,'item205':450,'item203':450,'coin':10000,},
                                      1998:{'item206':900,'item293':900,'item205':900,'item203':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o56':{      #骑兵技能
                        
                         'days':[111,112],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item294':20,'item211':20,'item212':20,'item290':20,'coin':680,},
                                      128:{'item294':50,'item211':50,'item212':50,'item290':50,'coin':1280,},
                                      328:{'item294':120,'item211':120,'item212':120,'item290':120,'coin':3280,},
                                      648:{'item294':300,'item211':300,'item212':300,'item290':300,'coin':6480,},
                                      998:{'item294':450,'item211':450,'item212':450,'item290':450,'coin':10000,},
                                      1998:{'item294':900,'item211':900,'item212':900,'item290':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o57':{      #方士技能
                        
                         'days':[113,114],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item296':20,'item292':20,'item223':20,'item224':20,'coin':680,},
                                      128:{'item296':50,'item292':50,'item223':50,'item224':50,'coin':1280,},
                                      328:{'item296':120,'item292':120,'item223':120,'item224':120,'coin':3280,},
                                      648:{'item296':300,'item292':300,'item223':300,'item224':300,'coin':6480,},
                                      998:{'item296':450,'item292':450,'item223':450,'item224':450,'coin':10000,},
                                      1998:{'item296':900,'item292':900,'item223':900,'item224':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o58':{      #阵法
                        
                         'days':[115,116],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item190':20,'item192':4,'item189':20,'item191':4,'coin':680,},
                                      128:{'item190':40,'item192':8,'item189':40,'item191':8,'coin':1280,},
                                      328:{'item190':80,'item192':12,'item189':80,'item191':12,'coin':3280,},
                                      648:{'item190':180,'item192':26,'item189':180,'item191':26,'coin':6480,},
                                      998:{'item190':300,'item192':40,'item189':300,'item191':40,'coin':10000,},
                                      1998:{'item190':640,'item192':85,'item189':640,'item191':85,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o59':{      #冲阵攻心
                        
                         'days':[117,118],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item245':20,'item239':20,'item240':20,'item243':20,'coin':680,},
                                      128:{'item245':50,'item239':50,'item240':50,'item243':50,'coin':1280,},
                                      328:{'item245':120,'item239':120,'item240':120,'item243':120,'coin':3280,},
                                      648:{'item245':300,'item239':300,'item240':300,'item243':300,'coin':6480,},
                                      998:{'item245':450,'item239':450,'item240':450,'item243':450,'coin':10000,},
                                      1998:{'item245':900,'item239':900,'item240':900,'item243':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o60':{      #死斗龙怒
                        
                         'days':[119,120],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item246':20,'item244':20,'item241':20,'item242':20,'coin':680,},
                                      128:{'item246':50,'item244':50,'item241':50,'item242':50,'coin':1280,},
                                      328:{'item246':120,'item244':120,'item241':120,'item242':120,'coin':3280,},
                                      648:{'item246':300,'item244':300,'item241':300,'item242':300,'coin':6480,},
                                      998:{'item246':450,'item244':450,'item241':450,'item242':450,'coin':10000,},
                                      1998:{'item246':900,'item244':900,'item241':900,'item242':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o61':{      #急智遁甲
                        
                         'days':[121,122],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item277':20,'item275':20,'item276':20,'item271':20,'coin':680,},
                                      128:{'item277':50,'item275':50,'item276':50,'item271':50,'coin':1280,},
                                      328:{'item277':120,'item275':120,'item276':120,'item271':120,'coin':3280,},
                                      648:{'item277':300,'item275':300,'item276':300,'item271':300,'coin':6480,},
                                      998:{'item277':450,'item275':450,'item276':450,'item271':450,'coin':10000,},
                                      1998:{'item277':900,'item275':900,'item276':900,'item271':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o62':{      #步兵技能
                        
                         'days':[123,124],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item206':20,'item293':20,'item205':20,'item203':20,'coin':680,},
                                      128:{'item206':50,'item293':50,'item205':50,'item203':50,'coin':1280,},
                                      328:{'item206':120,'item293':120,'item205':120,'item203':120,'coin':3280,},
                                      648:{'item206':300,'item293':300,'item205':300,'item203':300,'coin':6480,},
                                      998:{'item206':450,'item293':450,'item205':450,'item203':450,'coin':10000,},
                                      1998:{'item206':900,'item293':900,'item205':900,'item203':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o63':{      #骑兵技能
                        
                         'days':[125,126],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item294':20,'item211':20,'item212':20,'item290':20,'coin':680,},
                                      128:{'item294':50,'item211':50,'item212':50,'item290':50,'coin':1280,},
                                      328:{'item294':120,'item211':120,'item212':120,'item290':120,'coin':3280,},
                                      648:{'item294':300,'item211':300,'item212':300,'item290':300,'coin':6480,},
                                      998:{'item294':450,'item211':450,'item212':450,'item290':450,'coin':10000,},
                                      1998:{'item294':900,'item211':900,'item212':900,'item290':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o64':{      #方士技能
                        
                         'days':[127,128],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item296':20,'item292':20,'item223':20,'item224':20,'coin':680,},
                                      128:{'item296':50,'item292':50,'item223':50,'item224':50,'coin':1280,},
                                      328:{'item296':120,'item292':120,'item223':120,'item224':120,'coin':3280,},
                                      648:{'item296':300,'item292':300,'item223':300,'item224':300,'coin':6480,},
                                      998:{'item296':450,'item292':450,'item223':450,'item224':450,'coin':10000,},
                                      1998:{'item296':900,'item292':900,'item223':900,'item224':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o65':{      #阵法
                        
                         'days':[129,130],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item190':20,'item192':4,'item189':20,'item191':4,'coin':680,},
                                      128:{'item190':40,'item192':8,'item189':40,'item191':8,'coin':1280,},
                                      328:{'item190':80,'item192':12,'item189':80,'item191':12,'coin':3280,},
                                      648:{'item190':180,'item192':26,'item189':180,'item191':26,'coin':6480,},
                                      998:{'item190':300,'item192':40,'item189':300,'item191':40,'coin':10000,},
                                      1998:{'item190':640,'item192':85,'item189':640,'item191':85,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o66':{      #冲阵攻心
                        
                         'days':[131,132],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item245':20,'item239':20,'item240':20,'item243':20,'coin':680,},
                                      128:{'item245':50,'item239':50,'item240':50,'item243':50,'coin':1280,},
                                      328:{'item245':120,'item239':120,'item240':120,'item243':120,'coin':3280,},
                                      648:{'item245':300,'item239':300,'item240':300,'item243':300,'coin':6480,},
                                      998:{'item245':450,'item239':450,'item240':450,'item243':450,'coin':10000,},
                                      1998:{'item245':900,'item239':900,'item240':900,'item243':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o67':{      #死斗龙怒
                        
                         'days':[133,134],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item246':20,'item244':20,'item241':20,'item242':20,'coin':680,},
                                      128:{'item246':50,'item244':50,'item241':50,'item242':50,'coin':1280,},
                                      328:{'item246':120,'item244':120,'item241':120,'item242':120,'coin':3280,},
                                      648:{'item246':300,'item244':300,'item241':300,'item242':300,'coin':6480,},
                                      998:{'item246':450,'item244':450,'item241':450,'item242':450,'coin':10000,},
                                      1998:{'item246':900,'item244':900,'item241':900,'item242':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o68':{      #急智遁甲
                        
                         'days':[135,136],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item277':20,'item275':20,'item276':20,'item271':20,'coin':680,},
                                      128:{'item277':50,'item275':50,'item276':50,'item271':50,'coin':1280,},
                                      328:{'item277':120,'item275':120,'item276':120,'item271':120,'coin':3280,},
                                      648:{'item277':300,'item275':300,'item276':300,'item271':300,'coin':6480,},
                                      998:{'item277':450,'item275':450,'item276':450,'item271':450,'coin':10000,},
                                      1998:{'item277':900,'item275':900,'item276':900,'item271':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o69':{      #步兵技能
                        
                         'days':[137,138],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item206':20,'item293':20,'item205':20,'item203':20,'coin':680,},
                                      128:{'item206':50,'item293':50,'item205':50,'item203':50,'coin':1280,},
                                      328:{'item206':120,'item293':120,'item205':120,'item203':120,'coin':3280,},
                                      648:{'item206':300,'item293':300,'item205':300,'item203':300,'coin':6480,},
                                      998:{'item206':450,'item293':450,'item205':450,'item203':450,'coin':10000,},
                                      1998:{'item206':900,'item293':900,'item205':900,'item203':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o70':{      #骑兵技能
                        
                         'days':[139,140],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item294':20,'item211':20,'item212':20,'item290':20,'coin':680,},
                                      128:{'item294':50,'item211':50,'item212':50,'item290':50,'coin':1280,},
                                      328:{'item294':120,'item211':120,'item212':120,'item290':120,'coin':3280,},
                                      648:{'item294':300,'item211':300,'item212':300,'item290':300,'coin':6480,},
                                      998:{'item294':450,'item211':450,'item212':450,'item290':450,'coin':10000,},
                                      1998:{'item294':900,'item211':900,'item212':900,'item290':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o71':{      #方士技能
                        
                         'days':[141,142],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item296':20,'item292':20,'item223':20,'item224':20,'coin':680,},
                                      128:{'item296':50,'item292':50,'item223':50,'item224':50,'coin':1280,},
                                      328:{'item296':120,'item292':120,'item223':120,'item224':120,'coin':3280,},
                                      648:{'item296':300,'item292':300,'item223':300,'item224':300,'coin':6480,},
                                      998:{'item296':450,'item292':450,'item223':450,'item224':450,'coin':10000,},
                                      1998:{'item296':900,'item292':900,'item223':900,'item224':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o72':{      #弓兵技能
                        
                         'days':[143,144],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item291':20,'item295':20,'item218':20,'item217':20,'coin':680,},
                                      128:{'item291':50,'item295':50,'item218':50,'item217':50,'coin':1280,},
                                      328:{'item291':120,'item295':120,'item218':120,'item217':120,'coin':3280,},
                                      648:{'item291':300,'item295':300,'item218':300,'item217':300,'coin':6480,},
                                      998:{'item291':450,'item295':450,'item218':450,'item217':450,'coin':10000,},
                                      1998:{'item291':900,'item295':900,'item218':900,'item217':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o73':{      #骑兵技能
                        
                         'days':[145,146],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item294':20,'item211':20,'item212':20,'item290':20,'coin':680,},
                                      128:{'item294':50,'item211':50,'item212':50,'item290':50,'coin':1280,},
                                      328:{'item294':120,'item211':120,'item212':120,'item290':120,'coin':3280,},
                                      648:{'item294':300,'item211':300,'item212':300,'item290':300,'coin':6480,},
                                      998:{'item294':450,'item211':450,'item212':450,'item290':450,'coin':10000,},
                                      1998:{'item294':900,'item211':900,'item212':900,'item290':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o74':{      #方士技能
                        
                         'days':[147,148],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item296':20,'item292':20,'item223':20,'item224':20,'coin':680,},
                                      128:{'item296':50,'item292':50,'item223':50,'item224':50,'coin':1280,},
                                      328:{'item296':120,'item292':120,'item223':120,'item224':120,'coin':3280,},
                                      648:{'item296':300,'item292':300,'item223':300,'item224':300,'coin':6480,},
                                      998:{'item296':450,'item292':450,'item223':450,'item224':450,'coin':10000,},
                                      1998:{'item296':900,'item292':900,'item223':900,'item224':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o75':{      #阵法
                        
                         'days':[149,150],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item190':20,'item192':4,'item189':20,'item191':4,'coin':680,},
                                      128:{'item190':40,'item192':8,'item189':40,'item191':8,'coin':1280,},
                                      328:{'item190':80,'item192':12,'item189':80,'item191':12,'coin':3280,},
                                      648:{'item190':180,'item192':26,'item189':180,'item191':26,'coin':6480,},
                                      998:{'item190':300,'item192':40,'item189':300,'item191':40,'coin':10000,},
                                      1998:{'item190':640,'item192':85,'item189':640,'item191':85,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o76':{      #冲阵攻心
                        
                         'days':[151,152],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item245':20,'item239':20,'item240':20,'item243':20,'coin':680,},
                                      128:{'item245':50,'item239':50,'item240':50,'item243':50,'coin':1280,},
                                      328:{'item245':120,'item239':120,'item240':120,'item243':120,'coin':3280,},
                                      648:{'item245':300,'item239':300,'item240':300,'item243':300,'coin':6480,},
                                      998:{'item245':450,'item239':450,'item240':450,'item243':450,'coin':10000,},
                                      1998:{'item245':900,'item239':900,'item240':900,'item243':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o77':{      #死斗龙怒
                        
                         'days':[153,154],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item246':20,'item244':20,'item241':20,'item242':20,'coin':680,},
                                      128:{'item246':50,'item244':50,'item241':50,'item242':50,'coin':1280,},
                                      328:{'item246':120,'item244':120,'item241':120,'item242':120,'coin':3280,},
                                      648:{'item246':300,'item244':300,'item241':300,'item242':300,'coin':6480,},
                                      998:{'item246':450,'item244':450,'item241':450,'item242':450,'coin':10000,},
                                      1998:{'item246':900,'item244':900,'item241':900,'item242':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o78':{      #急智遁甲
                        
                         'days':[155,156],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item277':20,'item275':20,'item276':20,'item271':20,'coin':680,},
                                      128:{'item277':50,'item275':50,'item276':50,'item271':50,'coin':1280,},
                                      328:{'item277':120,'item275':120,'item276':120,'item271':120,'coin':3280,},
                                      648:{'item277':300,'item275':300,'item276':300,'item271':300,'coin':6480,},
                                      998:{'item277':450,'item275':450,'item276':450,'item271':450,'coin':10000,},
                                      1998:{'item277':900,'item275':900,'item276':900,'item271':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o79':{      #步兵技能
                        
                         'days':[157,158],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item206':20,'item293':20,'item205':20,'item203':20,'coin':680,},
                                      128:{'item206':50,'item293':50,'item205':50,'item203':50,'coin':1280,},
                                      328:{'item206':120,'item293':120,'item205':120,'item203':120,'coin':3280,},
                                      648:{'item206':300,'item293':300,'item205':300,'item203':300,'coin':6480,},
                                      998:{'item206':450,'item293':450,'item205':450,'item203':450,'coin':10000,},
                                      1998:{'item206':900,'item293':900,'item205':900,'item203':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o80':{      #骑兵技能
                        
                         'days':[159,160],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item294':20,'item211':20,'item212':20,'item290':20,'coin':680,},
                                      128:{'item294':50,'item211':50,'item212':50,'item290':50,'coin':1280,},
                                      328:{'item294':120,'item211':120,'item212':120,'item290':120,'coin':3280,},
                                      648:{'item294':300,'item211':300,'item212':300,'item290':300,'coin':6480,},
                                      998:{'item294':450,'item211':450,'item212':450,'item290':450,'coin':10000,},
                                      1998:{'item294':900,'item211':900,'item212':900,'item290':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o81':{      #方士技能
                        
                         'days':[161,162],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item296':20,'item292':20,'item223':20,'item224':20,'coin':680,},
                                      128:{'item296':50,'item292':50,'item223':50,'item224':50,'coin':1280,},
                                      328:{'item296':120,'item292':120,'item223':120,'item224':120,'coin':3280,},
                                      648:{'item296':300,'item292':300,'item223':300,'item224':300,'coin':6480,},
                                      998:{'item296':450,'item292':450,'item223':450,'item224':450,'coin':10000,},
                                      1998:{'item296':900,'item292':900,'item223':900,'item224':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o82':{      #阵法
                        
                         'days':[163,164],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item190':20,'item192':4,'item189':20,'item191':4,'coin':680,},
                                      128:{'item190':40,'item192':8,'item189':40,'item191':8,'coin':1280,},
                                      328:{'item190':80,'item192':12,'item189':80,'item191':12,'coin':3280,},
                                      648:{'item190':180,'item192':26,'item189':180,'item191':26,'coin':6480,},
                                      998:{'item190':300,'item192':40,'item189':300,'item191':40,'coin':10000,},
                                      1998:{'item190':640,'item192':85,'item189':640,'item191':85,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o83':{      #冲阵攻心
                        
                         'days':[165,166],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item245':20,'item239':20,'item240':20,'item243':20,'coin':680,},
                                      128:{'item245':50,'item239':50,'item240':50,'item243':50,'coin':1280,},
                                      328:{'item245':120,'item239':120,'item240':120,'item243':120,'coin':3280,},
                                      648:{'item245':300,'item239':300,'item240':300,'item243':300,'coin':6480,},
                                      998:{'item245':450,'item239':450,'item240':450,'item243':450,'coin':10000,},
                                      1998:{'item245':900,'item239':900,'item240':900,'item243':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o84':{      #死斗龙怒
                        
                         'days':[167,168],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item246':20,'item244':20,'item241':20,'item242':20,'coin':680,},
                                      128:{'item246':50,'item244':50,'item241':50,'item242':50,'coin':1280,},
                                      328:{'item246':120,'item244':120,'item241':120,'item242':120,'coin':3280,},
                                      648:{'item246':300,'item244':300,'item241':300,'item242':300,'coin':6480,},
                                      998:{'item246':450,'item244':450,'item241':450,'item242':450,'coin':10000,},
                                      1998:{'item246':900,'item244':900,'item241':900,'item242':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o85':{      #急智遁甲
                        
                         'days':[169,170],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item277':20,'item275':20,'item276':20,'item271':20,'coin':680,},
                                      128:{'item277':50,'item275':50,'item276':50,'item271':50,'coin':1280,},
                                      328:{'item277':120,'item275':120,'item276':120,'item271':120,'coin':3280,},
                                      648:{'item277':300,'item275':300,'item276':300,'item271':300,'coin':6480,},
                                      998:{'item277':450,'item275':450,'item276':450,'item271':450,'coin':10000,},
                                      1998:{'item277':900,'item275':900,'item276':900,'item271':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },


     'o86':{      #步兵技能
                        
                         'days':[171,172],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item206':20,'item293':20,'item205':20,'item203':20,'coin':680,},
                                      128:{'item206':50,'item293':50,'item205':50,'item203':50,'coin':1280,},
                                      328:{'item206':120,'item293':120,'item205':120,'item203':120,'coin':3280,},
                                      648:{'item206':300,'item293':300,'item205':300,'item203':300,'coin':6480,},
                                      998:{'item206':450,'item293':450,'item205':450,'item203':450,'coin':10000,},
                                      1998:{'item206':900,'item293':900,'item205':900,'item203':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o87':{      #骑兵技能
                        
                         'days':[173,174],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item294':20,'item211':20,'item212':20,'item290':20,'coin':680,},
                                      128:{'item294':50,'item211':50,'item212':50,'item290':50,'coin':1280,},
                                      328:{'item294':120,'item211':120,'item212':120,'item290':120,'coin':3280,},
                                      648:{'item294':300,'item211':300,'item212':300,'item290':300,'coin':6480,},
                                      998:{'item294':450,'item211':450,'item212':450,'item290':450,'coin':10000,},
                                      1998:{'item294':900,'item211':900,'item212':900,'item290':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o88':{      #方士技能
                        
                         'days':[175,176],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item296':20,'item292':20,'item223':20,'item224':20,'coin':680,},
                                      128:{'item296':50,'item292':50,'item223':50,'item224':50,'coin':1280,},
                                      328:{'item296':120,'item292':120,'item223':120,'item224':120,'coin':3280,},
                                      648:{'item296':300,'item292':300,'item223':300,'item224':300,'coin':6480,},
                                      998:{'item296':450,'item292':450,'item223':450,'item224':450,'coin':10000,},
                                      1998:{'item296':900,'item292':900,'item223':900,'item224':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o89':{      #阵法
                        
                         'days':[177,178],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item190':20,'item192':4,'item189':20,'item191':4,'coin':680,},
                                      128:{'item190':40,'item192':8,'item189':40,'item191':8,'coin':1280,},
                                      328:{'item190':80,'item192':12,'item189':80,'item191':12,'coin':3280,},
                                      648:{'item190':180,'item192':26,'item189':180,'item191':26,'coin':6480,},
                                      998:{'item190':300,'item192':40,'item189':300,'item191':40,'coin':10000,},
                                      1998:{'item190':640,'item192':85,'item189':640,'item191':85,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o90':{      #冲阵攻心
                        
                         'days':[179,180],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item245':20,'item239':20,'item240':20,'item243':20,'coin':680,},
                                      128:{'item245':50,'item239':50,'item240':50,'item243':50,'coin':1280,},
                                      328:{'item245':120,'item239':120,'item240':120,'item243':120,'coin':3280,},
                                      648:{'item245':300,'item239':300,'item240':300,'item243':300,'coin':6480,},
                                      998:{'item245':450,'item239':450,'item240':450,'item243':450,'coin':10000,},
                                      1998:{'item245':900,'item239':900,'item240':900,'item243':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o91':{      #死斗龙怒
                        
                         'days':[181,182],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item246':20,'item244':20,'item241':20,'item242':20,'coin':680,},
                                      128:{'item246':50,'item244':50,'item241':50,'item242':50,'coin':1280,},
                                      328:{'item246':120,'item244':120,'item241':120,'item242':120,'coin':3280,},
                                      648:{'item246':300,'item244':300,'item241':300,'item242':300,'coin':6480,},
                                      998:{'item246':450,'item244':450,'item241':450,'item242':450,'coin':10000,},
                                      1998:{'item246':900,'item244':900,'item241':900,'item242':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o92':{      #急智遁甲
                        
                         'days':[183,184],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item277':20,'item275':20,'item276':20,'item271':20,'coin':680,},
                                      128:{'item277':50,'item275':50,'item276':50,'item271':50,'coin':1280,},
                                      328:{'item277':120,'item275':120,'item276':120,'item271':120,'coin':3280,},
                                      648:{'item277':300,'item275':300,'item276':300,'item271':300,'coin':6480,},
                                      998:{'item277':450,'item275':450,'item276':450,'item271':450,'coin':10000,},
                                      1998:{'item277':900,'item275':900,'item276':900,'item271':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o93':{      #步兵技能
                        
                         'days':[185,186],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item206':20,'item293':20,'item205':20,'item203':20,'coin':680,},
                                      128:{'item206':50,'item293':50,'item205':50,'item203':50,'coin':1280,},
                                      328:{'item206':120,'item293':120,'item205':120,'item203':120,'coin':3280,},
                                      648:{'item206':300,'item293':300,'item205':300,'item203':300,'coin':6480,},
                                      998:{'item206':450,'item293':450,'item205':450,'item203':450,'coin':10000,},
                                      1998:{'item206':900,'item293':900,'item205':900,'item203':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o94':{      #骑兵技能
                        
                         'days':[187,188],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item294':20,'item211':20,'item212':20,'item290':20,'coin':680,},
                                      128:{'item294':50,'item211':50,'item212':50,'item290':50,'coin':1280,},
                                      328:{'item294':120,'item211':120,'item212':120,'item290':120,'coin':3280,},
                                      648:{'item294':300,'item211':300,'item212':300,'item290':300,'coin':6480,},
                                      998:{'item294':450,'item211':450,'item212':450,'item290':450,'coin':10000,},
                                      1998:{'item294':900,'item211':900,'item212':900,'item290':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o95':{      #方士技能
                        
                         'days':[189,190],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item296':20,'item292':20,'item223':20,'item224':20,'coin':680,},
                                      128:{'item296':50,'item292':50,'item223':50,'item224':50,'coin':1280,},
                                      328:{'item296':120,'item292':120,'item223':120,'item224':120,'coin':3280,},
                                      648:{'item296':300,'item292':300,'item223':300,'item224':300,'coin':6480,},
                                      998:{'item296':450,'item292':450,'item223':450,'item224':450,'coin':10000,},
                                      1998:{'item296':900,'item292':900,'item223':900,'item224':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o96':{      #阵法
                        
                         'days':[191,192],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item190':20,'item192':4,'item189':20,'item191':4,'coin':680,},
                                      128:{'item190':40,'item192':8,'item189':40,'item191':8,'coin':1280,},
                                      328:{'item190':80,'item192':12,'item189':80,'item191':12,'coin':3280,},
                                      648:{'item190':180,'item192':26,'item189':180,'item191':26,'coin':6480,},
                                      998:{'item190':300,'item192':40,'item189':300,'item191':40,'coin':10000,},
                                      1998:{'item190':640,'item192':85,'item189':640,'item191':85,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o97':{      #冲阵攻心
                        
                         'days':[193,194],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item245':20,'item239':20,'item240':20,'item243':20,'coin':680,},
                                      128:{'item245':50,'item239':50,'item240':50,'item243':50,'coin':1280,},
                                      328:{'item245':120,'item239':120,'item240':120,'item243':120,'coin':3280,},
                                      648:{'item245':300,'item239':300,'item240':300,'item243':300,'coin':6480,},
                                      998:{'item245':450,'item239':450,'item240':450,'item243':450,'coin':10000,},
                                      1998:{'item245':900,'item239':900,'item240':900,'item243':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o98':{      #死斗龙怒
                        
                         'days':[195,196],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item246':20,'item244':20,'item241':20,'item242':20,'coin':680,},
                                      128:{'item246':50,'item244':50,'item241':50,'item242':50,'coin':1280,},
                                      328:{'item246':120,'item244':120,'item241':120,'item242':120,'coin':3280,},
                                      648:{'item246':300,'item244':300,'item241':300,'item242':300,'coin':6480,},
                                      998:{'item246':450,'item244':450,'item241':450,'item242':450,'coin':10000,},
                                      1998:{'item246':900,'item244':900,'item241':900,'item242':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o99':{      #急智遁甲
                        
                         'days':[197,198],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item277':20,'item275':20,'item276':20,'item271':20,'coin':680,},
                                      128:{'item277':50,'item275':50,'item276':50,'item271':50,'coin':1280,},
                                      328:{'item277':120,'item275':120,'item276':120,'item271':120,'coin':3280,},
                                      648:{'item277':300,'item275':300,'item276':300,'item271':300,'coin':6480,},
                                      998:{'item277':450,'item275':450,'item276':450,'item271':450,'coin':10000,},
                                      1998:{'item277':900,'item275':900,'item276':900,'item271':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o100':{      #步兵技能
                        
                         'days':[199,200],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item206':20,'item293':20,'item205':20,'item203':20,'coin':680,},
                                      128:{'item206':50,'item293':50,'item205':50,'item203':50,'coin':1280,},
                                      328:{'item206':120,'item293':120,'item205':120,'item203':120,'coin':3280,},
                                      648:{'item206':300,'item293':300,'item205':300,'item203':300,'coin':6480,},
                                      998:{'item206':450,'item293':450,'item205':450,'item203':450,'coin':10000,},
                                      1998:{'item206':900,'item293':900,'item205':900,'item203':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o101':{      #骑兵技能
                        
                         'days':[201,202],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item294':20,'item211':20,'item212':20,'item290':20,'coin':680,},
                                      128:{'item294':50,'item211':50,'item212':50,'item290':50,'coin':1280,},
                                      328:{'item294':120,'item211':120,'item212':120,'item290':120,'coin':3280,},
                                      648:{'item294':300,'item211':300,'item212':300,'item290':300,'coin':6480,},
                                      998:{'item294':450,'item211':450,'item212':450,'item290':450,'coin':10000,},
                                      1998:{'item294':900,'item211':900,'item212':900,'item290':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o102':{      #方士技能
                        
                         'days':[203,204],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item296':20,'item292':20,'item223':20,'item224':20,'coin':680,},
                                      128:{'item296':50,'item292':50,'item223':50,'item224':50,'coin':1280,},
                                      328:{'item296':120,'item292':120,'item223':120,'item224':120,'coin':3280,},
                                      648:{'item296':300,'item292':300,'item223':300,'item224':300,'coin':6480,},
                                      998:{'item296':450,'item292':450,'item223':450,'item224':450,'coin':10000,},
                                      1998:{'item296':900,'item292':900,'item223':900,'item224':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o103':{      #阵法
                        
                         'days':[205,206],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item190':20,'item192':4,'item189':20,'item191':4,'coin':680,},
                                      128:{'item190':40,'item192':8,'item189':40,'item191':8,'coin':1280,},
                                      328:{'item190':80,'item192':12,'item189':80,'item191':12,'coin':3280,},
                                      648:{'item190':180,'item192':26,'item189':180,'item191':26,'coin':6480,},
                                      998:{'item190':300,'item192':40,'item189':300,'item191':40,'coin':10000,},
                                      1998:{'item190':640,'item192':85,'item189':640,'item191':85,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o104':{      #冲阵攻心
                        
                         'days':[207,208],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item245':20,'item239':20,'item240':20,'item243':20,'coin':680,},
                                      128:{'item245':50,'item239':50,'item240':50,'item243':50,'coin':1280,},
                                      328:{'item245':120,'item239':120,'item240':120,'item243':120,'coin':3280,},
                                      648:{'item245':300,'item239':300,'item240':300,'item243':300,'coin':6480,},
                                      998:{'item245':450,'item239':450,'item240':450,'item243':450,'coin':10000,},
                                      1998:{'item245':900,'item239':900,'item240':900,'item243':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o105':{      #死斗龙怒
                        
                         'days':[209,210],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item246':20,'item244':20,'item241':20,'item242':20,'coin':680,},
                                      128:{'item246':50,'item244':50,'item241':50,'item242':50,'coin':1280,},
                                      328:{'item246':120,'item244':120,'item241':120,'item242':120,'coin':3280,},
                                      648:{'item246':300,'item244':300,'item241':300,'item242':300,'coin':6480,},
                                      998:{'item246':450,'item244':450,'item241':450,'item242':450,'coin':10000,},
                                      1998:{'item246':900,'item244':900,'item241':900,'item242':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o106':{      #急智遁甲
                        
                         'days':[211,212],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item277':20,'item275':20,'item276':20,'item271':20,'coin':680,},
                                      128:{'item277':50,'item275':50,'item276':50,'item271':50,'coin':1280,},
                                      328:{'item277':120,'item275':120,'item276':120,'item271':120,'coin':3280,},
                                      648:{'item277':300,'item275':300,'item276':300,'item271':300,'coin':6480,},
                                      998:{'item277':450,'item275':450,'item276':450,'item271':450,'coin':10000,},
                                      1998:{'item277':900,'item275':900,'item276':900,'item271':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o107':{      #步兵技能
                        
                         'days':[213,214],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item206':20,'item293':20,'item205':20,'item203':20,'coin':680,},
                                      128:{'item206':50,'item293':50,'item205':50,'item203':50,'coin':1280,},
                                      328:{'item206':120,'item293':120,'item205':120,'item203':120,'coin':3280,},
                                      648:{'item206':300,'item293':300,'item205':300,'item203':300,'coin':6480,},
                                      998:{'item206':450,'item293':450,'item205':450,'item203':450,'coin':10000,},
                                      1998:{'item206':900,'item293':900,'item205':900,'item203':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o108':{      #骑兵技能
                        
                         'days':[215,216],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item294':20,'item211':20,'item212':20,'item290':20,'coin':680,},
                                      128:{'item294':50,'item211':50,'item212':50,'item290':50,'coin':1280,},
                                      328:{'item294':120,'item211':120,'item212':120,'item290':120,'coin':3280,},
                                      648:{'item294':300,'item211':300,'item212':300,'item290':300,'coin':6480,},
                                      998:{'item294':450,'item211':450,'item212':450,'item290':450,'coin':10000,},
                                      1998:{'item294':900,'item211':900,'item212':900,'item290':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o109':{      #方士技能
                        
                         'days':[217,218],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item296':20,'item292':20,'item223':20,'item224':20,'coin':680,},
                                      128:{'item296':50,'item292':50,'item223':50,'item224':50,'coin':1280,},
                                      328:{'item296':120,'item292':120,'item223':120,'item224':120,'coin':3280,},
                                      648:{'item296':300,'item292':300,'item223':300,'item224':300,'coin':6480,},
                                      998:{'item296':450,'item292':450,'item223':450,'item224':450,'coin':10000,},
                                      1998:{'item296':900,'item292':900,'item223':900,'item224':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o110':{      #阵法
                        
                         'days':[219,220],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item190':20,'item192':4,'item189':20,'item191':4,'coin':680,},
                                      128:{'item190':40,'item192':8,'item189':40,'item191':8,'coin':1280,},
                                      328:{'item190':80,'item192':12,'item189':80,'item191':12,'coin':3280,},
                                      648:{'item190':180,'item192':26,'item189':180,'item191':26,'coin':6480,},
                                      998:{'item190':300,'item192':40,'item189':300,'item191':40,'coin':10000,},
                                      1998:{'item190':640,'item192':85,'item189':640,'item191':85,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o111':{      #冲阵攻心
                        
                         'days':[221,222],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item245':20,'item239':20,'item240':20,'item243':20,'coin':680,},
                                      128:{'item245':50,'item239':50,'item240':50,'item243':50,'coin':1280,},
                                      328:{'item245':120,'item239':120,'item240':120,'item243':120,'coin':3280,},
                                      648:{'item245':300,'item239':300,'item240':300,'item243':300,'coin':6480,},
                                      998:{'item245':450,'item239':450,'item240':450,'item243':450,'coin':10000,},
                                      1998:{'item245':900,'item239':900,'item240':900,'item243':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o112':{      #死斗龙怒
                        
                         'days':[223,224],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item246':20,'item244':20,'item241':20,'item242':20,'coin':680,},
                                      128:{'item246':50,'item244':50,'item241':50,'item242':50,'coin':1280,},
                                      328:{'item246':120,'item244':120,'item241':120,'item242':120,'coin':3280,},
                                      648:{'item246':300,'item244':300,'item241':300,'item242':300,'coin':6480,},
                                      998:{'item246':450,'item244':450,'item241':450,'item242':450,'coin':10000,},
                                      1998:{'item246':900,'item244':900,'item241':900,'item242':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o113':{      #急智遁甲
                        
                         'days':[225,226],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item277':20,'item275':20,'item276':20,'item271':20,'coin':680,},
                                      128:{'item277':50,'item275':50,'item276':50,'item271':50,'coin':1280,},
                                      328:{'item277':120,'item275':120,'item276':120,'item271':120,'coin':3280,},
                                      648:{'item277':300,'item275':300,'item276':300,'item271':300,'coin':6480,},
                                      998:{'item277':450,'item275':450,'item276':450,'item271':450,'coin':10000,},
                                      1998:{'item277':900,'item275':900,'item276':900,'item271':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o114':{      #步兵技能
                        
                         'days':[227,228],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item206':20,'item293':20,'item205':20,'item203':20,'coin':680,},
                                      128:{'item206':50,'item293':50,'item205':50,'item203':50,'coin':1280,},
                                      328:{'item206':120,'item293':120,'item205':120,'item203':120,'coin':3280,},
                                      648:{'item206':300,'item293':300,'item205':300,'item203':300,'coin':6480,},
                                      998:{'item206':450,'item293':450,'item205':450,'item203':450,'coin':10000,},
                                      1998:{'item206':900,'item293':900,'item205':900,'item203':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o115':{      #骑兵技能
                        
                         'days':[229,230],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item294':20,'item211':20,'item212':20,'item290':20,'coin':680,},
                                      128:{'item294':50,'item211':50,'item212':50,'item290':50,'coin':1280,},
                                      328:{'item294':120,'item211':120,'item212':120,'item290':120,'coin':3280,},
                                      648:{'item294':300,'item211':300,'item212':300,'item290':300,'coin':6480,},
                                      998:{'item294':450,'item211':450,'item212':450,'item290':450,'coin':10000,},
                                      1998:{'item294':900,'item211':900,'item212':900,'item290':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o116':{      #方士技能
                        
                         'days':[231,232],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item296':20,'item292':20,'item223':20,'item224':20,'coin':680,},
                                      128:{'item296':50,'item292':50,'item223':50,'item224':50,'coin':1280,},
                                      328:{'item296':120,'item292':120,'item223':120,'item224':120,'coin':3280,},
                                      648:{'item296':300,'item292':300,'item223':300,'item224':300,'coin':6480,},
                                      998:{'item296':450,'item292':450,'item223':450,'item224':450,'coin':10000,},
                                      1998:{'item296':900,'item292':900,'item223':900,'item224':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o117':{      #阵法
                        
                         'days':[233,234],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item190':20,'item192':4,'item189':20,'item191':4,'coin':680,},
                                      128:{'item190':40,'item192':8,'item189':40,'item191':8,'coin':1280,},
                                      328:{'item190':80,'item192':12,'item189':80,'item191':12,'coin':3280,},
                                      648:{'item190':180,'item192':26,'item189':180,'item191':26,'coin':6480,},
                                      998:{'item190':300,'item192':40,'item189':300,'item191':40,'coin':10000,},
                                      1998:{'item190':640,'item192':85,'item189':640,'item191':85,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o118':{      #冲阵攻心
                        
                         'days':[235,236],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item245':20,'item239':20,'item240':20,'item243':20,'coin':680,},
                                      128:{'item245':50,'item239':50,'item240':50,'item243':50,'coin':1280,},
                                      328:{'item245':120,'item239':120,'item240':120,'item243':120,'coin':3280,},
                                      648:{'item245':300,'item239':300,'item240':300,'item243':300,'coin':6480,},
                                      998:{'item245':450,'item239':450,'item240':450,'item243':450,'coin':10000,},
                                      1998:{'item245':900,'item239':900,'item240':900,'item243':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o119':{      #死斗龙怒
                        
                         'days':[237,238],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item246':20,'item244':20,'item241':20,'item242':20,'coin':680,},
                                      128:{'item246':50,'item244':50,'item241':50,'item242':50,'coin':1280,},
                                      328:{'item246':120,'item244':120,'item241':120,'item242':120,'coin':3280,},
                                      648:{'item246':300,'item244':300,'item241':300,'item242':300,'coin':6480,},
                                      998:{'item246':450,'item244':450,'item241':450,'item242':450,'coin':10000,},
                                      1998:{'item246':900,'item244':900,'item241':900,'item242':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o120':{      #急智遁甲
                        
                         'days':[239,240],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item277':20,'item275':20,'item276':20,'item271':20,'coin':680,},
                                      128:{'item277':50,'item275':50,'item276':50,'item271':50,'coin':1280,},
                                      328:{'item277':120,'item275':120,'item276':120,'item271':120,'coin':3280,},
                                      648:{'item277':300,'item275':300,'item276':300,'item271':300,'coin':6480,},
                                      998:{'item277':450,'item275':450,'item276':450,'item271':450,'coin':10000,},
                                      1998:{'item277':900,'item275':900,'item276':900,'item271':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o121':{      #步兵技能
                        
                         'days':[241,242],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item206':20,'item293':20,'item205':20,'item203':20,'coin':680,},
                                      128:{'item206':50,'item293':50,'item205':50,'item203':50,'coin':1280,},
                                      328:{'item206':120,'item293':120,'item205':120,'item203':120,'coin':3280,},
                                      648:{'item206':300,'item293':300,'item205':300,'item203':300,'coin':6480,},
                                      998:{'item206':450,'item293':450,'item205':450,'item203':450,'coin':10000,},
                                      1998:{'item206':900,'item293':900,'item205':900,'item203':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o122':{      #骑兵技能
                        
                         'days':[243,244],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item294':20,'item211':20,'item212':20,'item290':20,'coin':680,},
                                      128:{'item294':50,'item211':50,'item212':50,'item290':50,'coin':1280,},
                                      328:{'item294':120,'item211':120,'item212':120,'item290':120,'coin':3280,},
                                      648:{'item294':300,'item211':300,'item212':300,'item290':300,'coin':6480,},
                                      998:{'item294':450,'item211':450,'item212':450,'item290':450,'coin':10000,},
                                      1998:{'item294':900,'item211':900,'item212':900,'item290':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o123':{      #方士技能
                        
                         'days':[245,246],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item296':20,'item292':20,'item223':20,'item224':20,'coin':680,},
                                      128:{'item296':50,'item292':50,'item223':50,'item224':50,'coin':1280,},
                                      328:{'item296':120,'item292':120,'item223':120,'item224':120,'coin':3280,},
                                      648:{'item296':300,'item292':300,'item223':300,'item224':300,'coin':6480,},
                                      998:{'item296':450,'item292':450,'item223':450,'item224':450,'coin':10000,},
                                      1998:{'item296':900,'item292':900,'item223':900,'item224':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o124':{      #阵法
                        
                         'days':[247,248],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item190':20,'item192':4,'item189':20,'item191':4,'coin':680,},
                                      128:{'item190':40,'item192':8,'item189':40,'item191':8,'coin':1280,},
                                      328:{'item190':80,'item192':12,'item189':80,'item191':12,'coin':3280,},
                                      648:{'item190':180,'item192':26,'item189':180,'item191':26,'coin':6480,},
                                      998:{'item190':300,'item192':40,'item189':300,'item191':40,'coin':10000,},
                                      1998:{'item190':640,'item192':85,'item189':640,'item191':85,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o125':{      #冲阵攻心
                        
                         'days':[249,250],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item245':20,'item239':20,'item240':20,'item243':20,'coin':680,},
                                      128:{'item245':50,'item239':50,'item240':50,'item243':50,'coin':1280,},
                                      328:{'item245':120,'item239':120,'item240':120,'item243':120,'coin':3280,},
                                      648:{'item245':300,'item239':300,'item240':300,'item243':300,'coin':6480,},
                                      998:{'item245':450,'item239':450,'item240':450,'item243':450,'coin':10000,},
                                      1998:{'item245':900,'item239':900,'item240':900,'item243':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o126':{      #死斗龙怒
                        
                         'days':[251,252],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item246':20,'item244':20,'item241':20,'item242':20,'coin':680,},
                                      128:{'item246':50,'item244':50,'item241':50,'item242':50,'coin':1280,},
                                      328:{'item246':120,'item244':120,'item241':120,'item242':120,'coin':3280,},
                                      648:{'item246':300,'item244':300,'item241':300,'item242':300,'coin':6480,},
                                      998:{'item246':450,'item244':450,'item241':450,'item242':450,'coin':10000,},
                                      1998:{'item246':900,'item244':900,'item241':900,'item242':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o127':{      #急智遁甲
                        
                         'days':[253,254],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item277':20,'item275':20,'item276':20,'item271':20,'coin':680,},
                                      128:{'item277':50,'item275':50,'item276':50,'item271':50,'coin':1280,},
                                      328:{'item277':120,'item275':120,'item276':120,'item271':120,'coin':3280,},
                                      648:{'item277':300,'item275':300,'item276':300,'item271':300,'coin':6480,},
                                      998:{'item277':450,'item275':450,'item276':450,'item271':450,'coin':10000,},
                                      1998:{'item277':900,'item275':900,'item276':900,'item271':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o128':{      #步兵技能
                        
                         'days':[255,256],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item206':20,'item293':20,'item205':20,'item203':20,'coin':680,},
                                      128:{'item206':50,'item293':50,'item205':50,'item203':50,'coin':1280,},
                                      328:{'item206':120,'item293':120,'item205':120,'item203':120,'coin':3280,},
                                      648:{'item206':300,'item293':300,'item205':300,'item203':300,'coin':6480,},
                                      998:{'item206':450,'item293':450,'item205':450,'item203':450,'coin':10000,},
                                      1998:{'item206':900,'item293':900,'item205':900,'item203':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o129':{      #骑兵技能
                        
                         'days':[257,258],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item294':20,'item211':20,'item212':20,'item290':20,'coin':680,},
                                      128:{'item294':50,'item211':50,'item212':50,'item290':50,'coin':1280,},
                                      328:{'item294':120,'item211':120,'item212':120,'item290':120,'coin':3280,},
                                      648:{'item294':300,'item211':300,'item212':300,'item290':300,'coin':6480,},
                                      998:{'item294':450,'item211':450,'item212':450,'item290':450,'coin':10000,},
                                      1998:{'item294':900,'item211':900,'item212':900,'item290':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o130':{      #方士技能
                        
                         'days':[259,260],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item296':20,'item292':20,'item223':20,'item224':20,'coin':680,},
                                      128:{'item296':50,'item292':50,'item223':50,'item224':50,'coin':1280,},
                                      328:{'item296':120,'item292':120,'item223':120,'item224':120,'coin':3280,},
                                      648:{'item296':300,'item292':300,'item223':300,'item224':300,'coin':6480,},
                                      998:{'item296':450,'item292':450,'item223':450,'item224':450,'coin':10000,},
                                      1998:{'item296':900,'item292':900,'item223':900,'item224':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o131':{      #阵法
                        
                         'days':[261,262],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item190':20,'item192':4,'item189':20,'item191':4,'coin':680,},
                                      128:{'item190':40,'item192':8,'item189':40,'item191':8,'coin':1280,},
                                      328:{'item190':80,'item192':12,'item189':80,'item191':12,'coin':3280,},
                                      648:{'item190':180,'item192':26,'item189':180,'item191':26,'coin':6480,},
                                      998:{'item190':300,'item192':40,'item189':300,'item191':40,'coin':10000,},
                                      1998:{'item190':640,'item192':85,'item189':640,'item191':85,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o132':{      #冲阵攻心
                        
                         'days':[263,264],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item245':20,'item239':20,'item240':20,'item243':20,'coin':680,},
                                      128:{'item245':50,'item239':50,'item240':50,'item243':50,'coin':1280,},
                                      328:{'item245':120,'item239':120,'item240':120,'item243':120,'coin':3280,},
                                      648:{'item245':300,'item239':300,'item240':300,'item243':300,'coin':6480,},
                                      998:{'item245':450,'item239':450,'item240':450,'item243':450,'coin':10000,},
                                      1998:{'item245':900,'item239':900,'item240':900,'item243':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o133':{      #死斗龙怒
                        
                         'days':[265,266],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item246':20,'item244':20,'item241':20,'item242':20,'coin':680,},
                                      128:{'item246':50,'item244':50,'item241':50,'item242':50,'coin':1280,},
                                      328:{'item246':120,'item244':120,'item241':120,'item242':120,'coin':3280,},
                                      648:{'item246':300,'item244':300,'item241':300,'item242':300,'coin':6480,},
                                      998:{'item246':450,'item244':450,'item241':450,'item242':450,'coin':10000,},
                                      1998:{'item246':900,'item244':900,'item241':900,'item242':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o134':{      #急智遁甲
                        
                         'days':[267,268],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item277':20,'item275':20,'item276':20,'item271':20,'coin':680,},
                                      128:{'item277':50,'item275':50,'item276':50,'item271':50,'coin':1280,},
                                      328:{'item277':120,'item275':120,'item276':120,'item271':120,'coin':3280,},
                                      648:{'item277':300,'item275':300,'item276':300,'item271':300,'coin':6480,},
                                      998:{'item277':450,'item275':450,'item276':450,'item271':450,'coin':10000,},
                                      1998:{'item277':900,'item275':900,'item276':900,'item271':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o135':{      #步兵技能
                        
                         'days':[269,270],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item206':20,'item293':20,'item205':20,'item203':20,'coin':680,},
                                      128:{'item206':50,'item293':50,'item205':50,'item203':50,'coin':1280,},
                                      328:{'item206':120,'item293':120,'item205':120,'item203':120,'coin':3280,},
                                      648:{'item206':300,'item293':300,'item205':300,'item203':300,'coin':6480,},
                                      998:{'item206':450,'item293':450,'item205':450,'item203':450,'coin':10000,},
                                      1998:{'item206':900,'item293':900,'item205':900,'item203':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o136':{      #骑兵技能
                        
                         'days':[271,272],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item294':20,'item211':20,'item212':20,'item290':20,'coin':680,},
                                      128:{'item294':50,'item211':50,'item212':50,'item290':50,'coin':1280,},
                                      328:{'item294':120,'item211':120,'item212':120,'item290':120,'coin':3280,},
                                      648:{'item294':300,'item211':300,'item212':300,'item290':300,'coin':6480,},
                                      998:{'item294':450,'item211':450,'item212':450,'item290':450,'coin':10000,},
                                      1998:{'item294':900,'item211':900,'item212':900,'item290':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o137':{      #方士技能
                        
                         'days':[273,274],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item296':20,'item292':20,'item223':20,'item224':20,'coin':680,},
                                      128:{'item296':50,'item292':50,'item223':50,'item224':50,'coin':1280,},
                                      328:{'item296':120,'item292':120,'item223':120,'item224':120,'coin':3280,},
                                      648:{'item296':300,'item292':300,'item223':300,'item224':300,'coin':6480,},
                                      998:{'item296':450,'item292':450,'item223':450,'item224':450,'coin':10000,},
                                      1998:{'item296':900,'item292':900,'item223':900,'item224':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o138':{      #阵法
                        
                         'days':[275,276],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item190':20,'item192':4,'item189':20,'item191':4,'coin':680,},
                                      128:{'item190':40,'item192':8,'item189':40,'item191':8,'coin':1280,},
                                      328:{'item190':80,'item192':12,'item189':80,'item191':12,'coin':3280,},
                                      648:{'item190':180,'item192':26,'item189':180,'item191':26,'coin':6480,},
                                      998:{'item190':300,'item192':40,'item189':300,'item191':40,'coin':10000,},
                                      1998:{'item190':640,'item192':85,'item189':640,'item191':85,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o139':{      #冲阵攻心
                        
                         'days':[277,278],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item245':20,'item239':20,'item240':20,'item243':20,'coin':680,},
                                      128:{'item245':50,'item239':50,'item240':50,'item243':50,'coin':1280,},
                                      328:{'item245':120,'item239':120,'item240':120,'item243':120,'coin':3280,},
                                      648:{'item245':300,'item239':300,'item240':300,'item243':300,'coin':6480,},
                                      998:{'item245':450,'item239':450,'item240':450,'item243':450,'coin':10000,},
                                      1998:{'item245':900,'item239':900,'item240':900,'item243':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o140':{      #死斗龙怒
                        
                         'days':[279,280],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item246':20,'item244':20,'item241':20,'item242':20,'coin':680,},
                                      128:{'item246':50,'item244':50,'item241':50,'item242':50,'coin':1280,},
                                      328:{'item246':120,'item244':120,'item241':120,'item242':120,'coin':3280,},
                                      648:{'item246':300,'item244':300,'item241':300,'item242':300,'coin':6480,},
                                      998:{'item246':450,'item244':450,'item241':450,'item242':450,'coin':10000,},
                                      1998:{'item246':900,'item244':900,'item241':900,'item242':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o141':{      #急智遁甲
                        
                         'days':[281,282],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item277':20,'item275':20,'item276':20,'item271':20,'coin':680,},
                                      128:{'item277':50,'item275':50,'item276':50,'item271':50,'coin':1280,},
                                      328:{'item277':120,'item275':120,'item276':120,'item271':120,'coin':3280,},
                                      648:{'item277':300,'item275':300,'item276':300,'item271':300,'coin':6480,},
                                      998:{'item277':450,'item275':450,'item276':450,'item271':450,'coin':10000,},
                                      1998:{'item277':900,'item275':900,'item276':900,'item271':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














	},

'week_card':{		#周卡活动
		'pay':30,		#需要单笔充值的金额，单位：RMB
		#'pay_reward':300,		#立即获得300黄金，单位：黄金
		'cycle':7,		#有奖励的周期，单位：天/偏移时间点
		'cycle_reward':400,		#周期奖励，单位：黄金
		#购买周卡后，立即获得300黄金，购买之后每天可以获得周期奖励，周期奖励是从第二天开始算的，每过一个偏移时间，可领取一次，一共领取7次。
		#第七次的奖励被领取的那天，弹出新一期的周卡奖励
		#玩家没在奖励周期时显示购买入口，玩家在奖励周期内不显示购买入口
		#玩家在奖励周期内时，每天第一次登陆时，发给玩家本日的黄金奖励，登录时推送固定界面
		#若玩家在奖励周期内，没有每天都登陆，未登陆的天数不做计数，即玩家可以完整领取所有7次的奖励
	},
	'member_card':{		#永久卡活动
		'pay':288,		#需要单笔充值的金额，单位：RMB
		'show_pay':6,		#充6元可见
		'day_reward':{'coin':200,'gold':50000,'food':100000,'wood':100000,'iron':100000,'item070':1,'item071':1, 'item021':5},		#每日赠送的奖励
		'limit_count':10,		#奖励最多累积的天数
		'help_info':'member_03', # 帮助
		#购买（或激活）永久卡后，立即获得Min（开服天数,'limit_count'）*'day_reward'奖励
		#之后每一天登录的角色皆可领取一份'day_reward'
		#玩家购买之后立即激活当前服永久卡，在其他区需要手动激活永久卡

	},

############折扣商店#####################
'act_sale_shop':{    #开服后第N天开启，当天可见（同一天只会有一个）
  'begin_time':[12,0],  #开启后持续过当天
  'open_days':{4:'goods1',5:'goods1',6:'goods1',10:'goods2',11:'goods2',12:'goods2',16:'goods3',17:'goods3',18:'goods3',22:'goods4',23:'goods4',24:'goods4',28:'goods5',29:'goods5',30:'goods5',34:'goods6',35:'goods6',36:'goods6',40:'goods7',41:'goods7',42:'goods7',46:'goods1',47:'goods1',48:'goods1',52:'goods2',53:'goods2',54:'goods2',58:'goods3',59:'goods3',60:'goods3',64:'goods4',65:'goods4',66:'goods4',70:'goods5',71:'goods5',72:'goods5',76:'goods6',77:'goods6',78:'goods6',82:'goods7',83:'goods7',84:'goods7',88:'goods1',89:'goods1',90:'goods1',94:'goods2',95:'goods2',96:'goods2',100:'goods3',101:'goods3',102:'goods3',106:'goods4',107:'goods4',108:'goods4',112:'goods5',113:'goods5',114:'goods5',118:'goods6',119:'goods6',120:'goods6',124:'goods7',125:'goods7',126:'goods7',130:'goods1',131:'goods1',132:'goods1',136:'goods2',137:'goods2',138:'goods2',142:'goods3',143:'goods3',144:'goods3',148:'goods4',149:'goods4',150:'goods4',154:'goods5',155:'goods5',156:'goods5',160:'goods6',161:'goods6',162:'goods6',166:'goods7',167:'goods7',168:'goods7',172:'goods1',173:'goods1',174:'goods1',178:'goods2',179:'goods2',180:'goods2',184:'goods3',185:'goods3',186:'goods3',190:'goods4',191:'goods4',192:'goods4',196:'goods5',197:'goods5',198:'goods5',202:'goods6',203:'goods6',204:'goods6',208:'goods7',209:'goods7',210:'goods7',214:'goods1',215:'goods1',216:'goods1',220:'goods2',221:'goods2',222:'goods2',226:'goods3',227:'goods3',228:'goods3',232:'goods4',233:'goods4',234:'goods4',238:'goods5',239:'goods5',240:'goods5',244:'goods6',245:'goods6',246:'goods6',250:'goods7',251:'goods7',252:'goods7',256:'goods1',257:'goods1',258:'goods1',262:'goods2',263:'goods2',264:'goods2',268:'goods3',269:'goods3',270:'goods3',274:'goods4',275:'goods4',276:'goods4',},  #开服第n天有效
  'goods1':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[20,200,6],
      'limit':20,
    },
    '2':{
      'reward':['item003',3],
      'price':[250,750,30],
      'limit':10,
    },
    '3':{
      'reward':['item291',10],
      'price':[400,2000,100],
      'limit':100,
    },
    '4':{
      'reward':['item292',10],
      'price':[400,2000,100],
      'limit':100,
    },
    '5':{
      'reward':['item273',5],
      'price':[200,1500,100],
      'limit':15,
    },
    '6':{
      'reward':['item294',5],
      'price':[400,2000,300],
      'limit':15,
    },
    '7':{
      'reward':['item293',5],
      'price':[400,2000,300],
      'limit':15,
    },
    '8':{
      'reward':['item039',2],
      'price':[500,7500,500],
      'limit':5,
    },
  },
  'goods2':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[20,200,6],
      'limit':20,
    },
    '2':{
      'reward':['item075',5],
      'price':[150,225,30],
      'limit':10,
    },
    '3':{
      'reward':['item206',10],
      'price':[400,2000,100],
      'limit':100,
    },
    '4':{
      'reward':['item211',10],
      'price':[400,2000,100],
      'limit':100,
    },
    '5':{
      'reward':['item244',5],
      'price':[300,2000,100],
      'limit':15,
    },
    '6':{
      'reward':['item296',5],
      'price':[400,2000,300],
      'limit':15,
    },
    '7':{
      'reward':['item295',5],
      'price':[400,2000,300],
      'limit':15,
    },
    '8':{
      'reward':['item039',2],
      'price':[500,7500,500],
      'limit':5,
    },
  },
  'goods3':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[20,200,6],
      'limit':20,
    },
    '2':{
      'reward':['item075',5],
      'price':[150,225,30],
      'limit':10,
    },
    '3':{
      'reward':['item109',5],
      'price':[500,1100,30],
      'limit':20,
    },
    '4':{
      'reward':['item246',5],
      'price':[300,2000,300],
      'limit':15,
    },
    '5':{
      'reward':['item294',5],
      'price':[400,2000,300],
      'limit':15,
    },
    '6':{
      'reward':['item293',5],
      'price':[400,2000,300],
      'limit':15,
    },
    '7':{
      'reward':['item035',1],
      'price':[1000,10000,300],
      'limit':1,
    },
    '8':{
      'reward':['item039',2],
      'price':[500,7500,500],
      'limit':5,
    },
  },
  'goods4':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[20,200,6],
      'limit':20,
    },
    '2':{
      'reward':['item075',5],
      'price':[150,225,30],
      'limit':10,
    },
    '3':{
      'reward':['item109',5],
      'price':[500,1100,30],
      'limit':20,
    },
    '4':{
      'reward':['item245',5],
      'price':[300,2000,300],
      'limit':15,
    },
    '5':{
      'reward':['item296',5],
      'price':[400,2000,300],
      'limit':15,
    },
    '6':{
      'reward':['item295',5],
      'price':[400,2000,300],
      'limit':15,
    },
    '7':{
      'reward':['item035',1],
      'price':[1000,10000,300],
      'limit':1,
    },
    '8':{
      'reward':['item039',2],
      'price':[500,7500,500],
      'limit':5,
    },
  },
  'goods5':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[20,200,6],
      'limit':20,
    },
    '2':{
      'reward':['item075',5],
      'price':[150,225,30],
      'limit':10,
    },
    '3':{
      'reward':['item294',5],
      'price':[400,2000,300],
      'limit':15,
    },
    '4':{
      'reward':['item293',5],
      'price':[400,2000,300],
      'limit':15,
    },
    '5':{
      'reward':['item296',5],
      'price':[400,2000,300],
      'limit':15,
    },
    '6':{
      'reward':['item295',5],
      'price':[400,2000,300],
      'limit':15,
    },
    '7':{
      'reward':['item035',1],
      'price':[1000,10000,300],
      'limit':1,
    },
    '8':{
      'reward':['item039',2],
      'price':[500,7500,500],
      'limit':5,
    },
  },
  'goods6':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[20,200,6],
      'limit':20,
    },
    '2':{
      'reward':['item003',3],
      'price':[250,750,30],
      'limit':10,
    },
    '3':{
      'reward':['item206',10],
      'price':[400,2000,100],
      'limit':100,
    },
    '4':{
      'reward':['item211',10],
      'price':[400,2000,100],
      'limit':100,
    },
    '5':{
      'reward':['item291',10],
      'price':[400,2000,200],
      'limit':100,
    },
    '6':{
      'reward':['item292',10],
      'price':[400,2000,200],
      'limit':100,
    },
    '7':{
      'reward':['item1030',1],
      'price':[800,2500,500],
      'limit':5,
    },
    '8':{
      'reward':['item1064',1],
      'price':[2000,20000,700],
      'limit':1,
    },
  },
  'goods7':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[20,200,6],
      'limit':20,
    },
    '2':{
      'reward':['item003',3],
      'price':[250,750,30],
      'limit':10,
    },
    '3':{
      'reward':['item274',5],
      'price':[300,2000,200],
      'limit':20,
    },
    '4':{
      'reward':['item240',5],
      'price':[300,2000,200],
      'limit':20,
    },
    '5':{
      'reward':['item244',5],
      'price':[300,2000,300],
      'limit':20,
    },
    '6':{
      'reward':['item280',5],
      'price':[300,2000,300],
      'limit':20,
    },
    '7':{
      'reward':['item1030',1],
      'price':[800,2500,500],
      'limit':5,
    },
    '8':{
      'reward':['item1064',1],
      'price':[2000,20000,700],
      'limit':1,
    },
  },
},












############限时免单########################
'limit_free':{    #开服后第N天开启，当天可见（同一天只会有一个）
  'begin_time':[[12,0,14,0],[18,0,22,0]],  #开启后持续过当天
  'open_days':{1:'goods1',2:'goods1',3:'goods1',7:'goods2',8:'goods2',9:'goods2',13:'goods3',14:'goods3',15:'goods3',19:'goods4',20:'goods4',21:'goods4',25:'goods5',26:'goods5',27:'goods5',31:'goods3',32:'goods3',33:'goods3',37:'goods4',38:'goods4',39:'goods4',43:'goods5',44:'goods5',45:'goods5',49:'goods3',50:'goods3',51:'goods3',55:'goods4',56:'goods4',57:'goods4',61:'goods5',62:'goods5',63:'goods5',67:'goods3',68:'goods3',69:'goods3',73:'goods4',74:'goods4',75:'goods4',79:'goods5',80:'goods5',81:'goods5',85:'goods3',86:'goods3',87:'goods3',91:'goods4',92:'goods4',93:'goods4',97:'goods5',98:'goods5',99:'goods5',103:'goods3',104:'goods3',105:'goods3',109:'goods4',110:'goods4',111:'goods4',115:'goods5',116:'goods5',117:'goods5',121:'goods3',122:'goods3',123:'goods3',127:'goods4',128:'goods4',129:'goods4',133:'goods5',134:'goods5',135:'goods5',139:'goods3',140:'goods3',141:'goods3',145:'goods4',146:'goods4',147:'goods4',151:'goods5',152:'goods5',153:'goods5',157:'goods3',158:'goods3',159:'goods3',163:'goods4',164:'goods4',165:'goods4',169:'goods5',170:'goods5',171:'goods5',175:'goods3',176:'goods3',177:'goods3',181:'goods4',182:'goods4',183:'goods4',187:'goods5',188:'goods5',189:'goods5',193:'goods3',194:'goods3',195:'goods3',199:'goods4',200:'goods4',201:'goods4',205:'goods5',206:'goods5',207:'goods5',211:'goods3',212:'goods3',213:'goods3',217:'goods4',218:'goods4',219:'goods4',223:'goods5',224:'goods5',225:'goods5',229:'goods3',230:'goods3',231:'goods3',235:'goods4',236:'goods4',237:'goods4',241:'goods5',242:'goods5',243:'goods5',247:'goods3',248:'goods3',249:'goods3',253:'goods4',254:'goods4',255:'goods4',259:'goods5',260:'goods5',261:'goods5',265:'goods3',266:'goods3',267:'goods3',271:'goods4',272:'goods4',273:'goods4',},  #开服第n天有效

  'buy_limit':[100,200,300,300,300],  #数组长度为可够买次数，每次购买all_limit次
  'all_limit':30,   #可购买道具的总次数上限，也是购买一次增加的次数
  'free_show':'free_show_speak',
  'goods1':{ 
    'free_chance':0.25, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item036':3},
        'price':80,
        'limit':10,
      },
      '2':{
        'reward':{'item213':5},
        'price':60,
        'limit':30,
      },
      '3':{
        'reward':{'item215':5},
        'price':60,
       'limit':30,
      },
      '4':{
        'reward':{'item214':5},
        'price':120,
        'limit':30,
      },
      '5':{
        'reward':{'item216':5},
        'price':120,
        'limit':30,
      },
      '6':{
        'reward':{'item1181':1},
        'price':225,
        'limit':5,
      },
    },
  },
  'goods2':{ 
    'free_chance':0.25, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item036':3},
        'price':80,
        'limit':10,
      },
      '2':{
        'reward':{'item219':5},
        'price':60,
        'limit':30,
      },
      '3':{
        'reward':{'item220':5},
        'price':60,
       'limit':30,
      },
      '4':{
        'reward':{'item221':5},
        'price':120,
        'limit':30,
      },
      '5':{
        'reward':{'item222':5},
        'price':120,
        'limit':30,
      },
      '6':{
        'reward':{'item1181':1},
        'price':225,
        'limit':5,
      },
    },
  },
  'goods3':{ 
    'free_chance':0.25, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item036':3},
        'price':80,
        'limit':10,
      },
      '2':{
        'reward':{'item124':5},
        'price':150,
        'limit':60,
      },
      '3':{
        'reward':{'item205':5},
        'price':200,
       'limit':30,
      },
      '4':{
        'reward':{'item212':5},
        'price':200,
        'limit':30,
      },
      '5':{
        'reward':{'item217':5},
        'price':200,
        'limit':30,
      },
      '6':{
        'reward':{'item224':5},
        'price':200,
        'limit':30,
      },
    },
  },
  'goods4':{ 
    'free_chance':0.25, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item036':3},
        'price':80,
        'limit':10,
      },
      '2':{
        'reward':{'item124':5},
        'price':150,
        'limit':60,
      },
      '3':{
        'reward':{'item1007':3},
        'price':300,
       'limit':20,
      },
      '4':{
        'reward':{'item166':5},
        'price':120,
        'limit':40,
      },
      '5':{
        'reward':{'item168':5},
        'price':120,
        'limit':40,
      },
      '6':{
        'reward':{'item167':5},
        'price':120,
        'limit':40,
      },
    },
  },
  'goods5':{ 
    'free_chance':0.25, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item036':3},
        'price':80,
        'limit':10,
      },
      '2':{
        'reward':{'item124':5},
        'price':150,
        'limit':60,
      },
      '3':{
        'reward':{'item1007':3},
        'price':300,
       'limit':20,
      },
      '4':{
        'reward':{'item165':5},
        'price':120,
        'limit':40,
      },
      '5':{
        'reward':{'item164':5},
        'price':120,
        'limit':40,
      },
      '6':{
        'reward':{'item169':5},
        'price':120,
        'limit':40,
      },
    },
  },
},








###########################累计消费###########################
           'consume':{       
                   'title':'502054',
                   'msg_name': '502001',
                   'msg_info': '502002',
                   'open_days':{'1_2':'C4','3_4':'C4','5_6':'C4','7_8':'C4','9_10':'C4','11_12':'C4','13_14':'C4','15_16':'C4','17_18':'C4','19_20':'C4','21_22':'C4','23_24':'C4','25_26':'C4','27_28':'C4','29_30':'C4','31_32':'C4','33_34':'C4','35_36':'C4','37_38':'C4','39_40':'C4','41_42':'C4','43_44':'C4','45_46':'C4','47_48':'C4','49_50':'C4','51_52':'C4','53_54':'C4','55_56':'C4','57_58':'C4','59_60':'C4','61_62':'C4','63_64':'C4','65_66':'C4','67_68':'C4','69_70':'C4','71_72':'C4','73_74':'C4','75_76':'C4','77_78':'C4','79_80':'C4','81_82':'C4','83_84':'C4','85_86':'C4','87_88':'C4','89_90':'C4','91_92':'C4','93_94':'C4','95_96':'C4','97_98':'C4','99_100':'C4','101_102':'C4','103_104':'C4','105_106':'C4','107_108':'C4','109_110':'C4','111_112':'C4','113_114':'C4','115_116':'C4','117_118':'C4','119_120':'C4','121_122':'C4','123_124':'C4','125_126':'C4','127_128':'C4','129_130':'C4','131_132':'C4','133_134':'C4','135_136':'C4','137_138':'C4','139_140':'C4','141_142':'C4','143_144':'C4','145_146':'C4','147_148':'C4','149_150':'C4','151_152':'C4','153_154':'C4','155_156':'C4','157_158':'C4','159_160':'C4','161_162':'C4','163_164':'C4','165_166':'C4','167_168':'C4','169_170':'C4','171_172':'C4','173_174':'C4','175_176':'C4','177_178':'C4','179_180':'C4','181_182':'C4','183_184':'C4','185_186':'C4','187_188':'C4','189_190':'C4','191_192':'C4','193_194':'C4','195_196':'C4','197_198':'C4','199_200':'C4','201_202':'C4','203_204':'C4','205_206':'C4','207_208':'C4','209_210':'C4','211_212':'C4','213_214':'C4','215_216':'C4','217_218':'C4','219_220':'C4','221_222':'C4','223_224':'C4','225_226':'C4','227_228':'C4','229_230':'C4','231_232':'C4','233_234':'C4','235_236':'C4','237_238':'C4','239_240':'C4','241_242':'C4','243_244':'C4','245_246':'C4','247_248':'C4','249_250':'C4','251_252':'C4','253_254':'C4','255_256':'C4','257_258':'C4','259_260':'C4','261_262':'C4','263_264':'C4','265_266':'C4','267_268':'C4','269_270':'C4','271_272':'C4','273_274':'C4','275_276':'C4','277_278':'C4','279_280':'C4','281_282':'C4','283_284':'C4','285_286':'C4','287_288':'C4','289_290':'C4','291_292':'C4','293_294':'C4','295_296':'C4','297_298':'C4','299_300':'C4',},
                        
         'C1':{      #第一期
                        
                         'character':'hero703',
                         'title':'502010',
                         'tips':'502011',
                         'reward':{
                                      300:{'food':30000,'item037':5,},
                                      800:{'food':50000,'item037':10,},
                                      1500:{'food':70000,'item037':15,'item101':1,},
                                      3000:{'food':100000,'item032':10,'item037':20,},
                                      5000:{'gold':75000,'food':150000,'item037':25,'item104':1,},
                                      10000:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item021':20,},
                                      20000:{'gold':150000,'food':300000,'wood':300000,'iron':300000,'item107':70,},
                                      
                                     },

                            },     
         'C2':{      #第二期
                        
                         'character':'hero703',
                         'title':'502010',
                         'tips':'502011',
                         'reward':{
                                      300:{'food':50000,'item037':10,},
                                      800:{'food':70000,'item037':20,},
                                      1500:{'food':100000,'item037':30,'item101':1,},
                                      3000:{'food':150000,'item032':10,'item037':40,},
                                      5000:{'gold':100000,'food':200000,'item037':25,'item104':1,},
                                      10000:{'gold':150000,'food':300000,'wood':200000,'iron':200000,'item107':50,},
                                      20000:{'gold':200000,'food':400000,'wood':300000,'iron':300000,'item107':70,},
                                      30000:{'gold':250000,'food':500000,'wood':500000,'iron':500000,'item107':100,},
                                     },

                            },     
         'C3':{      #第三期
                        
                         'character':'hero703',
                         'title':'502010',
                         'tips':'502011',
                         'reward':{
                                      300:{'food':100000,'item037':20,},
                                      800:{'food':150000,'item037':30,},
                                      1500:{'food':200000,'item037':40,'item104':1,},
                                      3000:{'food':250000,'item032':20,'item037':50,},
                                      5000:{'gold':150000,'food':300000,'item037':60,'item104':2,},
                                      10000:{'gold':200000,'food':400000,'wood':400000,'iron':400000,'item107':100,},
                                      20000:{'gold':250000,'food':500000,'wood':500000,'iron':500000,'item107':150,},
                                      30000:{'gold':300000,'food':600000,'wood':600000,'iron':600000,'item107':200,},
                                     },

                            },     
         'C4':{      #第四期
                        
                         'character':'hero703',
                         'title':'502010',
                         'tips':'502011',
                         'reward':{
                                      300:{'gold':75000,'food':50000,'item130':50,},
                                      800:{'gold':100000,'food':100000,'item113':50,},
                                      1500:{'gold':125000,'food':150000,'item107':50,},
                                      3000:{'gold':150000,'food':200000,'item130':100,},
                                      5000:{'gold':200000,'food':250000,'item113':100,},
                                      10000:{'gold':300000,'food':300000,'wood':100000,'iron':100000,'item107':100,},
                                      20000:{'gold':400000,'food':350000,'wood':200000,'iron':200000,'item113':200,},
                                      30000:{'gold':500000,'food':400000,'wood':300000,'iron':300000,'item107':300,},
                                     },

                            },},

#################################转盘#################################
	'dial':{		#三国秘藏（自选转盘）
		'name':'502055',		#标题名称
		'tips':'502056',		#活动说明
		'buy_num':50,		#充值满150money，可以抽奖一次
		'reward':{		#开服第几天开启，以及本次活动使用的奖池
	
			#1: 'd1',
			#24: 'd2',
			#26: 'd1',
			#28: 'd2',
			#30: 'd1',
			#32: 'd3',
			#34: 'd3',

		},
	},
	'treasure':{		#天下珍宝
		'name':'502057',		#标题名称
		'tips':'502058',		#活动说明
		'time':24,		#活动有效期，单位，小时
		'buy_one':[1,100,1000,25],		#每期活动初始免费次数，之后每次购买消耗的黄金，以及每次获得的银币数量，每次购买获得的积分
		'buy_five':[0,488,5000,125],		#每期活动初始免费次数，之后每次购买消耗的黄金，以及每次获得的银币数量，每次购买获得的积分
		'reward':{		#开服第几天开启，以及本次活动使用的奖池
			#1: 't1',
			#25: 't1',
			#27: 't2',
			#29: 't2',
                        #31: 't3',
                        #33: 't3',
                        #12: 't1',


		},
	},
	'reward_house':{
                                                          'd1':{
'award':[
[0.6,5,[['wood',200000],['gold',100000],['food',200000],['iron',200000],['item004',5],['item034',20000],['item203',30],['item217',30],['item290',30],['item223',30],]],
[0.36,3,[['item021',10],['item030',10],['item075',10],['coin',500],['item270',25],['item260',25],['item261',25],['item071',5],['item056',50],['item058',200],]],
[0.04,2,[['star1001',1],['star0801',1],['item238',20],['item233',20],['item236',20],['item229',20],['item036',30],['item603',3],['item606',5],['item076',10],]],
],
                         #额外奖励和每个奖励所需的抽奖次数(显示处理上，如果每挡只有一个奖励则直接显示，若有多个奖励点击展开奖励内容，可领奖时都是点击直接领取

'add':{
3:{'item075':20,},
10:{'item288':100,},
15:{'item036':30,},
30:{'item035':1,},
},
},

'd2':{
'award':[
[0.6,5,[['wood',200000],['gold',100000],['food',200000],['iron',200000],['item004',5],['item034',20000],['item203',30],['item217',30],['item290',30],['item223',30],]],
[0.36,3,[['item021',10],['item030',10],['item075',10],['coin',500],['item270',25],['item260',25],['item261',25],['item071',5],['item056',50],['item058',200],]],
[0.04,2,[['star1201',1],['star1401',1],['item234',20],['item235',20],['item237',20],['item232',20],['item036',30],['item603',3],['item606',5],['item076',10],]],
],
                         #额外奖励和每个奖励所需的抽奖次数(显示处理上，如果每挡只有一个奖励则直接显示，若有多个奖励点击展开奖励内容，可领奖时都是点击直接领取

'add':{
3:{'item075':20,},
10:{'item288':100,},
15:{'item036':30,},
30:{'item035':1,},
},





},

'd3':{
'award':[
[0.6,5,[['wood',200000],['gold',100000],['food',200000],['iron',200000],['item004',5],['item034',20000],['item203',30],['item217',30],['item290',30],['item223',30],]],
[0.36,3,[['item021',10],['item030',10],['item075',10],['coin',500],['item270',25],['item260',25],['item261',25],['item071',5],['item056',50],['item058',200],]],
[0.04,2,[['star1001',1],['star0801',1],['item238',20],['item233',20],['item236',20],['item229',20],['item036',30],['item603',3],['item606',5],['item076',10],]],
],
                         #额外奖励和每个奖励所需的抽奖次数(显示处理上，如果每挡只有一个奖励则直接显示，若有多个奖励点击展开奖励内容，可领奖时都是点击直接领取

'add':{
3:{'item075':20,},
10:{'item271':100,},
15:{'item036':30,},
30:{'item035':1,},
},
},


 
                         't1':{ 
                                             'award':[
                                     [1986,'item419',10,1],
                                     [1000,'item406',10],
                                     [3000,'item030',5],
                                     [1500,'item021',5],
                                     [1666,'item205',5,1],
                                     [1666,'item212',5,1],
                                     [1500,'item037',50],
                                     [3000,'food',100000],
                           ],

                                          'add':{
                                              5:{'item603':1,},
                                              15:{'item036':15,},
                                              30:{'item606':5,},
                                              80:{'item084':3,},
                                         },


                                          'treasure_shop':[

                                      ['item419',10,100,20],
                                      ['item030',1,25,100],
                                      ['item076',1,100,50],
                                      ['item036',1,100,30],
                                      ['item262',10,100,50],
                                      ['item263',10,100,50],
                                      ['item266',10,100,50],
                                      ['item267',10,100,50],
                           ],
                         },




                         't2':{ 
                                             'award':[
                                     [1986,'item420',10,1],
                                     [1000,'item406',10],
                                     [3000,'item030',5],
                                     [1500,'item021',5],
                                     [1666,'item218',5,1],
                                     [1666,'item224',5,1],
                                     [1500,'item037',50],
                                     [3000,'food',100000],
                           ],

                                          'add':{
                                              5:{'item603':1,},
                                              15:{'item036':15,},
                                              30:{'item606':5,},
                                              80:{'item084':3,},
                                         },


                                          'treasure_shop':[

                                      ['item420',10,100,20],
                                      ['item030',1,25,100],
                                      ['item076',1,100,50],
                                      ['item036',1,100,30],
                                      ['item264',10,100,20],
                                      ['item265',10,100,20],
                                      ['item268',10,100,20],
                                      ['item269',10,100,20],
                           ],
                         },



                         't3':{ 
                                             'award':[
                                     [1145,'item770',5,1],
                                     [1000,'item406',10],
                                     [3000,'item029',5],
                                     [1500,'item021',5],
                                     [1666,'item218',5,1],
                                     [1666,'item224',5,1],
                                     [1500,'item037',50],
                                     [3000,'food',100000],
                           ],

                                          'add':{
                                              5:{'item603':1,},
                                              15:{'item036':15,},
                                              30:{'item606':5,},
                                              80:{'item084':3,},
                                         },


                                          'treasure_shop':[

                                      ['item770',5,100,20],
                                      ['item029',1,25,100],
                                      ['item076',1,100,50],
                                      ['item036',1,100,30],
                                      ['item264',10,100,20],
                                      ['item265',10,100,20],
                                      ['item268',10,100,20],
                                      ['item269',10,100,20],
                           ],
                         },



},


###########限时兑换############
         'exchange_shop':{    #兑换商店
        'name':100000,
        'sort_by':['item042','item043','item044','item045','item195','item176','item1211'],#排序规则，使用货币道具ID
        'consume_items':['item042','item043','item044','item045','item195','item176','item1211'],# 每个商店中消耗的货币
           'begin_time':[
 {'time':[1,2],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[3,4],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[5,6],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[7,8],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[9,10],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[11,12],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[13,14],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[15,16],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[17,18],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[19,20],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[21,22],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[23,24],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[25,26],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[27,28],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[29,30],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[31,32],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[33,34],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[35,36],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[37,38],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[39,40],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[41,42],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[43,44],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[45,46],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[47,48],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[49,50],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[51,52],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[53,54],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[55,56],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[57,58],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[59,60],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[61,62],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[63,64],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[65,66],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[67,68],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[69,70],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[71,72],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[73,74],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[75,76],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[77,78],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[79,80],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[81,82],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[83,84],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[85,86],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[87,88],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[89,90],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[91,92],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[93,94],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[95,96],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[97,98],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[99,100],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[101,102],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[103,104],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[105,106],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[107,108],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[109,110],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[111,112],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[113,114],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[115,116],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[117,118],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[119,120],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[121,122],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[123,124],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[125,126],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[127,128],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[129,130],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[131,132],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[133,134],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[135,136],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[137,138],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[139,140],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[141,142],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[143,144],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[145,146],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[147,148],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[149,150],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[151,152],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[153,154],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[155,156],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[157,158],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[159,160],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[161,162],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[163,164],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[165,166],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[167,168],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[169,170],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[171,172],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[173,174],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[175,176],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[177,178],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[179,180],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[181,182],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[183,184],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[185,186],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[187,188],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[189,190],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[191,192],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[193,194],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[195,196],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[197,198],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[199,200],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[201,202],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[203,204],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[205,206],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[207,208],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[209,210],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[211,212],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[213,214],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[215,216],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[217,218],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[219,220],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[221,222],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[223,224],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[225,226],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[227,228],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[229,230],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[231,232],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[233,234],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[235,236],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[237,238],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[239,240],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[241,242],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[243,244],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[245,246],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[247,248],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[249,250],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[251,252],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[253,254],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[255,256],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[257,258],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[259,260],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[261,262],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[263,264],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[265,266],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[267,268],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[269,270],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[271,272],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[273,274],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[275,276],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[277,278],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[279,280],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[281,282],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[283,284],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[285,286],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[287,288],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[289,290],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[291,292],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[293,294],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[295,296],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[297,298],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[299,300],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[301,302],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
 {'time':[303,304],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1']},
], #开服第几天，1=开启天数，2=结束天数 （eg：偏移值5点，1号5点开服, 'days':[1, 2] 活动入口会在3号5点关闭,[青龙qinglong_shop，白虎baihu_shop,朱雀zhuque_shop,玄武xuanwu_shop]从这四个商店里取goods，当数组里该位置为null时，商店分页显示为未开启

     'shoplists':[
        {   #五铢商店

         'goods1':[
         {
           'reward':{'item412':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item056':20},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item058':80},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item602':2},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item605':4},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item272':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item241':10},
           'price':10,
           'limit':60,
              },











          ],
         'goods2':[
         {
           'reward':{'item412':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item056':20},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item058':80},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item602':2},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item605':4},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item272':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item241':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item273':10},
           'price':10,
           'limit':60,
              },










          ],
         'goods3':[
         {
           'reward':{'item412':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item419':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item056':20},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item058':80},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item602':2},
           'price':10,
           'limit':60,
              },

         {
           'reward':{'item272':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item241':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item273':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item242':10},
           'price':10,
           'limit':60,
              },








          ],
         'goods4':[
         {
           'reward':{'item412':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item419':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item420':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item056':20},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item058':80},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item602':2},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item605':4},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item272':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item241':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item273':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item242':10},
           'price':10,
           'limit':60,
              },







          ],
         'goods5':[
         {
           'reward':{'item412':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item056':20},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item058':80},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item602':2},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item605':4},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item272':10},
           'price':10,
           'limit':60,
              },












          ],
         'goods6':[
         {
           'reward':{'item412':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item419':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item056':20},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item058':80},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item602':2},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item605':4},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item272':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item241':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item273':10},
           'price':10,
           'limit':60,
              },









          ],
         'goods7':[
         {
           'reward':{'item412':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item419':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item420':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item056':20},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item058':80},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item602':2},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item605':4},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item272':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item241':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item273':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item242':10},
           'price':10,
           'limit':60,
              },







          ],
         'goods8':[
         {
           'reward':{'item412':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item419':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item420':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item426':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item056':20},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item058':80},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item602':2},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item605':4},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item272':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item241':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item273':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item242':10},
           'price':10,
           'limit':60,
              },






          ],
         'goods9':[
         {
           'reward':{'item412':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item419':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item420':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item426':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item056':20},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item058':80},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item602':2},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item605':4},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item272':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item241':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item273':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item242':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item1064':1},
           'price':600,
           'limit':10,
              },





          ],
         'goods10':[
         {
           'reward':{'item1057':1},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item1058':1},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item1059':1},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item1060':1},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item056':20},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item058':80},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item602':2},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item605':4},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item272':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item241':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item273':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item242':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item1064':1},
           'price':600,
           'limit':10,
              },





          ],
},

{   #武帝商店

 'goods1':[
        {
           'reward':{'equip':['equip036']},
           'price':30,
           'limit':1,
              },
        {
           'reward':{'equip':['equip039']},
           'price':200,
           'limit':1,
              },
        {
           'reward':{'equip':['equip050']},
           'price':300,
           'limit':1,
              },
        {
           'reward':{'equip':['equip054']},
           'price':400,
           'limit':1,
              },
        {
           'reward':{'equip':['equip047']},
           'price':500,
           'limit':1,
              },
        {
           'reward':{'item427':10},
           'price':15,
           'limit':50,
              },
        {
           'reward':{'item427':200},
           'price':300,
           'limit':20,
              },
        {
           'reward':{'item602':3},
           'price':10,
           'limit':60,
              },
        {
           'reward':{'item605':6},
           'price':10,
           'limit':60,
              },






          ],
 'goods2':[
        {
           'reward':{'equip':['equip036']},
           'price':50,
           'limit':1,
              },
        {
           'reward':{'equip':['equip039']},
           'price':400,
           'limit':1,
              },
        {
           'reward':{'equip':['equip050']},
           'price':600,
           'limit':1,
              },
        {
           'reward':{'equip':['equip054']},
           'price':800,
           'limit':1,
              },
        {
           'reward':{'equip':['equip047']},
           'price':1000,
           'limit':1,
              },
        {
           'reward':{'item427':10},
           'price':15,
           'limit':50,
              },
        {
           'reward':{'item427':200},
           'price':300,
           'limit':20,
              },
        {
           'reward':{'item042':10},
           'price':10,
           'limit':50,
              },







          ],
 'goods3':[
        {
           'reward':{'equip':['equip036']},
           'price':50,
           'limit':1,
              },
        {
           'reward':{'equip':['equip039']},
           'price':400,
           'limit':1,
              },
        {
           'reward':{'equip':['equip050']},
           'price':600,
           'limit':1,
              },
        {
           'reward':{'equip':['equip054']},
           'price':800,
           'limit':1,
              },
        {
           'reward':{'equip':['equip047']},
           'price':1000,
           'limit':1,
              },
        {
           'reward':{'item427':10},
           'price':15,
           'limit':50,
              },
        {
           'reward':{'item427':200},
           'price':300,
           'limit':20,
              },
        {
           'reward':{'item042':10},
           'price':10,
           'limit':50,
              },







          ],
},

{   #天罚商店

 'goods1':[
        {
           'reward':{'item413':5},
           'price':8,
           'limit':120,
              },
        {
           'reward':{'item414':5},
           'price':10,
           'limit':120,
              },
        {
           'reward':{'item415':5},
           'price':12,
           'limit':120,
              },
        {
           'reward':{'item418':5},
           'price':15,
           'limit':120,
              },
        {
           'reward':{'item421':5},
           'price':20,
           'limit':120,
              },
        {
           'reward':{'item059':200},
           'price':10,
           'limit':60,
              },
        {
           'reward':{'item076':10},
           'price':10,
           'limit':60,
              },



          ],
 'goods2':[
        {
           'reward':{'item413':5},
           'price':8,
           'limit':120,
              },
        {
           'reward':{'item414':5},
           'price':10,
           'limit':120,
              },
        {
           'reward':{'item415':5},
           'price':12,
           'limit':120,
              },
        {
           'reward':{'item418':5},
           'price':15,
           'limit':120,
              },
        {
           'reward':{'item421':5},
           'price':20,
           'limit':120,
              },
        {
           'reward':{'item042':10},
           'price':10,
           'limit':50,
              },




          ],
 'goods3':[
        {
           'reward':{'item413':5},
           'price':8,
           'limit':120,
              },
        {
           'reward':{'item414':5},
           'price':10,
           'limit':120,
              },
        {
           'reward':{'item415':5},
           'price':12,
           'limit':120,
              },
        {
           'reward':{'item418':5},
           'price':15,
           'limit':120,
              },
        {
           'reward':{'item421':5},
           'price':20,
           'limit':120,
              },
        {
           'reward':{'item042':10},
           'price':10,
           'limit':50,
              },




          ],
},

{   #斗神商店

 'goods1':[
        {
           'reward':{'equip':['equip059']},
           'price':50,
           'limit':1,
              },
        {
           'reward':{'equip':['equip060']},
           'price':400,
           'limit':1,
              },
        {
           'reward':{'equip':['equip061']},
           'price':600,
           'limit':1,
              },
        {
           'reward':{'equip':['equip062']},
           'price':800,
           'limit':1,
              },
        {
           'reward':{'equip':['equip063']},
           'price':1000,
           'limit':1,
              },
        {
           'reward':{'item428':10},
           'price':20,
           'limit':50,
              },
        {
           'reward':{'item428':200},
           'price':400,
           'limit':20,
              },
        {
           'reward':{'item042':10},
           'price':10,
           'limit':50,
              },













          ],
 'goods2':[
        {
           'reward':{'equip':['equip059']},
           'price':50,
           'limit':1,
              },
        {
           'reward':{'equip':['equip060']},
           'price':400,
           'limit':1,
              },
        {
           'reward':{'equip':['equip061']},
           'price':600,
           'limit':1,
              },
        {
           'reward':{'equip':['equip062']},
           'price':800,
           'limit':1,
              },
        {
           'reward':{'equip':['equip063']},
           'price':1000,
           'limit':1,
              },
        {
           'reward':{'item428':10},
           'price':20,
           'limit':1,
              },
        {
           'reward':{'item428':200},
           'price':400,
           'limit':1,
              },
        {
           'reward':{'item042':10},
           'price':10,
           'limit':50,
              },













          ],
 'goods3':[
        {
           'reward':{'equip':['equip059']},
           'price':50,
           'limit':1,
              },
        {
           'reward':{'equip':['equip060']},
           'price':400,
           'limit':1,
              },
        {
           'reward':{'equip':['equip061']},
           'price':600,
           'limit':1,
              },
        {
           'reward':{'equip':['equip062']},
           'price':800,
           'limit':1,
              },
        {
           'reward':{'equip':['equip063']},
           'price':1000,
           'limit':1,
              },
        {
           'reward':{'item428':10},
           'price':20,
           'limit':1,
              },
        {
           'reward':{'item428':200},
           'price':400,
           'limit':1,
              },
        {
           'reward':{'item042':10},
           'price':10,
           'limit':50,
              },













          ],
},

{   #羽林商店

 'goods1':[
        {
           'reward':{'equip':['equip070']},
           'price':20,
           'limit':1,
              },
        {
           'reward':{'equip':['equip071']},
           'price':30,
           'limit':1,
              },
        {
           'reward':{'equip':['equip072']},
           'price':40,
           'limit':1,
              },
        {
           'reward':{'equip':['equip073']},
           'price':50,
           'limit':1,
              },
        {
           'reward':{'equip':['equip074']},
           'price':60,
           'limit':1,
              },
        {
           'reward':{'item042':10},
           'price':10,
           'limit':50,
              },




          ],
},

{   #轩辕商店

 'goods1':[
        {
           'reward':{'equip':['equip065']},
           'price':4000,
           'limit':1,
              },
        {
           'reward':{'equip':['equip066']},
           'price':4000,
           'limit':1,
              },
        {
           'reward':{'equip':['equip067']},
           'price':4500,
           'limit':1,
              },
        {
           'reward':{'equip':['equip068']},
           'price':4500,
           'limit':1,
              },
        {
           'reward':{'equip':['equip069']},
           'price':5000,
           'limit':1,
              },
        {
           'reward':{'item434':10},
           'price':200,
           'limit':240,
              },
        {
           'reward':{'item434':200},
           'price':4000,
           'limit':12,
              },
        {
           'reward':{'item435':10},
           'price':300,
           'limit':100,
              },
        {
           'reward':{'item435':200},
           'price':6000,
           'limit':5,
              },

          ],
},

{   #女妖商店

 'goods1':[
        {
           'reward':{'equip':['equip093']},
           'price':500,
           'limit':1,
              },
        {
           'reward':{'equip':['equip094']},
           'price':500,
           'limit':1,
              },
        {
           'reward':{'equip':['equip095']},
           'price':800,
           'limit':1,
              },
        {
           'reward':{'equip':['equip096']},
           'price':800,
           'limit':1,
              },
        {
           'reward':{'equip':['equip097']},
           'price':1000,
           'limit':1,
              },
        {
           'reward':{'item477':10},
           'price':20,
           'limit':200,
              },
        {
           'reward':{'item477':200},
           'price':400,
           'limit':20,
              },



          ],
},

],
},
        'pay_choose':{ 
        'name':502062,
        'info':502063,
        'need_pay_coin':500,
        'limit_time':200,
        'miss_reward':['item042',10], #活动结束后，对未领取奖励的玩家进行补偿{补偿物品，数量}，邮件发奖，奖励总数=剩余可领取次数x奖励数量
        'mail_name':'502081',  #邮件标题
        'mail_info':'502082',  #邮件内容
        'begin_time':[ 
 {'time':[1,2],'goods':'goods10'},
 {'time':[3,4],'goods':'goods10'},
 {'time':[5,6],'goods':'goods10'},
 {'time':[7,8],'goods':'goods10'},
 {'time':[9,10],'goods':'goods10'},
 {'time':[11,12],'goods':'goods10'},
 {'time':[13,14],'goods':'goods10'},
 {'time':[15,16],'goods':'goods10'},
 {'time':[17,18],'goods':'goods10'},
 {'time':[19,20],'goods':'goods10'},
 {'time':[21,22],'goods':'goods10'},
 {'time':[23,24],'goods':'goods10'},
 {'time':[25,26],'goods':'goods10'},
 {'time':[27,28],'goods':'goods10'},
 {'time':[29,30],'goods':'goods10'},
 {'time':[31,32],'goods':'goods10'},
 {'time':[33,34],'goods':'goods10'},
 {'time':[35,36],'goods':'goods10'},
 {'time':[37,38],'goods':'goods10'},
 {'time':[39,40],'goods':'goods10'},
 {'time':[41,42],'goods':'goods10'},
 {'time':[43,44],'goods':'goods10'},
 {'time':[45,46],'goods':'goods10'},
 {'time':[47,48],'goods':'goods10'},
 {'time':[49,50],'goods':'goods10'},
 {'time':[51,52],'goods':'goods10'},
 {'time':[53,54],'goods':'goods10'},
 {'time':[55,56],'goods':'goods10'},
 {'time':[57,58],'goods':'goods10'},
 {'time':[59,60],'goods':'goods10'},
 {'time':[61,62],'goods':'goods10'},
 {'time':[63,64],'goods':'goods10'},
 {'time':[65,66],'goods':'goods10'},
 {'time':[67,68],'goods':'goods10'},
 {'time':[69,70],'goods':'goods10'},
 {'time':[71,72],'goods':'goods10'},
 {'time':[73,74],'goods':'goods10'},
 {'time':[75,76],'goods':'goods10'},
 {'time':[77,78],'goods':'goods10'},
 {'time':[79,80],'goods':'goods10'},
 {'time':[81,82],'goods':'goods10'},
 {'time':[83,84],'goods':'goods10'},
 {'time':[85,86],'goods':'goods10'},
 {'time':[87,88],'goods':'goods10'},
 {'time':[89,90],'goods':'goods10'},
 {'time':[91,92],'goods':'goods10'},
 {'time':[93,94],'goods':'goods10'},
 {'time':[95,96],'goods':'goods10'},
 {'time':[97,98],'goods':'goods10'},
 {'time':[99,100],'goods':'goods10'},
 {'time':[101,102],'goods':'goods10'},
 {'time':[103,104],'goods':'goods10'},
 {'time':[105,106],'goods':'goods10'},
 {'time':[107,108],'goods':'goods10'},
 {'time':[109,110],'goods':'goods10'},
 {'time':[111,112],'goods':'goods10'},
 {'time':[113,114],'goods':'goods10'},
 {'time':[115,116],'goods':'goods10'},
 {'time':[117,118],'goods':'goods10'},
 {'time':[119,120],'goods':'goods10'},
 {'time':[121,122],'goods':'goods10'},
 {'time':[123,124],'goods':'goods10'},
 {'time':[125,126],'goods':'goods10'},
 {'time':[127,128],'goods':'goods10'},
 {'time':[129,130],'goods':'goods10'},
 {'time':[131,132],'goods':'goods10'},
 {'time':[133,134],'goods':'goods10'},
 {'time':[135,136],'goods':'goods10'},
 {'time':[137,138],'goods':'goods10'},
 {'time':[139,140],'goods':'goods10'},
 {'time':[141,142],'goods':'goods10'},
 {'time':[143,144],'goods':'goods10'},
 {'time':[145,146],'goods':'goods10'},
 {'time':[147,148],'goods':'goods10'},
 {'time':[149,150],'goods':'goods10'},
 {'time':[151,152],'goods':'goods10'},
 {'time':[153,154],'goods':'goods10'},
 {'time':[155,156],'goods':'goods10'},
 {'time':[157,158],'goods':'goods10'},
 {'time':[159,160],'goods':'goods10'},
 {'time':[161,162],'goods':'goods10'},
 {'time':[163,164],'goods':'goods10'},
 {'time':[165,166],'goods':'goods10'},
 {'time':[167,168],'goods':'goods10'},
 {'time':[169,170],'goods':'goods10'},
 {'time':[171,172],'goods':'goods10'},
 {'time':[173,174],'goods':'goods10'},
 {'time':[175,176],'goods':'goods10'},
 {'time':[177,178],'goods':'goods10'},
 {'time':[179,180],'goods':'goods10'},
 {'time':[181,182],'goods':'goods10'},
 {'time':[183,184],'goods':'goods10'},
 {'time':[185,186],'goods':'goods10'},
 {'time':[187,188],'goods':'goods10'},
 {'time':[189,190],'goods':'goods10'},
 {'time':[191,192],'goods':'goods10'},
 {'time':[193,194],'goods':'goods10'},
 {'time':[195,196],'goods':'goods10'},
 {'time':[197,198],'goods':'goods10'},
 {'time':[199,200],'goods':'goods10'},
 {'time':[201,202],'goods':'goods10'},
 {'time':[203,204],'goods':'goods10'},
 {'time':[205,206],'goods':'goods10'},
 {'time':[207,208],'goods':'goods10'},
 {'time':[209,210],'goods':'goods10'},
 {'time':[211,212],'goods':'goods10'},
 {'time':[213,214],'goods':'goods10'},
 {'time':[215,216],'goods':'goods10'},
 {'time':[217,218],'goods':'goods10'},
 {'time':[219,220],'goods':'goods10'},
 {'time':[221,222],'goods':'goods10'},
 {'time':[223,224],'goods':'goods10'},
 {'time':[225,226],'goods':'goods10'},
 {'time':[227,228],'goods':'goods10'},
 {'time':[229,230],'goods':'goods10'},
 {'time':[231,232],'goods':'goods10'},
 {'time':[233,234],'goods':'goods10'},
 {'time':[235,236],'goods':'goods10'},
 {'time':[237,238],'goods':'goods10'},
 {'time':[239,240],'goods':'goods10'},
 {'time':[241,242],'goods':'goods10'},
 {'time':[243,244],'goods':'goods10'},
 {'time':[245,246],'goods':'goods10'},
 {'time':[247,248],'goods':'goods10'},
 {'time':[249,250],'goods':'goods10'},
 {'time':[251,252],'goods':'goods10'},
 {'time':[253,254],'goods':'goods10'},
 {'time':[255,256],'goods':'goods10'},
 {'time':[257,258],'goods':'goods10'},
 {'time':[259,260],'goods':'goods10'},
 {'time':[261,262],'goods':'goods10'},
 {'time':[263,264],'goods':'goods10'},
 {'time':[265,266],'goods':'goods10'},
 {'time':[267,268],'goods':'goods10'},
 {'time':[269,270],'goods':'goods10'},
        ],  #开服第几天，1=开启天数，2=结束天数 （eg：偏移值5点，1号5点开服, 'days':[1, 2]   活动入口会在3号5点关闭）




 'goods1':[
        {
           'reward':{'item036':2},
              },
        {
           'reward':{'item042':10},
              },
        {
           'reward':{'item091':1},
              },
        {
           'reward':{'item087':1},
              },
        {
           'reward':{'item088':1},
              },
        {
           'reward':{'item004':3},
              },













          ],
 'goods2':[
        {
           'reward':{'item036':2},
              },
        {
           'reward':{'item042':10},
              },
        {
           'reward':{'item043':10},
              },
        {
           'reward':{'item195':10},
              },
        {
           'reward':{'item091':1},
              },
        {
           'reward':{'item008':1},
              },
        {
           'reward':{'item009':1},
              },
        {
           'reward':{'item087':1},
              },
        {
           'reward':{'item088':1},
              },
        {
           'reward':{'item004':3},
              },









          ],
 'goods3':[
        {
           'reward':{'item770':5},
              },
        {
           'reward':{'item017':1},
              },
        {
           'reward':{'item036':2},
              },
        {
           'reward':{'item042':10},
              },
        {
           'reward':{'item043':10},
              },
        {
           'reward':{'item195':10},
              },
        {
           'reward':{'item091':1},
              },
        {
           'reward':{'item008':1},
              },
        {
           'reward':{'item009':1},
              },
        {
           'reward':{'item087':1},
              },
        {
           'reward':{'item088':1},
              },
        {
           'reward':{'item004':3},
              },







          ],
 'goods4':[
        {
           'reward':{'item770':5},
              },
        {
           'reward':{'item017':1},
              },
        {
           'reward':{'item018':1},
              },
        {
           'reward':{'item036':2},
              },
        {
           'reward':{'item042':10},
              },
        {
           'reward':{'item043':10},
              },
        {
           'reward':{'item195':10},
              },
        {
           'reward':{'item091':1},
              },
        {
           'reward':{'item008':1},
              },
        {
           'reward':{'item009':1},
              },
        {
           'reward':{'item087':1},
              },
        {
           'reward':{'item088':1},
              },
        {
           'reward':{'item004':3},
              },






          ],
 'goods5':[
        {
           'reward':{'item017':1},
              },
        {
           'reward':{'item018':1},
              },
        {
           'reward':{'item019':1},
              },
        {
           'reward':{'item770':5},
              },
        {
           'reward':{'item036':2},
              },
        {
           'reward':{'item042':10},
              },
        {
           'reward':{'item043':10},
              },
        {
           'reward':{'item044':10},
              },
        {
           'reward':{'item195':10},
              },
        {
           'reward':{'item091':1},
              },
        {
           'reward':{'item008':1},
              },
        {
           'reward':{'item009':1},
              },
        {
           'reward':{'item087':1},
              },
        {
           'reward':{'item088':1},
              },
        {
           'reward':{'item004':3},
              },




          ],
 'goods6':[
        {
           'reward':{'item017':1},
              },
        {
           'reward':{'item018':1},
              },
        {
           'reward':{'item019':1},
              },
        {
           'reward':{'item770':5},
              },
        {
           'reward':{'item036':2},
              },
        {
           'reward':{'item042':10},
              },
        {
           'reward':{'item043':10},
              },
        {
           'reward':{'item044':10},
              },
        {
           'reward':{'item045':10},
              },
        {
           'reward':{'item195':10},
              },
        {
           'reward':{'item091':1},
              },
        {
           'reward':{'item008':1},
              },
        {
           'reward':{'item009':1},
              },
        {
           'reward':{'item087':1},
              },
        {
           'reward':{'item088':1},
              },
        {
           'reward':{'item004':3},
              },



          ],
 'goods7':[
        {
           'reward':{'item036':2},
              },
        {
           'reward':{'item042':10},
              },
        {
           'reward':{'item043':10},
              },
        {
           'reward':{'item091':1},
              },
        {
           'reward':{'item087':1},
              },
        {
           'reward':{'item088':1},
              },
        {
           'reward':{'item004':3},
              },












          ],
 'goods8':[
        {
           'reward':{'item770':5},
              },
        {
           'reward':{'item017':1},
              },
        {
           'reward':{'item018':1},
              },
        {
           'reward':{'item036':2},
              },
        {
           'reward':{'item042':10},
              },
        {
           'reward':{'item043':10},
              },
        {
           'reward':{'item044':10},
              },
        {
           'reward':{'item195':10},
              },
        {
           'reward':{'item091':1},
              },
        {
           'reward':{'item008':1},
              },
        {
           'reward':{'item009':1},
              },
        {
           'reward':{'item087':1},
              },
        {
           'reward':{'item088':1},
              },
        {
           'reward':{'item004':3},
              },





          ],
 'goods9':[
        {
           'reward':{'item017':1},
              },
        {
           'reward':{'item018':1},
              },
        {
           'reward':{'item019':1},
              },
        {
           'reward':{'item770':5},
              },
        {
           'reward':{'item036':2},
              },
        {
           'reward':{'item042':10},
              },
        {
           'reward':{'item043':10},
              },
        {
           'reward':{'item044':10},
              },
        {
           'reward':{'item045':10},
              },
        {
           'reward':{'item195':10},
              },
        {
           'reward':{'item176':75},
              },
        {
           'reward':{'item091':1},
              },
        {
           'reward':{'item008':1},
              },
        {
           'reward':{'item009':1},
              },
        {
           'reward':{'item087':1},
              },
        {
           'reward':{'item088':1},
              },
        {
           'reward':{'item004':3},
              },


          ],
 'goods10':[
        {
           'reward':{'item017':1},
              },
        {
           'reward':{'item018':1},
              },
        {
           'reward':{'item019':1},
              },
        {
           'reward':{'item770':5},
              },
        {
           'reward':{'item036':2},
              },
        {
           'reward':{'item042':10},
              },
        {
           'reward':{'item043':10},
              },
        {
           'reward':{'item044':10},
              },
        {
           'reward':{'item045':10},
              },
        {
           'reward':{'item195':10},
              },
        {
           'reward':{'item176':75},
              },
        {
           'reward':{'item1211':10},
              },
        {
           'reward':{'item091':1},
              },
        {
           'reward':{'item008':1},
              },
        {
           'reward':{'item009':1},
              },
        {
           'reward':{'item087':1},
              },
        {
           'reward':{'item088':1},
              },
        {
           'reward':{'item004':3},
              },

          ],


},












############微信小程序分享#####################
	'share_reward':[[0,{'coin':50}],[30,{'coin':50}],[60,{'coin':50}]], #[[cd,reward],],



############惊喜礼包#####################   
        'surprise_gift':{
                         'active_time':[[10,00],[22,00]],
                         'openday':{'3_5':'goods1','9_11':'goods2','15_17':'goods3','21_23':'goods4','27_29':'goods4','33_35':'goods5','39_41':'goods6','45_47':'goods6','51_53':'goods6','57_59':'goods6','63_65':'goods6','69_71':'goods6','75_77':'goods6','81_83':'goods6','87_89':'goods6','93_95':'goods6','99_101':'goods6','105_107':'goods6','111_113':'goods6','117_119':'goods6','123_125':'goods6','129_131':'goods6','133_134':'goods6','137_139':'goods6','141_143':'goods6','145_147':'goods6','149_151':'goods6','153_155':'goods6','157_159':'goods6','161_163':'goods6','165_167':'goods6','169_171':'goods6','173_175':'goods6','177_179':'goods6','181_183':'goods6','185_187':'goods6','189_191':'goods6','193_195':'goods6','197_199':'goods6','201_203':'goods6','205_207':'goods6','209_211':'goods6','213_215':'goods6','217_219':'goods6','221_223':'goods6','225_227':'goods6','229_231':'goods6','233_235':'goods6','237_239':'goods6','241_243':'goods6','245_247':'goods6','249_251':'goods6','253_255':'goods6','257_259':'goods6','261_263':'goods6','265_267':'goods6','269_271':'goods6','273_275':'goods6'},


                         'goods1':{
                                    'gd1105':{ # 需要充值的人民币
                                       'name':'surprise_23', # 礼包名字
                                       'num':1, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':8100,
                                       'reward':{'item1183':3,},
                                      },
                                    'gd1106':{ # 需要充值的人民币
                                       'name':'surprise_24', # 礼包名字
                                       'num':2, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':17100,
                                       'reward':{'item1183':3,'item1182':5,},
                                      },
                                    'gd1107':{ # 需要充值的人民币
                                       'name':'surprise_25', # 礼包名字
                                       'num':2, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':37440,
                                       'reward':{'item1198':3,'item1182':6,},
                                      },
                                    'gd1108':{ # 需要充值的人民币
                                       'name':'surprise_25', # 礼包名字
                                       'num':2, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':37440,
                                       'reward':{'item1199':3,'item1182':6,},
                                      },
                                    
                         },
                         'goods2':{
                                    'gd1110':{ # 需要充值的人民币
                                       'name':'surprise_23', # 礼包名字
                                       'num':1, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':8100,
                                       'reward':{'item1183':3,},
                                      },
                                    'gd1111':{ # 需要充值的人民币
                                       'name':'surprise_24', # 礼包名字
                                       'num':3, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':17100,
                                       'reward':{'item1183':3,'item1182':5,},
                                      },
                                    'gd1112':{ # 需要充值的人民币
                                       'name':'surprise_25', # 礼包名字
                                       'num':3, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':37440,
                                       'reward':{'item1198':3,'item1182':6,},
                                      },
                                    'gd1113':{ # 需要充值的人民币
                                       'name':'surprise_25', # 礼包名字
                                       'num':3, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':37440,
                                       'reward':{'item1199':3,'item1182':6,},
                                      },
                                    
                         },
                         'goods3':{
                                    'gd1115':{ # 需要充值的人民币
                                       'name':'surprise_23', # 礼包名字
                                       'num':1, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':8100,
                                       'reward':{'item1183':3,},
                                      },
                                    'gd1116':{ # 需要充值的人民币
                                       'name':'surprise_24', # 礼包名字
                                       'num':3, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':17100,
                                       'reward':{'item1183':3,'item1182':5,},
                                      },
                                    'gd1117':{ # 需要充值的人民币
                                       'name':'surprise_25', # 礼包名字
                                       'num':3, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':37440,
                                       'reward':{'item1198':3,'item1182':6,},
                                      },
                                    'gd1118':{ # 需要充值的人民币
                                       'name':'surprise_25', # 礼包名字
                                       'num':3, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':37440,
                                       'reward':{'item1199':3,'item1182':6,},
                                      },
                                    'gd1119':{ # 需要充值的人民币
                                       'name':'surprise_27', # 礼包名字
                                       'num':2, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':126600,
                                       'reward':{'item1204':5,},
                                      },
                         },
                         'goods4':{
                                    'gd1120':{ # 需要充值的人民币
                                       'name':'surprise_23', # 礼包名字
                                       'num':1, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':8100,
                                       'reward':{'item1183':3,},
                                      },
                                    'gd1121':{ # 需要充值的人民币
                                       'name':'surprise_25', # 礼包名字
                                       'num':3, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':37440,
                                       'reward':{'item1198':3,'item1182':6,},
                                      },
                                    'gd1122':{ # 需要充值的人民币
                                       'name':'surprise_25', # 礼包名字
                                       'num':3, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':37440,
                                       'reward':{'item1199':3,'item1182':6,},
                                      },
                                    'gd1123':{ # 需要充值的人民币
                                       'name':'surprise_27', # 礼包名字
                                       'num':2, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':126600,
                                       'reward':{'item1204':5,},
                                      },
                                    'gd1124':{ # 需要充值的人民币
                                       'name':'surprise_27', # 礼包名字
                                       'num':2, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':135000,
                                       'reward':{'item1172':3,},
                                      },
                         },
                         'goods5':{
                                    'gd1125':{ # 需要充值的人民币
                                       'name':'surprise_23', # 礼包名字
                                       'num':1, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':8100,
                                       'reward':{'item1183':3,},
                                      },
                                    'gd1126':{ # 需要充值的人民币
                                       'name':'surprise_25', # 礼包名字
                                       'num':3, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':37440,
                                       'reward':{'item1198':3,'item1182':6,},
                                      },
                                    'gd1127':{ # 需要充值的人民币
                                       'name':'surprise_25', # 礼包名字
                                       'num':3, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':37440,
                                       'reward':{'item1199':3,'item1182':6,},
                                      },
                                    'gd1128':{ # 需要充值的人民币
                                       'name':'surprise_27', # 礼包名字
                                       'num':2, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':126600,
                                       'reward':{'item1204':5,},
                                      },
                                    'gd1129':{ # 需要充值的人民币
                                       'name':'surprise_27', # 礼包名字
                                       'num':1, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':133200,
                                       'reward':{'item1205':3,},
                                      },
                         },
                         'goods6':{
                                    'gd1130':{ # 需要充值的人民币
                                       'name':'surprise_23', # 礼包名字
                                       'num':1, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':8100,
                                       'reward':{'item1183':3,},
                                      },
                                    'gd1131':{ # 需要充值的人民币
                                       'name':'surprise_25', # 礼包名字
                                       'num':3, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':37440,
                                       'reward':{'item1198':3,'item1182':6,},
                                      },
                                    'gd1132':{ # 需要充值的人民币
                                       'name':'surprise_25', # 礼包名字
                                       'num':3, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':37440,
                                       'reward':{'item1199':3,'item1182':6,},
                                      },
                                    'gd1133':{ # 需要充值的人民币
                                       'name':'surprise_27', # 礼包名字
                                       'num':2, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':126600,
                                       'reward':{'item1204':5,},
                                      },
                                    'gd1134':{ # 需要充值的人民币
                                       'name':'surprise_27', # 礼包名字
                                       'num':3, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':133200,
                                       'reward':{'item1205':3,},
                                      },
                                    'gd1135':{ # 需要充值的人民币
                                       'name':'surprise_28', # 礼包名字
                                       'num':2, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':200280,
                                       'reward':{'item1207':2,},
                                      },

                         },


             },



############兑换皮肤币#####################
        'pay_skincoin':{
                         'active_time':[[9,00],[18,00]],
                         'hero_show':'skin721_1',
                         '1_999':{
                   #                 'pay301':{ # 需要充值的档位ID
                   #                    'name':'skincoin_01', # 礼包名字
                   #                    'icon':'icon_skinsd1.png', # 可缺省，缺省则罗列reward图标
                   #                    'num':-1, # 可购买次数，-1为不限制
                   #                    'exclusion':['mi'],
                   #                    'reward':{'item1030':5,'item1007':5,},
                   #                   },
                   #                 'pay302':{ # 需要充值的档位ID
                   #                    'name':'skincoin_02', # 礼包名字
                   #                    'icon':'icon_skinsd2.png', # 可缺省，缺省则罗列reward图标
                   #                    'num':5, # 可购买次数
                   #                    'exclusion':['mi'],
                   #                    'reward':{'item1030':10,'item1007':10,},
                   #                   },
                   #                 'pay303':{ # 需要充值的档位ID
                   #                    'name':'skincoin_03', # 礼包名字
                   #                    'icon':'icon_skinsd3.png', # 可缺省，缺省则罗列reward图标
                   #                    'num':5, # 可购买次数
                   #                    'exclusion':['mi'],
                   #                    'reward':{'item1031':10,'item1032':1,'item1007':15,},
                   #                   },
                                    'gd0004':{ # 需要充值的档位ID
                                       'name':'skincoin_04', # 礼包名字
                                       'icon':'icon_skinsd4.png', # 可缺省，缺省则罗列reward图标
                                       'num':-1, # 可购买次数
                                       'exclusion':['mi'],
                                       'reward':{'item1091':1280,},
                                      },
                                    'gd0005':{ # 需要充值的档位ID
                                       'name':'skincoin_05', # 礼包名字
                                       'icon':'icon_skinsd5.png', # 可缺省，缺省则罗列reward图标
                                       'num':-1, # 可购买次数
                                       'exclusion':['mi'],
                                       'reward':{'item1091':3500,},
                                      },
                                    'gd0006':{ # 需要充值的档位ID
                                       'name':'skincoin_06', # 礼包名字
                                       'icon':'icon_skinsd6.png', # 可缺省，缺省则罗列reward图标
                                       'num':-1, # 可购买次数
                                       'exclusion':['mi'],
                                       'reward':{'item1091':7000,},
                                      },
                                      
                         },
             },







##########################玲珑奇士#######################################



'sp_army_box':{
	#对应货币item1103
	'tips':'sp_army_tips',
        'sp_army_date':datetime.datetime(2020,8,6),#版本上线日期
        'mail_name':'sp_army_mail_name',
        'mail_info':'sp_army_mail_info',
	'begin_time':[ 
      {'time':[[4,4],[10,0],[23,0],[23,59]],'goods':'s2'},  #开服天数，开启时间，结束充值时间，结束活动的时间
      {'time':[[20,20],[10,0],[23,0],[23,59]],'goods':'s1'},  #开服天数，开启时间，结束充值时间，结束活动的时间
      {'time':[[48,48],[10,0],[23,0],[23,59]],'goods':'s2'},  #开服天数，开启时间，结束充值时间，结束活动的时间
      {'time':[[76,76],[10,0],[23,0],[23,59]],'goods':'s1'},  #开服天数，开启时间，结束充值时间，结束活动的时间
      {'time':[[104,104],[10,0],[23,0],[23,59]],'goods':'s2'},  #开服天数，开启时间，结束充值时间，结束活动的时间
      {'time':[[132,132],[10,0],[23,0],[23,59]],'goods':'s1'},  #开服天数，开启时间，结束充值时间，结束活动的时间
      {'time':[[160,160],[10,0],[23,0],[23,59]],'goods':'s2'},  #开服天数，开启时间，结束充值时间，结束活动的时间
      {'time':[[188,188],[10,0],[23,0],[23,59]],'goods':'s1'},  #开服天数，开启时间，结束充值时间，结束活动的时间
      {'time':[[216,216],[10,0],[23,0],[23,59]],'goods':'s2'},  #开服天数，开启时间，结束充值时间，结束活动的时间
      {'time':[[244,244],[10,0],[23,0],[23,59]],'goods':'s1'},  #开服天数，开启时间，结束充值时间，结束活动的时间
      {'time':[[272,272],[10,0],[23,0],[23,59]],'goods':'s2'},  #开服天数，开启时间，结束充值时间，结束活动的时间
      {'time':[[300,300],[10,0],[23,0],[23,59]],'goods':'s1'},  #开服天数，开启时间，结束充值时间，结束活动的时间
      {'time':[[328,328],[10,0],[23,0],[23,59]],'goods':'s2'},  #开服天数，开启时间，结束充值时间，结束活动的时间
      {'time':[[356,356],[10,0],[23,0],[23,59]],'goods':'s1'},  #开服天数，开启时间，结束充值时间，结束活动的时间




	   ],
	 'pay_sp_army':{
                                    'gd0108':{ # 需要充值的档位ID
                                       'name':'sp_army_coin_01', # 礼包名字
                                       'icon':'icon_qishi_bxbf.png', # 可缺省，缺省则罗列reward图标
                                       'exclusion':['local'],
                                       'reward_num':320,
                                      },
				    'gd0109':{ # 需要充值的档位ID
                                       'name':'sp_army_coin_02', # 礼包名字
                                       'icon':'icon_qishi_bxbf2.png', # 可缺省，缺省则罗列reward图标
                                       'exclusion':['local'],

                                       'reward_num':1100,
                                      },
									  },

	's1':{ 
		'ani': 'show_s1',
		'image': 'bg_qishi_map2.jpg',
		'name': '502001',
		'info': '502002',
		'buy_one':[1,100,10000],		#免费次数，每次购买消耗的道具数量，每次购买获得的银币数量gold
		'buy_ten':[0,1000,100000],		#免费次数，每次购买消耗的道具数量，每次购买获得的银币数量gold
	        'limit':9999,                 #抽奖限制次数每天偏移值恢复
		'lucky':0,                   #初始幸运值
                  
		'goods':[   #物品，幸运值，权重

#幸运值小于等于99时的奖池 #每个道具ID，幸运值，数量，权重
[99,[['item3050',0,500,19],['item3051',0,500,19],['item3150',0,500,19],['item3151',0,500,19],['item3050',0,200,150],['item3051',0,200,150],['item3150',0,200,150],['item3151',0,200,150],['item3050',0,100,580],['item3051',0,100,580],['item3150',0,100,580],['item3151',0,100,580],['item3050',0,50,4000],['item3051',0,50,4000],['item3150',0,50,4000],['item3151',0,50,4000],['item3050',0,20,10000],['item3051',0,20,10000],['item3150',0,20,10000],['item3151',0,20,10000],['item3050',1,10,35000],['item3051',1,10,35000],['item3150',1,10,35000],['item3151',1,10,35000],['item3050',1,5,49440],['item3051',1,5,49440],['item3150',1,5,49440],['item3151',1,5,49440],]],






#幸运值等于100时的奖池 #每个道具ID，幸运值，权重
[100,[['item3050',-100,500,40],['item3051',-100,500,40],['item3150',-100,500,40],['item3151',-100,500,40],['item3050',-100,200,320],['item3051',-100,200,320],['item3150',-100,200,320],['item3151',-100,200,320],['item3050',-100,100,1200],['item3051',-100,100,1200],['item3150',-100,100,1200],['item3151',-100,100,1200],]],




            
		],


		'show_chance':[
['item3050',0.0001,500],['item3051',0.0001,500],['item3150',0.0001,500],['item3151',0.0001,500],['item3050',0.0008,200],['item3051',0.0008,200],['item3150',0.0008,200],['item3151',0.0008,200],['item3050',0.003,100],['item3051',0.003,100],['item3150',0.003,100],['item3151',0.003,100],['item3050',0.01,50],['item3051',0.01,50],['item3150',0.01,50],['item3151',0.01,50],['item3050',0.025,20],['item3051',0.025,20],['item3150',0.025,20],['item3151',0.025,20],['item3050',0.0875,10],['item3051',0.0875,10],['item3150',0.0875,10],['item3151',0.0875,10],['item3050',0.1236,5],['item3051',0.1236,5],['item3150',0.1236,5],['item3151',0.1236,5],





		], 
 
 'num_reward':{25:{'item1104':1},50:{'item1104':1},75:{'item1104':1},100:{'item1104':1}}, #额外奖励抽取次数，奖励内容，奖励数量


  
   
 'sp_army_info':["sp3050","sp3051","sp3150","sp3151"],
   
},
	's2':{ 
		'ani': 'show_s2',
		'image': 'bg_qishi_map2.jpg',
		'name': '502001',
		'info': '502002',
		'buy_one':[1,100,10000],		#每期活动免费次数，每次购买消耗的道具数量，每次购买获得的银币数量
		'buy_ten':[0,1000,100000],		#每期活动免费次数，每次购买消耗的道具数量，每次购买获得的银币数量
	    'limit':9999,                 #抽奖限制次数每天偏移值恢复
		'lucky':0,                   #初始幸运值
                  
		'goods':[   #物品，幸运值，权重

#幸运值小于等于99时的奖池 #每个道具ID，幸运值，数量，权重
[99,[['item3250',0,500,19],['item3251',0,500,19],['item3350',0,500,19],['item3351',0,500,19],['item3250',0,200,150],['item3251',0,200,150],['item3350',0,200,150],['item3351',0,200,150],['item3250',0,100,580],['item3251',0,100,580],['item3350',0,100,580],['item3351',0,100,580],['item3250',0,50,4000],['item3251',0,50,4000],['item3350',0,50,4000],['item3351',0,50,4000],['item3250',0,20,10000],['item3251',0,20,10000],['item3350',0,20,10000],['item3351',0,20,10000],['item3250',1,10,35000],['item3251',1,10,35000],['item3350',1,10,35000],['item3351',1,10,35000],['item3250',1,5,49440],['item3251',1,5,49440],['item3350',1,5,49440],['item3351',1,5,49440],]],




#幸运值等于100时的奖池 #每个道具ID，幸运值，权重
[100,[['item3250',-100,500,40],['item3251',-100,500,40],['item3350',-100,500,40],['item3351',-100,500,40],['item3250',-100,200,320],['item3251',-100,200,320],['item3350',-100,200,320],['item3351',-100,200,320],['item3250',-100,100,1200],['item3251',-100,100,1200],['item3350',-100,100,1200],['item3351',-100,100,1200],]],




            
		],


		'show_chance':[
['item3250',0.0001,500],['item3251',0.0001,500],['item3350',0.0001,500],['item3351',0.0001,500],['item3250',0.0008,200],['item3251',0.0008,200],['item3350',0.0008,200],['item3351',0.0008,200],['item3250',0.003,100],['item3251',0.003,100],['item3350',0.003,100],['item3351',0.003,100],['item3250',0.01,50],['item3251',0.01,50],['item3350',0.01,50],['item3351',0.01,50],['item3250',0.025,20],['item3251',0.025,20],['item3350',0.025,20],['item3351',0.025,20],['item3250',0.0875,10],['item3251',0.0875,10],['item3350',0.0875,10],['item3351',0.0875,10],['item3250',0.1236,5],['item3251',0.1236,5],['item3350',0.1236,5],['item3351',0.1236,5],




		], 
 

 'num_reward':{25:{'item1105':1},50:{'item1105':1},75:{'item1105':1},100:{'item1105':1}}, #额外奖励抽取次数，奖励内容，奖励数量


  
   
 'sp_army_info':["sp3250","sp3251","sp3350","sp3351"],
   
},
    },  





	'big_shot':{		#紫气东来（通过充值触发）
		'switch':1,			#功能总开关，0表示关闭，1表示开启
		'info':'big_shot_12',			#规则说明
		'show_bgm':'bg_bigshot_ggt.png',		#上方大图
		'show_switch':0,		#控制是否显示公示概率面板的开关，1显示，0隐藏
		'open_ini':3,		#每期活动，默认显示前三天
		'open_days':{'5_999':'reward'},	#开启日期，包含起始和结束天数(一旦天数产生任何变化，则玩家身上刮奖位置数据都会重置)（账号已经获得该日期的情况下，修改日期会导致所有充值报错）
		'act_condition':[		#单笔充值金额区间 和 该区间的触发几率
			[[68,327],0.01],		#单笔充值rmb在6-68之间，触发几率25%
			[[328,997],0.3],
			[[998,1997],0.5],
			[[1998,0],1],		#单笔充值大于998，触发几率100%
		],
		'act_time':120,		#每次触发持续X分钟（后端记结束时间，结束时间内不在重复触发）
		'deduct_bird':1,			#每抽一次扣一分
		'buy_bird':['gd0501',1],			#买分，这个分数是一直记在玩家身上的，永久累计，不会被删除
		'reward':[		#奖励库，抽取时完全随机（去重），每期活动玩家自身记录单独的排序，活动结束后删除排序数据
{'item192':120},{'item084':10},{'item1008':150},{'item099':6},{'item016':40},{'item1044':20},{'item016':40},{'item099':6},{'coin':20000},{'item603':30},{'item483':200},{'item482':200},{'equip':['equip102']},{'item603':30},{'coin':20000},{'coin':10000},{'item606':60},{'awaken':['hero764']},{'awaken':['hero763']},{'awaken':['hero762']},{'item606':60},{'coin':10000},{'item099':6},{'item1044':20},{'item1205':1},{'item1044':20},{'item099':6},{'item1008':150},{'item084':10},{'item192':120},
		],
		'reward_show':[	  #奖励矩阵
			[0,0,1,2,3,0,0],
			[0,4,5,6,7,8,0],
			[9,10,11,12,13,14,15],
			[16,17,18,19,20,21,22],
			[0,23,24,25,26,27,0],
			[0,0,28,29,30,0,0],
		],
		#前端显示权重，总奖励数/100的百分比，
	},
}