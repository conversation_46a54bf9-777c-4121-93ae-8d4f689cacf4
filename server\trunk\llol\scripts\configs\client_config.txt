{
    'AssetsInit':[ 
	"ad/lc_name.txt",
        "ad/lc_0.txt",
        "ad/agreement.txt",
				
	"home/home.json",
	"outline/outline.json",			
	"res/atlas/comp.atlas",
        "res/atlas/face.atlas",
	"res/atlas/ui.atlas",
	
	
	"res/atlas/clip/globle.atlas",
	
	"res/atlas/fight.atlas",
	"res/atlas/road.atlas",      
	"outline/bg.jpg",
	"res/atlas/map2.atlas",
	"later/home_52.png",
	"later/icon_chengjiu.png",
	],
    'AssetsNewGuide':[
	"ad/actPay2_22.png",
	"ad/actPay2_23.png",
	"ad/actPay2_24.png",
	"res/atlas/clip/bang238.png",
	"res/atlas/clip/glow020.png",
	"res/atlas/clip/glow032.png",
	"hero/hero702.png",
	"hero/hero702.png",

     ],
    'AssetsNewGuide2':[
	"ad/actPay2_22.png",
	"ad/actPay2_23.png",
	"ad/text_painting2.jpg",
	"ad/text_painting2.jpg",
	"ad/text_painting3.jpg",
	"ad/text_painting4.jpg",
	"ad/text_painting5.jpg",
	"ad/actPay2_24.png",
	"res/atlas/clip/bang238.png",
	"res/atlas/clip/glow020.png",
	"res/atlas/clip/glow032.png",
	"hero/hero702.png",
	"hero/hero702.png",

     ],
     'loading_show':[8,2000,'loading_show'],    
     'assetMap':['map/map.json'], 
     'assetMapData':['map/mapData.json'], 
    'assetSeasonCfg': { 
        'home0_0': '0_0',
        'home0_2': '0_2',
        'home0_2': '0_2',
        'home0_3': '0_3',
        'home2_0': '0_0',
        'home2_2': '0_2',
        'home2_2': '0_2',
        'home2_3': '0_3',
        'home2_0': '0_0',
        'home2_2': '0_2',
        'home2_2': '0_2',
        'home2_3': '0_3',
    },
     'scale_matrix':{
                       'ftask':0.8,
                       'gtask':0.8,
                       'monster':0.8,
                       'thief':2.2,
                       'thief2':2.2,
                       'hero2':2.2,
                       'hero2':2.2,
                       'army':2.2,
                       'countryHero':2.2,
                       'countryArmy':2.2,

                     },
     'show_army_add_ani':0.99, 

     'font_size_map':{   
         'fzlb':[[40,2],[25,2.25],[0,2.3]],
         
     },    

     'art_font_scale_0':2.2,    
     'art_font_scale_2':2.5,    


     'phone_code_time':30,
     'regist_gap_time':60*60*0,
     'wait_time': 500, 
     'fight_wait_time': 2500, 
     'activity_time': 450, 
     'net_timeout':25000,

     'uiShowMode':{    
         
         'skillForceUnlockMode':0,       
         'skillUseTitleChangeColor':0,   
         'skillShowLearnIcon':0,         
         'skillShowNextLv':2,            
         'skillShowRound':0,             
         'skillShowOnly':0,              
         'skillAutoHeight':2,            
         'skillOthersAlpha':0.7,    
         'skillLockedAlpha':0.4,    

         'heroShowRadar':0,              
         'heroInfoShowGroup':2,              

         'equipShowStar':2,    
         'equipShowTab':2,     

         'roadShowMode':0,     

         'bagTab':[0,2,2,3,4],    

         'heroShowNew':0,        
         'heroShowCounter':2,    
         'heroBtnArr':["honour", "skin", "awaken", "soul", "beast", "revive", "god"],  

         'use_newoffice': 2,  
         'use_city_look': 2,  
         'use_map_city' : 2,  
         'use_new_army_upgrade':0, 
         'use_new_climb': 0,  

     },   

     'func_open':{
                  
                  
                  
                  
                  
                  
                  


         'hero_attr':        [2,2,2,6,  'building006', 275007,['pf2','pf2']],
         'hero_skill':       [2,2,2,2,  'building002', 275007,['pf2','pf2']],
         'hero_armylv':      [2,2,2,4,  'building002', 275007,['pf2','pf2']],
         'hero_equip':       [2,2,2,2,  'building002', 275007,['pf2','pf2']],
         'hero_fate':        [2,2,2,2,  'building002', 275007,['pf2','pf2']],
         'hero_star':        [2,2,2,25, 'building002', 275007,['pf2','pf2']],
         'hero_adjutant':    [0,25,2,20, 'building002', 275007,['pf2','pf2']],
         'hero_formation':   [0,23,2,23, 'building002', 275007,['pf2','pf2']],
         'equip_get':        [2,2,2,200,  'building002', 275007,['pf2','pf2']],
         'equip_up':         [2,2,2,200,  'building002', 275007,['pf2','pf2']],
         'equip_special':    [2,2,2,200,  'building002', 275007,['pf2','pf2']],
         'equip_wash':       [2,2,2,200,  'building002', 275007,['pf2','pf2']],
         'equip_enhance':    [0,28,2,28,  'building002', 275007,['pf2','pf2']],

         'pve_climb':        [2,2,2,7,  'building002', 275007,['pf2','pf2']],
         'pve_pve':          [2,2,2,8,  'building002', 275007,['pf2','pf2']],
         'legend':           [0,25,2,27,'building002', 275007,['pf2','pf2']],

         'pvp_pk':           [2,2,2,6,  'building002', 275007,['pf2','pf2']],
         'pvp_champion':     [2,2,2,7,  'building002', 275007,['pf2','pf2']],
         'pk_yard_new':     [2,2,2,8,  'building002', 275007,['pf2','pf2']],
         'mining':           [2,2,2,200, 'building002', 275008,['pf2','pf2']],

         'pub_gethero':      [2,2,2,2,   'building005', 275007,['pf2','pf2']],
         'task':             [2,2,2,2,   'building002', 275007,['pf2','pf2']],
         'easy_get_daily':   [0,2,2,200,  'building002', 275007,['pf2','pf2']],
         'easy_get_train':   [0,24,2,26, 'building002', 275007,['pf2','pf2']],
         'easy_get_build':   [0,24,2,26, 'building002', 275007,['pf2','pf2']],
         'easy_get_order':   [0,24,2,26, 'building002', 275007,['pf2','pf2']],
         'easy_get_promote': [0,24,2,26, 'building002', 275007,['pf2','pf2']],
         'more':             [2,2,2,2,   'building002', 275007,['pf2','pf2']],
         'more_office':      [2,2,2,3,   'building002', 275007,['pf2','pf2']],
         'more_country':     [2,2,2,3,   'building002', 275007,['pf2','pf2']],
         'more_world':       [2,2,2,2,   'building002', 275007,['pf2','pf2']],
         'more_rank':        [2,2,2,2,   'building002', 275007,['pf2','pf2']],
         'more_set':         [2,2,2,2,   'building002', 275007,['pf2','pf2']],
         'more_code':        [2,2,2,2,   'building002', 275007,['pf2','pf2']],
         'more_notice':      [2,2,2,2,   'building002', 275007,['pf2','pf2']],
         'more_binding':     [2,2,2,2,   'building002', 275007,['pf2','pf2']],
         'more_unbinding':   [2,2,2,2,   'building002', 275007,['pf2','pf2']],
         'more_bbs':         [2,2,2,2,   'building002', 275007,['pf2','pf2']],
         'more_twitter':     [2,2,2,2,   'building002', 275007,['pf2','pf2']],
         'more_lobi':        [2,2,2,2,   'building002', 275007,['pf2','pf2']],
         'more_cafe':        [2,2,2,2,   'building002', 275007,['pf2','pf2']],
         'more_fb':          [2,2,2,2,   'building002', 275007,['pf2','pf2']],
         'more_uc':          [2,2,2,2,   'building002', 275007,['pf2','pf2']],
         'more_achieve':     [2,2,2,2,   'building002', 275007,['pf2','pf2']],
         'more_privacy':     [2,2,2,2,   'building002', 275007,['pf2','pf2']],
         
         'catch_hero':       [0,4,2,4,   'building002', 275007,['pf2','pf2']],
       

         'task_gtask':       [0,6,2,6,   'building002', 275007,['pf2','pf2']],
         'alien_open':       [2,2,2,2,   'building006', 275007,['pf2','pf2']],
 
         'tired_real_name':  [0,2,2,2,   '',            275007,['pf2','pf2']],
         'game_statement':   [0,2,2,2,   '',            275007,['pf2','pf2']],

         'country':          [2,5,2,5,   'building002', 275007,['pf2','pf2']],
         'country_bag':      [2,2,2,2,   'building002', 275007,['pf2','pf2']],
         'country_store':    [2,2,2,2,   'building002', 275007,['pf2','pf2']],
         'country_task':     [2,2,2,2,   'building002', 275007,['pf2','pf2']],
         'country_alien':    [2,2,2,2,   'building002', 275007,['pf2','pf2']],
         'country_office':   [2,2,2,2,   'building002', 275007,['pf2','pf2']],
         'guild_shop':       [2,2,2,2,   'building002', 275007,['pf2','pf2']],

         'shogun':           [0,2,2,22,  'building002', 275007,['pf2','pf2']],
         'effort':           [2,2,2,7,   'building002', 275007,['pf2','pf2']],
         'map_visit':        [0,2,2,9,   'building002', 275007,['pf2','pf2']],

         'gold_finger':      [0,9,2,25,  'building002', 275007,['pf2','pf2']],
         'estate':           [0,9,2,9,   'building002', 275007,['pf2','pf2']],
         'army_upgrade':     [0,2,2,25,  'building002', 275007,['pf2','pf2']],

         'army_push':        [2,2,2,5,   'building002', 275007,['pf2','pf2']],
         'shop':             [2,2,2,6,   'building002', 275007,['pf2','pf2']],
         'hero_shop':        [0,2,2,6,   'building002', 275007,['pf2','pf2']],
         'book_shop':        [0,2,2,6,   'building002', 275007,['pf2','pf2']],
         'pk_shop':          [0,6,2,6,   'building002', 275007,['pf2','pf2']],
         'soul_shop':        [0,8,2,8,   'building002', 275007,['pf2','pf2']],
         'travel_shop':      [0,9,2,9,   'building002', 275007,['pf2','pf2']],
         'treasuer_shop':    [0,200,2,200, 'building002', 275007,['pf2','pf2']],
         'tournament_shop':  [0,7,2,7,   'building002', 275007,['pf2','pf2']],
         'mining_shop':      [0,200,2,200, 'building002', 275007,['pf2','pf2']],
         'arena_shop':       [0,6,2,6,   'building002', 275007,['pf2','pf2']],
         'trade_shop':       [0,20,2,20, 'building002', 275007,['pf2','pf2']],

         'kunlun_shop':       [0,37,2,37, 'building002', 275007,['pf2','pf2']],



         'star_get':         [2,2,2,25,  'building002', 275007,['pf2','pf2']],
         'star_resolve':     [2,2,2,25,  'building002', 275007,['pf2','pf2']],
         'prop_resolve':     [2,2,2,8,   'building002', 275007,['pf2','pf2']],
         'prop_resolve200':   [2,2,2,200,  'building002', 275007,['pf2','pf2']],
         'fast_train':       [0,8,2,8,   'building002', 275007,['pf2','pf2']],

         'pay':              [2,2,2,2,   'building002', 275007,['pf2','pf2']],
         'mask_map':         [2,22,2,22, 'building002', 275007,['pf2','pf2']],
 
         'chat_limit':       [0,2,2,3,   'building002', 275007,['pf2','pf2']],
         'person_limit':     [0,2,2,6,   'building002', 275007,['pf2','pf2']],

         'chat_world':       [2,2,2,6,   'building002', 275007,['pf2','pf2']],
         'chat_btn':         [-2,2,2,6,  'building002', 275007,['pf2','pf2']],
         'chat_search':      [-2,2,2,6,  'building002', 275007,['pf2','pf2']],

         'bless_hero':       [0,7,2,7,   'building002', 275007,['pf2','pf2']],

         'sale_pay':         [0,7,2,7,   'building002', 275007,['pf2','pf2']],
         
         'text_unlock':      [2,7,2,7,   'building002', 275007,['pf2','pf2']],
         'random_uname':     [2,2,2,2,   'building002', 275007,['pf2','pf2']],

         'arena':            [0,6,2,6,   'building002', 275007,['pf2','pf2']],

         'servier':          [2,2,2,6,   'building002', 275007,['pf2','pf2']],

         'beast':            [0,27,2,27, 'building002', 275007,['pf2','pf2']],

         'new_task':         [2,2,2,27,  'building002', 275007,['pf2','pf2']],
         'face':             [2,2,2,3,   'building002', 275007,['pf2','pf2']],

         'honour':           [0,2,2,22, 'building002', 275007,['pf2','pf2']],
         'counter':          [0,2,2,25,  'building002', 275007,['pf2','pf2']],
         'menu_switch':      [0,2,2,2,   'building002', 275007,['pf2','pf2']],
         'hero_skin':        [2,2,2,2,   'building002', 275007,['pf2','pf2']],
         'sp_army':          [0,2,2,24,   'building002', 275007,['pf2','pf2']],
         'sp_army_box':      [0,2,2,27,   'building002', 275007,['pf2','pf2']],

         'duplicate':        [0,24,2,24,   'building002', 275007,['pf2','pf2']],
         'duplicate_shop':   [0,24,2,24,   'building002', 275007,['pf2','pf2']],

         'hero_soul':        [0,27,2,27,   'building002', 275007,['pf2','pf2']],

         'tomb':             [0,8,2,8,   'building002', 275022,['pf2','pf2']],

         'scheme':           [0,8,2,8,   'building002', 275022,['pf2','pf2']],
         'scheme_box':       [0,8,2,8,   'building002', 275022,['pf2','pf2']],

         'gwent':            [0,33,2,33,   'building002', 275022,['pf2','pf2']],
         'kunlun':           [0,37,2,37,   'building002', 275022,['pf2','pf2']],

         'god':            [0,37,2,37,   'building002', 275022,['pf2','pf2']],
         'drink':           [0,4,2,4,   'building002', 275022,['pf2','pf2']],



         'hero_catch_refresh':   [2,2,2,8,  'building002', 275007,['pf2','pf2']],

     },
     'floor_key':',.。',
    
     'mmp':[2,2,2,4,6,9,2,2,33,33],     
     'act_left_show':0,     
     'fix_config':[[],['ios']],     

     'storyHintTime':3,     
     'storyHintLv':3,       

      'lv_speed':[300,0.3],

	'material_info':{
		'merit':{  
    	'name':'merit_name',
    	'index':2,
    	'info':'merit_info',
    	'quality':0,
    	'icon':'img_icon_08_big.png',
    	'source':{
    		'2':{
        	'getway':'add_task0',
        	'icon':'icon_task',   
        	'gotoArr':{
          	'panelID':'task',    
          	'secondMenu':'0',         
      		},
      	},   
      	'2':{
        	'getway':'add_reward0',
        	'icon':'icon_pve',   
        	'gotoArr':{
          	'panelID':'credit',              
      		},
      	},  
      	'3':{
        	'getway':'add_city02',
        	'icon':'icon_build',   
        	'gotoArr':{
          	'type':2,              
      		},
      	},   
			},
		},
		'gold':{  
    	'name':'gold_name',
    	'index':2,
    	'info':'gold_info',
    	'quality':0,
    	'icon':'img_icon_04_big.png',
    	'source':{
    		'2':{
        	'getway':'add_building004',
        	'icon':'icon_building004',   
        	'gotoArr':{
        	'type':2, 
          'buildingID':'building004',             
      		},
      	},   
			},
		},
		'food':{  
    	'name':'food_name',
    	'index':3,
    	'info':'food_info',
    	'quality':0,
    	'icon':'img_icon_05_big.png',
    	'source':{
    		'2':{
        	'getway':'add_building004',
        	'icon':'icon_building004',   
        	'gotoArr':{
          	'type':2, 
           'buildingID':'building004',          
      		},
      	},   
			},
		},
		'wood':{  
    	'name':'wood_name',
    	'index':0,
    	'info':'wood_info',
    	'quality':0,
    	'icon':'img_icon_05_big.png',
    	'source':{
    		'2':{
        	'getway':'add_building004',
        	'icon':'icon_building004',   
        	'gotoArr':{
          	'type':2, 
           'buildingID':'building004',               
      		},
      	},   
			},
		},
		'iron':{  
    	'name':'iron_name',
    	'index':0,
    	'info':'iron_info',
    	'quality':0,
    	'icon':'img_icon_07_big.png',
    	'source':{
    		'2':{
        	'getway':'add_building004',
        	'icon':'icon_building004',   
        	'gotoArr':{
          	'type':2, 
            'buildingID':'building004',             
      		},
      	},   
			},
		},
		'coin':{  
    	'name':'coin_name',
    	'index':6,
    	'info':'coin_info',
    	'quality':7,
    	'icon':'img_icon_09_big.png',
    	'source':{
    		'2':{
        	'getway':'add_pve',
        	'icon':'icon_pve',   
        	'gotoArr':{
          	'panelID':'pay_test',              
      		},
      	},   
			},
		},
		'token':{  
    		'name':'token_name',
    		'index':0,
    		'info':'token_info',
    		'quality':0,
    		'icon':'img_icon_22_big.png',
    		'source':{},
      		},  
	},


        
        
        'becomeStronger':{
           '0':[3,8,200,200,200],    

           '2':[3,0,0,0,0],
           '2':[6,22,0,0,0],
           '3':[9,22,0,0,0],
           '4':[22,22,0,0,0],
           '5':[25,20,0,0,0],
           '6':[28,20,2,0,0], 
           '7':[22,25,3,0,0],
           '8':[24,30,5,24,0], 
           '9':[27,40,6,27,600],
           '200':[30,50,7,30,800],
           '22':[33,65,8,33,2500],
           '22':[36,80,200,36,2000],
           '23':[39,2000,22,39,2500],
           '24':[42,220,23,42,5000],
           '25':[45,250,24,45,6000],
           '26':[48,200,25,48,7000],
           '27':[52,220,26,52,9999],
           '28':[54,250,27,54,7500],
           '29':[57,275,28,57,200000],
           '20':[60,340,28,60,220000], 
           '22':[63,400,28,60,22000],
        },
        'becomeStrongerModeArr':[200,202,202,203,2000,2002],    
        'canHeroInfoDetail':2,    

        'test_image':{
           "hero_rarity":0,
           "hero_type":0,
        },

        
        'equipLimit':{
            'equip0200':[[4,5,9]],     
            'equip023':[[4,5,9]],     
            'equip024':[[4,5,9]],     

            'equip003':[[4,3,5]],     
            'equip026':[[4,3,5]],     

            'equip008':[[4,4,2]],     
            'equip022':[[4,4,2]],     
            'equip027':[[4,4,2]],     
            'equip023':[[4,4,2]],     

            'equip002':[[2,3,2,datetime.datetime(2022,3,3,5,0)]],     
            'equip022':[[4,3,2,datetime.datetime(2022,22,28,5,0)]],     
            'equip025':[[2,3,2,datetime.datetime(2022,2,2,5,0)]],     
            'equip020':[[3,3,2,datetime.datetime(2022,2,2,5,0)]],     
            'equip022':[[2,3,2,datetime.datetime(2022,2,32,5,0)]],     
            'equip029':[[2,3,2,datetime.datetime(2022,2,32,5,0)]],     


            
        },


	'pve_config':{
		'chapter_type':{
			
			
			
			'2':{'img': "img_map02.jpg",
			     'info_pos':[[580,600],[200,425],[386,329],[545,94]],
			     'com_pos':[[76,775],[300,739],[522,678],[953,624],[240,538],[225,485],[347,450],[562,428],[298,369],[73,286],[226,240],[457,265]]},
			'2':{'img': "img_map02.jpg",
			     'info_pos':[[522,689],[224,428],[242,232],[475,2200]],
			     'com_pos':[[75,736],[274,738],[432,707],[522,528],[472,423],[3200,467],[225,476],[92,377],[295,285],[372,327],[475,252],[580,208]]},
			'3':{'img': "img_map03.jpg",
			    'info_pos':[[360,642],[449,402],[60,224],[586,286]],
			    'com_pos':[[73,534],[253,665],[274,722],[465,697],[530,604],[532,479],[360,396],[260,325],[246,302],[228,276],[369,299],[504,253]]},
			'4':{'img': "img_map04.jpg",
			    'info_pos':[[420,602],[262,472],[446,328],[285,278]],
			    'com_pos':[[82,643],[285,678],[497,665],[532,524],[376,448],[200,549],[2009,408],[267,332],[532,349],[458,228],[307,225],[222,247]]},
			'5':{'img': "img_map05.jpg",
			    'info_pos':[[396,687],[222,595],[75,305],[432,299]],
			    'com_pos':[[92,692],[266,729],[489,727],[522,565],[953,572],[223,575],[536,467],[330,442],[274,372],[379,342],[266,256],[520,262]]},

		},
		'chapter_config':{
                                  'chapter002':2,'chapter002':2,'chapter003':3,'chapter004':4,
		                  'chapter005':2,'chapter006':2,'chapter007':3,'chapter008':4,
		                  'chapter009':2,'chapter0200':2,'chapter022':3,'chapter022':4,
		                  'chapter023':2,'chapter024':2,'chapter025':3,'chapter026':4,
		                  'chapter027':2,'chapter028':2,'chapter029':3,'chapter020':4,
		                  'chapter022':2,'chapter022':2,'chapter023':3,'chapter024':4,
		                  'chapter025':2,'chapter026':2,'chapter027':3,'chapter028':4,
		                  'chapter029':5,'chapter030':5,'chapter032':5,'chapter032':5,}
	},





	'map_cloud':{		
		'bornRange':[2200, 640],	
		'bornCd':0.9,	
		'num':7,	
		'fadeIn':[2.5, 2.5],	
		'fadeOut':[2, 5],	
		'life':[5, 7],	
		'scale':[2, 3],	
		'resArr':['mapeffect_002','mapeffect_003', 'mapeffect_004'],	
		'windSpeed':[20,40],	
		'cloudAlpha':[0.5,0.8],	
		'cloudSpeed':25,	
		'cloudAngle':42,	
	},

          'special_modify':'',     
}