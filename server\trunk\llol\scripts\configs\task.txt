{
       'explain_show_hero':'hero702',                
       'task_show_hero':'hero747',                   
       'one_get_unlock':200,                          
       'show_rule':['main','gtask','common'],  
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 














    'daily':{                          
    'task002':{       
         'type':'ts_watch',  
         'name':'task002_name',
         'info':'task002_info',
         'index':'0_2',
         'unlock_level':25,
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building006',
                              'state':2,
                             },

         'need':[3],
         'reward':{'coin':30,'item029':2,},
        },
    'task002':{       
         'type':'ts_both',  
         'name':'task002_name',
         'info':'task002_info',
         'index':'0_2',
         'unlock_level':6,
         'goto_cfg':{
                              
                              'panelID':'pk',
                             },

         'need':[3],
         'reward':{'coin':20,'gold':5000,},
        },
    'task003':{       
         'type':'ts_sand',  
         'name':'task003_name',
         'info':'task003_info',
         'index':'0_3',
         'unlock_level':8,
         'goto_cfg':{
                              
                              'panelID':'pve',
                             },

         'need':[200,'all','all','all'],
         'reward':{'item022':2,'gold':5000,},
        },
    'task004':{       
         'type':'ts_cost',  
         'name':'task004_name',
         'info':'task004_info',
         'index':'0_4',
         'unlock_level':2,
         'goto_cfg':{
                              'type':2,
                             },

         'need':[2000],
         'reward':{'item072':2,'item037':5,},
        },
    'task005':{       
         'type':'ts_kill',  
         'name':'task005_name',
         'info':'task005_info',
         'index':'0_5',
         'unlock_level':2,
         'goto_cfg':{
                              'type':2,
                             },

         'need':[2000],
         'reward':{'item406':20,'item402':3,},
        },
    'task006':{        
         'type':'ts_kill',  
         'name':'task006_name',
         'info':'task006_info',
         'index':'0_6',
         'unlock_level':2,
         'goto_cfg':{
                              'type':2,
                             },

         'need':[5000],
         'reward':{'item408':20,'item403':3,},
        },
    'task007':{        
         'type':'ts_kill',  
         'name':'task007_name',
         'info':'task007_info',
         'index':'0_7',
         'unlock_level':2,
         'goto_cfg':{
                              'type':2,
                             },

         'need':[200000],
         'reward':{'item409':20,'item402':3,},
        },
    'task008':{        
         'type':'ts_kill',  
         'name':'task008_name',
         'info':'task008_info',
         'index':'0_8',
         'unlock_level':2,
         'goto_cfg':{
                              'type':2,
                             },

         'need':[20000],
         'reward':{'item407':20,'item404':3,},
        },
    'task009':{         
         'type':'ts_cutwill',  
         'name':'task009_name',
         'info':'task009_info',
         'index':'0_9',
         'unlock_level':7,
         'goto_cfg':{
                              
                              'panelID':'VIEW_CLIMB_MAIN',
                             },

         'need':[2],
         'reward':{'item037':5,'item020':6,},
        },
    'task0200':{       
         'type':'ts_gtask',  
         'name':'task0200_name',
         'info':'task0200_info',
         'index':'0_200',
         'unlock_level':6,
         'goto_cfg':{
                              
                              'panelID':'task',
                              'secondMenu':'0',
                             },

         'need':[5],
         'reward':{'merit':300,'coin':50,},
        },
    'task022':{        
         'type':'ts_build',  
         'name':'task022_name',
         'info':'task022_info',
         'index':'0_22',
         'unlock_level':3,
         'goto_cfg':{
                              'type':2,
                             },

         'need':[2,'all','all'],
         'reward':{'merit':200,'item059':5,},
        },
    'task022':{        
         'type':'ts_travel_buy',  
         'name':'task022_name',
         'info':'task022_info',
         'index':'0_22',
         'unlock_level':9,
         'goto_cfg':{
                              
                              'panelID':'shop',
                              'secondMenu':'travel_shop',
                             },

         'need':[2],
         'reward':{'item069':2,'item022':2,},
        },
    'task023':{       
         'type':'ts_asked',  
         'name':'task023_name',
         'info':'task023_info',
         'index':'0_23',
         'unlock_level':8,
         'goto_cfg':{
                              'type':2,
                              'panelID':'prop_resolve',
                             },

         'need':[3],
         'reward':{'item073':2,'item075':2,},
        },
    'task024':{       
         'type':'ts_pub',  
         'name':'task024_name',
         'info':'task024_info',
         'index':'0_24',
         'unlock_level':2,
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building005',
                              'panelID':'pub',
                             },

         'need':[2,'all'],
         'reward':{'item070':2,'item023':3,},
        },
    'task025':{        
         'type':'ts_trench',  
         'name':'task025_name',
         'info':'task025_info',
         'index':'0_25',
         'unlock_level':5,
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building004',
                             },

         'need':[3],
         'reward':{'item072':2,'item032':2,},
        },
    'task026':{       
         'type':'ts_booksell',  
         'name':'task026_name',
         'info':'task026_info',
         'index':'0_26',
         'unlock_level':6,
         'goto_cfg':{
                              
                              'panelID':'shop',
                              'secondMenu':'book_shop',
                             },

         'need':[2],
         'reward':{'item022':2,'item037':200,},
        },
    'task027':{        
         'type':'ts_peoplesell',  
         'name':'task027_name',
         'info':'task027_info',
         'index':'0_27',
         'unlock_level':6,
         'goto_cfg':{
                              
                              'panelID':'shop',
                              'secondMenu':'hero_shop',
                             },

         'need':[2],
         'reward':{'item074':2,'gold':5000,},
        },
    'task028':{       
         'type':'train_soldier',  
         'name':'task028_name',
         'info':'task028_info',
         'index':'0_28',
         'unlock_level':2,
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building022',
                             },

         'need':[3000,3],
         'reward':{'coin':20,'gold':5000,},
        },
    'task029':{       
         'type':'train_soldier',  
         'name':'task029_name',
         'info':'task029_info',
         'index':'0_29',
         'unlock_level':2,
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building022',
                             },

         'need':[3000,2],
         'reward':{'coin':20,'wood':200000,},
        },
    'task020':{       
         'type':'train_soldier',  
         'name':'task020_name',
         'info':'task020_info',
         'index':'0_20',
         'unlock_level':2,
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building0200',
                             },

         'need':[3000,2],
         'reward':{'coin':20,'food':200000,},
        },
    'task022':{       
         'type':'train_soldier',  
         'name':'task022_name',
         'info':'task022_info',
         'index':'0_22',
         'unlock_level':2,
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building009',
                             },

         'need':[3000,0],
         'reward':{'coin':20,'iron':200000,},
        },
    'task022':{       
         'type':'ts_treasuer',  
         'name':'task022_name',
         'info':'task022_info',
         'index':'0_0',
         'unlock_level':28,
         'goto_cfg':{
                              
                              'panelID':'shop',
                              'secondMenu':'treasuer_shop',
                             },

         'need':[2],
         'reward':{'item005':2,'food':200000,},
        },
    'task023':{       
         'type':'ts_cost',  
         'name':'task023_name',
         'info':'task023_info',
         'index':'0_2',
         'unlock_level':22,
         'switch':4,
         'goto_cfg':{
                              'type':2,
                             },

         'need':[300],
         'reward':{'item046':2,'food':200000,},
        },

},
    'main':{                          
    'main_2':{     
         'type':'have_hero',  
         'info':'main_2_info',
         'index':'2_0',
         'goto_cfg':{
                              'type':3,
                              'panelID':'hero',
                             },

         'need':[2,'all'],
         'reward':{'gold':200000,},
        },
    'main_2':{     
         'type':'have_force',  
         'info':'main_2_info',
         'index':'2_2',
         'goto_cfg':{
                              'type':2,
                             },

         'need':[2],
         'reward':{'item037':5,},
        },
    'main_3':{     
         'type':'build_up',  
         'info':'main_3_info',
         'index':'2_2',
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building002',
                             },

         'need':[2,2,['building002']],
         'reward':{'gold':200000,'food':20000,},
        },
    'main_4':{     
         'type':'build_up',  
         'info':'main_4_info',
         'index':'2_3',
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building009',
                             },

         'need':[2,2,['building009'],],
         'reward':{'iron':20000,'wood':20000,},
        },
    'main_5':{     
         'type':'train_soldier',  
         'info':'main_5_info',
         'index':'2_4',
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building009',
                             },

         'need':[300,0],
         'reward':{'item002':3,'item026':5,},
        },
    'main_6':{     
         'type':'build_up',  
         'info':'main_6_info',
         'index':'2_5',
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building0200',
                             },

         'need':[2,2,['building0200'],],
         'reward':{'iron':20000,'wood':20000,},
        },
    'main_7':{     
         'type':'train_soldier',  
         'info':'main_7_info',
         'index':'2_6',
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building0200',
                             },

         'need':[300,2],
         'reward':{'item002':3,'item026':5,},
        },
    'main_8':{     
         'type':'ts_pub',  
         'info':'main_8_info',
         'index':'2_7',
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building005',
                             },

         'need':[2,'hero_box2'],
         'reward':{'coin':50,'gold':200000,},
        },
    'main_9':{     
         'type':'fskill_lv',  
         'info':'main_9_info',
         'index':'2_8',
         'goto_cfg':{
                              'type':3,
                              'panelID':'hero',
                             },

         'need':[2,4,'skill225'],
         'reward':{'gold':200000,'item702':5,},
        },
    'main_200':{     
         'type':'com_people',  
         'info':'main_200_info',
         'index':'2_9',
         'goto_cfg':{
                              'type':2,
                             },

         'need':[2,['244','62','375']],
         'reward':{'food':20000,'item207':200,},
        },
    'main_22':{     
         'type':'build_up',  
         'info':'main_22_info',
         'index':'2_200',
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building002',
                             },

         'need':[2,3,['building002']],
         'reward':{'iron':25000,'gold':200000,},
        },
    'main_22':{     
         'type':'get_office',  
         'info':'main_22_info',
         'index':'2_22',
         'goto_cfg':{
                              'type':3,
                              'panelID':'office',
                             },

         'need':[2,2],
         'reward':{'food':200000,'merit':500,},
        },
    'main_23':{     
         'type':'build_up',  
         'info':'main_23_info',
         'index':'2_22',
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building026',
                             },

         'need':[2,2,['building026','building027','building028']],
         'reward':{'iron':50000,},
        },
    'main_24':{     
         'type':'build_up',  
         'info':'main_24_info',
         'index':'2_23',
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building023',
                             },

         'need':[2,2,['building023','building024','building025']],
         'reward':{'gold':25000,},
        },
    'main_25':{     
         'type':'build_up',  
         'info':'main_25_info',
         'index':'2_24',
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building022',
                             },

         'need':[2,2,['building022','building023','building024']],
         'reward':{'food':50000,},
        },
    'main_26':{     
         'type':'build_up',  
         'info':'main_26_info',
         'index':'2_25',
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building029',
                             },

         'need':[2,2,['building029','building020','building022']],
         'reward':{'wood':50000,},
        },
    'main_27':{     
         'type':'open_privilege',  
         'info':'main_27_info',
         'index':'2_26',
         'goto_cfg':{
                              'type':3,
                              'panelID':'office',
                             },

         'need':[2],
         'reward':{'coin':50,'item702':5,},
        },
    'main_28':{     
         'type':'build_up',  
         'info':'main_28_info',
         'index':'2_27',
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building026',
                             },

         'need':[2,3,['building026','building027','building028']],
         'reward':{'iron':20000,},
        },
    'main_29':{     
         'type':'build_up',  
         'info':'main_29_info',
         'index':'2_28',
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building023',
                             },

         'need':[2,3,['building023','building024','building025']],
         'reward':{'gold':20000,},
        },
    'main_20':{     
         'type':'build_up',  
         'info':'main_20_info',
         'index':'2_29',
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building022',
                             },

         'need':[2,3,['building022','building023','building024']],
         'reward':{'food':20000,},
        },
    'main_22':{     
         'type':'build_up',  
         'info':'main_22_info',
         'index':'2_20',
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building029',
                             },

         'need':[2,3,['building029','building020','building022']],
         'reward':{'wood':20000,},
        },
    'main_22':{     
         'type':'build_up',  
         'info':'main_22_info',
         'index':'2_22',
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building002',
                             },

         'need':[2,4,['building002']],
         'reward':{'food':200000,'iron':30000,},
        },
    'main_23':{     
         'type':'have_force',  
         'info':'main_23_info',
         'index':'2_22',
         'goto_cfg':{
                              'type':2,
                             },

         'need':[3],
         'reward':{'food':200000,'item002':5,},
        },
    'main_24':{     
         'type':'hero_lv',  
         'info':'main_24_info',
         'index':'2_23',
         'goto_cfg':{
                              'type':3,
                              'panelID':'hero',
                             },

         'need':[2,200],
         'reward':{'gold':200000,'wood':40000,},
        },
    'main_25':{     
         'type':'com_people',  
         'info':'main_25_info',
         'index':'2_24',
         'goto_cfg':{
                              'type':2,
                             },

         'need':[2,['248','66','950']],
         'reward':{'food':200000,'item222':5,},
        },
    'main_26':{     
         'type':'learn_fskill',  
         'info':'main_26_info',
         'index':'2_25',
         'goto_cfg':{
                              'type':3,
                              'panelID':'hero',
                             },

         'need':[2,'skill222'],
         'reward':{'food':200000,'item209':5,},
        },
    'main_27':{     
         'type':'build_up',  
         'info':'main_27_info',
         'index':'2_26',
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building026',
                             },

         'need':[2,4,['building026','building027','building028']],
         'reward':{'iron':50000,},
        },
    'main_28':{     
         'type':'build_up',  
         'info':'main_28_info',
         'index':'2_27',
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building023',
                             },

         'need':[2,4,['building023','building024','building025']],
         'reward':{'gold':25000,},
        },
    'main_29':{      
         'type':'build_up',  
         'info':'main_29_info',
         'index':'2_28',
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building022',
                             },

         'need':[2,4,['building022','building023','building024']],
         'reward':{'food':50000,},
        },
    'main_30':{     
         'type':'build_up',  
         'info':'main_30_info',
         'index':'2_29',
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building029',
                             },

         'need':[2,4,['building029','building020','building022']],
         'reward':{'wood':50000,},
        },
    'main_32':{     
         'type':'build_up',  
         'info':'main_32_info',
         'index':'2_30',
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building002',
                             },

         'need':[2,5,['building002']],
         'reward':{'food':200000,'item032':200,},
        },
    'main_32':{     
         'type':'ts_pub',  
         'info':'main_32_info',
         'index':'2_32',
         'goto_cfg':{
                              'type':3,
                              'panelID':'pub',
                             },

         'need':[2,'hero_box2'],
         'reward':{'food':20000,'item002':200,'iron':30000,},
        },
    'main_33':{     
         'type':'hero_lv',  
         'info':'main_33_info',
         'index':'2_32',
         'goto_cfg':{
                              'type':3,
                              'panelID':'hero',
                             },

         'need':[2,25],
         'reward':{'item702':200,'wood':20000,'gold':200000,},
        },
    'main_34':{     
         'type':'com_people',  
         'info':'main_34_info',
         'index':'2_33',
         'goto_cfg':{
                              'type':2,
                             },

         'need':[2,['260','33','348']],
         'reward':{'food':20000,'item207':20,},
        },
    'main_95':{     
         'type':'fskill_lv',  
         'info':'main_95_info',
         'index':'2_34',
         'goto_cfg':{
                              'type':3,
                              'panelID':'hero',
                             },

         'need':[2,5,'skill207'],
         'reward':{'gold':200000,'item702':5,},
        },
    'main_36':{     
         'type':'build_up',  
         'info':'main_36_info',
         'index':'2_95',
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building026',
                             },

         'need':[2,5,['building026','building027','building028']],
         'reward':{'iron':60000,},
        },
    'main_37':{     
         'type':'build_up',  
         'info':'main_37_info',
         'index':'2_36',
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building023',
                             },

         'need':[2,5,['building023','building024','building025']],
         'reward':{'gold':30000,},
        },
    'main_38':{     
         'type':'build_up',  
         'info':'main_38_info',
         'index':'2_37',
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building022',
                             },

         'need':[2,5,['building022','building023','building024']],
         'reward':{'food':60000,},
        },
    'main_39':{     
         'type':'build_up',  
         'info':'main_39_info',
         'index':'2_38',
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building029',
                             },

         'need':[2,5,['building029','building020','building022']],
         'reward':{'wood':60000,},
        },
    'main_40':{     
         'type':'build_up',  
         'info':'main_40_info',
         'index':'2_39',
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building009',
                             },

         'need':[2,5,['building009','building0200','building022','building022']],
         'reward':{'food':20000,'gold':200000,'item027':3,},
        },
    'main_42':{     
         'type':'build_up',  
         'info':'main_42_info',
         'index':'2_40',
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building002',
                             },

         'need':[2,6,['building002']],
         'reward':{'food':20000,'iron':30000,},
        },
    'main_42':{     
         'type':'cum_gtask',  
         'info':'main_42_info',
         'index':'2_42',
         'goto_cfg':{
                              'type':3,
                              'panelID':'task',
                             },

         'need':[2],
         'reward':{'food':20000,'gold':200000,'item002':5,},
        },
    'main_43':{     
         'type':'cum_both',  
         'info':'main_43_info',
         'index':'2_42',
         'goto_cfg':{
                              'type':3,
                              'panelID':'pk',
                             },

         'need':[2],
         'reward':{'coin':20,'item002':5,},
        },
    'main_44':{     
         'type':'hero_lv',  
         'info':'main_44_info',
         'index':'2_43',
         'goto_cfg':{
                              'type':3,
                              'panelID':'hero',
                             },

         'need':[2,28],
         'reward':{'item702':5,'item207':200,'item202':200,},
        },
    'main_45':{     
         'type':'com_people',  
         'info':'main_45_info',
         'index':'2_44',
         'goto_cfg':{
                              'type':2,
                             },

         'need':[2,['242','70','340']],
         'reward':{'coin':50,'food':20000,'gold':200000,},
        },
    'main_46':{     
         'type':'learn_skill',  
         'info':'main_46_info',
         'index':'2_45',
         'goto_cfg':{
                              'type':3,
                              'panelID':'hero',
                             },

         'need':[2,5],
         'reward':{'item037':5,},
        },
    'main_47':{     
         'type':'build_up',  
         'info':'main_47_info',
         'index':'2_46',
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building009',
                             },

         'need':[2,7,['building009','building0200','building022','building022']],
         'reward':{'food':200000,'gold':200000,},
        },
    'main_48':{     
         'type':'com_people',  
         'info':'main_48_info',
         'index':'2_47',
         'goto_cfg':{
                              'type':2,
                             },

         'need':[2,['234','26','364']],
         'reward':{'coin':50,'food':20000,'gold':200000,},
        },
    'main_49':{     
         'type':'cum_both',  
         'info':'main_49_info',
         'index':'2_48',
         'goto_cfg':{
                              'type':3,
                              'panelID':'pk',
                             },

         'need':[6],
         'reward':{'coin':80,},
        },
    'main_50':{     
         'type':'cum_catch',  
         'info':'main_50_info',
         'index':'2_49',
         'goto_cfg':{
                              'type':2,
                             },

         'need':[3],
         'reward':{'food':20000,'gold':200000,},
        },
    'main_52':{     
         'type':'train_soldier',  
         'info':'main_52_info',
         'index':'2_50',
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building009',
                             },

         'need':[20000,0],
         'reward':{'food':20000,'gold':200000,},
        },
    'main_52':{     
         'type':'get_office',  
         'info':'main_52_info',
         'index':'2_52',
         'goto_cfg':{
                              'type':3,
                              'panelID':'office',
                             },

         'need':[2,2],
         'reward':{'coin':2000,'item702':200,},
        },
    'main_53':{     
         'type':'build_up',  
         'info':'main_53_info',
         'index':'2_52',
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building002',
                             },

         'need':[2,7,['building002']],
         'reward':{'food':20000,'iron':30000,},
        },
    'main_54':{     
         'type':'hero_lv',  
         'info':'main_54_info',
         'index':'2_53',
         'goto_cfg':{
                              'type':3,
                              'panelID':'hero',
                             },

         'need':[3,20],
         'reward':{'item037':5,'item702':5,},
        },
    'main_75':{     
         'type':'com_people',  
         'info':'main_75_info',
         'index':'2_54',
         'goto_cfg':{
                              'type':2,
                             },

         'need':[2,['257','23','338']],
         'reward':{'coin':50,'food':20000,'gold':200000,},
        },
    'main_56':{     
         'type':'ts_build',  
         'info':'main_56_info',
         'index':'2_75',
         'goto_cfg':{
                              'type':2,
                              'state':2,
                              'secondMenu':3,
                             },

         'need':[2,[['257'],['23'],['338']],['b03','b03','b03']],
         'reward':{'wood':99975,'iron':40000,},
        },
    'main_57':{     
         'type':'cum_gtask',  
         'info':'main_57_info',
         'index':'2_56',
         'goto_cfg':{
                              'type':3,
                              'panelID':'task',
                             },

         'need':[6],
         'reward':{'coin':80,'iron':200000,},
        },
    'main_58':{     
         'type':'build_up',  
         'info':'main_58_info',
         'index':'2_57',
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building002',
                             },

         'need':[2,8,['building002']],
         'reward':{'gold':30000,'wood':30000,'iron':30000,},
        },
    'main_59':{     
         'type':'com_sand',  
         'info':'main_59_info',
         'index':'2_58',
         'goto_cfg':{
                              'type':3,
                              'panelID':'pve',
                             },

         'need':[2,'chapter002','battle022','all'],
         'reward':{'food':20000,'gold':200000,'item032':200,},
        },
    'main_60':{     
         'type':'grade_up',  
         'info':'main_60_info',
         'index':'2_59',
         'goto_cfg':{
                              'type':3,
                              'panelID':'hero',
                             },

         'need':[2,5,[0,2]],
         'reward':{'gold':200000,},
        },
    'main_62':{     
         'type':'ts_asked',  
         'info':'main_62_info',
         'index':'2_60',
         'goto_cfg':{
                              'type':3,
                              'panelID':'prop_resolve',
                             },

         'need':[200],
         'reward':{'item223':200,'item220':200,},
        },
    'main_62':{     
         'type':'com_people',  
         'info':'main_62_info',
         'index':'2_62',
         'goto_cfg':{
                              'type':2,
                             },

         'need':[2,['264','275','222']],
         'reward':{'coin':50,'food':20000,'gold':200000,},
        },
    'main_63':{     
         'type':'ts_build',  
         'info':'main_63_info',
         'index':'2_62',
         'goto_cfg':{
                              'type':2,
                              'state':2,
                              'secondMenu':3,
                             },

         'need':[2,[['264'],['275'],['222']],['b05','b05','b05']],
         'reward':{'wood':60000,'iron':30000,},
        },
    'main_64':{     
         'type':'com_sand',  
         'info':'main_64_info',
         'index':'2_63',
         'goto_cfg':{
                              'type':3,
                              'panelID':'pve',
                             },

         'need':[2,'chapter002','battle024','all'],
         'reward':{'food':200000,'gold':200000,},
        },
    'main_65':{     
         'type':'grade_up',  
         'info':'main_65_info',
         'index':'2_64',
         'goto_cfg':{
                              'type':3,
                              'panelID':'hero',
                             },

         'need':[2,8,[0,2]],
         'reward':{'wood':200000,'iron':200000,},
        },
    'main_66':{     
         'type':'com_people',  
         'info':'main_66_info',
         'index':'2_65',
         'goto_cfg':{
                              'type':2,
                             },

         'need':[2,['277','28','326']],
         'reward':{'coin':50,'food':20000,'gold':200000,},
        },
    'main_67':{     
         'type':'ts_build',  
         'info':'main_67_info',
         'index':'2_66',
         'goto_cfg':{
                              'type':2,
                              'state':2,
                              'secondMenu':3,
                             },

         'need':[2,[['277'],['28'],['326']],['b06','b06','b06']],
         'reward':{'wood':60000,'iron':30000,'food':40000,},
        },
    'main_68':{     
         'type':'com_sand',  
         'info':'main_68_info',
         'index':'2_67',
         'goto_cfg':{
                              'type':3,
                              'panelID':'pve',
                             },

         'need':[2,'chapter003','battle036','all'],
         'reward':{'food':200000,'gold':200000,},
        },
    'main_69':{      
         'type':'get_office',  
         'info':'main_69_info',
         'index':'2_68',
         'goto_cfg':{
                              'type':3,
                              'panelID':'office',
                             },

         'need':[2,3],
         'reward':{'coin':2000,},
        },
    'main_70':{     
         'type':'build_up',  
         'info':'main_70_info',
         'index':'2_69',
         'goto_cfg':{
                              'type':2,
                              'buildingID':'building002',
                             },

         'need':[2,9,['building002']],
         'reward':{'gold':30000,'wood':30000,'iron':30000,},
        },
    'main_72':{      
         'type':'com_people',  
         'info':'main_72_info',
         'index':'2_70',
         'goto_cfg':{
                              'type':2,
                             },

         'need':[2,['223','75','203']],
         'reward':{'coin':50,'food':20000,'gold':200000,},
        },
    'main_72':{     
         'type':'ts_build',  
         'info':'main_72_info',
         'index':'2_72',
         'goto_cfg':{
                              'type':2,
                              'state':2,
                              'secondMenu':3,
                             },

         'need':[2,[['223'],['75'],['203']],['b95','b26','b32']],
         'reward':{'wood':60000,'coin':30,},
        },
    'main_73':{      
         'type':'com_sand',  
         'info':'main_73_info',
         'index':'2_72',
         'goto_cfg':{
                              'type':3,
                              'panelID':'pve',
                             },

         'need':[2,'chapter004','battle048','all'],
         'reward':{'food':200000,'gold':200000,},
        },
    'main_74':{      
         'type':'com_people',  
         'info':'main_74_info',
         'index':'2_73',
         'goto_cfg':{
                              'type':2,
                             },

         'need':[2,['96','327','297']],
         'reward':{'coin':50,'food':20000,'gold':200000,},
        },
    'main_75':{     
         'type':'ts_build',  
         'info':'main_75_info',
         'index':'2_74',
         'goto_cfg':{
                              'type':2,
                              'state':2,
                              'secondMenu':3,
                             },

         'need':[2,[['96'],['327'],['297']],['b04','b04','b04']],
         'reward':{'food':30000,'wood':60000,'iron':30000,},
        },
    'main_76':{     
         'type':'get_office',  
         'info':'main_76_info',
         'index':'2_75',
         'goto_cfg':{
                              'type':3,
                              'panelID':'office',
                             },

         'need':[2,4],
         'reward':{'food':30000,'gold':30000,},
        },
    'main_77':{     
         'type':'ts_build',  
         'info':'main_77_info',
         'index':'2_76',
         'goto_cfg':{
                              'type':2,
                              'state':2,
                              'secondMenu':3,
                             },

         'need':[2,[['252'],['76'],['344']],['b34','b27','b30']],
         'reward':{'wood':60000,'coin':30,},
        },
    'main_78':{     
         'type':'com_sand',  
         'info':'main_78_info',
         'index':'2_77',
         'goto_cfg':{
                              'type':3,
                              'panelID':'pve',
                             },

         'need':[2,'chapter006','battle072','all'],
         'reward':{'food':200000,'gold':200000,},
        },
    'main_79':{     
         'type':'grade_up',  
         'info':'main_79_info',
         'index':'2_78',
         'goto_cfg':{
                              'type':3,
                              'panelID':'hero',
                             },

         'need':[2,25,[0,2]],
         'reward':{'wood':20000,'iron':20000,},
        },
    'main_80':{      
         'type':'com_people',  
         'info':'main_80_info',
         'index':'2_79',
         'goto_cfg':{
                              'type':2,
                             },

         'need':[2,['75','309','275']],
         'reward':{'coin':50,'food':20000,'gold':200000,},
        },
    'main_82':{     
         'type':'ts_build',  
         'info':'main_82_info',
         'index':'2_80',
         'goto_cfg':{
                              'type':2,
                              'state':2,
                              'secondMenu':3,
                             },

         'need':[2,[['75'],['309'],['275']],['b07','b07','b07']],
         'reward':{'wood':60000,'coin':40,},
        },
    'main_82':{      
         'type':'com_sand',  
         'info':'main_82_info',
         'index':'2_82',
         'goto_cfg':{
                              'type':3,
                              'panelID':'pve',
                             },

         'need':[2,'chapter007','battle084','all'],
         'reward':{'food':200000,'gold':200000,},
        },
    'main_83':{     
         'type':'com_people',  
         'info':'main_83_info',
         'index':'2_82',
         'goto_cfg':{
                              'type':2,
                             },

         'need':[2,['272','24','306']],
         'reward':{'coin':50,'food':25000,'gold':200000,},
        },
    'main_84':{     
         'type':'ts_build',  
         'info':'main_84_info',
         'index':'2_83',
         'goto_cfg':{
                              'type':2,
                              'state':2,
                              'secondMenu':3,
                             },

         'need':[3,[['2009'],['2009'],['296']],['b09','b09','b09']],
         'reward':{'wood':99975,'coin':50,},
        },
    'main_85':{     
         'type':'ts_build',  
         'info':'main_85_info',
         'index':'2_84',
         'goto_cfg':{
                              'type':2,
                              'state':2,
                              'secondMenu':3,
                             },

         'need':[3,[['296'],['299'],['299']],['b09','b09','b09']],
         'reward':{'wood':99975,'coin':50,},
        },
    'main_86':{      
         'type':'get_office',  
         'info':'main_86_info',
         'index':'2_85',
         'goto_cfg':{
                              'type':3,
                              'panelID':'office',
                             },

         'need':[2,5],
         'reward':{'coin':2000,},
        },
    'main_87':{     
         'type':'ts_build',  
         'info':'main_87_info',
         'index':'2_86',
         'goto_cfg':{
                              'type':2,
                              'state':2,
                              'secondMenu':3,
                             },

         'need':[3,[['246'],['32'],['953']],['b29','b29','b29']],
         'reward':{'food':50000,'wood':2000000,'iron':50000,},
        },

},
    'common':{                          
    'task030':{       
         'type':'learn_skill',  
         'name':'task030_name',
         'info':'task030_info',
         'explain_info':'task030_einfo',
         'index':'2_0',
         'task':[ 
                  {
                  'need':[2,5],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,6],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,5],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,6],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[3,5],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[3,6],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,7],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,8],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,7],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,8],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[3,7],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[3,8],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,9],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,200],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,9],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,200],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[3,9],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[3,200],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,22],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,22],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,22],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,22],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[3,22],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[3,22],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,23],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,24],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,25],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,26],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,27],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,28],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,23],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,24],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,25],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,26],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,27],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,28],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,29],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,20],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,22],
                  'reward':{'item037':5,},
                  },
                    ],
        },
    'task032':{       
         'type':'skill_lv',  
         'name':'task032_name',
         'info':'task032_info',
         'explain_info':'task032_einfo',
         'index':'2_2',
         'task':[ 
                  {
                  'need':[2,8],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,8],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,9],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,9],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,200],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,200],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,22],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,22],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[3,22],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,22],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,22],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[3,22],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[2,23],
                  'reward':{'item037':200,},
                  },
                  {
                  'need':[2,23],
                  'reward':{'item037':200,},
                  },
                  {
                  'need':[2,24],
                  'reward':{'item037':200,},
                  },
                  {
                  'need':[2,24],
                  'reward':{'item037':200,},
                  },
                  {
                  'need':[2,25],
                  'reward':{'item037':200,},
                  },
                  {
                  'need':[2,25],
                  'reward':{'item037':200,},
                  },
                  {
                  'need':[2,26],
                  'reward':{'item037':200,},
                  },
                  {
                  'need':[2,26],
                  'reward':{'item037':200,},
                  },
                  {
                  'need':[2,27],
                  'reward':{'item037':200,},
                  },
                  {
                  'need':[2,27],
                  'reward':{'item037':200,},
                  },
                  {
                  'need':[2,28],
                  'reward':{'item037':200,},
                  },
                  {
                  'need':[2,28],
                  'reward':{'item037':200,},
                  },
                  {
                  'need':[2,29],
                  'reward':{'item037':200,},
                  },
                  {
                  'need':[2,29],
                  'reward':{'item037':200,},
                  },
                  {
                  'need':[2,20],
                  'reward':{'item037':200,},
                  },
                  {
                  'need':[2,20],
                  'reward':{'item037':200,},
                  },
                  {
                  'need':[2,22],
                  'reward':{'item037':200,},
                  },
                  {
                  'need':[2,22],
                  'reward':{'item037':200,},
                  },
                  {
                  'need':[2,22],
                  'reward':{'item037':200,},
                  },
                  {
                  'need':[2,22],
                  'reward':{'item037':200,},
                  },
                  {
                  'need':[2,23],
                  'reward':{'item037':22,},
                  },
                  {
                  'need':[2,23],
                  'reward':{'item037':22,},
                  },
                  {
                  'need':[2,24],
                  'reward':{'item037':23,},
                  },
                  {
                  'need':[2,24],
                  'reward':{'item037':24,},
                  },
                  {
                  'need':[2,25],
                  'reward':{'item037':25,},
                  },
                  {
                  'need':[2,25],
                  'reward':{'item037':25,},
                  },
                    ],
        },
    'task032':{        
         'type':'have_hero',  
         'name':'task032_name',
         'info':'task032_info',
         'explain_info':'task032_einfo',
         'index':'2_2',
         'task':[ 
                  {
                  'need':[3,[2,2]],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[4,[2,2]],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[5,[2,2]],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[6,[2,2]],
                  'reward':{'gold':2000,},
                  },
                  {
                  'need':[7,[2,2]],
                  'reward':{'gold':2000,},
                  },
                  {
                  'need':[8,[2,2]],
                  'reward':{'gold':2000,},
                  },
                  {
                  'need':[9,[2,2]],
                  'reward':{'gold':3000,},
                  },
                  {
                  'need':[200,[2,2]],
                  'reward':{'gold':3000,},
                  },
                  {
                  'need':[22,[2,2]],
                  'reward':{'gold':3000,},
                  },
                  {
                  'need':[22,[2,2]],
                  'reward':{'gold':3000,},
                  },
                  {
                  'need':[23,[2,2]],
                  'reward':{'gold':3000,},
                  },
                  {
                  'need':[24,[2,2]],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[25,[2,2]],
                  'reward':{'gold':5000,},
                  },
                    ],
        },
    'task033':{        
         'type':'hero_lv',  
         'name':'task033_name',
         'info':'task033_info',
         'explain_info':'task033_einfo',
         'index':'2_3',
         'task':[ 
                  {
                  'need':[2,200],
                  'reward':{'item702':2,},
                  },
                  {
                  'need':[2,22],
                  'reward':{'item702':2,},
                  },
                  {
                  'need':[2,24],
                  'reward':{'item702':2,},
                  },
                  {
                  'need':[2,26],
                  'reward':{'item702':2,},
                  },
                  {
                  'need':[2,28],
                  'reward':{'item702':2,},
                  },
                  {
                  'need':[2,20],
                  'reward':{'item702':2,},
                  },
                  {
                  'need':[2,22],
                  'reward':{'item702':2,},
                  },
                  {
                  'need':[2,24],
                  'reward':{'item702':2,},
                  },
                  {
                  'need':[2,26],
                  'reward':{'item702':2,},
                  },
                  {
                  'need':[2,28],
                  'reward':{'item702':2,},
                  },
                  {
                  'need':[2,30],
                  'reward':{'item702':2,},
                  },
                  {
                  'need':[2,32],
                  'reward':{'item702':2,},
                  },
                  {
                  'need':[2,34],
                  'reward':{'item702':2,},
                  },
                  {
                  'need':[2,36],
                  'reward':{'item702':2,},
                  },
                  {
                  'need':[2,38],
                  'reward':{'item702':2,},
                  },
                  {
                  'need':[2,40],
                  'reward':{'item702':2,},
                  },
                  {
                  'need':[2,42],
                  'reward':{'item702':2,},
                  },
                  {
                  'need':[2,44],
                  'reward':{'item702':2,},
                  },
                  {
                  'need':[2,45],
                  'reward':{'item702':2,},
                  },
                  {
                  'need':[2,45],
                  'reward':{'item702':2,},
                  },
                  {
                  'need':[3,45],
                  'reward':{'item702':2,},
                  },
                  {
                  'need':[4,45],
                  'reward':{'item702':2,},
                  },
                  {
                  'need':[2,48],
                  'reward':{'item702':2,},
                  },
                  {
                  'need':[2,52],
                  'reward':{'item702':2,},
                  },
                  {
                  'need':[2,54],
                  'reward':{'item702':2,},
                  },
                  {
                  'need':[2,57],
                  'reward':{'item702':2,},
                  },
                    ],
        },
    'task034':{        
         'type':'hero_quality',  
         'name':'task034_name',
         'info':'task034_info',
         'explain_info':'task034_einfo',
         'index':'2_4',
         'task':[ 
                  {
                  'need':[2,2],
                  'reward':{'item032':5,},
                  },
                  {
                  'need':[2,2],
                  'reward':{'item032':5,},
                  },
                  {
                  'need':[2,3],
                  'reward':{'item032':5,},
                  },
                  {
                  'need':[2,4],
                  'reward':{'item032':5,},
                  },
                  {
                  'need':[2,5],
                  'reward':{'item032':5,},
                  },
                  {
                  'need':[2,6],
                  'reward':{'item032':200,},
                  },
                  {
                  'need':[2,6],
                  'reward':{'item032':200,},
                  },
                  {
                  'need':[2,7],
                  'reward':{'item032':5,},
                  },
                  {
                  'need':[2,8],
                  'reward':{'item032':5,},
                  },
                  {
                  'need':[2,9],
                  'reward':{'item032':5,},
                  },
                  {
                  'need':[2,200],
                  'reward':{'item032':5,},
                  },
                  {
                  'need':[2,22],
                  'reward':{'item032':5,},
                  },
                  {
                  'need':[2,22],
                  'reward':{'item032':200,},
                  },
                  {
                  'need':[2,22],
                  'reward':{'item032':200,},
                  },
                  {
                  'need':[3,22],
                  'reward':{'item032':200,},
                  },
                    ],
        },
    'task095':{       
         'type':'equip_num',  
         'name':'task095_name',
         'info':'task095_info',
         'explain_info':'task095_einfo',
         'index':'2_5',
         'task':[ 
                  {
                  'need':[ 2,'all'],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[ 2,'all'],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[3,'all'],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[4,'all'],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[5,'all'],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[6,'all'],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[7,'all'],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[8,'all'],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[9,'all'],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[200,'all'],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[22,'all'],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[22,'all'],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[23,'all'],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[24,'all'],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[25,'all'],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[26,'all'],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[27,'all'],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[28,'all'],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[29,'all'],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[20,'all'],
                  'reward':{'item402':3,},
                  },
                  {
                  'need':[22,'all'],
                  'reward':{'item402':3,},
                  },
                  {
                  'need':[22,'all'],
                  'reward':{'item402':3,},
                  },
                  {
                  'need':[23,'all'],
                  'reward':{'item402':3,},
                  },
                  {
                  'need':[24,'all'],
                  'reward':{'item402':3,},
                  },
                  {
                  'need':[25,'all'],
                  'reward':{'item402':3,},
                  },
                    ],
        },
    'task036':{         
         'type':'equip_quality',  
         'name':'task036_name',
         'info':'task036_info',
         'explain_info':'task036_einfo',
         'index':'2_6',
         'task':[ 
                  {
                  'need':[ 2,'all',2],
                  'reward':{'item403':2,},
                  },
                  {
                  'need':[ 2,'all',2],
                  'reward':{'item403':2,},
                  },
                  {
                  'need':[3,'all',2],
                  'reward':{'item403':2,},
                  },
                  {
                  'need':[ 2,'all',2],
                  'reward':{'item403':2,},
                  },
                  {
                  'need':[ 2,'all',2],
                  'reward':{'item403':2,},
                  },
                  {
                  'need':[3,'all',2],
                  'reward':{'item403':2,},
                  },
                  {
                  'need':[2,'all',3],
                  'reward':{'item403':3,},
                  },
                  {
                  'need':[2,'all',3],
                  'reward':{'item403':3,},
                  },
                  {
                  'need':[3,'all',3],
                  'reward':{'item403':3,},
                  },
                  {
                  'need':[2,'all',4],
                  'reward':{'item403':4,},
                  },
                  {
                  'need':[2,'all',4],
                  'reward':{'item403':4,},
                  },
                  {
                  'need':[3,'all',4],
                  'reward':{'item403':4,},
                  },
                  {
                  'need':[4,'all',4],
                  'reward':{'item403':4,},
                  },
                    ],
        },
    'task037':{        
         'type':'equip_num',  
         'name':'task037_name',
         'info':'task037_info',
         'explain_info':'task037_einfo',
         'index':'2_7',
         'task':[ 
                  {
                  'need':[ 2,0],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[ 2,0],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[ 3,0],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[ 4,0],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[ 5,0],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[ 6,0],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[ 7,0],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[ 8,0],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[ 9,0],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[ 200,0],
                  'reward':{'item402':2,},
                  },
                    ],
        },
    'task038':{       
         'type':'equip_quality',  
         'name':'task038_name',
         'info':'task038_info',
         'explain_info':'task038_einfo',
         'index':'2_8',
         'task':[ 
                  {
                  'need':[2,0,2],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[2,0,2],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[3,0,2],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[2,0,2],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[2,0,2],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[3,0,2],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[2,0,3],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[2,0,3],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[3,0,3],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[2,0,4],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[2,0,4],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[3,0,4],
                  'reward':{'item402':2,},
                  },
                    ],
        },
    'task039':{        
         'type':'equip_num',  
         'name':'task039_name',
         'info':'task039_info',
         'explain_info':'task039_einfo',
         'index':'2_9',
         'task':[ 
                  {
                  'need':[ 2,2],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[ 2,2],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[ 3,2],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[ 4,2],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[ 5,2],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[ 6,2],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[ 7,2],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[ 8,2],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[ 9,2],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[ 200,2],
                  'reward':{'item402':2,},
                  },
                    ],
        },
    'task040':{        
         'type':'equip_quality',  
         'name':'task040_name',
         'info':'task040_info',
         'explain_info':'task040_einfo',
         'index':'2_200',
         'task':[ 
                  {
                  'need':[2,2,2],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[2,2,2],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[3,2,2],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[2,2,2],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[2,2,2],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[3,2,2],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[2,2,3],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[2,2,3],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[3,2,3],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[2,2,4],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[2,2,4],
                  'reward':{'item402':2,},
                  },
                  {
                  'need':[3,2,4],
                  'reward':{'item402':2,},
                  },
                    ],
        },
    'task042':{         
         'type':'equip_num',  
         'name':'task042_name',
         'info':'task042_info',
         'explain_info':'task042_einfo',
         'index':'2_22',
         'task':[ 
                  {
                  'need':[ 2,2],
                  'reward':{'item403':2,},
                  },
                  {
                  'need':[ 2,2],
                  'reward':{'item403':2,},
                  },
                  {
                  'need':[ 3,2],
                  'reward':{'item403':2,},
                  },
                  {
                  'need':[ 4,2],
                  'reward':{'item403':2,},
                  },
                  {
                  'need':[ 5,2],
                  'reward':{'item403':2,},
                  },
                  {
                  'need':[ 6,2],
                  'reward':{'item403':2,},
                  },
                  {
                  'need':[ 7,2],
                  'reward':{'item403':2,},
                  },
                  {
                  'need':[ 8,2],
                  'reward':{'item403':2,},
                  },
                  {
                  'need':[ 9,2],
                  'reward':{'item403':2,},
                  },
                  {
                  'need':[ 200,2],
                  'reward':{'item403':2,},
                  },
                    ],
        },
    'task042':{       
         'type':'equip_quality',  
         'name':'task042_name',
         'info':'task042_info',
         'explain_info':'task042_einfo',
         'index':'2_22',
         'task':[ 
                  {
                  'need':[2,2,2],
                  'reward':{'item403':2,},
                  },
                  {
                  'need':[2,2,2],
                  'reward':{'item403':2,},
                  },
                  {
                  'need':[3,2,2],
                  'reward':{'item403':2,},
                  },
                  {
                  'need':[2,2,2],
                  'reward':{'item403':2,},
                  },
                  {
                  'need':[2,2,2],
                  'reward':{'item403':2,},
                  },
                  {
                  'need':[3,2,2],
                  'reward':{'item403':2,},
                  },
                  {
                  'need':[2,2,3],
                  'reward':{'item403':2,},
                  },
                  {
                  'need':[2,2,3],
                  'reward':{'item403':2,},
                  },
                  {
                  'need':[3,2,3],
                  'reward':{'item403':2,},
                  },
                  {
                  'need':[2,2,4],
                  'reward':{'item403':2,},
                  },
                  {
                  'need':[2,2,4],
                  'reward':{'item403':2,},
                  },
                  {
                  'need':[3,2,4],
                  'reward':{'item403':2,},
                  },
                    ],
        },
    'task043':{       
         'type':'equip_num',  
         'name':'task043_name',
         'info':'task043_info',
         'explain_info':'task043_einfo',
         'index':'2_23',
         'task':[ 
                  {
                  'need':[ 2,3],
                  'reward':{'item404':2,},
                  },
                  {
                  'need':[ 2,3],
                  'reward':{'item404':2,},
                  },
                  {
                  'need':[ 3,3],
                  'reward':{'item404':2,},
                  },
                  {
                  'need':[ 4,3],
                  'reward':{'item404':2,},
                  },
                  {
                  'need':[ 5,3],
                  'reward':{'item404':2,},
                  },
                  {
                  'need':[ 6,3],
                  'reward':{'item404':2,},
                  },
                  {
                  'need':[ 7,3],
                  'reward':{'item404':2,},
                  },
                  {
                  'need':[ 8,3],
                  'reward':{'item404':2,},
                  },
                  {
                  'need':[ 9,3],
                  'reward':{'item404':2,},
                  },
                  {
                  'need':[ 200,3],
                  'reward':{'item404':2,},
                  },
                    ],
        },
    'task044':{         
         'type':'equip_quality',  
         'name':'task044_name',
         'info':'task044_info',
         'explain_info':'task044_einfo',
         'index':'2_24',
         'task':[ 
                  {
                  'need':[2,3,2],
                  'reward':{'item404':2,},
                  },
                  {
                  'need':[2,3,2],
                  'reward':{'item404':2,},
                  },
                  {
                  'need':[3,3,2],
                  'reward':{'item404':2,},
                  },
                  {
                  'need':[2,3,2],
                  'reward':{'item404':2,},
                  },
                  {
                  'need':[2,3,2],
                  'reward':{'item404':2,},
                  },
                  {
                  'need':[3,3,2],
                  'reward':{'item404':2,},
                  },
                  {
                  'need':[2,3,3],
                  'reward':{'item404':2,},
                  },
                  {
                  'need':[2,3,3],
                  'reward':{'item404':2,},
                  },
                  {
                  'need':[3,3,3],
                  'reward':{'item404':2,},
                  },
                  {
                  'need':[2,3,4],
                  'reward':{'item404':2,},
                  },
                  {
                  'need':[2,3,4],
                  'reward':{'item404':2,},
                  },
                  {
                  'need':[3,3,4],
                  'reward':{'item404':2,},
                  },
                    ],
        },
    'task050':{       
         'type':'build_up',  
         'name':'task050_name',
         'info':'task050_info',
         'explain_info':'task050_einfo',
         'index':'3_0',
         'task':[ 
                  {
                  'need':[ 2,4,['building002']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,5,['building002']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,6,['building002']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,7,['building002']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,8,['building002']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,9,['building002']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,200,['building002']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,22,['building002']],
                  'reward':{'coin':50,},
                  },
                  {
                  'need':[ 2,22,['building002']],
                  'reward':{'coin':50,},
                  },
                  {
                  'need':[ 2,23,['building002']],
                  'reward':{'coin':50,},
                  },
                  {
                  'need':[ 2,24,['building002']],
                  'reward':{'coin':50,},
                  },
                  {
                  'need':[ 2,25,['building002']],
                  'reward':{'coin':50,},
                  },
                  {
                  'need':[ 2,26,['building002']],
                  'reward':{'coin':50,},
                  },
                  {
                  'need':[ 2,27,['building002']],
                  'reward':{'coin':50,},
                  },
                  {
                  'need':[ 2,28,['building002']],
                  'reward':{'coin':50,},
                  },
                  {
                  'need':[ 2,29,['building002']],
                  'reward':{'coin':50,},
                  },
                  {
                  'need':[ 2,20,['building002']],
                  'reward':{'coin':50,},
                  },
                  {
                  'need':[ 2,22,['building002']],
                  'reward':{'coin':50,},
                  },
                  {
                  'need':[ 2,22,['building002']],
                  'reward':{'coin':50,},
                  },
                    ],
        },
    'task052':{        
         'type':'build_up',  
         'name':'task052_name',
         'info':'task052_info',
         'explain_info':'task052_einfo',
         'index':'3_2',
         'task':[ 
                  {
                  'need':[ 2,2,['building002']],
                  'reward':{'gold':2000,},
                  },
                  {
                  'need':[ 2,3,['building002']],
                  'reward':{'gold':2000,},
                  },
                  {
                  'need':[ 2,4,['building002']],
                  'reward':{'coin':30,},
                  },
                  {
                  'need':[ 2,5,['building002']],
                  'reward':{'coin':30,},
                  },
                  {
                  'need':[ 2,6,['building002']],
                  'reward':{'coin':30,},
                  },
                  {
                  'need':[ 2,7,['building002']],
                  'reward':{'coin':30,},
                  },
                  {
                  'need':[ 2,8,['building002']],
                  'reward':{'coin':30,},
                  },
                  {
                  'need':[ 2,9,['building002']],
                  'reward':{'coin':30,},
                  },
                    ],
        },
    'task052':{        
         'type':'build_up',  
         'name':'task052_name',
         'info':'task052_info',
         'explain_info':'task052_einfo',
         'index':'3_2',
         'task':[ 
                  {
                  'need':[ 2,2,['building003']],
                  'reward':{'coin':30,},
                  },
                  {
                  'need':[ 2,3,['building003']],
                  'reward':{'coin':30,},
                  },
                  {
                  'need':[ 2,4,['building003']],
                  'reward':{'coin':30,},
                  },
                  {
                  'need':[ 2,5,['building003']],
                  'reward':{'coin':30,},
                  },
                  {
                  'need':[ 2,6,['building003']],
                  'reward':{'coin':30,},
                  },
                  {
                  'need':[ 2,7,['building003']],
                  'reward':{'coin':30,},
                  },
                  {
                  'need':[ 2,8,['building003']],
                  'reward':{'coin':30,},
                  },
                  {
                  'need':[ 2,9,['building003']],
                  'reward':{'coin':30,},
                  },
                    ],
        },
    'task053':{        
         'type':'build_up',  
         'name':'task053_name',
         'info':'task053_info',
         'explain_info':'task053_einfo',
         'index':'3_3',
         'task':[ 
                  {
                  'need':[ 2,2,['building004']],
                  'reward':{'gold':2000,},
                  },
                  {
                  'need':[ 2,3,['building004']],
                  'reward':{'gold':2000,},
                  },
                  {
                  'need':[ 2,4,['building004']],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[ 2,5,['building004']],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[ 2,6,['building004']],
                  'reward':{'coin':200,},
                  },
                  {
                  'need':[ 2,7,['building004']],
                  'reward':{'coin':200,},
                  },
                  {
                  'need':[ 2,8,['building004']],
                  'reward':{'coin':200,},
                  },
                  {
                  'need':[ 2,9,['building004']],
                  'reward':{'coin':200,},
                  },
                  {
                  'need':[ 2,200,['building004']],
                  'reward':{'coin':200,},
                  },
                  {
                  'need':[ 2,22,['building004']],
                  'reward':{'coin':200,},
                  },
                  {
                  'need':[ 2,22,['building004']],
                  'reward':{'coin':200,},
                  },
                  {
                  'need':[ 2,23,['building004']],
                  'reward':{'coin':200,},
                  },
                  {
                  'need':[ 2,24,['building004']],
                  'reward':{'coin':200,},
                  },
                  {
                  'need':[ 2,25,['building004']],
                  'reward':{'coin':200,},
                  },
                  {
                  'need':[ 2,26,['building004']],
                  'reward':{'coin':200,},
                  },
                  {
                  'need':[ 2,27,['building004']],
                  'reward':{'coin':200,},
                  },
                  {
                  'need':[ 2,28,['building004']],
                  'reward':{'coin':200,},
                  },
                  {
                  'need':[ 2,29,['building004']],
                  'reward':{'coin':200,},
                  },
                  {
                  'need':[ 2,20,['building004']],
                  'reward':{'coin':200,},
                  },
                    ],
        },
    'task054':{        
         'type':'build_up',  
         'name':'task054_name',
         'info':'task054_info',
         'explain_info':'task054_einfo',
         'index':'3_4',
         'task':[ 
                  {
                  'need':[ 2,2,['building005']],
                  'reward':{'item032':200,},
                  },
                  {
                  'need':[ 2,3,['building005']],
                  'reward':{'item032':200,},
                  },
                  {
                  'need':[ 2,4,['building005']],
                  'reward':{'item032':200,},
                  },
                  {
                  'need':[ 2,5,['building005']],
                  'reward':{'item032':200,},
                  },
                  {
                  'need':[ 2,6,['building005']],
                  'reward':{'item032':200,},
                  },
                  {
                  'need':[ 2,7,['building005']],
                  'reward':{'item032':200,},
                  },
                  {
                  'need':[ 2,8,['building005']],
                  'reward':{'item032':200,},
                  },
                  {
                  'need':[ 2,9,['building005']],
                  'reward':{'item032':200,},
                  },
                  {
                  'need':[ 2,200,['building005']],
                  'reward':{'item032':200,},
                  },
                  {
                  'need':[ 2,22,['building005']],
                  'reward':{'item032':200,},
                  },
                  {
                  'need':[ 2,22,['building005']],
                  'reward':{'item032':200,},
                  },
                  {
                  'need':[ 2,23,['building005']],
                  'reward':{'item032':200,},
                  },
                  {
                  'need':[ 2,24,['building005']],
                  'reward':{'item032':200,},
                  },
                  {
                  'need':[ 2,25,['building005']],
                  'reward':{'item032':200,},
                  },
                  {
                  'need':[ 2,26,['building005']],
                  'reward':{'item032':200,},
                  },
                  {
                  'need':[ 2,27,['building005']],
                  'reward':{'item032':200,},
                  },
                  {
                  'need':[ 2,28,['building005']],
                  'reward':{'item032':200,},
                  },
                  {
                  'need':[ 2,29,['building005']],
                  'reward':{'item032':200,},
                  },
                  {
                  'need':[ 2,20,['building005']],
                  'reward':{'item032':200,},
                  },
                    ],
        },
    'task075':{        
         'type':'build_up',  
         'name':'task075_name',
         'info':'task075_info',
         'explain_info':'task075_einfo',
         'index':'3_5',
         'task':[ 
                  {
                  'need':[ 2,2,['building006']],
                  'reward':{'gold':2000,},
                  },
                  {
                  'need':[ 2,3,['building006']],
                  'reward':{'gold':2000,},
                  },
                  {
                  'need':[ 2,4,['building006']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,5,['building006']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,6,['building006']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,7,['building006']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,8,['building006']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,9,['building006']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,200,['building006']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,22,['building006']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,22,['building006']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,23,['building006']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,24,['building006']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,25,['building006']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,26,['building006']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,27,['building006']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,28,['building006']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,29,['building006']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,20,['building006']],
                  'reward':{'coin':20,},
                  },
                    ],
        },
    'task056':{        
         'type':'build_up',  
         'name':'task056_name',
         'info':'task056_info',
         'explain_info':'task056_einfo',
         'index':'3_6',
         'task':[ 
                  {
                  'need':[ 2,2,['building007']],
                  'reward':{'gold':2000,},
                  },
                  {
                  'need':[ 2,3,['building007']],
                  'reward':{'gold':2000,},
                  },
                  {
                  'need':[ 2,4,['building007']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,5,['building007']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,6,['building007']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,7,['building007']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,8,['building007']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,9,['building007']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,200,['building007']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,22,['building007']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,22,['building007']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,23,['building007']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,24,['building007']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,25,['building007']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,26,['building007']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,27,['building007']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,28,['building007']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,29,['building007']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,20,['building007']],
                  'reward':{'coin':20,},
                  },
                    ],
        },
    'task057':{        
         'type':'build_up',  
         'name':'task057_name',
         'info':'task057_info',
         'explain_info':'task057_einfo',
         'index':'3_7',
         'task':[ 
                  {
                  'need':[ 2,2,['building008']],
                  'reward':{'gold':2000,},
                  },
                  {
                  'need':[ 2,3,['building008']],
                  'reward':{'gold':2000,},
                  },
                  {
                  'need':[ 2,4,['building008']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,5,['building008']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,6,['building008']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,7,['building008']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,8,['building008']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,9,['building008']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,200,['building008']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,22,['building008']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,22,['building008']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,23,['building008']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,24,['building008']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,25,['building008']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,26,['building008']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,27,['building008']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,28,['building008']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,29,['building008']],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[ 2,20,['building008']],
                  'reward':{'coin':20,},
                  },
                    ],
        },
    'task058':{        
         'type':'build_up',  
         'name':'task058_name',
         'info':'task058_info',
         'explain_info':'task058_einfo',
         'index':'3_8',
         'task':[ 
                  {
                  'need':[ 2,4,['building009']],
                  'reward':{'item020':2,},
                  },
                  {
                  'need':[ 2,5,['building009']],
                  'reward':{'item020':2,},
                  },
                  {
                  'need':[ 2,6,['building009']],
                  'reward':{'item020':2,},
                  },
                  {
                  'need':[ 2,7,['building009']],
                  'reward':{'item020':2,},
                  },
                  {
                  'need':[ 2,8,['building009']],
                  'reward':{'item020':2,},
                  },
                  {
                  'need':[ 2,9,['building009']],
                  'reward':{'item020':2,},
                  },
                  {
                  'need':[ 2,200,['building009']],
                  'reward':{'item020':3,},
                  },
                  {
                  'need':[ 2,22,['building009']],
                  'reward':{'item020':3,},
                  },
                  {
                  'need':[ 2,22,['building009']],
                  'reward':{'item020':3,},
                  },
                  {
                  'need':[ 2,23,['building009']],
                  'reward':{'item020':3,},
                  },
                  {
                  'need':[ 2,24,['building009']],
                  'reward':{'item020':4,},
                  },
                  {
                  'need':[ 2,25,['building009']],
                  'reward':{'item020':4,},
                  },
                  {
                  'need':[ 2,26,['building009']],
                  'reward':{'item020':4,},
                  },
                  {
                  'need':[ 2,27,['building009']],
                  'reward':{'item020':4,},
                  },
                  {
                  'need':[ 2,28,['building009']],
                  'reward':{'item020':4,},
                  },
                  {
                  'need':[ 2,29,['building009']],
                  'reward':{'item020':4,},
                  },
                  {
                  'need':[ 2,20,['building009']],
                  'reward':{'item020':4,},
                  },
                    ],
        },
    'task059':{        
         'type':'build_up',  
         'name':'task059_name',
         'info':'task059_info',
         'explain_info':'task059_einfo',
         'index':'3_9',
         'task':[ 
                  {
                  'need':[ 2,4,['building0200']],
                  'reward':{'item020':2,},
                  },
                  {
                  'need':[ 2,5,['building0200']],
                  'reward':{'item020':2,},
                  },
                  {
                  'need':[ 2,6,['building0200']],
                  'reward':{'item020':2,},
                  },
                  {
                  'need':[ 2,7,['building0200']],
                  'reward':{'item020':2,},
                  },
                  {
                  'need':[ 2,8,['building0200']],
                  'reward':{'item020':2,},
                  },
                  {
                  'need':[ 2,9,['building0200']],
                  'reward':{'item020':2,},
                  },
                  {
                  'need':[ 2,200,['building0200']],
                  'reward':{'item020':3,},
                  },
                  {
                  'need':[ 2,22,['building0200']],
                  'reward':{'item020':3,},
                  },
                  {
                  'need':[ 2,22,['building0200']],
                  'reward':{'item020':3,},
                  },
                  {
                  'need':[ 2,23,['building0200']],
                  'reward':{'item020':3,},
                  },
                  {
                  'need':[ 2,24,['building0200']],
                  'reward':{'item020':4,},
                  },
                  {
                  'need':[ 2,25,['building0200']],
                  'reward':{'item020':4,},
                  },
                  {
                  'need':[ 2,26,['building0200']],
                  'reward':{'item020':4,},
                  },
                  {
                  'need':[ 2,27,['building0200']],
                  'reward':{'item020':4,},
                  },
                  {
                  'need':[ 2,28,['building0200']],
                  'reward':{'item020':4,},
                  },
                  {
                  'need':[ 2,29,['building0200']],
                  'reward':{'item020':4,},
                  },
                  {
                  'need':[ 2,20,['building0200']],
                  'reward':{'item020':4,},
                  },
                    ],
        },
    'task060':{        
         'type':'build_up',  
         'name':'task060_name',
         'info':'task060_info',
         'explain_info':'task060_einfo',
         'index':'3_200',
         'task':[ 
                  {
                  'need':[ 2,4,['building022']],
                  'reward':{'item020':2,},
                  },
                  {
                  'need':[ 2,5,['building022']],
                  'reward':{'item020':2,},
                  },
                  {
                  'need':[ 2,6,['building022']],
                  'reward':{'item020':2,},
                  },
                  {
                  'need':[ 2,7,['building022']],
                  'reward':{'item020':2,},
                  },
                  {
                  'need':[ 2,8,['building022']],
                  'reward':{'item020':2,},
                  },
                  {
                  'need':[ 2,9,['building022']],
                  'reward':{'item020':2,},
                  },
                  {
                  'need':[ 2,200,['building022']],
                  'reward':{'item020':3,},
                  },
                  {
                  'need':[ 2,22,['building022']],
                  'reward':{'item020':3,},
                  },
                  {
                  'need':[ 2,22,['building022']],
                  'reward':{'item020':3,},
                  },
                  {
                  'need':[ 2,23,['building022']],
                  'reward':{'item020':3,},
                  },
                  {
                  'need':[ 2,24,['building022']],
                  'reward':{'item020':4,},
                  },
                  {
                  'need':[ 2,25,['building022']],
                  'reward':{'item020':4,},
                  },
                  {
                  'need':[ 2,26,['building022']],
                  'reward':{'item020':4,},
                  },
                  {
                  'need':[ 2,27,['building022']],
                  'reward':{'item020':4,},
                  },
                  {
                  'need':[ 2,28,['building022']],
                  'reward':{'item020':4,},
                  },
                  {
                  'need':[ 2,29,['building022']],
                  'reward':{'item020':4,},
                  },
                  {
                  'need':[ 2,20,['building022']],
                  'reward':{'item020':4,},
                  },
                    ],
        },
    'task062':{        
         'type':'build_up',  
         'name':'task062_name',
         'info':'task062_info',
         'explain_info':'task062_einfo',
         'index':'3_22',
         'task':[ 
                  {
                  'need':[ 2,4,['building022']],
                  'reward':{'item020':2,},
                  },
                  {
                  'need':[ 2,5,['building022']],
                  'reward':{'item020':2,},
                  },
                  {
                  'need':[ 2,6,['building022']],
                  'reward':{'item020':2,},
                  },
                  {
                  'need':[ 2,7,['building022']],
                  'reward':{'item020':2,},
                  },
                  {
                  'need':[ 2,8,['building022']],
                  'reward':{'item020':2,},
                  },
                  {
                  'need':[ 2,9,['building022']],
                  'reward':{'item020':2,},
                  },
                  {
                  'need':[ 2,200,['building022']],
                  'reward':{'item020':3,},
                  },
                  {
                  'need':[ 2,22,['building022']],
                  'reward':{'item020':3,},
                  },
                  {
                  'need':[ 2,22,['building022']],
                  'reward':{'item020':3,},
                  },
                  {
                  'need':[ 2,23,['building022']],
                  'reward':{'item020':3,},
                  },
                  {
                  'need':[ 2,24,['building022']],
                  'reward':{'item020':4,},
                  },
                  {
                  'need':[ 2,25,['building022']],
                  'reward':{'item020':4,},
                  },
                  {
                  'need':[ 2,26,['building022']],
                  'reward':{'item020':4,},
                  },
                  {
                  'need':[ 2,27,['building022']],
                  'reward':{'item020':4,},
                  },
                  {
                  'need':[ 2,28,['building022']],
                  'reward':{'item020':4,},
                  },
                  {
                  'need':[ 2,29,['building022']],
                  'reward':{'item020':4,},
                  },
                  {
                  'need':[ 2,20,['building022']],
                  'reward':{'item020':4,},
                  },
                    ],
        },
    'task062':{        
         'type':'build_up',  
         'name':'task062_name',
         'info':'task062_info',
         'explain_info':'task062_einfo',
         'index':'3_22',
         'task':[ 
                  {
                  'need':[ 2,2,['building023','building024','building025']],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[ 2,3,['building023','building024','building025']],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[ 2,4,['building023','building024','building025']],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[ 2,5,['building023','building024','building025']],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[ 2,6,['building023','building024','building025']],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[ 2,7,['building023','building024','building025']],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[ 2,8,['building023','building024','building025']],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[ 2,9,['building023','building024','building025']],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[ 2,200,['building023','building024','building025']],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[ 2,22,['building023','building024','building025']],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[ 2,22,['building023','building024','building025']],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[ 2,23,['building023','building024','building025']],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[ 2,24,['building023','building024','building025']],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[ 2,25,['building023','building024','building025']],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[ 2,26,['building023','building024','building025']],
                  'reward':{'gold':5000,},
                  },
                    ],
        },
    'task063':{        
         'type':'build_up',  
         'name':'task063_name',
         'info':'task063_info',
         'explain_info':'task063_einfo',
         'index':'3_23',
         'task':[ 
                  {
                  'need':[ 2,2,['building026','building027','building028']],
                  'reward':{'iron':25000,},
                  },
                  {
                  'need':[ 2,3,['building026','building027','building028']],
                  'reward':{'iron':25000,},
                  },
                  {
                  'need':[ 2,4,['building026','building027','building028']],
                  'reward':{'iron':25000,},
                  },
                  {
                  'need':[ 2,5,['building026','building027','building028']],
                  'reward':{'iron':25000,},
                  },
                  {
                  'need':[ 2,6,['building026','building027','building028']],
                  'reward':{'iron':25000,},
                  },
                  {
                  'need':[ 2,7,['building026','building027','building028']],
                  'reward':{'iron':25000,},
                  },
                  {
                  'need':[ 2,8,['building026','building027','building028']],
                  'reward':{'iron':25000,},
                  },
                  {
                  'need':[ 2,9,['building026','building027','building028']],
                  'reward':{'iron':25000,},
                  },
                  {
                  'need':[ 2,200,['building026','building027','building028']],
                  'reward':{'iron':25000,},
                  },
                  {
                  'need':[ 2,22,['building026','building027','building028']],
                  'reward':{'iron':25000,},
                  },
                  {
                  'need':[ 2,22,['building026','building027','building028']],
                  'reward':{'iron':25000,},
                  },
                  {
                  'need':[ 2,23,['building026','building027','building028']],
                  'reward':{'iron':25000,},
                  },
                  {
                  'need':[ 2,24,['building026','building027','building028']],
                  'reward':{'iron':25000,},
                  },
                  {
                  'need':[ 2,25,['building026','building027','building028']],
                  'reward':{'iron':25000,},
                  },
                  {
                  'need':[ 2,26,['building026','building027','building028']],
                  'reward':{'iron':25000,},
                  },
                    ],
        },
    'task064':{        
         'type':'build_up',  
         'name':'task064_name',
         'info':'task064_info',
         'explain_info':'task064_einfo',
         'index':'3_24',
         'task':[ 
                  {
                  'need':[ 2,2,['building029','building020','building022']],
                  'reward':{'wood':25000,},
                  },
                  {
                  'need':[ 2,3,['building029','building020','building022']],
                  'reward':{'wood':25000,},
                  },
                  {
                  'need':[ 2,4,['building029','building020','building022']],
                  'reward':{'wood':25000,},
                  },
                  {
                  'need':[ 2,5,['building029','building020','building022']],
                  'reward':{'wood':25000,},
                  },
                  {
                  'need':[ 2,6,['building029','building020','building022']],
                  'reward':{'wood':25000,},
                  },
                  {
                  'need':[ 2,7,['building029','building020','building022']],
                  'reward':{'wood':25000,},
                  },
                  {
                  'need':[ 2,8,['building029','building020','building022']],
                  'reward':{'wood':25000,},
                  },
                  {
                  'need':[ 2,9,['building029','building020','building022']],
                  'reward':{'wood':25000,},
                  },
                  {
                  'need':[ 2,200,['building029','building020','building022']],
                  'reward':{'wood':25000,},
                  },
                  {
                  'need':[ 2,22,['building029','building020','building022']],
                  'reward':{'wood':25000,},
                  },
                  {
                  'need':[ 2,22,['building029','building020','building022']],
                  'reward':{'wood':25000,},
                  },
                  {
                  'need':[ 2,23,['building029','building020','building022']],
                  'reward':{'wood':25000,},
                  },
                  {
                  'need':[ 2,24,['building029','building020','building022']],
                  'reward':{'wood':25000,},
                  },
                  {
                  'need':[ 2,25,['building029','building020','building022']],
                  'reward':{'wood':25000,},
                  },
                  {
                  'need':[ 2,26,['building029','building020','building022']],
                  'reward':{'wood':25000,},
                  },
                    ],
        },
    'task065':{        
         'type':'build_up',  
         'name':'task065_name',
         'info':'task065_info',
         'explain_info':'task065_einfo',
         'index':'3_25',
         'task':[ 
                  {
                  'need':[ 2,2,['building022','building023','building024']],
                  'reward':{'food':25000,},
                  },
                  {
                  'need':[ 2,3,['building022','building023','building024']],
                  'reward':{'food':25000,},
                  },
                  {
                  'need':[ 2,4,['building022','building023','building024']],
                  'reward':{'food':25000,},
                  },
                  {
                  'need':[ 2,5,['building022','building023','building024']],
                  'reward':{'food':25000,},
                  },
                  {
                  'need':[ 2,6,['building022','building023','building024']],
                  'reward':{'food':25000,},
                  },
                  {
                  'need':[ 2,7,['building022','building023','building024']],
                  'reward':{'food':25000,},
                  },
                  {
                  'need':[ 2,8,['building022','building023','building024']],
                  'reward':{'food':25000,},
                  },
                  {
                  'need':[ 2,9,['building022','building023','building024']],
                  'reward':{'food':25000,},
                  },
                  {
                  'need':[ 2,200,['building022','building023','building024']],
                  'reward':{'food':25000,},
                  },
                  {
                  'need':[ 2,22,['building022','building023','building024']],
                  'reward':{'food':25000,},
                  },
                  {
                  'need':[ 2,22,['building022','building023','building024']],
                  'reward':{'food':25000,},
                  },
                  {
                  'need':[ 2,23,['building022','building023','building024']],
                  'reward':{'food':25000,},
                  },
                  {
                  'need':[ 2,24,['building022','building023','building024']],
                  'reward':{'food':25000,},
                  },
                  {
                  'need':[ 2,25,['building022','building023','building024']],
                  'reward':{'food':25000,},
                  },
                  {
                  'need':[ 2,26,['building022','building023','building024']],
                  'reward':{'food':25000,},
                  },
                    ],
        },
    'task066':{       
         'type':'cum_catch',  
         'name':'task066_name',
         'info':'task066_info',
         'explain_info':'task066_einfo',
         'index':'3_26',
         'task':[ 
                  {
                  'need':[3],
                  'reward':{'item037':2,},
                  },
                  {
                  'need':[5],
                  'reward':{'item037':2,},
                  },
                  {
                  'need':[200],
                  'reward':{'item037':2,},
                  },
                  {
                  'need':[25],
                  'reward':{'item037':3,},
                  },
                  {
                  'need':[20],
                  'reward':{'item037':3,},
                  },
                  {
                  'need':[25],
                  'reward':{'item037':3,},
                  },
                  {
                  'need':[30],
                  'reward':{'item037':3,},
                  },
                  {
                  'need':[40],
                  'reward':{'item037':3,},
                  },
                  {
                  'need':[50],
                  'reward':{'item037':3,},
                  },
                  {
                  'need':[60],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[80],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[2000],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[220],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[250],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[280],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[2200],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[240],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[270],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[300],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[950],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[400],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[450],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[500],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[750],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[600],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[650],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[700],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[750],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[800],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[850],
                  'reward':{'item037':5,},
                  },
                    ],
        },
    'task067':{        
         'type':'cum_asked',  
         'name':'task067_name',
         'info':'task067_info',
         'explain_info':'task067_einfo',
         'index':'3_27',
         'task':[ 
                  {
                  'need':[2],
                  'reward':{'item037':2,},
                  },
                  {
                  'need':[200],
                  'reward':{'item037':2,},
                  },
                  {
                  'need':[20],
                  'reward':{'item037':2,},
                  },
                  {
                  'need':[30],
                  'reward':{'item037':3,},
                  },
                  {
                  'need':[60],
                  'reward':{'item037':3,},
                  },
                  {
                  'need':[75],
                  'reward':{'item037':3,},
                  },
                  {
                  'need':[220],
                  'reward':{'item037':3,},
                  },
                  {
                  'need':[250],
                  'reward':{'item037':3,},
                  },
                  {
                  'need':[200],
                  'reward':{'item037':3,},
                  },
                  {
                  'need':[250],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[300],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[950],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[400],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[600],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[750],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[2200],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[2500],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[2000],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[3000],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[4000],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[5000],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[6000],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[7000],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[9999],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[7500],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[200000],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[220000],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[22000],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[23000],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[24000],
                  'reward':{'item037':5,},
                  },
                    ],
        },
    'task068':{        
         'type':'cum_cutwill',  
         'name':'task068_name',
         'info':'task068_info',
         'explain_info':'task068_einfo',
         'index':'3_28',
         'task':[ 
                  {
                  'need':[2],
                  'reward':{'item037':2,},
                  },
                  {
                  'need':[4],
                  'reward':{'item037':2,},
                  },
                  {
                  'need':[8],
                  'reward':{'item037':2,},
                  },
                  {
                  'need':[22],
                  'reward':{'item037':3,},
                  },
                  {
                  'need':[26],
                  'reward':{'item037':3,},
                  },
                  {
                  'need':[20],
                  'reward':{'item037':3,},
                  },
                  {
                  'need':[25],
                  'reward':{'item037':3,},
                  },
                  {
                  'need':[30],
                  'reward':{'item037':3,},
                  },
                  {
                  'need':[95],
                  'reward':{'item037':3,},
                  },
                  {
                  'need':[40],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[50],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[60],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[70],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[80],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[2000],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[220],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[240],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[260],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[280],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[200],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[220],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[240],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[260],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[280],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[300],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[320],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[340],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[360],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[380],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[400],
                  'reward':{'item037':5,},
                  },
                    ],
        },
    'task070':{       
         'type':'cum_estate',  
         'name':'task070_name',
         'info':'task070_info',
         'explain_info':'task070_einfo',
         'index':'4_0',
         'task':[ 
                  {
                  'need':[ 3,'all','all'],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[ 5,'all','all'],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[ 200,'all','all'],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[ 25,'all','all'],
                  'reward':{'coin':200,},
                  },
                  {
                  'need':[20,'all','all'],
                  'reward':{'coin':200,},
                  },
                  {
                  'need':[25,'all','all'],
                  'reward':{'coin':200,},
                  },
                  {
                  'need':[30,'all','all'],
                  'reward':{'coin':200,},
                  },
                  {
                  'need':[95,'all','all'],
                  'reward':{'coin':200,},
                  },
                    ],
        },
    'task072':{        
         'type':'cum_estate',  
         'name':'task072_name',
         'info':'task072_info',
         'explain_info':'task072_einfo',
         'index':'4_2',
         'task':[ 
                  {
                  'need':[ 2,2,'2'],
                  'reward':{'item069':2,},
                  },
                  {
                  'need':[ 2,2,'2'],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[ 2,3,'2'],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[ 2,4,'2'],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[ 2,5,'2'],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[ 2,6,'2'],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[ 2,7,'2'],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[ 2,8,'2'],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[ 2,9,'2'],
                  'reward':{'gold':5000,},
                  },
                    ],
        },
    'task072':{        
         'type':'cum_estate_active',  
         'name':'task072_name',
         'info':'task072_info',
         'explain_info':'task072_einfo',
         'index':'4_2',
         'task':[ 
                  {
                  'need':[2,'2'],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[3,'2'],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[5,'2'],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[8,'2'],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[22,'2'],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[26,'2'],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[20,'2'],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[30,'2'],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[40,'2'],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[60,'2'],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[75,'2'],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[220,'2'],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[260,'2'],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[200,'2'],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[250,'2'],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[300,'2'],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[950,'2'],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[400,'2'],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[450,'2'],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[500,'2'],
                  'reward':{'gold':20000,},
                  },
                    ],
        },
    'task073':{       
         'type':'cum_estate',  
         'name':'task073_name',
         'info':'task073_info',
         'explain_info':'task073_einfo',
         'index':'4_3',
         'task':[ 
                  {
                  'need':[ 2,2,'2'],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[ 2,2,'2'],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[ 2,3,'2'],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[ 2,4,'2'],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[ 2,5,'2'],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[ 2,6,'2'],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[ 2,7,'2'],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[ 2,8,'2'],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[ 2,9,'2'],
                  'reward':{'gold':5000,},
                  },
                    ],
        },
    'task074':{       
         'type':'cum_estate',  
         'name':'task074_name',
         'info':'task074_info',
         'explain_info':'task074_einfo',
         'index':'4_4',
         'task':[ 
                  {
                  'need':[ 2,2,'3'],
                  'reward':{'item073':2,},
                  },
                  {
                  'need':[ 2,2,'3'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[ 2,3,'3'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[ 2,4,'3'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[ 2,5,'3'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[ 2,6,'3'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[ 2,7,'3'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[ 2,8,'3'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[ 2,9,'3'],
                  'reward':{'food':22500,},
                  },
                    ],
        },
    'task075':{        
         'type':'cum_estate_active',  
         'name':'task075_name',
         'info':'task075_info',
         'explain_info':'task075_einfo',
         'index':'4_5',
         'task':[ 
                  {
                  'need':[2,'3'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[3,'3'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[5,'3'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[8,'3'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[22,'3'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[26,'3'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[20,'3'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[30,'3'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[40,'3'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[60,'3'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[75,'3'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[220,'3'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[260,'3'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[200,'3'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[250,'3'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[300,'3'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[950,'3'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[400,'3'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[450,'3'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[500,'3'],
                  'reward':{'food':5000,},
                  },
                    ],
        },
    'task076':{        
         'type':'cum_estate',  
         'name':'task076_name',
         'info':'task076_info',
         'explain_info':'task076_einfo',
         'index':'4_6',
         'task':[ 
                  {
                  'need':[ 2,2,'4'],
                  'reward':{'item072':2,},
                  },
                  {
                  'need':[ 2,2,'4'],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[ 2,3,'4'],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[ 2,4,'4'],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[ 2,5,'4'],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[ 2,6,'4'],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[ 2,7,'4'],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[ 2,8,'4'],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[ 2,9,'4'],
                  'reward':{'wood':22500,},
                  },
                    ],
        },
    'task077':{        
         'type':'cum_estate_active',  
         'name':'task077_name',
         'info':'task077_info',
         'explain_info':'task077_einfo',
         'index':'4_7',
         'task':[ 
                  {
                  'need':[2,'4'],
                  'reward':{'wood':5000,},
                  },
                  {
                  'need':[3,'4'],
                  'reward':{'wood':5000,},
                  },
                  {
                  'need':[5,'4'],
                  'reward':{'wood':5000,},
                  },
                  {
                  'need':[8,'4'],
                  'reward':{'wood':5000,},
                  },
                  {
                  'need':[22,'4'],
                  'reward':{'wood':5000,},
                  },
                  {
                  'need':[26,'4'],
                  'reward':{'wood':5000,},
                  },
                  {
                  'need':[20,'4'],
                  'reward':{'wood':5000,},
                  },
                  {
                  'need':[30,'4'],
                  'reward':{'wood':5000,},
                  },
                  {
                  'need':[40,'4'],
                  'reward':{'wood':5000,},
                  },
                  {
                  'need':[60,'4'],
                  'reward':{'wood':5000,},
                  },
                  {
                  'need':[75,'4'],
                  'reward':{'wood':5000,},
                  },
                  {
                  'need':[220,'4'],
                  'reward':{'wood':5000,},
                  },
                  {
                  'need':[260,'4'],
                  'reward':{'wood':5000,},
                  },
                  {
                  'need':[200,'4'],
                  'reward':{'wood':5000,},
                  },
                  {
                  'need':[250,'4'],
                  'reward':{'wood':5000,},
                  },
                  {
                  'need':[300,'4'],
                  'reward':{'wood':5000,},
                  },
                  {
                  'need':[950,'4'],
                  'reward':{'wood':5000,},
                  },
                  {
                  'need':[400,'4'],
                  'reward':{'wood':5000,},
                  },
                  {
                  'need':[450,'4'],
                  'reward':{'wood':5000,},
                  },
                  {
                  'need':[500,'4'],
                  'reward':{'wood':5000,},
                  },
                    ],
        },
    'task078':{        
         'type':'cum_estate',  
         'name':'task078_name',
         'info':'task078_info',
         'explain_info':'task078_einfo',
         'index':'4_8',
         'task':[ 
                  {
                  'need':[ 2,2,'5'],
                  'reward':{'item074':2,},
                  },
                  {
                  'need':[ 2,2,'5'],
                  'reward':{'iron':22500,},
                  },
                  {
                  'need':[ 2,3,'5'],
                  'reward':{'iron':22500,},
                  },
                  {
                  'need':[ 2,4,'5'],
                  'reward':{'iron':22500,},
                  },
                  {
                  'need':[ 2,5,'5'],
                  'reward':{'iron':22500,},
                  },
                  {
                  'need':[ 2,6,'5'],
                  'reward':{'iron':22500,},
                  },
                  {
                  'need':[ 2,7,'5'],
                  'reward':{'iron':22500,},
                  },
                  {
                  'need':[ 2,8,'5'],
                  'reward':{'iron':22500,},
                  },
                  {
                  'need':[ 2,9,'5'],
                  'reward':{'iron':22500,},
                  },
                    ],
        },
    'task079':{       
         'type':'cum_estate_active',  
         'name':'task079_name',
         'info':'task079_info',
         'explain_info':'task079_einfo',
         'index':'4_9',
         'task':[ 
                  {
                  'need':[2,'5'],
                  'reward':{'iron':5000,},
                  },
                  {
                  'need':[3,'5'],
                  'reward':{'iron':5000,},
                  },
                  {
                  'need':[5,'5'],
                  'reward':{'iron':5000,},
                  },
                  {
                  'need':[8,'5'],
                  'reward':{'iron':5000,},
                  },
                  {
                  'need':[22,'5'],
                  'reward':{'iron':5000,},
                  },
                  {
                  'need':[26,'5'],
                  'reward':{'iron':5000,},
                  },
                  {
                  'need':[20,'5'],
                  'reward':{'iron':5000,},
                  },
                  {
                  'need':[30,'5'],
                  'reward':{'iron':5000,},
                  },
                  {
                  'need':[40,'5'],
                  'reward':{'iron':5000,},
                  },
                  {
                  'need':[60,'5'],
                  'reward':{'iron':5000,},
                  },
                  {
                  'need':[75,'5'],
                  'reward':{'iron':5000,},
                  },
                  {
                  'need':[220,'5'],
                  'reward':{'iron':5000,},
                  },
                  {
                  'need':[260,'5'],
                  'reward':{'iron':5000,},
                  },
                  {
                  'need':[200,'5'],
                  'reward':{'iron':5000,},
                  },
                  {
                  'need':[250,'5'],
                  'reward':{'iron':5000,},
                  },
                  {
                  'need':[300,'5'],
                  'reward':{'iron':5000,},
                  },
                  {
                  'need':[950,'5'],
                  'reward':{'iron':5000,},
                  },
                  {
                  'need':[400,'5'],
                  'reward':{'iron':5000,},
                  },
                  {
                  'need':[450,'5'],
                  'reward':{'iron':5000,},
                  },
                  {
                  'need':[500,'5'],
                  'reward':{'iron':5000,},
                  },
                    ],
        },
    'task080':{       
         'type':'cum_estate',  
         'name':'task080_name',
         'info':'task080_info',
         'explain_info':'task080_einfo',
         'index':'4_200',
         'task':[ 
                  {
                  'need':[ 2,2,'6'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[ 2,2,'6'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[ 2,3,'6'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[ 2,4,'6'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[ 2,5,'6'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[ 2,6,'6'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[ 2,7,'6'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[ 2,8,'6'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[ 2,9,'6'],
                  'reward':{'food':22500,},
                  },
                    ],
        },
    'task082':{        
         'type':'cum_estate_active',  
         'name':'task082_name',
         'info':'task082_info',
         'explain_info':'task082_einfo',
         'index':'4_22',
         'task':[ 
                  {
                  'need':[2,'6'],
                  'reward':{'item037':2,},
                  },
                  {
                  'need':[3,'6'],
                  'reward':{'item037':2,},
                  },
                  {
                  'need':[5,'6'],
                  'reward':{'item037':2,},
                  },
                  {
                  'need':[8,'6'],
                  'reward':{'item037':3,},
                  },
                  {
                  'need':[22,'6'],
                  'reward':{'item037':3,},
                  },
                  {
                  'need':[26,'6'],
                  'reward':{'item037':3,},
                  },
                  {
                  'need':[20,'6'],
                  'reward':{'item037':3,},
                  },
                  {
                  'need':[30,'6'],
                  'reward':{'item037':3,},
                  },
                  {
                  'need':[40,'6'],
                  'reward':{'item037':3,},
                  },
                  {
                  'need':[60,'6'],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[75,'6'],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[220,'6'],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[260,'6'],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[200,'6'],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[250,'6'],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[300,'6'],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[950,'6'],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[400,'6'],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[450,'6'],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[500,'6'],
                  'reward':{'item037':4,},
                  },
                    ],
        },
    'task082':{        
         'type':'cum_visit',  
         'name':'task082_name',
         'info':'task082_info',
         'explain_info':'task082_einfo',
         'index':'4_22',
         'task':[ 
                  {
                  'need':[2],
                  'reward':{'item037':2,},
                  },
                  {
                  'need':[3],
                  'reward':{'item037':2,},
                  },
                  {
                  'need':[5],
                  'reward':{'item037':2,},
                  },
                  {
                  'need':[8],
                  'reward':{'item037':3,},
                  },
                  {
                  'need':[22],
                  'reward':{'item037':3,},
                  },
                  {
                  'need':[26],
                  'reward':{'item037':3,},
                  },
                  {
                  'need':[20],
                  'reward':{'item037':3,},
                  },
                  {
                  'need':[30],
                  'reward':{'item037':3,},
                  },
                  {
                  'need':[40],
                  'reward':{'item037':3,},
                  },
                  {
                  'need':[60],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[75],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[220],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[260],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[200],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[250],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[300],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[950],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[400],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[450],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[500],
                  'reward':{'item037':4,},
                  },
                  {
                  'need':[750],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[600],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[650],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[700],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[750],
                  'reward':{'item037':5,},
                  },
                  {
                  'need':[800],
                  'reward':{'item037':5,},
                  },
                    ],
        },
    'task083':{        
         'type':'cum_train_soldier',  
         'name':'task083_name',
         'info':'task083_info',
         'explain_info':'task083_einfo',
         'index':'4_23',
         'task':[ 
                  {
                  'need':[200000,'all'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[20000,'all'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[40000,'all'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[60000,'all'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[99975,'all'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[2000000,'all'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[220000,'all'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[240000,'all'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[260000,'all'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[299975,'all'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[200000,'all'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[220000,'all'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[240000,'all'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[260000,'all'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[299975,'all'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[300000,'all'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[950000,'all'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[400000,'all'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[450000,'all'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[500000,'all'],
                  'reward':{'food':5000,},
                  },
                  {
                  'need':[750000,'all'],
                  'reward':{'food':20000,},
                  },
                  {
                  'need':[600000,'all'],
                  'reward':{'food':20000,},
                  },
                  {
                  'need':[650000,'all'],
                  'reward':{'food':20000,},
                  },
                  {
                  'need':[700000,'all'],
                  'reward':{'food':20000,},
                  },
                  {
                  'need':[750000,'all'],
                  'reward':{'food':20000,},
                  },
                  {
                  'need':[999750,'all'],
                  'reward':{'food':20000,},
                  },
                  {
                  'need':[850000,'all'],
                  'reward':{'food':20000,},
                  },
                  {
                  'need':[750000,'all'],
                  'reward':{'food':20000,},
                  },
                  {
                  'need':[950000,'all'],
                  'reward':{'food':20000,},
                  },
                  {
                  'need':[20000000,'all'],
                  'reward':{'food':20000,},
                  },
                  {
                  'need':[22000000,'all'],
                  'reward':{'food':20000,},
                  },
                  {
                  'need':[2200000,'all'],
                  'reward':{'food':20000,},
                  },
                  {
                  'need':[2300000,'all'],
                  'reward':{'food':20000,},
                  },
                  {
                  'need':[2400000,'all'],
                  'reward':{'food':20000,},
                  },
                  {
                  'need':[2500000,'all'],
                  'reward':{'food':20000,},
                  },
                  {
                  'need':[2600000,'all'],
                  'reward':{'food':20000,},
                  },
                  {
                  'need':[2700000,'all'],
                  'reward':{'food':20000,},
                  },
                  {
                  'need':[2999750,'all'],
                  'reward':{'food':20000,},
                  },
                  {
                  'need':[2750000,'all'],
                  'reward':{'food':20000,},
                  },
                  {
                  'need':[2000000,'all'],
                  'reward':{'food':20000,},
                  },
                  {
                  'need':[3000000,'all'],
                  'reward':{'food':20000,},
                  },
                    ],
        },
    'task084':{        
         'type':'cum_frequents',  
         'name':'task084_name',
         'info':'task084_info',
         'explain_info':'task084_einfo',
         'index':'4_24',
         'task':[ 
                  {
                  'need':[5],
                  'reward':{'merit':50,},
                  },
                  {
                  'need':[200],
                  'reward':{'merit':50,},
                  },
                  {
                  'need':[20],
                  'reward':{'merit':50,},
                  },
                  {
                  'need':[30],
                  'reward':{'merit':50,},
                  },
                  {
                  'need':[40],
                  'reward':{'merit':50,},
                  },
                  {
                  'need':[60],
                  'reward':{'merit':50,},
                  },
                  {
                  'need':[80],
                  'reward':{'merit':50,},
                  },
                  {
                  'need':[2000],
                  'reward':{'merit':50,},
                  },
                  {
                  'need':[250],
                  'reward':{'merit':50,},
                  },
                  {
                  'need':[200],
                  'reward':{'merit':50,},
                  },
                  {
                  'need':[300],
                  'reward':{'merit':50,},
                  },
                  {
                  'need':[400],
                  'reward':{'merit':50,},
                  },
                  {
                  'need':[600],
                  'reward':{'merit':50,},
                  },
                  {
                  'need':[800],
                  'reward':{'merit':50,},
                  },
                  {
                  'need':[20000],
                  'reward':{'merit':50,},
                  },
                  {
                  'need':[2500],
                  'reward':{'merit':50,},
                  },
                  {
                  'need':[2000],
                  'reward':{'merit':50,},
                  },
                  {
                  'need':[2500],
                  'reward':{'merit':50,},
                  },
                  {
                  'need':[3000],
                  'reward':{'merit':50,},
                  },
                  {
                  'need':[9500],
                  'reward':{'merit':50,},
                  },
                  {
                  'need':[4000],
                  'reward':{'merit':50,},
                  },
                  {
                  'need':[4500],
                  'reward':{'merit':50,},
                  },
                  {
                  'need':[5000],
                  'reward':{'merit':50,},
                  },
                  {
                  'need':[7500],
                  'reward':{'merit':50,},
                  },
                  {
                  'need':[6000],
                  'reward':{'merit':50,},
                  },
                  {
                  'need':[6500],
                  'reward':{'merit':50,},
                  },
                  {
                  'need':[7000],
                  'reward':{'merit':50,},
                  },
                  {
                  'need':[7500],
                  'reward':{'merit':50,},
                  },
                    ],
        },
    'task085':{         
         'type':'cum_kill',  
         'name':'task085_name',
         'info':'task085_info',
         'explain_info':'task085_einfo',
         'index':'4_25',
         'task':[ 
                  {
                  'need':[6000],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[7500],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[22000],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[25000],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[20000],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[25000],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[30000],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[40000],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[60000],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[75000],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[220000],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[250000],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[200000],
                  'reward':{'merit':200,},
                  },
                  {
                  'need':[250000],
                  'reward':{'merit':200,},
                  },
                  {
                  'need':[300000],
                  'reward':{'merit':200,},
                  },
                  {
                  'need':[950000],
                  'reward':{'merit':200,},
                  },
                  {
                  'need':[400000],
                  'reward':{'merit':200,},
                  },
                  {
                  'need':[500000],
                  'reward':{'merit':200,},
                  },
                  {
                  'need':[600000],
                  'reward':{'merit':200,},
                  },
                  {
                  'need':[700000],
                  'reward':{'merit':200,},
                  },
                  {
                  'need':[999750],
                  'reward':{'merit':200,},
                  },
                  {
                  'need':[750000],
                  'reward':{'merit':200,},
                  },
                  {
                  'need':[20000000],
                  'reward':{'merit':300,},
                  },
                  {
                  'need':[22000000],
                  'reward':{'merit':300,},
                  },
                  {
                  'need':[2200000],
                  'reward':{'merit':300,},
                  },
                  {
                  'need':[2300000],
                  'reward':{'merit':300,},
                  },
                  {
                  'need':[2400000],
                  'reward':{'merit':300,},
                  },
                  {
                  'need':[2500000],
                  'reward':{'merit':300,},
                  },
                    ],
        },
    'task086':{         
         'type':'com_sand',  
         'name':'task086_name',
         'info':'task086_info',
         'explain_info':'task086_einfo',
         'index':'4_26',
         'task':[ 
                  {
                  'need':[2,'chapter002','battle003','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter002','battle006','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter002','battle009','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter002','battle022','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter002','battle025','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter002','battle028','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter002','battle022','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter002','battle024','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter003','battle027','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter003','battle030','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter003','battle033','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter003','battle036','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter004','battle039','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter004','battle042','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter004','battle045','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter004','battle048','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter005','battle052','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter005','battle054','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter005','battle057','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter005','battle060','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter006','battle063','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter006','battle066','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter006','battle069','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter006','battle072','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter007','battle075','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter007','battle078','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter007','battle082','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter007','battle084','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter008','battle087','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter008','battle075','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter008','battle093','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter008','battle096','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter009','battle099','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter009','battle2002','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter009','battle2005','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter009','battle2008','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter0200','battle222','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter0200','battle224','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter0200','battle227','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter0200','battle220','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter022','battle223','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter022','battle226','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter022','battle229','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter022','battle232','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter022','battle295','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter022','battle238','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter022','battle242','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter022','battle244','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter023','battle247','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter023','battle250','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter023','battle253','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter023','battle256','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter024','battle259','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter024','battle262','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter024','battle265','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter024','battle268','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter025','battle272','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter025','battle274','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter025','battle277','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter025','battle280','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter026','battle283','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter026','battle286','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter026','battle289','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter026','battle292','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter027','battle295','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter027','battle298','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter027','battle202','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter027','battle204','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter028','battle207','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter028','battle2200','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter028','battle223','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter028','battle226','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter029','battle229','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter029','battle222','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter029','battle225','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter029','battle228','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter020','battle232','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter020','battle234','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter020','battle237','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter020','battle240','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter022','battle243','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter022','battle246','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter022','battle249','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter022','battle252','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter022','battle275','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter022','battle258','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter022','battle262','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter022','battle264','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter023','battle267','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter023','battle270','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter023','battle273','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter023','battle276','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter024','battle279','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter024','battle282','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter024','battle285','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter024','battle288','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter025','battle292','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter025','battle294','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter025','battle297','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter025','battle300','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter026','battle303','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter026','battle306','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter026','battle309','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter026','battle322','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter027','battle325','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter027','battle328','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter027','battle322','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter027','battle324','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter028','battle327','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter028','battle330','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter028','battle333','all'],
                  'reward':{'food':22500,},
                  },
                  {
                  'need':[2,'chapter028','battle336','all'],
                  'reward':{'food':22500,},
                  },
                    ],
        },
    'task087':{       
         'type':'com_sand',  
         'name':'task087_name',
         'info':'task087_info',
         'explain_info':'task087_einfo',
         'index':'4_27',
         'task':[ 
                  {
                  'need':[ 2,'chapter002','battle003',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[ 2,'chapter002','battle006',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[ 2,'chapter002','battle009',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[ 2,'chapter002','battle022',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[ 2,'chapter002','battle025',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[ 2,'chapter002','battle028',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[ 2,'chapter002','battle022',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[ 2,'chapter002','battle024',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[ 2,'chapter003','battle027',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[ 2,'chapter003','battle030',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[ 2,'chapter003','battle033',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[ 2,'chapter003','battle036',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[ 2,'chapter004','battle039',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[ 2,'chapter004','battle042',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[ 2,'chapter004','battle045',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[ 2,'chapter004','battle048',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[ 2,'chapter005','battle052',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[ 2,'chapter005','battle054',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter005','battle057',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter005','battle060',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter006','battle063',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter006','battle066',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter006','battle069',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter006','battle072',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter007','battle075',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter007','battle078',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter007','battle082',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter007','battle084',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter008','battle087',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter008','battle075',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter008','battle093',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter008','battle096',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter009','battle099',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter009','battle2002',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter009','battle2005',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter009','battle2008',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter0200','battle222',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter0200','battle224',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter0200','battle227',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter0200','battle220',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter022','battle223',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter022','battle226',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter022','battle229',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter022','battle232',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter022','battle295',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter022','battle238',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter022','battle242',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter022','battle244',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter023','battle247',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter023','battle250',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter023','battle253',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter023','battle256',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter024','battle259',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter024','battle262',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter024','battle265',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter024','battle268',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter025','battle272',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter025','battle274',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter025','battle277',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter025','battle280',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter026','battle283',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter026','battle286',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter026','battle289',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter026','battle292',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter027','battle295',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter027','battle298',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter027','battle202',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter027','battle204',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter028','battle207',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter028','battle2200',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter028','battle223',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter028','battle226',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter029','battle229',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter029','battle222',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter029','battle225',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter029','battle228',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter020','battle232',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter020','battle234',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter020','battle237',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter020','battle240',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter022','battle243',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter022','battle246',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter022','battle249',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter022','battle252',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter022','battle275',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter022','battle258',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter022','battle262',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter022','battle264',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter023','battle267',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter023','battle270',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter023','battle273',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter023','battle276',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter024','battle279',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter024','battle282',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter024','battle285',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter024','battle288',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter025','battle292',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter025','battle294',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter025','battle297',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter025','battle300',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter026','battle303',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter026','battle306',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter026','battle309',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter026','battle322',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter027','battle325',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter027','battle328',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter027','battle322',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter027','battle324',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter028','battle327',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter028','battle330',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter028','battle333',3],
                  'reward':{'iron':7500,},
                  },
                  {
                  'need':[2,'chapter028','battle336',3],
                  'reward':{'iron':7500,},
                  },
                    ],
        },
    'task088':{         
         'type':'cum_both',  
         'name':'task088_name',
         'info':'task088_info',
         'explain_info':'task088_einfo',
         'index':'4_28',
         'task':[ 
                  {
                  'need':[2],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[3],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[5],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[200],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[20],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[40],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[60],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[80],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[2000],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[250],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[200],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[250],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[300],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[400],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[500],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[600],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[700],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[800],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[750],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[20000],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[22000],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[2200],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[2300],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[2400],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[2500],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[2000],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[2500],
                  'reward':{'gold':20000,},
                  },
                  {
                  'need':[3000],
                  'reward':{'gold':20000,},
                  },
                    ],
        },
    'task089':{        
         'type':'rank_both',  
         'name':'task089_name',
         'info':'task089_info',
         'explain_info':'task089_einfo',
         'index':'4_29',
         'task':[ 
                  {
                  'need':[2,2600],
                  'reward':{'gold':3000,},
                  },
                  {
                  'need':[2,2200],
                  'reward':{'gold':3000,},
                  },
                  {
                  'need':[2,800],
                  'reward':{'gold':3000,},
                  },
                  {
                  'need':[2,500],
                  'reward':{'gold':3000,},
                  },
                  {
                  'need':[2,300],
                  'reward':{'gold':3000,},
                  },
                  {
                  'need':[2,200],
                  'reward':{'gold':3000,},
                  },
                  {
                  'need':[2,2000],
                  'reward':{'coin':20,},
                  },
                  {
                  'need':[2,20],
                  'reward':{'coin':30,},
                  },
                  {
                  'need':[2,200],
                  'reward':{'coin':50,},
                  },
                  {
                  'need':[2,5],
                  'reward':{'coin':2000,},
                  },
                  {
                  'need':[2,3],
                  'reward':{'coin':200,},
                  },
                  {
                  'need':[2,2],
                  'reward':{'coin':300,},
                  },
                    ],
        },
    'task075':{        
         'type':'cum_gtask',  
         'name':'task075_name',
         'info':'task075_info',
         'explain_info':'task075_einfo',
         'index':'5_0',
         'task':[ 
                  {
                  'need':[3],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[5],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[200],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[25],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[20],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[30],
                  'reward':{'coin':50,},
                  },
                  {
                  'need':[40],
                  'reward':{'coin':50,},
                  },
                  {
                  'need':[60],
                  'reward':{'coin':50,},
                  },
                  {
                  'need':[80],
                  'reward':{'coin':50,},
                  },
                  {
                  'need':[220],
                  'reward':{'coin':50,},
                  },
                  {
                  'need':[260],
                  'reward':{'coin':50,},
                  },
                  {
                  'need':[200],
                  'reward':{'coin':2000,},
                  },
                  {
                  'need':[250],
                  'reward':{'coin':2000,},
                  },
                  {
                  'need':[300],
                  'reward':{'coin':2000,},
                  },
                  {
                  'need':[400],
                  'reward':{'coin':2000,},
                  },
                  {
                  'need':[500],
                  'reward':{'coin':2000,},
                  },
                  {
                  'need':[600],
                  'reward':{'coin':2000,},
                  },
                  {
                  'need':[700],
                  'reward':{'coin':2000,},
                  },
                  {
                  'need':[800],
                  'reward':{'coin':2000,},
                  },
                  {
                  'need':[750],
                  'reward':{'coin':2000,},
                  },
                  {
                  'need':[20000],
                  'reward':{'coin':2000,},
                  },
                    ],
        },
    'task092':{        
         'type':'rat_gtask',  
         'name':'task092_name',
         'info':'task092_info',
         'explain_info':'task092_einfo',
         'hide':2,  
         'index':'5_2',
         'task':[ 
                  {
                  'need':[2,'gtask_eval_title05'],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[3,'gtask_eval_title05'],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[5,'gtask_eval_title05'],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[8,'gtask_eval_title05'],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[22,'gtask_eval_title05'],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[26,'gtask_eval_title05'],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[20,'gtask_eval_title05'],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[25,'gtask_eval_title05'],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[30,'gtask_eval_title05'],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[40,'gtask_eval_title05'],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[50,'gtask_eval_title05'],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[60,'gtask_eval_title05'],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[70,'gtask_eval_title05'],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[80,'gtask_eval_title05'],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[2000,'gtask_eval_title05'],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[220,'gtask_eval_title05'],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[250,'gtask_eval_title05'],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[200,'gtask_eval_title05'],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[250,'gtask_eval_title05'],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[300,'gtask_eval_title05'],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[950,'gtask_eval_title05'],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[400,'gtask_eval_title05'],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[450,'gtask_eval_title05'],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[500,'gtask_eval_title05'],
                  'reward':{'merit':2000,},
                  },
                    ],
        },
    'task092':{        
         'type':'get_office',  
         'name':'task092_name',
         'info':'task092_info',
         'explain_info':'task092_einfo',
         'index':'5_2',
         'task':[ 
                  {
                  'need':[2,2],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[2,2],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[2,3],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[2,4],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[2,5],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[2,6],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[2,7],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[2,8],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[2,9],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[2,200],
                  'reward':{'gold':5000,},
                  },
                  {
                  'need':[2,22],
                  'reward':{'gold':5000,},
                  },
                    ],
        },
    'task093':{         
         'type':'open_privilege',  
         'name':'task093_name',
         'info':'task093_info',
         'explain_info':'task093_einfo',
         'index':'5_3',
         'task':[ 
                  {
                  'need':[2],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[4],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[6],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[8],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[200],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[22],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[24],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[26],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[28],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[20],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[22],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[24],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[26],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[28],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[30],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[32],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[34],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[36],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[38],
                  'reward':{'merit':2000,},
                  },
                  {
                  'need':[40],
                  'reward':{'merit':2000,},
                  },
                    ],
        },
    'task094':{        
         'type':'degree_build',  
         'name':'task094_name',
         'info':'task094_info',
         'explain_info':'task094_einfo',
         'index':'5_4',
         'task':[ 
                  {
                  'need':[200],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[20],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[30],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[40],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[50],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[2000],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[200],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[300],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[500],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[800],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[2200],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[2600],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[2000],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[2500],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[3000],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[4000],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[5000],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[6000],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[7000],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[9999],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[200000],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[22000],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[25000],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[29999],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[220000],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[24000],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[27000],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[30000],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[95000],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[40000],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[50000],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[60000],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[70000],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[99975],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[75000],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[2000000],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[250000],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[200000],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[250000],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[300000],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[950000],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[400000],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[450000],
                  'reward':{'wood':22500,},
                  },
                  {
                  'need':[500000],
                  'reward':{'wood':22500,},
                  },
                    ],
        },

},
    'country':{                          
    'country_2':{      
         'type':'occupation_city',  
         'name':'country_2_name',
         'info':'country_2_info',
         'explain_info':'country_2_einfo',
         'complete_info':'country_2_cinfo',
         'index':'6_0',
         'need':[2,[['242','260','234'],['33','70','26'],['348','340','364']]],
         'reward':{'merit':500,'coin':50,},
        },
    'country_2':{      
         'type':'city_build_up',  
         'name':'country_2_name',
         'info':'country_2_info',
         'explain_info':'country_2_einfo',
         'complete_info':'country_2_cinfo',
         'index':'6_2',
         'need':[2,[['242','260','234'],['33','70','26'],['348','340','364']],['b02'],3],
         'reward':{'merit':520,'gold':200000,},
        },
    'country_3':{      
         'type':'occupation_city',  
         'name':'country_3_name',
         'info':'country_3_info',
         'explain_info':'country_3_einfo',
         'complete_info':'country_3_cinfo',
         'index':'6_2',
         'need':[2,[['259','257','262'],['25','23','22'],['347','338','228']]],
         'reward':{'merit':540,'coin':50,},
        },
    'country_4':{      
         'type':'city_build_up',  
         'name':'country_4_name',
         'info':'country_4_info',
         'explain_info':'country_4_einfo',
         'complete_info':'country_4_cinfo',
         'index':'6_3',
         'need':[2,[['259','257','262'],['25','23','22'],['347','338','228']],['b03'],3],
         'reward':{'merit':560,'gold':200000,},
        },
    'country_5':{      
         'type':'occupation_city',  
         'name':'country_5_name',
         'info':'country_5_info',
         'explain_info':'country_5_einfo',
         'complete_info':'country_5_cinfo',
         'index':'6_4',
         'need':[2,[['264'],['275'],['222']]],
         'reward':{'merit':20000,'coin':2000,},
        },
    'country_6':{       
         'type':'city_build_up',  
         'name':'country_6_name',
         'info':'country_6_info',
         'explain_info':'country_6_einfo',
         'complete_info':'country_6_cinfo',
         'index':'6_5',
         'need':[2,[['264'],['275'],['222']],['b02','b02','b03'],5],
         'reward':{'merit':600,'item022':5,},
        },
    'country_7':{      
         'type':'country_build',  
         'name':'country_7_name',
         'info':'country_7_info',
         'explain_info':'country_7_einfo',
         'complete_info':'country_7_cinfo',
         'index':'6_6',
         'need':[5,5,'b07'],
         'reward':{'merit':620,'gold':25000,},
        },
    'country_8':{      
         'type':'country_build',  
         'name':'country_8_name',
         'info':'country_8_info',
         'explain_info':'country_8_einfo',
         'complete_info':'country_8_cinfo',
         'index':'6_7',
         'need':[5,3,'b05'],
         'reward':{'merit':640,'gold':25000,},
        },
    'country_9':{      
         'type':'cum_estate',  
         'name':'country_9_name',
         'info':'country_9_info',
         'explain_info':'country_9_einfo',
         'complete_info':'country_9_cinfo',
         'persona_info':'country_9_pinfo',
         'is_persona':2,
         'index':'6_8',
         'need':[200,3,'all'],
         'reward':{'merit':660,'item072':3,},
        },
    'country_200':{      
         'type':'occupation_city',  
         'name':'country_200_name',
         'info':'country_200_info',
         'explain_info':'country_200_einfo',
         'complete_info':'country_200_cinfo',
         'index':'6_9',
         'need':[2,[['224','238','237'],['74','95','37'],['224','388','375']]],
         'reward':{'merit':680,'coin':50,},
        },
    'country_22':{       
         'type':'country_build',  
         'name':'country_22_name',
         'info':'country_22_info',
         'explain_info':'country_22_einfo',
         'complete_info':'country_22_cinfo',
         'index':'6_200',
         'need':[5,3,'b06'],
         'reward':{'merit':700,'gold':25000,},
        },
    'country_22':{       
         'type':'cum_country_active',  
         'name':'country_22_name',
         'info':'country_22_info',
         'explain_info':'country_22_einfo',
         'complete_info':'country_22_cinfo',
         'persona_info':'country_22_pinfo',
         'index':'6_22',
         'need':[300,'4'],
         'reward':{'merit':720,'gold':25000,},
        },
    'country_23':{      
         'type':'country_build',  
         'name':'country_23_name',
         'info':'country_23_info',
         'explain_info':'country_23_einfo',
         'complete_info':'country_23_cinfo',
         'index':'6_22',
         'need':[5,5,'b04'],
         'reward':{'merit':740,'gold':25000,},
        },
    'country_24':{      
         'type':'cum_country_trainedsoldiers',  
         'name':'country_24_name',
         'info':'country_24_info',
         'explain_info':'country_24_einfo',
         'complete_info':'country_24_cinfo',
         'persona_info':'country_24_pinfo',
         'index':'6_23',
         'need':[20000000,'all'],
         'reward':{'merit':760,'gold':20000,},
        },
    'country_25':{        
         'type':'occupation_city',  
         'name':'country_25_name',
         'info':'country_25_info',
         'explain_info':'country_25_einfo',
         'complete_info':'country_25_cinfo',
         'index':'6_24',
         'need':[2,[['227','240','223'],['42','20','75'],['375','203','332']]],
         'reward':{'merit':780,'coin':50,},
        },
    'country_26':{      
         'type':'city_build_up',  
         'name':'country_26_name',
         'info':'country_26_info',
         'explain_info':'country_26_einfo',
         'complete_info':'country_26_cinfo',
         'index':'6_25',
         'need':[2,[['227','240','223'],['42','20','75'],['375','203','332']],['b05'],5],
         'reward':{'merit':800,'gold':20000,},
        },
    'country_27':{       
         'type':'city_build_up',  
         'name':'country_27_name',
         'info':'country_27_info',
         'explain_info':'country_27_einfo',
         'complete_info':'country_27_cinfo',
         'index':'6_26',
         'need':[2,[['227','240','223'],['42','20','75'],['375','203','332']],['b07'],5],
         'reward':{'merit':820,'gold':20000,},
        },
    'country_28':{      
         'type':'city_build_up',  
         'name':'country_28_name',
         'info':'country_28_info',
         'explain_info':'country_28_einfo',
         'complete_info':'country_28_cinfo',
         'miracle_info':'country_28_binfo',
         'index':'6_27',
         'need':[2,[['246'],['32'],['953']],['b22','b22','b200'],5],
         'reward':{'merit':2500,'coin':250,},
        },
    'country_29':{       
         'type':'cum_estate_active',  
         'name':'country_29_name',
         'info':'country_29_info',
         'explain_info':'country_29_einfo',
         'complete_info':'country_29_cinfo',
         'persona_info':'country_29_pinfo',
         'is_persona':2,
         'index':'6_28',
         'need':[200,'5'],
         'reward':{'merit':860,'item074':3,},
        },
    'country_20':{      
         'type':'cum_country_trainedsoldiers',  
         'name':'country_20_name',
         'info':'country_20_info',
         'explain_info':'country_20_einfo',
         'complete_info':'country_20_cinfo',
         'persona_info':'country_20_pinfo',
         'index':'6_29',
         'need':[2000000,'all'],
         'reward':{'merit':880,'gold':29999,},
        },
    'country_22':{      
         'type':'cum_country_kill',  
         'name':'country_22_name',
         'info':'country_22_info',
         'explain_info':'country_22_einfo',
         'complete_info':'country_22_cinfo',
         'persona_info':'country_22_pinfo',
         'index':'6_20',
         'need':[5000000],
         'reward':{'merit':750,'item032':200,},
        },
    'country_22':{      
         'type':'occupation_city',  
         'name':'country_22_name',
         'info':'country_22_info',
         'explain_info':'country_22_einfo',
         'complete_info':'country_22_cinfo',
         'index':'6_22',
         'need':[2,[['220'],['47'],['362']]],
         'reward':{'merit':2500,'coin':250,},
        },
    'country_23':{      
         'type':'city_build_up',  
         'name':'country_23_name',
         'info':'country_23_info',
         'explain_info':'country_23_einfo',
         'complete_info':'country_23_cinfo',
         'index':'6_22',
         'need':[2,[['220'],['47'],['362']],['b04','b07','b06'],5],
         'reward':{'merit':940,'item022':5,},
        },
    'country_24':{      
         'type':'city_build_up',  
         'name':'country_24_name',
         'info':'country_24_info',
         'explain_info':'country_24_einfo',
         'complete_info':'country_24_cinfo',
         'index':'6_23',
         'need':[2,[['242','234','236'],['33','95','37'],['224','388','958']],['b03'],5],
         'reward':{'merit':960,'gold':20000,},
        },
    'country_25':{       
         'type':'city_build_up',  
         'name':'country_25_name',
         'info':'country_25_info',
         'explain_info':'country_25_einfo',
         'complete_info':'country_25_cinfo',
         'index':'6_24',
         'need':[2,[['236','237','238'],['37','40','42'],['958','376','375']],['b02'],5],
         'reward':{'merit':980,'gold':20000,},
        },
    'country_26':{       
         'type':'country_build',  
         'name':'country_26_name',
         'info':'country_26_info',
         'explain_info':'country_26_einfo',
         'complete_info':'country_26_cinfo',
         'index':'6_25',
         'need':[5,3,'b22'],
         'reward':{'merit':20000,'gold':25000,},
        },
    'country_27':{      
         'type':'country_build',  
         'name':'country_27_name',
         'info':'country_27_info',
         'explain_info':'country_27_einfo',
         'complete_info':'country_27_cinfo',
         'index':'6_26',
         'need':[5,7,'b07'],
         'reward':{'merit':20040,'gold':25000,},
        },
    'country_28':{       
         'type':'country_build',  
         'name':'country_28_name',
         'info':'country_28_info',
         'explain_info':'country_28_einfo',
         'complete_info':'country_28_cinfo',
         'index':'6_27',
         'need':[5,3,'b200'],
         'reward':{'merit':2500,'gold':25000,},
        },
    'country_29':{      
         'type':'country_build',  
         'name':'country_29_name',
         'info':'country_29_info',
         'explain_info':'country_29_einfo',
         'complete_info':'country_29_cinfo',
         'index':'6_28',
         'need':[5,7,'b05'],
         'reward':{'merit':2220,'gold':25000,},
        },
    'country_30':{       
         'type':'country_build',  
         'name':'country_30_name',
         'info':'country_30_info',
         'explain_info':'country_30_einfo',
         'complete_info':'country_30_cinfo',
         'index':'6_29',
         'need':[5,3,'b22'],
         'reward':{'merit':2260,'gold':25000,},
        },
    'country_32':{      
         'type':'city_build_up',  
         'name':'country_32_name',
         'info':'country_32_info',
         'explain_info':'country_32_einfo',
         'complete_info':'country_32_cinfo',
         'miracle_info':'country_32_binfo',
         'index':'6_30',
         'need':[2,[['264'],['275'],['222']],['b200','b22','b22'],5],
         'reward':{'merit':2000,'coin':2000,},
        },
    'country_32':{       
         'type':'cum_train_soldier',  
         'name':'country_32_name',
         'info':'country_32_info',
         'explain_info':'country_32_einfo',
         'complete_info':'country_32_cinfo',
         'persona_info':'country_32_pinfo',
         'is_persona':2,
         'index':'6_32',
         'need':[2000000,'all'],
         'reward':{'merit':2240,'gold':30000,},
        },
    'country_33':{       
         'type':'country_build',  
         'name':'country_33_name',
         'info':'country_33_info',
         'explain_info':'country_33_einfo',
         'complete_info':'country_33_cinfo',
         'index':'6_32',
         'need':[200,8,'b07'],
         'reward':{'merit':2280,'gold':30000,},
        },
    'country_34':{       
         'type':'country_build',  
         'name':'country_34_name',
         'info':'country_34_info',
         'explain_info':'country_34_einfo',
         'complete_info':'country_34_cinfo',
         'index':'6_33',
         'need':[200,8,'b03'],
         'reward':{'merit':2320,'gold':30000,},
        },
    'country_95':{       
         'type':'country_build',  
         'name':'country_95_name',
         'info':'country_95_info',
         'explain_info':'country_95_einfo',
         'complete_info':'country_95_cinfo',
         'index':'6_34',
         'need':[200,8,'b05'],
         'reward':{'merit':2360,'gold':30000,},
        },
    'country_36':{       
         'type':'cum_country_active',  
         'name':'country_36_name',
         'info':'country_36_info',
         'explain_info':'country_36_einfo',
         'complete_info':'country_36_cinfo',
         'persona_info':'country_36_pinfo',
         'index':'6_95',
         'need':[500,'5'],
         'reward':{'merit':2400,'item074':3,},
        },
    'country_37':{           
         'type':'country_build',  
         'name':'country_37_name',
         'info':'country_37_info',
         'explain_info':'country_37_einfo',
         'complete_info':'country_37_cinfo',
         'index':'6_36',
         'need':[5,5,'b22'],
         'reward':{'merit':2440,'gold':30000,},
        },
    'country_38':{      
         'type':'country_build',  
         'name':'country_38_name',
         'info':'country_38_info',
         'explain_info':'country_38_einfo',
         'complete_info':'country_38_cinfo',
         'index':'6_37',
         'need':[5,5,'b22'],
         'reward':{'merit':2480,'gold':30000,},
        },
    'country_39':{      
         'type':'country_build',  
         'name':'country_39_name',
         'info':'country_39_info',
         'explain_info':'country_39_einfo',
         'complete_info':'country_39_cinfo',
         'index':'6_38',
         'need':[5,5,'b200'],
         'reward':{'merit':2520,'gold':30000,},
        },
    'country_40':{        
         'type':'cum_country_trainedsoldiers',  
         'name':'country_40_name',
         'info':'country_40_info',
         'explain_info':'country_40_einfo',
         'complete_info':'country_40_cinfo',
         'persona_info':'country_40_pinfo',
         'index':'6_39',
         'need':[4000000,'all'],
         'reward':{'merit':2560,'gold':30000,},
        },
    'country_42':{      
         'type':'cum_country_kill',  
         'name':'country_42_name',
         'info':'country_42_info',
         'explain_info':'country_42_einfo',
         'complete_info':'country_42_cinfo',
         'persona_info':'country_42_pinfo',
         'index':'6_40',
         'need':[20000000],
         'reward':{'merit':2600,'item032':200,},
        },
    'country_42':{      
         'type':'country_build',  
         'name':'country_42_name',
         'info':'country_42_info',
         'explain_info':'country_42_einfo',
         'complete_info':'country_42_cinfo',
         'index':'6_42',
         'need':[5,5,'b09'],
         'reward':{'merit':2650,'gold':95000,},
        },
    'country_43':{       
         'type':'country_build',  
         'name':'country_43_name',
         'info':'country_43_info',
         'explain_info':'country_43_einfo',
         'complete_info':'country_43_cinfo',
         'index':'6_42',
         'need':[2,5,'b08'],
         'reward':{'merit':2700,'gold':95000,},
        },
    'country_44':{      
         'type':'country_build',  
         'name':'country_44_name',
         'info':'country_44_info',
         'explain_info':'country_44_einfo',
         'complete_info':'country_44_cinfo',
         'index':'6_43',
         'need':[5,7,'b200'],
         'reward':{'merit':2750,'gold':95000,},
        },
    'country_45':{      
         'type':'country_build',  
         'name':'country_45_name',
         'info':'country_45_info',
         'explain_info':'country_45_einfo',
         'complete_info':'country_45_cinfo',
         'index':'6_44',
         'need':[5,7,'b22'],
         'reward':{'merit':2800,'gold':95000,},
        },
    'country_46':{      
         'type':'country_build',  
         'name':'country_46_name',
         'info':'country_46_info',
         'explain_info':'country_46_einfo',
         'complete_info':'country_46_cinfo',
         'index':'6_45',
         'need':[5,7,'b08'],
         'reward':{'merit':2850,'gold':95000,},
        },
    'country_47':{      
         'type':'country_build',  
         'name':'country_47_name',
         'info':'country_47_info',
         'explain_info':'country_47_einfo',
         'complete_info':'country_47_cinfo',
         'index':'6_46',
         'need':[200,8,'b02'],
         'reward':{'merit':2750,'gold':95000,},
        },
    'country_48':{        
         'type':'cum_country_active',  
         'name':'country_48_name',
         'info':'country_48_info',
         'explain_info':'country_48_einfo',
         'complete_info':'country_48_cinfo',
         'persona_info':'country_48_pinfo',
         'index':'6_47',
         'need':[600,'2'],
         'reward':{'merit':2950,'item069':5,},
        },
    'country_49':{      
         'type':'country_build',  
         'name':'country_49_name',
         'info':'country_49_info',
         'explain_info':'country_49_einfo',
         'complete_info':'country_49_cinfo',
         'index':'6_48',
         'need':[3,5,'b26'],
         'reward':{'merit':2000,'gold':95000,},
        },
    'country_50':{       
         'type':'country_build',  
         'name':'country_50_name',
         'info':'country_50_info',
         'explain_info':'country_50_einfo',
         'complete_info':'country_50_cinfo',
         'index':'6_49',
         'need':[3,5,'b23'],
         'reward':{'merit':2050,'gold':95000,},
        },
    'country_52':{      
         'type':'country_visit',  
         'name':'country_52_name',
         'info':'country_52_info',
         'explain_info':'country_52_einfo',
         'complete_info':'country_52_cinfo',
         'persona_info':'country_52_pinfo',
         'index':'6_50',
         'need':[300],
         'reward':{'merit':22000,'item070':5,},
        },
    'country_52':{       
         'type':'country_build',  
         'name':'country_52_name',
         'info':'country_52_info',
         'explain_info':'country_52_einfo',
         'complete_info':'country_52_cinfo',
         'index':'6_52',
         'need':[3,5,'b27'],
         'reward':{'merit':2250,'gold':95000,},
        },
    'country_53':{      
         'type':'country_build',  
         'name':'country_53_name',
         'info':'country_53_info',
         'explain_info':'country_53_einfo',
         'complete_info':'country_53_cinfo',
         'index':'6_52',
         'need':[3,5,'b25'],
         'reward':{'merit':2200,'gold':95000,},
        },
    'country_54':{      
         'type':'cum_country_active',  
         'name':'country_54_name',
         'info':'country_54_info',
         'explain_info':'country_54_einfo',
         'complete_info':'country_54_cinfo',
         'persona_info':'country_54_pinfo',
         'index':'6_53',
         'need':[600,'6'],
         'reward':{'merit':2250,'item072':5,},
        },
    'country_75':{      
         'type':'country_build',  
         'name':'country_75_name',
         'info':'country_75_info',
         'explain_info':'country_75_einfo',
         'complete_info':'country_75_cinfo',
         'index':'6_54',
         'need':[2,5,'b24'],
         'reward':{'merit':2300,'gold':40000,},
        },
    'country_56':{      
         'type':'country_build',  
         'name':'country_56_name',
         'info':'country_56_info',
         'explain_info':'country_56_einfo',
         'complete_info':'country_56_cinfo',
         'index':'6_75',
         'need':[25,8,'b02'],
         'reward':{'merit':2950,'gold':40000,},
        },
    'country_57':{      
         'type':'cum_country_active',  
         'name':'country_57_name',
         'info':'country_57_info',
         'explain_info':'country_57_einfo',
         'complete_info':'country_57_cinfo',
         'persona_info':'country_57_pinfo',
         'index':'6_56',
         'need':[600,'5'],
         'reward':{'merit':2400,'item074':5,},
        },
    'country_58':{      
         'type':'city_build_up',  
         'name':'country_58_name',
         'info':'country_58_info',
         'explain_info':'country_58_einfo',
         'complete_info':'country_58_cinfo',
         'miracle_info':'country_58_binfo',
         'index':'6_57',
         'need':[2,[['220'],['47'],['362']],['b23','b26','b27'],5],
         'reward':{'merit':9500,'coin':250,},
        },
    'country_59':{       
         'type':'cum_kill',  
         'name':'country_59_name',
         'info':'country_59_info',
         'explain_info':'country_59_einfo',
         'complete_info':'country_59_cinfo',
         'persona_info':'country_59_pinfo',
         'is_persona':2,
         'index':'6_58',
         'need':[2000000],
         'reward':{'merit':2600,'gold':40000,},
        },
    'country_60':{       
         'type':'cum_country_trainedsoldiers',  
         'name':'country_60_name',
         'info':'country_60_info',
         'explain_info':'country_60_einfo',
         'complete_info':'country_60_cinfo',
         'persona_info':'country_60_pinfo',
         'index':'6_59',
         'need':[9997500,'all'],
         'reward':{'merit':2700,'gold':40000,},
        },
    'country_62':{      
         'type':'cum_country_kill',  
         'name':'country_62_name',
         'info':'country_62_info',
         'explain_info':'country_62_einfo',
         'complete_info':'country_62_cinfo',
         'persona_info':'country_62_pinfo',
         'index':'6_60',
         'need':[50000000],
         'reward':{'merit':2800,'item032':200,},
        },
    'country_62':{        
         'type':'cum_country_active',  
         'name':'country_62_name',
         'info':'country_62_info',
         'explain_info':'country_62_einfo',
         'complete_info':'country_62_cinfo',
         'persona_info':'country_62_pinfo',
         'index':'6_62',
         'need':[600,'3'],
         'reward':{'merit':2750,'gold':40000,},
        },
    'country_63':{      
         'type':'country_build',  
         'name':'country_63_name',
         'info':'country_63_info',
         'explain_info':'country_63_einfo',
         'complete_info':'country_63_cinfo',
         'index':'6_62',
         'need':[5,8,'b09'],
         'reward':{'merit':3000,'coin':2000,},
        },
    'country_64':{      
         'type':'country_build',  
         'name':'country_64_name',
         'info':'country_64_info',
         'explain_info':'country_64_einfo',
         'complete_info':'country_64_cinfo',
         'index':'6_63',
         'need':[25,8,'b02'],
         'reward':{'merit':3200,'coin':2000,},
        },
    'country_65':{      
         'type':'country_occupation_city',  
         'name':'country_65_name',
         'info':'country_65_info',
         'explain_info':'country_65_einfo',
         'complete_info':'country_65_cinfo',
         'persona_info':'country_65_pinfo',
         'index':'6_64',
         'need':[50],
         'reward':{'merit':3400,'coin':250,},
        },
    'country_66':{      
         'type':'country_build',  
         'name':'country_66_name',
         'info':'country_66_info',
         'explain_info':'country_66_einfo',
         'complete_info':'country_66_cinfo',
         'index':'6_65',
         'need':[5,8,'b08'],
         'reward':{'merit':3600,'coin':250,},
        },
    'country_67':{      
         'type':'country_build',  
         'name':'country_67_name',
         'info':'country_67_info',
         'explain_info':'country_67_einfo',
         'complete_info':'country_67_cinfo',
         'index':'6_66',
         'need':[5,8,'b24'],
         'reward':{'merit':3800,'coin':200,},
        },
    'country_68':{       
         'type':'country_occupation_city',  
         'name':'country_68_name',
         'info':'country_68_info',
         'explain_info':'country_68_einfo',
         'complete_info':'country_68_cinfo',
         'persona_info':'country_68_pinfo',
         'index':'6_67',
         'need':[80],
         'reward':{'merit':4000,'coin':200,},
        },
    'country_69':{       
         'type':'occupation_city',  
         'name':'country_69_name',
         'info':'country_69_info',
         'explain_info':'country_69_einfo',
         'complete_info':'country_69_cinfo',
         'miracle_info':'country_69_binfo',
         'index':'6_68',
         'need':[2,[['-2'],['-2'],['-2']]],
         'reward':{'merit':4200,'coin':300,},
        },
    'country_70':{      
         'type':'occupation_city',  
         'name':'country_70_name',
         'info':'country_70_info',
         'explain_info':'country_70_einfo',
         'complete_info':'country_70_cinfo',
         'index':'6_69',
         'need':[2,[['62',],['375',],['244',]]],
         'reward':{'merit':5000,'coin':500,},
        },

},}