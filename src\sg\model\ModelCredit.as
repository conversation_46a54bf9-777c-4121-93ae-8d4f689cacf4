package sg.model
{
	import sg.cfg.ConfigServer;
	import sg.manager.ModelManager;
	import sg.net.NetSocket;
	import laya.utils.Handler;
	import sg.net.NetPackage;
	import sg.manager.ViewManager;
	import sg.cfg.ConfigClass;

	/**
	 * 战功model
	 * <AUTHOR>
	public class ModelCredit extends ModelBase {

		public static var sModel:ModelCredit;

		public static function get instance():ModelCredit {
			if(!sModel)
				sModel = new ModelCredit();
			return sModel;
		}

		public function ModelCredit() {
			if(ConfigServer.credit) {
				if(ConfigServer.credit['merge_' + ModelManager.instance.modelUser.mergeNum]) {
					mCfg = ConfigServer.credit['merge_' + ModelManager.instance.modelUser.mergeNum];
				} else {
					mCfg = ConfigServer.credit;
				}
			}
			
		}

		public var mCfg:Object;
		// [uid,uname,head,country,guild_name,year_credit,credit]
		public var mCreditRankArr:Array = [];// 排行榜数据 来自接口get_credit_rank, {"is_year":true}
		
		public var mLv:int;
		public var mCredit:int;
		public var mCreditYear:int;
		public var mYearCredit:int;

		public var mCreditGetGifts:Array;
		public var mIsCreditLvUp:int;// = 0 || 1 || 2
		public var mLastYearCredit:int;
		public var mCreditRoolGiftsNum:int;
		
		/**
		 * credit:Number = 0;            //总的战功
         * year_credit:Number = 0;       //年度战功
         * credit_get_gifts:Array = [];  //已经领取过的奖励
         * credit_lv:Number = 0;         //战功等级
         * credit_year:Number = 0;       //战功年度
         * credit_settle:Array = null;   //战功结算数据
         * is_credit_lv_up:Number = 0;   //战功升级状态1升级，2没升级(调用reset_red_dot_attr接口后清零)
         * last_year_credit:Number;      //上赛季战功
         * credit_rool_gifts_num:Number; //额外的额外的战功奖励个数
		 
		public function updateData(re:Object):void {
			if(re["credit_lv"]) 
				mLv = re["credit_lv"];
			if(re["credit"]) 
				mCredit = re["credit"];
			if(re["credit_year"])
				mCreditYear = re["credit_year"];
			if(re["year_credit"]) 
				mYearCredit = re["year_credit"];

			if(re["credit_get_gifts"]) 
				mCredit = re["credit"];
			if(re["is_credit_lv_up"]) 
				mIsCreditLvUp = re["is_credit_lv_up"];
			if(re["last_year_credit"]) 
				mLastYearCredit = re["last_year_credit"];
			if(re["credit_rool_gifts_num"]) 
				mCreditRoolGiftsNum = re["credit_rool_gifts_num"];
			
		}
		*/

		/**
		 * 战功最大的等级
		 */
		public function maxLv():int {
			return mCfg.clv_up.length;
		}

		// 是否可领奖
		public var isCanGet:Boolean;
		// 可领取的最大索引值
		public var canGetMax:Number;
		// 已领取的最大索引值
		public var alreadyGetMax:Number;

		/**
		 * 获得奖励列表
		 */
		public function getRewardArr():Array {
			var arr:Array = [];
			isCanGet = false;
			canGetMax = -1;
			alreadyGetMax = -1;
			var user:ModelUser = ModelManager.instance.modelUser;
			var _num:Number = user.year_credit;
			var _lv:Number = user.credit_lv;

			var objAdd:Object = {};
			var max:Number = 0;
			var min:Number = 0;

			// 战功奖励
			getRewardArr2(false).forEach(function(obj:*):void {
				arr.push(obj);
			});
			
			//添加一个升级进度
			max = mCfg.clv_up[_lv] ? mCfg.clv_up[_lv] : mCfg.clv_max;
			min = mCfg.clv_first[_lv] * mCfg.clv_first_ratio[mCfg.clv_first_ratio.length-1];
			objAdd["rewardKey"] = "up";
			objAdd["reward"] = null;
			objAdd["credit"] = max;
			objAdd["maxProgress"] = max - min;
			objAdd["valueProgress"] = Math.max(_num - min, 0);
			arr.push(objAdd);
			if(_num >= max) {
				canGetMax += 1;
				alreadyGetMax += 1;
			}
			
			// 额外战功奖励
			getRewardArr2(true).forEach(function(obj:*):void {
				arr.push(obj);
			});

			//添加一个循环奖励
			var objRool:Object = {};
			// 已领取个数
			var user_rool:Number = user.credit_rool_gifts_num;
			var cfg_rool:Array = mCfg.clv_rool_reward[_lv];
			// 进度最小值，就是额外奖励进度的最大值
			var cfg_added:Number = mCfg.clv_added[_lv];
			var cfg_ratio:Array = mCfg.clv_added_ratio;
			min = cfg_added * cfg_ratio[cfg_ratio.length - 1];
			max = cfg_rool[1];
			objRool["rewardKey"] = "rool";
			objRool["reward"] = ModelManager.instance.modelProp.getRewardProp(cfg_rool[0])[0];//cfg_rool[0] = {id:num}
			objRool["credit"] = max;			
			var rool_num:Number = _num <= min ? 0 : Math.floor((_num - min) / max);
			objRool["rool_num"] = rool_num - user_rool;
			var cur_value:Number = _num <= min ? 0 : max - (min + (rool_num+1) * max - _num);
			objRool["valueProgress"] = cur_value;
			objRool["maxProgress"] = max;
			if(objRool["rool_num"] > 0) {
				canGetMax += 1;
				alreadyGetMax += 1;
				isCanGet = true;
			}
			arr.push(objRool);
			
			return arr;
		}

		private function getRewardArr2(isAdd:Boolean):Array {
			var arr:Array = [];
			var user:ModelUser = ModelManager.instance.modelUser;
			var _num:Number = user.year_credit;
			var _lv:Number = user.credit_lv;
			var credit_get_gifts:Array = user.credit_get_gifts;

			// 修复：如果credit_get_gifts为null或undefined，初始化为空数组
			if(credit_get_gifts == null || credit_get_gifts == undefined) {
				trace("!!! 修复credit_get_gifts为null/undefined的问题 !!!");
				credit_get_gifts = [];
				user.credit_get_gifts = [];
			}
			// 是否是额外奖励
			var is_add:Boolean = isAdd;//credit_get_gifts.length >= mCfg.clv_first_reward[_lv].length;
			// 奖励配置
			var cfg_reward:Array = is_add ? mCfg.clv_added_reward[_lv] : mCfg.clv_first_reward[_lv];
			// 目标战功值基数
			var cfg_added:Number = is_add ? mCfg.clv_added[_lv] : mCfg.clv_first[_lv];
			// 目标战功值系数
			var cfg_ratio:Array  = is_add ? mCfg.clv_added_ratio : mCfg.clv_first_ratio;
			
			// 上一级战功
			var lastCredit:Number = is_add ? (mCfg.clv_up[_lv] ? mCfg.clv_up[_lv] : mCfg.clv_max) : 0;
			
			for(var i:int = 0; i < cfg_reward.length; i++) {
				var obj:Object = {};
				obj["rewardKey"] = "reward";
				// 奖励
				obj["reward"] = cfg_reward[i];//["id":num]
				// 所需战功数
				obj["credit"] = Math.floor(cfg_added * cfg_ratio[i]);
				// 是否领取过
				obj["is_get"] = credit_get_gifts.indexOf(i + "_" + Number(is_add)) != -1;
				// 是否可领取
				obj["can_get"] = _num >= obj["credit"];
				// 进度
				obj["maxProgress"] = obj["credit"] - lastCredit;
				// 进度
				obj["valueProgress"] = Math.max(_num - lastCredit, 0);

				arr.push(obj);

				lastCredit = obj["credit"];

				if(obj["is_get"]) {
					alreadyGetMax += 1;
				}
				
				if(obj["can_get"]) {
					canGetMax += 1;
					if(obj["is_get"] == false) {
						isCanGet = true;
					}
				}

			}

			return arr;
		}

		/**
		 * 
		 */
		private function showAdd():Boolean {
			var boo:Boolean = false;
			var credit_get_gifts:Array = ModelManager.instance.modelUser.credit_get_gifts;
			var _lv:Number = ModelManager.instance.modelUser.credit_lv;
			var cfg_reward:Array = mCfg.clv_first_reward[_lv];
			if(credit_get_gifts.length >= cfg_reward.length) {
				boo = true;
			}
			return boo;
		}

		/**
		 * sg新的战功界面
		 */
		public function showCreditView():void {
			trace("=== showCreditView开始 ===");
			mCreditRankArr = [];
			trace("mCreditRankArr已清空");

			NetSocket.instance.send("get_credit_rank", {"is_year":true}, new Handler(this,function(np:NetPackage):void{
				//[uid,uname,head,country,guild_name,year_credit,credit]
				trace("=== showCreditView收到数据 ===");
				trace("np.receiveData:", np.receiveData);
				trace("np.receiveData是否为null:", np.receiveData == null);

				mCreditRankArr = np.receiveData;
				trace("mCreditRankArr设置完成:", mCreditRankArr);

				ViewManager.instance.showView(ConfigClass.VIEW_CREDIT_MAIN_2);

			}));

			// ViewManager.instance.showView(ConfigClass.VIEW_CREDIT_MAIN);
		}

		/**
		 * 根据名次获得每日奖励
		 * index 从0开始
		 */
		public function getDayRewradByIndex(index:int):Array {
			index += 1;
			var arr:Array = [];
			var cfg:Array = ConfigServer.credit.list_reward_day;
			for(var i:int = cfg.length - 1; i >= 0; i--) {
				if(index >= cfg[i][0]) {
					var temp:Array = cfg[i];
					for(var j:int = 1; j < temp.length; j++) {
						arr.push(temp[j]);// [id, num]
					}
					break;
				}
			}
			return arr;
		}

		/**
		 * 根据名次获得年度奖励
		 * index 从0开始
		 */
		public function getYearRewradByIndex(index:int):Array {
			index += 1;
			var arr:Array = [];
			var cfg:Array = ConfigServer.credit.list_reward_year;
			for(var i:int = cfg.length - 1; i >= 0; i--) {
				if(index >= cfg[i][0]) {
					var temp:Array = cfg[i];
					for(var j:int = 1; j < temp.length; j++) {
						if(temp[j][0] && temp[j][0].indexOf("title")!=-1) {
							if(temp[j].length == 1) {
								arr.push(temp[j]);
							} else{
								// 称号四个中随机给一个，称号信息里显示随机属性
								arr.push([temp[j][0], 0]);
							}		
						} else {
							if(temp[j].length > 0) {
								arr.push(temp[j]);// [id, num]
							}
						}
					
					}
					break;
				}
			}
			return arr;
		}

		/**
		 * 每日奖励的图标
		 */
		public function getDayRewradIcon():String {
			return "item0074";
		}
		/**
		 * 根据名次获得年度奖励的图标
		 * 显示称号或者宝箱
		 * index 从0开始
		 */
		public function getYearRewradIcon(index:int):String {
			index += 1;
			var str:String = "";
			var arr:Array = [];
			var cfg:Array = ConfigServer.credit.list_reward_year;
			for(var i:int = cfg.length - 1; i >= 0; i--) {
				if(index >= cfg[i][0]) {
					var temp:Array = cfg[i][1];
					if(temp[0] && temp[0].indexOf("title")!=-1) {
						str = temp[0];	
					} else {
						str = "item0075";
					}
					break;
				}
			}
			return str;
		}

		public function testSocket():void {
			// 一键全领
			NetSocket.instance.send("get_credit_gift_v1", {}, new Handler(this,function(np:NetPackage):void{
				ModelManager.instance.modelUser.updateData(np.receiveData);
				ViewManager.instance.showRewardPanel(np.receiveData.gift_dict_list);
			}));
		}

	}

}