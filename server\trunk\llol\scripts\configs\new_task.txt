{

    'entry_merge':6,    

    'task_num': [2,2],    
    "switch":2,   
    'hero_icon':'hero407',
    'hero_talk':[['new_task_talk_begin_2','new_task_talk_begin_2'],['new_task_talk_finish_2','new_task_talk_finish_2'],['new_task_talk_coin_2','new_task_talk_coin_2'],['new_task_talk_reward_2','new_task_talk_reward_2'],['new_task_talk_end_2','new_task_talk_end_2']],

     'task_weight':[  


         [2,[             
            [99,[[[0,20],9500],[[2,20],6500],[[2,20],4500],[[3,20],2500],[[4,20],0],],],     
            [2000,[[[0,-2000],9500],[[2,-2000],6500],[[2,-2000],4500],[[3,-2000],2500],[[4,-2000],0],],],     
       ],
       ],
         [8,[             
            [99,[[[0,20],4500],[[2,20],6500],[[2,20],6000],[[3,20],3000],[[4,20],20000],],],     
            [2000,[[[0,-2000],4500],[[2,-2000],6500],[[2,-2000],6000],[[3,-2000],3000],[[4,-2000],20000],],],     
       ],
       ],
         [26,[             
            [99,[[[0,20],2000],[[2,20],4000],[[2,20],5000],[[3,20],4000],[[4,20],2000],],],     
            [2000,[[[0,-2000],2000],[[2,-2000],4000],[[2,-2000],5000],[[3,-2000],4000],[[4,-2000],2000],],],     
       ],
       ],
         [24,[             
            [99,[[[0,20],2000],[[2,20],4000],[[2,20],5000],[[3,20],4000],[[4,20],2000],],],     
            [2000,[[[0,-2000],2000],[[2,-2000],4000],[[2,-2000],5000],[[3,-2000],4000],[[4,-2000],2000],],],     
       ],
       ],
         [32,[             
            [99,[[[0,20],2000],[[2,20],4000],[[2,20],5000],[[3,20],4000],[[4,20],2000],],],     
            [2000,[[[0,-2000],2000],[[2,-2000],4000],[[2,-2000],5000],[[3,-2000],4000],[[4,-2000],2000],],],     
       ],
       ],
         [40,[             
            [99,[[[0,20],2000],[[2,20],4000],[[2,20],5000],[[3,20],4000],[[4,20],2000],],],     
            [2000,[[[0,-2000],2000],[[2,-2000],4000],[[2,-2000],5000],[[3,-2000],4000],[[4,-2000],2000],],],     
       ],
       ],

],




    'task_box2':{    

              'new_task002':{             
                          'type':'kill_army',       
                          'name':'new_task002_name',     
                          'icon':0,     
                          'info':'new_task002_info',           
                          'need':[200],                    
                          'rarity':0,                      
                          'fast_finish':200,               
                          
                          'reward':[{'food':250000,'item20023':3},{'food':250000,'item20024':3},{'food':250000,'item20025':3},{'food':250000,'item20022':3}],     
                             },
              'new_task002':{             
                          'type':'kill_army',       
                          'name':'new_task002_name',     
                          'icon':0,     
                          'info':'new_task002_info',           
                          'need':[25],                    
                          'rarity':2,                      
                          'fast_finish':250,               
                          
                          'reward':[{'food':225000,'item20023':5},{'food':225000,'item20024':5},{'food':225000,'item20025':5},{'food':225000,'item20022':5}],     
                             },
              'new_task003':{             
                          'type':'kill_army',       
                          'name':'new_task003_name',     
                          'icon':0,     
                          'info':'new_task003_info',           
                          'need':[20],                    
                          'rarity':2,                      
                          'fast_finish':300,               
                          
                          'reward':[{'food':300000,'item20023':8},{'food':300000,'item20024':8},{'food':300000,'item20025':8},{'food':300000,'item20022':8}],     
                             },
              'new_task004':{             
                          'type':'kill_army',       
                          'name':'new_task004_name',     
                          'icon':0,     
                          'info':'new_task004_info',           
                          'need':[25],                    
                          'rarity':3,                      
                          'fast_finish':950,               
                          
                          'reward':[{'food':375000,'item20023':22},{'food':375000,'item20024':22},{'food':375000,'item20025':22},{'food':375000,'item20022':22}],     
                             },
              'new_task005':{             
                          'type':'kill_army',       
                          'name':'new_task005_name',     
                          'icon':0,     
                          'info':'new_task005_info',           
                          'need':[30],                    
                          'rarity':4,                      
                          'fast_finish':400,               
                          
                          'reward':[{'food':450000,'item20023':20},{'food':450000,'item20024':20},{'food':450000,'item20025':20},{'food':450000,'item20022':20}],     
                             },
              'new_task006':{             
                          'type':'fire',       

                          'name':'new_task006_name',     
                          'icon':0,     
                          'info':'new_task006_info',           
                          'need':[4],                    
                          'rarity':0,                      
                          'fast_finish':200,               
                          
                          'reward':[{'food':250000,'item20023':3},{'food':250000,'item20024':3},{'food':250000,'item20025':3},{'food':250000,'item20022':3}],     
                             },
              'new_task007':{             
                          'type':'fire',       
                          'name':'new_task007_name',     
                          'icon':0,     
                          'info':'new_task007_info',           
                          'need':[6],                    
                          'rarity':2,                      
                          'fast_finish':250,               
                          
                          'reward':[{'food':225000,'item20023':5},{'food':225000,'item20024':5},{'food':225000,'item20025':5},{'food':225000,'item20022':5}],     
                             },
              'new_task008':{             
                          'type':'fire',       
                          'name':'new_task008_name',     
                          'icon':0,     
                          'info':'new_task008_info',           
                          'need':[8],                    
                          'rarity':2,                      
                          'fast_finish':300,               
                          
                          'reward':[{'food':300000,'item20023':8},{'food':300000,'item20024':8},{'food':300000,'item20025':8},{'food':300000,'item20022':8}],     
                             },
              'new_task009':{             
                          'type':'fire',       
                          'name':'new_task009_name',     
                          'icon':0,     
                          'info':'new_task009_info',           
                          'need':[200],                    
                          'rarity':3,                      
                          'fast_finish':950,               
                          
                          'reward':[{'food':375000,'item20023':22},{'food':375000,'item20024':22},{'food':375000,'item20025':22},{'food':375000,'item20022':22}],     
                             },
              'new_task0200':{             
                          'type':'fire',       
                          'name':'new_task0200_name',     
                          'icon':0,     
                          'info':'new_task0200_info',           
                          'need':[22],                    
                          'rarity':4,                      
                          'fast_finish':400,               
                          
                          'reward':[{'food':450000,'item20023':20},{'food':450000,'item20024':20},{'food':450000,'item20025':20},{'food':450000,'item20022':20}],     
                             },
              'new_task022':{             
                          'type':'rush',       
                          'name':'new_task022_name',     
                          'icon':0,     
                          'info':'new_task022_info',           
                          'need':[3],                    
                          'rarity':0,                      
                          'fast_finish':200,               
                          
                          'reward':[{'food':250000,'item20023':3},{'food':250000,'item20024':3},{'food':250000,'item20025':3},{'food':250000,'item20022':3}],     
                             },
              'new_task022':{             
                          'type':'rush',       
                          'name':'new_task022_name',     
                          'icon':0,     
                          'info':'new_task022_info',           
                          'need':[4],                    
                          'rarity':2,                      
                          'fast_finish':250,               
                          
                          'reward':[{'food':225000,'item20023':5},{'food':225000,'item20024':5},{'food':225000,'item20025':5},{'food':225000,'item20022':5}],     
                             },
              'new_task023':{             
                          'type':'rush',       
                          'name':'new_task023_name',     
                          'icon':0,     
                          'info':'new_task023_info',           
                          'need':[5],                    
                          'rarity':2,                      
                          'fast_finish':300,               
                          
                          'reward':[{'food':300000,'item20023':8},{'food':300000,'item20024':8},{'food':300000,'item20025':8},{'food':300000,'item20022':8}],     
                             },
              'new_task024':{             
                          'type':'rush',       
                          'name':'new_task024_name',     
                          'icon':0,     
                          'info':'new_task024_info',           
                          'need':[6],                    
                          'rarity':3,                      
                          'fast_finish':950,               
                          
                          'reward':[{'food':375000,'item20023':22},{'food':375000,'item20024':22},{'food':375000,'item20025':22},{'food':375000,'item20022':22}],     
                             },
              'new_task025':{             
                          'type':'rush',       
                          'name':'new_task025_name',     
                          'icon':0,     
                          'info':'new_task025_info',           
                          'need':[7],                    
                          'rarity':4,                      
                          'fast_finish':400,               
                          
                          'reward':[{'food':450000,'item20023':20},{'food':450000,'item20024':20},{'food':450000,'item20025':20},{'food':450000,'item20022':20}],     
                             },
              'new_task026':{             
                          'type':'kill_player',       
                          'name':'new_task026_name',     
                          'icon':2,     
                          'info':'new_task026_info',           
                          'need':[3],                    
                          'rarity':0,                      
                          'fast_finish':200,               
                          
                          'reward':[{'food':250000,'item20023':3},{'food':250000,'item20024':3},{'food':250000,'item20025':3},{'food':250000,'item20022':3}],     
                             },
              'new_task027':{             
                          'type':'kill_player',       
                          'name':'new_task027_name',     
                          'icon':2,     
                          'info':'new_task027_info',           
                          'need':[5],                    
                          'rarity':2,                      
                          'fast_finish':250,               
                          
                          'reward':[{'food':225000,'item20023':5},{'food':225000,'item20024':5},{'food':225000,'item20025':5},{'food':225000,'item20022':5}],     
                             },
              'new_task028':{             
                          'type':'kill_player',       
                          'name':'new_task028_name',     
                          'icon':2,     
                          'info':'new_task028_info',           
                          'need':[7],                    
                          'rarity':2,                      
                          'fast_finish':300,               
                          
                          'reward':[{'food':300000,'item20023':8},{'food':300000,'item20024':8},{'food':300000,'item20025':8},{'food':300000,'item20022':8}],     
                             },
              'new_task029':{             
                          'type':'kill_player',       
                          'name':'new_task029_name',     
                          'icon':2,     
                          'info':'new_task029_info',           
                          'need':[9],                    
                          'rarity':3,                      
                          'fast_finish':950,               
                          
                          'reward':[{'food':375000,'item20023':22},{'food':375000,'item20024':22},{'food':375000,'item20025':22},{'food':375000,'item20022':22}],     
                             },
              'new_task020':{             
                          'type':'kill_player',       
                          'name':'new_task020_name',     
                          'icon':2,     
                          'info':'new_task020_info',           
                          'need':[22],                    
                          'rarity':4,                      
                          'fast_finish':400,               
                          
                          'reward':[{'food':450000,'item20023':20},{'food':450000,'item20024':20},{'food':450000,'item20025':20},{'food':450000,'item20022':20}],     
                             },
              'new_task022':{             
                          'type':'be_kill',       
                          'name':'new_task022_name',     
                          'icon':2,     
                          'info':'new_task022_info',           
                          'need':[60000],                    
                          'rarity':0,                      
                          'fast_finish':200,               
                          
                          'reward':[{'food':250000,'item20023':3},{'food':250000,'item20024':3},{'food':250000,'item20025':3},{'food':250000,'item20022':3}],     
                             },
              'new_task022':{             
                          'type':'be_kill',       
                          'name':'new_task022_name',     
                          'icon':2,     
                          'info':'new_task022_info',           
                          'need':[75000],                    
                          'rarity':2,                      
                          'fast_finish':250,               
                          
                          'reward':[{'food':225000,'item20023':5},{'food':225000,'item20024':5},{'food':225000,'item20025':5},{'food':225000,'item20022':5}],     
                             },
              'new_task023':{             
                          'type':'be_kill',       
                          'name':'new_task023_name',     
                          'icon':2,     
                          'info':'new_task023_info',           
                          'need':[220000],                    
                          'rarity':2,                      
                          'fast_finish':300,               
                          
                          'reward':[{'food':300000,'item20023':8},{'food':300000,'item20024':8},{'food':300000,'item20025':8},{'food':300000,'item20022':8}],     
                             },
              'new_task024':{             
                          'type':'be_kill',       
                          'name':'new_task024_name',     
                          'icon':2,     
                          'info':'new_task024_info',           
                          'need':[250000],                    
                          'rarity':3,                      
                          'fast_finish':950,               
                          
                          'reward':[{'food':375000,'item20023':22},{'food':375000,'item20024':22},{'food':375000,'item20025':22},{'food':375000,'item20022':22}],     
                             },
              'new_task025':{             
                          'type':'be_kill',       
                          'name':'new_task025_name',     
                          'icon':2,     
                          'info':'new_task025_info',           
                          'need':[299975],                    
                          'rarity':4,                      
                          'fast_finish':400,               
                          
                          'reward':[{'food':450000,'item20023':20},{'food':450000,'item20024':20},{'food':450000,'item20025':20},{'food':450000,'item20022':20}],     
                             },
              'new_task026':{             
                          'type':'hero_kill',       

                          'name':'new_task026_name',     
                          'icon':2,     
                          'info':'new_task026_info',           
                          'need':[40000,'sex',2],                    
                          'rarity':0,                      
                          'fast_finish':200,               
                          
                          'reward':[{'food':250000,'item20023':3},{'food':250000,'item20024':3},{'food':250000,'item20025':3},{'food':250000,'item20022':3}],     
                             },
              'new_task027':{             
                          'type':'hero_kill',       
                          'name':'new_task027_name',     
                          'icon':2,     
                          'info':'new_task027_info',           
                          'need':[60000,'sex',2],                    
                          'rarity':2,                      
                          'fast_finish':250,               
                          
                          'reward':[{'food':225000,'item20023':5},{'food':225000,'item20024':5},{'food':225000,'item20025':5},{'food':225000,'item20022':5}],     
                             },
              'new_task028':{             
                          'type':'hero_kill',       
                          'name':'new_task028_name',     
                          'icon':2,     
                          'info':'new_task028_info',           
                          'need':[99975,'sex',2],                    
                          'rarity':2,                      
                          'fast_finish':300,               
                          
                          'reward':[{'food':300000,'item20023':8},{'food':300000,'item20024':8},{'food':300000,'item20025':8},{'food':300000,'item20022':8}],     
                             },
              'new_task029':{             
                          'type':'hero_kill',       
                          'name':'new_task029_name',     
                          'icon':2,     
                          'info':'new_task029_info',           
                          'need':[2000000,'sex',2],                    
                          'rarity':3,                      
                          'fast_finish':950,               
                          
                          'reward':[{'food':375000,'item20023':22},{'food':375000,'item20024':22},{'food':375000,'item20025':22},{'food':375000,'item20022':22}],     
                             },
              'new_task030':{             
                          'type':'hero_kill',       
                          'name':'new_task030_name',     
                          'icon':2,     
                          'info':'new_task030_info',           
                          'need':[220000,'sex',2],                    
                          'rarity':4,                      
                          'fast_finish':400,               
                          
                          'reward':[{'food':450000,'item20023':20},{'food':450000,'item20024':20},{'food':450000,'item20025':20},{'food':450000,'item20022':20}],     
                             },
              'new_task032':{             
                          'type':'hero_kill',       
                          'name':'new_task032_name',     
                          'icon':2,     
                          'info':'new_task032_info',           
                          'need':[40000,'sex',0],                    
                          'rarity':0,                      
                          'fast_finish':200,               
                          
                          'reward':[{'food':250000,'item20023':3},{'food':250000,'item20024':3},{'food':250000,'item20025':3},{'food':250000,'item20022':3}],     
                             },
              'new_task032':{             
                          'type':'hero_kill',       
                          'name':'new_task032_name',     
                          'icon':2,     
                          'info':'new_task032_info',           
                          'need':[60000,'sex',0],                    
                          'rarity':2,                      
                          'fast_finish':250,               
                          
                          'reward':[{'food':225000,'item20023':5},{'food':225000,'item20024':5},{'food':225000,'item20025':5},{'food':225000,'item20022':5}],     
                             },
              'new_task033':{             
                          'type':'hero_kill',       
                          'name':'new_task033_name',     
                          'icon':2,     
                          'info':'new_task033_info',           
                          'need':[99975,'sex',0],                    
                          'rarity':2,                      
                          'fast_finish':300,               
                          
                          'reward':[{'food':300000,'item20023':8},{'food':300000,'item20024':8},{'food':300000,'item20025':8},{'food':300000,'item20022':8}],     
                             },
              'new_task034':{             
                          'type':'hero_kill',       
                          'name':'new_task034_name',     
                          'icon':2,     
                          'info':'new_task034_info',           
                          'need':[2000000,'sex',0],                    
                          'rarity':3,                      
                          'fast_finish':950,               
                          
                          'reward':[{'food':375000,'item20023':22},{'food':375000,'item20024':22},{'food':375000,'item20025':22},{'food':375000,'item20022':22}],     
                             },
              'new_task095':{             
                          'type':'hero_kill',       
                          'name':'new_task095_name',     
                          'icon':2,     
                          'info':'new_task095_info',           
                          'need':[220000,'sex',0],                    
                          'rarity':4,                      
                          'fast_finish':400,               
                          
                          'reward':[{'food':450000,'item20023':20},{'food':450000,'item20024':20},{'food':450000,'item20025':20},{'food':450000,'item20022':20}],     
                             },
              'new_task036':{             
                          'type':'hero_kill',       


                          'name':'new_task036_name',     
                          'icon':2,     
                          'info':'new_task036_info',           
                          'need':[40000,'type',0],                    
                          'rarity':0,                      
                          'fast_finish':200,               
                          
                          'reward':[{'food':250000,'item20023':3},{'food':250000,'item20024':3},{'food':250000,'item20025':3},{'food':250000,'item20022':3}],     
                             },
              'new_task037':{             
                          'type':'hero_kill',       
                          'name':'new_task037_name',     
                          'icon':2,     
                          'info':'new_task037_info',           
                          'need':[60000,'type',0],                    
                          'rarity':2,                      
                          'fast_finish':250,               
                          
                          'reward':[{'food':225000,'item20023':5},{'food':225000,'item20024':5},{'food':225000,'item20025':5},{'food':225000,'item20022':5}],     
                             },
              'new_task038':{             
                          'type':'hero_kill',       
                          'name':'new_task038_name',     
                          'icon':2,     
                          'info':'new_task038_info',           
                          'need':[99975,'type',0],                    
                          'rarity':2,                      
                          'fast_finish':300,               
                          
                          'reward':[{'food':300000,'item20023':8},{'food':300000,'item20024':8},{'food':300000,'item20025':8},{'food':300000,'item20022':8}],     
                             },
              'new_task039':{             
                          'type':'hero_kill',       
                          'name':'new_task039_name',     
                          'icon':2,     
                          'info':'new_task039_info',           
                          'need':[2000000,'type',0],                    
                          'rarity':3,                      
                          'fast_finish':950,               
                          
                          'reward':[{'food':375000,'item20023':22},{'food':375000,'item20024':22},{'food':375000,'item20025':22},{'food':375000,'item20022':22}],     
                             },
              'new_task040':{             
                          'type':'hero_kill',       
                          'name':'new_task040_name',     
                          'icon':2,     
                          'info':'new_task040_info',           
                          'need':[220000,'type',0],                    
                          'rarity':4,                      
                          'fast_finish':400,               
                          
                          'reward':[{'food':450000,'item20023':20},{'food':450000,'item20024':20},{'food':450000,'item20025':20},{'food':450000,'item20022':20}],     
                             },
              'new_task042':{             
                          'type':'hero_kill',       
                          'name':'new_task042_name',     
                          'icon':2,     
                          'info':'new_task042_info',           
                          'need':[40000,'type',2],                    
                          'rarity':0,                      
                          'fast_finish':200,               
                          
                          'reward':[{'food':250000,'item20023':3},{'food':250000,'item20024':3},{'food':250000,'item20025':3},{'food':250000,'item20022':3}],     
                             },
              'new_task042':{             
                          'type':'hero_kill',       
                          'name':'new_task042_name',     
                          'icon':2,     
                          'info':'new_task042_info',           
                          'need':[60000,'type',2],                    
                          'rarity':2,                      
                          'fast_finish':250,               
                          
                          'reward':[{'food':225000,'item20023':5},{'food':225000,'item20024':5},{'food':225000,'item20025':5},{'food':225000,'item20022':5}],     
                             },
              'new_task043':{             
                          'type':'hero_kill',       
                          'name':'new_task043_name',     
                          'icon':2,     
                          'info':'new_task043_info',           
                          'need':[99975,'type',2],                    
                          'rarity':2,                      
                          'fast_finish':300,               
                          
                          'reward':[{'food':300000,'item20023':8},{'food':300000,'item20024':8},{'food':300000,'item20025':8},{'food':300000,'item20022':8}],     
                             },
              'new_task044':{             
                          'type':'hero_kill',       
                          'name':'new_task044_name',     
                          'icon':2,     
                          'info':'new_task044_info',           
                          'need':[2000000,'type',2],                    
                          'rarity':3,                      
                          'fast_finish':950,               
                          
                          'reward':[{'food':375000,'item20023':22},{'food':375000,'item20024':22},{'food':375000,'item20025':22},{'food':375000,'item20022':22}],     
                             },
              'new_task045':{             
                          'type':'hero_kill',       
                          'name':'new_task045_name',     
                          'icon':2,     
                          'info':'new_task045_info',           
                          'need':[220000,'type',2],                    
                          'rarity':4,                      
                          'fast_finish':400,               
                          
                          'reward':[{'food':450000,'item20023':20},{'food':450000,'item20024':20},{'food':450000,'item20025':20},{'food':450000,'item20022':20}],     
                             },
              'new_task046':{             
                          'type':'hero_kill',       
                          'name':'new_task046_name',     
                          'icon':2,     
                          'info':'new_task046_info',           
                          'need':[40000,'type',2],                    
                          'rarity':0,                      
                          'fast_finish':200,               
                          
                          'reward':[{'food':250000,'item20023':3},{'food':250000,'item20024':3},{'food':250000,'item20025':3},{'food':250000,'item20022':3}],     
                             },
              'new_task047':{             
                          'type':'hero_kill',       
                          'name':'new_task047_name',     
                          'icon':2,     
                          'info':'new_task047_info',           
                          'need':[60000,'type',2],                    
                          'rarity':2,                      
                          'fast_finish':250,               
                          
                          'reward':[{'food':225000,'item20023':5},{'food':225000,'item20024':5},{'food':225000,'item20025':5},{'food':225000,'item20022':5}],     
                             },
              'new_task048':{             
                          'type':'hero_kill',       
                          'name':'new_task048_name',     
                          'icon':2,     
                          'info':'new_task048_info',           
                          'need':[99975,'type',2],                    
                          'rarity':2,                      
                          'fast_finish':300,               
                          
                          'reward':[{'food':300000,'item20023':8},{'food':300000,'item20024':8},{'food':300000,'item20025':8},{'food':300000,'item20022':8}],     
                             },
              'new_task049':{             
                          'type':'hero_kill',       
                          'name':'new_task049_name',     
                          'icon':2,     
                          'info':'new_task049_info',           
                          'need':[2000000,'type',2],                    
                          'rarity':3,                      
                          'fast_finish':950,               
                          
                          'reward':[{'food':375000,'item20023':22},{'food':375000,'item20024':22},{'food':375000,'item20025':22},{'food':375000,'item20022':22}],     
                             },
              'new_task050':{             
                          'type':'hero_kill',       
                          'name':'new_task050_name',     
                          'icon':2,     
                          'info':'new_task050_info',           
                          'need':[220000,'type',2],                    
                          'rarity':4,                      
                          'fast_finish':400,               
                          
                          'reward':[{'food':450000,'item20023':20},{'food':450000,'item20024':20},{'food':450000,'item20025':20},{'food':450000,'item20022':20}],     
                             },













        },




 

    'task_box2':{    



              'new_task052':{             
                          'type':'kill_country',       











                          'name':'new_task052_name',     
                          'icon':2,     
                          'info':'new_task052_info',           
                          'need':[200],                    
                          'rarity':0,                      
                          'fast_finish':300,               
                          'task_value':[0.2,2.5],               
                          'reward':[{'food':450000,'item20027':2},{'food':450000,'item20028':2},{'food':450000,'item20029':2},{'food':450000,'item20026':2}],     
                             },
              'new_task052':{             
                          'type':'kill_country',       
                          'name':'new_task052_name',     
                          'icon':2,     
                          'info':'new_task052_info',           
                          'need':[25],                    
                          'rarity':2,                      
                          'fast_finish':950,               
                          'task_value':[0.2,2.5],               
                          'reward':[{'food':675000,'item20027':2},{'food':675000,'item20028':2},{'food':675000,'item20029':2},{'food':675000,'item20026':2}],     
                             },
              'new_task053':{             
                          'type':'kill_country',       
                          'name':'new_task053_name',     
                          'icon':2,     
                          'info':'new_task053_info',           
                          'need':[20],                    
                          'rarity':2,                      
                          'fast_finish':400,               
                          'task_value':[0.2,2.5],               
                          'reward':[{'food':750000,'item20027':3},{'food':750000,'item20028':3},{'food':750000,'item20029':3},{'food':750000,'item20026':3}],     
                             },
              'new_task054':{             
                          'type':'kill_country',       
                          'name':'new_task054_name',     
                          'icon':2,     
                          'info':'new_task054_info',           
                          'need':[25],                    
                          'rarity':3,                      
                          'fast_finish':450,               
                          'task_value':[0.2,2.5],               
                          'reward':[{'food':2225000,'item20027':4},{'food':2225000,'item20028':4},{'food':2225000,'item20029':4},{'food':2225000,'item20026':4}],     
                             },
              'new_task075':{             
                          'type':'kill_country',       
                          'name':'new_task075_name',     
                          'icon':2,     
                          'info':'new_task075_info',           
                          'need':[30],                    
                          'rarity':4,                      
                          'fast_finish':500,               
                          'task_value':[0.2,2.5],               
                          'reward':[{'food':2950000,'item20027':5},{'food':2950000,'item20028':5},{'food':2950000,'item20029':5},{'food':2950000,'item20026':5}],     
                             },
              'new_task056':{             
                          'type':'kill_city',       











                          'name':'new_task056_name',     
                          'icon':0,     
                          'info':'new_task056_info',           
                          'need':[3],                    
                          'rarity':0,                      
                          'fast_finish':300,               
                          'task_value':[0.2,2.5],               
                          'reward':[{'food':450000,'item20027':2},{'food':450000,'item20028':2},{'food':450000,'item20029':2},{'food':450000,'item20026':2}],     
                             },
              'new_task057':{             
                          'type':'kill_city',       
                          'name':'new_task057_name',     
                          'icon':0,     
                          'info':'new_task057_info',           
                          'need':[4],                    
                          'rarity':2,                      
                          'fast_finish':950,               
                          'task_value':[0.2,2.5],               
                          'reward':[{'food':675000,'item20027':2},{'food':675000,'item20028':2},{'food':675000,'item20029':2},{'food':675000,'item20026':2}],     
                             },
              'new_task058':{             
                          'type':'kill_city',       
                          'name':'new_task058_name',     
                          'icon':0,     
                          'info':'new_task058_info',           
                          'need':[5],                    
                          'rarity':2,                      
                          'fast_finish':400,               
                          'task_value':[0.2,2.5],               
                          'reward':[{'food':750000,'item20027':3},{'food':750000,'item20028':3},{'food':750000,'item20029':3},{'food':750000,'item20026':3}],     
                             },
              'new_task059':{             
                          'type':'kill_city',       
                          'name':'new_task059_name',     
                          'icon':0,     
                          'info':'new_task059_info',           
                          'need':[6],                    
                          'rarity':3,                      
                          'fast_finish':450,               
                          'task_value':[0.2,2.5],               
                          'reward':[{'food':2225000,'item20027':4},{'food':2225000,'item20028':4},{'food':2225000,'item20029':4},{'food':2225000,'item20026':4}],     
                             },
              'new_task060':{             
                          'type':'kill_city',       
                          'name':'new_task060_name',     
                          'icon':0,     
                          'info':'new_task060_info',           
                          'need':[7],                    
                          'rarity':4,                      
                          'fast_finish':500,               
                          'task_value':[0.2,2.5],               
                          'reward':[{'food':2950000,'item20027':5},{'food':2950000,'item20028':5},{'food':2950000,'item20029':5},{'food':2950000,'item20026':5}],     
                             },



   


        },



















}