package sg.view.map
{
	import ui.map.creditMain2UI;
	import sg.model.ModelCredit;
	import ui.map.item_credit_user2UI;
	import ui.map.item_credit2UI;
	import laya.events.Event;
	import sg.manager.ViewManager;
	import sg.utils.Tools;
	import sg.cfg.ConfigServer;
	import laya.utils.Handler;
	import sg.manager.ModelManager;
	import sg.model.ModelUser;
	import laya.ui.Box;
	import ui.map.item_credit2_2UI;
	import ui.map.item_credit2_3UI;
	import ui.map.item_credit2_1UI;
	import sg.view.com.ComPayType;
	import sg.model.ModelProp;
	import sg.model.ModelItem;
	import sg.view.com.comIcon;
	import sg.net.NetSocket;
	import sg.net.NetPackage;
	import laya.display.Sprite;
	import laya.ui.Image;
	import laya.display.Animation;
	import sg.manager.EffectManager;
	import sg.cfg.ConfigApp;

	/**
	 * sg项目新的战功界面
	 * <AUTHOR>
	public class ViewCreditMain2 extends creditMain2UI {

		private var model:ModelCredit;
		private var myRank:Number;
		private var myLv:Number;
		private var nextYearTime:Number;
		private var scrollBarMax:Number;
		public function ViewCreditMain2() {
			this.btnTips.on(Event.CLICK, this, function():void{
				ViewManager.instance.showTipsPanel(Tools.getMsgById(ConfigServer.credit.credit_info));
			});

			this.rankList.renderHandler = new Handler(this, rankListRender);
			this.rankList.scrollBar.visible = false;
			this.rankList.scrollBar.elasticDistance = 100;
            this.rankList.scrollBar.elasticBackTime = 100;

			this.rewardList.renderHandler = new Handler(this, rewardListRender);
			this.rewardList.scrollBar.visible = false;
			this.rewardList.scrollBar.changeHandler = new Handler(this, scrollChange);

			this.tEmpty.text = Tools.getMsgById("_credit_text18");

			if(this["label0"]) {
				this["label0"].text = Tools.getMsgById("_more_rank06");
				this["label1"].text = Tools.getMsgById("_more_rank07");
				this["label2"].text = Tools.getMsgById("_lht18");
				this["label3"].text = Tools.getMsgById("_credit_text26");
			}
			
			var _this:* = this;
			this.tTimeHint.text = Tools.getMsgById("_credit_text07");
			this.tTime.text = "22时22分";
			this.tTime.visible = false;
			_this.tTimeHint.width = _this.tTimeHint.textField.textWidth + 4;
			_this.tTime.width = _this.tTime.textField.textWidth;
			Laya.timer.callLater(this, function():void{
				var box:Box = (_this.tTimeHint.parent) as Box;
				if(ConfigApp.isLandscape) {//cb项目 右对齐
					box.width = _this.tTimeHint.width + _this.tTime.width;
					_this.tTimeHint.x = 0;
					_this.tTime.x = _this.tTimeHint.width;
				} else {
					_this.tTimeHint.x = (box.width - (_this.tTimeHint.width + _this.tTime.width)) / 2;
					_this.tTime.x = _this.tTimeHint.x + _this.tTimeHint.width;
				}
			});

			this.tRank["orignalX"] = this.tRank.x;

		}

		override public function onAdded():void {
			var cfg:Object = ConfigServer.credit;
			ModelManager.instance.modelUser.on(ModelUser.EVENT_UPDATE_CREDIT, this, creditCallBack);
			ModelManager.instance.modelUser.on(ModelUser.EVENT_IS_NEW_DAY,this, newDayCallBack);
			this.setTitle(Tools.getMsgById("_lht18"));
			model = ModelCredit.instance;
			myRank = -1;
			setRankList();
			updateUI();

			nextYearTime = Tools.getYearDis(3, cfg.list_reward_time[0]);
			onTimer();

			this.tTips.text = Tools.getMsgById("_credit_text10",[cfg.list_reward_time[0][0], cfg.list_reward_long]);
		}

		private function onTimer():void {
			nextYearTime -= 1000;
			this.tTime.visible = true;
			this.tTime.text = Tools.getTimeStyle(nextYearTime);
			Laya.timer.once(1000, this, onTimer);
		}

		private function newDayCallBack():void {
			if(ModelManager.instance.modelUser.getGameSeason() == 0) {
				this.click_closeScenes();
			}
		}

		private var isUpdating:Boolean = false;
		private function creditCallBack():void {
			trace("=== creditCallBack开始 ===");
			trace("isUpdating:", isUpdating);
			trace("model.mCreditRankArr:", model.mCreditRankArr);
			trace("model.mCreditRankArr是否为null:", model.mCreditRankArr == null);

			// 如果数据已经存在，直接使用，不再发送请求
			if(model.mCreditRankArr != null && model.mCreditRankArr.length > 0) {
				trace("数据已存在，直接使用");
				setRankList();
				updateUI();
				return;
			}

			if(isUpdating)
				return;
			isUpdating = true;
			trace("发送get_credit_rank请求");
			NetSocket.instance.send("get_credit_rank", {"is_year":true}, new Handler(this,function(np:NetPackage):void{
				//[uid,uname,head,country,guild_name,year_credit,credit]

				// 添加调试日志
				trace("=== 前端收到战功排行榜数据 ===");
				trace("np.receiveData类型:", typeof(np.receiveData));
				trace("np.receiveData是否为null:", np.receiveData == null);
				if(np.receiveData != null) {
					trace("np.receiveData长度:", np.receiveData.length);
					if(np.receiveData.length > 0) {
						trace("第一个元素:", np.receiveData[0]);
					}
				}

				model.mCreditRankArr = np.receiveData;

				trace("model.mCreditRankArr设置后:", model.mCreditRankArr);
				trace("model.mCreditRankArr是否为null:", model.mCreditRankArr == null);

				setRankList();
				updateUI();
				isUpdating = false;
			}));
		}

		private function updateUI():void {
			var myCredit:Number = ModelManager.instance.modelUser.year_credit;
			myLv = ModelManager.instance.modelUser.credit_lv;
			if(model.mCfg.clv_first.length == 1) {
				myLv = -1;
				this.tCredit.text = Tools.getMsgById("_credit_text30", [myCredit]);// 我的战功
			} else {
				this.tCredit.text =  Tools.getMsgById("_credit_text31", [(myLv+1), myCredit]);// 我的战功x级
			}
			var myRankStr:String = myRank == -1 ? Tools.getMsgById("_public76") : (myRank + 1) + "";
			this.tRank.text = Tools.getMsgById("_public214") + Tools.SYMBOL_COLON + myRankStr;// 排名
			
			var _this:* = this;
			Laya.timer.callLater(this, function():void {
				var rankX:Number = _this.tCredit.x + _this.tCredit.textField.textWidth + 10;
				_this.tRank.x = _this.tRank["orignalX"];
				_this.tRank.x = _this.tRank.x < rankX ? rankX : _this.tRank.x;
			});

			this.rewardList.array = model.getRewardArr();

			Laya.timer.callLater(this, function():void {
				_this.rewardList.tweenTo(model.alreadyGetMax - 1);	
			});

			var cellWidth:Number = ConfigApp.isLandscape ? 185 : 200;
			scrollBarMax = cellWidth * (model.canGetMax + 6) - this.rewardList.width;
			if(scrollBarMax < 0) {
				scrollBarMax = 0;
			}
		}

		private function setRankList():void {
			trace("=== setRankList开始 ===");
			var arr:Array = model.mCreditRankArr;
			trace("arr:", arr);
			trace("arr是否为null:", arr == null);
			trace("arr类型:", typeof(arr));

			if(arr == null) {
				trace("!!! 错误：arr为null !!!");
				return;
			}

			trace("arr长度:", arr.length);

			trace("当前玩家UID:", ModelManager.instance.modelUser.mUID);
			trace("当前玩家UID类型:", typeof(ModelManager.instance.modelUser.mUID));

			for(var i:int = 0; i < arr.length; i++) {
				trace("检查玩家", i, "UID:", arr[i][0], "类型:", typeof(arr[i][0]));
				trace("转换后:", arr[i][0] + "", "比较结果:", (arr[i][0] + "" == ModelManager.instance.modelUser.mUID));

				if(arr[i][0] + "" == ModelManager.instance.modelUser.mUID) {
					myRank = i;
					trace("找到当前玩家，排名:", myRank);
					break;
				}
			}

			trace("myRank:", myRank);
			trace("设置UI可见性...");

			this.tEmpty.visible = arr.length == 0;
			this.rankList.visible = arr.length > 0;
			// var testArr:Array = [];
			// for(var j:int = 0; j < 52; j++) {
			// 	testArr.push(arr[0]);
			// }
			this.rankList.array = arr;
		}

		private function rankListRender(cell:item_credit_user2UI, index:int):void {
			//[uid,uname,head,country,guild_name,year_credit,credit]
			var arr:Array = this.rankList.array[index];
			var uid:String = arr[0] + "";
			cell.comIndex.setRankIndex(index+1,"",true);
			Tools.changeLabelFont(cell.nameLabel);

			Tools.setUserNameLabelColor(cell.nameLabel, uid);
			cell.comCountry.setCountryFlag(arr[3]);
			cell.comNum.text = arr[5]+"";

			cell.userBtn.off(Event.CLICK,this,userClick);
			cell.userBtn.on(Event.CLICK,this,userClick, [uid]);

			var sgBox:Box = cell["rewardBox"] as Box;
			if(sgBox) {
				sgBox.off(Event.CLICK, this, boxClick);
				sgBox.on(Event.CLICK, this, boxClick, [index]);
			}

			if(cell["heroIcon"]) {
				(cell["heroIcon"] as ComPayType).setHeroIcon(ModelUser.getUserHead(arr[2]));
			}

			var cbBox1:Box = cell["boxToday"] as Box;
			var cbBox2:Box = cell["boxYear"] as Box;
			if(ConfigApp.isLandscape) {//cb
				if(!cell.nameLabel["originalFontSize"]) {
					cell.nameLabel["originalFontSize"] = cell.nameLabel.fontSize;
				}
				cell.nameLabel.fontSize = cell.nameLabel["originalFontSize"];
				cell.nameLabel.text = arr[1];
				var nameWidth:Number = cell.nameLabel.textField.textWidth;
				if(nameWidth >= 100) {
					cell.nameLabel.width = 100;
					Tools.textFitFontSize(cell.nameLabel);
				} else {
					cell.nameLabel.width = nameWidth;
				}
				var m:Number = cell.comCountry.width + cell.nameLabel.width + 2;
				cell.comCountry.x = (cell.width - m) / 2;
				cell.nameLabel.x = cell.comCountry.x + cell.comCountry.width;

				cell.comNum.width = cell.comNum.textField.textWidth;
				var n:Number = cell["imgCredit"]["width"] + cell.comNum.width + 2;
				cell["imgCredit"]["x"] = (cell.width - n) / 2;
				cell.comNum.x = cell["imgCredit"]["x"] + cell["imgCredit"]["width"] + 2;

				cell["tToday"]["text"] = Tools.getMsgById("_credit_text26");
				cell["tYear"]["text"] = Tools.getMsgById("_credit_text36");
				(cell["comToday"] as comIcon).setBgColor(3);
				(cell["comToday"] as comIcon).setNum("");
				var dayItem:String = model.getDayRewradIcon();
				(cell["comToday"] as comIcon).setIcon(ModelItem.getIconUrl(dayItem));
				var yearItem:String = model.getYearRewradIcon(index);
				if(yearItem.indexOf("title") != -1) {
					(cell["comYear"] as comIcon).setData(yearItem, -1, -1, false);
				} else {
					(cell["comYear"] as comIcon).setBgColor(4);
					(cell["comYear"] as comIcon).setIcon(ModelItem.getIconUrl(yearItem));
					(cell["comYear"] as comIcon).setNum("");
				}
				
			} else {
				cell.nameLabel.text = arr[1];
				Tools.textFitFontSize(cell.nameLabel);
			}

			if(cbBox1) {
				cbBox1.off(Event.CLICK, this, boxClick1);
				cbBox1.on(Event.CLICK, this, boxClick1, [index]);
			}

			if(cbBox2) {
				cbBox2.off(Event.CLICK, this, boxClick2);
				cbBox2.on(Event.CLICK, this, boxClick2, [index]);
			}
			
		}

		private function boxClick(index:int):void {
			ViewManager.instance.showView(["ViewCreditGiftTips", ViewCreditGiftTips], {"index":index});
		}

		private function boxClick1(index:int):void {
			var arr:Array = model.getDayRewradByIndex(index);
			ViewManager.instance.showRewardPanel(arr, null, true);
		}

		private function boxClick2(index:int):void {
			var arr:Array = model.getYearRewradByIndex(index);
			ViewManager.instance.showRewardPanel(arr, null, true);
		}

		private function userClick(_id:*):void{
			ModelManager.instance.modelUser.selectUserInfo(_id);
		}

		private function rewardListRender(cell:item_credit2UI, index:int):void {
			if(ConfigApp.isLandscape) {
				cell.zOrder = index * -1;
			}
			if(!cell.progress["orginalX"]) {
				cell.progress["orginalX"] = cell.progress.x;
			}
			if(!cell.progress["orginalWidth"]) {
				cell.progress["orginalWidth"] = cell.progress.width;
			}

			cell.progress.x = cell.progress["orginalX"];
			cell.progress.width = cell.progress["orginalWidth"];
			
			var data:Object = this.rewardList.array[index];
			var box:Box = cell.box;
			box.destroyChildren();
			var _value:Number = data["valueProgress"];
			var _max:Number = data["maxProgress"];
			
			var item:*;
			if(data["rewardKey"] == "up") {
				item = new item_credit2_2UI();
				item["tLv"]["text"] = myLv == -1 ? Tools.getMsgById("_credit_text25") : (myLv + 2) + "";
				Tools.textFitFontSize(item["tLv"], null);
				item["proPanel"].width = Math.min(1, _value / _max) * 108;
				cell.progress.value = _value > 0 ? 1 : 0;
			} else if(data["rewardKey"] == "rool") {
				item = new item_credit2_3UI();
				item["tNum"]["text"] = "x" + data["reward"][1];
				item["tRool"]["text"] = data["rool_num"];
				(item["comReward"] as comIcon).setData(data["reward"][0], -1, -1, false);
				cell.progress.value = _value > 0 ? 1 : 0;
				var sp:Sprite = item["sp"] as Sprite;
				var img:Image = item["img"] as Image;
				var percent:Number = _value / _max;
				var _x:Number = sp.width / 2;
				var _y:Number = sp.height / 2;
				var _r:Number = Math.max(_x, _y);
				sp.graphics.clear();
				if (percent >= 1) {
					sp.graphics.drawPie(_x, _y, _r, 0, 360, '#ff0000');
				} else if (percent === 0) {
				    
				} else {
					var _startAngle:Number = ConfigApp.isLandscape ? 90 : 180;
					var _endAngle:Number = Math.ceil(percent * 360) + _startAngle;
					sp.graphics.drawPie(_x, _y, _r, _startAngle, _endAngle, '#ff0000');
				} 
				var ani:Animation;
				if(item.getChildByName("rool_ani")) {
					ani = item.getChildByName("rool_ani");
				} else {
					ani = EffectManager.loadAnimation("glow041");
					ani.x = item["comReward"]["x"];
					ani.y = item["comReward"]["y"];
					ani.scale(1.3, 1.3);
					ani.name = "rool_ani";
					item.addChild(ani);
				}
				ani.visible = (percent >= 1);	

			} else {
				item = new item_credit2_1UI();
				(item["comReward"] as comIcon).setData(data["reward"][0], data["reward"][1], -1, false);
				(item["comReward"] as comIcon).setSpecial(data["can_get"] && !data["is_get"]);
				item["imgGet"]["visible"] = data["is_get"];
				item["comReward"]["gray"] = item["imgGet"]["visible"];
				cell.progress.value = _value >= _max ? 1 : _value / _max;
				if(index == 0) {
					cell.progress.width += cell.progress.x;
					cell.progress.x = 0;
				}
				if(cell.progress.value < 1 && cell.progress.value > 0.9)
					cell.progress.value = 0.9;
				
			}

			box.addChild(item);

			(item["comPay"] as ComPayType).setData(ModelItem.getIconUrl(ModelProp.ITEM_CREIDT), data["credit"]);

			box.off(Event.CLICK, this, rewardClick);
			box.on(Event.CLICK, this, rewardClick, [index]);

		}

		private function rewardClick(index:int):void {
			if(model.isCanGet) {
				// 一键全领
				NetSocket.instance.send("get_credit_gift_v1", {}, new Handler(this,function(np:NetPackage):void{
					ModelManager.instance.modelUser.updateData(np.receiveData);
					ViewManager.instance.showRewardPanel(np.receiveData.gift_dict_list);
					updateUI();
				}));
			} else {
				var data:Object = this.rewardList.array[index];
				if(data["reward"]) {
					ViewManager.instance.showItemTips(data["reward"][0]);
				}
			}
		}

		private function scrollChange(v:int):void {
			if(v >= scrollBarMax) {
				this.rewardList.scrollBar.value = scrollBarMax;
				return;
			}
		}

		override public function onRemoved():void {
			ModelManager.instance.modelUser.off(ModelUser.EVENT_UPDATE_CREDIT, this, creditCallBack);
			ModelManager.instance.modelUser.off(ModelUser.EVENT_IS_NEW_DAY,this, newDayCallBack);
			Laya.timer.clear(this, onTimer);
		}

	}

}