{
#杀人战功=击杀部队数*双方战力倍数*旗子*爵位加成*玩家或者npc系数*精英系数*杀人系数
#牺牲战功=牺牲部队数量*部队等级系数*爵位*旗子*牺牲系数
	'army_ratio':[1,1.5,2,2.5,3,3.5],        #分别对应部队armylv的1,2,3,4,5,6[1,1.5,2,2.5,3,3.5],  
	'power_by':[0.5,10],					#双方战力倍数最大值和最小值，取敌方战力/我方战力[0.5,10],
	'play_npc':[3,1],           #玩家系数，NPC系数[3,1],   
	'guard_lv':[1,1.2,1.5],     #NPC中的[普通，精英，大将]战功参数[1,1.2,1.5],
	'kill_ratio':0.06,	  #0.06				#杀人系数0.06,	
	'dead_ratio':0.08,#0.08					#牺牲系数0.08,
        'credit_info':'credit_info1',    #战功获得解释
        'credit_result_hero':'hero407', #战功结算显示的英雄id
 
	'clv_first':[192,300,448,624,846,1172,1642,2316,3244,4000,4728,5406,6000,6572,7048,7420,7696,7866,7940,7968],    #每一个等级的第一个数
	'clv_first_ratio':[2,4,6,10,13,16,20,23,26,32,35,38,43,47,50],       #15档奖励参数
	'clv_up':[12120,18600,27020,36960,49590,66940,90950,124260,169160,210200,248100,283980,315300,343420,367100,386060,400580,410310,415600,],    #升级所需要的数180000
        'clv_max':418200,#20级时，达到这个数值之后进入额外等级
	'clv_added':[2000,3000,4200,5600,7400,9600,12400,16000,20600,26000,30600,35100,39000,42000,44600,46800,48600,50000,51000,51600],
	'clv_added_ratio':[8,9,10,11,12,13,14,15,16,17,18,19,20,21,22],
	
 'clv_first_reward':[
  [['item109',1],['merit',400],['item001',5],['item100',1],['merit',500],['item132',1],['item101',1],['gold',200000],['item020',2],['item055',50],['wood',400000],['merit',500],['item032',10],['item114',1],['merit',600]],
  [['item109',1],['merit',500],['item002',2],['item100',1],['merit',1150],['item133',1],['item101',1],['gold',250000],['item020',2],['item056',60],['wood',450000],['merit',850],['item032',10],['item115',1],['item057',100]],
  [['item109',1],['merit',600],['item003',1],['item056',20],['merit',1400],['item133',1],['item102',1],['gold',300000],['item020',3],['item056',70],['wood',500000],['merit',1000],['item032',10],['item115',1],['item057',110]],
  [['item109',1],['merit',700],['item003',1],['item056',25],['merit',1650],['item133',1],['item102',1],['gold',350000],['item020',3],['item056',80],['wood',550000],['merit',1150],['item032',10],['item116',1],['item057',120]],
  [['item109',1],['merit',800],['item004',1],['item056',30],['merit',1850],['item134',1],['item104',1],['gold',400000],['item020',4],['item056',100],['wood',600000],['merit',1350],['item032',10],['item116',1],['item057',130]],
  [['item109',1],['merit',900],['item004',1],['item056',40],['merit',2100],['item134',1],['item104',1],['gold',450000],['item020',4],['item056',110],['wood',650000],['merit',1500],['item032',10],['item117',1],['item057',140]],
  [['item109',1],['merit',1000],['item004',1],['item056',45],['merit',2350],['item135',1],['item104',1],['gold',500000],['item020',5],['item056',120],['wood',700000],['merit',1650],['item032',10],['item117',1],['item057',150]],
  [['item109',1],['merit',1100],['item004',2],['item056',50],['merit',2560],['item136',1],['item105',1],['gold',550000],['item020',5],['item056',130],['wood',750000],['merit',1850],['item032',10],['item118',1],['item057',160]],
  [['item110',1],['merit',1200],['item004',2],['item056',55],['merit',2800],['item136',1],['item105',1],['gold',600000],['item021',2],['item056',140],['wood',800000],['merit',2000],['item032',10],['item118',1],['item057',170]],
  [['item110',1],['merit',1300],['item004',2],['item056',60],['merit',3050],['item137',1],['item105',1],['gold',650000],['item021',2],['item056',150],['wood',850000],['merit',2150],['item032',10],['item118',1],['item057',180]],
  [['item110',1],['merit',1400],['item004',2],['item056',65],['merit',3250],['item137',1],['item105',1],['gold',700000],['item021',2],['item056',160],['wood',900000],['merit',2350],['item032',10],['item118',1],['item057',190]],
  [['item110',1],['merit',1500],['item004',2],['item056',70],['merit',3500],['item138',1],['item105',1],['gold',750000],['item021',2],['item056',170],['wood',950000],['merit',2500],['item032',10],['item118',1],['item057',200]],
  [['item110',1],['merit',1600],['item004',3],['item056',75],['merit',3750],['item138',1],['item106',1],['gold',800000],['item021',2],['item056',180],['wood',1000000],['merit',2650],['item032',10],['item119',1],['item057',210]],
  [['item110',1],['merit',1700],['item004',3],['item056',80],['merit',3950],['item138',1],['item106',1],['gold',850000],['item021',2],['item056',190],['wood',1050000],['merit',2850],['item032',10],['item119',1],['item057',220]],
  [['item110',1],['merit',1800],['item004',3],['item056',85],['merit',4200],['item139',1],['item106',1],['gold',900000],['item021',2],['item056',200],['wood',1100000],['merit',3000],['item032',10],['item119',1],['item057',230]],
  [['item111',1],['merit',1900],['item004',3],['item056',90],['merit',4450],['item139',1],['item106',1],['gold',950000],['item021',2],['item056',210],['wood',1150000],['merit',3150],['item032',10],['item119',1],['item057',240]],
  [['item111',1],['merit',2000],['item004',3],['item056',95],['merit',4650],['item139',1],['item106',1],['gold',1000000],['item021',2],['item056',220],['wood',1200000],['merit',3350],['item032',10],['item119',1],['item057',250]],
  [['item111',1],['merit',2100],['item004',3],['item056',100],['merit',4900],['item140',1],['item106',1],['gold',1050000],['item021',2],['item056',230],['wood',1250000],['merit',3500],['item032',10],['item119',1],['item057',260]],
  [['item111',1],['merit',2200],['item004',3],['item056',105],['merit',5150],['item140',1],['item106',1],['gold',1100000],['item021',2],['item056',240],['wood',1300000],['merit',3650],['item032',10],['item120',1],['item057',270]],
  [['item111',1],['merit',2300],['item004',3],['item056',110],['merit',5300],['item140',1],['item106',1],['gold',1150000],['item021',2],['item056',250],['wood',1350000],['merit',3850],['item032',10],['item120',1],['item057',280]],
 ],

 'clv_added_reward':[
  [['item109',1],['merit',500],['gold',250000],['item056',15],['merit',800],['food',250000],['item101',1],['wood',300000],['item020',2],['item056',45],['food',350000],['merit',1000],['item032',10],['item114',1],['item057',135]],
  [['item109',1],['merit',600],['gold',270000],['item056',20],['merit',1000],['food',270000],['item101',1],['wood',340000],['item020',2],['item056',50],['food',390000],['merit',1100],['item032',10],['item115',1],['item057',150]],
  [['item109',1],['merit',700],['gold',290000],['item056',25],['merit',1200],['food',290000],['item102',1],['wood',380000],['item020',3],['item056',55],['food',430000],['merit',1200],['item032',10],['item115',1],['item057',165]],
  [['item109',1],['merit',800],['gold',310000],['item056',30],['merit',1400],['food',310000],['item102',1],['wood',420000],['item020',3],['item056',60],['food',470000],['merit',1300],['item032',10],['item116',1],['item057',180]],
  [['item109',1],['merit',900],['gold',330000],['item056',35],['merit',1600],['food',330000],['item104',1],['wood',460000],['item020',4],['item056',65],['food',510000],['merit',1400],['item032',10],['item116',1],['item057',195]],
  [['item109',1],['merit',1000],['gold',350000],['item056',40],['merit',1800],['food',350000],['item104',1],['wood',500000],['item020',4],['item056',70],['food',550000],['merit',1500],['item032',10],['item117',1],['item057',210]],
  [['item109',1],['merit',1100],['gold',370000],['item056',45],['merit',2000],['food',370000],['item104',1],['wood',540000],['item020',5],['item056',75],['food',590000],['merit',1600],['item032',10],['item117',1],['item057',225]],
  [['item109',1],['merit',1200],['gold',390000],['item056',50],['merit',2200],['food',390000],['item105',1],['wood',580000],['item020',5],['item056',80],['food',630000],['merit',1700],['item032',10],['item118',1],['item057',240]],
  [['item110',1],['merit',1300],['gold',410000],['item056',55],['merit',2400],['food',410000],['item105',1],['wood',620000],['item021',2],['item056',85],['food',670000],['merit',1800],['item032',10],['item118',1],['item057',255]],
  [['item110',1],['merit',1400],['gold',430000],['item056',60],['merit',2600],['food',430000],['item105',1],['wood',660000],['item021',2],['item056',90],['food',710000],['merit',1900],['item032',10],['item118',1],['item057',270]],
  [['item110',1],['merit',1500],['gold',450000],['item056',65],['merit',2800],['food',450000],['item105',1],['wood',700000],['item021',2],['item056',95],['food',750000],['merit',2000],['item032',10],['item118',1],['item057',285]],
  [['item110',1],['merit',1600],['gold',470000],['item056',70],['merit',3000],['food',470000],['item105',1],['wood',740000],['item021',2],['item056',100],['food',790000],['merit',2100],['item032',10],['item118',1],['item057',300]],
  [['item110',1],['merit',1700],['gold',490000],['item056',75],['merit',3200],['food',490000],['item106',1],['wood',780000],['item021',2],['item056',105],['food',830000],['merit',2200],['item032',10],['item119',1],['item057',315]],
  [['item110',1],['merit',1800],['gold',510000],['item056',80],['merit',3400],['food',510000],['item106',1],['wood',820000],['item021',2],['item056',110],['food',870000],['merit',2300],['item032',10],['item119',1],['item057',330]],
  [['item110',1],['merit',1900],['gold',530000],['item056',85],['merit',3600],['food',530000],['item106',1],['wood',860000],['item021',2],['item056',115],['food',910000],['merit',2400],['item032',10],['item119',1],['item057',345]],
  [['item111',1],['merit',2000],['gold',550000],['item056',90],['merit',3800],['food',550000],['item106',1],['wood',900000],['item021',2],['item056',120],['food',950000],['merit',2500],['item032',10],['item119',1],['item057',360]],
  [['item111',1],['merit',2100],['gold',570000],['item056',95],['merit',4000],['food',570000],['item106',1],['wood',940000],['item021',2],['item056',125],['food',990000],['merit',2600],['item032',10],['item119',1],['item057',375]],
  [['item111',1],['merit',2200],['gold',590000],['item056',100],['merit',4200],['food',590000],['item106',1],['wood',980000],['item021',2],['item056',130],['food',1030000],['merit',2700],['item032',10],['item119',1],['item057',390]],
  [['item111',1],['merit',2300],['gold',610000],['item056',105],['merit',4400],['food',610000],['item106',1],['wood',1020000],['item021',2],['item056',135],['food',1070000],['merit',2800],['item032',10],['item120',1],['item057',405]],
  [['item111',1],['merit',2400],['gold',630000],['item056',110],['merit',4600],['food',630000],['item106',1],['wood',1060000],['item021',2],['item055',140],['food',1110000],['merit',2900],['item032',10],['item120',1],['item057',420]],
 ],

 'clv_rool_reward':[#战功循环宝箱
  [{'item142':1},25000],
  [{'item143':1},30000],
  [{'item144':1},35000],
  [{'item145':1},40000],
  [{'item146':1},45000],
  [{'item147':1},50000],
  [{'item148':1},55000],
  [{'item149':1},60000],
  [{'item150':1},65000],
  [{'item151':1},70000],
  [{'item152':1},75000],
  [{'item153':1},80000],
  [{'item154':1},85000],
  [{'item155':1},90000],
  [{'item156':1},95000],
  [{'item157':1},100000],
  [{'item158':1},105000],
  [{'item159':1},110000],
  [{'item160':1},115000],
  [{'item161':1},120000],
 ], 


	'list_reward_time':[[22,0],[22,0]],       #每日奖励时间，每年冬季奖励时间
        'credit_mailday_name':'credit_mailday_name',     #每日邮件title
        'credit_mailday_info':'credit_mailday_info',
        'credit_mailyear_name':'credit_mailyear_name',     #未领取战功补偿奖励
        'credit_mailyear_info':'credit_mailyear_info',
	'list_reward_long':100,                    	#排行榜只排100名
	'list_reward_day':[
		[1,['merit',1000],['gold',100000]],      #每日排名结算奖励
		[2,['merit',900],['gold',90000]],
		[3,['merit',800],['gold',80000]],
		[4,['merit',700],['gold',70000]],
		[5,['merit',600],['gold',60000]],
		[6,['merit',500],['gold',50000]],
		[11,['merit',450],['gold',45000]],
		[21,['merit',400],['gold',40000]],
		[31,['merit',350],['gold',35000]],
		[41,['merit',300],['gold',30000]],
		[51,['merit',250],['gold',25000]],
		[81,['merit',200],['gold',20000]],
	],
	'list_reward_year':[
		[1,['title024'],['item401',80],['item402',80],['item403',80],['item404',80],],      #每年排名结算奖励
		[2,['title025'],['item401',60],['item402',60],['item403',60],['item404',60],],
		[3,['title026'],['item401',60],['item402',60],['item403',60],['item404',60],],
		[6,['title027'],['item401',40],['item402',40],['item403',40],['item404',40],],
		[11,['title028'],['item401',35],['item402',35],['item403',35],['item404',35],],
		[21,['title029','title030','title031','title032'],['item401',30],['item402',30],['item403',30],['item404',30],],
		[51,[],['item401',25],['item402',25],['item403',25],['item404',25],],
	],
	

################合服后的战功配置#####################################
	'merge_1':{
		'clv_first':[8000],    #每一个等级的第一个数
		'clv_first_ratio':[2,4,6,10,13,16,20,23,26,32,35,38,43,47,50],       #15档奖励参数
		'clv_up':[],    #升级所需要的数180000
		'clv_max':420000,#20级时，达到这个数值之后进入额外等级
		'clv_added':[52000],
		'clv_added_ratio':[9,10,11,12,13,14,15,16,17,18,19,20,21,22,23],
		'clv_first_reward':[
  [['item111',1],['merit',2000],['item189',20],['gold',500000],['item191',3],['merit',5000],['item140',1],['item189',40],['item056',200],['item191',6],['merit',4000],['item120',1],['item189',60],['item057',250],['item191',9],
 ],
	  ],
		'clv_added_reward':[
		  [['item111',1],['merit',2400],['gold',500000],['item056',80],['merit',4000],['food',500000],['item106',1],['wood',860000],['item021',2],['item056',100],['food',860000],['merit',2900],['item032',10],['item120',1],['item057',400]]
		],
		'clv_rool_reward':[#战功循环宝箱
  		[{'item196':1},120000],
  	],
	},

################合服后的战功配置#####################################
	'merge_2':{
		'clv_first':[12000],    #每一个等级的第一个数
		'clv_first_ratio':[2,4,6,10,13,16,20,23,26,32,35,38,43,47,50],       #15档奖励参数
		'clv_up':[],    #升级所需要的数180000
		'clv_max':630000,#20级时，达到这个数值之后进入额外等级
		'clv_added':[78000],
		'clv_added_ratio':[9,10,11,12,13,14,15,16,17,18,19,20,21,22,23],
		'clv_first_reward':[
[['item111',1],['merit',2600],['item189',26],['gold',650000],['item191',4],['merit',6500],['item140',1],['item189',52],['item056',260],['item191',8],['merit',5200],['item120',1],['item189',78],['item057',325],['item191',12],]

	  ],
		'clv_added_reward':[
[['item111',1],['merit',3200],['gold',650000],['item056',100],['merit',5200],['food',650000],['item106',1],['wood',1100000],['item021',2],['item056',130],['food',1100000],['merit',3800],['item032',15],['item120',1],['item057',520],]

		],
		'clv_rool_reward':[#战功循环宝箱
  		[{'item196':1},120000],
  	],
	},
################合服后的战功配置#####################################
	'merge_3':{
		'clv_first':[12000],    #每一个等级的第一个数
		'clv_first_ratio':[2,4,6,10,13,16,20,23,26,32,35,38,43,47,50],       #15档奖励参数
		'clv_up':[],    #升级所需要的数180000
		'clv_max':630000,#20级时，达到这个数值之后进入额外等级
		'clv_added':[78000],
		'clv_added_ratio':[9,10,11,12,13,14,15,16,17,18,19,20,21,22,23],
		'clv_first_reward':[
[['item111',1],['merit',2600],['item189',26],['gold',650000],['item191',4],['merit',6500],['item140',1],['item189',52],['item056',260],['item191',8],['merit',5200],['item120',1],['item189',78],['item057',325],['item191',12],]

	  ],
		'clv_added_reward':[
[['item111',1],['merit',3200],['gold',650000],['item056',100],['merit',5200],['food',650000],['item106',1],['wood',1100000],['item021',2],['item056',130],['food',1100000],['merit',3800],['item032',15],['item120',1],['item057',520],]

		],
		'clv_rool_reward':[#战功循环宝箱
  		[{'item196':1},120000],
  	],
	},

################合服后的战功配置#####################################
	'merge_4':{
		'clv_first':[18000],    #每一个等级的第一个数
		'clv_first_ratio':[2,4,6,10,13,16,20,23,26,32,35,38,43,47,50],       #15档奖励参数
		'clv_up':[],    #升级所需要的数180000
		'clv_max':950000,#20级时，达到这个数值之后进入额外等级
		'clv_added':[115000],
		'clv_added_ratio':[9,10,11,12,13,14,15,16,17,18,19,20,21,22,23],
  'clv_first_reward':[
[['item111',1],['merit',3500],['item189',26],['gold',700000],['item191',4],['merit',7000],['item140',1],['item189',52],['item046',1],['item191',8],['item120',1],['item189',78],['item057',325],['item191',12],['item046',1],
              ] ],
  'clv_added_reward':[
[['item111',1],['merit',4500],['gold',700000],['item046',1],['merit',9000],['food',700000],['item106',1],['wood',1200000],['item021',2],['food',1200000],['item032',20],['item046',1],['iron',1200000],['item120',1],['item057',520],
              ] ],


		'clv_rool_reward':[#战功循环宝箱
  		[{'item196':1},120000],
  	],
	},

################合服后的战功配置#####################################
	'merge_5':{
		'clv_first':[18000],    #每一个等级的第一个数
		'clv_first_ratio':[2,4,6,10,13,16,20,23,26,32,35,38,43,47,50],       #15档奖励参数
		'clv_up':[],    #升级所需要的数180000
		'clv_max':950000,#20级时，达到这个数值之后进入额外等级
		'clv_added':[115000],
		'clv_added_ratio':[9,10,11,12,13,14,15,16,17,18,19,20,21,22,23],
  'clv_first_reward':[
[['item111',1],['merit',3500],['item189',26],['gold',700000],['item191',4],['merit',7000],['item140',1],['item189',52],['item046',1],['item191',8],['item120',1],['item189',78],['item057',325],['item191',12],['item046',1],
              ] ],
  'clv_added_reward':[
[['item111',1],['merit',4500],['gold',700000],['item046',1],['merit',9000],['food',700000],['item106',1],['wood',1200000],['item021',2],['food',1200000],['item032',20],['item046',1],['iron',1200000],['item120',1],['item057',520],
              ] ],


		'clv_rool_reward':[#战功循环宝箱
  		[{'item196':1},120000],
  	],
	},
################合服后的战功配置#####################################
	'merge_6':{
		'clv_first':[18000],    #每一个等级的第一个数
		'clv_first_ratio':[2,4,6,10,13,16,20,23,26,32,35,38,43,47,50],       #15档奖励参数
		'clv_up':[],    #升级所需要的数180000
		'clv_max':950000,#20级时，达到这个数值之后进入额外等级
		'clv_added':[115000],
		'clv_added_ratio':[9,10,11,12,13,14,15,16,17,18,19,20,21,22,23],
  'clv_first_reward':[
[['item111',1],['item2002',25],['item189',26],['item2003',10],['item191',4],['item2007',2],['item2123',10],['item189',52],['item046',1],['item191',8],['item120',1],['item189',78],['item057',325],['item191',12],['item046',1],
              ] ],
  'clv_added_reward':[
[['item111',1],['item2002',25],['gold',700000],['item046',1],['merit',9000],['item2123',20],['item106',1],['wood',1200000],['item021',2],['food',1200000],['item032',20],['item046',1],['item2123',20],['item120',1],['item057',520],
              ] ],
		'clv_rool_reward':[#战功循环宝箱
  		[{'item2194':1},1000000],
  	],
	},
################合服后的战功配置#####################################
	'merge_7':{
		'clv_first':[18000],    #每一个等级的第一个数
		'clv_first_ratio':[2,4,6,10,13,16,20,23,26,32,35,38,43,47,50],       #15档奖励参数
		'clv_up':[],    #升级所需要的数180000
		'clv_max':950000,#20级时，达到这个数值之后进入额外等级
		'clv_added':[115000],
		'clv_added_ratio':[9,10,11,12,13,14,15,16,17,18,19,20,21,22,23],
  'clv_first_reward':[
[['item111',1],['item2002',25],['item189',26],['item2003',10],['item191',4],['item2007',2],['item2123',10],['item189',52],['item046',1],['item191',8],['item120',1],['item189',78],['item057',325],['item191',12],['item046',1],
              ] ],
  'clv_added_reward':[
[['item111',1],['item2002',25],['gold',700000],['item046',1],['merit',9000],['item2123',20],['item106',1],['wood',1200000],['item021',2],['food',1200000],['item032',20],['item046',1],['item2123',20],['item120',1],['item057',520],
              ] ],


		'clv_rool_reward':[#战功循环宝箱
  		[{'item2194':1},1000000],
  	],
	},
}