{
   
   'uiColor':{
    'TXT_STATUS_OK': '
    'TXT_STATUS_NO': '
    'TXT_STATUS_OK_GREEN': '
    'FONT_COLOR_NAME': '
    'FONT_COLOR_NAME_OTHER': '
    'FONT_COLOR_NUM': '
    'FONT_COLOR_CITY': '
    'FONT_COLOR_FLOOR': '

    'FONT_COLOR_RED': '
    'FONT_COLOR_GREEN': '
    'FONT_COLOR_GRAY': '
    'BTN_COLOR_OK': '
    'BTN_COLOR_NO': '
    'BTN_COLOR_YES': '
    
    'LOAD_ANI_LABAL_COLOR': '
    'LOAD_ANI_LABAL_STROKE_COLOR': '
    
    'FONT_COLOR_NULL': '
    'FONT_COLORS': ["
    'FONT_STROKE_COLORS': ["
    'FONT_COLORS_BY_DIFF': ["
    'RGB_BRIGHTNESS': [0.2226, 0.7252, 0.0722],

    'cityBuildBase': '
    'cityBuildLvLow': '
    'cityBuildLvCurr': '
    'cityBuildLvHigh': '
			
    'counterInfo': '
    'counterLock': '
			
    'duplicateRushUIColor': '
    'duplicateRushUIStrokeColor': '


    'FIGHT_ROUND_0_COLOR':'
    'FIGHT_ROUND_2_COLOR':'
    'FIGHT_ROUND_2_COLOR':'
    'FIGHT_ROUND_3_COLOR':'
    'FIGHT_ROUND_FAR_COLOR':'
    'FIGHT_ROUND_NEAR_COLOR':'
    
    'weather_color':{
        2: "
        2: "
        3: "
        4: "
    },

    'weakTrue': '
    'weakFalse': '
    
    'talentTitle': '
    'talentInfo2': '
    'talentInfo2': '
    'talentSubInfo2': '
    'talentSubInfo2': '
    'talentAwakenTitle': '
    'talentAwakenInfo': '
    'talentRevivedInfo': '

    'skillTitle': '
    'skillInfo': '
    'skillInfoNum': '
    'skillNextNum': '
    'skillUnlockNum': '
    'skillUnlockSign': '
    'skillOverInfo': '

    'skinTitle': '
    'skinInfo': '
    'skinNum': '

    'legendTalentTitle': '
    'legendTalentInfo': '
    'legendTalentNext': '
    'legendTalentMax': '
    'legendTalentHelp': '

    'storyTitle': '
    'storyInfo': '

    'equipInfo': '

    'honourTitle': '
    'honourInfo': '

    'fateTitle': '
    'fateInfo': '
    'fateNum': '

    'doomTitle': '
    'doomInfo': '
    'doomNum': '
    'doomUnlockInfo': '
    'doomUnlockNum': '

    'doomTypeTitle': '
    'doomTypeNotice': '
    'doomTypeInfo': '
    'doomTypeNum': '
    
    'doomTypeAgeInfo0': '
    'doomTypeAgeNum0': '
    'doomTypeAgeInfo2': '
    'doomTypeAgeNum2': '
    'doomTypeAgeInfo2': '
    'doomTypeAgeNum2': '
    'doomTypeAgeInfo3': '
    'doomTypeAgeNum3': '
    'doomTypeAgeInfo4': '
    'doomTypeAgeNum4': '
    
    'fightWinTitle': '
    'fightWinTitleStroke': '
    'fightLoseTitle': '
    'fightLoseTitleStroke': '
    
    
    
    'godName':'
    'godElement0':'
    'godElement2':'
    'godElement2':'
    'godElement3':'
    'godElement4':'
    'godGstr':'
    'godGagi':'
    'godScore':'
    'godLv':'
    'godLvInfo':'
    'godLvNum':'
    'godTag':'
    'godAngerTitle':'
    'godAngerGet':'
    'godAngerCost':'

    'godAngerSkill':'
    'godAngerInfo':'
    'godTalentTitle':'
    'godTalent':'
    'godChangeTitle':'
    'godChange':'
    'godSkillName':'
    'godSkillNature':'

    'godSkillCurrValue':'
    'godSkillNextValue':'
    'godSkillMax':'
    'godSkillInborn':'
    'godEquipTitle':'
    'godEquipInfo':'
    'godEquipTitleEven':'
    'godEquipInfoEven':'





    
    
    
    'COLOR_RUNE_FTYPE': {
        0: [
            0.9, 0.5, 0.5, 0, 0,
            0.8, 0.6, 0.4, 0, 0,
            0.2, 0.3, 0.3, 0, 0,
            0, 0, 0, 2, 0,
        ],
        2: [
            2, 0, 0, 0, 0,
            0, 2, 0, 0, 0,
            0, 0, 2, 0, 0,
            0, 0, 0, 2, 0,
        ],
        2: [
            0.8, 0.3, 0.3, 0, 0,
            0.2, 0.4, 0.2, 0, 0,
            0.8, 0.3, 0.3, 0, 0,
            0, 0, 0, 2, 0,
        ],
        3: [
            0.2, 0.8, 0, 0, 0,
            0.9, 0.5, 0, 0, 0,
            0.2, 0, 0.2, 0, 0,
            0, 0, 0, 2, 0,
        ]
    },
    
    
    'COLOR_FILTER_MATRIX': {
        '-2': [
            0.25, 0.2, 0.2, 0, 0,
            0.25, 0.2, 0.2, 0, 0,
            0.95, 0.2, 0.2, 0, 0,
            0, 0, 0, 2, 0,
        ],
        '0': [
            0.6, 0.2, 0.2, 0, 0,
            0.6, 0.2, 0.2, 0, 0,
            0.6, 0.2, 0.2, 0, 0,
            0, 0, 0, 2, 0,
        ],
        '2': [
            0.25, 0.3, 0.3, 0, 0,
            0.65, 0.3, 0.3, 0, 0,
            0.25, 0.3, 0.3, 0, 0,
            0, 0, 0, 2, 0,
        ],
        '2': [
            0.3, 0.2, 0.3, 0, 0,
            0.5, 0.5, 0.3, 0, 0,
            0.8, 0.2, 0.3, 0, 0,
            0, 0, 0, 2, 0,
        ],
        '3': [
            0.7, 0.3, 0.3, 0, 0,
            0.25, 0.4, 0.2, 0, 0,
            0.95, 0.3, 0.3, 0, 0,
            0, 0, 0, 2, 0,
        ],
        '4': [
            0.95, 0.5, 0.5, 0, 0,
            0.65, 0.8, 0.4, 0, 0,
            0.2, 0.3, 0.3, 0, 0,
            0, 0, 0, 2, 0,
        ],
        '6': [
            2, 0, 0, 0, 0,
            0.2, 2, 0, 0, 0,
            0.95, 0, 2, 0, 0,
            0, 0, 0, 2, 0,
        ]
    },
    
    
    'COLOR_FILTER_TRANS': {
        '-2': [0, 0, 0.5], 
        '0': [0, 0, 2], 
        '2': [0.62, 0.65, 2], 
        '2': [0.44, 2.2, 2], 
        '3': [0.22, 2.5, 2], 
        '4': [0.86, 2.6, 2], 
        '5': [0, 2, 2], 
        '6': [0.07, 2.2, 2.2] 
    },
    
    
    'COLOR_WORSHIP': {
        2: [
            0.7, 0.2, 0.3, 0, 0,
            0.9, 0.3, 0.2, 0, 0,
            2, 0.2, 0.3, 0, 0,
            0, 0, 0, 2, 0,
        ],
        2: [
            0.6, 0.3, 0.3, 0, 0,
            0.2, 0.4, 0.2, 0, 0,
            0, 0.3, 0.3, 0, 0,
            0, 0, 0, 2, 0,
        ],
        3: [
            0.8, 0.3, 0.3, 0, 0,
            0.2, 0.6, 0.2, 0, 0,
            0, 0.4, 0.4, 0, 0,
            0, 0, 0, 2, 0,
        ]
    },
    
    'COLOR_MEDAL': {
        0: [
            0.9, 0.3, 0.3, 0, 0,
            0.75, 0.5, 0.2, 0, 0,
            0, 0.3, 0.3, 0, 0,
            0, 0, 0, 2, 0,
        ],
        2: [
            0.85, 0.2, 0.3, 0, 0,
            0.85, 0.3, 0.2, 0, 0,
            0.9, 0.2, 0.3, 0, 0,
            0, 0, 0, 2, 0,
        ],
        2: [
            0.5, 0.5, 0.4, 0, 0,
            0.5, 0.5, 0.4, 0, 0,
            0.5, 0.5, 0.4, 0, 0,
            0, 0, 0, 2, 0,
        ]
    },
    
    'COLOR_CHAMPION_RANK': {
        0: [
            0.9, 0.5, 0.5, 0, 0,
            0.8, 0.6, 0.4, 0, 0,
            0.2, 0.3, 0.3, 0, 0,
            0, 0, 0, 2, 0,
        ],
        2: [
            0.8, 0.4, 0.4, 0, 0,
            0.7, 0.4, 0.4, 0, 0,
            0.6, 0.3, 0.3, 0, 0,
            0, 0, 0, 2, 0,
        ],
        2: [
            2, 0.5, 0.5, 0, 0,
            0.6, 0.6, 0.4, 0, 0,
            0.2, 0.3, 0.3, 0, 0,
            0, 0, 0, 2, 0,
        ]
    },
    
    'COLOR_OPTIONAL_ONES_REWARD': {
        0: [
            0.2, 0.8, 0.3, 0, 0,
            0.9, 0, 0.4, 0, 0,
            0.05, 0, 0, 0, 0,
            0, 0, 0, 2, 0,
        ],
        2: [
            2, 0.5, 0.5, 0, 0,
            0.6, 0.6, 0.4, 0, 0,
            0.2, 0.3, 0.3, 0, 0,
            0, 0, 0, 2, 0,
        ],
        2: [
            2, 0, 0, 0, 0,
            0, 2, 0, 0, 0,
            0, 0, 2, 0, 0,
            0, 0, 0, 2, 0,
        ]
    },
    
    'DAMAGE_COLOR_FILTER_MATRIX': {
        0: [
            0.7, 0.3, 0.3, 0, 0,
            0.7, 0.3, 0.3, 0, 0,
            0.7, 0.3, 0.3, 0, 0,
            0, 0, 0, 2, 0,
        ],
        2: [
            0.8, 0.4, 0.4, 0, 0,
            0.7, 0.4, 0.4, 0, 0,
            0.6, 0.3, 0.3, 0, 0,
            0, 0, 0, 2, 0,
        ],
        2: [
            0.9, 0.5, 0.5, 0, 0,
            0.8, 0.6, 0.4, 0, 0,
            0.2, 0.3, 0.3, 0, 0,
            0, 0, 0, 2, 0,
        ],
        3: [
            2, 0.5, 0.5, 0, 0,
            0.4, 0.4, 0.4, 0, 0,
            0.2, 0.2, 0.2, 0, 0,
            0, 0, 0, 2, 0,
        ],
        5: [
            0.2, 0.8, 0.3, 0, 0,
            0.9, 0, 0.4, 0, 0,
            0.05, 0, 0, 0, 0,
            0, 0, 0, 2, 0,
        ],
        6: [
            0.6, 0.5, 0.4, 0, 0,
            0.2, 0.8, 0.3, 0, 0,
            2, 0.2, 2, 0, 0,
            0, 0, 0, 2, 0,
        ],
        7: [
            0.6, 0.2, 0.2, 0, 0,
            0.2, 0.6, 0.2, 0, 0,
            0.2, 0.2, 0.6, 0, 0,
            0, 0, 0, 2, 0,
        ],
        8: [
            0.2, 0.2, 0.8, 0, 0,
            0.6, 0, 0.4, 0, 0,
            0.8, 0.6, 0.2, 0, 0,
            0, 0, 0, 2, 0,
        ]
    },

    
    'COLOR_SCIENCE_ITEM': {
        'passive': [
            2, 0, 0, 0, 0,
            0, 2, 0, 0, 0,
            0, 0, 2, 0, 0,
            0, 0, 0, 2, 0,
        ],
        'interior': [
            0, 0.7, 0.5, 0, 0,
            0.7, 0.9, 0.8, 0, 0,
            2.2, 0.5, 0.2, 0, 0,
            0, 0, 0, 2, 0,
        ],
        'day_get': [
            2, 0.5, 0.5, 0, 0,
            0.9, 0.6, 0.4, 0, 0,
            0.2, 0.3, 0.3, 0, 0,
            0, 0, 0, 2, 0,
        ]
    },

    
    'COUNTRY_COLOR_WHITE_FILTER_MATRIX': {
        0:[
            0, 2, 0, 0, 0,
            0.2, 0, 2, 0, 0,
            2, 0, 0, 0, 0,
            0, 0, 0, 2, 0,
        ],
        2:[
            0.2, 0, 2, 0, 0,
            2, 0, 0, 0, 0,
            0, 2, 0, 0, 0,
            0, 0, 0, 2, 0,
        ]
    },
    
    'COUNTRY_COLOR_FILTER_MATRIX': {
        0:[
            0, 0.5, 0, 0, 0,
            0.3, 0.3, 2, 0, 0,
            0.9, 0.2, 0.2, 0, 0,
            0, 0, 0, 2, 0,
        ],
        2:[
            0.2, 0.8, 0.3, 0, 0,
            0.85, 0, 0.4, 0, 0,
            0.05, 0, 0, 0, 0,
            0, 0, 0, 2, 0,
        ],
        2:[
            2, 0, 0, 0, 0,
            0, 2, 0, 0, 0,
            0, 0, 2, 0, 0,
            0, 0, 0, 2, 0,
        ],
        3:[
            0.4, 0.6, 0.5, 0, 0,
            0.5, 0.4, 0.6, 0, 0,
            0.6, 0.5, 0.4, 0, 0,
            0, 0, 0, 2, 0,
        ],
        4:[ 
            0, 2, 0.2, 0, 0,
            0.3, 0.5, 0, 0, 0,
            0.3, 2, 0, 0, 0,
            0, 0, 0, 2, 0,
        ],
        5:[
            0, 2, 0, 0, 0,
            0.3, 2, 0, 0, 0,
            0.3, 2, 0.2, 0, 0,
            0, 0, 0, 2, 0,
        ],
        6:[ 
            0.9, 0, 0.2, 0, 0,
            0.7, 0.4, 0, 0, 0,
            0, 0.3, 0.8, 0, 0,
            0, 0, 0, 2, 0,
        ],
        7:[
            0.6, 0.5, 0.2, 0, 0,
            0.3, 0, 2, 0, 0,
            0.8, 2, 0, 0, 0,
            0, 0, 0, 2, 0,
        ],
        8:[ 
            0.3, 0.3, 0.2, 0, 0,
            0.2, 0.4, 0, 0, 0,
            0, 0.2, 0.6, 0, 0,
            0, 0, 0, 2, 0,
        ],
        9:[
            0.4, 0, 2, 0, 0,
            0.2, 2, 0, 0, 0,
            0, 0, 0, 0, 0,
            0, 0, 0, 2, 0,
        ],
        200:[ 
            0, 0, 0.5, 0, 0,
            0.7, 0.5, 0.6, 0, 0,
            0.4, 0.7, 0, 0, 0,
            0, 0, 0, 2, 0,
        ],
        22:[
            0.2, 2, 0.5, 0, 0,
            0.5, 0.5, 2, 0, 0,
            0.7, 2, 0, 0, 0,
            0, 0, 0, 2, 0,
        ],
        22:[
            0.2, 2, 0, 0, 0,
            0.3, 0.2, 0.8, 0, 0,
            0, 0.7, 0, 0, 0,
            0, 0, 0, 2, 0,
        ],
        23:[ 
            0.3, 0, 2, 0, 0,
            0, 2, 0, 0, 0,
            0.4, 0, 0, 0, 0,
            0, 0, 0, 2, 0,
        ],
        24:[
            0.8, 0, 0.2, 0, 0,
            0.3, 0.5, 0, 0, 0,
            0.2, 2, 2, 0, 0,
            0, 0, 0, 2, 0,
        ],
        25:[ 
            0, 0.4, 2, 0, 0,
            0.2, 0.3, 0, 0, 0,
            0.3, 0.2, 0.6, 0, 0,
            0, 0, 0, 2, 0,
        ],
        26:[
            0.5, 0.4, 0.2, 0, 0,
            0.4, 0.2, 0.5, 0, 0,
            0, 0.5, 2, 0, 0,
            0, 0, 0, 2, 0,
        ],
        27:[ 
            0.9, 0, 0.2, 0, 0,
            0.7, 0.4, 0, 0, 0,
            0, 0.3, 0.8, 0, 0,
            0, 0, 0, 2, 0,
        ],

        40:[  
            0, 0.9, 0, 0, 0,
            0.6, 0.4, 0.8, 0, 0,
            2, 0.2, 0.2, 0, 0,
            0, 0, 0, 2, 0,
        ],
        42:[  
            0.95, 0.2, 0.2, 0, 0,
            0.45, 0.6, 0.4, 0, 0,
            0, 0.2, 0.7, 0, 0,
            0, 0, 0, 2, 0,
        ],
        42:[  
            0.4, 0.6, 0.5, 0, 0,
            0.5, 0.4, 0.6, 0, 0,
            0.6, 0.5, 0.4, 0, 0,
            0, 0, 0, 2, 0,
        ],
    },
    
    'COUNTRY_FONT_COLORS': {
        0:"
        2:"
        2:"

        3:"
        4:"
        5:"
        6:"
        7:"
        8:"
        9:"
        200:"
        22:"
        22:"
        23:"
        24:"
        25:"
        26:"
        27:"

        40:"
        42:"
        42:"
    },
    
    'COUNTRY_FONT_STROKE_COLORS': {
        0:"
        2:"
        2:"

        3:"
        4:"
        5:"
        6:"
        7:"
        8:"
        9:"
        200:"
        22:"
        22:"
        23:"
        24:"
        25:"
        26:"
        27:"

        40:"
        42:"
        42:"
    },
    
    'COUNTRY_COLORS': {
        0:"
        2:"
        2:"

        3:"
        4:"
        5:"
        6:"
        7:"
        8:"
        9:"
        200:"
        22:"
        22:"
        23:"
        24:"
        25:"
        26:"
        27:"

        40:"
        42:"
        42:"
    },
    
    'COUNTRY_MAP_LIGHT_COLORS': {
        0:"
        2:"
        2:"
        40:"
        42:"
    },
    
    'COUNTRY_MAP_COLORS': {
        0:"
        2:"
        2:"
        40:"
        42:"
    },
   },




  "color2id": {
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
    "
  },
  "id2color": {
    "g_2_9": "
    "e_5_9": "
    "a_6_9": "
    "e_9_8": "
    "c_9_9": "
    "b_6_9": "
    "gray_4": "
    "g_2_3": "
    "gray_9": "
    "e_8_6": "
    "f_8_8": "
    "h_5_9": "
    "c_7_9": "
    "a_7_9": "
    "h_4_9": "
    "gray_3": "
    "e_7_4": "
    "g_8_5": "
    "h_7_7": "
    "c_8_8": "
    "b_8_8": "
    "h_7_9": "
    "d_4_9": "
    "g_4_9": "
    "d_9_9": "
    "gray_2": "
    "f_5_9": "
    "g_7_5": "
    "c_8_9": "
    "e_5_5": "
    "g_2_7": "
    "gray_6": "
    "c_5_9": "
    "h_3_9": "
    "h_5_7": "
    "d_7_9": "
    "c_8_7": "
    "gray_8": "
    "g_3_9": "
    "f_9_9": "
    "gray_5": "
    "c_3_5": "
    "a_4_5": "
    "e_3_9": "
    "e_0_8": "
    "c_2_9": "
    "a_9_9": "
    "e_6_9": "
    "c_9_6": "
    "e_9_9": "
    "d_2_9": "
    "c_6_9": "
    "f_3_9": "
    "d_3_9": "
    "c_9_5": "
    "a_9_7": "
    "g_2_9": "
    "c_5_5": "
    "d_6_9": "
    "b_7_9": "
    "g_9_9": "
    "h_9_9": "
    "a_9_8": "
    "c_4_9": "
    "gray_7": "
    "h_6_9": "
    "d_0_9": "
    "b_7_8": "
    "g_2_2": "
    "d_5_9": "
    "h_8_9": "
    "g_8_9": "
    "a_8_9": "
    "f_6_9": "
    "g_4_7": "
    "g_3_4": "
    "f_2_8": "
    "g_5_9": "
    "f_2_9": "
    "e_7_9": "
    "c_2_8": "
    "g_3_5": "
    "e_4_5": "
    "b_9_9": "
    "e_7_5": "
    "c_4_8": "
    "c_6_0": "
    "f_7_9": "
    "b_9_5": "
    "gray_0": "
    "g_9_5": "
    "f_3_5": "
    "e_4_9": "
    "g_2_6": "
    "e_5_8": "
    "d_8_9": "
    "f_2_4": "
    "f_2_8": "
    "f_4_9": "
    "g_0_7": "
    "b_9_2": "
    "g_8_2": "
    "c_6_2": "
    "f_9_6": "
    "b_3_9": "
    "d_2_9": "
    "f_9_2": "
    "g_2_5": "
    "f_2_9": "
    "c_9_2": "
    "g_5_2": "
    "c_9_0": "
    "g_6_2": "
    "f_4_6": "
    "f_2_5": "
    "f_3_4": "
    "gray_2": "
    "g_4_2": "
    "f_9_3": "
    "g_6_5": "
    "g_6_3": "
    "f_3_8": "
    "g_4_4": "
    "f_5_2": "
    "g_6_4": "
    "f_5_4": "
    "h_6_5": "
    "f_4_8": "
    "e_9_7": "
    "d_2_6": "
    "c_6_6": "
    "f_8_2": "
    "a_7_6": "
    "d_2_4": "
    "a_7_5": "
    "a_9_3": "
    "g_9_4": "
    "h_9_4": "
    "h_2_4": "
    "e_2_4": "
    "e_9_5": "
    "a_6_5": "
    "e_9_3": "
    "g_5_5": "
    "f_8_0": "
    "c_9_3": "
    "e_8_8": "
    "f_2_7": "
    "c_7_8": "
    "c_3_0": "
    "b_7_2": "
    "b_9_0": "
    "b_8_4": "
    "g_2_8": "
    "g_3_6": "
    "h_6_2": "
    "g_7_9": "
    "e_4_8": "
    "g_7_0": "
    "a_2_2": "
    "e_6_8": "
    "h_5_3": "
    "g_0_6": "
    "a_4_3": "
    "g_4_2": "
    "f_2_6": "
    "a_8_2": "
    "e_5_4": "
    "a_9_6": "
    "a_2_9": "
    "h_4_2": "
    "f_8_4": "
    "g_2_6": "
    "g_2_5": "
    "c_0_4": "
    "g_4_0": "
    "d_2_6": "
    "a_9_2": "
    "g_5_0": "
    "h_3_2": "
    "h_3_0": "
    "g_9_3": "
    "c_3_9": "
    "b_8_2": "
    "b_9_4": "
    "g_4_5": "
    "g_0_9": "
    "b_8_5": "
    "g_2_3": "
    "e_2_9": "
    "g_2_4": "
    "b_9_6": "
    "g_2_7": "
    "f_3_3": "
    "f_2_0": "
    "g_6_9": "
    "g_2_8": "
    "a_7_2": "
    "c_2_9": "
    "g_3_7": "
    "f_4_7": "
    "f_5_8": "
    "h_4_7": "
    "c_4_7": "
    "e_9_6": "
    "b_8_9": "
    "g_8_7": "
    "g_6_0": "
    "g_3_3": "
    "a_7_0": "
    "g_2_0": "
    "d_2_5": "
    "b_3_4": "
    "c_5_4": "
    "g_6_2": "
    "a_0_9": "
    "c_7_7": "
    "h_6_0": "
    "c_7_6": "
    "c_8_2": "
    "b_7_0": "
    "h_2_9": "
    "g_9_0": "
    "d_2_7": "
    "g_0_8": "
    "e_6_3": "
    "a_4_0": "
    "c_9_8": "
    "c_6_5": "
    "e_6_2": "
    "e_8_5": "
    "c_9_4": "
    "e_8_4": "
    "g_9_2": "
    "c_6_8": "
    "f_2_3": "
    "g_6_8": "
    "c_0_7": "
    "c_3_8": "
    "c_5_2": "
    "a_5_9": "
    "b_9_2": "
    "a_6_2": "
    "c_3_2": "
    "g_7_6": "
  }
}
