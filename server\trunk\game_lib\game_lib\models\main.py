#-*- coding: utf-8 -*-
import random, sys
import hashlib, socket, zlib
import datetime, time, urllib2, urllib, json
import cPickle as pickle
from decimal import Decimal

from sqlalchemy import *
import sqlalchemy, re, math
from django.conf import settings
from game_lib.db.database import c_cache as cache
from copy import deepcopy as copy

from game_lib.db import database
from game_lib.models.cache import CacheTable
from game_lib.libs import model
from game_lib.common import utils
from game_lib.common.utils import total_seconds
from game_lib.common.id_validator import id_validator
from game_lib.logics import game_config, reward_config, reward_cache, notice_config

from game_lib.models.analytics import AL
from libs.weixin import WXAPI
from libs.huawei import HuaWei
from libs.huawei_tw import HuaWei as HuaWei_tw
from libs.vivo import Vivo
from libs.oppo import Oppo
from libs.ysdk import Ysdk
import logging

from game_lib.common.tencentcloud import send_sms
# logging.basicConfig(level=logging.INFO,
#                 format='%(message)s',
#                 #datefmt='%a, %d %b %Y %H:%M:%S',
#                 filename=settings.QH_LOG_FILE,
#                 filemode='a+')
from u37 import u_dict_37

fmter = logging.Formatter(fmt='%(message)s')

h5qh_logger = logging.getLogger('h5_360')
h5qh_logger.setLevel(logging.INFO)
h5_fhd = logging.FileHandler(filename=settings.QH_LOG_FILE, mode='a+')
h5_fhd.setFormatter(fmter)
h5qh_logger.addHandler(h5_fhd)

adqh_logger = logging.getLogger('ad_360')
adqh_logger.setLevel(logging.INFO)
ad_fhd = logging.FileHandler(filename=settings.ADQH_LOG_FILE, mode='a+')
ad_fhd.setFormatter(fmter)
adqh_logger.addHandler(ad_fhd)
import ipdb
ip_db = ipdb.City(settings.BASEROOT+"/ipiptest.ipdb")

random.seed(time.time())

one_day = 60*60*24

def select_keys(data, keys):
    res = {}
    for k in keys:
        res[k] = data[k]
    return res

class InstallCode(model.Model):
    is_multi_table = False
    autoincrement_key = False
    table_num = database.user_table_num
    use_cache = False
    cache_perfix = settings.CACHE_PRE
    cache_time = 60*60
    database = database
    table_format = 'InstallCode_table'
    key_name_field = 'phone_ip'

    seq_attrs = ['phone_ip', 'code', 'phone_data', 'add_time']

    def __init__(self, phone_ip=None):
        self.phone_ip = phone_ip
        self.code = None
        self.phone_data = '{}'
        self.add_time = datetime.datetime.now()
        super(InstallCode, self).__init__()

    @classmethod
    def install_request(cls, request, params, phone_id):
        try:
            user_ip = request.META['HTTP_X_FORWARDED_FOR'].split(",")[0] if 'HTTP_X_FORWARDED_FOR' in request.META else request.META.get('REMOTE_ADDR', '')
            if phone_id[1]:
                phone_ip = phone_id[1].lower()
            else:
                phone_ip = user_ip
            if phone_ip == '00000000-0000-0000-0000-000000000000':
                phone_ip = user_ip
            if not phone_ip:
                return False
            AdClick.check_click(phone_ip)


            install_code = cls.get(phone_ip)
            if not install_code:
                install_code = cls(phone_ip=phone_ip)
                install_code.phone_data = json.dumps(params)
            install_code.code = params['code']
            install_code.save()
            return True
        except:
            utils.print_err()
            return False



class Waiter(model.Model):
    is_multi_table = False
    autoincrement_key = False
    table_num = database.user_table_num
    use_cache = settings.USE_CACHE
    cache_perfix = settings.CACHE_PRE
    cache_time = 60*60
    database = database
    table_format = 'Waiter_table'
    key_name_field = 'waiter_id'

    seq_attrs = ['waiter_id', 'waiter_pwd', 'waiter_lv', 'admin_user',
            'zone_list', 'zone_log','status']
    adv_seq_attrs = ['zone_list', 'zone_log']

    def __init__(self, waiter_id=None):
        self.waiter_id = waiter_id
        self.waiter_pwd = None
        self.waiter_lv = 1
        self.admin_user = None
        self.zone_list = []
        self.zone_log = {}
        self.status = 0
        super(Waiter, self).__init__()

    def get_limit_dict(self, zone):
        deviation = game_config.system_simple['deviation']
        deviation_minutes = datetime.timedelta(minutes = deviation)

        now = datetime.datetime.now()
        dev_now = now-deviation_minutes
        open_game_time = game_config.zone[zone][2]
        dev_open_game_time = open_game_time-deviation_minutes
        season_num = (dev_now.date()-dev_open_game_time.date()).days
        open_days = season_num+1
        waiter_config = game_config.waiter
        lv_add = waiter_config['level'][self.waiter_lv]
        limit_dict = {}
        for key in ['pay_all','pay','coin']:
            for item in waiter_config[key]:
                if open_days < item[0]:
                    break
                a,b,c,d = item
                day = open_days-a
            limit_dict[key] = int((b+(c*day)) * (1+d*lv_add))
        return limit_dict

    def check_zone_log(self, zone):
        now = datetime.datetime.now()
        self.zone_log.setdefault(zone,{})
        self.zone_log[zone].setdefault('waiter_time',now)
        self.zone_log[zone].setdefault('pay_all',0)
        self.zone_log[zone].setdefault('pay',0)
        self.zone_log[zone].setdefault('coin',0)
        if self.zone_log[zone]['waiter_time'].date() != now.date():
            self.zone_log[zone]['waiter_time'] = now
            self.zone_log[zone]['pay'] = 0
            self.zone_log[zone]['coin'] = 0




class UserZone(model.Model):
    is_multi_table = False
    autoincrement_key = 'uid'
    table_num = database.user_table_num
    use_cache = False
    cache_perfix = settings.CACHE_PRE
    cache_time = 60*60
    database = database
    table_format = 'UserZone_table'
    key_name_field = 'uid'

    seq_attrs = ['uid', 'pf', 'pf_key', 'pf_pwd', 'zones', 'tel','user_code','www','add_time','freeze_status','login_time','phone_id','max_lv','member','user_ip','pay_money','zone_login','ucoin', 'inviter', 'player', 'player_id']
    adv_seq_attrs = ['zone_login']

    def __init__(self):
        self.uid = None
        self.pf = 'local'
        self.pf_key = None
        self.pf_pwd = None
        self.zones = ''
        self.user_code = ''
        self.tel = ''
        self.www = 0
        self.add_time = datetime.datetime.now()
        self.login_time = datetime.datetime.now()
        self.phone_id = None
        self.max_lv = 1
        self.member = 0
        self.user_ip = None
        self.pay_money = 0
        self.freeze_status = 0
        self.zone_login= {}
        self.ucoin = 100000
        self.inviter = None
        self.player = 0
        self.player_id = None
        super(UserZone, self).__init__()

    @classmethod
    def get_config_value(cls, key, zone, lan=None, use_merge=None):
        pre_configs = {
            'country': [
                'country.capital',
                'expedition.target_city',
                'milepost',
            ],
            'attack_city_npc': [
                'thief_one.safe_city',
                'thief_two.target_city',
            ],
            'country_army': [
                '0.target_city',
                '1.target_city',
                '2.target_city',
                'patriot.0.target_city',
                'patriot.1.target_city',
                'patriot.2.target_city',
                'patriot.arise_time',
                'patriot.end_day',
                'patriot.speed',
            ],
            'mining': ['grab_city'],
            'pk_npc': ['alien_flush_city'],
            'system_simple': [
                'catch_hero_init',
                'init_city',
                'fight_task.city_gauge',
                'drink.city',
                'drink.init_hero',
            ],
            'dungeon_date': [
                'default_map',
            ],
            'ftask': [
                'merge',
            ],
            'ctask': [
                'chapter',
                'task',
            ],
            'task': [
                'country',
            ],
            'bless_hero': [
                'position',
            ],
            'arena': [
                'posArr',
            ],
            'climb': [
                'configure.num_reward',
                'configure.preview',
                'configure.rewardorder2',
            ],
        }
        ##预处理配置，地图相关配置覆盖处理
        def _check_maps_config_value(config_value, merge_maps):
            _config_value = copy(config_value)
            if merge_maps is None or int(merge_maps) == -1:
                return _config_value
            for k, v in pre_configs.items():
                if k != key:
                    continue
                for item in v:
                    l = _config_value
                    _item = item.split('.')
                    target_k = _item.pop(-1)
                    maps_k = '%s_maps' % target_k
                    for _k in _item:
                        l = l[_k]
                    try:
                        if isinstance(l, list):
                            for i, _l in enumerate(l):
                                l[i][target_k] = _l[maps_k][int(merge_maps)]
                        elif isinstance(l, dict):
                            l[target_k] = l[maps_k][int(merge_maps)]
                    except IndexError:
                        if key == 'task' and item == 'country':
                            del _config_value['country']
                    except KeyError:
                        utils.print_err()
            if key == 'office':
                for k,v in config_value['office_lv'].items():
                    if v['condition'].get('merge_times_maps'):
                        _config_value['office_lv'][k]['condition']['merge_times'] = v['condition']['merge_times_maps']
            elif key == 'country':
                if 'new_milepost_maps' in _config_value:
                    _config_value['new_milepost'] = _config_value['new_milepost_maps']
            return _config_value


        if use_merge is not None:
            merge_maps, merge_ploys = use_merge.split("|")
        else:
            merge_maps, merge_ploys = -1, -1
        if zone and zone.find(':') == -1:
            merge_times = game_config.zone[zone][8]
        else:
            merge_times = None
        if key == 'ploy':
            try:
                if int(merge_ploys) == -1:
                    if merge_times:
                        cval = getattr(game_config, '%s_merge_%s' % (key, merge_times))
                    else:
                        cval = getattr(game_config, 'ploy')
                else:
                    cval = getattr(game_config, '%s_%s' % (key, merge_ploys))
            except ValueError:
                cval = getattr(game_config, '%s_%s' % (key, merge_ploys))
        elif key in ['map', 'city']:
            if int(merge_maps) == -1:
                cval = getattr(game_config, key)
            else:
                cval = getattr(game_config, '%s_%s' % (key, merge_maps))
        elif key in ['credit', 'guide', 'bless_hero']:
            if int(merge_maps) == -1:
                cval = getattr(game_config, key)
            else:
                cval = getattr(game_config, '%s_%s' % (key, 0))
        elif key == 'climb':
            if int(merge_maps) == -1:
                if merge_times:
                    cval = getattr(game_config, 'climb_new')
                else:
                    cval = getattr(game_config, key)
            else:
                cval = getattr(game_config, 'climb_0')
        elif key in ['return_msg', 'ope_msg'] and lan and lan != 'cn':
            cval = getattr(game_config, '%s_%s' % (key, lan))
        elif key == 'notice':
            cval = getattr(notice_config, key)
        else:
            cval = getattr(game_config, key)
        cval = _check_maps_config_value(cval, merge_maps)
        # 调试输出festival配置
        # if key == 'festival':
        #     import os
        #     log_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "app.log")
        #     with open(log_file, "a") as f:
        #         f.write("=== DEBUG: get_config_value festival ===\n")
        #         f.write("key: " + str(key) + "\n")
        #         f.write("cval: " + str(cval) + "\n")
        #         f.write("cval type: " + str(type(cval)) + "\n")
        #         f.write("cval keys: " + str(cval.keys() if hasattr(cval, 'keys') else "No keys") + "\n")
        #         f.write("=====================================\n")        
        return cval

    @classmethod
    def push_server_config(cls, zone):
        if zone.find(':') == -1:
            use_merge = cls.call_server_api(zone, 'get_use_merge', {})
        else:
            use_merge = None
        config_data = {'version':  game_config.version}
        for item in reduce(lambda x, y: x + y, game_config.all_config_list, []):
            key = item[0]
            if key in ['pf_system_simple', 'client_config']:
                continue
            config_data[key] = cls.get_config_value(key, zone, use_merge=use_merge)
        # config_data = dict([(key, getattr(game_config, key)) for key in [item[0] for item in reduce(lambda x, y: x+y, game_config.all_config_list, [])] if
        #         key not in ['pf_system_simple', 'client_config']
        #         ])
        # config_data['version'] = game_config.version
        if_push = cache.get(settings.CACHE_PRE+'if_push_%s' % game_config.version, 0)
        data = ['sync_configs',[if_push, config_data]]
        if zone.find(':') == -1:
            server_addr = ':'.join(map(str, game_config.zone[zone][1]))
        else:
            server_addr = str(zone)

        if settings.USE_SSL:
            res = urllib2.urlopen('https://'+server_addr+'/api/?pwd=' + settings.PWD, pickle.dumps(data)).read()
        else:
            res = urllib2.urlopen('http://'+server_addr+'/api/?pwd=' + settings.PWD, pickle.dumps(data)).read()
        return True

    @classmethod
    def push_server_reward_config(cls,zone):
        config_data = {'rewards': reward_config.rewards, 'version': reward_config.version}
        data = ['sync_reward_config', config_data]
        
        if settings.USE_SSL:
            res = urllib2.urlopen('https://'+':'.join(map(str, game_config.zone[zone][1]))+'/api/?pwd='+ settings.PWD, pickle.dumps(data)).read()
        else:
            res = urllib2.urlopen('http://'+':'.join(map(str, game_config.zone[zone][1]))+'/api/?pwd='+ settings.PWD, pickle.dumps(data)).read()
        return True


    @classmethod
    def login(cls, request, params, phone_id):
        user_ip = request.META['HTTP_X_FORWARDED_FOR'].split(",")[0] if 'HTTP_X_FORWARDED_FOR' in request.META else request.META.get('REMOTE_ADDR', '')

        utype = params.get('utype', None)
        user_id = None
        ext = {}  # 扩展参数，各渠道特殊需求参数都放这里
        now = datetime.datetime.now()
        if params.get('wx_code'):
            js_code = params['wx_code']
            wx_api = WXAPI()
            data = wx_api.get_weixin_info(js_code)
            openid = data.get('openid',None)
            if not openid:
                return {'status': 'error', 'msg': u'登陆失败'}
            session_key = data['session_key']
            user_zone_list = list(cls.query({'pf_key': openid}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx'
                user_zone.pf_key = openid
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        if params.get('wx_gzsgz'):
            js_code = params['wx_gzsgz']
            wx_api = WXAPI(pf='wx_gzsgz')
            data = wx_api.get_weixin_info(js_code)
            user_id = data.get('openid',None)
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}
            session_key = data['session_key']
            user_zone_list = list(cls.query({'pf_key': user_id}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx'
                user_zone.pf_key = user_id
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_qqdt'):
            from libs.qqgame import QqGame

            api = QqGame()
            openid = params['h5_qqdt']['openid']
            openkey = params['h5_qqdt']['openkey']
            if not api.is_login(params['h5_qqdt']):
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|h5_qqdt' % openid
            session_key = openkey
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_qqdt'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_qqdt2'):
            from libs.qqgame import QqGame

            api = QqGame()
            openid = params['h5_qqdt2']['openid']
            openkey = params['h5_qqdt2']['openkey']
            if not api.is_login(params['h5_qqdt2']):
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|h5_qqdt2' % openid
            session_key = openkey
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_qqdt2'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_qqdt3'):
            from libs.qqgame import QqGame

            api = QqGame(pf='h5_qqdt3')
            openid = params['h5_qqdt3']['openid']
            openkey = params['h5_qqdt3']['openkey']
            if not api.is_login(params['h5_qqdt3']):
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|h5_qqdt3' % openid
            session_key = openkey
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_qqdt3'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_h5_qqdt'):
            from libs.qqgame import QqGame

            api = QqGame(pf='cb_h5_qqdt')
            openid = params['cb_h5_qqdt']['openid']
            openkey = params['cb_h5_qqdt']['openkey']
            if not api.is_login(params['cb_h5_qqdt']):
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|cb_h5_qqdt' % openid
            session_key = openkey
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_h5_qqdt'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
                try:
                    api.report_data(user_zone, user_ip)
                except:
                    utils.print_err()
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key


        elif params.get('ad_xiaoy'):
            from libs.ad_xiaoy import Api

            api = Api()
            openid = params['ad_xiaoy']['userName']
            if not api.check_login(params['ad_xiaoy']):
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|ad_xiaoy' % openid
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'ad_xiaoy'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('yyb') or params.get('yyb_gdt') or params.get('gdt_1') or params.get('gdt_2') or params.get('gdt_3') or params.get('gdt_4') or params.get('gdt_5'):
            api  = Ysdk()
            
            if 'yyb' in params:
                yyb = params['yyb']
                pf = 'yyb'
            elif 'yyb_gdt' in params:
                yyb = params['yyb_gdt']
                pf = 'yyb_gdt'

            for i in range(1, 6):
                if ('gdt_%s' % i) in params:
                    pf  = 'gdt_%s' % i
                    yyb = params[pf]
                    break

            openid = yyb['open_id']
            openkey = yyb['accessToken']
            login_type = yyb['login_type']
            if login_type == 'qq':
                api_data = api.check_qq(openid,openkey)
            else:
                api_data = api.check_wx(openid,openkey)
            if not api_data:
                return {'status': 'error', 'msg': u'无效的帐号'}

            pf_key = '%s|%s' % (openid, pf)
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = pf
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key
        elif params.get('test_yyb'):
            api  = Ysdk()
            test_yyb = params['test_yyb']

            openid = test_yyb['open_id']
            openkey = test_yyb['accessToken']
            login_type = test_yyb['login_type']
            if login_type == 'qq':
                api_data = api.check_qq(openid,openkey)
            else:
                api_data = api.check_wx(openid,openkey)
            if not api_data:
                return {'status': 'error', 'msg': u'无效的帐号'}

            pf_key = '%s|test_yyb' % openid
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'test_yyb'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key
        elif params.get('yyb2'):
            api  = Ysdk(pf='yyb2')
            yyb2 = params['yyb2']

            openid = yyb2['open_id']
            openkey = yyb2['accessToken']
            login_type = yyb2['login_type']
            if login_type == 'qq':
                api_data = api.check_qq(openid,openkey)
            else:
                api_data = api.check_wx(openid,openkey)
            if not api_data:
                return {'status': 'error', 'msg': u'无效的帐号'}

            pf_key = '%s|yyb2' % openid
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'yyb2'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key
        elif params.get('hw'):
            api  = HuaWei()
            hw = params['hw']

            openid = hw['playerId']
            playerLevel= hw['playerLevel']
            playerSSign = hw['gameAuthSign']
            ts = hw['ts']
            sdk_v = hw.get('v')
            api_data = api.check_session(openid, playerLevel, playerSSign, ts, sdk_v)
            if not api_data:
                return {'status': 'error', 'msg': u'无效的帐号'}

            pf_key = '%s|hw' % openid
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'hw'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key
        elif params.get('hw_gzsgz'):
            api = HuaWei(pf='hw_gzsgz')
            hw = params['hw_gzsgz']

            openid = hw['playerId']
            playerLevel = hw['playerLevel']
            playerSSign = hw['gameAuthSign']
            ts = hw['ts']
            sdk_v = hw.get('v')
            api_data = api.check_session(openid, playerLevel, playerSSign, ts, sdk_v)
            if not api_data:
                return {'status': 'error', 'msg': u'无效的帐号'}

            pf_key = '%s|hw_gzsgz' % openid
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'hw_gzsgz'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key
        elif params.get('hw_jtzl'):
            api = HuaWei(pf='hw_jtzl')
            hw = params['hw_jtzl']

            openid = hw['playerId']
            playerLevel = hw['playerLevel']
            playerSSign = hw['gameAuthSign']
            ts = hw['ts']
            sdk_v = hw.get('v')
            api_data = api.check_session(openid, playerLevel, playerSSign, ts, sdk_v)
            if not api_data:
                return {'status': 'error', 'msg': u'无效的帐号'}

            pf_key = '%s|hw_jtzl' % openid
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'hw_jtzl'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key
        elif params.get('hw_tw'):
            api = HuaWei_tw()
            hw = params['hw_tw']

            openid = hw['playerId']
            playerLevel= hw['playerLevel']
            playerSSign = hw['gameAuthSign']
            ts = hw['ts']
            api_data = api.check_session(openid,playerLevel,playerSSign,ts)
            if not api_data:
                return {'status': 'error', 'msg': u'无效的帐号'}

            pf_key = '%s|hw_tw' % openid
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'hw_tw'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('muyou_hw_tl'):
            from libs.muyou import Api
            api = Api(pf='muyou_hw_tl')
            user_id = params['muyou_hw_tl']['user_id']
            is_login = api.check_login(params['muyou_hw_tl'])
            if not is_login:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|muyou_ad_tl' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'muyou_hw_tl'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('muyou_hw_vn'):
            from libs.muyou import Api
            api = Api()
            user_id = params['muyou_hw_vn']['user_id']
            is_login = api.check_login(params['muyou_hw_vn'])
            if not is_login:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|muyou_ad_vn' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'muyou_hw_vn'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('vivo'):
            api  = Vivo()
            vivo = params['vivo']
            token = vivo['token']

            openid = api.get_user_info(token)
            if not openid:
                return {'status': 'error', 'msg': u'无效的帐号'}

            pf_key = '%s|vivo' % openid
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'vivo'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key
        elif params.get('oppo'):
            api  = Oppo()
            oppo = params['oppo']

            ssoid = oppo['ssoid']
            token = oppo['token']

            openid = api.get_user_info(ssoid, token)
            if not openid:
                return {'status': 'error', 'msg': u'无效的帐号'}

            pf_key = '%s|oppo' % openid
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'oppo'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key
        elif params.get('juedi'):
            from libs.juedi import JueDi
            api  = JueDi()
            juedi_params = params['juedi']
            token = juedi_params['token']
            user_id = str(juedi_params['user_id'])
            api_data = api.get_userinfo(user_id,token)
            if not api_data:
                return {'status': 'error', 'msg': u'登陆失败'}
            pf_key = '%s|juedi' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'juedi'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key
        elif params.get('juedi_ios'):
            from libs.juedi_ios import JueDiIos
            api  = JueDiIos()
            juedi_ios_params = params['juedi_ios']
            token = juedi_ios_params['token']
            user_id = str(juedi_ios_params['user_id'])
            api_data = api.get_userinfo(user_id,token)
            if not api_data:
                return {'status': 'error', 'msg': u'登陆失败'}
            pf_key = '%s|juedi_ios' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'juedi_ios'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('juedi_ad'):
            from libs.juedi import JueDi
            api  = JueDi()
            juedi_ad_params = params['juedi_ad']
            token = juedi_ad_params['token']
            user_id = str(juedi_ad_params['user_id'])
            api_data = api.get_userinfo(user_id,token)
            if not api_data:
                return {'status': 'error', 'msg': u'登陆失败'}
            pf_key = '%s|juedi_ad' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'juedi_ad'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('muyou_ad_vn'):
            from libs.muyou import Api
            api = Api()
            user_id = params['muyou_ad_vn']['user_id']
            is_login = api.check_login(params['muyou_ad_vn'])
            if not is_login:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|muyou_ad_vn' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'muyou_ad_vn'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_muyou_ad_vn'):
            from libs.muyou import Api
            api = Api(pf='cb_muyou_ad_vn')
            user_id = params['cb_muyou_ad_vn']['user_id']
            is_login = api.check_login(params['cb_muyou_ad_vn'])
            if not is_login:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|cb_muyou_ad_vn' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_muyou_ad_vn'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_muyou_ad_vn1'):
            from libs.muyou import Api
            api = Api(pf='cb_muyou_ad_vn1')
            user_id = params['cb_muyou_ad_vn1']['user_id']
            is_login = api.check_login(params['cb_muyou_ad_vn1'])
            if not is_login:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|cb_muyou_vn' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_muyou_ad_vn1'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_muyou_ad_vn2'):
            from libs.muyou import Api
            api = Api(pf='cb_muyou_ad_vn1')
            user_id = params['cb_muyou_ad_vn2']['user_id']
            is_login = api.check_login(params['cb_muyou_ad_vn2'])
            if not is_login:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|cb_muyou_vn' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_muyou_ad_vn2'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_muyou_ios_vn'):
            from libs.muyou import Api
            api = Api(pf='cb_muyou_ios_vn')
            user_id = params['cb_muyou_ios_vn']['user_id']
            is_login = api.check_login(params['cb_muyou_ios_vn'])
            if not is_login:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|cb_muyou_vn' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_muyou_ios_vn'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('muyou_ad_vn1'):
            """
            沐游官网安卓包
            """
            from libs.muyou import Api
            api = Api()
            user_id = params['muyou_ad_vn1']['user_id']
            is_login = api.check_login(params['muyou_ad_vn1'])
            if not is_login:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|muyou_ad_vn' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'muyou_ad_vn1'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('muyou_ios_vn'):
            from libs.muyou import Api
            api = Api(pf='muyou_ios_vn')
            user_id = params['muyou_ios_vn']['user_id']
            is_login = api.check_login(params['muyou_ios_vn'])
            if not is_login:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|muyou_ios_vn' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'muyou_ios_vn'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('muyou_ios_vn1'):
            from libs.muyou import Api
            api = Api(pf='muyou_ios_vn1')
            user_id = params['muyou_ios_vn1']['user_id']
            is_login = api.check_login(params['muyou_ios_vn1'])
            if not is_login:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|muyou_ios_vn' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'muyou_ios_vn1'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('muyou_h5_vn1'):
            from libs.muyou import Api
            api = Api(pf='muyou_h5_vn1')
            user_id = params['muyou_h5_vn1']['userid']
            is_login = api.check_login_h5(params['muyou_h5_vn1'])
            if not is_login:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|muyou_ios_vn' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'muyou_h5_vn1'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('muyou_ad_tl'):
            from libs.muyou import Api
            api = Api(pf='muyou_ad_tl')
            user_id = params['muyou_ad_tl']['user_id']
            is_login = api.check_login(params['muyou_ad_tl'])
            if not is_login:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|muyou_ad_tl' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'muyou_ad_tl'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('muyou_ios_tl'):
            from libs.muyou import Api
            api = Api(pf='muyou_ios_tl')
            user_id = params['muyou_ios_tl']['user_id']
            is_login = api.check_login(params['muyou_ios_tl'])
            if not is_login:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|muyou_ios_tl' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'muyou_ios_tl'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('muyou_h5_tl'):
            from libs.h5_muyou import Api
            api = Api(pf='muyou_h5_tl')
            user_id = params['muyou_h5_tl']['user_id']
            is_login = api.check_login(params['muyou_h5_tl'])
            if not is_login:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|muyou_h5_tl' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'muyou_h5_tl'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('muyou_h5_vn'):
            from libs.h5_muyou import Api
            api = Api(pf='muyou_h5_vn')
            user_id = params['muyou_h5_vn']['user_id']
            is_login = api.check_login(params['muyou_h5_vn'])
            if not is_login:
                return {'status': 'error', 'msg': u'登陆失败'}

            loginplatform2cp = params['muyou_h5_vn']['loginplatform2cp']
            if loginplatform2cp == 'v1':
                pf_key = '%s|muyou_ios_vn' % user_id
            else:
                pf_key = '%s|muyou_h5_vn' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'muyou_h5_vn'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('yqwb'):
            from libs.yqwb import API
            uid = params['yqwb']['uid']
            code = params['yqwb']['code']
            user_id = API.check_user(uid, code)
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|yqwb' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                return {'status': 'error', 'msg': u'登陆失败'}
                user_zone = cls()
                user_zone.pf = 'yqwb'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('mi'):
            from libs.xiaomi import XiaoMi
            uid = params['mi']['uid']
            session = params['mi']['session']
            api = XiaoMi()
            user_id = api.check_session(uid, session)
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|mi' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'mi'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('mz'):
            from libs.meizu import MeiZu
            uid = params['mz']['uid']
            session = params['mz']['session']
            api = MeiZu()
            user_id = api.check_session(uid, session)
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|mz' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'mz'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('uc'):
            from libs.qyou import Api
            session = params['uc']['sid']
            api = Api()
            user_id = api.check_session(session)
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|uc' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                if phone_id:
                    imei_id = str(phone_id[1]).lower()
                ohter_pf_user_list = list(cls.query({'phone_id': imei_id}))
                for other_pf_user in ohter_pf_user_list:
                    if other_pf_user.pf != 'uc':
                        return {'status': 'error', 'msg': u'Server Error'}
                user_zone = cls()
                user_zone.pf = 'uc'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('google'):
            if params['google']['login_type']=='fb':
                from libs.fb import Api
                api = Api()
                user_id = api.check_session(params['google'])
            else:
                from libs.google import Api
                api = Api()
                user_id = api.check_session(params['google'])

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|google' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'google'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('ios_mengwoai'):
            if params['ios_mengwoai']['login_type'] == 'fb':
                from libs.fb import Api
                api = Api(pf='ios_mengwoai')
                user_id = api.check_session(params['ios_mengwoai'])
            elif params['ios_mengwoai']['login_type'] == 'gg':
                from libs.google import Api
                api = Api(pf='ios_mengwoai')
                user_id = api.check_session(params['ios_mengwoai'])
            elif params['ios_mengwoai']['login_type'] == 'apple':
                from libs.appstore_pay import AppStorePay
                user_id = AppStorePay.verify_token(params['ios_mengwoai']['token'])

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|ios_mengwoai' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'ios_mengwoai'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('ios_tw'):
            if params['ios_tw']['login_type']=='fb':
                from libs.fb import Api
                api = Api()
                user_id = api.check_session(params['ios_tw'])

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|ios_tw' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'ios_tw'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('ios_37'):
            from libs.ios_37 import Api

            api = Api()
            user_id = api.check_login(params['ios_37']['pst'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            my_uid = u_dict_37.get(str(user_id), None)
            if my_uid:
                user_zone = UserZone.get(my_uid)
            else:
                pf_key = '%s|h5_37' % user_id
                session_key = pf_key
                user_zone_list = list(cls.query({'pf_key': pf_key}))
                if not user_zone_list:
                    user_zone = cls()
                    user_zone.pf = 'ios_37'
                    user_zone.pf_key = pf_key
                    user_zone.pf_pwd = session_key
                else:
                    user_zone = user_zone_list[0]
                    user_zone.pf_pwd = session_key



        elif params.get('h5_37'):
            from libs.h5_37 import Api

            api = Api()
            user_id = api.check_session(params['h5_37'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}
            my_uid = u_dict_37.get(str(user_id), None)
            if my_uid:
                user_zone = UserZone.get(my_uid)
            else:
                pf_key = '%s|h5_37' % user_id
                session_key = pf_key
                user_zone_list = list(cls.query({'pf_key': pf_key}))
                if not user_zone_list:
                    user_zone = cls()
                    user_zone.pf = 'h5_37'
                    user_zone.pf_key = pf_key
                    user_zone.pf_pwd = session_key
                else:
                    user_zone = user_zone_list[0]
                    user_zone.pf_pwd = session_key

        elif params.get('wx_37'):
            from libs.h5_37 import Api

            api = Api(pf='wx_37')
            user_id = api.check_session(params['wx_37']['data'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}
            app_id = api.get_uid_appid(user_id)
            if app_id != '37wxxyx':
                return {'status': 'error', 'msg': u'账号不合法'}
            pf_key = '%s|wx_37' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_37'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wx_37_1'):
            from libs.h5_37 import Api

            api = Api(pf='wx_37')
            user_id = api.check_session(params['wx_37_1']['data'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}
            pf_key = '%s|wx_37' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_37_1'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wx_37_2'):
            from libs.h5_37 import Api

            api = Api(pf='wx_37')
            user_id = api.check_session(params['wx_37_2']['data'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}
            pf_key = '%s|wx_37' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_37_2'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wx_37_3'):
            from libs.h5_37 import Api

            api = Api(pf='wx_37')
            user_id = api.check_session(params['wx_37_3']['data'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}
            pf_key = '%s|wx_37_3' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_37_3'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_37_wx1'):
            from libs.h5_37 import Api

            api = Api(pf='h5_37_wx1')
            user_id = api.check_session(params['h5_37_wx1'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}
            pf_key = '%s|wx_37' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_37_wx1'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_37_wx2'):
            from libs.h5_37 import Api

            api = Api(pf='h5_37_wx2')
            user_id = api.check_session(params['h5_37_wx2'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}
            pf_key = '%s|wx_37' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_37_wx2'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key


        elif params.get('h5_muzhi'):
            from libs.h5_muzhi import Api

            api = Api()
            user_id = api.check_login(params['h5_muzhi'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|h5_muzhi' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_muzhi'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_muzhi2'):
            from libs.h5_muzhi import Api

            api = Api(pf='h5_muzhi2')
            user_id = api.check_login(params['h5_muzhi2'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|h5_muzhi2' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_muzhi2'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_leyou'):
            from libs.leyou import LeYou

            api = LeYou()
            user_id = api.check_login(params['h5_leyou']['userid'], params['h5_leyou']['token'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|h5_leyou' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_leyou'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wende_ad'):
            from libs.wende import WenDe

            api = WenDe()
            user_id = api.check_login(params['wende_ad']['uid'], params['wende_ad']['token'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|wende_ad' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wende_ad'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wende_ios'):
            from libs.wende import WenDe

            api = WenDe()
            user_id = api.check_login(params['wende_ios']['uid'], params['wende_ios']['token'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|wende_ios' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wende_ios'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_wende'):
            from libs.wende import WenDe

            api = WenDe()
            user_id = api.check_login_h5(params['h5_wende']['user_id'], params['h5_wende']['access_token'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|h5_wende' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_wende'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_dqd'):
            user_id = params['h5_dqd']['open_id']

            pf_key = '%s|h5_dqd' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_dqd'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_7k'):
            from libs.h5_7k import Api7k

            api = Api7k()
            user_id = api.check_session(params['h5_7k'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|h5_7k' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_7k'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_qq_ios'):
            from libs.h5_qq import Api

            api = Api()
            user_id = api.get_open_id(params['h5_qq_ios'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}
            open_id, session_key = user_id


            pf_key = '%s|h5_qq_ios' % open_id
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_qq_ios'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_qq_ad'):
            from libs.h5_qq import Api

            api = Api()
            user_id = api.get_open_id(params['h5_qq_ad'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}
            open_id, session_key = user_id


            pf_key = '%s|h5_qq_ad' % open_id
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_qq_ad'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('you77'):
            from libs.you77 import Api

            api = Api()
            open_id = api.check_login(params['you77']['token_string'])
            if not open_id:
                return {'status': 'error', 'msg': u'登陆失败'}



            pf_key = '%s|you77' % open_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'you77'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('you77_h5_jp'):
            from libs.h5_you77 import Api

            api = Api()
            if not api.check_login(params['you77_h5_jp']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['you77_h5_jp']['qqesuid']


            pf_key = '%s|you77' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'you77_h5_jp'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_shouqu'):
            from libs.h5_shouqu import Api

            api = Api()
            open_id = params['h5_shouqu']['userId']
            if not api.check_login(params['h5_shouqu']):
                return {'status': 'error', 'msg': u'登陆失败'}



            pf_key = '%s|h5_shouqu' % open_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_shouqu'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key


        elif params.get('yx7477'):
            from libs.yx7477 import Api

            api = Api()
            logged = api.check_login(params['yx7477'])
            if not logged:
                return {'status': 'error', 'msg': u'登陆失败'}
            open_id = params['yx7477']['uid']


            pf_key = '%s|yx7477' % open_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'yx7477'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('yx7477_1'):
            from libs.yx7477 import Api

            api = Api(pf='yx7477_1')
            logged = api.check_login(params['yx7477_1'])
            if not logged:
                return {'status': 'error', 'msg': u'登陆失败'}
            open_id = params['yx7477_1']['uid']


            pf_key = '%s|yx7477_1' % open_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'yx7477_1'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_7477'):
            from libs.h5_7477 import Api

            api = Api()
            logged = api.check_login(params['h5_7477'])
            if not logged:
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['h5_7477']['uid']


            pf_key = '%s|h5_7477' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_7477'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_y5'):
            from libs.h5_y5 import Api

            api = Api()
            logged = api.check_wakool_login(params['h5_y5'])
            if not logged:
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['h5_y5']['id']


            pf_key = '%s|h5_y5' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_y5'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_wakool'):
            from libs.h5_y5 import Api

            api = Api()
            logged = api.check_wakool_login(params['h5_wakool'])
            if not logged:
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['h5_wakool']['id']


            pf_key = '%s|h5_wakool' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_wakool'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_mengyou'):
            from libs.h5_mengyou import Api

            api = Api()
            logged = api.check_login(params['h5_mengyou'])
            if not logged:
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['h5_mengyou']['uid']


            pf_key = '%s|h5_mengyou' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_mengyou'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_mengyou1'):
            from libs.h5_mengyou import Api

            api = Api()
            logged = api.check_login(params['h5_mengyou1'])
            if not logged:
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['h5_mengyou1']['uid']


            pf_key = '%s|h5_mengyou1' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_mengyou1'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('ad_mengyou'):
            from libs.h5_mengyou import Api

            api = Api()
            user_id = api.ad_check_login(params['ad_mengyou']['token'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}


            pf_key = '%s|ad_mengyou' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'ad_mengyou'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('ad_360'):
            from libs.ad_360 import Api

            api = Api()
            user_id = api.get_user(params['ad_360']['token'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}


            pf_key = '%s|ad_360' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'ad_360'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('ad_6kw'):
            from libs.ad_6kw import Api

            api = Api()
            user_id = api.check_login(params['ad_6kw']['token'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}


            pf_key = '%s|ad_6kw' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'ad_6kw'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_6kw'):
            from libs.h5_6kw import Api

            api = Api()
            user_id = api.check_login(params['h5_6kw']['sid'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}


            pf_key = '%s|h5_6kw' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_6kw'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_6kw2'):
            from libs.h5_6kw import Api

            api = Api(pf='h5_6kw2')
            user_id = api.check_login(params['h5_6kw2']['sid'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}


            pf_key = '%s|h5_6kw' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_6kw2'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_6kw3'):
            from libs.h5_6kw import Api

            api = Api(pf='h5_6kw3')
            user_id = api.check_login(params['h5_6kw3']['sid'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}


            pf_key = '%s|h5_6kw' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_6kw3'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_panbao'):
            from libs.h5_panbao_liaobe import Api

            api = Api()
            user_id = api.get_user(params['h5_panbao']['code'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}


            pf_key = '%s|h5_panbao' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_panbao'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('ad_panbao'):
            from libs.panbao import Api
            user_id = params['ad_panbao']['user_id']

            api = Api()
            if not api.check_login(params['ad_panbao']):
                return {'status': 'error', 'msg': u'登陆失败'}


            pf_key = '%s|ad_panbao' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'ad_panbao'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('ios_panbao'):
            from libs.panbao import Api
            user_id = params['ios_panbao']['user_id']

            api = Api(pf='ios_panbao')
            if not api.check_login(params['ios_panbao']):
                return {'status': 'error', 'msg': u'登陆失败'}


            pf_key = '%s|ios_panbao' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'ios_panbao'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_changwan'):
            user_id = params['h5_changwan']['uid']

            from libs.h5_changwan import Api
            api = Api()
            if not api.check_login(params['h5_changwan']):
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|h5_changwan' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_changwan'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_hutao'):
            from libs.hutao import Api

            api = Api()
            user_id = api.check_login(params['h5_hutao']['code'], params['h5_hutao']['verify_url'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            open_id, session_key = user_id


            pf_key = '%s|h5_hutao' % open_id
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_hutao'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_hutao2'):
            from libs.hutao import Api

            api = Api(pf='h5_hutao2')
            user_id = api.check_login(params['h5_hutao2']['code'], params['h5_hutao2']['verify_url'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            open_id, session_key = user_id


            pf_key = '%s|h5_hutao2' % open_id
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_hutao2'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_changxiang'):
            from libs.changxiang import Api

            api = Api()
            user_id = api.check_login(params['h5_changxiang']['token'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|h5_changxiang' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_changxiang'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('changxiang'):
            from libs.changxiang import Api

            api = Api()
            user_id = api.check_login(params['changxiang']['token'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|h5_changxiang' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_changxiang'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('37_ios'):
            from libs.h5_37 import Api

            api = Api('8et2wmCJVAnyxophDB4RWaN31vzLG790')
            user_id = api.ios_login(params['37_ios'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|37_ios' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = '37_ios'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_1377'):
            from libs.h5_1377 import Api

            api = Api()
            user_id = api.check_login(params['h5_1377'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|h5_1377' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_1377'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_5599'):
            from libs.h5_5599 import Api

            api = Api()
            user_id = api.get_user_info(params['h5_5599']['access_token'], user_ip)
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}


            pf_key = '%s|h5_5599' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_5599'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('hf'):
            from libs.hf import Api
            user_id = Api().login(params['hf'])

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|hf' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'hf'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('caohua'):
            from libs.caohua import Api
            user_id = Api().login(params['caohua'])

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|caohua' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'caohua'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_ch'):
            from libs.caohua import ApiH5
            user_id = ApiH5().login(params['h5_ch'])

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|h5_ch' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_ch'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_ch2'):
            from libs.caohua import ApiH5_2
            user_id = ApiH5_2().login(params['h5_ch2'])

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|h5_ch2' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_ch2'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_bg'):
            key = '1C1CPKQUPHAD05FC'
            sign_str = '&'.join(unicode(k)+'='+unicode(params['h5_bg'][k]) for k in sorted(params['h5_bg'].keys()) if k!='sign')+key
            sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
            if sign==params['h5_bg']['sign']:
                user_id = params['h5_bg']['user_id']
            else:
                user_id = None

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|h5_bg' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_bg'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_360'):
            key = 'Ea0FQnj2CvbqYcr0aWkbii8l3OVKcN6h'
            qid = params['h5_360']['qid']
            ts = params['h5_360']['time']
            server_id= params['h5_360']['server_id']
            sign_str = 'qid=%s&time=%s&server_id=%s%s' % (qid,ts,server_id,key)
            sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
            if sign==params['h5_360']['sign']:
                user_id = params['h5_360']['qid']
            else:
                user_id = None

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|h5_360' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_360'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_360_2'):
            secret = 'dbc2f58e1527b7e026c1f8ca8b0c98ab'
            plat_user_id = params['h5_360_2']['plat_user_id']
            channel = params['h5_360_2']['channel']
            sign_str = str(plat_user_id) + secret
            sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
            if sign==params['h5_360_2']['sign']:
                user_id = plat_user_id
            else:
                user_id = None

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|ald_%s' % (user_id, channel)
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'ald_%s' % channel
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_360_3'):
            secret = '93a545f6b58c09b8bc236de2993626bb'
            user_id = params['h5_360_3']['userdata']['uid']
            channel = params['h5_360_3']['common']['sdkindx']
            ts = params['h5_360_3']['userdata']['t']
            sign_str = 'secret_key=%s&t=%s&uid=%s' % (secret, ts, user_id)
            sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
            if sign==params['h5_360_3']['userdata']['sign']:
                user_id = user_id
            else:
                user_id = None

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|sy_%s' % (user_id, channel)
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'sy_%s' % channel
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_twyx'):
            key = '47502c6b543e9be04a77dbf6cc1c86ec'
            sign_str = unicode(params['h5_twyx']['uid']+ params['h5_twyx']['appid']+ params['h5_twyx']['time']+key)
            sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
            if sign==params['h5_twyx']['sign']:
                user_id = params['h5_twyx']['uid']
            else:
                user_id = None

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|h5_twyx' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_twyx'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_twyx2'):
            mpc = params['h5_twyx2'].get('mpc', 0)
            if mpc == 1:
                # pc版
                key = '7b49debfc04e8d4d3c50d9c1fc0646a6'
            else:
                # 移动版
                key = '304a11a33f79c6aa3cbf44e958d3dc82'
            sign_str = unicode(params['h5_twyx2']['uid']+ params['h5_twyx2']['appid']+ params['h5_twyx2']['time']+key)
            sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
            if sign==params['h5_twyx2']['sign']:
                user_id = params['h5_twyx2']['uid']
            else:
                user_id = None

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|h5_twyx2' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_twyx2'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_twyx3'):
            key = '99bc6a030f66e2e6f36d6eedc97abd04'
            sign_str = unicode(params['h5_twyx3']['uid']+ params['h5_twyx3']['appid']+ params['h5_twyx3']['time']+key)
            sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
            if sign==params['h5_twyx3']['sign']:
                user_id = params['h5_twyx3']['uid']
            else:
                user_id = None

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|h5_twyx3' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_twyx3'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_twyx4'):
            """
            appid/game_id : 12
            appkey : fe8cf6fb52c18e7a6f335761e35f1cf1
            login_key : 456f4fe0ae03d72dc00c9c8efd6b43df
            pay_key : 70eac6997722f2689ce85ea5e95d059e
            """
            key = '456f4fe0ae03d72dc00c9c8efd6b43df'
            sign_str = unicode(params['h5_twyx4']['uid']+ params['h5_twyx4']['appid']+ params['h5_twyx4']['time']+key)
            sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
            if sign==params['h5_twyx4']['sign']:
                user_id = params['h5_twyx4']['uid']
            else:
                user_id = None

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|h5_twyx4' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_twyx4'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_twyx5'):
            mpc = params['h5_twyx5'].get('mpc', 0)
            if mpc == 1:
                # pc版
                key = '5c7f25c2841861c1fddbedc024d99fda'
            else:
                # 移动版
                key = '2d4f2797299fdf43abaeff9b98141a69'
            sign_str = unicode(params['h5_twyx5']['uid']+ params['h5_twyx5']['appid']+ params['h5_twyx5']['time']+key)
            sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
            if sign==params['h5_twyx5']['sign']:
                user_id = params['h5_twyx5']['uid']
            else:
                user_id = None

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|h5_twyx5' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_twyx5'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('r2g_xm'):
            uid = params['r2g_xm']['uid']
            timeg = params['r2g_xm']['time']
            sign = params['r2g_xm']['sign']
            key = '8cb95719ee0dae53'

            if hashlib.md5(hashlib.md5('%s%s%s' % (uid, timeg, key)).hexdigest()+key).hexdigest()==sign:
                user_id = uid
            else:
                user_id = None

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|r2g_xm' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'r2g_xm'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('r2g_kr'):
            key = 'dd0c8aeb0e3024b6'
            user_id = None
            if params['r2g_kr'].get('r2g_kr_h5',None):
                username = str(params['r2g_kr']['username'])
                timeg = str(params['r2g_kr']['time'])
                game = str(params['r2g_kr']['game'])
                serverid = str(params['r2g_kr']['serverid'])
                site = str(params['r2g_kr']['site'])
                site_sign = hashlib.md5(key+site).hexdigest()
                sign = hashlib.md5(username+timeg+game+serverid+site_sign).hexdigest()
                if sign == params['r2g_kr']['sign']:
                    user_id = params['r2g_kr']['username']
            else:
                uid = params['r2g_kr']['uid']
                timeg = params['r2g_kr']['time']
                sign = params['r2g_kr']['sign']

                if hashlib.md5(hashlib.md5('%s%s%s' % (uid, timeg, key)).hexdigest()+key).hexdigest()==sign:
                    user_id = uid

            if not user_id:
                return {'status': 'error', 'msg': u'Login Error'}

            pf_key = '%s|r2g_kr' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'r2g_kr'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key


        elif params.get('h5_yyjh'):
            from libs.yiyoujiahe import YiyouJH
            user_id = YiyouJH().check_login_sign(params['h5_yyjh'])

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|h5_yyjh' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_yyjh'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_yyjh2'):
            from libs.yiyoujiahe import YiyouJH
            user_id = YiyouJH(tap=1).check_login_sign(params['h5_yyjh2'])

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|h5_yyjh2' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_yyjh2'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_yyjh_hk'):
            from libs.yiyoujiahe import YiyouJH
            user_id = YiyouJH(tap=3).check_login_sign(params['h5_yyjh_hk'])

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|h5_yyjh_hk' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_yyjh_hk'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key


        elif params.get('h5_9130'):
            from libs.h5_9130 import Api
            user_id = Api().login(params['h5_9130'])

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|h5_9130' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_9130'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_kuku'):
            key = u'3179f2b7-1265-453b-b5bb-6b620f44a3ee'
            sign_str = ''.join(unicode(params['h5_kuku'][k]) for k in sorted(params['h5_kuku'].keys()) if k!='sign')+key
            sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
            if sign==params['h5_kuku']['sign']:
                user_id = params['h5_kuku']['uid']
            else:
                user_id = None

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|h5_kuku' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_kuku'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key


        elif params.get('xh_h5'):
            from libs.rastar import Rastar
            api  = Rastar()
            xh_h5 = params['xh_h5']
            access_token = xh_h5['access_token']
            cch_id = xh_h5['cch_id']
            rand = xh_h5['rand']
            api_data = api.get_open_id(access_token,cch_id,rand)
            if not api_data:
                return {'status': 'error', 'msg': u'无效的帐号'}

            openid = api_data.get('openid',None)
            if not openid:
                return {'status': 'error', 'msg': u'登陆失败'}
            pf_key = '%s|xh_h5' % openid
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'xh_h5'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_qianqi'):
            from libs.qianqi import Api
            api = Api(pf='h5_qianqi')
            resp_data = api.verify_login(params['h5_qianqi'])
            if resp_data['error_code'] != 0:
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['h5_qianqi']['uid']
            ext['birthday'] = resp_data['birthday']

            pf_key = '%s|h5_qianqi' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_qianqi'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_qianguo'):
            from libs.qianqi import Api
            api = Api(pf='h5_qianguo')
            resp_data = api.verify_login(params['h5_qianguo'])
            if resp_data['error_code'] != 0:
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['h5_qianguo']['uid']
            ext['birthday'] = resp_data['birthday']

            pf_key = '%s|h5_qianguo' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_qianguo'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('jj_h5_37'):
            from libs.h5_37 import Api

            api = Api('tEH(eun)23^w2H~6P@5LU^_,5C7j@t')
            user_id = api.check_session(params['jj_h5_37'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|jj_h5_37' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'jj_h5_37'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('jj_h5_yyjh'):
            from libs.yiyoujiahe import YiyouJH
            user_id = YiyouJH(tap=2).check_login_sign(params['jj_h5_yyjh'])

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|jj_h5_yyjh' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'jj_h5_yyjh'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('jj_h5_twyx'):
            key = 'ae09b6dd6fac57ab4f252af4cdde5833'
            sign_str = unicode(params['jj_h5_twyx']['uid']+ params['jj_h5_twyx']['appid']+ params['jj_h5_twyx']['time']+key)
            sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
            if sign==params['jj_h5_twyx']['sign']:
                user_id = params['jj_h5_twyx']['uid']
            else:
                user_id = None

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|jj_h5_twyx' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'jj_h5_twyx'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('jj_h5_7k'):
            from libs.h5_7k import Api7k

            api = Api7k(pf='jj_h5_7k')
            user_id = api.check_session(params['jj_h5_7k'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|jj_h5_7k' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'jj_h5_7k'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('jj_ad_caohua'):
            from libs.caohua import Api
            user_id = Api(pf='jj_ad_caohua').login(params['jj_ad_caohua'])

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|jj_ad_caohua' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'jj_ad_caohua'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('jj_h5_leyou'):
            from libs.leyou import LeYou

            api = LeYou(pf='jj_h5_leyou')
            user_id = api.check_login(params['jj_h5_leyou']['userid'], params['jj_h5_leyou']['token'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|jj_h5_leyou' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'jj_h5_leyou'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('jj_mina_ios_ar'):
            from libs.mina import Api

            api = Api(pf='jj_mina_ios_ar')
            user_id = api.check_login(params['jj_mina_ios_ar'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|jj_mina_ios_ar' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'jj_mina_ios_ar'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('jj_mina_ad_ar'):
            from libs.mina import Api

            api = Api(pf='jj_mina_ad_ar')
            user_id = api.check_login(params['jj_mina_ad_ar'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|jj_mina_ad_ar' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'jj_mina_ad_ar'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('jj_h5_ch'):
            from libs.caohua import ApiH5_2
            user_id = ApiH5_2().login(params['jj_h5_ch'])

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|jj_h5_ch' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'jj_h5_ch'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('jj_h5_mg'):
            channel = params['jj_h5_mg']['pf']
            from libs.h5_mg import Api
            api = Api(pf=channel)
            if not api.verify_login(params['jj_h5_mg']):
                return {'status': 'error', 'msg': u'登陆失败'}

            user_id = params['jj_h5_mg']['id']
            pf_key = '%s|jj_h5_mg' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'jj_h5_mg'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_chaoyi'):
            from libs.h5_chaoyi import Api

            api = Api()
            is_login = api.check_login(params['h5_chaoyi'])
            if not is_login:
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['h5_chaoyi']['user_id']

            pf_key = '%s|h5_chaoyi' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_chaoyi'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('ea37'):
            from libs.ea37 import Api

            api = Api(pf='ea37')
            is_login = api.check_login(params['ea37'])
            if not is_login:
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['ea37']['uid']
            channel = params['ea37']['pf']

            pf_key = '%s|ea37' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = channel
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('tw37'):
            from libs.ea37 import Api

            api = Api(pf='tw37')
            is_login = api.check_login(params['tw37'])
            if not is_login:
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['tw37']['uid']
            channel = params['tw37']['pf']

            pf_key = '%s|tw37' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = channel
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('indofun'):
            from libs.indofun import Api

            api = Api()
            is_login = api.check_login(params['indofun'])
            if not is_login:
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['indofun']['userId']
            pf = params['indofun']['pf']

            pf_key = '%s|indofun' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = pf
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_wx_twyx1'):
            from libs.twyx import Api

            api = Api(pf='cb_wx_twyx1')
            user_id = api.check_wx_login(params['cb_wx_twyx1'])

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|cb_h5_twyx' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_wx_twyx1'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_wx_twyx2'):
            from libs.twyx import Api

            api = Api(pf='cb_wx_twyx2')
            user_id = api.check_wx_login(params['cb_wx_twyx2'])

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|cb_h5_twyx' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_wx_twyx2'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_h5_twyx1'):
            """
            appid/game_id : 2903
            appkey : a39635c1accfb76bb1c34efbe364cd7e
            login_key : 3f94d9f99247000d2d973c9b5e5516cb
            pay_key : b9cc429c36f6e628610d2fd2e8cdb919
            """
            key = '3f94d9f99247000d2d973c9b5e5516cb'
            sign_str = unicode(params['cb_h5_twyx1']['uid']+ params['cb_h5_twyx1']['appid']+ params['cb_h5_twyx1']['time']+key)
            sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
            if sign==params['cb_h5_twyx1']['sign']:
                user_id = params['cb_h5_twyx1']['uid']
            else:
                user_id = None

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|cb_h5_twyx' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_h5_twyx1'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_h5_twyx2'):
            """
            appid/game_id : 2901
            appkey : 7efd4d2198f10b0d49de0d04cc2c35b2
            login_key : 1fd440ec239505c1f12b1b95ec4302fd
            pay_key : 4fac50e10f2058c90e0cd364855faca0
            """
            key = '1fd440ec239505c1f12b1b95ec4302fd'
            sign_str = unicode(params['cb_h5_twyx2']['uid']+ params['cb_h5_twyx2']['appid']+ params['cb_h5_twyx2']['time']+key)
            sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
            if sign==params['cb_h5_twyx2']['sign']:
                user_id = params['cb_h5_twyx2']['uid']
            else:
                user_id = None

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|cb_h5_twyx' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_h5_twyx2'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key


        elif params.get('cb_yuanqu_gg'):
            if params['cb_yuanqu_gg']['login_type']=='fb':
                from libs.fb import Api
                api = Api(pf='cb_yuanqu_gg')
                user_id = api.check_session(params['cb_yuanqu_gg'])
            else:
                from libs.google import Api
                api = Api(pf='cb_yuanqu_gg')
                user_id = api.check_session(params['cb_yuanqu_gg'])

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|cb_yuanqu_gg' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_yuanqu_gg'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key



        elif params.get('cb_yuanqu_ios'):
            if params['cb_yuanqu_ios']['login_type'] == 'fb':
                from libs.fb import Api
                api = Api(pf='cb_yuanqu_gg')
                user_id = api.check_session(params['cb_yuanqu_ios'])
            elif params['cb_yuanqu_ios']['login_type'] == 'apple':
                from libs.appstore_pay import AppStorePay
                user_id = AppStorePay.verify_token(params['cb_yuanqu_ios']['token'])

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|cb_yuanqu_gg' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_yuanqu_ios'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_r2g_kr'):
            from libs.r2game import Api
            channel = params['cb_r2g_kr']['pf']
            api = Api(pf='cb_r2g_kr')
            #if channel == 'cb_h5_r2':
            #    user_id = api.h5_check_login(params['cb_r2g_kr'])
            #else:
            #    user_id = api.check_login(params['cb_r2g_kr'])
            user_id = api.check_login(params['cb_r2g_kr'])

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|cb_r2g_kr' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = channel
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key
        elif params.get('cb_r2g_hk'):
            from libs.r2game import Api
            channel = params['cb_r2g_hk']['pf']
            api = Api(pf='cb_r2g_hk')
            user_id = api.check_login(params['cb_r2g_hk'])

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|cb_r2g_hk' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = channel
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_fy_hk'):
            from libs.feiyou import Api
            channel = params['cb_fy_hk']['pf']
            api = Api()
            if not api.check_login(params['cb_fy_hk']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['cb_fy_hk']['user_id']

            pf_key = '%s|cb_fy_hk' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = channel
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif utype == 'uid':
            if not game_config.system_simple.get('test_login',None):
                return {'status': 'error', 'msg': u'登陆失败'}
            uid = params['uid']
            user_zone = cls.get(uid)
            if not user_zone:
                user_zone = cls()
                if uid:
                    if int(uid) > 100000 or int(uid) < 0:
                        return {'status': 'error', 'msg': u'登陆失败'}
                    user_zone.uid = uid
                user_zone.pf = 'local'
                user_zone.pf_key = hashlib.md5('%s%s' % (str(user_zone.uid),str(time.time()))).hexdigest()
        elif utype == 'username':
            username = params['username']
            pwd = params['pwd']
            user_zone_list = list(cls.query({'pf_key': username}))
            login_succ = False
            if user_zone_list:
                user_zone = user_zone_list[0]
                if user_zone.pf_pwd == pwd or user_zone.pf_pwd == hashlib.md5(pwd).hexdigest():
                    login_succ = True
            if not login_succ:
                if pwd and pwd == cache.get(settings.CACHE_PRE+'interim_pwd'+username, ''):
                    user_zone = UserZone.get(int(username))
                    login_succ = True
                else:
                    return {'status': 'error', 'msg': u'用户名或密码错误'}

            if login_succ and 'pre_register_reward' in game_config.system_simple and user_zone.pf in game_config.system_simple['pre_register_reward'][2]:
                state_cache_key = '%spre_register_state_%s' % (settings.CACHE_PRE, user_zone.uid)
                if CacheTable.get(state_cache_key) is None:
                    if user_zone.pf in ['cb_google', 'cb_google1']:
                        state = params.get('fromPreRegister') == '1'
                    else:
                        state = False
                    CacheTable.set(state_cache_key, state, 60*60*24*365*100)
        elif utype == 'tel_sign':
            tel = params['tel']
            sign = params['sign']
            cache_sign = cache.get(settings.CACHE_PRE+str(tel)+'_loginsign')
            if not cache_sign or cache_sign != sign:
                return {'status': 'error', 'msg': u'验证码错误'}
            user_zone_list = list(cls.query({'tel': tel}))
            user_zone = user_zone_list[0]
            if not user_zone.pf_pwd:
                user_zone.pf_pwd = user_zone.pf_key

        elif params.get('jj_junhai'):
            from libs.junhai import Api
            channel = params['jj_junhai']['pf']

            api = Api(pf=channel)
            status, data = api.get_user_info(params['jj_junhai']['session_id'])
            if not status:
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = data['data']['user_id']
            ext = data

            pf_key = '%s|jj_junhai' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = channel
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_h5_37'):
            ## 广电审核用
            from libs.h5_37 import Api

            api = Api('4;8,m6DWm@8;bu798_xbSG^nUz6(q9tw')
            user_id = api.check_session(params['cb_h5_37'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}
            pf_key = '%s|cb_h5_37' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_h5_37'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_ad_37_cn'):
            from libs.h5_37 import Api
            api = Api(pf='cb_37_ad')
            res = api.check_login(params['cb_ad_37_cn'])
            if not res:
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = res['uid']
            ext['is_phone_bind'] = res['is_phone_bind']  #是否绑定手机, 1已绑定，0未绑定
            pf_key = '%s|cb_37_cn' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_ad_37_cn'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_h5_37_cn'):
            from libs.h5_37 import Api
            api = Api(pf='cb_37_h5')
            user_id = api.check_session(params['cb_h5_37_cn'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}
            pf_key = '%s|cb_37_cn' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_h5_37_cn'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_ios_37_cn'):
            from libs.h5_37 import Api

            api = Api(pf='cb_37_ad')
            res = api.check_login(params['cb_ios_37_cn'])
            if not res:
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = res['uid']
            pf_key = '%s|cb_37_cn' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_ios_37_cn'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_wx_37_cn'):
            from libs.h5_37 import Api

            api = Api(pf='cb_37_wx')
            user_id = api.check_session(params['cb_wx_37_cn']['data'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}
            pf_key = '%s|cb_37_cn' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_wx_37_cn'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_37_kr'):
            from libs.ea37 import Api

            api = Api(pf='cb_37_kr')
            is_login = api.check_login(params['cb_37_kr'])
            if not is_login:
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['cb_37_kr']['uid']
            channel = params['cb_37_kr']['pf']

            pf_key = '%s|cb_37_kr' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = channel
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_ad_just4fun'):
            from libs.just4fun import Api

            api = Api()
            user_id = api.check_login(params['cb_ad_just4fun'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|cb_ad_just4fun' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_ad_just4fun'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_google'):
            if params['cb_google']['login_type']=='fb':
                from libs.fb import Api
                api = Api(pf='cb_google')
                user_id = api.check_session(params['cb_google'])
            else:
                from libs.google import Api
                api = Api()
                user_id = api.check_session(params['cb_google'])

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|cb_meng52' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_google'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_google1'):
            if params['cb_google1']['login_type']=='fb':
                from libs.fb import Api
                api = Api(pf='cb_google1')
                user_id = api.check_session(params['cb_google1'])
            else:
                from libs.google import Api
                api = Api()
                user_id = api.check_session(params['cb_google1'])

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|cb_meng52' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_google1'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_ad_mycard'):
            if params['cb_ad_mycard']['login_type']=='fb':
                from libs.fb import Api
                api = Api(pf='cb_ad_mycard')
                user_id = api.check_session(params['cb_ad_mycard'])
            else:
                from libs.google import Api
                api = Api()
                user_id = api.check_session(params['cb_ad_mycard'])

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|cb_meng52' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_ad_mycard'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_ios'):
            if params['cb_ios']['login_type'] == 'fb':
                from libs.fb import Api
                api = Api(pf='cb_google')
                user_id = api.check_session(params['cb_ios'])
            elif params['cb_ios']['login_type'] == 'gg':
                from libs.google import Api
                api = Api(pf='cb_ios')
                user_id = api.check_session(params['cb_ios'])
            elif params['cb_ios']['login_type'] == 'apple':
                from libs.appstore_pay import AppStorePay
                user_id = AppStorePay.verify_token(params['cb_ios']['token'])

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|cb_meng52' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_ios'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_h5_yyjh'):
            from libs.yiyoujiahe import YiyouJH
            user_id = YiyouJH(tap=4).check_login_sign(params['cb_h5_yyjh'])

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|cb_h5_yyjh' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_h5_yyjh'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_h5_yyjh_cn'):
            from libs.yiyoujiahe import YiyouJH
            user_id = YiyouJH(tap=5).check_login_sign(params['cb_h5_yyjh_cn'])

            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|cb_h5_yyjh_cn' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_h5_yyjh_cn'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_h5_mojie'):
            from libs.h5_mojie import Api
            api = Api()
            if not api.check_login(params['cb_h5_mojie']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['cb_h5_mojie']['Uid']

            pf_key = '%s|cb_h5_mojie' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_h5_mojie'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wx_bugu'):
            from libs.bugu import Api
            api = Api()
            if not api.check_login(params['wx_bugu']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['wx_bugu']['data']['mg_mem_id']

            pf_key = '%s|wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_bugu'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wx_bugu_1'):
            from libs.bugu import Api
            api = Api(pf='wx_bugu_1')
            if not api.check_login(params['wx_bugu_1']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['wx_bugu_1']['data']['mg_mem_id']

            pf_key = '%s|wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_bugu_1'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wx_bugu_2'):
            from libs.bugu import Api
            api = Api(pf='wx_bugu_2')
            if not api.check_login(params['wx_bugu_2']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['wx_bugu_2']['data']['mg_mem_id']

            pf_key = '%s|wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_bugu_2'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wx_bugu_3'):
            from libs.bugu import Api
            api = Api(pf='wx_bugu_3')
            if not api.check_login(params['wx_bugu_3']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['wx_bugu_3']['data']['mg_mem_id']

            pf_key = '%s|wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_bugu_3'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wx_bugu_4'):
            from libs.bugu import Api
            api = Api(pf='wx_bugu_4')
            if not api.check_login(params['wx_bugu_4']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['wx_bugu_4']['data']['mg_mem_id']

            pf_key = '%s|wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_bugu_4'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wx_bugu_5'):
            from libs.bugu import Api
            api = Api(pf='wx_bugu_5')
            if not api.check_login(params['wx_bugu_5']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['wx_bugu_5']['data']['mg_mem_id']

            pf_key = '%s|wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_bugu_5'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wx_bugu_6'):
            from libs.bugu import Api
            api = Api(pf='wx_bugu_6')
            if not api.check_login(params['wx_bugu_6']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['wx_bugu_6']['data']['mg_mem_id']

            pf_key = '%s|wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_bugu_6'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wx_bugu_7'):
            from libs.bugu import Api
            api = Api(pf='wx_bugu_7')
            if not api.check_login(params['wx_bugu_7']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['wx_bugu_7']['data']['mg_mem_id']

            pf_key = '%s|wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_bugu_7'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wx_bugu_8'):
            from libs.bugu import Api
            api = Api(pf='wx_bugu_8')
            if not api.check_login(params['wx_bugu_8']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['wx_bugu_8']['data']['mg_mem_id']

            pf_key = '%s|wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_bugu_8'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wx_bugu_9'):
            from libs.bugu import Api
            api = Api(pf='wx_bugu_9')
            if not api.check_login(params['wx_bugu_9']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['wx_bugu_9']['data']['mg_mem_id']

            pf_key = '%s|wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_bugu_9'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wx_bugu_10'):
            from libs.bugu import Api
            api = Api(pf='wx_bugu_10')
            if not api.check_login(params['wx_bugu_10']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['wx_bugu_10']['data']['mg_mem_id']

            pf_key = '%s|wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_bugu_10'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wx_bugu_11'):
            from libs.bugu import Api
            api = Api(pf='wx_bugu_11')
            if not api.check_login(params['wx_bugu_11']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['wx_bugu_11']['data']['mg_mem_id']

            pf_key = '%s|wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_bugu_11'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wx_bugu_12'):
            from libs.bugu import Api
            api = Api(pf='wx_bugu_12')
            if not api.check_login(params['wx_bugu_12']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['wx_bugu_12']['data']['mg_mem_id']

            pf_key = '%s|wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_bugu_12'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wx_bugu_13'):
            from libs.bugu import Api
            api = Api(pf='wx_bugu_13')
            if not api.check_login(params['wx_bugu_13']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['wx_bugu_13']['data']['mg_mem_id']

            pf_key = '%s|wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_bugu_13'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wx_bugu_14'):
            from libs.bugu import Api
            api = Api(pf='wx_bugu_14')
            if not api.check_login(params['wx_bugu_14']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['wx_bugu_14']['data']['mg_mem_id']

            pf_key = '%s|wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_bugu_14'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wx_bugu_15'):
            from libs.bugu import Api
            api = Api(pf='wx_bugu_15')
            if not api.check_login(params['wx_bugu_15']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['wx_bugu_15']['data']['mg_mem_id']

            pf_key = '%s|wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_bugu_15'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wx_bugu_16'):
            from libs.bugu import Api
            api = Api(pf='wx_bugu_16')
            if not api.check_login(params['wx_bugu_16']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['wx_bugu_16']['data']['mg_mem_id']

            pf_key = '%s|wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_bugu_16'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wx_bugu_17'):
            from libs.bugu import Api
            api = Api(pf='wx_bugu_17')
            if not api.check_login(params['wx_bugu_17']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['wx_bugu_17']['data']['mg_mem_id']

            pf_key = '%s|wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_bugu_17'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key


        elif params.get('wx_bugu_18'):
            from libs.bugu import Api
            api = Api(pf='wx_bugu_18')
            if not api.check_login(params['wx_bugu_18']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['wx_bugu_18']['data']['mg_mem_id']

            pf_key = '%s|wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_bugu_18'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wx_bugu_19'):
            from libs.bugu import Api
            api = Api(pf='wx_bugu_19')
            if not api.check_login(params['wx_bugu_19']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['wx_bugu_19']['data']['mg_mem_id']

            pf_key = '%s|wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_bugu_19'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wx_bugu_20'):
            from libs.bugu import Api
            api = Api(pf='wx_bugu_20')
            if not api.check_login(params['wx_bugu_20']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['wx_bugu_20']['data']['mg_mem_id']

            pf_key = '%s|wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_bugu_20'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wx_bugu_21'):
            from libs.bugu import Api
            api = Api(pf='wx_bugu_21')
            if not api.check_login(params['wx_bugu_21']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['wx_bugu_21']['data']['mg_mem_id']

            pf_key = '%s|wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_bugu_21'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wx_bugu_22'):
            from libs.bugu import Api
            api = Api(pf='wx_bugu_22')
            if not api.check_login(params['wx_bugu_22']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['wx_bugu_22']['data']['mg_mem_id']

            pf_key = '%s|wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_bugu_22'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wx_bugu_23'):
            from libs.bugu import Api
            api = Api(pf='wx_bugu_23')
            if not api.check_login(params['wx_bugu_23']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['wx_bugu_23']['data']['mg_mem_id']

            pf_key = '%s|wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_bugu_23'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wx_bugu_24'):
            from libs.bugu import Api
            api = Api(pf='wx_bugu_24')
            if not api.check_login(params['wx_bugu_24']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['wx_bugu_24']['data']['mg_mem_id']

            pf_key = '%s|wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_bugu_24'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wx_bugu_25'):
            from libs.bugu import Api
            api = Api(pf='wx_bugu_25')
            if not api.check_login(params['wx_bugu_25']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['wx_bugu_25']['data']['mg_mem_id']

            pf_key = '%s|wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_bugu_25'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wx_bugu_26'):
            from libs.bugu import Api
            api = Api(pf='wx_bugu_26')
            if not api.check_login(params['wx_bugu_26']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['wx_bugu_26']['data']['mg_mem_id']

            pf_key = '%s|wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_bugu_26'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wx_bugu_27'):
            from libs.bugu import Api
            api = Api(pf='wx_bugu_27')
            if not api.check_login(params['wx_bugu_27']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['wx_bugu_27']['data']['mg_mem_id']

            pf_key = '%s|wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_bugu_27'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wx_bugu_28'):
            from libs.bugu import Api
            api = Api(pf='wx_bugu_28')
            if not api.check_login(params['wx_bugu_28']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['wx_bugu_28']['data']['mg_mem_id']

            pf_key = '%s|wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_bugu_28'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('wx_bugu_29'):
            from libs.bugu import Api
            api = Api(pf='wx_bugu_29')
            if not api.check_login(params['wx_bugu_29']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['wx_bugu_29']['data']['mg_mem_id']

            pf_key = '%s|wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'wx_bugu_29'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_wx_bugu_1'):
            from libs.bugu import Api
            api = Api(pf='cb_wx_bugu_1')
            if not api.check_login(params['cb_wx_bugu_1']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['cb_wx_bugu_1']['data']['mg_mem_id']

            pf_key = '%s|cb_wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_wx_bugu_1'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_wx_bugu_2'):
            from libs.bugu import Api
            api = Api(pf='cb_wx_bugu_2')
            if not api.check_login(params['cb_wx_bugu_2']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['cb_wx_bugu_2']['data']['mg_mem_id']

            pf_key = '%s|cb_wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_wx_bugu_2'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_wx_bugu_3'):
            from libs.bugu import Api
            api = Api(pf='cb_wx_bugu_3')
            if not api.check_login(params['cb_wx_bugu_3']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['cb_wx_bugu_3']['data']['mg_mem_id']

            pf_key = '%s|cb_wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_wx_bugu_3'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_wx_bugu_4'):
            from libs.bugu import Api
            api = Api(pf='cb_wx_bugu_4')
            if not api.check_login(params['cb_wx_bugu_4']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['cb_wx_bugu_4']['data']['mg_mem_id']

            pf_key = '%s|cb_wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_wx_bugu_4'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_wx_bugu_5'):
            from libs.bugu import Api
            api = Api(pf='cb_wx_bugu_5')
            if not api.check_login(params['cb_wx_bugu_5']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['cb_wx_bugu_5']['data']['mg_mem_id']

            pf_key = '%s|cb_wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_wx_bugu_5'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('h5_bugu'):
            from libs.bugu import Api
            api = Api(pf='h5_bugu')
            if not api.check_login_h5(params['h5_bugu']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['h5_bugu']['mem_id']

            pf_key = '%s|wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'h5_bugu'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('sg4_h5_bugu'):
            from libs.bugu import Api
            api = Api(pf='sg4_h5_bugu')
            if not api.check_login_h5(params['sg4_h5_bugu']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['sg4_h5_bugu']['mem_id']

            pf_key = '%s|sg4_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'sg4_h5_bugu'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('sg4_h5_bugu1'):
            from libs.bugu_h5 import Api
            api = Api(pf='sg4_h5_bugu1')
            user_id = api.check_login(params['sg4_h5_bugu1'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|sg4_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'sg4_h5_bugu1'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('sg4_wx_bugu1'):
            from libs.bugu import Api
            api = Api(pf='sg4_wx_bugu1')
            if not api.check_login(params['sg4_wx_bugu1']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['sg4_wx_bugu1']['data']['mg_mem_id']

            pf_key = '%s|sg4_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'sg4_wx_bugu1'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('sg4_wx_bugu2'):
            from libs.bugu_h5 import Api
            api = Api(pf='sg4_wx_bugu2')
            user_id = api.check_login(params['sg4_wx_bugu2'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|sg4_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'sg4_wx_bugu2'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_h5_bugu'):
            from libs.bugu import Api
            api = Api(pf='cb_h5_bugu')
            if not api.check_login_h5(params['cb_h5_bugu']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['cb_h5_bugu']['mem_id']

            pf_key = '%s|cb_wx_bugu' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_h5_bugu'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_hw_cn'):
            api = HuaWei(pf='cb_hw_cn')
            hw = params['cb_hw_cn']

            access_token = hw['access_token']
            openid = api.verify_access_token(access_token)
            if not openid:
                return {'status': 'error', 'msg': u'无效的帐号'}

            pf_key = '%s|cb_hw_cn' % openid
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_hw_cn'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_hw_tw'):
            api = HuaWei_tw(pf='cb_hw_tw')
            hw = params['cb_hw_tw']

            access_token = hw['access_token']
            openid = api.verify_access_token(access_token)
            if not openid:
                return {'status': 'error', 'msg': u'无效的帐号'}

            pf_key = '%s|cb_hw_tw' % openid
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_hw_tw'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_wx_zhangwan_1'):
            from libs.zhangwan import Api
            api = Api(pf='cb_wx_zhangwan_1')
            if not api.check_login(params['cb_wx_zhangwan_1']):
                return {'status': 'error', 'msg': u'无效的帐号'}
            user_id = params['cb_wx_zhangwan_1']['player_id']

            pf_key = '%s|cb_wx_zhangwan' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_wx_zhangwan_1'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_wx_zhangwan_2'):
            from libs.zhangwan import Api
            api = Api(pf='cb_wx_zhangwan_2')
            if not api.check_login(params['cb_wx_zhangwan_2']):
                return {'status': 'error', 'msg': u'无效的帐号'}
            user_id = params['cb_wx_zhangwan_2']['player_id']

            pf_key = '%s|cb_wx_zhangwan' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_wx_zhangwan_2'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_wx_zhangwan_3'):
            from libs.zhangwan import Api
            api = Api(pf='cb_wx_zhangwan_3')
            if not api.check_login(params['cb_wx_zhangwan_3']):
                return {'status': 'error', 'msg': u'无效的帐号'}
            user_id = params['cb_wx_zhangwan_3']['player_id']

            pf_key = '%s|cb_wx_zhangwan' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_wx_zhangwan_3'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_wx_zhangwan_5'):
            from libs.zhangwan import Api
            api = Api(pf='cb_wx_zhangwan_5')
            if not api.check_login(params['cb_wx_zhangwan_5']):
                return {'status': 'error', 'msg': u'无效的帐号'}
            user_id = params['cb_wx_zhangwan_5']['player_id']

            pf_key = '%s|cb_wx_zhangwan' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_wx_zhangwan_5'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_h5_zhangwan'):
            from libs.zhangwan import Api
            api = Api(pf='cb_h5_zhangwan')
            if not api.check_login(params['cb_h5_zhangwan']):
                return {'status': 'error', 'msg': u'无效的帐号'}
            user_id = params['cb_h5_zhangwan']['player_id']

            pf_key = '%s|cb_wx_zhangwan' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_h5_zhangwan'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_ad_zhangwan'):
            from libs.zhangwan import Api
            api = Api(pf='cb_ad_zhangwan')
            if not api.check_login(params['cb_ad_zhangwan']):
                return {'status': 'error', 'msg': u'无效的帐号'}
            user_id = params['cb_ad_zhangwan']['player_id']

            pf_key = '%s|cb_wx_zhangwan' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_ad_zhangwan'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_ad_zhangwan1'):
            from libs.zhangwan import Api
            api = Api(pf='cb_ad_zhangwan1')
            if not api.af_check_login(params['cb_ad_zhangwan1']):
                return {'status': 'error', 'msg': u'无效的帐号'}
            user_id = params['cb_ad_zhangwan1']['open_id']

            pf_key = '%s|cb_ad_zhangwan1' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_ad_zhangwan1'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_ad_zhangwan2'):
            from libs.zhangwan import Api
            api = Api(pf='cb_ad_zhangwan2')
            if not api.af_check_login(params['cb_ad_zhangwan2']):
                return {'status': 'error', 'msg': u'无效的帐号'}
            user_id = params['cb_ad_zhangwan2']['open_id']

            pf_key = '%s|cb_ad_zhangwan2' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_ad_zhangwan2'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_wx_yiyang_1'):
            from libs.yiyang import Api
            api = Api(pf='cb_wx_yiyang_1')
            if not api.check_login(params['cb_wx_yiyang_1']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['cb_wx_yiyang_1']['data']['mg_mem_id']

            pf_key = '%s|cb_wx_yiyang' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_wx_yiyang_1'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_h5_yiyang'):
            from libs.yiyang import Api
            api = Api(pf='cb_h5_yiyang')
            if not api.check_login_h5(params['cb_h5_yiyang']):
                return {'status': 'error', 'msg': u'登陆失败'}
            user_id = params['cb_h5_yiyang']['mem_id']

            pf_key = '%s|cb_wx_yiyang' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'cb_h5_yiyang'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('cb_h5_mengwoai'):
            username = params['cb_h5_mengwoai']['uid']
            pwd = params['cb_h5_mengwoai']['token']
            user_zone_list = list(cls.query({'pf_key': username}))
            login_succ = False
            if user_zone_list:
                user_zone = user_zone_list[0]
                if user_zone.pf_pwd == pwd or user_zone.pf_pwd == hashlib.md5(pwd).hexdigest():
                    login_succ = True
            if not login_succ:
                if pwd and pwd == cache.get(settings.CACHE_PRE+'interim_pwd'+username, ''):
                    user_zone = UserZone.get(int(username))
                else:
                    return {'status': 'error', 'msg': u'用户名或密码错误'}

        # 封神
        elif params.get('fs_wx_37'):
            from libs.zsh_37 import Api
            api = Api(pf='fs_wx_37')
            user_id = api.check_login(params['fs_wx_37'])
            if not user_id:
                return {'status': 'error', 'msg': u'登陆失败'}

            pf_key = '%s|fs_wx_37' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'fs_wx_37'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        elif params.get('fs_wx_zhangwan_1'):
            from libs.zhangwan import Api
            api = Api(pf='fs_wx_zhangwan_1')
            if not api.check_login(params['fs_wx_zhangwan_1']):
                return {'status': 'error', 'msg': u'无效的帐号'}
            user_id = params['fs_wx_zhangwan_1']['player_id']

            pf_key = '%s|fs_wx_zhangwan' % user_id
            session_key = pf_key
            user_zone_list = list(cls.query({'pf_key': pf_key}))
            if not user_zone_list:
                user_zone = cls()
                user_zone.pf = 'fs_wx_zhangwan_1'
                user_zone.pf_key = pf_key
                user_zone.pf_pwd = session_key
            else:
                user_zone = user_zone_list[0]
                user_zone.pf_pwd = session_key

        #回调广告
        if phone_id[1]:
            phone_id = str(phone_id[1]).lower()
            freeze_phone = FreezePhone.get(phone_id)
            if freeze_phone:
                if settings.WHERE == 'kr':
                    return {'status': 'error', 'msg': u'자체환불로 인한 계정 정지, *******************으로 연락하세요'}
                else:
                    return {'status': 'error', 'msg': u'Login Error'}
            if user_zone.phone_id != phone_id:
                user_zone.phone_id = phone_id
                
                if phone_id == '00000000-0000-0000-0000-000000000000':
                    phone_id = user_ip
                AdClick.check_click(phone_id, uid=user_zone.uid)

        if user_ip:
            user_zone.user_ip = user_ip
        #     if cache.get(settings.CACHE_PRE+str(user_ip)+'_fz', 0):
        #         if settings.WHERE == 'kr':
        #             return {'status': 'error', 'msg': u'자체환불로 인한 계정 정지, *******************으로 연락하세요'}
        #         else:
        #             return {'status': 'error', 'msg': u'Login Error'}
        new_install_login_conf = game_config.help_msg.get('new_install_login_conf',None)
        if not new_install_login_conf:
            white_pf = True
        elif new_install_login_conf.get('white_pfs') and user_zone.pf in new_install_login_conf['white_pfs']:
            white_pf = True
        else:
            white_pf = False
        if new_install_login_conf and not new_install_login_conf['new_install'] and not white_pf:
            if not getattr(user_zone,'is_saved',None):
                return {'status': 'error', 'msg': u'连接服务器失败'}
            if str(user_zone.uid) in new_install_login_conf['black_uid_phone_id']:
                return {'status': 'error', 'msg': u'连接服务器失败'}
            if user_zone.phone_id in new_install_login_conf['black_uid_phone_id']:
                return {'status': 'error', 'msg': u'连接服务器失败'}
            a,b,c = new_install_login_conf['login_cond']
            if user_zone.login_time + datetime.timedelta(days=a) < now:
                if user_zone.pay_money < b:
                    if user_zone.max_lv < c:
                        return {'status': 'error', 'msg': u'连接服务器失败'}


        if not getattr(user_zone, 'is_saved', None) and params.get('invitation'):
            user_zone.inviter = params['invitation']
        user_zone.login_time = now
        user_zone.save()
        tstr = str(int(time.time()))
        md5_str = hashlib.md5('%s%s%s' % (user_zone.uid,tstr,settings.PWD)).hexdigest()
        sessionid = '%s|%s|%s' % (md5_str,tstr,user_zone.pf)

        #pid = hashlib.md5(phone_id[1].upper()).hexdigest()
        #AdClick.submit_gdt(None, pid)

        zone_user_num = {}
        user_num_zone_list = []
        zone_config = game_config.zone
        zone_pf_config = game_config.zone_pf
        d = None
        for zone, item in sorted(zone_config.items(),key=lambda x:x[1][2],reverse=True):
            zone_pf = zone_pf_config.get(zone, None)
            if zone_pf:
                if zone_pf[0] and user_zone.pf not in zone_pf[0]:
                    continue
                if zone_pf[1] and user_zone.pf in zone_pf[1]:
                    continue
            if item[8]:
                continue
            if item[2]>now:
                continue
            if d and item[2].date() != d.date():
                break
            d = item[2]
            user_num_zone_list.append(zone)
        if len(user_num_zone_list) > 1:
            for zone in user_num_zone_list:
                user_count= CacheTable.get('unum%s' % zone, 0)
                zone_user_num[zone] = user_count
                continue
                #cache_key = settings.CACHE_PRE + 'zone_user_count_%s' % zone
                #cache_data = cache.get(cache_key)
                #if cache_data is not None:
                #    cache_data = json.loads(cache_data)
                #    user_count, expire = cache_data
                #    if user_count == -1 and expire >= int(time.time()):
                #        continue
                #    if expire >= int(time.time()):
                #        zone_user_num[zone] = user_count
                #        continue
                #try:
                #    if settings.USE_SSL:
                #        user_count = pickle.loads(urllib2.urlopen('https://'+':'.join(map(str, game_config.zone[zone][1]))+'/api/?pwd='+ settings.PWD, pickle.dumps(['get_user_count',[]])).read())
                #    else:
                #        user_count = pickle.loads(urllib2.urlopen('http://'+':'.join(map(str, game_config.zone[zone][1]))+'/api/?pwd='+ settings.PWD, pickle.dumps(['get_user_count',[]])).read())
                #    zone_user_num[zone] = user_count
                #    cache_data = [user_count, int(time.time()) + 60*5]
                #except urllib2.URLError:
                #    cache_data = [-1, int(time.time()) + 60*5]

                #cache.set(cache_key, json.dumps(cache_data))

        #ip = unicode(request.META['HTTP_X_FORWARDED_FOR'].split(",")[0] if 'HTTP_X_FORWARDED_FOR' in request.META else request.META.get('REMOTE_ADDR', ''))
        #is_china = ip_db.find_map(ip, "CN")['country_name']==u'中国'

        ### 获取维护状态
        zone_maintain_status = CacheTable.get('zone_maintain_status', 1)
        if zone_maintain_status == 1:
            service_zone_list = []
        else:
            maintain_config = CacheTable.get('zone_maintain_config', '{}')
            maintain_config = eval(str(maintain_config))
            service_zone_list = maintain_config.get('zone_list', [])
        full_maintain_status = CacheTable.get('full_maintain_status', 1)
        service_info = [full_maintain_status]

        ### 预注册用户状态
        try:
            if 'pre_register_reward' in game_config.system_simple and user_zone.pf in game_config.system_simple['pre_register_reward'][2]:
                state_cache_key = '%spre_register_state_%s' % (settings.CACHE_PRE, user_zone.uid)
                if CacheTable.get(state_cache_key) is None:
                    if params.get('ea37'):
                        state = params['ea37'].get('fromPreRegister') == '1'
                    elif params.get('tw37'):
                        state = params['tw37'].get('fromPreRegister') == '1'
                    elif params.get('cb_37_kr'):
                        state = params['cb_37_kr'].get('fromPreRegister') == '1'
                    elif params.get('cb_google') or params.get('cb_google1'):
                        state = params.get('fromPreRegister') == '1'
                    else:
                        state = False
                    CacheTable.set(state_cache_key, state, 60*60*24*365*100)
        except:
            utils.print_err()

        return {'status': 'success',
                'tel': user_zone.tel,
                'uid': user_zone.uid,
                'sessionid': sessionid,
                'ucoin': user_zone.ucoin,
                'zones': user_zone.zones,
                'zone_login': user_zone.zone_login,
                'user_code': user_zone.user_code,
                'user_age': user_zone.get_user_age(**ext),
                'username': user_zone.pf_key,
                'pwd': user_zone.pf_pwd,
                'zones_user_num': zone_user_num,
                'user_id': user_id,
                'member': user_zone.member,
                'user_ip': user_ip,
                'service_info': service_info,
                'service_zone_list': service_zone_list,
                'ext': ext,
                'invitation': user_zone.inviter if user_zone.inviter else user_zone.inviter,
                }

    #发送登陆验证码
    @classmethod
    def send_login_sign(cls, request, params):
        tel = params['tel']
        user_zone_list = list(cls.query({'tel': tel}))
        if not user_zone_list:
            return {'status': 'error', 'msg': u'账号不存在'}
        cache_sign = cache.get(settings.CACHE_PRE+str(tel)+'_loginsign')
        if cache_sign:
            sign = cache_sign
        else:
            sign = '%06d' % random.randint(0, 6688)
        r = send_sms.send_sign(tel,sign)
        if not r:
            return {'status': 'error', 'msg': u'验证码发送失败，请重新发送'}
        cache.set(settings.CACHE_PRE+str(tel)+'_loginsign', sign, 60*5)
        return {'status': 'success'}

    #发送验证码
    @classmethod
    def send_sign(cls, request, params):
        tel = params['tel']
        uid = cls.get_old_uid(params['uid'],params['zone'])
        user_zone = cls.get(uid)
        if not user_zone:
            return {'status': 'error', 'msg': u'帐号不存在'}
        if user_zone.tel:
            return {'status': 'error', 'msg': u'该帐号已绑定手机'}
        user_zone_list  = list(cls.query({'tel': tel}))
        if user_zone_list:
            return {'status': 'error', 'msg': u'该手机号已绑定帐号'}
        cache_sign = cache.get(settings.CACHE_PRE+str(uid)+'_bsign')
        if cache_sign and cache_sign['tel'] == tel:
            sign = cache_sign['sign']
        else:
            sign = '%06d' % random.randint(0,11999)
        r = send_sms.send_sign(tel,sign)
        if not r:
            return {'status': 'error', 'msg': u'验证码发送失败，请重新发送'}
        cache.set(settings.CACHE_PRE+str(uid)+'_bsign', {'sign': sign,'tel': tel}, 60*5)
        return {'status': 'success'}

    #注册绑定手机号
    @classmethod
    def tel_bind(cls, request, params):
        sign = params['sign']
        uid = cls.get_old_uid(params['uid'],params['zone'])
        user_zone = cls.get(uid)
        if not user_zone:
            return {'status': 'error', 'msg': u'帐号不存在'}
        cache_sign = cache.get(settings.CACHE_PRE+str(uid)+'_bsign')
        unverified_pfs = game_config.system_simple.get('unverified_pfs', ['h5_37'])
        if user_zone.pf in unverified_pfs:
            cache_sign = {'sign': sign, 'tel': user_zone.uid}
        if not cache_sign or cache_sign['sign'] != sign:
            return {'status': 'error', 'msg': u'验证码不正确，请重新输入'}
        user_zone.tel = cache_sign['tel']
        user_zone.save()

        return {'status': 'success'}

    #fb,google绑定
    @classmethod
    def fb_google_bind(cls, request, params):
        pf_key = params['login_name']
        pf_pwd = params['login_pwd']
        if pf_key.find('|') != -1:
            return {'status': 'error', 'msg': u'無效賬號'}

        user_zone_list = list(cls.query({'pf_key': pf_key}))
        if not user_zone_list:
            return {'status': 'error', 'msg': u'無效賬號'}
        user_zone = user_zone_list[0]

        if params['login_type']=='fb':
            from libs.fb import Api
            pf = user_zone.pf
            if pf == 'cb_ios':
                pf = 'cb_google'
            api = Api(pf=pf)
            user_id = api.check_session(params)
        elif params['login_type'] == 'apple':
            from libs.appstore_pay import AppStorePay
            user_id = AppStorePay.verify_token(params['token'])
        else:
            from libs.google import Api
            api = Api()
            user_id = api.check_session(params)

        if not user_id:
            return {'status': 'error', 'msg': u'捆綁無效'}

        if user_zone.pf in ['cb_ios', 'cb_google', 'cb_google1']:
            _pf = 'cb_meng52'
        else:
            _pf = user_zone.pf
        new_pf_key = '%s|%s' % (user_id, _pf)
        try:
            #user_zone_list = list(cls.query({'pf_key': new_pf_key}))
            #if user_zone_list:
            #    user_zone_list[0].delete()

            user_zone.pf_key = new_pf_key
            user_zone.save()
        except:
            return {'status': 'error', 'msg': u'捆綁無效'}

        user_dict = user_zone.dumps(shallow=True)
        user_dict['status'] = 'success'
        return user_dict



    @classmethod
    def get_old_uid(cls, uid, zone):
        uid = int(uid)
        if settings.UID_SEPARATE:
            uid = uid - settings.UIDADD * int(zone)
        else:
            if uid > settings.UIDADD * int(zone):
                uid = uid - settings.UIDADD * int(zone)
        return uid

    @classmethod
    def get_new_uid(cls, uid, zone):
        uid = int(uid)
        if settings.UID_SEPARATE:
            uid = settings.UIDADD * int(zone) + int(uid)
        else:
            if int(uid) < settings.UIDADD * int(zone):
                uid = settings.UIDADD * int(zone) + int(uid)
        return uid

    @classmethod
    def get_old_uid_zone(cls, uid, zone):
        """
        获取原区号和短uid
        :param uid: 长uid
        :param zone: 合服区号
        :return:
        """
        if settings.UID_SEPARATE:
            old_uid = int(uid) % settings.UIDADD
            old_zone = int(uid) / settings.UIDADD
        else:
            if game_config.zone[zone][8]:
                old_uid = int(uid) % settings.UIDADD
                old_zone = int(uid) / settings.UIDADD
            else:
                old_uid = uid
                old_zone = zone
        return old_uid, str(old_zone)

    @staticmethod
    def safe_zone_sort_key(x):
        try:
            parts = x[0].split('_')
            if len(parts) == 2 and parts[0].startswith('h'):
                return int(parts[1]) - int(parts[0][1:]) * 100000
            else:
                return int(x[0])
        except Exception:
            return 0

    @classmethod
    def get_zone_list(cls, zone_status, **kwargs):
        """

        :param zone_status:
        :param kwargs:
                session_user: 如果包含此参数，则根据user的zone_list和zone_group_list筛选可操作性区列表返回
        :return:
        """
        import os
        log_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "app.log")
        now = datetime.datetime.now()
        with open(log_file, "a") as f:
            f.write(str(zone_status) + "\n")
        if zone_status=='old':
            zone_list = [[k, u'%s区-%s' % (k,v[0])] for k,v in game_config.zone.items() if v[8]==0]
            zone_list.sort(key=lambda x: int(x[0]))
        elif zone_status=='running':
         
            old_zone_list = []
            new_zone_list = []
            for k,v in sorted(game_config.zone.items(),key=lambda x:x[1][2]):
                if v[2] > now:
                    continue
                if not v[1][0]:
                    continue
                if v[8] == 0:
                    old_zone_list.append([k,u'%s区-%s' % (k,v[0]),v[8]])
                else:
                    new_zone_list.append([k,u'%s区-%s' % (k,v[0]),v[8]])
            old_zone_list.sort(key=lambda x: int(x[0]))
            #new_zone_list.sort(key=lambda x: x[2],reverse=True)
            new_zone_list.sort(key=cls.safe_zone_sort_key)
            zone_list = new_zone_list+old_zone_list
        elif zone_status=='all':
            old_zone_list = []
            new_zone_list = []
            for k,v in game_config.zone.items():
                if v[8] == 0:
                    old_zone_list.append([k,u'%s区-%s' % (k,v[0])])
                else:
                    new_zone_list.append([k,u'%s区-%s' % (k,v[0]),v[2],v[8]])
            old_zone_list.sort(key=lambda x: int(x[0]))
           
            new_zone_list.sort(key=cls.safe_zone_sort_key)
            
            zone_list = new_zone_list+old_zone_list
        elif zone_status == 'merge':
            zone_list = [[k, u'%s区-%s' % (k,v[0])] for k,v in game_config.zone.items() if v[8] != 0 and v[1][0] and v[2] < now]
            zone_list.sort(key=cls.safe_zone_sort_key)

        session_user = kwargs.get('session_user')
        data = []
        if session_user and session_user['username'] != 'admin':
            if session_user['zone_list']:
                for zone in zone_list:
                    if zone[0] not in session_user['zone_list']:
                        continue
                    data.append(zone)
            elif session_user['zone_group_list']:
                for zone in zone_list:
                    if not cls.belong_to_zone_groups(zone[0], session_user['zone_group_list']):
                        continue
                    data.append(zone)
            else:
                data = zone_list
        else:
            data = zone_list

        return data

    @classmethod
    def get_duplicate_open_date(cls, now=None):
        if not now:
            now = datetime.datetime.now()
        now_date = now.date()
        duplicate_config = game_config.duplicate
        open_day = duplicate_config['open_day']
        begin_date = datetime.date(*open_day[0])
        if now_date <= begin_date:
            open_date = begin_date + datetime.timedelta(days=open_day[2][0])
        else:
            pass_days = (now_date-begin_date).days
            cycle_num = pass_days/open_day[1]
            begin_date = begin_date+datetime.timedelta(days=cycle_num*open_day[1])
            for item in open_day[2]:
                if begin_date + datetime.timedelta(days=item)>=now_date:
                    open_date = begin_date + datetime.timedelta(days=item)
                    break
            else:
                open_date = begin_date + datetime.timedelta(days=open_day[1]+open_day[2][0])
        return open_date

            


    @classmethod
    def get_zone_group_list(cls):
        """
        获取分区组（专区）列表
        :return:
        """
        zone_group_list = []
        for group_name, values in sorted(game_config.help_msg['zone_groups'].items(), key=lambda x:x[1]['index']):
            zone_group_list.append({
                "id": group_name,
                "name": values['name']
            })
        return zone_group_list

    @classmethod
    def belong_to_zone_groups(cls, zone, zone_groups):
        """
        检测指定区（zone）是否在所属分组中
        :param zone: 区号
        :param zone_groups: 分组列表
                ['main', '37game']
        :return:
            True/False
        """
        for zone_group in zone_groups:
            group = game_config.help_msg['zone_groups'][zone_group]
            for _pattern in group['zone_match_pattern']:
                pattern = re.compile(_pattern)
                if pattern.match(zone):
                    return True
        else:
            return False

    @classmethod
    def get_zone_group(cls, zone):
        for group, v in sorted(game_config.help_msg['zone_groups'].items(), key=lambda x:x[1]['index']):
            for _pattern in v['zone_match_pattern']:
                pattern = re.compile(_pattern)
                if pattern.match(zone):
                    return group
        else:
            return ''

    def get_login_zone_list(self, merge_zone=False):
        zone_list = []
        for k,v in sorted(self.zone_login.items(),key=lambda x:x[1]['login_time'],reverse=True):
            if merge_zone:
                zone_list.append([k,v['merge_zone']])
            else:
                zone_list.append(k)
        return zone_list


    @classmethod
    def set_zones(cls, request, params):
        zone = str(params['zone'])
        uid = params['uid']
        lv = int(params['lv'])
        user_zone = cls.get(uid)
        user_ip = request.META['HTTP_X_FORWARDED_FOR'].split(",")[0] if 'HTTP_X_FORWARDED_FOR' in request.META else request.META.get('REMOTE_ADDR', '')
        
        #360syslog
        #user_zone.syslog_360(zone, user_ip)

        now = datetime.datetime.now()
        if user_zone.zone_login.has_key(zone):
            user_zone.zone_login[zone]['login_time'] = now
        else:
            # if not game_config.zone[zone][8]:
            user_num = CacheTable.get('unum%s' % zone, 0)
            user_num += 1
            CacheTable.set('unum%s' % zone, user_num)
            user_zone.zone_login[zone] = {
                    'merge_zone': zone,
                    'login_time': now,
                    }




        zones = user_zone.zones.split('|')
        if zone not in zones:
            user_num = CacheTable.get('unum%s' % zone, 0)
            user_num += 1
            CacheTable.set('unum%s' % zone, user_num)
            zones.append(str(zone))
            user_zone.zones = '|'.join(zones)
            user_zone.login_time = now
        else:
            if zone != zones[-1]:
                zones.remove(zone)
                zones.append(zone)
                user_zone.zones = '|'.join(zones)
                user_zone.login_time = now
        if user_zone.max_lv < lv:
            user_zone.max_lv = lv
        c_game_id = params.get('c_game_id',None)
        if c_game_id:
            user_zone.user_code = c_game_id
        user_zone.save()
        ## 预注册奖励发送
        cls.send_pre_register_gift(user_zone, zone)
        return {
                'status': 'success',
                'merge_uid': cls.get_new_uid(uid, zone)
                }

    def syslog_360(self, zone, user_ip):
        try:
            if self.pf in ['h5_360']:
                qid,x = self.pf_key.split('|')
                lv = self.max_lv
                uid = self.uid
                gid = '273'
                sid = 'S%s' % zone
                old_sid = 'S%s' % zone
                if self.zones:
                    log_type = 'login'
                else:
                    log_type = 'create_role'

                if log_type == 'create_role':
                    create_role_str = 'gameinfo interface=create_role&gname=zqwzh5&gid=%s&sid=%s&oldsid=%s&dept=38&user=%s&roleid=%s&time=%s&rolename=%s&channel=0&poster=0&site=0&ip=%s&prof=1' % (gid,sid,old_sid,qid,uid,int(time.time()),uid,user_ip)
                    h5qh_logger.info(create_role_str)
                login_str = 'gameinfo interface=login&gname=zqwzh5&gid=%s&sid=%s&user=%s&roleid=%s&oldsid=%s&dept=38&level=%s&ip=%s&map_id=0&time=%s' % (gid, sid, qid, uid, old_sid, lv, user_ip, int(time.time()))
                h5qh_logger.info(login_str)
            elif self.pf in ['ad_360']:
                log_info = {
                    'userid': self.pf_key.split('|')[0],
                    'deviceid': hashlib.md5(self.phone_id).hexdigest() if self.phone_id else None,
                    'msec': int(time.time()),
                    'appid': 'zqwzad',
                    'logname': None,
                    'version': None,
                    'stepnumid': None,
                    'normversion': 'v1.6',
                    'serverid': zone,
                    'gamechannel': 'tuiguang',
                    'roleid': self.uid,
                    'rolelevel': self.max_lv,
                    'combatpower': 0,
                    'dtime': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'test': 0,
                    'logsrc': 'server',
                    'os_version': 'android',
                    'model': None
                }

                if not self.zones:
                    role_info = {}
                    role_info.update(log_info)
                    role_info['logname'] = 'rolebuild'
                    role_info['stepnumid'] = '3025'
                    role_info['accname'] = self.pf_key.split('|')[0]
                    role_info['rolename'] = self.uid
                    role_info['roletypeid'] = None
                    adqh_logger.info('mgameinfo {log_info}'.format(log_info=json.dumps(role_info, separators=(',', ':'))))
                login_info = {}
                login_info.update(log_info)
                login_info['logname'] = 'login'
                login_info['stepnumid'] = '2050'
                login_info['ip'] = self.user_ip
                login_info['valueamount'] = 0
                login_info['server_name'] = zone
                login_info['package_name'] = ''
                adqh_logger.info('mgameinfo {log_info}'.format(log_info=json.dumps(login_info, separators=(',', ':'))))
        except:
            utils.print_err()

    @classmethod
    def ucoin_pay(cls, request, params):
        pid, zone, uid, ts = params['order_id'].split('|')
        md5_str,tstr,_ = params['sessionid'].split('|')
        if md5_str != hashlib.md5('%s%s%s' % (uid,tstr,settings.PWD)).hexdigest():
            return {'status': 'error', 'msg': u'非法登陆', 'ucoin': user_zone.ucoin}
        user_zone = cls.get(uid)
        v = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
        pay_id = '%s|%s|ucoin' % (pid,v)

        goods_config = game_config.goods.get(pid,None)
        if goods_config:
            pay_config = game_config.pay_config[goods_config['pay_id']]
        else:
            pay_config = game_config.pay_config[pid]
        pay_money = pay_config[0]
        if user_zone.ucoin < pay_money:
            return {'status':'error', 'msg': '充值失败', 'ucoin': user_zone.ucoin}
        user_zone.ucoin -= pay_money

        pay_result = UserZone.call_server_api(zone, 'user_pay', {'uid': user_zone.uid, 'pid': pid, 'pay_id': pay_id})
        if pay_result['succ'] != 1:
            return {'status':'error', 'msg': '充值失败', 'ucoin': user_zone.ucoin}
        user_zone.save()

        return {'status': 'success', 'ucoin': user_zone.ucoin}



    @classmethod
    def register(cls, request, params):
        username = params['username']
        pwd = params['pwd']
        pwd1 = params['pwd1']
        pf = params['pf']
        new_install_login_conf = game_config.help_msg.get('new_install_login_conf',None)
        if not new_install_login_conf:
            white_pf = True
        elif new_install_login_conf.get('white_pfs') and pf in new_install_login_conf['white_pfs']:
            white_pf = True
        else:
            white_pf = False
        if new_install_login_conf and not new_install_login_conf['new_install'] and not white_pf:
            return {'status': 'error', 'msg': u'连接服务器失败'}
        if len(username) not in range(3,17) or len(pwd) not in range(6,17):
            return {'status': 'error', 'msg': u'账户名或密码格式不正确'}
        if pwd != pwd1:
            return {'status': 'error', 'msg': u'重复输入密码不正确'}
        try:
            user_zone = cls()
            user_zone.pf = pf
            user_zone.pf_key = username
            user_zone.pf_pwd = hashlib.md5(pwd).hexdigest()
            user_zone.www = 1
            user_zone.save()
        except:
            return {'status': 'error', 'msg': u'用户名重复'}
        return {'status': 'success', 'username': username, 'pwd': pwd, 'uid': user_zone.uid}

    @classmethod
    def register_fast(cls, request, params):
        pf = params['pf']
        new_install_login_conf = game_config.help_msg.get('new_install_login_conf',None)
        if not new_install_login_conf:
            white_pf = True
        elif new_install_login_conf.get('white_pfs') and pf in new_install_login_conf['white_pfs']:
            white_pf = True
        else:
            white_pf = False
        if new_install_login_conf and not new_install_login_conf['new_install'] and not white_pf:
            return {'status': 'error', 'msg': u'连接服务器失败'}
        user_zone = cls()
        user_zone.pf = pf
        rnd_str = '%06d_%s' % (random.randint(0,229999),time.time())
        user_zone.pf_key = hashlib.md5(rnd_str).hexdigest()
        user_zone.www = 1
        user_zone.save()
        uid = user_zone.uid
        #username = utils.to_ustr(user_zone.uid)
        rnd_unamestr = '%03d' % random.randint(0,89)
        username = 'wz%s%s' % (uid,rnd_unamestr)
        for item in range(10):
            try:
                user_zone.pf_key = username
                pwd = '%06d' % random.randint(0,998899)
                pwd = hashlib.md5(pwd).hexdigest()[:8]
                user_zone.pf_pwd = hashlib.md5(pwd).hexdigest()
                user_zone.save()
                break
            except:
                rnd_unamestr = '%03d' % random.randint(0,555)
                username = 'wz%s%s' % (uid,rnd_unamestr)
                continue
        return {'status': 'success', 'username': username, 'pwd': pwd, 'uid': user_zone.uid}

    @classmethod
    def validate_user_code(cls, request, params):
        username = params['username']
        user_code = params['user_code']
        user_zone_list = list(cls.query({'pf_key': username}))
        if not user_zone_list:
            return {'status': 'error', 'msg': u'账户名不存在'}
        user_zone = user_zone_list[0]
        if user_zone.user_code:
            return {'status': 'error', 'msg': u'此账户已经实名认证'}
        if not id_validator.is_valid(user_code):
            return {'status': 'error', 'msg': u'请输入合法的身份证号'}
        user_zone.user_code = user_code
        user_zone.save()
        return {'status': 'success'}

    def get_age_by_birthday(self, birthday):
        if not birthday:
            return None
        birth_year = int(birthday[:4])
        birth_month = int(birthday[4:6])
        birth_day = int(birthday[6:8])
        now = datetime.datetime.now() + datetime.timedelta(days=1)
        year = now.year
        month = now.month
        day = now.day

        if year == birth_year:
            return 0
        else:
            if birth_month > month or (birth_month == month and birth_day > day):
                return year - birth_year - 1
            else:
                return year - birth_year

    def get_user_age(self, **kwargs):
        """
        获取玩家年龄
        :return:
        """
        if self.pf == 'hw':
            age = None
        elif self.pf in ['h5_qianqi', 'h5_qianguo']:
            age = self.get_age_by_birthday(kwargs['birthday'])
        else:
            try:
                user_code = utils.UserCode(self.user_code)
                age = user_code.age
            except:
                age = None
        return age

    def check_real_name_auth(self):
        """
        实名认证和防沉迷检测
        :return:
            True    # 可登录
            False   # 不能登录
        """
        now = datetime.datetime.now()
        real_name_config = game_config.system_simple['real_name']
        indulge_config = game_config.system_simple['indulge']
        if self.pf in real_name_config['pfs']:
            ## 在pfs列表中的不检测实名认证
            return True
        user_code = utils.UserCode(self.user_code)
        if user_code.age is None:
            ## 没有实名认证的
            if self.add_time + datetime.timedelta(minutes=real_name_config['time']) < now:
                return False
        else:
            if user_code.age >= 18:
                return True
            if now.hour not in xrange(*indulge_config['curfew']):
                ## 配置时间外防沉迷用户不可登陆
                return False
        return True

    @classmethod
    def call_server_api(cls, zone, method, params, retry=1, timeout=5):
        params['zone'] = zone
        data = [method, params]
        uid = params.get('uid')
        if uid:
            user_zone = cls.get(uid)
            server = game_config.zone[user_zone.zone_login[zone]['merge_zone']][1]
        else:
            server = game_config.zone[zone][1]
        for i in xrange(retry):
            try:
                if settings.USE_SSL:
                    return pickle.loads(urllib2.urlopen('https://' + ':'.join(map(str, server)) + '/api/?pwd=' + settings.PWD,
                                                        pickle.dumps(data), timeout=timeout).read())
                else:
                    return pickle.loads(urllib2.urlopen('http://' + ':'.join(map(str, server)) + '/api/?pwd=' + settings.PWD,
                                                        pickle.dumps(data), timeout=timeout).read())
            except:
                utils.print_err()
                if retry > 1:
                    time.sleep(1)
                continue

    @classmethod
    def call_duplicate_server_api(cls, server_addr, method, params, timeout=10):
        data = [method, params]
        if settings.USE_SSL:
            return pickle.loads(
                urllib2.urlopen('https://' + server_addr + '/api/?pwd=' + settings.PWD, pickle.dumps(data), timeout=timeout).read())
        else:
            return pickle.loads(
                urllib2.urlopen('http://' + server_addr + '/api/?pwd=' + settings.PWD, pickle.dumps(data), timeout=timeout).read())

    @classmethod
    def send_pre_register_gift(cls, user_zone, zone):
        """
        预注册奖励发送
        :return:
        """
        try:
            state_cache_key = '%spre_register_state_%s' % (settings.CACHE_PRE, user_zone.uid)
            pre_register_state = CacheTable.get(state_cache_key)
            if not pre_register_state:
                return
            pre_register_config = game_config.system_simple['pre_register_reward']
            gift_cache_key = '%spre_register_gift_%s' % (settings.CACHE_PRE, user_zone.uid)
            if CacheTable.get(gift_cache_key, default=False):
                return
            pf_lan = game_config.system_simple['server_raise_pf'].get(user_zone.pf, None)
            if pf_lan:
                return_msg_config = getattr(game_config, 'server_raise_msg_%s' % pf_lan)
            else:
                return_msg_config = game_config.server_raise_msg
            mail_name = return_msg_config[pre_register_config[0]]
            mail_info = return_msg_config[pre_register_config[1]]

            cls.call_server_api(zone,
                            'admin_gift_msg',
                            {'name_info': {'cn': [mail_name, mail_info]},
                             'gift_dict': pre_register_config[3],
                             'uids': [user_zone.uid]
                             })
            CacheTable.set(gift_cache_key, True, 60*60*24*365*100)
        except:
            utils.print_err()


    @classmethod
    def register_player(cls, player_id):
        """
        创建陪玩账号
        """
        user_zone = cls()
        user_zone.pf = 'auto_player'
        user_zone.player = 1
        user_zone.player_id = player_id
        rnd_str = '%06d_%s' % (random.randint(0, 777777),time.time())
        user_zone.pf_key = hashlib.md5(rnd_str).hexdigest()
        user_zone.www = 1
        user_zone.save()
        uid = user_zone.uid
        rnd_unamestr = '%03d' % random.randint(0, 669)
        username = 'ap%s%s' % (uid,rnd_unamestr)
        for item in range(10):
            try:
                user_zone.pf_key = username
                pwd = '%06d' % random.randint(0,88999)
                pwd = hashlib.md5(pwd).hexdigest()[:8]
                user_zone.pf_pwd = hashlib.md5(pwd).hexdigest()
                user_zone.save()
                break
            except:
                rnd_unamestr = '%03d' % random.randint(0,199)
                username = 'ap%s%s' % (uid,rnd_unamestr)
                continue
        return {'status': 'success', 'username': username, 'pwd': pwd, 'uid': user_zone.uid}

    @classmethod
    def get_random_player(cls, zone, count):
        uids = []
        data = list(UserZone.query({'player': 1}))
        if len(data) < game_config.player_robot['ai_mould_num']:
            return uids
        robot_uids = []
        for user_zone in data:
            gather_player = GatherPlayer.get(user_zone.player_id)
            if not gather_player:
                continue
            if gather_player.status != 1:
                continue
            if zone in user_zone.zone_login:
                continue
            robot_uids.append(user_zone.uid)
        if len(robot_uids) < sum(count):
            return uids
        for i,v in enumerate(count):
            # 三个国家
            for uid in random.sample(robot_uids, v):
                _user_zone = UserZone.get(uid)
                _user_zone.zone_login[zone] = {
                    'merge_zone': zone,
                    'login_time': datetime.datetime.now()
                }
                _user_zone.save()
                uids.append({'uid': uid, 'pf': _user_zone.pf, 'country': i})
                robot_uids.remove(uid)
        return uids

    @classmethod
    def get_zone_merge_times(cls, zone):
        if zone.startswith('h') and '_' in zone:
            try:
                merge_times = int(zone.split('_')[0][2:])
            except Exception:
                merge_times = 0
        else:
            merge_times = 0
        return merge_times

    @classmethod
    def get_service_port(cls, new_zone):
        """
        获取新增区所用的端口号
        :param new_zone:
        :return:
        """
        try:
            zone_num = int(new_zone.split('_')[1])
        except Exception:
            zone_num = int(new_zone) if new_zone.isdigit() else 0
        merge_times = cls.get_zone_merge_times(new_zone)
        if settings.WHERE == 'sg3':
            if zone_num in xrange(501, 700) or zone_num in xrange(2001, 4999):
                base_num = 10000
            else:
                base_num = 27000
        elif settings.WHERE == 'qh':
            if zone_num in xrange(1, 500):
                if merge_times == 1:
                    base_num = 29000
                else:
                    base_num = 39000
            elif zone_num in xrange(500, 600):
                if merge_times == 1:
                    base_num = 25000
                else:
                    base_num = 35000
            elif zone_num in xrange(600, 700):
                if merge_times == 1:
                    base_num = 26000
                else:
                    base_num = 36000
            elif zone_num in xrange(700, 800):
                if merge_times == 1:
                    base_num = 7000
                else:
                    base_num = 37000
            elif zone_num in xrange(800, 900):
                if merge_times == 1:
                    base_num = 28000
                else:
                    base_num = 38000
            elif zone_num in xrange(900, 1000):
                if merge_times == 1:
                    base_num = 49000
                else:
                    base_num = 59000
            elif zone_num in xrange(1000, 1100):
                if merge_times == 1:
                    base_num = 40000
                else:
                    base_num = 50000
            elif zone_num in xrange(1100, 1200):
                if merge_times == 1:
                    base_num = 41000
                else:
                    base_num = 51000
            elif zone_num in xrange(1200, 1300):
                if merge_times == 1:
                    base_num = 43000
                else:
                    base_num = 53000
            elif zone_num in xrange(1700, 1800):
                base_num = 27000
            elif zone_num in xrange(1500, 1600):
                base_num = 25000
            elif zone_num in xrange(1600, 1700):
                base_num = 46000
            elif zone_num in xrange(1800, 1900):
                base_num = 47000
            elif zone_num in xrange(1900, 2000):
                base_num = 48000
            elif zone_num in xrange(9001, 10000):
                base_num = 10000
        elif settings.WHERE == 'hk':
            if merge_times == 1:
                base_num = 7000
            else:
                base_num = 10000
        elif settings.WHERE == 'kr':
            if merge_times == 1:
                base_num = 7000
            else:
                base_num = 20000
        elif settings.WHERE == 'jpn':
            base_num = 15000
        elif settings.WHERE == 'vn':
            base_num = 10000
        elif settings.WHERE == 'war':
            base_num = 11000
        elif settings.WHERE == 'ea37':
            base_num = 10000
        elif settings.WHERE == 'tw37':
            base_num = 10000
        elif settings.WHERE == 'cb':
            if zone_num in xrange(1, 200):
                base_num = 30000
            elif zone_num in xrange(200, 400):
                base_num = 32000
            elif zone_num in xrange(400, 500):
                base_num = 34000
            elif zone_num in xrange(500, 600):
                base_num = 35000
        else:
            base_num = 20000
            try:
                return base_num + int(new_zone.split('_')[1])
            except Exception:
                return base_num
    @classmethod
    def get_user_ip_addr(cls, request, params):
        uid = params['uid']
        user_zone = cls.get(uid)
        if not user_zone:
            return {}
        user_ip = user_zone.user_ip
        if not user_ip:
            user_ip = request.META['HTTP_X_FORWARDED_FOR'].split(",")[0] if 'HTTP_X_FORWARDED_FOR' in request.META else request.META.get('REMOTE_ADDR', '')
        return utils.get_ip_addr(user_ip)




class FreezePhone(model.Model):
    is_multi_table = False
    autoincrement_key = False
    table_num = 1
    use_cache = settings.USE_CACHE
    cache_perfix = settings.CACHE_PRE
    cache_time = 60*10
    database = database
    table_format = 'FreezePhone_table'
    key_name_field = 'phone_id'
    seq_attrs = ['phone_id']

    def __init__(self, phone_id=None):
        self.phone_id = phone_id
        super(FreezePhone, self).__init__()




class FreezeLog(model.Model):
    is_multi_table = False
    autoincrement_key = False
    table_num = 1
    use_cache = settings.USE_CACHE
    cache_perfix = settings.CACHE_PRE
    cache_time = 60*10
    database = database
    table_format = 'FreezeLog_table'
    key_name_field = 'uid'
    seq_attrs = ['uid', 'zone', 'uname', 'status','freeze_time', 'freeze_msg', 'admin_user', 'admin_time']

    def __init__(self):
        self.uid= None
        self.zone = None
        self.uname = None
        self.status = None
        self.freeze_time = None
        self.freeze_msg = None
        self.admin_user = None
        self.admin_time = None
        super(FreezeLog, self).__init__()

    @classmethod
    def add_freeze_log(cls, uid, zone, uname, status, hours, admin_user, freeze_msg):
        uid_zone = '%s|%s' % (uid,zone)
        fz = cls.get(uid_zone)
        now = datetime.datetime.now()
        if not fz:
            fz = cls()
            fz.uid = uid_zone
            fz.zone = zone
        fz.uname = uname
        fz.status = status
        fz.admin_user = admin_user
        fz.admin_time = now

        if status != 0:
            fz.freeze_msg = freeze_msg
            if hours == -1:
                fz.freeze_time = datetime.datetime(2050,1,1)
                #封机器码
                if status == 1:
                    user_zone = UserZone.get(uid)
                    user_zone.freeze_status = 1
                    user_zone.save()
                    phone_id = user_zone.phone_id
                    if phone_id and status == 1:
                        if phone_id != '00000000-0000-0000-0000-000000000000' and not FreezePhone.get(phone_id):
                            FreezePhone(phone_id).save()
                    #if user_zone.user_ip:
                    #    cache.set(settings.CACHE_PRE+str(user_zone.user_ip)+'_fz', 1, 24*60*60)
            else:
                fz.freeze_time = now + datetime.timedelta(hours=hours)
        else:
            fz.freeze_time = now
            user_zone = UserZone.get(uid)
            user_zone.freeze_status = 0
            user_zone.save()
            phone_id = user_zone.phone_id
            if phone_id:
                freeze_phone = FreezePhone.get(phone_id)
                if freeze_phone:
                    freeze_phone.delete()
            user_ip = user_zone.user_ip
            #if user_ip:
            #    cache.set(settings.CACHE_PRE+str(user_ip)+'_fz', 0, 1)
        fz.save()
        FreezeHistory.add_freeze_history(fz)
        return True

class FreezeHistory(model.Model):
    is_multi_table = False
    autoincrement_key = False
    table_num = 1
    use_cache = settings.USE_CACHE
    cache_perfix = settings.CACHE_PRE
    cache_time = 60*10
    database = database
    table_format = 'FreezeHistory_table'
    key_name_field = 'id'
    seq_attrs = ['id', 'uid', 'zone', 'status','freeze_time', 'freeze_msg', 'admin_user', 'admin_time']

    def __init__(self):
        self.id = None
        self.uid = None
        self.zone = None
        self.uname = None
        self.status = None
        self.freeze_time = None
        self.freeze_msg = None
        self.admin_user = None
        self.admin_time = None
        super(FreezeHistory, self).__init__()

    @classmethod
    def add_freeze_history(cls, freeze):
        uid,zone = freeze.uid.split('|')
        fz_history = cls()
        fz_history.uid = uid
        fz_history.zone = zone
        fz_history.status = freeze.status
        fz_history.freeze_msg = freeze.freeze_msg
        fz_history.freeze_time = freeze.freeze_time
        fz_history.admin_user = freeze.admin_user
        fz_history.admin_time = freeze.admin_time
        fz_history.save()


class BugMsg(model.Model):
    is_multi_table = False
    autoincrement_key = False
    table_num = 1
    use_cache = settings.USE_CACHE
    cache_perfix = settings.CACHE_PRE
    cache_time = 60*10
    database = database
    table_format = 'BugMsg_table'
    key_name_field = 'uid'
    seq_attrs = ['uid', 'zone', 'time', 'content', 'status', 'data']
    adv_seq_attrs = ['data']

    def __init__(self,uid_zone=None):
        self.uid= uid_zone
        self.zone = None
        self.time = None
        self.content = None
        self.status = -1
        self.data = []
        super(BugMsg, self).__init__()

    @classmethod
    def install(cls, uid_zone):
        bug_msg = cls.get(uid_zone)
        if not bug_msg:
            zone = uid_zone.split('|')[1]
            bug_msg = cls(uid_zone)
            bug_msg.zone = zone
            bug_msg.save()
        return bug_msg

    @classmethod
    def check_session(cls, uid, sessionid):
        md5_str,tstr,_ = sessionid.split('|')
        if md5_str != hashlib.md5('%s%s%s' % (uid,tstr,settings.PWD)).hexdigest():
            return False
        return True



    @classmethod
    def get_bug_msg(cls, request, params):
        zone = str(params['zone'])
        uid = UserZone.get_old_uid(params['uid'],zone)
        zone = params['zone']
        uid_zone = '%s|%s' % (uid,zone)
        sessionid = params['sessionid']
        if not cls.check_session(uid,sessionid):
            return {'status': 'error', 'msg': u'非法登陆'}

        bug_msg = BugMsg.install(uid_zone)

        return bug_msg.data

    @classmethod
    def send_bug_msg(cls,request,params):
        content = params['content']
        zone = params['zone']
        uid = UserZone.get_old_uid(params['uid'],zone)
        uid_zone = '%s|%s' % (uid,zone)
        sessionid = params['sessionid']
        if not cls.check_session(uid,sessionid):
            return {'status': 'error', 'msg': u'非法登陆'}
        if len(content) == 0:
            return {'status': 'error', 'msg': u'密码错误'}
        bug_msg = cls.install(uid_zone)
        bug_msg.add_bug_msg(content)
        return bug_msg.data


    def add_bug_msg(self, content, st=0):
        now = datetime.datetime.now()
        data_dict = {
                'time': now,
                'content': content,
                'st': st,
                }
        self.data.append(data_dict)
        self.data = self.data[-20:]
        self.time = now
        self.content = content
        if self.status != 2 or st == 1:
            self.status = st
        if st == 3:
            self.status = 1
        #自动回复
        if st == 0:
            sys_re = True
            cd_time, msg_id,hero_head = game_config.system_simple['auto_reply_bug_msg']

            for item in self.data[::-1]:
                if item['st'] == 1:
                    if item['time'] + datetime.timedelta(minutes=cd_time) > now:
                        sys_re = False
                    break
            if sys_re:
                return_msg_config = game_config.return_msg
                msg_content = return_msg_config[msg_id]
                self.data.append({
                    'time':now,
                    'content': u'sys|%s' % msg_content,
                    'st': 1,
                    })
        self.save()
        return self.data






class AdminLog(model.Model):
    is_multi_table = False
    autoincrement_key = 'id'
    table_num = 1
    use_cache = False
    cache_perfix = settings.CACHE_PRE
    cache_time = 60*60

    database = database
    table_format = 'AdminLog_table'
    key_name_field = 'id'
    seq_attrs = ['id', 'admin_user', 'func_name', 'subtime', 'status', 'content']


    def __init__(self):
        self.id = None
        self.amdin_user = None
        self.subtime = datetime.datetime.now()
        self.func_name = None
        self.status = 0
        self.content = None
        super(AdminLog, self).__init__()

    @classmethod
    def add_adminlog(cls, admin_user,func_name,content,status=0):
        l = cls()
        l.admin_user = admin_user
        l.func_name = func_name
        l.content = str(content)
        l.status = status
        l.save()

    @classmethod
    def acquire_adminlogs(cls, func_name):
        return list(cls.query(condition={'func_name': func_name}, limit=100, order_by='-subtime'))


class AdClick(model.Model):
    is_multi_table = False
    autoincrement_key = False
    table_num = 1
    use_cache = False
    cache_perfix = settings.CACHE_PRE
    cache_time = 60*60

    database = database
    table_format = 'Adclick_table'
    key_name_field = 'imei'
    seq_attrs = ['imei', 'callback_url', 'status','pf','click_time','install_time', 'is_ad', 'uid']

    def __init__(self, imei=None):
        now = datetime.datetime.now()
        self.imei= imei
        self.callback_url = None
        self.status = 0
        self.is_ad = 0
        self.pf = ''
        self.click_time = now
        self.install_time = now
        self.uid = None
        super(AdClick, self).__init__()

    @classmethod
    def add_click(cls, imei, pf, callback_url, is_ad=0):
        if not imei:
            return False
        ad = cls.get(imei)
        now = datetime.datetime.now()
        if not ad:
            ad = cls(imei)
            ad.callback_url = callback_url
            ad.status = 0
            ad.is_ad = is_ad
            ad.pf = pf
            ad.save()
        else:
            if ad.status==0:
                if ad.click_time + datetime.timedelta(minutes=5) < now or ad.pf != pf:
                    ad.callback_url = callback_url
                    ad.pf = pf
                    ad.click_time = now
                    ad.is_ad = 5
                    ad.save()
        return True

    @classmethod
    def check_click(cls, phone_id, uid='',event_type=0):
        try:
            now = datetime.datetime.now()
            ad = None
            if phone_id:
                ad = AdClick.get(hashlib.md5(phone_id).hexdigest())
                if not ad:
                    ad = AdClick.get(phone_id)

            if event_type == 2 and not ad:
                if uid:
                    ad_list = list(AdClick.query({'uid': uid}))
                    if ad_list:
                        ad = ad_list[0]
            if ad:
                if_save = False
                if ad.status == 0:
                    callback_url = ad.callback_url + '&event_type=0'
                    data = urllib2.urlopen(callback_url, timeout=5).read()
                    ad.status = 1
                    if_save = True
                if not ad.uid and uid:
                    ad.uid = uid
                    if_save = True
                if event_type and ad.status != 2:
                    callback_url = ad.callback_url + '&event_type=2'
                    data = urllib2.urlopen(callback_url, timeout=5).read()
                    ad.status = 2
                    if_save = True
                if if_save:
                    ad.install_time = now
                    ad.save()


                """
                if not ad.uid or ad.status==0:
                    data = urllib2.urlopen(ad.callback_url, timeout=5).read()
                    ad.status = 1
                    ad.uid = uid
                    ad.install_time = now
                    ad.save()
                """
        except:
            utils.print_err()



    @classmethod
    def submit_gdt(cls, ad, phone_id):
        #https://developers.e.qq.com/oauth/authorize?client_id=**********&redirect_uri=http%3A%2F%2Fsg3.ptkill.com%2Fgdt_click%2F&account_type=ACCOUNT_TYPE_QQ&state=%2CwxLogin
        #https://sg3.ptkill.com/gdt_click/?authorization_code=c712477443698cb3c31215e061fce9ac&state=,wxLogin
        #https://api.e.qq.com/oauth/token?client_id=**********&client_secret=13ccc0de51d538b046eed47d5769e871&grant_type=authorization_code&authorization_code=c712477443698cb3c31215e061fce9ac&redirect_uri=http://sg3.ptkill.com/gdt_click/

        access_token = cls.refresh_token()
        url = 'https://api.e.qq.com/v1.1/user_actions/add?access_token=%s&timestamp=%s&nonce=%s' % (access_token, int(time.time()), random.randint(20000, 66999))
        #url = 'https://api.e.qq.com/v1.1/user_action_sets/add?access_token=%s&timestamp=%s&nonce=%s' % (access_token, int(time.time()), random.randint(10000, 99999))
        data = {'account_id': 8635690, 'user_action_set_id': **********, 'actions': [
            {'action_time': int(time.time()), 'user_id': {'hash_idfa': phone_id}, 'action_type': 'REGISTER', 'trace': {'click_id': ad.callback_url if ad else 'null'}}
            ]}
        #data = {'account_id': 9815598, 'type': 'IOS', 'mobile_app_id': **********}
        data = json.dumps(data)
        req = urllib2.Request(url, data, headers={'Content-Type': 'application/json'})
        res = urllib2.urlopen(req).read()

    @classmethod
    def refresh_token(cls):
        ar = CacheTable.get('gdt_token2')
        if not ar:
            #access_token =  "bb6da540c789d1b471233450cf5fbce1" 
            #refresh_token =  "73e65538e920f1226fd286b22056e172"
            access_token = "825952d6cab2515e6eb560a553516187"
            refresh_token = "554270a36f9b9556eb3b2a098774a74c"
        else:
            access_token, refresh_token = ar

        url = 'https://api.e.qq.com/oauth/token?client_id=**********&client_secret=13ccc0de51d538b046eed47d5769e871&grant_type=refresh_token&refresh_token=%s&redirect_uri=http://sg3.ptkill.com/gdt_click/' % refresh_token
        res = json.loads(urllib2.urlopen(url).read())
        access_token = res['data']['access_token']
        refresh_token = res['data']['refresh_token']
        CacheTable.set('gdt_token2', (access_token, refresh_token))

        return access_token

class UserDuplicate(model.Model):
    is_multi_table = False
    autoincrement_key = False
    table_num = database.user_table_num
    #use_cache = settings.USE_CACHE
    use_cache = False
    cache_perfix = settings.CACHE_PRE
    cache_time = 60*60
    database = database
    table_format = 'UserDuplicate_table'
    key_name_field = 'udid'

    seq_attrs = ['udid','uid', 'uname', 'zone', 'level', 'prestige', 'head', 'join_time', 'open_date','group_id', 'hero_data','troop_data','pay_money','admin_pay','power','dnum','pf']
    adv_seq_attrs = ['hero_data','troop_data']

    def __init__(self,udid=None):
        self.udid= udid
        self.uid = None
        self.uname = None
        self.zone = None
        self.level = None
        self.prestige = None
        self.group_id = None
        self.head = None
        self.open_date = None
        self.join_time = datetime.datetime.now()
        self.pay_money = 0
        self.admin_pay = 0
        self.power = 0
        self.dnum = -1
        self.hero_data = {}
        self.troop_data = {}
        self.pf = None
        super(UserDuplicate, self).__init__()


class CountryNotice(model.Model):
    is_multi_table = False
    autoincrement_key = False
    table_num = 1
    use_cache = settings.USE_CACHE
    cache_perfix = settings.CACHE_PRE
    cache_time = 60*10
    database = database
    table_format = 'CountryNotice_table'
    key_name_field = 'zone_cid'
    seq_attrs = ['zone_cid', 'update_time', 'content']

    def __init__(self, zone_cid=None):
        self.zone_cid = zone_cid
        self.update_time = None
        self.content = None
        super(CountryNotice, self).__init__()



class AdminPayRecord(model.Model):
    is_multi_table = False
    autoincrement_key = 'id'
    table_num = 1
    use_cache = False
    cache_perfix = settings.CACHE_PRE
    cache_time = 60*60

    database = database
    table_format = 'AdminPayRecords_table'
    key_name_field = 'id'
    seq_attrs = ['id', 'uid', 'admin_user', 'pay_money', 'last_zone', 'last_uname', 'first_time', 'last_time']


    def __init__(self):
        self.id = None
        self.uid = None
        self.amdin_user = None
        self.pay_money = 0
        self.last_zone = None
        self.last_uname = None
        self.first_time = None
        self.last_time = None
        super(AdminPayRecord, self).__init__()

    @classmethod
    def add_pay(cls, admin_user, uid, pay_money, zone, uname, last_time=None):
        r = list(cls.query(condition={'admin_user': admin_user, 'uid': uid}))
        if not r:
            r = cls()
            r.uid = uid
            r.admin_user = admin_user
            r.first_time = datetime.datetime.now()
        else:
            r = r[0]
        r.pay_money += pay_money
        r.last_zone = zone
        r.last_uname = uname
        r.last_time = last_time if last_time else datetime.datetime.now()
        r.save()



class SendUcoinLog(model.Model):
    is_multi_table = False
    autoincrement_key = 'id'
    table_num = 1
    use_cache = False
    cache_perfix = settings.CACHE_PRE
    cache_time = 60*60

    database = database
    table_format = 'send_ucoin_log_table'
    key_name_field = 'id'
    seq_attrs = ['id', 'uids', 'admin_user', 'ucoin', 'remark', 'subtime', 'status']


    def __init__(self):
        self.id = None
        self.uids = None
        self.amdin_user = None
        self.ucoin = None
        self.subtime = datetime.datetime.now()
        self.remark = ''
        self.status = 0
        super(SendUcoinLog, self).__init__()

class CallInterfaceRecord(model.Model):
    is_multi_table = False
    autoincrement_key = 'id'
    table_num = 1
    use_cache = False
    cache_perfix = settings.CACHE_PRE
    cache_time = 60*60

    database = database
    table_format = 'call_interface_records_table'
    key_name_field = 'id'
    seq_attrs = ['id', 'op_user', 'time', 'req_data', 'rsp_data']
    adv_seq_attrs = ['req_data', 'rsp_data']


    def __init__(self):
        self.id = None
        self.time = datetime.datetime.now()
        self.op_user = None
        self.req_data = {}
        self.rsp_data = {}
        super(CallInterfaceRecord, self).__init__()

    @classmethod
    def save_record(cls, op_user, req_data, rsp_data=None):
        r = cls()
        r.op_user = op_user
        if not isinstance(req_data, dict):
            req_data = json.loads(req_data)
        r.req_data = req_data
        if rsp_data:
            if not isinstance(rsp_data, dict):
                rsp_data = json.loads(rsp_data)
            r.rsp_data = rsp_data
        r.save()

class GatherPlayer(model.Model):
    is_multi_table = False
    autoincrement_key = 'id'
    table_num = 1
    use_cache = False
    cache_perfix = settings.CACHE_PRE
    cache_time = 60*60

    database = database
    table_format = 'gather_players_table'
    key_name_field = 'uid'
    seq_attrs = ['uid', 'zone', 'status', 'score', 'days', 'times', 'last_time', 'last_did', 'add_time', 'login_time', 'discard_time']
    adv_seq_attrs = ['day_score']

    STATUS_MAPS = {
        '0_0': [u'未采满', 'orange'],
        '0_1': [u'可登用', 'blue'],
        '1': [u'已登用', 'green'],
        '2': [u'已废弃', 'red'],
        '3': [u'已删除', 'gray'],
    }

    
    Army_type = {
        'building009': 0,    #步
        'building010': 1,    #骑
        'building011': 2,    #弓
        'building012': 3,    #方
        }

    Army_type_2 = {
        0: 'building009',
        1: 'building010',
        2: 'building011',
        3: 'building012',
        }

    def __init__(self):
        self.uid = None
        self.zone = None
        self.status = 0   #0普通 1已登用 2已废弃 3已删除
        self.days = 0
        self.times = 0
        self.score = 0
        self.last_time = None
        self.last_did = None
        self.add_time = None
        self.login_time = None
        self.discard_time = None
        self.day_score = {}
        super(GatherPlayer, self).__init__()
        
    def to_dict(self):
        dic = {}
        dic['uid'] = self.uid
        dic['zone'] = self.zone
        dic['status'] = self.status
        dic['status_cn'] = self.status_cn
        dic['days'] = self.days
        dic['times'] = self.times
        dic['score'] = self.score
        dic['last_time'] = utils.format_time(self.last_time) if self.last_time else '-'
        dic['last_did'] = self.last_did
        dic['add_time'] = utils.format_time(self.add_time) if self.add_time else '-'
        dic['login_time'] = utils.format_time(self.login_time) if self.login_time else '-'
        dic['discard_time'] = utils.get_time_str(self.discard_time) if self.discard_time else '-'
        return dic

    @property
    def registrable(self):
        return self.days >= game_config.player_robot['mould_day'][0] and self.times >= game_config.player_robot['mould_times'][0]

    def get_operation_menu(self):
        op_menus = [
            [u'普通', 0, 0], #操作，状态码，是否可用
            [u'登用', 1, 0],
            [u'废弃', 2, 0],
            ]
        if self.status == 0:
            if self.registrable:
                # 可登用
                op_menus[1][2] = 1
                op_menus[2][2] = 1
            else:
                # 未采满
                op_menus[2][2] = 1
        elif self.status == 1:
            # 已登用
            op_menus[0][2] = 1
            op_menus[2][2] = 1
        elif self.status == 2:
            # 已废弃
            op_menus[0][2] = 1
            if self.registrable:
                op_menus[1][2] = 1
        return op_menus

    @property
    def status_cn(self):
        if self.status == 0:
            if self.registrable:
                # 可登用
                status = GatherPlayer.STATUS_MAPS[str('0_1')]
            else:
                # 未采满
                status = GatherPlayer.STATUS_MAPS[str('0_0')]
        else:
            status = GatherPlayer.STATUS_MAPS[str(self.status)]
        return status

    @classmethod
    def get_fight_prepare_data(cls, hid, user_data):
        hero_config = game_config.hero
        barracks_config = game_config.system_simple['barracks']
        shogun_val = user_data['shogun_value']

        hero = user_data['hero'][hid]
        army = [{},{}]
        hero_type = hero_config[hid]['type']
        if shogun_val:
            shogun = [shogun_val[hero_type]]

        for i in [0,1]:
            army_type = hero_config[hid]['army'][i]
            if shogun_val:
                shogun.append(shogun_val[army_type+3])
            army_lv = hero['army'][i]
            bid = cls.Army_type_2[army_type]
            blv = user_data['home'][bid]['lv']-1
            if blv < 0:
                blv = 0
            rank = barracks_config[bid][blv][3]
            army[i]['type'] = army_type
            army[i]['rank'] = rank
            army[i]['lv'] = army_lv
            army[i]['add'] = [user_data['home'][bid]['science'][0], user_data['home'][bid]['science'][6]]
        hero_equip = []
        for eid in hero['equip']:
            item = [eid, user_data['equip'][eid]['lv'], user_data['equip'][eid]['wash'], user_data['equip'][eid].get('enhance', 0)]
            hero_equip.append(item)
        star = {}
        for star_id in hero['star'].values():
            star_lv = user_data['star'][star_id]['lv']
            star_id = star_id.split('|')[0]
            if star.has_key(star_id):
                star[star_id] += star_lv
            else:
                star[star_id] = star_lv


        build78_lv = [user_data['home']['building007']['lv'],user_data['home']['building008']['lv']]


        hero_dict = {
            'uid': user_data['uid'],
            'hid': hid,
            'lv': hero['lv'],
            'skill': hero['skill'],
            'fate': hero['fate'],
            'army': army,
            'hero_star': hero['hero_star'],
            'star': star,
            'legend': {},
            'equip': hero_equip,
            'science_passive': user_data['science'].get('passive',{}),
            'building': build78_lv,
            'skin_lv': user_data['skin_stars'],
            }
        if hero.has_key('revived'):
            hero_dict['revived'] = hero['revived']
        if hero.has_key('assign'):
            hero_dict['assign'] = hero['assign']

        #战计
        if user_data['scheme']:
            hero_dict['scheme'] = user_data['scheme']
        scheme_box = None
        for item in user_data['scheme_box']:
            if item['hid'] != hid:
                continue
            scheme_box = item['scheme']
            break
        if scheme_box is not None:
            hero_dict['scheme_box'] = scheme_box
        #魂玉
        if hero.has_key('soul'):
            hero_dict['soul'] = hero['soul'][:9]
            if hero['soul'][9]:
                eid = hero['soul'][9]
                soul_equip= [eid, user_data['equip'][eid]['lv'], user_data['equip'][eid]['wash'], user_data['equip'][eid].get('enhance', 0)]
                hero_dict['soul'].append(soul_equip)
            else:
                hero_dict['soul'].append(None)
        #############################
        if hero.has_key('sp_id'):
            if hero['sp_id'] != [None,None]:
                hero_dict['sp_army'] = []
                for item in hero['sp_id']:
                    if item:
                        hero_dict['sp_army'].append([item,user_data['sp_army'][item]])
                    else:
                        hero_dict['sp_army'].append(None)


        if hero.has_key('counter'):
            counter_dict = {}
            for item in hero['counter'].values():
                counter_dict[item[0]] = item[1]
            hero_dict['counter'] = counter_dict
        if hero.has_key('beast_ids'):
            beast = []
            for item in hero['beast_ids']:
                if item:
                    beast.append(user_data['beast'][item])
                else:
                    beast.append(None)

            hero_dict['beast'] = beast
        if hero.get('skin_id',None):
            hero_dict['skin_id'] = hero['skin_id']
        if hero.has_key('awaken'):
            hero_dict['awaken'] = hero['awaken']
        if hero.has_key('formation'):
            hero_dict['formation_arr'] = hero['formation']
        if hero.has_key('formation_index'):
            hero_dict['formation_index'] = hero['formation_index']

        for legend_hid in game_config.fight['legendTalent'].keys():
            if user_data['hero'].has_key(legend_hid):
                hero_dict['legend'][legend_hid] = user_data['hero'][legend_hid]['hero_star']
        if shogun_val:
            hero_dict['shogun'] = shogun
        hero_adjutant = hero.get('adjutant', None)
        adj_hero_skill_lv_max = game_config.system_simple['skill_cost_max_lv']['7']
        adj_hero_army_lv_max = game_config.system_simple['skill_cost_max_lv']['8']

        for science_id, science_add_lv in game_config.system_simple['skill_cost_max_lv_science']['7']:
            stype = game_config.science[science_id]['type']
            slv = user_data['science'].get(stype,{}).get(science_id,0)
            adj_hero_skill_lv_max += science_add_lv*slv

        for science_id, science_add_lv in game_config.system_simple['skill_cost_max_lv_science']['8']:
            stype = game_config.science[science_id]['type']
            slv = user_data['science'].get(stype,{}).get(science_id,0)
            adj_hero_army_lv_max += science_add_lv*slv

        equip_config = game_config.equip
        adjutant_enhance = game_config.system_simple['adjutant_enhance']
        adjutant_wash = game_config.system_simple['adjutant_wash']
        wash_config = game_config.equip_wash
        if hero_adjutant:
            adjutant = []
            skill_config = game_config.skill
            for item in hero_adjutant:
                if not item:
                    adjutant.append(None)
                    continue
                adj_hero_skill_lv = 0
                adj_hero_army_lv = 0
                adj_hero_hero_star = user_data['hero'][item]['hero_star']
                for k,v in user_data['hero'][item]['skill'].items():
                    skill_type = skill_config[k].get('type',888)
                    if skill_type > 4:
                        continue
                    if skill_type == 4:
                        adj_hero_skill_lv += v
                    else:
                        adj_hero_army_lv += v
                adj_hero_skill_lv = min(adj_hero_skill_lv,adj_hero_skill_lv_max)
                adj_hero_army_lv = min(adj_hero_army_lv,adj_hero_army_lv_max)
                equip_score = 0

                # 副将魂玉连接装备
                if user_data['hero'][item].has_key('soul') and user_data['hero'][item]['soul'][9]:
                    adj_soul_equip = user_data['hero'][item]['soul'][9]
                    soul_id = user_data['hero'][item]['soul'][-2]
                    soul_lv = int(soul_id[6:8])
                    soul_equip_limit = game_config.fight['soulEquipLimitArr'][soul_lv-1]
                    adj_soul_equip_lv = min(user_data['equip'][adj_soul_equip]['lv'], soul_equip_limit[0])
                    adj_soul_equip_enhance = min(user_data['equip'][adj_soul_equip].get('enhance',0), soul_equip_limit[2])
                    adj_soul_equip_wash = user_data['equip'][adj_soul_equip]['wash'][:soul_equip_limit[1]]
                    try:
                        equip_score += equip_config[adj_soul_equip]['adjutant_equip'][adj_soul_equip_lv]
                        equip_score += adjutant_enhance[adj_soul_equip_enhance]
                        for wash_id in adj_soul_equip_wash:
                            if not wash_id:
                                continue
                            wash_rarity = wash_config[wash_id]['rarity']
                            equip_score += adjutant_wash[wash_rarity]
                    except:
                        if settings.WHERE == 'local':
                            raise
                for equip_id in user_data['hero'][item]['equip']:
                    equip_lv = user_data['equip'][equip_id]['lv']
                    equip_enhance = user_data['equip'][equip_id].get('enhance',0)
                    try:
                        equip_score += equip_config[equip_id]['adjutant_equip'][equip_lv]
                        equip_score += adjutant_enhance[equip_enhance]
                        for wash_id in user_data['equip'][equip_id]['wash']:
                            if not wash_id:
                                continue
                            wash_rarity = wash_config[wash_id]['rarity']
                            equip_score += adjutant_wash[wash_rarity]
                    except:
                        if settings.WHERE == 'local':
                            raise
                adj_awaken = user_data['hero'][item].get('awaken',0)
                adj_skin_id = user_data['hero'][item].get('skin_id',None)

                adjutant.append([item,adj_hero_skill_lv,adj_hero_army_lv,adj_hero_hero_star,equip_score,adj_awaken,adj_skin_id])
            if adjutant != [None,None]:
                hero_dict['adjutant'] = adjutant



        return hero_dict

    @classmethod
    def get_user_pay(cls, user_data):
        pay_config = game_config.pay_config
        admin_pay = 0
        pay_money = user_data['records']['pay_money']
        for pay_id in user_data['records']['pay_ids']:
            if str(pay_id).find('|') == -1:
                continue
            if str(pay_id).startswith('pay'):
                pid = pay_id.split('|')[0]
            elif str(pay_id).startswith('gd'):
                pid = pay_id.split('|')[0]
                try:
                    pid = game_config.goods[pid]['pay_id']
                except KeyError:
                    continue
            else:
                continue
            if pay_config.has_key(pid):
                admin_pay += pay_config[pid][0]
        sale_money = 0
        sale_pay_config = game_config.system_simple['sale_pay']
        for item in user_data['used_sale_pay']:
            try:
                item_sale = pay_config[sale_pay_config[item][0]][0] - pay_config[item][0]
                sale_money += item_sale
            except KeyError:
                continue

        pay_money = pay_money-admin_pay
        pay_money = pay_money-sale_money
        return pay_money, admin_pay, sale_money

class GatherPlayerData(model.Model):
    is_multi_table = False
    autoincrement_key = 'id'
    table_num = 1
    use_cache = False
    cache_perfix = settings.CACHE_PRE
    cache_time = 60*60

    database = database
    table_format = 'gather_player_data_table'
    key_name_field = 'id'
    seq_attrs = ['id', 'did', 'uid', 'time', 'data']
    adv_seq_attrs = ['data']


    def __init__(self):
        self.id = None
        self.did = None
        self.uid = None
        self.time = datetime.datetime.now()
        self.data = {}
        super(GatherPlayerData, self).__init__()

class InvitationRecord(model.Model):
    is_multi_table = False
    autoincrement_key = False
    table_num = 1
    use_cache = False
    cache_perfix = settings.CACHE_PRE
    cache_time = 60*60

    database = database
    table_format = 'invitation_records_table'
    key_name_field = 'uid'
    seq_attrs = ['uid', 'data']
    adv_seq_attrs = ['data']


    def __init__(self):
        self.uid = None
        self.data = {}
        super(InvitationRecord, self).__init__()

    @classmethod
    def sync_invitation(cls, request, params):
        """
        37微信邀请数据同步
        """
        if 'get_friend' not in game_config.system_simple:
            return {'status': 'success'}
        if game_config.system_simple['get_friend'].get('switch', 1) == 1:
            return {'status': 'success'}

        from libs.h5_37 import Api
        uid = params['uid']
        zone = params['zone']
        app_data = UserZone.call_server_api(zone, 'get_invitation_data', {'uid': uid})
        if not app_data:
            return {'status': 'success'}
        master_uid = params['master_uid']
        if master_uid != app_data['master_uid']:
            return {'status': 'success'}
        buildinglv = app_data['buildinglv']
        money = app_data['money']
        pf_data = params['pf_data']
        user_zone = UserZone.get(uid)
        pf_uid = user_zone.pf_key.split('|')[0]
        if pf_uid != str(pf_data['uid']):
            return {'status': 'success'}
        api = Api(pf='wx_37')
        if not api.check_uid(pf_data):
            return {'status': 'success'}
        if not user_zone.inviter:
            return {'status': 'success'}
        if user_zone.inviter != str(master_uid):
            return {'status': 'success'}
        old_uid = int(master_uid)%settings.UIDADD
        old_zone = str(int(master_uid)/settings.UIDADD)
        master_user_zone = UserZone.get(old_uid)
        if not master_user_zone or old_zone not in master_user_zone.zone_login:
            return {'status': 'success'}
        r = cls.get(master_uid)
        if not r:
            r = cls()
            r.uid = master_uid
        r.data.setdefault(uid, {})
        if buildinglv >= r.data[uid].get('buildinglv', 0):
            r.data[uid]['buildinglv'] = buildinglv
        if money >= r.data[uid].get('money', 0):
            r.data[uid]['money'] = money
        r.save()
        return {'status': 'success'}


class IpaddressRecord(model.Model):
    is_multi_table = False
    autoincrement_key = False
    table_num = 1
    use_cache = False
    cache_perfix = settings.CACHE_PRE
    cache_time = 60*60

    database = database
    table_format = 'ipaddress_records_table'
    key_name_field = 'ip'
    seq_attrs = ['ip', 'count', 'data']
    adv_seq_attrs = ['data']


    def __init__(self):
        self.ip = None
        self.count = 0
        self.data = {}
        super(IpaddressRecord, self).__init__()

    @classmethod
    def add_record(cls, uid, zone, ip, pf_key, sessionid):
        ir = cls.get(ip)
        if not ir:
            ir = cls()
            ir.ip = ip
        if pf_key not in ir.data:
            ir.count += 1
        ir.data[uid] = [pf_key, sessionid, datetime.datetime.now(), zone]
        ir.save()

