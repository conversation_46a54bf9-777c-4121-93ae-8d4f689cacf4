{

   'effS000':{  
      'speed':9.9,                       
      'fire':{
         'time':75000,                   
         'res':'fire036',             
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer',                
      },
      'move':{
         
      },
      'bang':{
         'res':'bangS000',             
         'resSpeed':0.8,      
         'time':800,                 

         'sound':'hit7272',           
         'soundVolume':0.9,           
         'soundDelay':500,            
      },
      'hurt':{
         'injured':2,                 
         'forceO':45,                
         'forceY':25,
         'time':0,                 
         'shock':800, 
         'hitTime':0,
         
         'pauseTime':200,              
      },

   },

   'effS002':{ 
      'bannerTime':2500,  
      'fire':{
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
      'bang':{
         'res':'bangS002',             
         'resAdd':2,    
         'resSpeed':0.6,    
         'time':500,                  

         'sound':'hit7272',           
         'soundVolume':0.9,        
         'soundDelay':500,            
      },
      'hurt':{
         'injured':2,                 
         'forceO':25,               
         'forceY':80,
         'time':800,                 
         'shock':800, 
         'endTime':2500, 
         
      },
   },

   'effS002':{  
      'bannerTime':2500,  
      'noHurt':2,
      'fire':{
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
      'speed':9.4,                       
      'bullet':{
         'res':'buffS002',           
         'resScale':2.5,      
         'gravity':2,                 
      },
      'hurt':{
         'injured':2,                 
         'time':800,                 
         
      },
   },
   'effS002h':{  
      'fire':{
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
      'hurt':{
         'special':'special230',      
         'specialAdd':2,
         'specialScale':2,
         'specialSpeed':2,
         'specialRndDelay':200,          
         'injured':2,                 
         'num':5, 		      
         'forceR':30,                
         'shock':800, 
         
         'pauseTime':500,   

         'sound':'hit230',              
         'soundVolume':2,              
      },
   },

   'effS003':{  
      'bannerTime':2500,  
      'speed': 3.5,                       
      'fire':{
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer',                
      },
      'move':{
         'endTime':2200,              
      },
      'bang':{
         'res':'bangS003',             
         'resSpeed':0.5,      
         'time':8000,                  

         'sound':'hit7272',           
         'soundVolume':0.9,            
         'soundDelay':500,            
      },
      'hurt':{
         'injured':2,                 
         'forceO':45,                
         'forceY':25,
         'time':0,                 
         'shock':800, 
         'hitTime':0,
         
         'pauseTime':800,              
      },

   },

   'effS004':{  
      'bannerTime':2500,  
      'speed':4.8,                       
      'fire':{
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer',                
      },
      'move':{
         'endTime':600,              
      },
      'bang':{
         'res':'bang827',             
         'resSpeed':0.8,      
         'time':800,                  

         'sound':'hit7022',            
         'soundVolume':0.6,            
      },
      'hurt':{
         'injured':2,                 
         'forceO':45,                
         'forceY':25,
         'time':0,                 
         'shock':800, 
         'hitTime':0,
         
         'pauseTime':200,              
      },

   },

   'effS005':{  
      'bannerTime':2500,  
      'fire':{
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer',                
      },
      'speed':5.7,                       
      'move':{
         'endTime':600,              
      },
      'bang':{
         'res':'specialS005',             
         'resSpeed':0.8,      
         'time':800,                  

         'sound':'hit7022',             
         'soundVolume':0.6,           
      },
      'hurt':{
         'injured':2,                 
         'forceO':45,                
         'forceY':25,
         'time':0,                 
         'shock':800, 
         'hitTime':0,
         
         'pauseTime':200,              
      },

   },

   'effS006':{  
      'bannerTime':2500,  
      'fire':{
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
      'bang':{
         'res':'bang863',             
         'resSpeed':0.8,      
         'time':800,                  

         'sound':'hit7022',              
         'soundVolume':0.6,              
      },
      'hurt':{
         'injured':2,                 
         'forceO':45,                
         'forceY':25,
         'time':0,                 
         'shock':800, 
         'hitTime':0,
         
         'pauseTime':500,              
      },

   },
   'effS007':{  
      'bannerTime':2500,  
      'fire':{
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
      'bang':{
         'res':'bang7092',             
         'resSpeed':2.2,      
         'resScale':0.8,
         'resX':-800,
         'resZ':250,
         'time':8000,                  

         'sound':'hit7092',             
         'soundVolume':2,            
      },
      'hurt':{
         'special':'bang7092',      
         
         'specialScale':0.5,
         'specialSpeed':2.2,
         'specialRndDelay':800,          
         'injured':2,                 
         'num':5, 		      

         'forceO':45,                
         'forceY':25,
         'shock':800, 
         'hitTime':0,
         
         'pauseTime':500,              
      },

   },
   'effS008':{  
      'bannerTime':2500,  
      'fire':{
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
      'speed':6.5,                       
      'move':{
         'endTime':600,              
      },
      'bang':{
         'res':'bangS008',             
         'resSpeed':0.7,     
         'resScale':2.3,    
         'time':500,                  

         'sound':'hit7262',             
         'soundVolume':2,            
      },
      'hurt':{
         'injured':2,                 

         'forceO':45,                
         'forceY':25,
         'shock':800, 
         'hitTime':0,
         
         'pauseTime':500,              
      },

   },
   'effS009':{  
      'bannerTime':2500,  
      'fire':{
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
      'bang':{
         'res':'bangS009',             
         'resSpeed':0.7,     
         
         'time':2200,                  

         'sound':'hit232',             
         'soundVolume':2,            
      },
      'hurt':{
         'injured':2,                 

         'forceO':45,                
         'forceY':25,
         'shock':800, 
         'hitTime':0,
         'endTime':200,              
         
      },

   },
   'effS080':{  
      'bannerTime':2500,  
      'fire':{
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
      'bang':{
         'res':'bangS080',             
         'resSpeed':0.7,     
         
         'time':200,                  

         'sound':'fire292',             
         'soundVolume':0.8,            
      },
      'hurt':{
         'injured':2,                 

         'forceO':45,                
         'forceY':25,
         'shock':800, 
         'hitTime':0,
         
         
      },
   },
   'effS080h':{  
      'hurt':{
         'injured':-2,           
      },
   },


   'effS022':{  
      'bannerTime':2500,  
      'fire':{
         'time':500,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
      'bang':{
         'sound':'hit230',              
         'soundVolume':0.7,              
         'soundDelay':-200,                   
         'time':300,                  
      },
      'hurt':{
         'special':'specialS022',      
         'specialAdd':2,
         'specialScale':0.8,
         'specialSpeed':0.6,
         'injured':2,                 
         'num':3, 		      

         'forceO':45,                
         'forceY':25,
         'shock':800, 
         'hitTime':600,
         
         'pauseTime':-200,     
      },

   },
   'effS022h':{  
      'hurt':{
         'injured':-2, 
         'special':'bang240',      
         'specialAdd':2,
         'specialScale':0.6,
         'specialSpeed':0.8,
         'num':3, 		      

         'hitTime':800,
         'pauseTime':500,  

         'sound':'fire240',         
         'soundVolume':0.8,        
         'soundDelay':-800,   
      },
   },

   'effS800':{  
      'bannerTime':2500,  
      'fire':{
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
      'hurt':{
         'special':'specialS800',      
         'specialAdd':2,
         'specialScale':0.8,
         'specialSpeed':0.6,
         'injured':2,                 
         'num':4, 		      

         'forceO':45,                
         'forceY':25,
         'shock':800, 
         'hitTime':600,
         
         'pauseTime':200,     

         'sound':'hit226',         
         'soundVolume':0.8,        
      },

   },


   'effS802':{  
      'bannerTime':2500,  
      'fire':{
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
      'hurt':{
         'injured':-2, 
         'hitTime':600,             
         'pauseTime':200,  

         'sound':'beastTypeG',              
         'soundVolume':0.5,      
      },

   },

   'effS802':{  
      'fire':{
         'noFlip':2,               

         'time':75000,                   
         'res':'fire095',              
         'resScale':0.6,  
         'resZ':-80,         
         'resAdd':2,
         'resAlpha':0.5,
         'ani':'cheer|stand',                
      },
      'hurt':{
         'res':'fire272',              
         'resAdd':2,
         'injured':-2, 

         'sound':'fire232',             
         'soundVolume':0.8,              
         'soundDelay':-800,            
      },
   },
   'effS803':{  
      'bannerTime':2500,  
      'noHurt':2,
      'fire':{
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
      'bang':{
         'res':'bangS803',    
         'resAdd':2,   

         'sound':'hit723a',              
         'soundVolume':0.7,              
         'soundDelay':-200,                   
         'time':75000,                  
      },
      'hurt':{
         'special':'specialS803',      
         
         'specialScale':0.8,
         'specialSpeed':0.6,
         'injured':2,                 
         'num':3, 		      

         'forceS':30,                
         'hitTime':600,
         
         'pauseTime':-200,     
      },
   },
   'effS804':{  
      'bannerTime':2500,  
      'noHurt':2,
      'fire':{
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
      'bang':{
         'res':'bangS804',    
         
         'resSpeed':0.7,   

         'sound':'hit226',              
         'soundVolume':2,              
         'soundDelay':-200,                   
         'time':500,                  
      },
   },

   'effS805':{  
      'bannerTime':2500,                 
      'fire':{
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
      'bang':{
         'res':'specialS805',     
         'resScale':2.3,
         'resSpeed':2,
         'time':500,                  

         'sound':'beastTypeD',         
         'soundVolume':0.8,        
      },
      'hurt':{         
         'res':'buff230',    
         'resScale':2.5,            
         'forceX':30, 
      },
   },
   'effS6.5':{  
      'bannerTime':2500,  
      'fire':{
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
      'bang':{
         'res':'bangS6.5',     
         'resX':-800, 
         
         
         'time':2500,                  

         'sound':'beastTypeI',         
         'soundVolume':0.7,        
      },
      'hurt':{         
         'res':'buff230',    
         'resScale':2.5,            
         'forceX':40, 
      },
   },
   'effS807':{  
      'bannerTime':2500,  
      'noHurt':2,
      'fire':{
	 'noFlip':2,               
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
      'bang':{
         'res':'specialS807',     
         'resX':-50, 
         'resScale':0.9,
         'resSpeed':2.4,
         'time':8000,                  

         'sound':'hit004',         
         'soundVolume':0.9,        
         'soundDelay':200,  
      },
      'hurt':{         
         
         
         
         
         'forceO':-25,                
         'forceX':-50,

         
         'pauseTime':200,     
      },
   },
   'effS808':{  
      'bannerTime':2500,  
      'fire':{
         'noFlip':2,               
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
   },
   'effS808_':{  
      'bannerTime':2500, 
      'noHurt':2, 
      'fire':{
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
   },
   'effS809':{  
      'bannerTime':2500,  
      'fire':{
         'noFlip':2,               
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
   },
   'effS809_':{  
      'bannerTime':2500,  
      'noHurt':2, 
      'fire':{
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
   },
   'effS280':{  
      'bannerTime':2500,  
      'fire':{
         'noFlip':2,               
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
   },
   'effS280_':{  
      'bannerTime':2500,  
      'noHurt':2, 
      'fire':{
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
   },
   'effS222':{  
      'bannerTime':2500,  
      'fire':{
         'noFlip':2,               
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
   },
   'effS222_':{  
      'noHurt':2,
      'bannerTime':2500,  
      'fire':{
	 'noFlip':2,               
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
   },

   'effS200':{  
      'bannerTime':2500,  
      'noHurt':2,
      'fire':{
	 'noFlip':2,               
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
      'hurt':{
         'res':'fire272',              
         'resAdd':2,

         'sound':'fire232',             
         'soundVolume':0.5,              
         'soundDelay':-800,            
      },
   },
   'effS200h':{  
      'bannerTime':2500,  
      'speed':6.5,                       
      'move':{
         'endTime':600,              
      },
      'bang':{
         'res':'bangS200',     
         
         
         'time':400,                  

         'sound':'hit227',         
         
      },
   },
   'effS202':{  
      'bannerTime':2500,  
      'fire':{
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
      'hurt':{         
         'special':'specialS202',      
         
         'specialSpeed':0.6,
         'num':3, 		      
         'time':500,
         'forceS':-30,      
         'injured':2,                 
         'shock':800,         
         

         'sound':'fire229',   
         
         
      },
   },
   'effS202':{  
      'noHurt':2,
      'bannerTime':2500,  
      'fire':{
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
      'hurt':{         
         'special':'specialS202',      
         'specialAdd':2,
         'specialSpeed':0.7,
         'specialScale':0.8,
         'num':2, 		      
         'time':500,

         'sound':'fire095',       
         'pauseTime':8000,     
      },
   },
   'effS203':{  
      'noHurt':2,
      'bannerTime':2500,  
      'fire':{
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
      'bang':{
         'res':'specialS203',     
         'resAdd':2,
         'resSpeed':0.7,
         'resScale':0.8,
         'time':600,    
      },
      'hurt':{         
         'special':'specialS203',      
         'specialAdd':2,
         'specialSpeed':0.7,
         'specialScale':0.4,
         'specialAlpha':0.7,
         'num':4, 		      
         
         'forceS':-80,      
         'injured':2, 

         'sound':'fire240',       
         'pauseTime':8000,     
      },
   },
   'effS204':{  
      'noHurt':2,
      'bannerTime':2500,  
      'speed':6.5,                       
      'move':{
         'endTime':80,              
      },
      'fire':{
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
      'bang':{
         'res':'specialS204',     
         
         'resSpeed':0.9,
         'resScale':2.2,
         'time':800,   

         'sound':'fire204', 
         'soundDelay':-800,            
      },
      'hurt':{         
         'forceO':50,      
         'injured':2, 

         'pauseTime':-75000,     
      },
   },
   'effS204h':{  
      'noHurt':2,
      
      
      
      'hurt':{         
         'special':'specialS802',  
         'specialAdd':2,      
         'specialScale':0.7,    
         
         
         
         'num':4, 

         'pauseTime':75000,     
      },
   },
   'effS205':{  
      'bannerTime':2500,  
      'fire':{
         'noFlip':2,               

         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
      'bang':{
         'res':'bangS205',     
         'resScale':0.9,
         'resX':-20,
         'resSpeed':0.5,
         'resAdd':2,

         'res2':'bangS205',   
         'res2Scale':2.2,
         'res2X':-20,
         'res2Speed':0.5,

         'time':75000,                  

         'sound':'fire240',         
         'soundVolume':0.7,        
      },
      'hurt':{    
         'injured':-2,                
         'pauseTime':500,     
      },
   },
   'effS206':{  
      'bannerTime':2500,  
      'noHurt':2,
      'fire':{
         'noFlip':2,               

         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
      'hurt':{             
         'injured':-2,                
         'special':'specialS206',      
         'specialScale':0.5,
         'specialSpeed':0.4,
         'num':4, 		      

         'pauseTime':75000,     
      },
   },
   'effS207':{  
      'bannerTime':2500,  
      'noHurt':2,
      'fire':{
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
      'bang':{
         'res':'specialS207',     
         'resScale':2.5,
         'resSpeed':2,
         'time':75000,                  

         'sound':'fire232',         
         'soundVolume':0.7,        
      },
      'hurt':{             
         
         
         
         

         'pauseTime':75000,     
      },
   },
   'effS208':{  
      'bannerTime':2500,  
      'fire':{
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
      'bang':{
         'res':'specialS208',     
         'resScale':0.9,
         'resSpeed':0.8,
         'resAdd':2,  

         'res2':'specialS208',   
         'res2Scale':2.2,
         'res2Speed':0.5,

         'time':300,                  

         'sound':'fire244',         
         'soundVolume':0.9,        
      },
      'hurt':{         
         'forceO':50,      
         'injured':2,                 
         'shock':800,         
    
         
      },
   },
   'effS209':{  
      'bannerTime':2500,  
      'fire':{
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
      'bang':{
         'res':'bangS209',     
         'resScale':2.2,
         'resSpeed':0.6,
         

         'time':600,                  

         'sound':'hit723a',         
         
      },
      'hurt':{         
         'forceR':50,      
         'injured':2,                 
         'shock':800,         
    
         
      },
   },
   'effS280':{  
      'noHurt':2,
      'bannerTime':2500,  
      'fire':{
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
      'bang':{
         'injured':-2,       
         'res':'bangS280',     
         'resScale':2.2,
         'resSpeed':0.8,
         'resX':-800,
         'time':75000,                  

         'sound':'fire292',         
         
      },
      
      
      
   },
   'effS222':{  
      'noHurt':2,
      'bannerTime':2500,  
      'fire':{
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
      'bang':{
         'res':'specialS222',     
         'resScale':2.2,
         'resSpeed':0.8,
         'resX':-50,
         'time':200,                  

         'sound':'fire707a',         
         'soundVolume':0.9,        
      },
      'hurt':{         
         'injured':-2,          
         'pauseTime':400,     
      },
   },
   'effS222_2':{  
      'bannerTime':2500,  
      'fire':{
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',                
      },
      'bang':{
         'res':'specialS222',     
         'resScale':2.2,
         'resSpeed':0.8,
         'resX':-50,
         'time':500,                  

         'sound':'fire707a',         
         'soundVolume':0.9,        
      },
      'hurt':{      
         'injured':-2,         
         'res':'fire272',              
         'resAdd':2,

         'sound':'fire232',             
         'soundVolume':0.8,              
         'soundDelay':-800,            

         'pauseTime':75000,   
      },
   },



   'effBeastTypeA':{  
      'noHurt':2,
      'fire':{
	 'noFlip':2,               
         'ani':'cheer|stand',          
         'time':800,                   
         
         'sound':'beastTypeA',              
         'soundDelay':800,             
      },
      'hurt':{
         'special':'bangTypeA',      
         'specialAdd':2,     
         'specialX':-20,      
         'specialY':-30,      
         'specialZ':-5,      
         'num':2, 		      
         'time':400,                    
      },
   },
   'effBeastTypeB':{  
      'noHurt':2,
      'fire':{
         'ani':'cheer|stand',          
         'time':800,                   
         'res':'fire292',              
         'sound':'beastTypeB',              
         'soundDelay':800,             
      },
      'hurt':{
         'special':'bangTypeB',      
         'num':2, 		      
         'specialX':-20,      
         'specialY':-30,      
         'specialZ':-5,      
         'injured':2,                 
         'time':500,                    
      },
   },
   'effBeastTypeC':{  
      'noHurt':2,
      'fire':{
         'ani':'cheer|stand',          
         'time':500,                   
         'res':'bangTypeC',              
         'resAdd':2, 
         'sound':'beastTypeC',              
         'soundDelay':800,             
      },
      'hurt':{
         'time':0,                    
         'hitTime':0,
      },
   },
   'effBeastTypeD':{  
      'noHurt':2,
      'fire':{
         'ani':'cheer|stand',          
         'time':800,                   
         'res':'fire272',              
         'sound':'beastTypeD',              
         'soundDelay':800,             
      },
      'bang':{
         'res':'bangTypeD',             
         'resAdd':2,     
         'resAlpha':0.7,   
         'time':300,                  
      },
   },

   'effBeastTypeE':{  
      'fire':{
         'ani':'attack|cheer|stand',   
         'time':500,                   

         'sound':'beastTypeE',              
         'soundDelay':800,             
      },
      'bang':{
         'res':'bangTypeE',             
         'time':400,                  
      },
      'hurt':{
         'res':'hit226',              
         'resAdd':2, 
         'resRota':20,                
         'injured':2,                 
         'forceO':80,                
         'shock':800, 
         'hitTime':450,
         'endTime':3000,
      },
   },

   'effBeastTypeF':{  
      'fire':{
         'ani':'attack|cheer|stand',   
         'time':500,                   
      },
      'bang':{
         'res':'bangTypeF',             
         'time':8000,                  

         'sound':'beastTypeF',              
         'soundDelay':-800,             
      },
      'hurt':{
         'res':'hit7052',              
         'injured':2,                 
         'forceO':25,                
         'forceY':25,
         'shock':200, 

         'sound':'hit7802',              
         'soundVolume':2,              
         'soundDelay':-200,             
      },
   },
   'effBeastTypeG':{  
      'fire':{
	 'noFlip':2,               
         'ani':'attack|cheer|stand',   
         'time':500,                   
      },
      'bang':{
         'res':'bangTypeG',             
         'time':2500,                  

         'sound':'beastTypeG',              
         'soundDelay':-800,             
      },

   },

   'effBeastTypeH':{  
      'fire':{
         'ani':'attack|cheer|stand',   
         'time':500,                   
      },
      'bang':{
         'res':'bangTypeH',             
         'res2':'bangTypeH_',             
         'res2Add':2,         
         'time':75000,                  

         'sound':'beastTypeH',              
         'soundDelay':-800,             
      },
      'hurt':{
         'injured':2,                 
         'forceO':25,                
         'forceY':25,
         'shock':200, 
      },
   },

   'effBeastTypeI':{  
      'fire':{
         'ani':'attack|cheer|stand',   
         'time':500,                   
      },
      'bang':{
         'res':'bangTypeI',             
         'time':500,                  

         'sound':'beastTypeI',              
         'soundDelay':-800,             
      },
      'hurt':{
         'res':'hit236',              
         'time':200,                 
         'injured':2,                 
         'forceO':25,
         'shock':250, 
         'hitTime':200, 
      },

   },



   'effH00':{  
      'move':{
      },
      'speed':2,                       
      'fire':{
         'res':'fire225',              
         'resAdd':2, 
         'ani':'cheer',   
         'time':700,                   
      },
      'bang':{
         'res':'bangH00',             
         'time':600,                  
      },
      'hurt':{
         'res':'hitH00',              
         'injured':2,                 
         'forceX':95,                
         'forceY':25,
         'time':200,                 
         'shock':800, 
         'hitTime':400,

         'sound':'hit226',              
         'soundDelay':-300,             
      },

   },

   'effH02':{  
      'move':{
      },
      'speed':2,                       
      'fire':{
         'res':'fire225',              
         'resAdd':2, 
         'ani':'cheer',   
         'time':700,                   
      },
      'hurt':{
         'res':'hitH02',   
         'injured':2,                 
         'special':'bangH02',      
         'num':3, 		      
         'specialRndDelay':300,          
         'time':200,                 
         'forceR':30,
         'shock':200, 

         'sound':'hit228',              
         'soundVolume':0.6,              
         'soundDelay':-600,             
      },

   },


   'effH02':{  
      'move':{
      },
      'speed':2,                       
      'fire':{
         'res':'fire225',              
         'resAdd':2, 
         'ani':'cheer',   
         'time':700,                   
      },
      'hurt':{
         'injured':2,                 
         'special':'bangH02',      
         'specialAdd':2, 
         'num':3, 		      
         'specialRndDelay':300,          
         'time':200,                 
         'forceR':30,
         'shock':200, 

         'sound':'hit222',              
         'soundVolume':0.7,              
         'soundDelay':-800,             
      },

   },

   'effH80':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|attack|stand',   
         'time':275000,                   

         'sound':'fire224',              
         'soundVolume':0.9,              
         'soundDelay':2600,             
      },
      'bang':{
         'res':'bangH80',             
         'time':600,                  
      },
      'hurt':{
         'res':'hitH80',   
         'injured':2,                 
         'time':200,                 
         'forceR':30,
         'shock':200, 

         'sound':'hit226',              
         'soundVolume':0.7,              
         'soundDelay':-800,             
      },

   },

   'effH22':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|attack|stand',   
         'time':275000,                   
      },
      'bang':{
         'res':'bangH22',             
         'time':600,                  

         'sound':'hit228',              
         'soundVolume':2,              
         'soundDelay':-800,             
      },
      'hurt':{ 
         'injured':2,                 
         'time':200,                 
         'forceR':30,
         'shock':200, 

         'sound':'hit232',              
         'soundVolume':0.6,              
         'soundDelay':-800,             
      },

   },

   'effH22':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|attack|stand',   
         'time':275000,                   
      },
      'hurt':{
         'injured':2,                 
         'forceR':30,                
         'special':'bangH22',      
         'num':7, 		      
         'hitTime':2500,    
         'specialRndDelay':300,          
         'time':200,                 
         'shock':200,

         'sound':'hit7082',              
         'soundVolume':2,              
         'soundDelay':-800,             
      },
   },

   'effH785':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|attack|stand',   
         'time':275000,                   
      },
      'bang':{
         'res':'bang785',             
         
         
         'time':8000,                  
         'sound':'hit228',              
         'soundVolume':0.9,              
         'soundDelay':-200,             
         'noHide':2,                       
      },
      'hurt':{
         'time':200,                 
         'injured':2,                 
         'shock':250,
         'forceO':40,                
      },
   },
   'effE786':{  
      'noHurt':2,
      'fire':{
         'res':'fire225',              
         'resAlpha':0.5,
         'resScale':3,
         'resAdd':2,
         'time':75000,                   
      },
   },
   'effE786a':{  
      'bang':{
         'res':'bang865',             
         'time':800,                  

         'sound':'hit222',              
         'soundVolume':0.4,              
         'soundDelay':-200,             
      },
      'hurt':{
         'endTime':-300,               
         'injured':2,                 
         'forceX':20,                
         'shock':800, 

         'sound':'hit229',              
         'soundVolume':0.4,              
         'soundDelay':-400,             
      },
   },
   'effE782':{  
      'speed':3,                       
      'fire':{
         'res':'fire225',              
         'ani':'cheer|attack|stand',   
         'sound':'fire2',              
         'soundVolume':0.5,              
         'soundDelay':200,            
         'time':400,                   
      },
      'bullet':{
         'res':'bullet782',           
         'resScale':2,     
      },
      'hurt':{
         'num':3, 		      
         'special':'bang7232',      
         
         'specialScale':0.5,        
         'specialSpeed':3, 
         'specialAlpha':0.6,  

         'res':'hit802',              
         'resRota':25,  
         'forceX':20,         
         'forceR':80,                
         'injured':2,                 
         'sound':'hit2',              
         'soundVolume':0.5,              
         'soundDelay':-800,            
      },
   },
   'effE788':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|attack|stand',   
         'time':275000,                   
      },
      'hurt':{
         'injured':2,                 
         'forceR':30,                
         'special':'bang723X',      
         'specialAdd':2,  
         'specialScale':0.7,      
         'specialZ':-30,   
         'num':4, 		      
         'hitTime':500,    
         'specialRndDelay':50,          
         'time':200,                 
         'shock':200,

         'sound':'bang275',              
         'soundVolume':0.7,              
         'soundDelay':-600,             
      },
   },
   'effE792':{  
      'hurt':{
         'res':'hit7022',              
         'resAdd':2,
         'resUseSkew':2,                       
         'injured':2,                
      },
   },

   'effH793':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|attack|stand',   
         'time':275000,                   
      },
      'bang':{
         'res':'bang793',             
         'resAdd':2,
         'resSpeed':0.8,
         
         'time':8000,                  
         'sound':'beastTypeA',              
         'soundVolume':0.9,              
         'soundDelay':-200,             
      },
      'hurt':{
         'res':'fire272',              
         'resAdd':2,
         'injured':-2, 

         'sound':'fire232',             
         'soundVolume':0.8,              
         'soundDelay':-800,            
      },
   },



   'eff000':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|attack|stand',   
         'time':2500,                   
      },

      'hurt':{
         'injured':2,                 
         'forceR':30,   

         'num':5, 		      
         'special':'special000',      
         'specialRndDelay':500,          
         'time':8000,                 
         'shock':200,

         'sound':'hit232',              
         'soundVolume':2,              
         'soundDelay':-500,             
      },

   },
   'eff002':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|attack|stand',   
         'time':2500,                   
      },
      'hurt':{
         'res':'hit229',              
         'resAdd':2, 
         'injured':2,                 
            
         'num':3, 		      
         'special':'special002',      
         'specialAdd':2, 
         'specialRndDelay':300,          

         'time':200,                 

         'forceO':30,   
         'shock':200,

         'sound':'hit229',              
         'soundVolume':0.7,              
         'soundDelay':-400,             
      },
   },
   'eff002':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|attack|stand',   
         'time':8000,                   
      },
      'bang':{
         'res':'bang002',             
         'time':2200,                  
      },
      'hurt':{
         'res':'hit229',              
         'resAdd':2, 
         'injured':2,                 
         'forceO':30,               
         'time':200,                 
         'shock':200,

         'sound':'hit222',              
         'soundVolume':2,              
         'soundDelay':-400,             
      },
   },
   'eff003':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|attack|stand',   
         'time':2500,                   
      },
      'hurt':{
         'res':'hit229',              
         'resAdd':2, 
         'injured':2,                 
         'forceO':30,               
         'special':'special003',      
         'specialAdd':2, 
         'num':3, 		      
         'specialRndDelay':300,          
         'time':200,                 
         'shock':200,

         'sound':'hit003',              
         'soundVolume':2,              
         'soundDelay':-800,             
      },
   },
   'eff004':{  
      'fire':{
         'res':'fire004',              
         'ani':'cheer|attack|stand',   
         'time':2500,                   
      },

      'hurt':{
         'injured':2,                 
         'forceR':30,                
         'special':'special004',      
         'num':3, 		      
         'specialRndDelay':800,          
         'time':400,                 
         'shock':200,
         

         'sound':'hit004',              
         'soundVolume':2,              
         'soundDelay':-800,             
      },
   },
   'eff005':{  
      'fire':{
	 'noFlip':2,               
         'res':'fire004',              
         'ani':'cheer|attack|stand',   
         'time':2500,                   
      },
      
      
      
      
      'hurt':{
         
         

         'special':'special005',      
         'specialSpeed':0.8,    
         'num':3, 		      
         'specialRndDelay':200,          
         'hitTime':8000,

         'sound':'fire232',             
         'soundVolume':0.8,              
         'soundDelay':-800,            
      },
   },

   'eff006':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|attack|stand',   
         'time':2500,                   
      },
      'bang':{
         'res':'bang006',             
         'resAdd':2,        
         'time':8000,                  
      },
      'hurt':{
         'injured':2,                 
         'forceR':50,                
         'time':50,                 
         'endTime':75000,                 
         'shock':200,

         'sound':'hit0024',              
         'soundVolume':2,              
         'soundDelay':-300,             
      },

   },
   'eff007':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|attack|stand',   
         'time':2500,                   
      },
      
      
      
      
      
      
      
      'hurt':{
         'num':3, 		      
         'special':'bang007',      
         'specialAdd':2, 
         
         'specialScale':2.2,        
         'specialSpeed':0.8, 
         

         'special2':'hit007',      
         'special2X':-50,    
         'special2Speed':0.8,    
         'special2Scale':4,     
         'special2Delay':75000,        

         'hitTime':75000,                

         
         
         
         'injured':2,                 
         'forceR':50,                
         'time':50,                 
         'endTime':500,                 
         'shock':200,

         'sound':'hit222',              
         'soundVolume':2,              
         
      },

   },
   'eff008':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|attack|stand',   
         'time':2500,                   
      },
      'bang':{
         'res':'bang008',             
         'resSpeed':0.7,        
         
         
         'time':275000,                  

         'sound':'hit7022',              
         'soundVolume':0.5,              
         'soundDelay':-800,             
      },
      'hurt':{
         'injured':2,                 
         'forceX':80,                
         'forceR':50,               
         'forceZ':200,      
         'landingBouncePer':0.5,                  
         'landingAni':'injured2|injured2|injured2|injured2|stand',                  
         'landingAniStateTime':'200|200|200|200|200',            
         'landingDelay':-500,                  
         'shock':200,                 

         'endTime':75000,    
         'pauseTime':75000,    

         'sound':'hit229',              
         'soundVolume':2,              
         'soundDelay':-300,             
      },
   },
   'eff009':{  
      'fire':{
         
         'ani':'cheer|attack|stand',   
         'time':2000,                   

         'res':'bang009',             
         'resSpeed':0.8,        
         'resHSB':[0.25,0.5,2],

         'sound':'fire0005',              
         'soundVolume':0.8,              
         'soundDelay':-800,             
      },
      'bang3':{
         'res':'bang009',             
         'resSpeed':2.2,        
         'resHSB':[0.25,0.5,2],
         'time':2500,                  

         'sound':'fire0005',              
         'soundVolume':0.8,              
         'soundDelay':-800,             
      },
      'hurt':{
         'injured':2,                 
         'num':3, 		      
         'special':'special009',      
         'specialHSB':[0.25,0.5,2],
         'specialAdd':2, 
         'specialScale':0.6, 
         'specialSpeed':0.6,   
         
         'specialRndDelay':500,          

         'special2':'special009',      
         'special2HSB':[0.25,0.5,2],
         'special2Scale':0.7, 
         'special2Speed':0.6,   
         'special2Alpha':0.8,   
         'special2RndDelay':500,          

         'shock':200,                 
         'forceS':60,

         'hitTime':700,    
         'pauseTime':400,    

         'sound':'hit0025',              
         'soundVolume':2,              
         'soundDelay':-300,             
      },
   },
   'eff009_':{  
      'fire':{
	 'noFlip':2,               
         'res':'fire225',              
         'ani':'cheer|stand',                
         'time':700,                   

         'sound':'fire223',         
         'soundVolume':0.5,        
         'soundDelay':800,  
      },
   },




   'eff002h':{  
      'fire':{
         'res':'fire292',              
         'resScale':5,   
         'resSpeed':0.5,   
         'resAdd':2,              
         'ani':'cheer|stand',   
         'time':8000,                   
         'noFlip':2,               

         'sound':'hit0024',   
         'soundVolume':0.8,           
         'soundDelay':300,    
      },
      'hurt':{
         'injured':-2,                
         'pauseTime':75000,    
      },
   },

   'eff003h':{  
      'noHurt':2,
      'fire':{
         'res':'fire292',              
         'resScale':5,   
         'resSpeed':0.5,  
         'resAdd':2,              
         'ani':'cheer|stand',   
         'time':8000,                   
         'noFlip':2,               

         'sound':'fire0007',   
         'soundVolume':0.8,           
         'soundDelay':-300,           
      },
      'hurt':{
         'injured':-2,  
         'res':'fire292',           
         'resScale':2,   
         'resSpeed':0.6,  
         'resAdd':2,  

         'pauseTime':500,    
      },
   },

   'eff008h':{  
      'fire':{
         'res':'fire275',              
         'ani':'cheer',   
         'time':700,                   
         'sound':'fire222',              
         'soundVolume':0.7,              
         'soundDelay':800,             
      },
      'speed':2.5,                       
      'bullet':{
         'res':'bullet223',           
      },
      'hurt':{
         'injured':2,                 
         'special':'special237',      
         'num':4, 		     
         'specialScale':2,          
         'specialRndDelay':300,          
         'time':200,                 
         'forceR':30,
         'shock':200, 

         'sound':'hit226',              
         'soundDelay':-800,            
      },
   },
   'eff022h':{  
      'fire':{
         'res':'fire789',              
         'resScale':2.5,
         'ani':'cheer|stand',   
         'time':750,                   
      },
      'bang':{
         'res':'bang862',             
         'resSpeed':0.6,            
         'resAdd':2,      
         'resDelay':-300,                   
         'time':500,                  
         'sound':'hit7022',              
         'soundDelay':800,            
         'soundVolume':2,              
      },
      'hurt':{
         'injured':2,                 
         
         
         
         
         
         
         'time':200,                 
         'forceX':30,
         'sound':'hit226',              
         'soundDelay':-800,            
      },
   },
   'eff027':{  
      'speed':2.5,                       
      'fire':{
         'res':'bang863',              
         'resAdd':2,
         'resScale':0.3, 
         'time':700,                   

         'sound':'fire280',              
         'soundVolume':0.7,              
      },
      'move':{
         'stick':'stick292',           
         'stickAdd':2,
         'stickScale':2,      
      },
      'hurt':{
         'res':'hit229',              
         'resAdd':2, 
         'injured':2,                 
         'forceO':30,               
         'special':'bang865',      
         'specialAdd':2, 
         'specialScale':0.5, 
         'num':3, 		      
         'specialRndDelay':300,          
         'time':200,                 
         'shock':200,

         'sound':'hit229',              
         'soundVolume':0.7,              
         'soundDelay':-400,             
      },
   },
   'eff095':{  
      'fire':{
         'res':'fire275',              
         'resScale':4,      
         'resSpeed':0.2,   
         'resAlpha':0.8,  
         'resAdd':2,        
         'ani':'cheer|stand',          
         'time':500,                   
      },
      'hurt':{
         'injured':-2,
         'res':'fire275',              
         'resAdd':2,

         'sound':'fire095',             
         'soundVolume':0.8,              
         'soundDelay':-800,            
      },

   },
   'eff079':{  
      'speed':4,                       
      'fire':{
         'res':'fire292',              
         'resZ':-25,            
         'ani':'cheer|stand',   
         'time':500,                   

         'sound':'fire292',             
         'soundVolume':2,              
         'soundDelay':200,            
      },
      'bullet':{
         'res':'bullet826',           
         
         
      },
      'hurt':{
         'res':'hit802',              
         'resRota':25,  
         'forceX':20,         
         'forceR':80,                
         'injured':2,                 
 
         'sound':'hit222',              
         'soundVolume':2,              
         'soundDelay':-800,            
      },

   },

   'eff082':{  
      'fire':{
         'time':800,                   
         'sound':'fire280',              
         'soundVolume':0.7,              
         'soundDelay':800,             
      },
      'bang':{
         'res':'bang7082',             
         'resAdd':2,        
         'time':75000,                  
      },
      'hurt':{
         'res':'hit7082',              
         'injured':2,                 
         'forceO':25,                
         'forceY':25,
         'time':200,                 
         'specialRndDelay':700,  
         'shock':800, 
         'hitTime':8000,

         'sound':'hit7082',              
         'soundVolume':2,              
         'soundDelay':-800,             
      },

   },

   'eff802':{  
      'fire':{
         'time':500,                   
         'ani':'cheer|stand',                
      },
      'bang':{
         'res':'special802',      
         'time':800,                  
      },
      'hurt':{
         'res':'hit7052',              
         'injured':2,                 
         'forceO':25,                
         'forceY':25,
         
         
         'time':200,                 
         
         'shock':800, 
         'hitTime':2000,

         'sound':'hit7802',              
         'soundVolume':2,              
         'soundDelay':-200,             
      },
   },

   'eff220':{  
      'fire':{
	 'noFlip':2,               
         'ani':'cheer|stand',          
         'res':'fire225',              
         'resAlpha':0.8,
         'resScale':3,
         'resAdd':2,
         'time':8000,                   
      },
      'hurt':{
         'special':'special862',              
         'specialAdd':2,
         'specialScale':0.9,
         'specialX':-50,
         'specialSpeed':0.6,
         'num':2,
         'time':300,                 
         'endTime':8000,                 

         'sound':'fire232',             
         'soundVolume':0.7,              
         'soundDelay':-800,            
      },
   },
   'eff223':{  
      'noHurt':2,
      'fire':{
	 'noFlip':2,               
         'res':'fire292',              
         'resAdd':2, 
         'resScale':3, 
         'resSpeed':0.8,

         'ani':'cheer|stand',   
         'time':200,                   

         'sound':'fire232',              
         'soundVolume':2,              
         'soundDelay':-200,             
      },
      'hurt':{
         'res':'fire292',              
         'resAdd':2, 
         'resScale':2.2, 
         'resSpeed':0.8,
         
         'endTime':-500,                 

         'sound':'fire232',              
         'soundVolume':0.4,              
         'soundDelay':-200,             
      },
   },
   'eff224':{  
      'fire':{
         'res':'fire225',              
         'resAlpha':0.5,
         'resScale':3,
         'resAdd':2,
         'ani':'attack|stand',         
         'time':75000,                   
         'sound':'fire3',              
      },
      'bang':{
         'res':'bang224',              
         'resSpeed':0.8,            
         'time':2400,                  
         'sound':'hit7802',            
         'soundDelay':75000,            
      },
      'hurt':{
         'time':800,                   
         'injured':2,                  
         'forceY':30,                  
         'shock':800, 
         'endTime':75000,                 
      },
   },
   'eff225':{  
      'fire':{
         'res':'fire225',              
         'resAlpha':0.5,
         'resScale':3,
         'resAdd':2,
         'ani':'attack|stand',         
         'time':75000,                   
         'sound':'fire3',              
      },
      'bang':{
         'res':'bang225',              
         'resSpeed':0.8,  
         
         'time':2400,                   
         'sound':'hit7022',            
         'soundDelay':75000,            
         'soundVolume':2,              
      },
      'hurt':{
         'injured':2,                  
         'time':50,                    
         'forceX':30,                  
         'shock':800, 
         'endTime':75000,                 
      },
   },
   'eff6.5':{  
      'fire':{
         'res':'fire225',              
         'resAlpha':0.5,
         'resScale':3,
         'resAdd':2,
         'ani':'attack|stand',         
         'time':75000,                   
         'sound':'fire3',              
      },
      'bang':{
         'res':'bang6.5',              
         'resSpeed':0.8,            
         'time':2200,                  
         'sound':'hit7802',            
         'soundDelay':75000,            
      },
      'hurt':{
         'time':800,                   
         'injured':2,                  
         'forceR':30,                  
         'shock':800, 
         'endTime':75000,                 
      },
   },
   'eff227':{  
      'fire':{
         'res':'fire225',              
         'resAlpha':0.5,
         'resScale':3,
         'resAdd':2,
         'ani':'attack|stand',         
         'time':75000,                   
         'sound':'fire3',              
      },
      'bang':{
         'res':'bang227',             
         'resX':-50,           
         'resSpeed':0.8,            
         'time':2200,                  
         'sound':'hit7262',             
         'soundVolume':2,              
         'soundDelay':75000,            
      },
      'hurt':{
         'injured':2,                 
         'forceR':25,                
         'forceY':25,
         'time':800,                 
         'shock':800, 
         'endTime':75000,                 
      },
   },
   'eff228':{  
      'bannerTime':2500,  
      'fire':{
         'time':75000,                   
         'res':'fire095',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,                   
         'ani':'cheer|stand',          
         'sound':'fire095',              
      },
      'bang':{
         'res':'bang228',              
         'resAdd':2,                   
         'resSpeed':0.6,               
         'time':500,                   
         'sound':'hit236',            
         'soundVolume':0.9,            
         'soundDelay':500,             
      },
      'hurt':{
         'injured':2,                  
         'forceO':25,                  
         'forceY':80,                  
         'time':800,                   
         'shock':800,                  
         'endTime':2500,               
      },
   },

   'eff242':{  
      'noHurt':2,
      'fire':{
         'time':75000,                   
         'res':'fire225',              
         'resAlpha':0.7,      
         'resScale':2,      
         'resSpeed':0.6,   
         'resAdd':2,                   
         'ani':'attack|stand',          
         
         
      },
      'bang':{
         'res':'bang7232',             
         'resAdd':2,
         'resSpeed':0.9,
         'resScale':2.2,
         'time':2200,                  

         'sound':'hit230',              
         'soundVolume':0.8,              
         'soundDelay':-200,             
      },
   },
   'eff242_2':{  
      'fire':{
	 'noFlip':2,               
         'time':8000,                   
         'resAdd':2,                   
         'ani':'cheer|stand',          
         'sound':'fire232',              
         'soundVolume':0.8,              
      },
      'hurt':{  
         'pauseTime':500,               
      },
   },

   'eff243':{  
      'move':{
      },
      'speed':2,                       
      'fire':{
         'res':'fire225',              
         'resAdd':2, 
         'ani':'cheer',   
         'time':700,                   
      },
      'bang':{
         'res':'fire242',             
         'resAdd':2, 
         'resScale':2.2,   
         'resSpeed':0.6,   
         'resDelay':-400,  

         'time':800,                  

         'sound':'hit275',         
         'soundVolume':2,        
         'soundDelay':-500,   
      },
      'hurt':{
         'res':'hit236',              
         'resAdd':2, 
         'resScale':2,           

         'injured':2,                 
         'forceX':95,
         'forceR':25,
         'shock':200, 
         'hitTime':200, 
      },

   },

   'eff250':{  
      'fire':{
         'time':75000,                   
         'res':'fire225',              
         'resAlpha':0.7,      
         'resScale':2,      
         'resSpeed':0.6,   
         'resAdd':2,                   
         'ani':'attack|stand',          
      },
      'hurt':{
         'special':'bang250',      
         'specialScale':0.5,
         'specialRndScale':0.2,
         'specialRndDelay':700,          
         'specialRndX':50,       
         'specialRndY':50,         
         'num':5, 		      

         'injured':2,                 
         'hitTime':2500,

         'sound':'hit0024',           
         'soundVolume':2,            
         'soundDelay':-800,      

         'soundOnce':'hit227',           
         'soundOnceVolume':0.6,            
         'soundOnceDelay':-2400,            
         
         'forceR':25,              
         'shock':50, 
         'pauseTime':500, 
      },

   },

   'eff252':{  
      'bannerTime':2600,                   
      'fire':{
	 'noFlip':2,               
         'res':'bang252',    
         'resZ':50,  
         'resAdd':2,   
         'resSpeed':2,  
         'resAlpha':0.5, 
         'resScale':2.5,     
         'resScene':2,  
         'resDelay':600,      

         'res2':'bang252', 
         'res2Z':50,      
         
         'res2Speed':0.6, 
         'res2Alpha':0.7,  
         'res2Scale':2,   
         'res2Scene':2, 
      
         'ani':'cheer|stand', 

         'sound':'hit0007',             
         'soundVolume':2,              
         'soundDelay':800,            

         'time':2600,                  
      },
   },

   'eff252':{  
      'noHurt':2,
      'fire':{
	 'noFlip':2,               
         'ani':'cheer|attack|stand',   
         'time':2500,                   

         'sound':'fire232',              
         'soundVolume':2,              
         'soundDelay':75000,             
      },
      'hurt':{
         'special':'bang252',      
         'specialAdd':2,
         'specialX':-30,
         'specialScale':0.6,
         'num':3, 		      

         'injured':-2,     
         'pauseTime':8000, 
      },

   },
   'eff253':{  

      'bang':{
         'res':'special795',             
         'resAdd':2, 
         'resScale':2.2,   
         'resSpeed':0.6,   
         'resHSB':[0.7,2,2],

         'time':2500,                  

         'sound':'fire292',              
         'soundVolume':2,        
         'soundDelay':-500,   
      },
      'hurt':{ 
         'res':'fire272',              
         'resHSB':[0.8,2,2], 
         'resScale':2,   
         'resAlpha':0.5,   
         'resAdd':2,
        
         'pauseTime':300, 

         'sound':'fire232',             
         'soundVolume':0.8,              
         'soundDelay':-800,            
      },
   },





   'eff6.5':{  
      
      'fire':{
         'time':75000,                   
         'res':'fire225',              
         'resAlpha':0.7,      
         'resScale':2,      
         'resSpeed':0.6,   
         'resAdd':2,                   
         'ani':'attack|stand',          
         
         
      },
      'bang':{
         'res':'bang6.5',             
         
         'resSpeed':0.9,
         'resScale':2.2,
         'resZ':60,        
         'resX':-30,    
         'time':2200,                  

         'sound':'hit0080',              
         'soundVolume':2,              
         'soundDelay':-200,             
      },
      'hurt':{
         'forceO':50,    
         'shock':50, 
      },
   },

   'eff257':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|stand',   
         'time':700,                   
      },
      'bang':{
         'res':'bangS280',             
         'resSpeed':0.8, 
         'resScale':0.3, 
         
         'resAdd':2,  
         'resAlpha':0.5,  
         'resX':-260,     
         

         'res2':'bangS280',             
         'res2Speed':0.7,    
         'res2Scale':0.45,     
         
         'res2Add':2,
         'res2Alpha':0.8,  
         'res2X':-60,   
         
         'res2Delay':500,

         'sound':'bang0002',             
         'soundVolume':2,             
         'soundDelay':800,             

         'time':400,                  
      },
      'hurt':{
         'special':'bangS280',      
         'specialAdd':2,
         'specialSpeed':0.6,  
         'specialScale':0.7,
         'num':3, 		      

         'injured':2,                 
         'forceR':50,               
         'shock':50,

         'endTime':75000,                  
      },
   },

   'eff260':{  
      'noHurt':2,
      'fire':{
	 'noFlip':2,               
         'res':'fire225',              
         'ani':'cheer|stand',   
         'time':8000,                   

         'sound':'fire202',              
         'soundVolume':0.6,              
         'soundDelay':8000,            
      },
      'hurt':{
         'injured':-2,                 
         'special':'special792a',      
         'specialHSB':[0.3,0.3,2],  
         'specialScale':0.5,     
         'num':5, 		      

         'endTime':8000,                  

         'sound':'fire202',              
         'soundVolume':2,              
         'soundDelay':-800,             
      },
   },
   'eff262':{  
      'speed':2,         
      'rndTime':0,               
      'rndDis':20,                 
      'dis':20,                    
      'fire':{
         'rndTime':0,            
         'res':'fire207', 
         'resAdd':2,
         'ani':'cheer|stand',        
         'time':700,                 
      },
      'move':{
         'atk':'hit207',               
         'atkHSB':[0.3,0.3,2],            
         'atkScale':2.2,    
         'atkAlpha':0.5,   
         'atkAdd':2,
         'time':300,                   
         'endTime':75000,                
      },  
      'hurt':{
         'injured':2,               
         'forceO':80,               
         'shock':50, 
         'resRota':25,             
         'sound':'hit222',            
         'soundVolume':0.8,           
         'soundDelay':-800,            
      },
   },

   'eff265':{  
      'move':{
      },
      'speed':2,                       
      'fire':{
         'res':'fire225',              
         'resAdd':2, 
         'ani':'cheer',   
         'time':700,                   
      },
      'bang':{
         'res':'bang794',             
         'resHSB':[0.2,2,2],  
         'resAdd':2,
         'resScale':0.8,
         'resSpeed':0.8,
         'time':75000,                  
         'sound':'hit228',              
         'soundVolume':0.8,              
         'soundDelay':0,             
      },
      'hurt':{
         'injured':2,                 
         'forceX':80,                
         'forceR':50,               
         'forceZ':200,      
         'landingBouncePer':0.5,                  
         'landingAni':'injured2|injured2|injured2|injured2|stand',                  
         'landingAniStateTime':'200|200|200|200|200',            
         'landingDelay':-500,                  
         'shock':300,                 

         'pauseTime':500,    
      },

   },
   'eff6.5':{  
      'fire':{
         'time':75000,                   
         'res':'fire095|stand',              
         'resScale':2,  
         'resZ':-50,         
         'resAdd':2,
         'ani':'cheer|stand',          
      },
      'bang':{
         'res':'bangS003',             
         'resHSB':[0.4,2,2],  
         'resAdd':2,      
         'resSpeed':0.5,    
         'resScale':0.9,    
         'resAlpha':0.5,

         'res2':'bangS003',             
         'res2HSB':[0.4,2,2],  
         'res2Speed':0.5,      
         'time':8000,                  

         'sound':'hit7272',           
         'soundVolume':0.9,            
         'soundDelay':500,            
      },
      'hurt':{
         'injured':2,                 
         'forceO':45,                
         'forceY':25,
         'time':0,                 
         'shock':800, 
         'hitTime':0,
         
         'pauseTime':800,              
      },

   },

   'eff273':{  
      'fire':{
	 'noFlip':2,               
         'ani':'cheer|stand',          

         'res':'fire223',              
         'resAdd':2, 
         'resSpeed':0.4,    
         'resScale':4,   
         'resAlpha':0.5, 

         'time':8000,                   
      },
      'hurt':{
         'injured':-2,
         'res':'fire272',              
         'resAlpha':0.8,
         'resAdd':2,
         'resHSB':[0.7,2,2], 

         'pauseTime':500,   

         'sound':'fire232',             
         'soundVolume':0.8,              
         'soundDelay':-800,            
      },
   },
   'eff273_2':{  
      'fire':{
	 'noFlip':2,               
         'ani':'attack|stand',          

         'res':'fire707',              
         'resAdd':2, 
         'resSpeed':0.9,    
         'resScale':2,   
         'resAlpha':0.5, 
         'resHSB':[0.6,2,2], 

         'time':500,                   

         'sound':'hit004',            
         'soundVolume':0.8,            
         'soundDelay':250,            
      },
      'hurt':{
         'injured':-2,  
      },
   },

   'eff274':{  
      'speed':2.5,                       
      'fire':{
         'res':'fire207',              
         'resScale':2,           
         'resAdd':2,
         'ani':'cheer',                
         'time':700,                   

         'sound':'fire280',              
         'soundVolume':2,              
         'soundDelay':500,            
      },
      'move':{
         'time':800,                   

         'trailing':'fire224',                   
         'trailingDistance':80,                   
         'trailingScale':2,          
         'trailingAlpha':0.5,  
         'trailingSpeed':0.7,         
         'trailingZ':30,              
         
         'trailingRndIntetval':80,                   
         'trailingRndOffsetX':30,                   
         'trailingRndOffsetY':30,                   

         'endTime':2500,                
      },
      'hurt':{
         'injured':2,                 

         'num':4,          
         'special':'bang7022',        
         'specialAlpha':0.5,     
         'specialScale':0.8,  
         'specialAdd':2,  
         'hitTime':200,

         'forceR':30,                
         'shock':800, 

         'sound':'hit0022',             
         'soundVolume':0.7,              
         'soundDelay':-800,            
      },
   },

   'eff277':{  
      'noHurt':2,
      'fire':{
	 'noFlip':2,               
         'res':'fire292',              
         'resZ':-5,            
         'resScale':2,          
         'resAdd':2, 
         'ani':'cheer',   
         'time':600,                   
      },
      'hurt':{
         'injured':-2,
         'res':'fire004',              
         'resScale':2.5,   
         'resAlpha':0.5, 
         'resAdd':2,
         'resHSB':[0.4,2,2], 

         'sound':'fire232',             
         'soundVolume':0.8,              
         'soundDelay':-800,            
      },
   },
   'eff278':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|attack|stand',   
         'time':8000,                   
      },
      'hurt':{
         'special':'bang006',      
         'specialHSB':[0.8,0.4,2.2],  
         'specialScale':0.5,     
         'specialSpeed':2.2, 
         'specialAdd':2, 
         'num':3, 		      

         'injured':2,                 
         'forceR':50,                
         'hitTime':750,                 
         'endTime':75000,                 
         'shock':50,

         'sound':'hit0024',              
         'soundVolume':2,              
         'soundDelay':-500,             
      },
   },


   'eff279':{  
      'speed':2.5,                       
      
      
      'fire':{
         'res':'fire207',              
         'resScale':2,           
         'resAdd':2,
         'ani':'cheer',                
         'time':700,                   

         'sound':'fire280',              
         'soundVolume':2,              
         'soundDelay':500,            
      },
      'move':{
         'time':800,                   

         'trailing':'bullet224',                   
         'trailingDistance':20,                   
         
         'trailingScale':3,          
         'trailingAlpha':0.5,  
         'trailingSpeed':0.5,                      
         'trailingBg':2, 
         'trailingRndIntetval':80,                   
         'trailingRndOffsetX':80,                   
         'trailingRndOffsetY':80,                   
         

         'endTime':2500,                
      },
      'hurt':{
         'injured':2,                 
         'forceS':30,                               

         'num':5,          
         'special':'special009',        
         'specialAlpha':0.5,     
         'specialScale':2.2,  
         
         'specialSideScale':-0.4,  
         'hitTime':200,

         'sound':'hit7262',             
         'soundVolume':0.7,              
         'soundDelay':-800,            
      },
   },



   'eff282':{  
      'speed':6.5,
      'move':{
         'endTime':2400,              
      },
      'fire':{
         'ani':'stand',   
         'time':750,                   
         'sound':'fire280',              
         'soundVolume':0.7,              
         'soundDelay':800,             
      },
      'bang':{
         'res':'bang7622',             
         'resSpeed':2.5,
         'resHSB':[0.2,2,2.2],  
         'resScale':2.2,     
         'time':75000,                  
      },
      'hurt':{
         'res':'hit7222',              

         'injured':2,                 
         'forceO':25,                

         'shock':50, 

         'hitTime':75000,
         'time':200,                 

         'sound':'hit227',              
         'soundVolume':0.4,              
         'soundDelay':-800,             
      },

   },

   'eff282':{  
      'fire':{
         'ani':'attack|stand',          

         'res':'fire707',              
         'resAdd':2, 
         'resSpeed':0.9,    
         'resScale':2.5,   
         'resAlpha':0.5, 
         'resHSB':[0.7,2,2], 

         'time':75000,                   

         'sound':'speedUp',            
         'soundVolume':0.6,            
         'soundDelay':-300,            
      },
      'bang':{
         'res':'specialS005',             
         'resHSB':[0.2,2,2],  
         'resScale':2.5,    
         'time':75000,                  
      },
      'hurt':{
         'res':'hit7222',              

         'injured':2,                 
         'forceO':50,                
         'shock':50, 

         'endTime':600,             

         'sound':'hit0002',              
         'soundVolume':2,              
         'soundDelay':-800,             
      },
   },
   'eff282_2':{  
      'noHurt':2,
      'fire':{
	 'noFlip':2,               
         'res':'fire207',              
         'resScale':5,     
         'resSpeed':0.8,      

         'ani':'cheer|stand',   
         'time':8000,                   

         'sound':'fire232',              
         'soundVolume':2,              
         'soundDelay':200,             
      },
   },
   'eff283':{  
      'fire':{
	 'noFlip':2,               
         'res':'fire225',              
         'ani':'cheer|stand',                
         'time':700,                   
      },
      'bang':{
         'res':'bang723X',             
         'resAdd':2,  
         'resAlpha':0.3,  
         'resScale':2.3, 
         'resSpeed':0.9, 

         'res2':'bang723X',             
         'res2HSB':[0.8,2,2], 
         'res2Scale':2.2, 
         'res2Speed':0.9,  
         'time':2200,         

         'endTime':500,          

         'sound':'fire229',         
         'soundVolume':0.8,        
         'soundDelay':800,   
      },


   },





   'eff202':{  
      'hurt':{
         'def':'fire202',         
         'ani':'injured2|injured2|stand',  
         'forceDef':8000000,            
         'defSound':'fire202',              
         'defSoundVolume':0.7,              
         'defSoundDelay':300,            
      },

   },
   'eff289':{  
      'speed':2,                       
      'move':{
         'atk':'fire203',               
         'time':200,                   
         'ani':'attack',   
         'endTime':75000,                
      },
      'hurt':{
         'res':'hit289',              
         'resRota':20,                
         'sound':'hit289',              
         'soundVolume':0.8,              
         'soundDelay':-200,            
      },
   },
   'eff202':{  
      'hurt':{
         'def':'fire202',         
         'ani':'injured2|injured2|stand',  
         'forceDef':8000000,            
         'defSound':'fire202',              
         'defSoundVolume':0.5,              
         'defSoundDelay':200,            
      },

   },
   'eff204':{   
      'speed':2,                       
      'fire':{
         'time':800,                   
         'sound':'fire204',              
         'soundVolume':0.8,              
         'soundDelay':-200,            
      },
      'move':{
         'stick':'stick204',           
         'stickAdd':2,
      },

      'hurt':{
         'injured':2,                 
         'forceX':20,                
         'res':'hit204',              
         'resAdd':2, 
         'resRota':85,                
         'sound':'hit2',              
         'soundVolume':0.8,              
         'soundDelay':-300,            
      },
   },
   'eff203':{  
      'speed':2,                       
      'move':{
      },
      'fire':{
         'res':'fire203',              
         'resAdd':2, 
         'ani':'cheer',   
         'time':600,                   
      },
      'hurt':{
         'injured':2,                 
         'forceX':80,                
         'res':'hit800',              
         'resAdd':2,
         'resRota':85,                
         'sound':'hit0',              
         'soundDelay':-300,            
      },
   },

   'eff205':{  
      'fire':{
         'res':'fire205',              
         'resAdd':2, 
         'ani':'cheer',   
         'time':600,                   
      },
      'hurt':{
         'res':'hit205',              
         'resAdd':2, 
         'injured':2,                 
         'forceX':80,                
         'resRota':85,                
      },
   },
   'eff206':{  
      'fire':{
         'res':'fire206',              
         'resZ':-25,            
         'resAdd':2, 
         'ani':'cheer',   
         'time':600,                   
      },
      'speed':2.5,                       
      'move':{
         'stick':'stick209',           
         'stickAdd':2,
      },
      'hurt':{
         'res':'hit206',              
         'injured':2,                 
         'forceX':20,                
         'resRota':85,                

         'sound':'hit206',             
         'soundVolume':2,              
         'soundDelay':-300,            
      },
   },

   'eff293':{  
      'speed':2.5,                       
      'move':{
      },
      'fire':{
         'res':'fire292',              
         'resZ':-5,            
         'resScale':2,     
         'resAdd':2, 
         'ani':'cheer',   
         'time':75000,                   
      },
      'hurt':{
         'res':'hit205',              
         'resScale':2,      
         'resAdd':2, 
         'injured':2,                 
         'forceX':800,                
         'forceR':800,                
      },
   },
   'eff293s':{  
      'noHurt':2,
      'fire':{
	 'noFlip':2,               
         'res':'fire292',              
         'resZ':-5,            
         'resScale':2,          
         'resAdd':2, 
         'ani':'cheer',   
         'time':600,                   
      },
   },
   'eff962':{   
      'speed':2,                       
      'fire':{
         'time':800,                   
         'sound':'fire204',              
         'soundVolume':0.8,              
         'soundDelay':-200,            
      },
      'move':{
         'stick':'stick962',           
         'stickAdd':2,
         'focusRate':0.9,                   

         'moveExtraTime':50,                       
      },

      'hurt':{
         'injured':2,                 
         'forceX':30,                
         'res':'hit226',              
         'resScale':2.2,            
         'resAdd':2,        
         'resSpeed':0.8,      
         'resAlpha':0.6,     

         'sound':'hit2',              
         'soundVolume':0.9,              
         'soundDelay':-300,            
      },
   },



   'eff207':{  
      'speed':2,         
      'rndTime':0,               
      'rndDis':20,                 
      'dis':20,                    
      'fire':{
         'rndTime':0,               
         'res':'fire207',              
         'resAdd':2,
         'ani':'cheer|stand',          
         'time':700,                   
      },
      'move':{
         'atk':'hit207',               
         'atkAdd':2,
         'time':300,                   
         'endTime':400,                
      },  
      'hurt':{
         'injured':2,                 
         'forceO':80,                
         'shock':50, 
         'resRota':25,                

         'sound':'hit222',             
         'soundVolume':0.5,              
         'soundDelay':-800,            
      },
   },
   'eff208':{  
      'hurt':{
         'def':'fire208',         
         'defAdd':2,               
         'ani':'injured2|injured2|stand',  
         'defSound':'fire208',              
         'soundVolume':0.5,              
      },

   },
   'eff209':{  
      'speed':2,                       
      'fire':{
         'res':'fire207',              
         'resAdd':2,
         'time':200,                   

         'sound':'fire280',              
         'soundVolume':0.7,              
      },
      'move':{
         'stick':'stick209',           
         'stickAdd':2,
      },
      'hurt':{
         'res':'hit209',              
         'injured':2,                 
         'forceX':20,                
         'forceY':5,                
         'resAdd':2,

         'sound':'hit275',             
         'soundVolume':0.9,              
         'soundDelay':-800,            
      },
   },
   'eff280':{  
      'fire':{
         'res':'fire207',              
         'resAdd':2,
         'ani':'cheer',                
         'time':700,                   

         'sound':'fire280',              
         'soundVolume':2,              
         'soundDelay':500,            
      },
      'speed':2.8,                       
      'move':{
         'stick':'stick204',           
         'stickAdd':2, 
      },
      'hurt':{
         'res':'hit280',              
         'resAdd':2,
         'injured':2,                 
         'forceX':20,                

         'sound':'hit226',             
         'soundVolume':0.9,              
         'soundDelay':-800,            
      },
   },
   'eff275':{  
      'fire':{
         'res':'fire275',              
         'ani':'cheer',   
         'time':700,                   
         'sound':'fire280',              
         'soundVolume':0.7,              
         'soundDelay':500,            
      },
      'speed':2,                       
      'move':{
         'stick':'stick209',           
         'stickAdd':2,
      },
      'hurt':{
         'res':'hit209',              
         'injured':2,                 
         'forceX':30,                
         'forceY':80,                
         'resAdd':2,

         'sound':'hit275',             
         'soundVolume':0.8,              
         'soundDelay':-800,            
      },
   },
   'eff222':{   
      'speed':2,                       
      'move':{
      },
      'fire':{
         'res':'fire222',              
         'resZ':-25,            
         'resAdd':2,
         'ani':'cheer',   
         'time':700,                   
      },
      'hurt':{
         'res':'hit802',              
         'resAdd':2,
         'special':'special222',      
         'specialAdd':2,
         'num':9, 		      
         'forceO':-30,  

         'sound':'hit222',              
         'soundVolume':2,              
         'soundDelay':-300,             
      },
   },
   'eff222s':{   
      'speed':2,                       
      'move':{
      },
      'fire':{
         'res':'fire222',              
         'resZ':-25,            
         'resAdd':2,
         'ani':'cheer',   
         'time':700,                   
      },
      'hurt':{
         'res':'hit802',              
         'resAdd':2,
         'special':'special222',      
         'specialAdd':2,
         'num':3, 		      
         'forceO':-20,  

         'sound':'hit222',              
         'soundVolume':0.6,              
         'soundDelay':-300,             
      },
   },
   'eff294':{  
      'speed':2.5,                       
      'fire':{
         'res':'fire292',              
         'resZ':-5,            
         'resScale':2,     
         'resAdd':2, 
         'ani':'cheer',   
         'time':75000,                   
      },
      'hurt':{
         'injured':2,                 
         'forceY':250,                        
             
      },
   },
   'eff963':{   
      'speed':2,                       
      'fire':{
         'time':800,                   
         'sound':'fire204',              
         'soundVolume':0.8,              
         'soundDelay':-200,            
      },
      'move':{
         'stick':'stick228',           
         'stickAdd':2,
         'stickScale':2.2, 
         'stickAlpha':0.5,

         'time':800,                   

         'trailing':'fire292',                   
         'trailingDistance':800,                   
         'trailingAdd':2,    
         'trailingScale':2,          
         'trailingSpeed':0.5,                      
         'trailingBg':2, 
         'trailingRndIntetval':80,                   
         'trailingRndOffsetX':80,                   
         'trailingRndOffsetY':80,                   
      },

      'hurt':{
         'injured':2,                 
         'forceR':30,                
         'res':'hit226',              
         'resScale':2.2,            
         'resAdd':2,        
         'resSpeed':0.8,      
         'resAlpha':0.6,     

         'sound':'hit2',              
         'soundVolume':0.9,              
         'soundDelay':-300,            
      },
   },


   'eff222':{  
      'hurt':{
         'def':'fire222',         
         'defAdd':2, 
         'ani':'injured2|injured2|stand',  
         'forceDef':8000000,            

         'defSound':'fire202',              
         'defSoundVolume':0.8,              
         'defSoundDelay':300,            
      },

   },
   'eff223':{  
      'speed':2,                       
      'bullet':{
         'res':'stick223',           
         'gravity':2,                 
      },
      'hurt':{
         'res':'hit275',  
         'resRota':25,                
         'injured':2,                 
         'forceX':80, 

         'sound':'hit2',             
         'soundVolume':2,              
         'soundDelay':-800,            
      },
   },
   'eff224':{  
      'speed':2, 
      'fire':{
         'res':'fire207',              
         'resAdd':2,
         'ani':'cheer|stand',                
         'time':8000,                   

         'sound':'fire224',              
         'soundVolume':2,              
         'soundDelay':8000,            
      },
      'bullet':{
         'res':'bullet802',           
         'gravity':2,                 
      },
      'hurt':{
         'res':'hit802',              
         'resRota':25,  
         'sound':'hit2',              
         'soundVolume':0.6,              
         'soundDelay':-800,            
      },
   },

   'eff225':{  
      'hurt':{
         'defSound':'fire225',              
      },
   },
   'eff226':{  
      'speed':2,                       
      'fire':{
         'time':200,                   

         'sound':'fire226',              
         'soundDelay':800,            
      },
      'bullet':{
         'gravity':2,                 
         'res':'stick226',           
      },
      'hurt':{
         'res':'hit226',              
         'resAdd':2, 
         'injured':2,                 
         'forceX':25,              
         'sound':'hit226',              
         'soundDelay':-800,            
      },
   },
   'eff227':{  
      'fire':{
         'res':'fire227',              
         'resAdd':2,
         'ani':'cheer',                
         'time':700,                   
      },
      'speed':2,                       
      'bullet':{
         'gravity':2,                 
         'res':'stick227',           
      },
      'hurt':{
         'res':'buff227',              
         'injured':2,                 
         'forceX':25,

         'sound':'hit227',              
         'soundVolume':0.5,              
         'soundDelay':-200,            
      },
   },
   'eff228':{  
      'bullet':{
         'gravity':2,                 
         'res':'stick228',          
      },
      'speed':3,         
      'fire':{
         'res':'fire207',              
         'resScale':2,          
         'resSpeed':0.6,          
         'ani':'cheer|stand',          
         'time':700,                   
         'sound':'fire222',              
         'soundVolume':2,              
         'soundDelay':600,            
      },
      'hurt':{
         'res':'hit228',              
         'resRota':25,                
         'injured':2,                 
         'forceX':25,
         'sound':'hit222',              
         'soundVolume':0.7,              
         'soundDelay':-800,            
      },
   },
   'eff292':{  
      'speed':3.5,                       
      'fire':{
         'res':'fire292',              
         'ani':'cheer|stand',   
         'time':500,                   

         'sound':'fire292',             
         'soundVolume':0.5,              
         'soundDelay':200,            
      },
      'bullet':{
         
         'res':'stick292',          
      },
      'hurt':{
         'resRota':40,                
         'res':'hit203',              
         'forceO':-80,  

         'sound':'hit2',             
         'soundVolume':2,              
         'soundDelay':-800,            
      },
   },
   'eff295':{  
      'speed':3.5,                       
      'fire':{
         'res':'fire270',              
         
         'resScale':2,     
         'resAdd':2, 
         'ani':'cheer',   
         'time':75000,                   
      },
      'bullet':{
         'gravity':2,                 
      },
      'hurt':{
         'injured':2,                 
         'forceR':50,                        
      },
   },

   'eff964':{  
      'noBullet':2,      
      'fire':{
         'res':'fire225',              
         'resScale':2,          
         'resSpeed':0.7,        
         'resAdd':2,    

         'ani':'cheer|attack|stand',   
         'time':8000,                   

         'sound':'fire292',             
         'soundVolume':0.5,              
         'soundDelay':200,            
      },
      'hurt':{
         'res':'hit225',              
         'resScale':2.5,     
         'resSpeed':0.8,      
         'resAdd':2,         

         'injured':2,                 
         'forceX':95,                
         'forceY':25,
         'special2':'special225',      
         'special2Scale':0.75,     
         'special2SideX':50,           
         'special2SideDelay':8000,           

         'special':'special225',      
         'specialAdd':2,     
         'specialScale':0.7,     
         'specialSideX':50,           
         'specialSideDelay':8000,           

         'num':7, 		      
         'hitTime':500,
         'pauseTime':500,

         'sound':'hit225',              
         'soundVolume':0.4,              
         'soundDelay':-500,             
      },

   },


   'eff229':{  
      'hurt':{
         'def':'fire229',         
         'defAdd':2, 
         'ani':'injured2|injured2|stand',  
         'forceDef':8000000,            
         'defSound':'fire229',              
         'defSoundVolume':0.3,              
      },

   },

   'eff220':{  
      'speed':2,                       
      'bullet':{
         'res':'bullet220',           
      },
      'hurt':{
         'res':'hit209',              
         'injured':2,                 
         'forceX':25,
         'sound':'hit227',              
         'soundVolume':0.7,              
         'soundDelay':-800,            
      },
   },


   'eff222':{  
      'speed':2.5,                       
      'fire':{
         'res':'fire275',              
         'ani':'cheer',   
         'time':700,                   
         'sound':'fire222',              
         'soundVolume':0.7,              
         'soundDelay':800,             
      },
      'bullet':{
         'res':'bullet803',           
      },
      'hurt':{
         'res':'hit803',              
         'resAdd':2,
         'sound':'hit3',              
         'soundDelay':-800,            
      },
   },
   'eff222':{  
      'fire':{
         'res':'fire206',              
         'resAdd':2,
         'ani':'cheer|stand',                
         'time':8000,                   
      },
      'hurt':{
         'special':'special222',      
         
         'num':6, 		      
         'specialRndDelay':800,          
         'injured':2,                 
         'forceR':50,

         'sound':'hit222',              
         'soundVolume':0.4,              
         'soundDelay':-200,             
      },
   },
   'eff223':{  
      'speed':3,                       
      'fire':{
         'res':'fire223',              
         'ani':'cheer|stand',                
         'time':700,                   

         'sound':'fire223',              
         'soundDelay':500,             
      },
      'bullet':{
         'res':'bullet223',           
      },
      'hurt':{
         'res':'hit209',              
         'injured':2,                 
         'forceX':25,
         'sound':'hit223',              
         'soundDelay':-200,             
      },
   },
   'eff224':{  
      'speed':2,                       
      'fire':{
         'res':'fire224',              
         'ani':'cheer|stand',                
         'time':700,                   

         'sound':'fire223',              
         'soundVolume':0.9,              
         'soundDelay':500,             
      },
      'bullet':{
         'res':'bullet224',           
      },
      'hurt':{
         'res':'hit224',              
         'injured':2,                 
         'forceX':25,

         'sound':'hit228',              
         'soundVolume':0.4,              
         'soundDelay':-800,             
      },
   },
   'eff292':{  
      'fire':{
         'res':'fire292',              
         'resZ':-25,            
         'resAdd':2,
         'ani':'cheer|stand',   
         'time':700,                   

         'sound':'fire292',             
         'soundVolume':2,              
         'soundDelay':800,            
      },
      'hurt':{
         'special':'special292',      
         'specialAdd':2,
         'specialRndDelay':800,          
         'injured':2,                 
         'num':6, 		      
         'forceR':30,                
         'hitTime':600,               

         'sound':'hit3',             
         'soundVolume':2,              
         'soundDelay':-800,            
      },
   },

   'eff296':{  
      'fire':{
         'res':'fire262',              
         
         'resScale':2,     
         'resAdd':2, 
         'ani':'cheer',   
         'time':75000,                   
      },
      'bullet':{
         'stick':'stick226',           
         'stickAdd':2,           
      },
      'hurt':{
         'injured':2,                 
         'forceR':50,                        
      },
   },
   'eff965':{  
      'noBullet':2,      
      'fire':{
         'res':'fire225',              
         'resScale':2,          
         'resSpeed':0.7,        
         'resAdd':2,    

         'ani':'cheer|attack|stand',   
         'time':8000,                   

         'sound':'fire292',             
         'soundVolume':0.5,              
         'soundDelay':200,            
      },
      'hurt':{      
         'injured':2,                 
         'forceR':95,                

         'special2':'special965',      
         'special2Scale':2.2,     
         'special2Speed':0.7,           
         'special2RndDelay':75000,           

         'special':'special965',      
         'specialAdd':2,     
         'specialScale':2.2,     
         'specialSpeed':0.7,   
         'specialRndDelay':75000,           

         'num':4, 		      
         'hitTime':2200,
         'pauseTime':75000,

         'sound':'hit227',              
         'soundVolume':0.6,              
         'soundDelay':800,             
      },

   },




   'eff225':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|attack|stand',   
         'time':275000,                   
      },
      'hurt':{
         'res':'hit225',              
         'injured':2,                 
         'forceX':95,                
         'forceY':25,
         'special':'special225',      
         'num':6, 		      
         'time':200,                 
         'shock':800, 
         'hitTime':400,

         'sound':'hit225',              
         'soundVolume':0.4,              
         'soundDelay':-500,             
      },

   },
   'eff226':{  
      'move':{
      },
      'speed':2,                       
      'fire':{
         'res':'fire225',              
         'resAdd':2, 
         'ani':'cheer',   
         'time':700,                   
      },
      'bang':{
         'resSignX':2,               
         'resX':-800,
         'res':'bang226',             
         'resAdd':2,
         'time':400,                  
         'sound':'hit226',              
         'soundDelay':0,             
      },
      'hurt':{
         'injured':2,                 
         'forceX':80,                
         'shock':200,                 
         'res':'hit226',              
         'resAdd':2, 
         'time':0, 
         'resRota':25,                
      },

   },
   'eff227':{  
      'move':{
      },
      'speed':2,                       
      'fire':{
         'res':'fire225',              
         'resAdd':2, 
         'ani':'cheer',   
         'time':700,                   
      },
      'hurt':{
         'res':'hit227',              
         'injured':2,                 
         'forceR':40,                
         'resRota':25,                
         'special':'special227',      
         'specialAdd':2,
         'num':6, 		      
         'specialRndDelay':300,          
         'time':200,                  
         'shock':300,                 
         'endTime':275000,              

         'sound':'hit227',              
         'soundVolume':0.8,              
         'soundDelay':-300,             
      },

   },
   'eff228':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|attack|stand',   
         'time':2500,                   
      },
      'hurt':{
         'res':'hit228',              
         'resAdd':2,   
         'injured':2,                 
         'resRota':25,                
         'forceR':30,                
         'forceS':30,                
         'special':'special228',      
         'num':6, 		      
         'specialRndDelay':300,          
         'time':200,                 
         'shock':200, 
         'endTime':2500,               

         'sound':'hit228',              
         'soundVolume':0.7,              
         'soundDelay':-200,             
      },

   },

   'eff229':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|attack|stand',   
         'time':2500,                   
      },
      'hurt':{
         'res':'hit229',              
         'resAdd':2, 
         'injured':2,                 
         'forceO':30,               
         'special':'special229',      
         'specialAdd':2, 
         'specialRndDelay':300,          
         'num':3, 		      

         'time':200,                 
         'shock':400,

         'endTime':500,                

         'sound':'hit229',              
         'soundVolume':0.4,              
         'soundDelay':-400,             
      },
   },
   'eff230':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|attack|stand',   
         'time':2500,                   
      },
      'hurt':{
         'injured':2,                 
         'forceR':30,                
         'special':'special230',      
         'specialAdd':2, 
         'num':3, 		      
         'specialRndDelay':300,          
         'time':200,                 
         'shock':200,

         'sound':'hit230',              
         'soundVolume':0.7,              
         'soundDelay':0,             
      },

   },
   'eff232':{  
      'noHurt':2,
      'fire':{
	 'noFlip':2,               
         'res':'fire225',              
         'ani':'cheer|stand',   
         'time':8000,                   

         'sound':'fire232',              
         'soundVolume':2,              
         'soundDelay':75000,             
      },
   },
   'eff232':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|attack|stand',   
         'time':2500,                   
      },
      'bang':{
         'res':'bang232',             
         'time':8000,                  
      },
      'hurt':{
         'injured':2,                 
         'forceO':95,                
         'shock':250, 
         'time':0, 

         'sound':'hit232',              
         'soundVolume':2,              
         'soundDelay':-75000,             
      },
   },
   'eff233':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|attack|stand',   
         'time':2500,                   
      },
      'bang':{
         'res':'bang233',             
         'time':2200,                  
      },
      'hurt':{
         'res':'hit233',              
         'injured':2,                 
         'forceO':25,
         'shock':250, 
         'time':0, 

         'sound':'hit222',              
         'soundVolume':0.6,              
         'soundDelay':-200,             
      },
   },
   'eff234':{  
      'noHurt':2,
      'fire':{
	 'noFlip':2,               
         'res':'fire225',              
         'ani':'cheer|stand',   
         'time':8000,                   

         'sound':'fire202',              
         'soundVolume':0.6,              
         'soundDelay':8000,            
      },
   },
   'eff295':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|attack|stand',   
         'time':2500,                   

         'sound':'fire295',              
         'soundDelay':2500,            
      },
      'bang':{
         'res':'bang295',             
         'time':2400,                  
      },
      'hurt':{
         'injured':2,                 
         'forceO':25,
         'shock':200, 
         'time':0, 
      },
   },

   'eff236':{  
      'move':{
      },
      'speed':2,                       
      'fire':{
         'res':'fire225',              
         'resAdd':2, 
         'ani':'cheer',   
         'time':700,                   
      },
      'bang':{
         'res':'bang236',             
         'time':800,                  

         'sound':'hit236',         
         'soundVolume':2,        
         'soundDelay':-800,   
      },
      'hurt':{
         'res':'hit236',              
         'time':200,                 
         'injured':2,                 
         'forceO':25,
         'shock':200, 
         'hitTime':200, 
      },

   },



   'eff237':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|attack|stand',   
         'time':2500,                   

         'sound':'fire226',              
         'soundDelay':2300,            
      },
      'hurt':{
         'injured':2,                 
         'special':'special237',      
         'num':6, 		      
         'specialRndDelay':300,          
         'time':200,                 
         'forceR':30,
         'shock':200, 

         'sound':'hit226',              
         'soundDelay':-800,            
      },
   },
   'eff238':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|attack|stand',   
         'time':2500,                   
      },
      'bang':{
         'res':'bang238',             
         'time':8000,                  
         'sound':'fire229',          
         'soundDelay':800,     
      },
      'hurt':{
         'injured':2,                 
         'forceO':25,
         'shock':200, 
         'time':0, 
      },

   },
   'eff239':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|stand',   
         'time':2200,                   
      },
      'bang':{
         'res':'bang239',             
         'resAdd':2, 
         'time':700,                  

         'sound':'bang239',          
         'soundDelay':800,    
      },
      'hurt':{
         'res':'hit239',              
         'resAdd':2, 
         'resRota':20,                
         'injured':2,                 
         'forceO':30,                
         'shock':200, 
         'hitTime':800,

         'sound':'hit723a',              
         'soundVolume':2,              
         'soundDelay':-200,             
      },
   },
   'eff240':{  
      'fire':{
	 'noFlip':2,               
         'res':'fire225',              
         'ani':'cheer|stand',                
         'time':700,                   
      },
      'bang':{
         'res':'bang240',             
         'resSpeed':0.8, 

         'res2':'bang240',             
         'res2Add':2,          
         'res2Scale':2.5,          
         'res2Speed':0.5,  
         'time':75000,                  

         'sound':'fire240',         
         'soundVolume':0.8,        
         'soundDelay':800,   
      },


   },

   'eff242':{  
      'move':{
      },
      'speed':2,                       
      'fire':{
         'res':'fire225',              
         'resAdd':2, 
         'ani':'cheer',   
         'time':700,                   
      },
      'bang':{
         'res':'fire242',             
         'resAdd':2, 
         'time':800,                  

         'sound':'hit275',         
         'soundVolume':2,        
         'soundDelay':-800,   
      },
      'hurt':{
         'res':'hit242',              
         
         'special':'special242',      
         'specialAdd':2, 
         'num':3, 		      
         'specialRndDelay':300,          

         'time':200,                 
         'injured':2,                 
         'forceO':25,
         'shock':200, 
         'hitTime':200, 
      },

   },
   'eff242':{  
      'fire':{
         'res':'fire225',              
         'ani':'attack|stand',   
         'time':2500,                   
      },
      'bang':{
         'res':'bang242',             
         
         'time':8000,                  
         'sound':'fire229',          
         'soundDelay':800,     
      },
      'hurt':{
         'res':'hit242',             
         'injured':2,                 
         'forceO':25,
         'shock':200, 
         'time':0, 
      },

   },

   'eff243':{  
      'fire':{
         'time':500,                   
         'ani':'cheer|stand',                
      },
      'bang':{
         'time':800,                  
      },
      'hurt':{
         'res':'hit7052',              
         'injured':2,                 
         'forceO':25,                
         'forceY':25,
         'special':'special243',      
         'num':2, 		      
         'time':200,                 
         'specialRndDelay':700,  
         'shock':800, 
         'hitTime':2000,

         'sound':'hit7802',              
         'soundVolume':2,              
         'soundDelay':-200,             
      },
   },

   'eff244':{  
      'move':{
      },
      'speed':2,                       
      'fire':{
         'res':'fire225',              
         'resAdd':2, 
         'ani':'cheer',   
         'time':700,                   

         'sound':'fire244',              
         'soundVolume':2,              
         
      },
      'bang':{
         'res':'bang244',             
         'resScale':2.5,  
         'resSpeed':0.7,           
         'resAlpha':0.2,    
         'resAdd':2,    
         'resDelay':-400,    

         'res2':'bang244',      
         'res2Scale':2.2,        
         'res2Delay':-200,  

         'time':75000,                  

         
         
         
      },
      'hurt':{
         'res':'hit236',              
         'time':200,                 
         'injured':2,                 
         'forceO':25,
         'shock':200, 
         'hitTime':200, 
      },

   },

  'eff245':{  
      'move':{
      },
      'speed':2,                       
      'fire':{
         'res':'fire225',              
         'resAdd':2, 
         'ani':'cheer',   
         'time':700,                   
      },
      'bang':{
         'res':'bang245',             
         'resAdd':2, 
         'time':800,                  

         'sound':'bang245',         
         'soundVolume':2,        
         'soundDelay':-800,   
      },
      'hurt':{
         
         
         
         
         
         

         
         'injured':2,                 
         'forceO':25,
         'shock':200, 
         'hitTime':200, 
      },

   },

   'eff246':{  
      'noHurt':2,
      'fire':{
	 'noFlip':2,               
         'res':'fire292',              
         'resAdd':2, 
         'resScale':2, 
         'resZ':-80, 

         'ani':'cheer|attack|stand',   
         'time':2200,                   

         'sound':'fire232',              
         'soundVolume':2,              
         'soundDelay':75000,             
      },
   },

   'eff247':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|attack|stand',   
         'time':2500,                   
         'sound':'fire226',              
         'soundDelay':2300,            
      },
      'hurt':{
         'injured':2,                 
         'special':'bang247',      
         'specialSpeed':0.8,   
         'num':3, 		      
         'specialRndDelay':300,       

         'time':200,                  
         'hitTime':600,               
         'endTime':8000,               

         'forceS':30,
         'shock':200, 

         'sound':'hit222',              
         'soundDelay':800,            
      },
   },
   'eff248':{  
      'fire':{
         'res':'fire229',              
         'resScale':3,          
         'resSpeed':0.6,   
         'ani':'cheer|attack|stand',   
         'time':2500,                   

         'sound':'fire226',              
         'soundDelay':2300,            
      },
      'hurt':{
         'injured':2,                 
         'special':'bang248',      
         'specialSpeed':0.8,   
         'specialScale':0.7,   
         'num':3, 		      
         'specialRndDelay':300,          

         'time':200,                 
         'hitTime':2200,               
         'endTime':2000,               

         'forceS':30,
         'shock':200, 

         'sound':'hit228',              
         'soundDelay':-800,            
      },
   },
   'eff960':{  
      'fire':{
	 'noFlip':2,               
         'res':'fire225',              
         'ani':'cheer|stand',                
         'time':700,                   
      },
      'bang':{
         'injured':-2, 
         'res':'bang960',             
         'resSpeed':0.9, 
         'resScale':2.4, 
         'time':2000,                  

         'sound':'fire292',         
         'soundVolume':0.8,        
         'soundDelay':800,   
      },
   },
   'eff962':{  
      'noHurt':2,
      'fire':{
         'res':'fire225',              
         'ani':'cheer|stand',                
         'time':700,                   
      },
      'bang':{
         'res':'bang962',             
         
         'resScale':2.2, 


         'time':2000,                  

         'sound':'hit228',         
         'soundVolume':0.8,        
         'soundDelay':800,   
      },
      'hurt':{
         'injured':2,                 
         'endTime':500,               
         'forceR':80,
      },
   },
   'eff962_':{  
      'hurt':{
         'injured':2,                 
         'special':'bang962_',      
         'specialSpeed':0.8,   
         'specialScale':0.8,   
         'specialRndScale':0.2,   
         'specialRndDelay':500,
         'specialBg':2,   
         'num':5, 		      

         'hitTime':300, 
         'endTime':500, 
      },
   },

   'eff970':{  
      'move':{
      },
      'speed':2,                       
      'fire':{
         'res':'fire225',              
         'resAdd':2, 
         'ani':'cheer',   
         'time':700,                   
      },
      'bang':{
         'res':'bang970',             
         'resAdd':2, 
         'resScale':2.2, 
         'resAlpha':0.2, 

         'res2':'bang970',             
         

         'time':75000,                  

         'sound':'fire208',         
         'soundVolume':0.8,        
         'soundDelay':-800,   
      },
      'hurt':{            
         'endTime':75000,

         'injured':2,                 
         'forceO':45,
         'shock':200, 

         'sound':'hit222',         
         'soundVolume':0.9,        
         'soundDelay':-800,   
      },

   },
   'eff972':{  
      'noHurt':2,
      'fire':{
	 'noFlip':2,               
         'res':'fire225',              
         'ani':'cheer|stand',                
         'time':700,                   
      },
      'bang':{
         'res':'bang972',             
         'resScale':2.2, 
         'resSpeed':2.2, 
         'resAlpha':0.5,  
         'resAdd':2,        

         'res2':'bang972',             
         'res2Delay':800,     
         'res2Speed':2.2,    
         'res2Scale':2.2,     

         'time':75000,                  
         'endTime':75000,

         'sound':'beastTypeA',         
         'soundVolume':0.8,        
         'soundDelay':-800,   
      },

   },
   'eff972_0':{  
      'hurt':{
         
         
         
         
         
         

         'special':'buff972',      
         'specialX':-20,  
         'specialZ':-25,   
         'specialSpeed':0.8,  
         'specialAdd':2,  
         'specialAlpha':0.6,  
         'specialScale':2,                       
         'specialEvenX':50,           
         'specialSideDelay':600,           
         'num':9, 		      

         'endTime':75000,

         'sound':'fire232',             
         'soundVolume':0.8,              
         'soundDelay':-800,            
      },
   },
   'eff972_2':{  
      'fire':{
	 'noFlip':2,               
         'res':'fire225',              
         'ani':'cheer|stand',                
         'time':700,                   
      },
      'hurt':{
         'special':'bang972',      
         'specialSpeed':0.8,  
         'specialAdd':2,  
         'specialAlpha':0.6,  
         'specialScale':0.7,                       
         'specialRndScale':0.2,                       
         'specialRndX':50,           
         'specialRndDelay':600,           
         'num':5, 		      

         'hitTime':500,
         'endTime':8000,

         'injured':2,                 
         'forceO':30,
         'forceR':30,
         'shock':200, 

         'sound':'hit7022',             
         'soundVolume':0.5,              
         'soundDelay':800,            
      },
   },
   'eff972':{  
      'fire':{
         'res':'fire223',              
         'resScale':8,             
         'resSpeed':0.5,         
         'resAlpha':0.9,  
         'resAdd':2,  
         'resZ':800, 
         'resScene':2,     
         'ani':'cheer|attack|stand',   
         'time':8000,                   
      },
      'hurt':{
         'injured':2,                 
         'special':'special972',      
         'specialSpeed':0.8,     
         'specialAdd':2,  
         'num':2, 		      

         'forceR':25,
         'shock':200, 
         'hitTime':300,
         'endTime':8000,

         'sound':'fire229',          
         'soundDelay':-300,     
      },
   },
   'eff972h':{  
      'fire':{
	 'noFlip':2,               
         'time':600,                   
         'res':'fire223',              
         'resScale':5,             
         'resSpeed':0.6,          
         'resAdd':2,  
         'resZ':50, 
         'resScene':2,     
      },
      'hurt':{
         'res':'fire272',              
         'resAdd':2,
         'injured':-2, 

         'sound':'fire232',             
         'soundVolume':0.8,              
         'soundDelay':-800,            
      },
   },

   'eff973':{  
      'fire':{
         'res':'fire275',              
         'resScale':8,             
         'resSpeed':0.5,         
         'resAlpha':0.5,  
         'resAdd':2,  
         
         'resScene':2,     
         'ani':'cheer|attack|stand',   
         'time':8000,                   
      },
      'hurt':{
         'injured':2,                 
         'special':'special973',      
         'specialSpeed':0.8,     
         'specialAdd':2,  
         'specialScale':0.6,  
         'specialAlpha':0.6,   
         'specialRndX':50,           
         'num':4, 	 

         'forceO':25,
         'shock':200, 
         'hitTime':2500,
         'endTime':8000,

         'sound':'fire229',        
         'soundVolume':0.5,     
         'soundDelay':-500, 

         'soundOnce':'hit7092',          
         'soundOnceVolume':0.9,            
         'soundOnceDelay':-700,            
      },
   },



  
  
  
  
  
  
  

  
  
  
  
  
  
  

   'eff270':{  
      'fire':{
         'res':'fire270',              
         'ani':'cheer|stand',                
         'time':700,                   
      },
   },
   'eff272':{  
      
         
      
      

      
      
      
      
      'hurt':{
         'injured':-2,
         'res':'fire272',              
         'resAdd':2,

         'sound':'fire232',             
         'soundVolume':0.8,              
         'soundDelay':-800,            
      },
   },
   'eff273':{  
      'bannerTime':2600,                   
      'fire':{
	 'noFlip':2,               
         'res':'fire273',       
         'resSpeed':2,       
         'resScene':2,       

         'res2':'fire273',    
         'res2Add':2,  
         'res2Alpha':0.5,  
         'res2Scale':2.2,   
         'res2Speed':2, 
         'res2Scene':2, 
      
         'ani':'cheer|stand', 

         'sound':'fire232',             
         'soundVolume':0.8,              
         'soundDelay':-800,            

         'time':75000,                  
      },
   },
   'eff275':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|attack|stand',   
         'time':2500,                   
      },
      'bang':{
         'res':'bang275',             
         
         'time':8000,                  
         'sound':'bang275',          
         'soundDelay':800,     
      },
      'hurt':{
         'injured':2,                 
         'forceO':25,
         'shock':200, 
         'time':0, 
      },
   },
   'eff276':{  
      'fire':{
         'res':'fire206',              
         'resAdd':2,
         'ani':'cheer|stand',                
         'time':8000,                   
      },
      'hurt':{
         'special':'special276',      
         
         
         'num':4, 		      
         'specialRndDelay':800,          
         'injured':2,                 
         'forceR':50,

         'sound':'hit222',              
         'soundVolume':0.4,              
         'soundDelay':-200,             
      },
   },
   'eff278':{  
      
      'fire':{
         'res':'fire789',              
         'resAlpha':0.5,
         'ani':'cheer|stand',                
         'time':8000,                   
         'noFlip':2, 
      },
      'hurt':{
         'special':'special707X',      
         
         
         'specialRndDelay':800,          
         'num':4, 		      

         'injured':-2,              

         'sound':'hit222',              
         'soundVolume':0.4,              
         'soundDelay':-200,             
      },
   },
   'eff975':{  
      'bannerTime':2600,                   
      'fire':{
         'time':500,                   
      },
      'hurt':{
         'special':'special975',      
         'specialAdd':2,
         'specialX':-30,
         'specialSpeed':0.6,   
         'specialScale':2.2,   
         'num':2, 		 
         'hitTime':8000,                  

         'res':'fire272',              
         'resHSB':[0.8,2,2], 
         'resScale':2,   
         'resAlpha':0.5,   
         'resAdd':2,

         'soundOnce':'fire229',            
         'soundOnceVolume':0.8,            
         'soundOnceDelay':-8000,          
         'sound':'fire232',             
         'soundVolume':0.8,              
         'soundDelay':-800,            
      },
   },










   'eff702r':{   
      'speed':2,                       
      'fire':{
         'time':800,                   
         'sound':'fire204',              
         'soundVolume':0.8,              
         'soundDelay':-200,            
      },
      'move':{
         'stick':'stick204',           
         'stickScale':2,    
         'stickAlpha':0.4,         
         'stickAdd':2,
      },

      'hurt':{
         'injured':2,                 
         'forceX':20,                
         'res':'hit204',              
         'resAdd':2, 
         'resRota':85,                
         'sound':'hit2',              
         'soundVolume':0.8,              
         'soundDelay':-300,            
      },
   },
   'eff707':{  
      'speed':2.5, 
      'bullet':{
         'res':'bullet707',   
      },
      'fire':{
         'res':'fire707',              
         'resAdd':2,
         'ani':'cheer|stand',   
         'time':700,                   
         'sound':'hit230',              
         'soundVolume':0.9,              
         'soundDelay':600,            
      },
      'hurt':{
         'special':'bang707',      
         'specialAdd':2,
         'specialRndDelay':800,          
         'injured':2,                 
         'num':3, 		      
         'forceR':30,                
         'hitTime':600,               
         'res':'hit707',  

         'sound':'hit7022',              
         'soundVolume':0.8,              
         'soundDelay':-800,             
      },
   },
   'eff707a':{  
      'fire':{
         'ani':'cheer|stand',   
         'time':700,                   
         'sound':'fire707a',             
         'soundVolume':2,              
         'soundDelay':-300,            
      },
      'hurt':{
         'res':'fire272',              
         'resAdd':2,

         'special':'special707X',      
         'num':3, 		      
         'specialRndDelay':800,          

         'sound':'fire232',             
         'soundVolume':0.8,              
         'soundDelay':-800,            
      },
   },
   'eff723a':{  
      'fire':{
         'ani':'cheer|stand',   
         'time':2200,                   

      },
      'bang':{
         'res':'bang723X',             
         'resSpeed':0.8,                 
         'time':700,                  

         'sound':'bang275',              
         'soundVolume':2,              
      },
      'hurt':{
         'res':'hit224',              
         'resRota':20,                
         'injured':2,                 
         'forceO':30,                
         'shock':200, 
         'hitTime':800,

         'sound':'hit723a',              
         'soundVolume':2,              
         'soundDelay':-200,             
      },

   },
   'eff724a':{  
      'fire':{
         'res':'fire225',              
         'resAdd':2, 
         'ani':'cheer|stand',   
         'time':700,                   
      },
      'hurt':{
         'special':'special724X',      
         'num':2, 		      
         'specialRndDelay':800,          

         'res':'hit236',              
         'time':200,                 
         'injured':2,                 
         'forceO':25,
         'shock':200, 
         'hitTime':8000, 

         'sound':'hit724a',         
         'soundVolume':2,        
         'soundDelay':-300,   
      },

   },

   'eff724r':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|stand',   
         'time':700,                   
      },
      'bang':{
         'res':'bang724r',             
         'resSpeed':2.2, 
         'resScale':2.2, 
         'resAlpha':0.5,  
         'resY':50, 
         'resAdd':2,        

         'res2':'bang724r',             
         'res2Delay':800,     
         'res2Speed':2.3,    
         'res2Scale':2.2,   
         'res2Y':50,   
 
         'sound':'hit229',              
         'soundVolume':2,              
         'soundDelay':-800,             

         'time':8000,                  
      },
      'hurt':{
         'injured':2,                 
         'forceR':30,                
         'forceZ':200,
         'landingBouncePer':0.5,                  
         'landingAni':'injured2|injured2|injured2|injured2|stand',                  
         'landingAniStateTime':'200|200|200|200|200',            
         'landingDelay':-500,                  
         'shock':800, 
         'endTime':8000,
         'pauseTime':500,    
      },
   },

   'eff727r':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|stand',   
         'time':700,                   
      },
      'bang':{
         'res':'bang727r',             
         'resSpeed':2.2, 
         'resScale':2.2, 
         'resAlpha':0.3,  
         'resY':50, 
         'resAdd':2,        

         'res2':'bang727r',             
         'res2Delay':800,     
         'res2Speed':2.3,    
         'res2Scale':2.2,     
         'res2Y':50,  
 
         'sound':'hit232',              
         'soundVolume':2,              
         'soundDelay':-800,             

         'time':8000,                  
      },
      'hurt':{
         'injured':2,                 
         'forceR':30,                
         'forceZ':200,
         'landingBouncePer':0.5,                  
         'landingAni':'injured2|injured2|injured2|injured2|stand',                  
         'landingAniStateTime':'200|200|200|200|200',            
         'landingDelay':-500,                  
         'shock':200, 
         'endTime':8000,
         'pauseTime':500,    
      },
   },
   'eff720r':{  
      'noHurt':2,
      'fire':{
	 'noFlip':2,               
         'res':'fire292',              
         'resAdd':2, 
         'resScale':3, 
         'resZ':-80, 

         'ani':'cheer|stand',   
         'time':800,                   

         'sound':'fire232',              
         'soundVolume':2,              
         'soundDelay':800,             
      },
      'hurt':{
         'injured':-2,           
         'res':'fire292',              
         'resAdd':2, 
         'resScale':2.5, 
         'resZ':-80, 
         'pauseTime':500,    
      },
   },

   'eff722r':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|stand',   
         'time':700,                   
      },
      'bang':{
         'res':'special722r',             
         'resScale':2.2, 
         'resSpeed':0.7, 
         'resAlpha':0.5,  
         'resAdd':2,        

         'res2':'special722r',             
         'res2Delay':800,     
         'res2Speed':0.9,    
         'res2Scale':2.2,     
 
         'sound':'beastTypeA',              
         'soundVolume':2,              
         'soundDelay':-800,             

         'time':8000,                  
      },
      'hurt':{
         'injured':2,                 
         'forceO':-30,                
         'forceZ':200,
         'landingBouncePer':0.5,                  
         'landingAni':'injured2|injured2|injured2|injured2|stand',                  
         'landingAniStateTime':'200|200|200|200|200',            
         'landingDelay':-500,                  
         'shock':800, 
         'endTime':8000,
         'pauseTime':500,    
      },
   },

   'eff725r':{  
      'speed':2.5,                       
      'move':{
         'stick':'buff230',           
         'stickScale':3,    
         'stickAlpha':2,         
         
      },

      'fire':{
         'res':'fire225',              
         'ani':'cheer|stand',   
         'time':700,                   
      },
      'bang':{
         'res':'bang725r',             
         'resDelay':-500,     
         'resSpeed':2.2, 
         'resScale':2.2, 
         'resY':50, 
         'resAdd':2,        

         'res2':'bang725r',             
         'res2Delay':-500,     
         'res2Speed':2.3,    
         'res2Scale':2.2,     
         'res2Y':50, 

         'time':400,                  
      },
      'hurt':{
         'injured':2,                 
         'forceX':50,               
         'shock':200,

         'endTime':75000,                  

         'sound':'hit222',              
         'soundVolume':0.7,              
         'soundDelay':-800,             
      },
   },

   'eff726r':{  
      'speed':2,                       
      'move':{
         'stick':'bullet782',           
         'stickScale':2.5,    
         'stickAlpha':0.7,         
         'stickX':-60,   
         
      },

      'fire':{
         'res':'fire225',              
         'resScale':2,     
         'resSpeed':0.7,        
         'time':8000,                   

         'ani':'cheer|attack|stand',   
         'sound':'fire2',              
         'soundVolume':0.5,              
         'soundDelay':200,            
      },
      'bang':{
         'res':'bang7232',             
         'resDelay':-400,     
         'resSpeed':2.2, 
         'resScale':2.2, 
         'resAdd':2,        

         'res2':'bang7232',             
         'res2Delay':-500,     
         'res2Speed':2.4, 
         'res2Scale':2.2, 
     

         'sound':'hit228',              
         'soundVolume':2,              
         'soundDelay':-75000,             

         'time':300,                  
      },
      'hurt':{
         'forceX':20,         
         'forceR':30,                
         'injured':2,                 
         'shock':800,
      },
   },


   'eff732r':{  
      'noHurt':2,      
      'fire':{
         
         
         'ani':'cheer|stand',   
         'time':700,                   

         'res':'bang732r',             
         'resSpeed':0.8,
         'resScale':2.2,     
         'resDelay':800,  
         'resAlpha':0.4,      
         'resAdd':2,   
         'resScene':2,   
         'resX':300,  

         'res2':'bang732r',             
         'res2Speed':0.8,
         'res2Scale':2.2,     
         'res2Delay':800,  
         'res2Scene':2,   
         'res2X':300,  

         'sound':'beastTypeI',              
         'soundVolume':2,              
         'soundDelay':200,             
      },
      'hurt':{
         'injured':2,                 
         'forceO':25,
         'forceX':40,

         'endTime':2200,
      },
   },
   'eff734r':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|attack|stand',   
         'time':275000,                   
      },
      'bang':{
         'res':'bangH22',             
         'resAdd':2,            
         'time':600,                  

         'sound':'hit228',              
         'soundVolume':2,              
         'soundDelay':-300,             
      },
      'hurt':{ 
         'injured':2,                 
         'time':200,                 
         'forceR':30,
         'forceO':30,
         'shock':800, 

         'sound':'hit232',              
         'soundVolume':0.6,              
         'soundDelay':-800,             
      },

   },
   'eff775r':{  
      'fire':{
	 'noFlip':2,               
         'res':'fire225',              
         'ani':'cheer|stand',                
         'time':700,                   
      },
      'bang':{
         'res':'bang723X',             
         'resAdd':2,   
         'resSpeed':2.2, 

         'res2':'bang723X',             
         'res2Scale':2.2,          
         'res2Speed':2.2,  
         'time':2200,                 

         'sound':'fire229',         
         'soundVolume':0.8,        
         'soundDelay':800,   
      },


   },

   'eff763':{  
      'fire':{
         'ani':'cheer|stand',          
         'time':800,                   
         'res':'fire272',              

         'sound':'fire232',            
         'soundVolume':0.8,            
         'soundDelay':-800,            
      },
      'hurt':{
         'res':'fire272',              
         'resAdd':2,

         'sound':'fire232',             
         'soundVolume':0.8,              
         'soundDelay':-800,            
      },
   },
   'eff763r':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|attack|stand',   
         'time':2500,                   
      },

      'hurt':{
         'injured':2,                 
         'forceS':30,   

         'num':2, 		      
         'special':'special763r',      
         'specialScale':2.3,  
         'specialSpeed':0.9,           
         'specialAlpha':0.5,    
         'specialAdd':2,    
         'specialDelay':-400,    

         'special2':'special763r',      
         'special2Scale':2.2,        
         'special2Delay':-200,  

         'shock':200,
         'endTime':2200,

         'sound':'hit232',              
         'soundVolume':2,              
         'soundDelay':-500,             
      },

   },

   'eff765r':{  
      'noHurt':2,      
      'fire':{
	 'noFlip':2,               
         'ani':'cheer|stand',   
         'time':2500,                   

         'res':'bang765r',             
         'resSpeed':0.8,
         'resScale':2.2,     
         'resDelay':800,  
         'resAlpha':0.4,      
         'resAdd':2,   
         'resScene':2,   
         

         'res2':'bang765r',             
         'res2Speed':0.8,
         'res2Scale':2.2,     
         'res2Delay':800,  
         'res2Scene':2,   
         

         'sound':'hit7272',              
         'soundVolume':2,              
         'soundDelay':200,             
      },
      'hurt':{
         'injured':2,                 
         'forceO':25,
         'forceX':40,

         'endTime':2200,
      },
   },

   'eff767a':{  
      'fire':{
         'res':'fire225',              
         'resAdd':2,
         'ani':'cheer',                
         'time':700,                   

         'sound':'fire280',              
         'soundVolume':0.5,              
         'soundDelay':800,             
      },
      'speed':6.5,                       
      'move':{
         'stick':'stick204',           
         'stickAdd':2,
         'atk':'fire273',               
         'atkAdd':'hit207',              
      },
      
      
      
      
      'hurt':{
         
         
         'endTime':500,         
         'pauseTime':-8000,               
      },
   },
   'eff767a2':{  
      'noReStand':2,      
      'hurt':{
         'special':'specialLose0',      
         'num':6, 		      
         'specialRndDelay':800,          
         'injured':2,                 
         'forceR':50,
         'endTime':2500,

         'sound':'hitLoser2',              
         'soundVolume':2,              
         'soundDelay':-300,             
      },
   },

   'eff770a':{  
      'fire':{
         'res':'fire789',              
         'resScale':2.5,

         'ani':'cheer|attack|stand',   
         'time':750,                   
      },
      'bang':{
         'res':'bang789',             
         'resAdd':2,
         'time':75000,                  
      },
      'hurt':{
         'num':5,          
         'special':'special770a',      
         'specialScale':2,  
    
         'sound':'beastTypeH',              
         'soundVolume':2,              
         'soundDelay':300,             

         'endTime':8000,
      },
   },
   'eff770a2':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|stand',                
         'time':700,                   
      },
   },

   'eff772a':{  
      'hurt':{
         'res':'fire272',              
         'resAdd':2,

         'sound':'fire232',             
         'soundVolume':0.8,              
         'soundDelay':-800,            
         'endTime':800,                   
      },
   },
   'eff774a':{  
      'speed':2,                       
      'fire':{
         'res':'fire207',              
         'resAdd':2,
         'time':200,                   

         'sound':'fire280',              
         'soundVolume':0.7,              
      },
      'move':{
         'stick':'stick209',           
         'stickAdd':2,
      },
      'hurt':{
         'res':'hit209',              
         'injured':2,                 
         'forceX':30,                
         'forceY':80,                
         'resAdd':2,

         'sound':'hit275',             
         'soundVolume':0.9,              
         'soundDelay':-800,            
      },
   },


   'eff775a':{  
      'hurt':{
         'res':'fire272',              
         'resAdd':2,

         'sound':'fire232',             
         'soundVolume':0.8,              
         'soundDelay':-800,            
      },

   },
   'eff775a2':{  
      'hurt':{
         'res':'fire275',              
         'resScale':2,              
         'resAdd':2,

         'sound':'fire240',         
         'soundVolume':0.3,        
      },

   },


   'eff776r':{   
      'speed':2,                       
      'fire':{
         'res':'fire275',              
         'ani':'cheer',   
         'time':8000,                   

         'sound':'fire204',              
         'soundVolume':0.8,              
         'soundDelay':-200,            
      },
      'move':{
         'stick':'stick962',           
         'stickAdd':2,
         'stickScale':2.2, 
         'stickAlpha':0.5,

         'time':800,                   

         'trailing':'hit203',                   
         'trailingDistance':800,                   
         'trailingAdd':2,    
         'trailingScale':2.5,          
         'trailingSpeed':0.5,                      
         'trailingBg':2, 
         'trailingRndIntetval':5,                   
         'trailingRndOffsetX':30,                   
         'trailingRndOffsetY':30,                   
      },

      'hurt':{
         'injured':2,                 
         'forceR':30,                
         'res':'hit226',              
         'resScale':2.2,            
         'resAdd':2,        
         'resSpeed':0.8,      
         'resAlpha':0.6,     

         'sound':'hit2',              
         'soundVolume':0.9,              
         'soundDelay':-300,            
      },
   },



   'eff778a':{  
      'fire':{
         'res':'fire789',              
         'resScale':2.5,
         'ani':'cheer|stand',   
         'time':750,                   
      },
      'bang':{
         'res':'bang778a',             
         'resX':-30,            
         'resScale':2.2,      

         'res2':'bang778a',             
         'resDelay':800,    
         'resX':-50, 
         'resAdd':2, 
         'resAlpha':0.5,            
         'resScale':2.2,    

         'time':2200,                  
         'sound':'fire223',              
         'soundDelay':800,            
         'soundVolume':2,              
      },
      'hurt':{
         'injured':2,                 
         'endTime':500,               
         'forceX':30,
         'sound':'hit226',              
         'soundDelay':-800,            
      },
   },
   'eff778r':{  
      'bannerTime':2600,                   
      'fire':{
	 'noFlip':2,               
         'res':'fire789',              
         'resScale':2.5,              
         'ani':'cheer|stand',   
         'time':750,                   
      },
      'bang':{
         'res':'bang778r',       
         'resSpeed':6.5,         

         'time':2200,                  
         'sound':'beastTypeA',              
         'soundDelay':-800,            
         'soundVolume':2,              
      },
   },
   'eff779r':{  
      'noHurt':2,
      'fire':{
	 'noFlip':2,               

         'res':'buff246',              
         'resSpeed':0.4,   
         'resScale':5,      
         'resAdd':2,    
         'resDelay':800,  

         'ani':'cheer|stand',   
         'time':800,                   

         'sound':'hit7272',              
         'soundVolume':0.3,              
         'soundDelay':-800,            
      },
      'hurt':{
         'res':'buff246',              
         'resSpeed':0.4,   
         'resScale':2.5,      
         'resAdd':2,   
         'resAlpha':0.8,        
  
         'endTime':300,               
      },
   },

   'eff782r':{  
      'noHurt':2,
      'fire':{
	 'noFlip':2,               
         'res':'buff782r',              
         'resSpeed':0.3,   
         'resScale':2.5,     
         'resScene':2,     
         'resAlpha':0.4,   
         'resAdd':2, 

         'res2':'buff782r',              
         'res2Speed':0.4,   
         'res2Scale':3,      
         'res2Scene':2, 
         'res2Alpha':0.7,  
         'res2Bg':2,    

         'ani':'cheer|stand',   
         'time':8000,                   

         'sound':'fire202',              
         'soundVolume':0.6,              
         'soundDelay':8000,            
      },
   },

   'eff783a':{  
      'fire':{
         'res':'fire275',              
         'ani':'cheer',   
         'time':700,                   
         'sound':'fire280',              
         'soundVolume':0.7,              
         'soundDelay':500,            
      },
      'speed':2,                       
      'move':{
         'stick':'stick209',           
         'stickAdd':2,
      },
      'hurt':{
         'res':'hit209',              
         'injured':2,                 
         'forceX':30,                
         'forceY':80,                
         'resAdd':2,

         'sound':'hit275',             
         'soundVolume':0.8,              
         'soundDelay':-800,            
      },
   },

   'eff783r':{  
      'noHurt':2,
      'fire':{
         'noFlip':2,               
         'res':'fire223',              
         'resAdd':2,         
         'resScale':20,   
         'resSpeed':0.05,          
         'resAlpha':0.7,  
         'resZ':250,     
         'resBg':2,   
         'ani':'cheer|stand',   
         'time':8000,                   

         'sound':'fire232',              
         'soundDelay':800,             
      },
   },


   'eff784a2':{  
      'fire':{
         'ani':'cheer|stand',          
         'res':'fire225',              
         'resAlpha':0.8,
         'resScale':3,
         'resAdd':2,
         'time':8000,                   
      },
      'hurt':{
         'special':'special862',              
         'specialAdd':2,
         'specialScale':0.7,
         'specialX':-50,
         'specialSpeed':0.6,
         'num':3,
         
         
         
         
         'time':300,                 
         'endTime':8000,                 

         'sound':'fire232',             
         'soundVolume':0.7,              
         'soundDelay':-800,            
      },
   },
   'eff789':{  
      'fire':{
         'res':'fire789',              
         'resScale':2.5,

         'ani':'cheer|stand',   
         'time':750,                   
      },
      'bang':{
         'res':'bang789',             
         'resScale':2.9,   
         'resAlpha':0.6,        
         'resAdd':2,
         'resX':250,

         'res2':'bang789',             
         'res2Scale':6.5,        
         'res2X':800,

         'sound':'fire292',             
         'soundVolume':0.3,              
         'soundDelay':-800,            

         'time':50,                  
      },
   },
   'eff789a':{  
      'hurt':{
         'res':'hit226',              
         'injured':2,                 
      },
   },
   'eff789r':{  
      'speed':2.5,
      'fire':{
         'res':'fire789',              
         'resScale':2.5,

         'ani':'cheer|stand',   
         'time':8000,                   
         'sound':'fire292',              
         'soundVolume':0.5,              
         'soundDelay':800,             
      },
      'bullet':{
         'res':'bullet782',           
         'resScale':2,
      },
      'hurt':{
         'injured':2,                 
         'forceS':30,              
         'special':'special724X',      
         'specialScale':0.9,     
         'num':3, 		      

         'endTime':8000,                  

         'sound':'hit228',              
         'soundVolume':2,              
         'soundDelay':-800,             
      },
   },


   'eff792':{  
      'hurt':{
         'res':'hit7022',              
         'resAdd':2,
         'resUseSkew':2,                       
         'injured':2,                
      },
   },
   'eff792r':{  
      'fire':{
	 'noFlip':2,               
         'res':'fire225',              
         'ani':'cheer|stand',   
         'time':8000,                   

         'sound':'fire202',              
         'soundVolume':0.6,              
         'soundDelay':8000,            
      },
      'hurt':{
         'injured':-2,                 
         'special':'special792a',      
         'specialScale':0.9,     
         'num':3, 		      

         'endTime':8000,                  

         'sound':'fire202',              
         'soundVolume':2,              
         'soundDelay':-800,             
      },
   },



   'effH7002':{  
      'bannerTime':2600,                   
      'fire':{
         'res':'fire789',              
         'resScale':2.5,
         'ani':'cheer|stand',   
         'time':8000,                   
      },
      'hurt':{ 
         'special':'special7002',   
         
         
         
         'specialScale':2.2,
         'specialSpeed':0.9,
         'num':3, 		      

         'hitTime':2200,                 
         'pauseTime':300, 

         'sound':'fire232',             
         'soundVolume':0.8,              
         'soundDelay':-800,            
      },
   },

   'eff7002':{  
      'hurt':{ 
         'special':'special7002',   
         'specialScale':0.8,
         'specialSpeed':0.9,
         'num':3, 		      

         'injured':2,    

         'hitTime':2800,                 
         'pauseTime':300, 

         'sound':'hit0004',             
         'soundVolume':0.5,              
         'soundDelay':-2300,            
      },
   },



   'eff826':{  
      'speed':5,                       
      'fire':{
         'sound':'fire2',              
         'soundVolume':0.5,              
         'soundDelay':200,            
         'time':200,                   
      },
      'bullet':{
         'res':'bullet826',           
         'resZ':40,            
         
      },
      'bang':{
         'res':'bang826',             
         'resSpeed':2.2,
         'resDelay':-300,     
         'time':300,                  
         'sound':'hit7022',              
         'soundVolume':0.5,              
      },

      'hurt':{
         'res':'hit802',              
         'resRota':25,  
         'forceX':20,    
         'forceY':80,     
         'time':50,    
         'hitTime':-200,    
         'pauseTime':600,      
         'injured':2,                 
         'sound':'hit2',              
         'soundVolume':0.2,              
         'soundDelay':-800,            
      },
   },
   'eff827':{  
      'speed':2,                       
      'fire':{
         'sound':'fire2',              
         'soundVolume':0.5,              
         'soundDelay':200,            
         'time':200,                   
      },
      'bullet':{
         'res':'bullet827',           
         'resX':-20,            
         'resZ':275,            
         'gravity':5,                 
         'gravityNear':3,                  
      },
      'bang':{
         'res':'bang827',             
         'resSpeed':0.8,
         'time':800,                  
         'sound':'hit7022',              
         'soundVolume':0.5,              
      },
      'hurt':{
         'res':'hit226',              
         'injured':2,                 
         'forceO':30,                 
         'shock':50, 

         'time':50, 
         'hitTime':-200,  
         'pauseTime':2200,

         'sound':'hit2',              
         'soundVolume':0.2,              
         'soundDelay':-800,            
      },
   },
   'effHurtBack':{  
      'hurt':{
         'forceX':800,
         'forceDef':4000,            
      },
   },

   'eff832':{  
      'fire':{
         'res':'fire207',              
         'resAdd':2,
         'ani':'cheer',                
         'time':500,                   
      },
      'speed':2.5,                       
      'move':{
         'stick':'stick204',           
         'stickAdd':2, 
      },
      'hurt':{
         'res':'hit280',              
         'resScale':2,        
         'resAdd':2,
         'injured':2,                 
         'forceX':20,                

         'time':50, 

         'sound':'hit226',             
         'soundVolume':0.9,              
         'soundDelay':-800,            
      },
   },

   'eff423':{  
      'speed':2.2,                       
      'range':800,                 
      'rangeLock':2,                

      'move':{
         'rndX':0,   
         'rndY':0,   
         'endTime':2400,                
      },
      'bang':{
         'res':'bang7262',             
         'resDelay':800,           
         'resHSB':[0.5,2.2,2.2],    
         'time':600,                  
         'noHide':2,                       
      },
      'hurt':{
         'res':'hit7262',              
         'injured':2,                 
         'forceO':50,                
         'forceZ':250,
         'landingBouncePer':0.5,                  
         'landingAni':'injured2|injured2|injured2|injured2|stand',                  
         'landingAniStateTime':'200|200|200|200|200',            
         'landingDelay':-500,                  
         'shock':800, 
         'hitTime':0,
         'sound':'hit7262',              
         'soundVolume':0.6,              
         'soundDelay':-800,             
         'endTime':2200,
      },
   },
   'eff423_2':{  
      'speed':2.5,                       
      'range':800,                 
      'rangeLock':2,                
      'fire':{
         'res':'fire207',              
         'resSpeed':0.2,
         'resScale':5,
         'resAdd':2,
         'ani':'attack|stand',                
         'aniSpeed':0.6,            
         'time':2000,                   
         'sound':'fire292',              
         'soundVolume':2,              
         'soundDelay':800,             
      },
      'move':{
         'rndX':0,   
         'rndY':0,   
         'endTime':2500,                
      },
      'bang':{
         'res':'bang7262',             
         'resScale':6.5,
         'resDelay':800,           
         'resHSB':[0.6,2,2],    
         'time':600,                  
         'noHide':2,                       
      },
      'hurt':{
         'res':'hit7262',              
         'resHSB':[0.8,2,2],   
         'resScale':2,
         'injured':2,                 
         'forceO':50,                
         'forceZ':250,
         'landingBouncePer':0.5,                  
         'landingAni':'injured2|injured2|injured2|injured2|stand',                  
         'landingAniStateTime':'200|200|200|200|200',            
         'landingDelay':-500,                  
         'shock':800, 
         'hitTime':0,
         'sound':'hit7262',              
         'soundVolume':2,              
         'soundDelay':-800,             
         'endTime':2300,
      },
   },

   'eff850':{  
      'speed':2.5,                       
      'move':{
         'rndX':0,   
         'rndY':0,   
      },
      'bang':{
         'res':'bang7262',             
         'time':75000,                  
         'noHide':2,                       
      },
      'hurt':{
         'res':'hit7262',              
         'injured':2,                 
         'forceR':25,                
         'forceY':25,
         'time':800,                 
         'shock':800, 
         'hitTime':0,
         'sound':'hit7262',              
         'soundVolume':2,              
         'soundDelay':-800,             
      },
   },
   'eff860':{  
      'speed':2.5,                       
      'move':{
         'rndX':0,   
         'rndY':0,   
      },
      'bang':{
         'res':'bang7262',             
         'time':600,                  
         'noHide':2,                       
      },
      'hurt':{
         'res':'hit7262',              
         'injured':2,                 
         'forceR':25,                
         'forceY':25,
         'time':800,                 
         'shock':200, 
         'hitTime':0,
         'sound':'hit7262',              
         'soundVolume':2,              
         'soundDelay':-800,             
      },
   },
   'eff860a':{  
      'fire':{
         'res':'fire225',              
         'resAlpha':0.5,
         'resScale':3,
         'resAdd':2,
         'ani':'attack|stand',                
         'time':8000,                   
      },
      'bang':{
         'res':'special860',             
         'time':800,                  

         'sound':'hit222',              
         'soundVolume':2,              
         'soundDelay':-200,             
      },
      'hurt':{
         'time':50,                 
         'endTime':300,               
         'injured':2,                 
         'forceX':20,                
         'shock':800, 
      },
   },

   'eff862':{  
      'speed':3,                       
      'range':50,                
      'rangeLock':2,                
      'move':{
         'rndX':0,   
         'rndY':0,   
      },
      'bang':{
         'res':'bang862',             
         'time':400,                  
         'sound':'hit236',              
         'soundVolume':2,              
         'soundDelay':-400,             
         'noHide':2,                       
      },
      'hurt':{
         'injured':2,                 
         'forceR':20,                
         'shock':50, 
      },
   },

   'eff862s':{  
      'fire':{
         'res':'fire225',              
         'resAlpha':0.8,
         'resScale':3,
         'resAdd':2,
         'time':2200,                   
      },
      'bang':{
         'res':'special862',             
         'resAdd':2,
         'resX':-60,
         'resZ':-30,
         'time':400,                  
      },
      'hurt':{
         
         
         
         
         
         'time':300,                 
         'endTime':2500,                 

         'sound':'fire232',             
         'soundVolume':0.7,              
         'soundDelay':-800,            
      },
   },
   'eff862a':{  
      'fire':{
         'res':'fire225',              
         'resAlpha':0.5,
         'resScale':3,
         'resAdd':2,
         'time':75000,                   
      },
      'range':400,                
      'rangeLock':2,                
      'speed':2.5,                       
      'move':{
         'ani':'attack|stand',   
         'endTime':2500,   
         'rndX':0,   
         'rndY':0,  
      },
      'attack':{
         'res':'fire862',   
         'resSpeed':2.5,  
         'resX':30,   
         'resZ':-85,   
         'resAdd':2,  
      },
      'bang':{
         'res':'bang7802',             
         
         
         'time':800,                  
         'sound':'hit7802',              
         'soundVolume':0.6,              
         'noHide':2,                       
      },
      'hurt':{
         'endTime':300,               
         
         'injured':2,                 
         'forceX':20,                
      },
   },

   'eff862':{  
      'range':250,                
      'speed':2.5,                       
      'fire':{
         'res':'fire225',              
         'resAlpha':0.5,
         'resScale':3,
         'resAdd':2,
         'time':75000,                   
      },
      'move':{
         'ani':'attack|stand',   
         'endTime':2500,   
         'rndX':0,   
         'rndY':0,  
      },
      'bang':{
         'res':'bang862',             
         'resDelay':300,                   
         'time':75000,                  
         'sound':'hit7022',              
         'soundDelay':700,            
         'soundVolume':2,              
         'noHide':2,                       
      },
      'hurt':{
         'injured':2,                 
         'time':50,                 
         'forceX':20,                
         'shock':800, 
      },
   },
   'eff863a':{  
      'fire':{
         'res':'fire225',              
         'resAlpha':0.5,
         'resScale':3,
         'resAdd':2,
         'time':75000,                   
      },
      'range':600,                
      'rangeLock':2,                
      'speed':2.5,                       
      'move':{
         'ani':'attack|stand',   
         'endTime':2000,   
         'rndX':0,   
         'rndY':0,  
      },
      'attack':{
         'res':'fire863',  
         'resSpeed':2.5,   
         'resX':30,   
         'resZ':-70,   
         'resAdd':2,  
         'sound':'fire229',          
         'soundDelay':800,     
      },
      'bang':{
         'resDelay':-300,
         'res':'bang863',             
         
         
         'time':50,                  
         'sound':'hit7802',              
         'soundVolume':0.6,              
         'noHide':2,                       
      },
      'hurt':{
         'endTime':300,               
         'time':800,                 
         'injured':2,                 
         'forceX':20,                
      },
   },


   'eff865':{  
      'fire':{
         'res':'fire225',              
         'resAlpha':0.5,
         'resScale':3,
         'resAdd':2,
         'ani':'attack|stand',                
         'time':75000,                   
      },
      'bang':{
         'res':'bang865',             
         'time':800,                  

         'sound':'hit222',              
         'soundVolume':0.4,              
         'soundDelay':-200,             
      },
      'hurt':{
         'endTime':300,               
         'injured':2,                 
         'forceX':20,                
         'shock':800, 

         'sound':'hit229',              
         'soundVolume':0.4,              
         'soundDelay':-400,             
      },
   },
   'eff865s':{  
      'fire':{
         'noFlip':2,
         'res':'fire225',              
         'ani':'attack|stand',                
         'time':700,                   
      },
      'bang':{
         'res':'bang240',             
         'resAdd':2,        
         'resScale':2.5,      
         'resX':-60,
         'resZ':5,
         'time':75000,                  

         'sound':'fire240',         
         'soundVolume':2,        
         'soundDelay':-800,   
      },
   },



   'eff880':{  
      'speed':2,                       
      'move':{
      },
      'hurt':{
         'res':'hit280',              
         'resAdd':2,
         'injured':2,                 
         'forceX':20,                

         'sound':'hit226',             
         'soundVolume':0.9,              
         'soundDelay':-800,            
      },
   },
   'eff882':{  
      'fire':{
         'res':'fire207',              
         'resAdd':2,
         'ani':'cheer',                
         'time':500,                   
      },
      'speed':2.5,                       
      'move':{
         'stick':'stick204',           
         'stickAdd':2, 
      },
      'hurt':{
         'res':'hit280',              
         'resAdd':2,
         'injured':2,                 
         'forceX':20,                

         'sound':'hit226',             
         'soundVolume':0.9,              
         'soundDelay':-800,            
      },
   },

   'eff882':{  
      'fire':{
         'res':'fire095',              
         'resAdd':2,
         'ani':'cheer',                
         'time':500,                   
      },
      'speed':2.5,                       
      'move':{
         'stick':'stick209',           
         'stickAdd':2, 
         'stickScale':2,           
      },
      'hurt':{
         'res':'hit280',              
         'resAdd':2,
         'injured':2,                 
         'forceX':20,                

         'sound':'hit226',             
         'soundVolume':0.9,              
         'soundDelay':-800,            
      },
   },

   'eff883':{  
      'fire':{
         'res':'fire206',              
         'resAdd':2,
         'ani':'cheer',                
         'time':500,                   
      },
      'speed':3,                       
      'move':{
         'stick':'stick292',           
         'stickAdd':2, 
         'stickScale':2,           
      },
      'hurt':{
         'res':'hit280',              
         'resAdd':2,
         'injured':2,                 
         'forceX':20,                

         'sound':'hit226',             
         'soundVolume':0.9,              
         'soundDelay':-800,            
      },
   },

   'eff884':{  
      'speed':2,                       
      'fire':{
         'sound':'fire2',              
         'soundVolume':0.5,              
         'soundDelay':200,            
         'time':200,                   
      },
      'bullet':{
         'res':'bullet827',           
         'resScale':0.5,            
         'resX':-20,            
         'resZ':20,            
         'gravity':3,                 
         'gravityNear':2,                  
      },
      'hurt':{
         'res':'hit802',              
         'resRota':25,  
         'forceR':80,                
         'forceX':20,               
         'forceO':5,
         'injured':2,                 
         'sound':'hit2',              
         'soundVolume':0.2,              
         'soundDelay':-800,            
      },
   },

   'eff885':{  
      'fire':{
         'res':'fire205',              
         'resAdd':2,
         'resScale':2,
         'ani':'cheer',                
         'time':500,                   
      },
      'speed':3,                       
      'move':{
         'stick':'stick223',           
         'stickAdd':2, 
         'stickScale':3,           
      },
      'hurt':{
         'res':'hit280',              
         'resAdd':2,
         'injured':2,                 
         'forceX':20,                

         'sound':'hit226',             
         'soundVolume':0.9,              
         'soundDelay':-800,            
      },
   },
   'eff886':{  
      'speed':2,                       
      'fire':{
         'res':'fire225',              
         'resAlpha':0.5,
         'ani':'cheer|stand',                
         'time':700,                   

         'sound':'fire223',              
         'soundVolume':0.9,              
         'soundDelay':500,             
      },
      'bullet':{
         'res':'bullet223',           
         
      },
      'hurt':{
         'res':'hit224',              
         'injured':2,                 
         'forceX':25,

         'sound':'hit228',              
         'soundVolume':0.4,              
         'soundDelay':-800,             
      },
   },







   'eff3803':{  
      'fire':{
         'res':'fire292',              
         'resAdd':2,
         'resScale':2,
         'time':400,                   
      },
   },

   'eff3223':{  
      'speed':2,                       
      'move':{
      },
      'fire':{
         'res':'fire203',              
         'resAdd':2, 
         'ani':'cheer',   
         'time':600,                   
      },
      'hurt':{
         'injured':2,                 
         'forceX':20,                
         'res':'hit800',              
         'resAdd':2,
         'resRota':85,                
         'sound':'hit0',              
         'soundDelay':-300,            
      },
   },

   'eff3202':{  
      'speed':3,                       
      'fire':{
         'sound':'fire2',              
         'soundVolume':0.5,              
         'soundDelay':200,            
         'time':200,                   
      },
      'bullet':{
         'res':'bullet222',           
         'resScale':2.2,         
         'gravity':0,                 
      },
      'hurt':{
         'res':'hit802',              
         'resAdd':2,
         'sound':'hit2',              
         'soundVolume':0.2,              
         'soundDelay':-800,            
      },
   },
   'eff3203_0':{  
      'speed':2,                       
      'bullet':{
         'res':'bullet3203',           
         
         'gravity':2,                 
      },
   },
   'eff3203':{  
      'fire':{
         'res':'bullet226',   
         'resScale':2,     
         'resAdd':2,             
         'time':2200,                   
      },
      'hurt':{
         'special':'bang7802',      
         'specialScale':0.4,     
         'specialSpeed':6.5,     
         'num':3, 		      
         'specialRndDelay':300,          
         'endTime':2200,        
         'forceR':30,

         'sound':'hit222',              
         'soundDelay':800,            
      },
   },

   'eff3280':{  
      'bullet':{
         'res':'bullet3280',           
         'gravity':2.5,                 
      },
   },

   'eff3222':{  
      'speed':2.5,                       
      'bullet':{
         'res':'bullet3222',           
         'gravity':2.5,                 
      },
   },
   'eff3222':{  
      'speed':2.5,                       
      'bullet':{
         'res':'bullet3222',           
         
         'resX':-2,            
         'resZ':80,            
         'gravity':2.5,                 
         'gravityNear':2,                  
      },
   },
   'eff3380':{  
      'speed':2.5,
      'fire':{
         'ani':'cheer|stand',   
         'time':8000,                   
         'sound':'fire292',              
         'soundVolume':0.5,              
         'soundDelay':800,             
      },
      'bullet':{
         'res':'bang7072',           
         'resScale':0.95,
      },
      'hurt':{
         'injured':2,                 
         'forceO':30,              
         'special':'bang7072',      
         'specialScale':0.6,     
         'num':7, 		      
         'hitTime':400,

         'sound':'hit228',              
         'soundVolume':2,              
         'soundDelay':-800,             
      },
   },
   'eff3303':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|attack|stand',   
         'time':2500,                   
      },

      'hurt':{
         'injured':2,                 
         'forceR':30,   

         'num':2, 		      
         'special':'special292',      
         'specialScale':6.5,      
         'specialSpeed':0.8,      
         'hitTime':600,                 
         'endTime':2000,                 
         'forceS':30,  

         'sound':'hit232',              
         'soundVolume':2,              
         'soundDelay':-500,             
      },

   },
   'eff3322_0':{  
      'bullet':{
         'res':'bullet223',           
         'resScale':0.8,       
         'resX':-2,            
         'resZ':8,            
      },
   },
   'eff3322':{  
      'fire':{
         'res':'fire275',              
         'ani':'cheer|stand',   
         'time':500,                   
      },
      'hurt':{
         'res':'fire275',              
         'resScale':2.5,
         'resAdd':2,
         'endTime':500,                 

         'sound':'fire095',             
         'soundVolume':0.8,              
         'soundDelay':-800,            
      },

   },





   'eff7022':{  
      'speed':6.5,                       
      'fire':{
         'time':800,                   
         'sound':'fire280',              
         'soundVolume':0.7,              
         'soundDelay':800,             
      },
      'move':{
         'stick':'stick204',           
         'stickAdd':2, 
      },
      'bang':{
         'res':'bang7022',             
         'time':300,                  
         'sound':'hit7022',              
         'soundVolume':0.5,              
      },
      'hurt':{
         'res':'hit7022',              
         'resRota':20,                
         'injured':2,                 
         'forceO':30,                
         'shock':200, 
         'hitTime':400,
      },

   },

   'eff7052':{  
      'speed':6.5,
      'fire':{
         'time':800,                   
         'sound':'fire280',              
         'soundVolume':0.7,              
         'soundDelay':800,             
      },
      'move':{
         'endTime':2200,              
      },
      'bang':{
         'time':800,                  
      },
      'hurt':{
         'res':'hit7052',              
         'injured':2,                 
         'forceO':25,                
         'forceY':25,
         'special':'special7052',      
         'num':2, 		      
         'time':200,                 
         'specialRndDelay':700,  
         'shock':800, 
         'hitTime':2000,

         'sound':'hit7802',              
         'soundVolume':2,              
         'soundDelay':-200,             
      },
   },

   'eff7072':{  
      'speed':6.5,
      'move':{
         'endTime':2400,              
      },
      'fire':{
         'ani':'cheer|stand',   
         'time':8000,                   
         'sound':'fire280',              
         'soundVolume':0.7,              
         'soundDelay':800,             
      },
      'bang':{
         'res':'bang7072',             
         'time':2200,                  

         'sound':'hit228',              
         'soundVolume':2,              
         'soundDelay':-800,             
      },
      'hurt':{
         'res':'hit233',              
         'resRota':20,                
         'injured':2,                 
         'forceO':30,                
         'shock':200, 
         'hitTime':400,
      },

   },

   'eff7082':{  
      'speed':6.5,                       
      'fire':{
         'time':800,                   
         'sound':'fire280',              
         'soundVolume':0.7,              
         'soundDelay':800,             
      },
      'move':{
         'endTime':275000,              
      },
      'bang':{
         'res':'bang7082',             
         'time':75000,                  
      },
      'hurt':{
         'res':'hit7082',              
         'injured':2,                 
         'forceO':25,                
         'forceY':25,
         'time':200,                 
         'specialRndDelay':700,  
         'shock':800, 
         'hitTime':8000,

         'sound':'hit7082',              
         'soundVolume':2,              
         'soundDelay':-800,             
      },

   },

   'eff7092':{  
      'speed':6.5,                       
      'fire':{
         'time':800,                   
         'sound':'fire280',              
         'soundVolume':0.7,              
         'soundDelay':800,             
      },
      'move':{
         'stick':'stick204',           
         'stickAdd':2, 
      },
      'bang':{
         'res':'bang7092',             
         'resAdd':2,  
         'time':400,                  

         'sound':'hit7092',              
         'soundVolume':2,              
         'soundDelay':0,             
      },
      'hurt':{
         'res':'hit228',              
         'resRota':20,                
         'injured':2,                 
         'forceO':30,                
         'shock':200, 
         'hitTime':400,
      },

   },

   'eff7802':{  
      'speed':6.5,                       
      'fire':{
         'time':800,                   
         'sound':'fire280',              
         'soundVolume':0.7,              
         'soundDelay':800,             
      },
      'move':{
         'endTime':275000,              
      },
      'bang':{
         'res':'bang7802',             
         'time':0,                  

         'sound':'hit7802',              
         'soundVolume':2,              
         'soundDelay':-200,             
      },
      'hurt':{
         'res':'hit7802',              
         'injured':2,                 
         'forceR':25,                
         'forceY':25,
         'time':400,                 
         'shock':800, 
         'hitTime':0,
      },

   },


   'eff7232':{  
      'speed':6.5,
      'move':{
         'endTime':2200,              
      },
      'fire':{
         'ani':'cheer|stand',   
         'time':2200,                   
         'sound':'fire280',              
         'soundVolume':0.7,              
         'soundDelay':800,             
      },
      'bang':{
         'res':'bang7232',             
         'time':2300,                  

         'sound':'hit230',              
         'soundVolume':0.8,              
         'soundDelay':-200,             
      },
      'hurt':{
         'res':'hit224',              
         'resRota':20,                
         'injured':2,                 
         'forceO':30,                
         'shock':200, 
         'hitTime':400,
      },

   },


   'eff7242':{  
      'speed':6.5,
      'move':{
         'endTime':2400,              
      },
      'fire':{
         'ani':'stand',   
         'time':750,                   
         'sound':'fire280',              
         'soundVolume':0.7,              
         'soundDelay':800,             
      },
      'bang':{
         'res':'bang7242',             
         'time':0,                  
      },
      'hurt':{
         'res':'hit7242',              
         'injured':2,                 
         'forceR':25,                
         'forceY':25,
         'time':300,                 
         'specialRndDelay':700,  
         'shock':800, 
         'hitTime':0,

         'sound':'hit229',              
         'soundVolume':0.4,              
         'soundDelay':-400,             
      },

   },


   'eff7262':{  
      'speed':6.5,                       
      'fire':{
         'time':800,                   
         'sound':'fire280',              
         'soundVolume':0.7,              
         'soundDelay':800,             
      },
      'move':{
      },
      'bang':{
         'res':'bang7262',             
         'time':8000,                  
      },
      'hurt':{
         'res':'hit7262',              
         'injured':2,                 
         'forceR':25,                
         'forceY':25,
         'time':300,                 
         'specialRndDelay':700,  
         'shock':800, 
         'hitTime':0,
         'sound':'hit7262',              
         'soundVolume':2,              
         'soundDelay':-800,             
      },

   },


   'eff7272':{  
      'speed':6.5,                       
      'fire':{
         'time':800,                   
         'sound':'fire280',              
         'soundVolume':0.7,              
         'soundDelay':800,             
      },
      'move':{
         'endTime':275000,              
      },
      'bang':{
         'res':'bang7272',             
         'time':2500,                  

         'sound':'hit7272',              
         'soundVolume':0.9,              
         'soundDelay':500,             
      },
      'hurt':{
         
         'injured':2,                 
         'forceO':45,                
         'forceY':25,
         'time':0,                 
         'specialRndDelay':700,  
         'shock':800, 
         'hitTime':0,
      },

   },


   'eff7282':{     
      'speed':6.5,                       
      'fire':{
         'time':800,                   
         'sound':'fire280',              
         'soundVolume':0.7,              
         'soundDelay':800,             
      },
      'move':{
      },
      'bang':{
         'res':'bang7282',             
         'time':800,                  

         'sound':'hit7802',              
         'soundVolume':0.9,              
         'soundDelay':-800,             
      },
      'hurt':{
         'res':'hit226',              
         'resAdd':2, 
         'resRota':20,                
         'injured':2,                 
         'forceO':80,                
         'shock':200, 
         'hitTime':450,
         'endTime':3000,
      },

   },


   'eff7202':{  
      'speed':6.5,                       
      'fire':{
         'time':800,                   
         'sound':'fire280',              
         'soundVolume':0.7,              
         'soundDelay':800,             
      },
      'move':{
      },
      'bang':{
         'time':-200,                  

         'sound':'hit7262',              
         'soundVolume':2,              
         'soundDelay':-800,             
      },
      'hurt':{
         'injured':2,                 
         'forceX':25,                
         'forceY':25,
         'special':'special7202',      
         'num':2, 		      
         'time':0,                 
         
         'shock':800, 
         'hitTime':2600,
      },
   },

   'eff7222':{  
      'fire':{
         'ani':'stand',   
         'time':750,                   
      },
      'bang':{
         'res':'bang7222',             
         'time':2500,                  
      },
      'hurt':{
         'res':'hit7222',              
         'injured':2,                 
         'forceO':45,                
         'forceY':25,
         'time':0,                 
         'specialRndDelay':700,  
         'shock':800, 
         'hitTime':0,
      },

   },

   'eff7232':{  
      'speed':6.5,
      'move':{
         'endTime':2600,              
      },
      'fire':{
         'ani':'stand',   
         'time':750,                   
         'sound':'fire280',              
         'soundVolume':0.7,              
         'soundDelay':800,             
      },
      'bang':{
         'res':'bang7232',             
         'time':2500,                  
      },
      'hurt':{
         'res':'hit7232',              
         'injured':2,                 
         'forceO':45,                
         'forceY':25,
         'time':0,                 
         'specialRndDelay':700,  
         'shock':800, 
         'hitTime':0,

         'sound':'hit7082',              
         'soundVolume':2,              
         'soundDelay':-800,             
      },

   },



   'eff7242':{  
      'speed':6.5,                       
      'fire':{
         'time':800,                   
         'sound':'fire280',              
         'soundVolume':0.7,              
         'soundDelay':800,             
      },
      'move':{
         'endTime':2500,              
      },
      'bang':{
         'res':'bang7242',             
         'time':2000,                  
      },

      'hurt':{
         
         'injured':2,                 
         'forceO':45,                
         'forceY':25,

         'specialRndDelay':700,  
         'shock':800, 
         'time':0,                 
         'hitTime':0,
         'endTime':600,

         'sound':'hit7082',              
         'soundVolume':0.3,              
         'soundDelay':800,             
      },

   },
   'eff724a':{  
      'speed':6.5,                       
      'fire':{
         'res':'fire292',              
         'resAdd':2,
         'time':800,                   
         'sound':'fire280',              
         'soundVolume':0.7,              
         'soundDelay':800,             
      },
      'move':{
         'endTime':2200,              
      },
      'bang':{
         'res':'bang724X',             
         'time':2200,                  

         'sound':'hit7092',              
         'soundVolume':2,              
         'soundDelay':800,             
      },

      'hurt':{
         'res':'hit7802',              
         'injured':2,                 
         'forceR':25,               
         'forceX':25,
         'shock':800, 
         'time':0,
         'hitTime':0,
         'endTime':600,
      },
   },
   'eff724b':{  
      'hurt':{
         'noNum':2,
         'res':'fire292',              
         'resAdd':2,
         'ani':'cheer|stand',  
         'endTime':500,                   
         'sound':'fire232',             
         'soundVolume':0.8,              
         'soundDelay':-800,            

      },
   },


   'eff7622':{  
      'speed':6.5,                       
      'fire':{
         'time':800,                   
         'sound':'fire280',              
         'soundVolume':0.7,              
         'soundDelay':800,             
      },
      'move':{
         'stick':'stick204',           
         'stickAdd':2, 
         'endTime':2400,              
      },
      'bang':{
         'res':'bang7622',             
         'time':275000,                  
      },
      'hurt':{
         'res':'hit226',              
         'resRota':20,                
         'injured':2,                 
         'forceO':30,                
         'shock':200, 
         'hitTime':400,

         'sound':'hit227',              
         'soundVolume':0.7,              
         'soundDelay':-800,             
      },

   },

   'eff762a':{  
      'speed':2,                       
      'fire':{
         'res':'fire225',              
         'resAdd':2, 
         'ani':'cheer',   
         'time':700,                   

         'sound':'fire280',              
         'soundVolume':0.7,              
         'soundDelay':800,             
      },

      'move':{
         'stick':'stick204',           
         'stickAdd':2, 
      },
      'hurt':{
         'special':'bang7022',      
         
         'num':2, 		      
         'time':200,                 
         'specialRndDelay':800,  

         'injured':2,                 
         'forceO':30,               
         'shock':200, 
         'hitTime':400,

         'sound':'hit7022',              
         'soundVolume':2,              
         'soundDelay':-800,             
      },

   },

   'eff786a':{  
      'noHurt':2,
      'fire':{
         'time':400,                   
         'ani':'cheer|stand',                
         'sound':'fire095',             
         'soundVolume':0.8,              
         'soundDelay':200,            
      },
   },

   'eff794':{  
      'move':{
      },
      'speed':2,                       
      'fire':{
         'res':'fire225',              
         'resAdd':2, 
         'ani':'cheer',   
         'time':700,                   
      },
      'bang':{
         'res':'bang794',             
         'resAdd':2,
         'resSpeed':0.8,
         'time':75000,                  
         'sound':'hit228',              
         'soundDelay':0,             
      },
      'hurt':{
         'injured':2,                 
         'forceX':80,                
         'forceR':50,               
         'forceZ':200,      
         'landingBouncePer':0.5,                  
         'landingAni':'injured2|injured2|injured2|injured2|stand',                  
         'landingAniStateTime':'200|200|200|200|200',            
         'landingDelay':-500,                  
         'shock':300,                 

         'pauseTime':500,    
      },

   },

   'eff795':{  
      'noHurt':2,
      'fire':{
         'res':'fire206',              
         'resAdd':2,
         'resScale':3,
         'ani':'cheer|stand',                
         'time':300,                   

         'sound':'fire202',          
         'soundVolume':0.5,             
         'soundDelay':-300,            
      },
      'speed':3,                       
      'bullet':{
         'res':'bullet220',       
         'resAdd':2,       
         'resScale':2.5,      
      },
      'hurt':{
         'special':'special795',      
         'specialAdd':2,  
         'specialSpeed':0.5,  
         'specialScale':0.5,                       
         'num':3, 		      
         'specialEdgeDelay':500,  
         'specialEdgeScale':-0.2,  
         'specialEdgeY':-800,  

         'injured':2,                 
         'hitTime':500,    

         'sound':'fire292',              
         'soundVolume':0.3,              
         'soundDelay':-500,             
      },
   },
   'eff795a':{  
      'noHurt':0,
      'fire':{
         'time':500,                   
         'soundVolume':2,              
         'soundDelay':-500,            
      },
      'bullet':{      
         'resScale':3,      
      },
      'hurt':{
         'specialSpeed':0.7,  
         'specialScale':0.7,                    
         'num':5, 		      
         'specialEdgeDelay':8000,    
         'specialEdgeScale':-0.2,  
         'specialEdgeY':0, 

         'soundVolume':0.7,              
         'soundDelay':-2200,             
 
         'hitTime':2200,    
         'endTime':600,    
      },
   },
   'eff795r':{  
      'fire':{
         'noFlip':2,
         'res':'fire206',              
         'resAdd':2,
         'resScale':3,

         'ani':'cheer|stand',   
         'time':8000,                   
      },
   },

   'eff797':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|attack|stand',   
         'time':2500,                   

         'sound':'fire226',              
         'soundDelay':2300,            
      },
      'bang':{
         'res':'bang797',             
         'resScale':0.9,             
         'resSpeed':0.8,
         'time':275000,                  
         'sound':'hitLoser2',              
         'soundDelay':2200,             
      },
      'hurt':{
         'injured':2,                 
         'forceR':800,               
         'forceZ':50,            
         'shock':800,                 
         'pauseTime':2600, 
      },
   },

   'eff798':{  
      'bannerTime':2600,                   
      'fire':{
         'res':'fire225',             
         'ani':'cheer|stand', 

         
         
         

         'time':500,                  
      },
      'bang':{
         'res':'bang798',             
         'resScale':0.9,      
         'resAlpha':0.6,
         'resSpeed':0.8,
         'time':8000,                  
         'sound':'fire244',              
         'soundDelay':-800,             
      },
   },
   'eff798a':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|stand',   
         'time':700,                   
      },
      'bang':{
         'res':'bang798a',             
         'resSpeed':0.8, 
         'resScale':2.5, 
 
         'sound':'hit232',              
         'soundVolume':2,              
         'soundDelay':-800,             

         'time':2500,                  
      },
      'hurt':{
         'injured':2,                 
         'forceR':30,                
         'forceZ':200,
         'landingBouncePer':0.5,                  
         'landingAni':'injured2|injured2|injured2|injured2|stand',                  
         'landingAniStateTime':'200|200|200|200|200',            
         'landingDelay':-500,                  
         'shock':200, 
         'endTime':8000,
         'pauseTime':500,    
      },
   },


   'eff7700':{  
      'bannerTime':2600,                   
      'fire':{
         'res':'fire206',   
         'resAdd':2,        
         'resScale':5,     
         'resSpeed':0.3,     
         'ani':'cheer|stand', 
         'time':500,    
      },
      'speed':2,                       
      'bullet':{
         'res':'bullet223',       
         'resAdd':2,       
         'resScale':3,  
         'resAlpha':0.8,  
         'resHSB':[0.85,2,2],   
         
         
      },
      'hurt':{
         'injured':2,                 
         'forceR':50,   

         'num':7, 		      
         'special':'special7700',      
         'specialX':-30,    
         'specialAdd':2,    
         'specialScale':0.5,  
         'specialRndDelay':8000,          
         'shock':50,

         'hitTime':300,   
         'pauseTime':500, 

         'sound':'hit232',              
         'soundVolume':2,              
         'soundDelay':-500,             
      },

   },

   'eff7700a':{  
      'fire':{
         'res':'fire206',   
         'resAdd':2,        
         'resScale':4,     
         'resSpeed':0.2,     
         'ani':'cheer|stand', 
         'time':500,    
         'noFlip':2,               

         'sound':'fire0005',              
         'soundVolume':2,              
         'soundDelay':-300,            

      },
      'hurt':{
         'res':'fire206',              
         'resAdd':2,
         'resScale':2.2,  
         'resSpeed':0.3,   
         'resAlpha':0.5,  

      },

   },




   'effG800':{  
      'bang':{
         'res':'bang7802',             
         'resScale':2.2,          
         'resSpeed':2.5,         
         'resAdd':2,     
         'resHSB':[0.2,2,2],  
         'time':800,                  

         'sound':'hit7802',              
         'soundVolume':2,              
         'soundDelay':-200,             
      },
      'hurt':{
         'injured':2,            
         'forceR':25,              
         'shock':800, 
         'pauseTime':500, 
      },

   },

   'effG802':{  
      'noHurt':2,
      'bang':{
         'res':'bang798',             
         'resAdd':2,
         'resSpeed':6.5,
         'resScale':2.05,
         'resAlpha':0.6,
         'resHSB':[0.5,2,2],  
 
         'time':800,                  

         'sound':'fire222',              
         'soundVolume':0.5,              
         'soundDelay':-800,             
      },
      'hurt':{
         'injured':-2,            
         'pauseTime':-500, 
      },
   },
   'effG802_2':{  
      'bang':{
         'res':'bangG802',             
         'resScale':2.4,          
         'resSpeed':0.8,         
         'resAdd':2,     

         'res2':'bangG802',             
         'res2Scale':2.2,          
         'res2Speed':0.8,   
         'res2Delay':200,   
         
         
         

         'time':8000,                  

         'sound':'hit7092',              
         'soundVolume':0.3,              
         'soundDelay':800,             
      },
      'hurt':{
         'special':'bang786a',      
         'specialScale':0.8,

         'num':3, 		      

         'injured':2,            
         'forceX':95,
         'forceR':80,               
         'shock':800, 
         'sound':'fire707a',              
         'soundVolume':2,              
         'soundDelay':-200,             

         'pauseTime':500, 
      },
   },
   'effAddRich':{  
      'noHurt':2,
      'fire':{
         'res':'buff226',              
         'resAdd':2,                   
         'resScale':2.5,                   
         'resHSB':[0.9,2,2.5], 
         'time':400,                   
         'sound':'fire707a',              
         'soundVolume':0.9,              
         'soundDelay':200,             
      },
   },
   'effG802_2':{  
      'noHurt':2,
      'bang':{
         'time':800,                  
         'sound':'fire232',              
         'soundVolume':0.5,              
         'soundDelay':800,             
      },
      'hurt':{
         'res':'fire292', 
         'resScale':2.5,          
         'resSpeed':0.5,         
         'resAdd':2,     
       
         'injured':-2,            
         'pauseTime':500, 
      },
   },

   'effG802':{  
      'hurt':{
         'res':'equipH000',              
         'resScale':0.8,              
         'resAdd':2,
         'resAlpha':0.2,
         'resSpeed':0.7,
         
         'injured':-2,   
         'pauseTime':500, 

         'sound':'fire240',         
         'soundVolume':0.6,   
      },
   },
   'effG802_4':{  
      'fire':{
         'time':75000,                   
      },
      'bang':{
         'res':'bangG802',             
         'resAdd':2,    
         'resAlpha':0.5,              
         'resSpeed':2.2,

         'res2':'bangG802',             
         'res2Speed':2.2,

         'time':75000,                  

         'sound':'hit222',              
         'soundVolume':0.4,              
         'soundDelay':75000,             
      },
      'hurt':{
         'injured':2,                 
         'forceO':30,                
         'shock':800, 

         'sound':'hit004',              
         'soundVolume':0.4,              
         'soundDelay':-200,             
      },
   },


   'effG803':{  
      
      'bang':{
         'res':'bangTypeD2',            
         'resSpeed':2.2,  
         'resFlip':2,        

         'time':2500,                  
         'sound':'beastTypeD2',              
         'soundVolume':0.8,              
         'soundDelay':800,             
      },
      'hurt':{
         'injured':-2,                

         'res':'fire272',              
         'resAdd':2,
         'resSpeed':0.8,

         'hitTime':400,

         'sound':'fire232',             
         'soundVolume':0.8,              
         'soundDelay':-800,            

         'pauseTime':200, 
      },
   },
   'effG803_2':{  
      'hurt':{
         'injured':-2,                

         'res':'fire272',              
         'resAdd':2,
         'resSpeed':0.8,
         'resHSB':[0.8,2,2], 

         'hitTime':400,

         'sound':'fire232',             
         'soundVolume':0.8,              
         'soundDelay':-800,            

         'pauseTime':200, 
      },
   },


   'effG804':{  
      'noHurt':2,
      'bang':{
         'res':'bang798',             
         
         'resSpeed':2.5,
         'resScale':0.8,
         'resAlpha':0.5,
         
         'time':800,                  

         'sound':'hit230',              
         'soundVolume':0.3,              
         'soundDelay':-200,             
      },
      'hurt':{
         'injured':2,   
         'pauseTime':800,          
      },
   },
   'effG804_2':{  
      'bang':{
         'res':'bangG804',             
         'resScale':2.3,    
         'resAlpha':0.5,          
         
         
         'resZ':-50,  
         'resX':-50, 
         'resAdd':2,    

         'res2':'bangG804',             
         'res2Scale':2.3,          
         
         
         'res2Z':-50,  
         'res2X':-50,  
         
         

         'time':275000,                  

         'sound':'hit0080',              
         'soundVolume':2,              
         'soundDelay':800,             
      },
      'hurt':{
         'injured':2,            
         'forceR':25,              
         'shock':800, 
         'pauseTime':500, 
      },
   },
   'effAddAbstain':{  
      'noHurt':2,
      'bang':{
         'res':'bang789',             
         'resScale':6.5,          
         'resX':800,    
         'resSpeed':2,          
         'resHSB':[0.5,2,2.5],  

         'time':300,                  

         'sound':'fire292',              
         'soundVolume':0.5,              
         'soundDelay':-200,             
      },
      'hurt':{
         'res':'buff226',              
         'resAdd':2,                   
         'resScale':2.5,              
         'resAlpha':0.5,              
         'resHSB':[0.45,2,2], 
         'time':400,                   
         'sound':'fire232',              
         'soundVolume':0.5,              
         'soundDelay':200,             
      },
   },
   'effG804_2':{  
      'noHurt':2,
      'bang':{
         'time':800,                  
         'sound':'fire232',              
         'soundVolume':0.5,              
         'soundDelay':800,             
      },
      'hurt':{
         'res':'fire275', 
         'resScale':2.5,          
         'resSpeed':0.4,         
         'resAdd':2,     
       
         'injured':-2,            
         'pauseTime':500, 
      },
   },


   'effWeather':{  
      'noHurt':2,
      'bang':{
         'res':'fire207',             
         'resScale':20,          
         'resSpeed':0.2,         
         'resHSB':[0.2,2,2],    
         'resAdd':2,    
         'resAlpha':0.6,    

         'time':500,                  
      },
      'hurt':{
         'injured':-2,            
         'pauseTime':500, 
      },
   },
   'effGSummon':{  
      'bang':{
         'res':'bangS802',       
         'resAdd':2,    
         'resScale':2.2,    
         'resSpeed':0.6, 
         'resAlpha':0.8,  
         'resDelay':500,    

         'res2':'bangS802',  
         'res2Speed':0.7,         
         'res2Scale':2.2,   
         

         'time':2400,      

         'sound':'fire0002',             
         'soundVolume':0.3,              
         'soundDelay':800,            
      },
      'hurt':{
         'injured':-2,                

         'res':'fire272',              
         'resScale':2,         
         'resSpeed':0.8,        
         'resAlpha':0.5,   
         'resAdd':2,
         'resHSB':[0.8,2,2], 

         'hitTime':600,

         'sound':'fire232',             
         'soundVolume':0.8,              
         'soundDelay':-800,            

         'pauseTime':75000, 
      },
   },



   'effG200':{  
      'bang':{
         'res':'bangG200',             
         'resSpeed':0.6,       
         'resScale':2.8,       
         'time':2300,                  

         'sound':'fire208',           
         'soundVolume':2,            
         'soundDelay':800,   
      },
      'hurt':{
         
         
         
         
         
         

         
         
         
         
         

         

         'injured':2,                 
         'hitTime':80,

         'sound':'hit0002',           
         'soundVolume':2,            
         'soundDelay':800,   
   
         'forceO':50,     
         'forceR':30,           
         'shock':50, 
         'pauseTime':500, 
      },
   },
   'effG202':{  
      'bang':{
         'res':'bangTypeK2',             
         'resZ':20,    
         'resX':-50,         
         'resSpeed':2.3,       
         'time':2200,                  

         'sound':'beastTypeK2_',              
         'soundDelay':800,             
      },
      'hurt':{
         'injured':2,                 
         'forceO':20,               
         'hitTime':500,
         'shock':800, 
         'pauseTime':500,
      },
   },
   'effG202':{  
      'bang':{
         'res':'bangG202',             
         'resSpeed':0.9,       
         'resScale':2.5,       
         'time':75000,                  

         'sound':'hit0002',           
         'soundVolume':0.5,            
         'soundDelay':-800,   
      },
      'hurt':{
         'special':'bang250',      
         'specialHSB':[0.7,0.7,2],    
         'specialScale':0.8,
         'specialRndScale':0.2,
         'specialRndDelay':500,          

         'num':3, 		      

         'injured':2,                 
         'hitTime':800,

         'sound':'hit0024',           
         'soundVolume':2,            
         'soundDelay':-800,      

         'soundOnce':'hit227',           
         'soundOnceVolume':0.6,            
         'soundOnceDelay':-2400,            
         
         'forceR':25,              
         'shock':50, 
         'pauseTime':500, 


      },
   },
   'effG203':{  
      'hurt':{
         'special':'bang7072',      
         'specialAdd':2,     
         
         'specialAlpha':0.3, 
         'specialScale':2.5,
         'specialRndDelay':500,          
         'specialEdgeScale':-0.5,           
         'specialEvenX':-20,           
         'specialRndY':80,      

         'special2':'bang7072',      
         'special2HSB':[0.2,0.7,2],   
         'special2Alpha':0.3, 
         'special2Scale':2.5,
         'special2RndDelay':500,          
         'special2EdgeScale':-0.5,           
         'special2EvenX':-20,           
         'special2RndY':80,      

         'num':5, 		      

         'injured':2,                 
         'hitTime':2500,

         'sound':'hit0004',           
         'soundVolume':2,            
         'soundDelay':-2500,      

         'soundOnce':'hit0006',           
         'soundOnceVolume':0.6,            
         'soundOnceDelay':-500,            
         
         'forceR':25,              
         'shock':50, 
         'pauseTime':500, 
      },
   },
   'effG204':{  
      'hurt':{
         'special':'bang7242',      
         'specialAdd':2, 
         
         
         'specialAlpha':0.5, 
         'specialScale':2.5,
         'specialRndDelay':500,          
         'specialEdgeScale':-0.5,           
         'specialEvenX':-20,           
         'specialRndY':80,      

         'special2':'bang727r',      
         
         'special2Speed':2.2, 
         'special2Alpha':0.8, 
         'special2Scale':2.5,
         'special2RndDelay':500,          
         'special2EdgeScale':-0.5,           
         'special2EvenX':-20,           
         'special2RndY':80,      

         'num':2, 		      

         'injured':2,                 
         'hitTime':2500,

         'sound':'hit229',           
         'soundVolume':0.5,            
         'soundDelay':-2500,      

         'soundOnce':'hit7242',           
         'soundOnceVolume':2,            
         'soundOnceDelay':-300,            
         
         'forceR':25,              
         'shock':50, 
         'pauseTime':500, 
      },
   },

   'effG300':{  
      'hurt':{
         'injured':2,                 
         'forceR':30,                
         'special':'bangH22',      
         'specialAdd':2,  
         'specialSpeed':0.7,  
         'specialScale':0.9,    
         'specialRndDelay':300,          
         'specialHSB':[0.2,2,0.7],   

         'num':4, 		      

         'hitTime':2400,    
         'pauseTime':400,    
         'time':200,                 
         'forceR':25,  
         'shock':30,

         'sound':'hit7082',              
         'soundVolume':2,              
         'soundDelay':-800,             
      },
   },

   'effG300_':{  
      'fire':{
         'ani':'cheer|stand',          
         'time':800,                   

         'sound':'fire232',            
         'soundVolume':0.8,            
         'soundDelay':-800,            
      },
      'hurt':{
         'res':'fire272',              
         'resAdd':2,
         'resHSB':[0.5,2,2],   
         'injured':-2,

         'sound':'fire232',             
         'soundVolume':0.8,              
         'soundDelay':-800,            
      },
   },

   'effG302':{  
      'hurt':{
         'special':'fire863',      
         'specialAdd':2,  
         'specialSpeed':0.6,  
         'specialScale':0.9,    
         'specialX':-950,              
         'specialHSB':[0.6,2,2], 
         'num':3, 		     

         'injured':2,               
         'hitTime':2200,    

         'sound':'fire229',              
         'soundVolume':0.5,              
         'soundDelay':-200,             

         'soundOnce':'sound',              
         'soundOnceVolume':0.7,              
         'soundOnceDelay':8000,             
      },
   },

   'effG302_':{  
      'hurt':{
         'def':'fire202',         
         'defScale':2.3,
         'defSpeed':0.3, 
         'defAdd':2, 
         'defHSB':[0.4,2,2], 

         'forceDef':8000000,            
         'defSound':'fire202',              
         'defSoundVolume':0.7,              
         'defSoundDelay':300,            
      },

   },

   'effG302':{  
      'fire':{
         'sound':'hit0003',              
         'soundVolume':0.4,              
         'soundDelay':-800,             
      },
      'hurt':{
         'special':'special795',      
         'specialAdd':2,  
         'specialSpeed':0.7,  
         'specialScale':0.8,               

         'specialEdgeDelay':200,  
         'specialEdgeScale':-0.2,  
         'specialEdgeY':-20,  
         'specialHSB':[0.25,2,0.5], 
         'num':4, 		     

         'special2':'specialG302',    
         'special2Scale':0.7, 
         'special2Alpha':0.8, 
         
         
         'special2Delay':600, 

         'res':'hit7022',    
         'resHSB':[0.65,0.6,0.2],  
         'resScale':2.5, 
         'resAdd':2, 
         'resAlpha':0.8, 
         'resRndRota':75,              

         'injured':2,               
         'hitTime':2600,    
         'pauseTime':500,  

         'forceS':50,  
         'shock':50,

         'soundOnce':'hit0025',              
         'soundOnceVolume':0.5,              
         'soundOnceDelay':-800,             
      },
   },
   'effG302_2':{  
      'fire':{
         'time':75000,                   

         'sound':'fire292',              
         'soundVolume':0.5,              
         'soundDelay':700,             
      },
      'hurt':{
         'special':'bangG302',      
         
         'specialSpeed':0.8,  
         'specialScale':2.2,               

         'specialEdgeDelay':200,  
         'specialEdgeScale':-0.3,  
         'specialEdgeY':-20,  
         
         'num':3, 		     

         'injured':2,               
         'hitTime':2200,    
         'pauseTime':600,  

         'forceS':-40,  

      },
   },

   'effG302_':{  
      'fire':{
         'time':75000,                   
      },
      'hurt':{
         'special':'special005',      
         'specialSpeed':0.8,    
         'num':3, 		      
         'hitTime':8000,
         'time':2,

         'sound':'fire232',             
         'soundVolume':0.8,              
         'soundDelay':-800,            
      },
   },

   'effG303':{  
      'bang':{
         'res':'bangG303',             
         'resSpeed':2.5,       
         'resScale':2.5,       

         'res2':'bangG303_2',      
         'res2Add':2, 
         'res2Scale':2.2,
         'res2Speed':2.2,
         'res2Delay':650,

         'time':8000,                  

         'sound':'hit206',           
         'soundVolume':0.5,            
         'soundDelay':-800,   
      },
      'hurt':{
         'injured':2,                 

         'pauseTime':2200,

         'forceO':60,                
         'forceZ':800,
         'landingBouncePer':0.5,                  
         'landingAni':'injured2|injured2|injured2|injured2|stand',                  
         'landingAniStateTime':'200|200|200|200|200',            
         'landingDelay':-500,                  
         'shock':50, 

         'sound':'hitLoser2',              
         'soundVolume':2,              
         'soundDelay':-800,             
      },
   },
   'effG303_':{  
      'fire':{
         'time':75000,                   
      },
      'hurt':{
         'injured':-2,

         'pauseTime':500,
         'sound':'fire095',             
         'soundVolume':2,              
         'soundDelay':-800,            
      },
   },
   'effG303_2':{  
      'fire':{
         'time':75000,                   
      },
      'hurt':{
         'injured':2,                 
         'special':'bangH02',      
         
         'specialScale':0.7,
         'specialSpeed':0.8,
         'specialRndX':50,
         'specialRndY':50,
         'specialHSB':[0,0.6,0.9], 
         'specialRndDelay':400,          
         'num':25, 		      

         'hitTime':75000,
         'time':200,                 
         'pauseTime':8000,

         'forceR':30,                
         'forceZ':200,
         'landingBouncePer':0.5,                  
         'landingAni':'injured2|injured2|injured2|injured2|stand',                  
         'landingAniStateTime':'200|200|200|200|200',            
         'landingDelay':-500,                  
         'shock':50, 

         'sound':'hit222',              
         'soundVolume':0.2,              
         'soundDelay':-400,             

         'soundOnce':'hit222',              
         'soundOnceVolume':0.4,              
         'soundOnceDelay':-200,             
      },
   },
   'effG303_4':{  
      'fire':{
         'time':75000,                   
      },
      'bang':{
         'res':'special860',             
         
         'resSpeed':0.6,       
         'resScale':2.2,   
         'resHSB':[0.9,0.8,2],     
         'time':600,                  

         'sound':'hit222',           
         'soundVolume':0.7,            
         'soundDelay':400,   
      },
      'hurt':{
         'injured':2,                 
         'pauseTime':2500,

         'forceO':-250,                
         'forceZ':250,
         'landingBouncePer':0.5,                  
         'landingAni':'injured2|injured2|injured2|injured2|stand',                  
         'landingAniStateTime':'200|200|200|200|200',            
         'landingDelay':-500,                  
         'shock':50, 

         'sound':'hit222',              
         'soundVolume':0.2,              
         'soundDelay':800,             
      },
   },

   'effG304':{  
      'hurt':{
         'special':'bang798a',      
         'specialAdd':2, 
         'specialAlpha':0.5, 
         
         'specialScale':2.4, 
         'specialZ':800, 
         'specialEdgeScale':-0.2,           

         'special2':'bang798a',      
         'special2HSB':[0.9,2,2], 
         'special2Scale':2.5, 
         'special2EdgeScale':-0.2,           
         'num':3, 		      

         'injured':2,                 
         'hitTime':2500,

         'sound':'hit232',           
         'soundVolume':0.9,            
         'soundDelay':-2200,               
         
         'forceR':25,              
         'shock':50, 
         'pauseTime':500, 
      },
   },
   'effG304_':{  
      'fire':{
         'time':75000,                   
      },
      'hurt':{
         'special':'special005',      
         'specialSpeed':0.8,   
         'specialScale':2.2,   
         'specialHSB':[0.7,2,2], 
         'num':2, 		      
         'hitTime':8000,
         'time':2,

         'sound':'fire232',             
         'soundVolume':0.8,              
         'soundDelay':-800,            
      },
   },

   'effG304_2':{  
      'fire':{
         'time':75000,                   
         'sound':'hit0080',           
         'soundVolume':2,            
         'soundDelay':-800,   
      },
      'bang':{
         'res':'special770a',             
         'resAdd':2,      
         'resSpeed':0.8,       
         'resScale':3,   
         'resHSB':[0.5,2,2],     
         'time':400,                 
      },
      'hurt':{
         'injured':2,                 
         'special':'bang232',      
         'specialAdd':2, 
         'specialScale':0.5,
         'specialSpeed':2.5,
         'specialRndX':50,
         'specialRndY':50,
         'specialHSB':[0.6,2,2], 
         'specialRndDelay':200,          
         'num':5, 		      

         'forceO':25,      
         'forceR':25,   
         'shock':50,  

         'hitTime':200,
         'time':200,                 
         'pauseTime':8000,

         'sound':'hit232',              
         'soundVolume':0.2,              
         'soundDelay':-75000,             
      },
   },



   'effCut':{  
      'hurt':{
         'res':'hit203',              
         'resScale':2,      
         'resAdd':2,      
      },
   },
   'effProtectHp':{  
      'noHurt':2,            
      'hurt':{
         'res':'fire272',              
         'resScale':2,      
         'resAdd':2,
      },
   },


   'effBossAtk':{  
      'bannerTime':2000,                   
      'fire':{
         'res':'fire229',  
         
         'resAdd':2,        
         'resScale':3.5,     
         'resSpeed':0.4,    
         'aniSpeed':2.2,            
      
         'ani':'attack|stand', 

         
         
         

         'time':75000,                  
      },
      'bang':{
         'res':'bangS280',             
         'resScale':0.8,    
         'resAdd':2,  
         'resHSB':[0.7,2,0.5],   
         'resSpeed':0.6,

         'res2':'bang798',             
         'res2Scale':0.8,      
         'res2Alpha':0.4,        
         'res2Speed':0.7,
         'res2HSB':[0.85,2,2],   

         'time':75000,                  
         'sound':'fire244',              
         'soundDelay':-800,             
      },
      'hurt':{
         'forceR':30,
         'pauseTime':75000, 
      },
   },
   'effBossMad':{  
      'bannerTime':3000,                   
      'fire':{
         'res':'fire292', 
         
         'resAdd':2,        
         'resScale':7,     
         'resSpeed':0.2,    

         'res2':'fire292', 
         
         'res2Scale':8,     
         'res2Speed':0.2,    
      
         'ani':'cheer|attack|stand', 
         'aniSpeed':6.5,            

         
         
         

         'time':2500,                  
      },
      'bang':{
         'res':'bang7242',             
         'resScale':2.3,      
         'resAlpha':0.9,
         'resSpeed':0.8,
         'resHSB':[0.2,2,2],  

         'res2':'bang252',             
         'res2Delay':500, 
         'res2Scale':2.3,      
         'res2Add':2,
         'res2Speed':0.7,
         'res2Alpha':0.9,
         'res2HSB':[0.4,2,2],  

         'time':8000,                  
         'sound':'hit229',              
         'soundDelay':-800,             
      },
      'hurt':{
         'injured':2,                 
         'forceO':50,                
         'forceZ':250,
         'landingBouncePer':0.5,                  
         'landingAni':'injured2|injured2|injured2|injured2|stand',                  
         'landingAniStateTime':'200|200|200|200|200',            
         'landingDelay':-500,                  

         'time':0,                 
         'shock':800, 
         'endTime':2000,
      },
   },


   'effStop':{  
      'bannerTime':2200,                   
      'fire':{
         'ani':'cheer|stand',   
         'time':400,                   
      },
   },
   'effNull':{
      'noHurt':2,
   },

   'effBanHeroSkill':{  
      'noHurt':2,
      'fire':{
	 'noFlip':2,               
         'res':'buff232',              
         'resScale':4,
         'resSpeed':0.8,
         'sound':'hit004',              
         'soundVolume':0.5,              
         'soundDelay':800,             
         'time':400,                  
      },
      
      
      
   },

   'effNpc2':{  
      'fire':{
         'res':'fire222',             
         'resAdd':2, 
         'resSpeed':0.5, 
         'resScale':4,              

         'ani':'cheer|attack|stand',  
         'time':2200,                  

         'sound':'fire232',         
         'soundVolume':0.6,            
         'soundDelay':200,                  
      },
      'bang':{
         'res':'bang7232', 
         'resX':-800,    
         'resScale':2.2,
         'resSpeed':2.5,     
         'resAdd':2,       
         'time':400,               

         'sound':'hit228',    
         'soundVolume':0.6,       
         'soundDelay':800,           
      },
      'hurt':{
                
         'forceR':30,               
         'shock':50, 
         'pauseTime':8000,
      },
   },
   'effNpc2':{  
      'fire':{
         'res':'fire292',           
         'resAdd':2, 
         'resSpeed':0.5, 
         'resScale':5, 
         

         'ani':'cheer|attack|stand',   
         'time':2200,                  

         'sound':'fire232',            
         'soundVolume':2,             
         'soundDelay':200,            
      },
      'bang':{
         'res':'bangTypeC2',     
         'resAdd':2,
         'resX':-800, 
         'resScale':2.3,
         'resSpeed':2.5,

         'time':400,               

         'sound':'beastTypeI',         
         'soundVolume':0.7, 
         'soundDelay':800,         
      },
      'hurt':{     
         'injured':2,            
         'forceO':40, 
         'shock':800, 
         'endTime':8000,
      },
   },




   'effLoser':{  
      'canRevive':2,      
      'fire':{
         'res':'fire225',              
         'resAdd':2,
         'ani':'cheer',                
         'time':700,                   

         'sound':'fire280',              
         'soundVolume':0.5,              
         'soundDelay':800,             
      },
      'speed':6.5,                       
      'move':{
         'stick':'stick204',           
         'stickAdd':2, 
         'noBack':2,           
      },
      'hurt':{
         'res':'hit203',              
         'endTime':-300,               
      },
   },
   'effLoser0':{  
      'noReStand':2,      
      'hurt':{
         'special':'specialLose2',      
         'num':6, 		      
         'specialRndDelay':800,          
         'injured':2,                 
         'forceR':50,
         'endTime':200,

         'sound':'hitLoser0',              
         'soundVolume':0.8,              
         'soundDelay':-400,             
      },
   },
   'effLoser2':{  
      'noReStand':2,      
      'hurt':{
         'special':'specialLose0',      
         'num':6, 		      
         'specialRndDelay':800,          
         'injured':2,                 
         'forceR':50,
         'endTime':200,

         'sound':'hitLoser2',              
         'soundVolume':2,              
         'soundDelay':-300,             
      },
   },
   'effLoser2':{  
      'noReStand':2,      
      'hurt':{
         'special':'specialLose2',      
         'num':6, 		      
         'specialRndDelay':800,          
         'injured':2,                 
         'forceR':50,
         'endTime':200,

         'sound':'hitLoser2',              
         'soundVolume':2,              
         'soundDelay':-200,             
      },
   },


   'effDefault':{
      'speed':2,                       
      'move':{},
      'hurt':{
         'res':'hit802',              
      },
   },
   'eff_Faction':{  
      'speed':0.8,                      
      'move':{},
      'hurt':{
         'res':'hit800',              
         'resRota':25,  
         'sound':'hit0',              
         'soundDelay':-300,            
      },
   },

   'eff_0':{  
      'speed':2,                       
      'move':{},
      'hurt':{
         'res':'hit800',              
         'resRota':25,  
         'sound':'hit0',              
         'soundDelay':-300,            
      },
   },
   'eff_2':{  
      'speed':2.5,                       
      'move':{},
      'hurt':{
         'res':'hit802',              
         'resRota':25,  
         'sound':'hit2',              
         'soundVolume':0.6,              
         'soundDelay':-300,            
      },
   },
   'eff_2':{  
      'speed':2,                       
      'fire':{
         'sound':'fire2',              
         'soundVolume':0.5,              
         'soundDelay':200,            
         'time':200,                   
      },
      'bullet':{
         'res':'bullet802',           
         'gravity':2,                 
      },
      'hurt':{
         'res':'hit802',              
         'resRota':25,  
         'sound':'hit2',              
         'soundVolume':0.2,              
         'soundDelay':-800,            
      },
   },
   'eff_3':{  
      'speed':2.5,                       
      'fire':{
         'sound':'fire3',              
         'soundVolume':0.3,              
         'soundDelay':200,            
         'time':200,                   
      },
      'bullet':{
         'res':'bullet803',           
      },
      'hurt':{
         'res':'hit803',              
         'resAdd':2,
         'sound':'hit3',              
         'soundDelay':-800,            
      },
   },
   'effTower0':{  
      'noImg':2,
      'fire':{
         'time':8000,                   
      },
      'hurt':{
         'res':'hit800',              
         'injured':2,                 
         'forceX':95,                
         'forceY':25,
         'special':'effTower0',      
         'num':6, 		      
         'time':200,                 
         'shock':800, 
         'hitTime':700,
      },
   },
   'effTower2':{  
      'noImg':2,
      'fire':{
         'time':8000,                   
      },
      'hurt':{
         'special':'effTower2',      
         'num':6, 		      
         'specialRndDelay':800,          
         'injured':2,                 
         'forceR':50,
         'time':200,                 
         'shock':250, 
         'hitTime':8000,
      },
   },



   'effHero':{  
      'speed':2.5,                       
      'move':{
         'stick':'stick204',           
         'stickAdd':2, 
      },
      'bang':{
         'res':'bang7092',             
         'time':300,                  
      },
      'hurt':{
         'res':'hit7022',              
         'resRota':20,                
         'injured':2,                 
         'forceO':30,                
         'shock':200, 
         'hitTime':400,

         'sound':'hit7022',              
         'soundVolume':2,              
         'soundDelay':-800,             
      },

   },


   'effArmy0':{  
      'fire':{
         'res':'fire270',              
         'ani':'cheer|stand',                
         'time':700,                   
      },
   },
   'effArmy2':{  
      'speed':2, 
      'fire':{
         'res':'fire207',              
         'resAdd':2,
         'ani':'cheer|stand',                
         'time':8000,                   
      },
      'bullet':{
         'res':'bullet802',           
         'gravity':2,                 
      },
      'hurt':{
         'res':'hit802',              
         'resRota':25,  
      },
   },
   'effHero2':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|attack|stand',   
         'time':2700,                   
      },
      'hurt':{
         'res':'hit227',              
         'resRota':25,                
         'special':'special227',      
         'num':6, 		      
         'specialRndDelay':300,          
         'time':0,                  
         'hitTime':75000,               
         'forceX':50,                
         'forceY':50,                
         'shock':200,                 
      },

   },
   'effHero2':{  
      'fire':{
         'res':'fire225',              
         'ani':'cheer|attack|stand',   
         'time':2700,                   
      },
      'hurt':{
         'res':'hit227',              
         'resRota':25,                
         'special':'special227',      
         'num':6, 		      
         'specialRndDelay':300,          
         'time':200,                 
      },

   },
   'effAtk':{  
      'fire':{
         'res':'bang238',              
         'ani':'cheer|attack|stand',   
         'time':2700,                   
      },

   },
   'effDef':{  
      'hurt':{
         'def':'fire229',         
         'defAdd':2, 
         'ani':'injured2|injured2|stand',  
         'forceDef':8000000,            
      },

   },

   'effTest':{   
      'speed':2,                       
      'rndTime':8000,                  
      'rndDis':200,                    
      'fire':{
         'res':'fire225',              
         'resAdd':2,                   
         'sound':'fire2',              
         'soundVolume':2,              
         'soundDelay':-800,            
         'ani':'attack|cheer|stand',   
         'time':600,                   
         'rndTime':8000,               
      },
      'move':{
         'stick':'stick204',           
         'stickAdd':2,                 
         'atk':'hit207',               
         'atkAdd':2,                   
         'time':600,                   
         'ani':'attack|cheer|stand',   
         'endTime':600,                
      },
      'bullet':{
         'res':'bullet802',            
         'resAdd':2,                   
         'gravity':2,                  
         'stick':'stick226',           
      },
      'bang':{
         'res':'bang238',              
         'resAdd':2,                   
         'sound':'bang002',            
         'soundVolume':2,              
         'soundDelay':-800,            
         'time':75000,                   
      },
      'hurt':{
         'def':'fire202',              
         'defAdd':2,                   
         'defSound':'fire2',              
         'defSoundVolume':2,              
         'defSoundDelay':-800,            
         'ani':'injured2|cheer|stand',  

         'res':'hit800',               
         'resAdd':2,                   
         'resRota':25,                 
         'sound':'hit002',             
         'soundVolume':2,              
         'soundDelay':-800,            
         'injured':2,                  
         'forceX':50,                  
         'forceY':50,                  
         'forceF':50,                  
         'forceO':50,                  
         'forceS':50,                  
         'forceR':50,                  
         'forceDef':8000,              
         'shock':300,                  
         'special':'special222',       
         'specialAdd':2,               
         'specialRndDelay':300,           

         'num':6, 		       
         'time':75000,                   
         'hitTime':75000,                
         'endTime':275000,               
      },
   },




   'effBurn':{  
      'hurt':{
         'res':'buff226',              
         'injured':2,                 
         'sound':'hit226',              
      },
   },

   'effFire':{  
      'fire':{
         'res':'buff226',              
         'resAdd':2,                   
         'time':600,                   
         'sound':'hit226',              
      },
   },

   'effCure':{  
      'hurt':{
         'injured':-2,
         'res':'fire272',              
         'resAlpha':0.8,
         'resAdd':2,

         'sound':'fire232',             
         'soundVolume':0.8,              
         'soundDelay':-800,            
      },

   },
   'effSummon':{  
      'hurt':{
         'injured':-2,
         'res':'fire272',              
         'resAlpha':0.8,
         'resAdd':2,
         'resHSB':[0.7,2,2], 

         'sound':'fire232',             
         'soundVolume':0.8,              
         'soundDelay':-800,            
      },
   },





   'effMagma':{  
      'fire':{
         'res':'buff226',              
         'resAdd':2,                   
         'resScale':2.5,                   
         'resHSB':[0.3,2,2.5], 
         'time':600,                   
         'sound':'hit226',              
      },
   },


   'effFlee':{  
      'fire':{
         'res':'buff239',              
         'resAdd':2,                   
         'resScale':2.5,                   
         'time':600,                   
         'sound':'hit226',              
      },
   },
   'effElephant':{  
      'fire':{
         'res':'hit800',              
         'time':400,                   

         'sound':'hit222',             
         'soundVolume':0.5,              
         'soundDelay':-800,            
      },
   },




   'buffFire':{  
         'res':'buff226',   

         'fire':'buff226',  
         'fireAdd':2,      
         'fireScale':2,    
         'fireAlpha':0.6,    
         'fireSpeed':2.2,   
         
   },

   'buffHealing':{  
         'res':'fire272',
         'resScale':0.3,   
         'resX':-5,
         'resZ':-22,

         'fire':'fire272',  
         'fireAdd':2,      
         'fireScale':0.4,    
         'fireAlpha':0.6,    
         'fireSpeed':2.2,   
         'fireX':-5,  
         'fireZ':-22,     
   },
   'buffRelief':{  
         'res':'fire272',
         'resScale':0.3,   
         'resHSB':[0.7,2,2],
         'resX':5,
         'resZ':-22, 

         'fire':'fire272',  
         'fireHSB':[0.7,2,2], 
         'fireAdd':2,      
         'fireScale':0.4,    
         'fireAlpha':0.6,    
         'fireSpeed':2.2, 
         'fireX':5,  
         'fireZ':-22,           
   },



   'buffWeak':{  
         'res':'buff230',  

         'fire':'buff230',  
         'fireAdd':2,      
         'fireScale':2,    
         'fireAlpha':0.6,    
         'fireSpeed':0.8,   
         
   },

   'buffFlee':{  
         'res':'buff239', 
         

         'fire':'buff239',  
         'fireAdd':2,      
         'fireScale':2,    
         'fireAlpha':0.6,    
         'fireSpeed':2,   
         'fireZ':-80,             
   },

   'buffPoison':{  
         'res':'buff227',

         'fire':'buff227',  
         'fireAdd':2,      
         'fireScale':2,    
         'fireAlpha':0.6,    
         'fireSpeed':0.8,   
         
   },

   'buffPoisonous':{  
         'res':'buff227',
         'resScale':3, 
         'resAlpha':0.2,   
         'resAdd':2, 

         'resBg':2,
   },
   'buffStun':{  
         'res':'buff227',
    
         'fire':'buff227',  
         'fireAdd':2,      
         'fireScale':2,    
         'fireAlpha':0.6,    
         'fireSpeed':0.8,   
         'fireZ':-50,         
   },
   'buffFaction':{  
         'res':'buff228',  

         'fire':'buff228',  
         'fireAdd':2,      
         'fireScale':2,    
         'fireAlpha':0.6,    
         'fireSpeed':0.8,   
         'fireZ':-50,           
   },

   'buffBanArmyA2':{  
         'res':'buffSilent',
         'resScale':0.9,    

         'fire':'buffSilent',  
         'fireAdd':2,      
         'fireScale':2.2,    
         'fireAlpha':0.4,    
         'fireSpeed':2.5,   
         'fireZ':-5,           
   },
   'buffBanArmyB2':{  
         'res':'buffGloom',  
         'resScale':0.9,

         'fire':'buffGloom',  
         'fireAdd':2,      
         'fireScale':2.2,    
         'fireAlpha':0.4,    
         'fireSpeed':2.5,   
         'fireZ':-5,           
   },
   'buffBanHeroA2':{  
         'res':'buffSilent', 
         'resScale':2.2, 
         'resHSB':[0.9,2,2], 
         'resZ':25, 

         'fire':'buffSilent',  
         'fireAdd':2,      
         'fireScale':2.3,    
         'fireAlpha':0.4,    
         'fireSpeed':2.5,   
         'fireZ':30,           
   },



   'buffMorale':{  
         'res':'buff232',    
         'resSpeed':0.8,   
         'resScale':2,    

         'buffZPer':0,   

         'fire':'buff232',  
         'fireAdd':2,      
         'fireScale':2.5,    
         'fireAlpha':0.6,    
         'fireSpeed':2,   
         'fireZ':-5,  
   },

   'equip252':{  
         'res':'buff252',   
         'resAdd':2, 
         
         
         

         'buffZPer':0,   

         'fire':'buff252', 
         'fireAdd':2,      
         'fireScale':2.2,    
         'fireAlpha':0.6,    
         'fireSpeed':2,   
         'fireZ':-5,  
   },
   'equip282':{  
         'res':'buffH22',    
         'resSpeed':0.8,   
         'resScale':2,    
         'resHSB':[0.2,2,2], 

         'buffZPer':0,   

         'fire':'buffH22', 
         'fireHSB':[0.2,2,2],  
         'fireAdd':2,      
         'fireScale':2.5,    
         'fireAlpha':0.6,    
         'fireSpeed':2,   
         'fireZ':-5,  
   },


   'buffSlow':{  
         'res':'buff232',   

         'fire':'buff232',  
         'fireAdd':2,      
         'fireScale':2.5,    
         
         'fireSpeed':0.8,   
         'fireZ':-80,          
   },
   'buffAlert':{  
         'res':'buff233',   
         'buffZPer':0,   

         'fire':'buff233',  
         'fireAdd':2,      
         'fireScale':2.2,    
         'fireAlpha':0.6,    
         'fireSpeed':0.8,   
         'fireZ':-5,   
   
         'sound':'fire232',            
         'soundVolume':0.3,              
   },
   'buffBreak':{  
         'res':'buff002', 

         'fire':'buff002',  
         'fireAdd':2,      
         'fireScale':2.2,    
         'fireAlpha':0.6,    
         'fireSpeed':0.8,   
         'fireZ':-80,            
   },
   'buffBreak2':{  
         'res':'buff762a',           
         'resAdd':2,  
         
         'resAlpha':0.5,    

         'fire':'buff762a',  
         'fireAdd':2,      
         'fireScale':2.2,    
         'fireAlpha':0.6,    
         'fireSpeed':0.8,   
         'fireZ':-80, 
   },
   'buffFrozen':{  
         'res':'buffFrozen',   
         'resScale':0.9,    
         'resAlpha':0.7, 
         'resSpeed':0.5,  

         'fire':'buffFrozen',  
         'fireAdd':2,      
         'fireScale':2.2,    
         'fireAlpha':0.3,    
         'fireSpeed':0.8,   
         'fireZ':-80,          
   },
   'buffLure':{  
         'res':'buff795',   
         'resScale':2.2,    
         'resAlpha':0.8, 
         'resSpeed':0.5,  

         'fire':'buff795',  
         'fireAdd':2,      
         'fireScale':2.3,    
         'fireAlpha':0.6,    
         'fireSpeed':0.8,   
         'fireZ':-80,               
   },
   'buffDefeat':{  
         'res':'buff227',
         'resHSB':[0.5,2,2],  
    
         'fire':'buff227',  
         'resHSB':[0.5,2,2],  
         'fireAdd':2,      
         'fireScale':2,    
         'fireAlpha':0.6,    
         'fireSpeed':0.8,   
         'fireZ':-50,           
   },

   'buffDirty':{  
         'res':'buffDirty',   
         'resScale':2.5,
         'resBg':2,
         'resZ':-5,  
         'buffZPer':0,   

         'fire':'buffDirty',  
         'fireAdd':2,      
         'fireScale':2,    
         'fireAlpha':0.6,    
         'fireSpeed':2.2,   
         'fireBg':2,
         
   },

   'equip002':{  
         'res':'buff865',  
         'resAdd':2,       
         'resScale':2.2, 
         'resAlpha':0.3, 
         

         'fire':'buff865',  
         'fireAdd':2,      
         'fireScale':2.3,    
         'fireAlpha':0.3,    
         'fireSpeed':2.5,   
         

         'buffZPer':0,   
   },
   'buff095':{  
         'res':'buff095',  
         'resAdd':2,          
         

         'fire':'buff095',  
         'fireAdd':2,      
         'fireScale':2,    
         'fireAlpha':0.3,    
         'fireSpeed':2.8,   
         'fireZ':-50, 
   },
   'buff773a':{  
         'res':'buff776a',  
         'resAdd':2,          
         'fire':'buff776a',  
         'fireAdd':2,      
         'fireScale':2.5,    
         'fireAlpha':0.3,    
         'fireSpeed':2.5,   
         'fireZ':-50,  
   },

   'buff775a':{  
         'res':'buff775a',  
         'resAdd':2,   

         'fire':'buff775a',  
         'fireAdd':2,      
         'fireScale':2.2,    
         'fireAlpha':0.6,    
         'fireSpeed':0.8,   
         'fireZ':-80,        
   },
   'buff776a':{  
         'res':'buff776a',  
         'resAdd':2,          
         

         'fire':'buff776a',  
         'fireAdd':2,      
         'fireScale':2.5,    
         'fireAlpha':0.3,    
         'fireSpeed':2.5,   
         'fireZ':-50,  
   },
   'buff782r':{  
         'res':'buff782r',  
         'resScale':2, 

         'fire':'buff782r',  
         'fireAdd':2,      
         'fireScale':2.5,    
         'fireAlpha':0.5,    
         'fireSpeed':2.2,   

         'buffZPer':0,   
   },
   'buff786a':{  
         'res':'buff776a',  
         'resAdd':2,    
         'resHSB':[0.3,2,2],       
         

         'fire':'buff776a',  
         'fireHSB':[0.3,2,2],   
         'fireAdd':2,      
         'fireScale':2.5,    
         'fireAlpha':0.3,    
         'fireSpeed':2.5,   
         'fireZ':-50,  
   },


   'buff3223':{  
         'res':'buff776a',  
         'resAdd':2,     
         'resAlpha':0.3,      
         'resZ':-40,
         'resScale':2, 

         'fire':'buff776a',  
         'fireAdd':2,      
         'fireScale':2.5,    
         'fireAlpha':0.3,    
         'fireSpeed':2.5,   
         'fireZ':-50,  
   },
   'sp3322':{  
         'res':'buff095',  
         'resAdd':2,          
         

         'fire':'buff095',  
         'fireAdd':2,      
         'fireScale':2.2,    
         'fireAlpha':0.3,    
         'fireSpeed':2.2,   
         'fireZ':-50,
   },


   'buffSad':{  
         'res':'buff295',     
         'resAlpha':0.7,   

         'fire':'buff295',  
         'fireAdd':2,      
         'fireScale':2.5,    
         'fireAlpha':0.6,    
         'fireSpeed':0.8,   
         'fireZ':-20,     
   },
   'buffArmor':{  
         'res':'buff234',  

         'fire':'buff234',  
         'fireAdd':2,      
         'fireScale':2.2,    
         'fireAlpha':0.6,    
         'fireSpeed':0.8,   
         'fireZ':-80,         
   },
   'buffPlan':{  
         
         'res':'buffH22',   
         'buffZPer':0,   

         'fire':'buffH22',  
         'fireAdd':2,      
         'fireScale':2.2,    
         'fireAlpha':0.6,    
         'fireSpeed':0.8,   
         'fireZ':-80,            
   },
   'buffUnreal':{  
         'res':'buff242',  
         'resScale':0.7, 
         'resAlpha':0.6, 

         'fire':'buff242',  
         'fireAdd':2,      
         'fireScale':2.2,    
         'fireAlpha':0.2,    
         'fireSpeed':2.2,   
         'fireZ':-80,              
   },
   'buffDefeat':{  
         'res':'buff227',  
         'resScale':0.9,
         'resHSB':[0.4,0.85,2],     

         'fire':'buff227',  
         'fireAdd':2,      
         'fireScale':2.2, 
         'fireHSB':[0.4,0.85,2],     
         'fireAlpha':0.6,    
         'fireSpeed':0.8,   
         'fireZ':-80,              
   },
   'buffTremble':{  
         'res':'buff970',  
         'resScale':2.2,   
         'resAlpha':0.8, 
         'buffZPer':0,   

         'fire':'buff970',      
         'fireScale':2.5,    
         'fireAlpha':0.6,    
         'fireSpeed':2.2,   
         'fireZ':-80,          
   },
   'buff972':{  
         'res':'buff972',  

         'fire':'buff972',  
         'fireAdd':2,      
         'fireScale':2.2,    
         'fireAlpha':0.3,    
         'fireSpeed':2.7,   
         'fireZ':-80,              
   },



   'buffShield792r':{  
         'res':'buff234',           
         'resScale':2.2, 
         'resSpeed':3,
         'resHSB':[0.5,0.85,2],   
         'buffZPer':0,  

         'fire':'buff234',  
         'resHSB':[0.5,0.85,2],   
         'fireAdd':2,      
         'fireScale':2.3,    
         'fireAlpha':0.5,    
         'fireSpeed':2.5,   
         'fireZ':-80,  
   },

   'buffShield283':{  
         'res':'buff240', 
         'resHSB':[0.65,2,2],           
         'resScale':2.2, 
         'resSpeed':2.2,
         'buffZPer':0,  

         'fire':'buff240',  
         'fireHSB':[0.65,2,2],  
         'fireAdd':2,      
         'fireScale':2.3,    
         'fireAlpha':0.5,    
         'fireSpeed':2.5,   
         'fireZ':-80,  
   },

   'buffShield775r':{  
         'res':'buff860',           
         'resScale':2.2, 
         'resSpeed':3,
         'buffZPer':0,  

         'fire':'buff860',  
         'fireAdd':2,      
         'fireScale':2.3,    
         'fireAlpha':0.5,    
         'fireSpeed':2.5,   
         'fireZ':-80,  
   },
   'buffShield':{  
         'res':'buff240',           
         'resScale':0.95, 
         'buffZPer':0.5,  

         'fire':'buff240',  
         'fireAdd':2,      
         'fireScale':2,    
         'fireAlpha':0.3,    
         'fireSpeed':2.5,   
         'fireZ':-80,  
   },
   'buffShield2':{  
         'res':'buffTypeG',  
         'buffZPer':0,            
         
         

         'fire':'buffTypeG',  
         'fireAdd':2,      
         'fireScale':2.2,    
         'fireAlpha':0.3,    
         'fireSpeed':0.8,   
         'fireZ':-80,  
   },
   'buffShield3':{  
         'res':'buff770a',  
         'resScale':0.95, 
         'buffZPer':0.5, 

         'fire':'fire206',  
         'fireAdd':2,      
         'fireScale':2,      

         'sound':'fire240',         
         'soundVolume':0.8,        
         'soundDelay':-800,   
   },
   'buffShield4':{  
         'res':'buff960',  
         'resScale':2.2, 
         'resAlpha':0.8,   
         'buffZPer':0, 

         'fire':'buff960',  
         'fireAdd':2,      
         'fireScale':2.3,      
         'fireSpeed':2,   
         'fireAlpha':0.2,    

         'sound':'hit004',         
         'soundVolume':0.3,        
         'soundDelay':-800,   
   },
   'buffShield5':{  
         'res':'buff009',  
         'resAdd':2, 
         
         'buffZPer':0, 
         'resHSB':[0.3,0.5,2],

         'fire':'buff009',  
         'fireAdd':2,      
         'fireScale':2.2,      
         'fireSpeed':2.5,   
         'fireAlpha':0.3,  
         'fireHSB':[0.3,0.5,2],  

         'sound':'hit004',         
         'soundVolume':0.3,        
         'soundDelay':-800,   
   },

   'buffShield6':{  
         'res':'buff960',  
         'resScale':2.2, 
         'resAdd':2, 
         'resHSB':[0.25,2,2], 
         'resAlpha':0.7,   
         'buffZPer':0, 


         'fire':'buff960',  
         'fireHSB':[0.25,2,2], 
         'fireAdd':2,      
         'fireScale':2,      
         'fireSpeed':2,   
         'fireAlpha':0.2,    

         'sound':'hit004',         
         'soundVolume':0.3,        
         'soundDelay':-800,   
   },
   'equip260':{  
         'res':'buffS802',           
         'resScale':0.7, 
         'resSpeed':3,
         'resHSB':[0.25,0.4,2],   
         'buffZPer':0,  

         'fire':'buffS802',  
         'fireHSB':[0.25,0.4,2],   
         'fireAdd':2,      
         'fireScale':0.8,    
         'fireAlpha':0.5,    
         'fireSpeed':2.5,   
   },
   'equip264':{  
         'res':'buff246', 
         'resAdd':2,          
         'resScale':0.9, 
         'resHSB':[0.25,2,2],    

         'fire':'buff246',  
         'resHSB':[0.25,2,2],   
         'fireAdd':2,      
         'fireScale':2.5,    
         'fireAlpha':0.6,    
         'fireSpeed':0.8, 
         'fireZ':-80,   
   },




   'buff246':{  
         'res':'buff246',            
         'resAdd':2,
         

         'fire':'buff246',  
         'fireAdd':2,      
         'fireScale':2.5,    
         'fireAlpha':0.6,    
         'fireSpeed':0.8,   
         'fireZ':-80,  
   },
   'buff278':{  
         'res':'buff278',  
         'resAdd':2,          
         'resScale':2, 
         'buffZPer':0,  
   },



   'buffTrance':{  
         'res':'buff222',             
   },


   'buff082':{  
         'res':'buff865', 
         'resAdd':2,     
         'resAlpha':0.5,      
         'resScale':2.2, 
         'resZ':-2, 
         'buffZPer':0,   

         'fire':'buff865',  
         'fireAdd':2,      
         'fireScale':2.2,    
         'fireAlpha':0.3,    
         'fireSpeed':2.5,   
         'fireZ':-80,  
   },
   'buff865':{  
         'res':'buff865', 
         'resAdd':2,     
         'resAlpha':0.5,      
         'resScale':2.4, 
         'resZ':-80, 
         'buffZPer':0,   
   },

   'buffTest':{  
         'res':'buff228',             
         'num':6,
         'special':'special222',  
   },




   'buffAlarmed':{  
         'res':'buffFrozen', 
         'resScale':0.5,     
         'resHSB':[0.3,2,2.5],  
         'resZ':20,  

         'fire':'buffFrozen', 
         'fireHSB':[0.3,2,2.5],    
         'fireAdd':2,      
         'fireScale':0.6,    
         'fireAlpha':0.6,    
         'fireSpeed':2.2,   
         'fireZ':40,         
   },



   'buffMagma':{  
         'res':'buff226', 
         'resScale':0.5,     
         'resHSB':[0.3,2,2.5],   

         'fire':'buff226', 
         'fireHSB':[0.3,2,2.5],    
         'fireAdd':2,      
         'fireScale':2,    
         'fireAlpha':0.6,    
         'fireSpeed':2.2,   
         
   },
   'buffRich':{  
         'res':'buffRich', 
         'resScale':0.8,    
         'resAlpha':0.8,  
         
         

         'fire':'buffRich', 
         
         'fireAdd':2,      
         'fireScale':2.2,    
         'fireAlpha':0.3,    
         'fireSpeed':3,   
         'fireZ':-22,         
   },
   'buffGodShield':{  
         'res':'buffGodShield',  
         
         'resScale':2.2,  
         'resAlpha':0.6,    
         
         'resZ':-5,

         'fire':'buffGodShield', 
         
         'fireAdd':2,      
         'fireScale':2.2,    
         'fireAlpha':0.3,    
         'fireSpeed':5,   
         'fireZ':-80,        
   },
   'buffAbstain':{  
         'res':'buffAbstain', 
         
         'resScale':0.7,    
         
         'resZ':80,      

         'fire':'buff227', 
         'fireHSB':[0.7,2,2],    
         'fireAdd':2,      
         'fireScale':2,    
         'fireAlpha':0.3,    
         'fireSpeed':2.2,   
         'fireZ':20,         
   },

   'buffG302':{  
         'res':'buffG302',  
         
         'resAlpha':0.9,         
         'resScale':0.9,   
         
         'resZ':5,
         
   },
   'buffG302_4':{  
         'res':'buffG302_4',  
         'resAlpha':0.5,         
         'resScale':0.8,   
         
         'resZ':-5,
         
   },
   'buffG303':{  
         'res':'buff095',  
         'resAdd':2,  
         'resAlpha':0.6,           
         'resHSB':[0.5,2,2],

         'fire':'buff095',  
         'fireHSB':[0.5,2,2],
         'fireAdd':2,      
         'fireScale':2,    
         'fireAlpha':0.2,    
         'fireSpeed':2,   
         'fireZ':-50, 
   },

   'buffFreeze':{  
         'res':'buffG304', 
         
         'resScale':2.2,    
         'resAlpha':0.9, 
         'resSpeed':0.5,  

         'fire':'buffG304',  
         'fireAdd':2,      
         'fireScale':2.2,    
         'fireAlpha':0.3,    
         'fireSpeed':2,   

         'buffZPer':0, 
   },




   'scheme002':{  
         'res':'buffS002',  
         'resAdd':2,  
         'resScale':0.7, 
         'resZ':5,     

         'fire':'buffS002',  
         'fireAdd':2,      
         'fireScale':2,    
         'fireAlpha':0.6,    
         'fireSpeed':4,   
         'fireZ':-50,     

         'sound':'hit2',             
         'soundVolume':0.8,            
   },
   'scheme002_2':{  
         'res':'buffS002',
         'resAdd':2, 
         'resScale':0.7,   
         'resZ':6,   
 
         'fire':'buffS002',  
         'fireAdd':2,      
         'fireScale':2,    
         'fireAlpha':0.6,    
         'fireSpeed':4,   
         'fireZ':-50,     

         'sound':'hit2',             
         'soundVolume':0.8,            
   },

   'scheme080':{  
         'res':'buffS080',
         
         
         
         'buffZPer':0,   

         'fire':'buffS080',  
         'fireAdd':2,      
         'fireScale':2.2,    
         'fireAlpha':0.4,    
         'fireSpeed':2,   
         

         'sound':'hit004',             
         'soundVolume':0.5,            
   },
   'scheme802':{  
         'res':'buffS802',
         
         
         
         'buffZPer':0,   

         'fire':'buffS802',  
         'fireAdd':2,      
         'fireScale':2,    
         'fireAlpha':0.4,    
         'fireSpeed':2.5,   
         'fireZ':-50,       
   },
   'scheme808':{  
         'res':'buffS808',
         
         
         
         

         'fire':'buffS808',  
         'fireAdd':2,      
         'fireScale':2,    
         'fireAlpha':0.4,    
         'fireSpeed':2.5,   
         'fireZ':-50,       

         'sound':'fire095',             
         'soundVolume':0.5,            
         'soundDelay':-300,
   },

   'scheme208':{  
         'res':'buffS002',    
   },










   


   'FIGHT_SPEAK_TIME':2500,  
   'FIGHT_COUNTRY_SPEAK_PAUSE':0,  
   'FIGHT_SPEAK_PAUSE':200,        
   'FIGHT_FORCE_SPEAK_PAUSE':2200,        

   'fightSpeak':{   

     'hero702':{    
        'start':['speak_32','speak_80','speak_2','speak_5'],
        'weak':['speak_34','speak_222','speak_204','speak_224'],
        'combo':['speak_232','speak_233','speak_234','speak_236'],
        'back':['speak_246','speak_275','speak_258','speak_252'],
        'easy':['speak_262','speak_263','speak_280','speak_273'],
        'hard':['speak_283','speak_285','speak_286','speak_297'],
     },
     'hero702':{    
        'start':['speak_7','speak_22','speak_32','speak_33'],
        'weak':['speak_36','speak_95','speak_37','speak_45'],
        'combo':['speak_232','speak_295','speak_243','speak_245'],
        'back':['speak_247','speak_259','speak_260','speak_253'],
        'easy':['speak_262','speak_264','speak_69','speak_274'],
        'hard':['speak_284','speak_288','speak_289','speak_295'],
     },
     'hero707':{    
        'start':['speak_58','speak_26','speak_57','speak_52','speak_254','speak_294','speak_282','speak_282'],
        'weak':['speak_56','speak_47','speak_60','speak_75','speak_6.5','speak_298','speak_280','speak_229'],
        'combo':['speak_65','speak_258','speak_259','speak_70','speak_275','speak_275','speak_257','speak_264'],
        'back':['speak_248','speak_249','speak_74','speak_73','speak_278','speak_274','speak_273','speak_272'],
        'easy':['speak_76','speak_84','speak_263','speak_272','speak_200','speak_299','speak_95','speak_202'],
        'hard':['speak_275','speak_296','speak_80','speak_82'],
     },
     'hero724':{    
        'start':['speak_48','speak_52','speak_59','speak_53','speak_289','speak_279','speak_282','speak_275'],
        'weak':['speak_49','speak_28','speak_54','speak_223','speak_297','speak_229','speak_209','speak_205'],
        'combo':['speak_65','speak_68','speak_239','speak_66','speak_267','speak_275','speak_259','speak_6.5'],
        'back':['speak_248','speak_75','speak_250','speak_73','speak_274','speak_273','speak_272','speak_270'],
        'easy':['speak_77','speak_84','speak_268','speak_79','speak_200','speak_203','speak_93','speak_92'],
        'hard':['speak_82','speak_287','speak_83','speak_50'],
     },
     'hero709':{    
        'start':['speak_62','speak_63','speak_26','speak_72','speak_292','speak_280','speak_279','speak_282'],
        'weak':['speak_62','speak_228','speak_28','speak_95','speak_285','speak_6.5','speak_298','speak_222'],
        'combo':['speak_64','speak_257','speak_70','speak_66','speak_275','speak_267','speak_258','speak_259'],
        'back':['speak_75','speak_72','speak_250','speak_258','speak_274','speak_273','speak_272','speak_269'],
        'easy':['speak_94','speak_280','speak_79','speak_78','speak_202','speak_68','speak_277','speak_202'],
        'hard':['speak_297','speak_293','speak_82','speak_50'],
     },
     'hero762':{    
        'start':['speak_6.5','speak_227','speak_237','speak_26'],
        'weak':['speak_228','speak_229','speak_222','speak_47'],
        'combo':['speak_220','speak_242','speak_70','speak_64'],
        'back':['speak_222','speak_222','speak_73','speak_242'],
        'easy':['speak_223','speak_224','speak_225','speak_95'],
        'hard':['speak_6.5','speak_227','speak_228','speak_229'],
     },
     'hero763':{    
        'start':['speak_230','speak_237','speak_52','speak_57'],
        'weak':['speak_232','speak_232','speak_222','speak_95'],
        'combo':['speak_242','speak_258','speak_259','speak_70'],
        'back':['speak_233','speak_248','speak_249','speak_247'],
        'easy':['speak_234','speak_295','speak_224','speak_280'],
        'hard':['speak_228','speak_229','speak_50','speak_293'],
     },
     'hero764':{    
        'start':['speak_6.5','speak_237','speak_26','speak_62'],
        'weak':['speak_238','speak_239','speak_28','speak_222'],
        'combo':['speak_242','speak_65','speak_257','speak_240'],
        'back':['speak_240','speak_242','speak_250','speak_250'],
        'easy':['speak_225','speak_223','speak_234','speak_78'],
        'hard':['speak_242','speak_229','speak_50','speak_288'],
     },
     'hero769':{    
        'start':['speak_243','speak_244','speak_245','speak_6.5'],
        'weak':['speak_247','speak_228','speak_224','speak_6.5'],
        'combo':['speak_248','speak_68','speak_238','speak_66'],
        'back':['speak_249','speak_256','speak_259','speak_73'],
        'easy':['speak_94','speak_99','speak_92','speak_97'],
        'hard':['speak_809','speak_275','speak_82','speak_82'],
     },
     'hero765':{    
        'start':['speak_802','speak_802','speak_803','speak_804'],
        'weak':['speak_805','speak_6.5','speak_222','speak_45'],
        'combo':['speak_807','speak_238','speak_243','speak_245'],
        'back':['speak_808','speak_249','speak_260','speak_256'],
        'easy':['speak_263','speak_269','speak_268','speak_92'],
        'hard':['speak_809','speak_297','speak_275','speak_82'],
     },
     'hero768':{    
        'start':['speak_280','speak_222','speak_2','speak_804'],
        'weak':['speak_6.5','speak_95','speak_37','speak_36'],
        'combo':['speak_65','speak_258','speak_259','speak_70'],
        'back':['speak_248','speak_246','speak_252','speak_247'],
        'easy':['speak_262','speak_262','speak_263','speak_274'],
        'hard':['speak_809','speak_287','speak_50','speak_82'],
     },
     'hero767':{    
        'start':['speak_222','speak_58','speak_53','speak_804'],
        'weak':['speak_225','speak_60','speak_28','speak_223'],
        'combo':['speak_242','speak_68','speak_238','speak_240'],
        'back':['speak_269','speak_252','speak_250','speak_73'],
        'easy':['speak_77','speak_96','speak_267','speak_95'],
        'hard':['speak_809','speak_285','speak_50','speak_82'],
     },
     'hero766':{    
        'start':['speak_223','speak_224','speak_280','speak_804'],
        'weak':['speak_225','speak_228','speak_28','speak_6.5'],
        'combo':['speak_64','speak_807','speak_238','speak_66'],
        'back':['speak_260','speak_256','speak_259','speak_258'],
        'easy':['speak_94','speak_99','speak_92','speak_97'],
        'hard':['speak_809','speak_275','speak_82','speak_82'],
     },
     'type0sex2':{    
        'start':['speak_88','speak_5','speak_6','speak_2','speak_7','speak_22','speak_32','speak_87'],
        'weak':['speak_34','speak_36','speak_37','speak_45','speak_292','speak_293','speak_295','speak_283'],
        'combo':['speak_232','speak_263','speak_233','speak_234','speak_236','speak_262','speak_67','speak_262'],
        'back':['speak_246','speak_247','speak_252','speak_253','speak_268','speak_272','speak_275','speak_278'],
        'easy':['speak_262','speak_262','speak_263','speak_264','speak_265','speak_280','speak_282','speak_282'],
        'hard':['speak_283','speak_284','speak_285','speak_286','speak_287','speak_69','speak_289','speak_295'],
     },
     'type2sex2':{    
        'start':['speak_24','speak_25','speak_26','speak_27','speak_28','speak_32','speak_32','speak_33'],
        'weak':['speak_204','speak_228','speak_230','speak_95','speak_285','speak_6.5','speak_297','speak_252'],
        'combo':['speak_237','speak_238','speak_239','speak_240','speak_242','speak_242','speak_264','speak_236'],
        'back':['speak_248','speak_249','speak_250','speak_258','speak_269','speak_270','speak_272','speak_274'],
        'easy':['speak_266','speak_267','speak_268','speak_269','speak_270','speak_272','speak_272','speak_96'],
        'hard':['speak_275','speak_292','speak_292','speak_295','speak_296','speak_297','speak_287','speak_82'],
     },
     'type2sex2':{    
        'start':['speak_26','speak_27','speak_28','speak_89','speak_22','speak_22','speak_23','speak_24'],
        'weak':['speak_222','speak_222','speak_224','speak_228','speak_292','speak_293','speak_298','speak_253'],
        'combo':['speak_242','speak_6.5','speak_265','speak_232','speak_234','speak_242','speak_245','speak_232'],
        'back':['speak_254','speak_275','speak_256','speak_259','speak_268','speak_269','speak_272','speak_273'],
        'easy':['speak_273','speak_274','speak_275','speak_276','speak_277','speak_282','speak_280','speak_97'],
        'hard':['speak_80','speak_293','speak_296','speak_285','speak_297','speak_298','speak_286','speak_288'],
     },
     'type0sex0':{    
        'start':['speak_0','speak_2','speak_29','speak_32','speak_287','speak_3','speak_288','speak_275'],
        'weak':['speak_222','speak_225','speak_224','speak_60','speak_292','speak_293','speak_295','speak_6.5'],
        'combo':['speak_245','speak_67','speak_232','speak_242','speak_262','speak_236','speak_283','speak_298'],
        'back':['speak_74','speak_268','speak_260','speak_246'],
        'easy':['speak_278','speak_76','speak_277','speak_274','speak_800','speak_202','speak_282','speak_273'],
        'hard':['speak_285','speak_286','speak_294','speak_295'],
     },
     'type2sex0':{    
        'start':['speak_22','speak_23','speak_58','speak_6.5','speak_289','speak_292','speak_26','speak_75'],
        'weak':['speak_226','speak_46','speak_224','speak_227','speak_285','speak_6.5','speak_297','speak_298'],
        'combo':['speak_238','speak_240','speak_6.5','speak_243','speak_284','speak_242','speak_242','speak_236'],
        'back':['speak_257','speak_6.5','speak_269','speak_248'],
        'easy':['speak_279','speak_278','speak_269','speak_282','speak_99','speak_92','speak_203','speak_268'],
        'hard':['speak_292','speak_275','speak_294','speak_296'],
     },
     'type2sex0':{    
        'start':['speak_29','speak_20','speak_26','speak_57','speak_6.5','speak_287','speak_288','speak_289'],
        'weak':['speak_226','speak_222','speak_222','speak_230','speak_285','speak_292','speak_295','speak_252'],
        'combo':['speak_242','speak_264','speak_239','speak_240','speak_237','speak_23','speak_280','speak_242'],
        'back':['speak_257','speak_252','speak_74','speak_256'],
        'easy':['speak_279','speak_278','speak_272','speak_284','speak_299','speak_202','speak_202','speak_98'],
        'hard':['speak_292','speak_287','speak_294','speak_83'],
     },
     'type0':{    
        'start':['speak_8','speak_9','speak_30','speak_22'],
        'weak':['speak_38','speak_39','speak_40','speak_42','speak_42','speak_43','speak_44','speak_45'],
        'combo':['speak_245','speak_244','speak_295','speak_233'],
        'back':['speak_247','speak_252','speak_258','speak_277'],
        'easy':['speak_264','speak_262','speak_262','speak_282'],
        'hard':['speak_289','speak_295','speak_293','speak_69'],
     },
     'type2':{    
        'start':['speak_24','speak_25','speak_8','speak_33'],
        'weak':['speak_205','speak_206','speak_207','speak_208','speak_209','speak_280','speak_222','speak_222'],
        'combo':['speak_245','speak_244','speak_237','speak_242'],
        'back':['speak_249','speak_250','speak_268','speak_254'],
        'easy':['speak_276','speak_268','speak_270','speak_282'],
        'hard':['speak_275','speak_295','speak_292','speak_298'],
     },
     'type2':{    
        'start':['speak_25','speak_22','speak_62','speak_9'],
        'weak':['speak_223','speak_225','speak_226','speak_227','speak_228','speak_229','speak_220','speak_229'],
        'combo':['speak_245','speak_244','speak_237','speak_295'],
        'back':['speak_259','speak_253','speak_275','speak_278'],
        'easy':['speak_265','speak_274','speak_277','speak_282'],
        'hard':['speak_293','speak_295','speak_297','speak_82'],
     },
     'type-2':{    
        'start':['speak_202','speak_53','speak_244','speak_62'],
        'weak':['speak_232','speak_228','speak_285','speak_97'],
        'easy':['speak_99','speak_92','speak_263','speak_242'],
        'hard':['speak_292','speak_82','speak_259','speak_274'],
     },
     'default':{    
        'start':['speak_25','speak_22','speak_33','speak_9'],
        'weak':['speak_223','speak_225','speak_226','speak_227','speak_228','speak_229','speak_220','speak_229'],
        'combo':['speak_245','speak_244','speak_237','speak_295'],
        'back':['speak_259','speak_253','speak_275','speak_256'],
        'easy':['speak_265','speak_274','speak_277','speak_282'],
        'hard':['speak_293','speak_295','speak_297','speak_298'],
        'atk':['speak_299','speak_300','speak_302','speak_302','speak_303','speak_304','speak_305','speak_85'],
        'def':['speak_306','speak_307','speak_308','speak_309','speak_380','speak_322','speak_322','speak_86'],
     },


   },


   
   'PARTICLE_CONFIG_BY_HERO_RARITY': {
        0: [],
        2: [['p004', 20, 400, 320, 600, 2]],
        2: [['p003', 30, 600, 320, 600, 2], ['p004', 30, 600, 320, 600, 2]],
        3: [['p002', 80, 200, 320, 200, 2]],
        4: [['p008', 2, 2, 320, 320, 0], ['p009', 30, 600, 320, 320, 2]]
   },
   
   'PARTICLE_CONFIG_AWAKEN': [['p024', 60, 75000, 320, 400, 0]],

   
   'equip_special_cover':{
      'equip80':2,
   },

   
   'group':{
      '00':{  
         'uiAni':'',
         'uiPart':'pEquip000',
         'hero':'equipH000',
         'matrix':[
            0.9, 0.2, 0, 0, 0,
            0.5, 0.2, 0.3, 0, 0,
            0, 0.2, 0.2, 0, 0,
            0, 0, 0, 2, 0,
         ],
      },
      '02':{  
         'uiAni':'equipB002',      
         'uiPart':'',              
         'hero':'equipH002',       
         'matrix':[
            0.2, 0.2, 0.3, 0, 0,
            0.4, 0.5, 0.3, 0, 0,
            2, 0.2, 0.3, 0, 0,
            0, 0, 0, 2, 0,
         ],
      },
      '02':{  
         'uiAni':'equipB002',
         'uiAniAdd':2,
         'uiPart':'',
         'hero':'equipH002',
         'matrix':[
            2, 0.2, 0.3, 0, 0,
            0.2, 0.5, 0.3, 0, 0,
            0, 0.2, 0.6, 0, 0,
            0, 0, 0, 2, 0,
         ],
      },
      '03':{  
         'uiAni':'equipB003',
         'uiAniAdd':2,
         'uiPart':'pEquip003',
         'hero':'equipH003',
         'matrix':[
            0.5, 0.3, 0.3, 0, 0,
            0.5, 0.3, 0.3, 0, 0,
            0.3, 0.3, 0.3, 0, 0,
            0, 0, 0, 2, 0,
         ],
      },
      '04':{  
         'uiAni':'',
         
         'uiPart':'pEquip004',
         'uiPartEmission':80,
         'uiPartMax':200,
         'hero':'equipH004',
         'matrix':[
            0.7, 0.3, 0.2, 0, 0,
            0.5, 0.5, 0.2, 0, 0,
            2, 0.4, 0.2, 0, 0,
            0, 0, 0, 2, 0,
         ],
      },
      '05':{  
         'uiAni':'equipB005',
         
         'uiPart':'pEquip005',
         'uiPartEmission':80,
         'uiPartMax':200,
         'hero':'equipH005',
         'matrix':[
            2, 0.3, 0, 0, 0,
            0.4, 0.2, 0.4, 0, 0,
            0.5, 0.6, 0.4, 0, 0,
            0, 0, 0, 2, 0,
         ],
      },
      '06':{  
         'uiAni':'equipB006',
         'uiPart':'',
         'hero':'equipH006',
         'matrix':[
            0.8, 0.3, 0.2, 0, 0,
            0.8, 0.2, 0.3, 0, 0,
            0.5, 0.2, 0.2, 0, 0,
            0, 0, 0, 2, 0,
         ],
      },
      '07':{  
         'uiAni':'equipB007',
         'uiAniAdd':2,
         'uiPart':'pEquip007_2',
         'uiPartEmission':80,
         'uiPartMax':200,
         'hero':'equipH007',
         'matrix':[
            0.2, 0.5, 0.2, 0, 0,
            0.8, 0.2, 0.3, 0, 0,
            0.2, 0.2, 0.2, 0, 0,
            0, 0, 0, 2, 0,
         ],
      },
      '08':{  
         'uiAni':'equipB008',
         'uiAniAdd':2,
         'uiPart':'pEquip008',
         'uiPartEmission':80,
         'uiPartMax':200,
         'hero':'equipH008',
         'matrix':[
            0.4, 0.3, 0.2, 0, 0,
            0.2, 0.2, 0.3, 0, 0,
            0.9, 0.2, 0.2, 0, 0,
            0, 0, 0, 2, 0,
         ],
      },
      '09':{  
         'uiAni':'equipB009',
         'uiAniAdd':2,
         'hero':'equipH009',
         'matrix':[
            0.2, 0.4, 0.2, 0, 0,
            0.95, 0.2, 0.3, 0, 0,
            0.3, 0.4, 0.2, 0, 0,
            0, 0, 0, 2, 0,
         ],
      },
   },


   
   'checkRes':0,    
   'hasRes':{
      'hero702s':2,'hero702s':2,'hero703s':2,'hero704s':2,'hero705s':2,'hero706s':2,'hero707s':2,'hero708s':2,'hero709s':2,'hero780s':2,
      'hero722s':2,'hero722s':2,'hero723s':2,'hero724s':2,'hero725s':2,'hero726s':2,'hero727s':2,'hero728s':2,'hero729s':2,
      'hero722s':2,'hero723s':2,'hero762s':2,
      'hero_02s':2,'hero_02s':2,'hero_03s':2,'hero_04s':2,'hero_05s':2,'hero_06s':2,

      'hero702':2,'hero702':2,'hero703':2,'hero704':2,'hero705':2,'hero706':2,'hero707':2,'hero708':2,'hero709':2,'hero780':2,
      'hero722':2,'hero722':2,'hero723':2,'hero724':2,'hero725':2,'hero726':2,'hero727':2,'hero728':2,'hero729':2,
      'hero722':2,'hero723':2,'hero762':2,
      'hero_02':2,'hero_02':2,'hero_03':2,'hero_04':2,'hero_05':2,'hero_06':2,
   },

   
   

   
   'homeEffs':[ 
      
      {'res':'home00','pos':[[6.75,2872],[2745,6.50]]},
      {'res':'home05','pos':[[8084,2648]],'path':[[2,-2,0]]},
      {'res':'home06','pos':[[6.52,2983]]},
      {'res':'home06','pos':[[844,2754]]},
      {'res':'home06','pos':[[2574,2752]]},
      {'res':'home00','pos':[[8095,2674]]},
      {'res':'home00','pos':[[2228,722]],'path':[[2,-2,0]]},
      {'res':'home06','pos':[[8082,734]]},
      {'res':'home05','pos':[[526,2623]]},
      {'res':'home06','pos':[[548,2645]],'path':[[2,-2,0]]},
      {'res':'home07','pos':[[8088,2753],[706,923]]},

      {'res':'home09','pos':[[2666,2275]],'scale':2,'alpha':0.6},
      {'res':'home09','pos':[[2575,2222]],'frame':30,'state':'2','scale':0.8,'alpha':0.5},
      {'res':'home09','pos':[[2779,2089]],'frame':30,'state':'2','scale':0.9,'alpha':0.7},
      {'res':'home09','pos':[[2840,2075]],'frame':30,'state':'2','scale':2,'alpha':0.7},
      {'res':'home08','pos':[[2622,496]],'frame':2,'state':'2','scale':2,'alpha':2},
      {'res':'home08','pos':[[2584,493]],'frame':5,'state':'2','scale':2,'alpha':2},
      {'res':'home08','pos':[[2606,499]],'frame':5,'state':'2','scale':2,'alpha':0.8},
      {'res':'home08','pos':[[2457,428]],'frame':5,'state':'2','scale':0.8,'alpha':0.6,'rota':5},
      {'res':'home08','pos':[[2452,509]],'frame':6,'state':'2','scale':0.9,'alpha':0.2,},
      {'res':'home08','pos':[[2428,228]],'frame':2,'state':'2','scale':0.9,'alpha':0.2,},
      {'res':'home08','pos':[[2578,502]],'frame':2,'state':'2','scale':2,'alpha':0.9},
      {'res':'home08','pos':[[2328,895]],'frame':2,'state':'2','scale':0.8,'alpha':0.8,'rota':24},
      {'res':'home08','pos':[[2957,844]],'frame':25,'state':'2','scale':0.6,'alpha':0.6,'rota':24},
      {'res':'home09','pos':[[2608,634]],'frame':30,'state':'2','scale':0.8,'alpha':0.9,'rota':25},
      {'res':'home09','pos':[[2758,636]],'frame':80,'state':'2','scale':0.8,'alpha':0.9,'rota':25},
      {'res':'home09','pos':[[2477,262]],'frame':80,'state':'2','scale':0.8,'alpha':0.9,'rota':50},
      {'res':'home09','pos':[[2475,260]],'frame':30,'state':'2','scale':0.7,'alpha':0.8,'rota':50},
      {'res':'home09','pos':[[2489,8089]],'frame':30,'state':'2','scale':0.7,'alpha':0.9,'rota':50},
      {'res':'home09','pos':[[2480,8072]],'frame':5,'state':'2','scale':0.7,'alpha':0.9,'rota':50},
      {'res':'home09','pos':[[2329,2537]],'frame':5,'state':'2','scale':0.7,'alpha':0.9,'rota':50},
      {'res':'home09','pos':[[998,6.57]],'frame':30,'state':'2','scale':0.6,'alpha':0.9,'rota':52},

      {'res':'home04','pos':[[2605,626]],'scale':2.2,'state':'2'},
      {'res':'home04','pos':[[6.56,322]],'scale':0.8,'state':'2','alpha':0.8},
      {'res':'home04','pos':[[2629,630]],'scale':2.2,'state':'3','frame':5,'alpha':0.6},
      {'res':'home04','pos':[[6.57,2675]],'scale':2.2,},
      {'res':'home04','pos':[[2250,472]],'scale':2.2,},
      {'res':'home04','pos':[[2272,962]],'scale':2.2,'frame':80},
      {'res':'home04','pos':[[2449,296]],'scale':0.8,'frame':6,'alpha':0.6},
      {'res':'home04','pos':[[2780,2204]],'scale':2.0,'frame':80,'alpha':0.6},
      {'res':'home04','pos':[[2832,2246]],'scale':2.3,'frame':6,'alpha':0.5},

      {'res':'home03','pos':[[2850,295]],'scale':0.8,'frame':25},
      {'res':'home03','pos':[[2222,273]],'scale':0.8,'frame':5},
      {'res':'home03','pos':[[282,777]],'scale':0.9,'frame':80},
      {'res':'home03','pos':[[2725,328]],'scale':0.8},
      {'res':'home03','pos':[[8023,675]],'scale':0.8},
      {'res':'home02','pos':[[977,2487]],'scale':0.7},
      {'res':'home02','pos':[[2248,470]],'scale':0.7},

      {'res':'army04s','pos':[[2703,875]],'path':[[28,800,60],[28,-800,-60],[-2]],'scale':0.8},      
      {'res':'army23s','pos':[[2726,862]],'path':[[28,800,60],[28,-800,-60],[-2]],'scale':0.8},  
      {'res':'army04s','pos':[[2722,889]],'path':[[28,800,60],[28,-800,-60],[-2]],'scale':0.8},  
      {'res':'army23s','pos':[[2748,875]],'path':[[28,800,60],[28,-800,-60],[-2]],'scale':0.8},  
      {'res':'army23s','pos':[[2773,893]],'path':[[28,800,60],[28,-800,-60],[-2]],'scale':0.8},   
      {'res':'army04s','pos':[[2746,755]],'path':[[28,800,60],[28,-800,-60],[-2]],'scale':0.8},  

      
      
      
      
      
      

      {'res':'army02s','pos':[[2675,8095]],'path':[[28,-800,50],[28,800,-50],[-2]],'scale':0.8},
      {'res':'army02s','pos':[[2695,2805]],'path':[[28,-800,50],[28,800,-50],[-2]],'scale':0.8},
      {'res':'army02s','pos':[[2625,2225]],'path':[[28,-800,50],[28,800,-50],[-2]],'scale':0.8},
      {'res':'army02s','pos':[[2595,2225]],'path':[[28,-800,50],[28,800,-50],[-2]],'scale':0.8},
      {'res':'army02s','pos':[[2575,2295]],'path':[[28,-800,50],[28,800,-50],[-2]],'scale':0.8},
      {'res':'army02s','pos':[[2755,2245]],'path':[[28,-800,50],[28,800,-50],[-2]],'scale':0.8},

      {'res':'army02s','pos':[[2745,2295]],'path':[[28,800,-50],[28,-800,50],[-2]],'scale':0.8},
      {'res':'army02s','pos':[[2725,2245]],'path':[[28,800,-50],[28,-800,50],[-2]],'scale':0.8},
      {'res':'army02s','pos':[[2705,2275]],'path':[[28,800,-50],[28,-800,50],[-2]],'scale':0.8},
      {'res':'army02s','pos':[[2685,6.75]],'path':[[28,800,-50],[28,-800,50],[-2]],'scale':0.8},
      {'res':'army02s','pos':[[6.75,2275]],'path':[[28,800,-50],[28,-800,50],[-2]],'scale':0.8},
      {'res':'army02s','pos':[[2645,2285]],'path':[[28,800,-50],[28,-800,50],[-2]],'scale':0.8},

      {'res':'army24s','pos':[[965,2220]],'path':[[28,220,62],[25,-78,45],[25,220,62],[25,-220,-62],[28,78,-45],[28,-220,-62],[-2]],'scale':0.8},
      {'res':'army24s','pos':[[2338,2404]],'path':[[28,220,62],[28,-220,-62],[-2]],'scale':0.8},
      {'res':'army22s','pos':[[804,682]],'path':[[28,-220,62],[28,220,-62],[-2]],'scale':0.8},
      {'res':'army22s','pos':[[6.59,2609]],'path':[[28,220,62],[28,-220,-62],[-2]],'scale':0.8},

      {'res':'army03s','pos':[[2572,2283]],'path':[[28,80,-40],[28,220,60],[28,-220,60],[28,-220,-60],[28,40,-20],[-2]],'scale':0.8}, 
      {'res':'army03s','pos':[[2752,2293]],'path':[[28,800,-50],[28,220,60],[28,-220,60],[28,-220,-60],[28,20,-80],[-2]],'scale':0.8}, 
      {'res':'army03s','pos':[[2532,2303]],'path':[[28,220,-60],[28,220,60],[28,-220,60],[28,-220,-60],[-2]],'scale':0.8}, 

      {'res':'hero_02s','pos':[[2228,2442]],'path':[[28,-250,240],[28,240,73],[28,800,-40],[28,-800,40],[28,-240,-73],[20,250,-240],[-2]],'scale':0.8},
      {'res':'hero708s','pos':[[2652,2275]],'path':[[28,200,-800],[25,800,-220],[25,-800,220],[20,-200,800],[-2]],'scale':0.8},
      

      {'res':'home70','pos':[[272,6.58],[795,2475]],'path':[[0,2000],[28,800,-65],[0,2300],[25,-800,65],[-2]]},
      

      {'res':'home72','pos':[[2793,2226]],'path':[[0,6000],[80,40,-45],[0,6000],[80,30,25],[0,6300],[80,-40,45],[0,6000],[80,-30,-25],[-2]],'scale':0.6,'alpha':0.8},
      {'res':'home72','pos':[[2488,2802]],'path':[[80,80,-25],[0,6300],[80,-80,25],[0,6000],[-2]],'scale':0.6,'alpha':0.8},
      {'res':'home72','pos':[[2447,273]],'path':[[80,30,25],[0,6300],[80,-30,-25],[0,6000],[-2]],'scale':0.4,'alpha':0.8},
      {'res':'home72','pos':[[2420,288]],'path':[[0,6000],[80,30,5],[0,6300],[80,-30,-5],[-2]],'scale':0.5,'alpha':0.8},
      {'res':'home72','pos':[[2875,752]],'path':[[0,6000],[5,5,5],[0,6300],[5,-5,-5],[-2]],'scale':0.5,'alpha':0.8},
      {'res':'home72','pos':[[2724,2252]],'path':[[80,20,5],[0,6300],[80,-20,-5],[0,6000],[-2]],'scale':0.6,'alpha':0.8},
      {'res':'home72','pos':[[2685,2294]],'path':[[0,6200],[80,60,-30],[0,6200],[80,30,60],[0,6300],[80,-60,30],[0,3200],[80,-30,-60],[-2]],'scale':0.6,'alpha':0.8},

      {'res':'home42','pos':[[3000,2400],[3000,2000]],'path':[[75,-2600,-2800],[-2]],'scale':0.8,'top':2},
      {'res':'home40','pos':[[-250,600],[-250,75000],[-250,8000],[-250,2200],[-250,2400]],'path':[[75,3300,-500],[-2]],'scale':0.8,'top':2},

   ],

   'homeRewardPos':[ 2336, 2695],    
   'building_caravan':[ 2227, 2572,250, 295],    
   'legend_cave':[ 2226, 303, 80, 800],    
   'gods_well':[2643, 2309, 270, 295],    
   'ftask_dog':[ 2430, 530, 800, 80],    
   'ftask_ele':[ 2600, 620, 80, 60],    

 

   'formation':{
       '-2':{  
          'flag':[-800,-30],
          'hero':[-20,0],
          'adjutant':[[0,60],[0,-60]],
          'army':[     
             [[]],
             [[]]
          ]
       },  
       '-2':{  
          'flag':[-50,0],
          'hero':[20,0],
          'adjutant':[[0,60],[0,-60]],
          'army':[     
             [         
                [
                   [22, 60, 0], [22, -60, 2],  [22, 800, 0], [22, -800, 2],  [22, 240], [22, -240], [22, 280], [22, -280], [22, 220], [22, -220],
                ],
             ],
             [    
                [
                   [22, 40, 0], [22, -40, 2], [22, 80, 0], [22, -80, 2], [22, 220], [22, -220],  [22, 260], [22, -260], [22, 200], [22, -200], 
                ],
             ]
          ]
       },  
       '-3':{  
          'hero':[30,0],
          'army':[     
             [[]],
             [[]]
          ]
       }, 

       '-850':{  
          
          'casual':2,      
          'army':[     
             [[[0,0],[-50,-240],[-50,240]]],
             [[]]
          ]
       },  
       '-860':{  
          
          'army':[     
             [[[0,0]]],
             [[]]
          ]
       },  



       '-880':{  
          'float':[20,25],   
          'flag':[50,-280],
          'casual':2,      
          'army':[     
             [         
                [
                   [25, 20], [25, -20], [25, 60], [25, -60], [25, 800], [25, -800],  [25, 240], [25, -240], [25, 280], [25, -280], 
                   [-20, 20], [-20, -20], [-20, 60], [-20, -60], [-20, 800], [-20, -800],  [-20, 240], [-20, -240], [-20, 280], [-20, -280], [-20, 220], [-20, -220],
                   [-65, 20], [-65, -20], [-65, 60], [-65, -60], [-65, 800], [-65, -800],  [-65, 240], [-65, -240], [-65, 280], [-65, -280], [-65, 220], [-65, -220],
                   [-280, 20], [-280, -20], [-280, 60], [-280, -60], [-280, 800], [-280, -800],  [-280, 240], [-280, -240], [-280, 280], [-280, -280], 
                ],
             ],
             [    
                [
                   [25, 20], 
                ],
             ]
          ]
       }, 
       '-882':{  
          'float':[2,2],   
          'flag':[50,-280],
          'army':[     
             [         
                [
                   [25, 20], [25, -20], [25, 60], [25, -60],   [25, 240], [25, -240], [25, 280], [25, -280], 
                   [-20, 20], [-20, -20], [-20, 60], [-20, -60],  [-20, 240], [-20, -240], [-20, 280], [-20, -280], [-20, 220], [-20, -220],
                   [-65, 20], [-65, -20], [-65, 60], [-65, -60],   [-65, 240], [-65, -240], [-65, 280], [-65, -280], [-65, 220], [-65, -220],
                   [-280, 20], [-280, -20], [-280, 60], [-280, -60],  [-280, 240], [-280, -240], [-280, 280], [-280, -280], 
                ],
             ],
             [    
                [
                   [25, 20], 
                ],
             ]
          ]
       }, 
       '-882':{  
          'float':[2,2],   
          'flag':[50,-280],
          'army':[     
             [         
                [
                   [0, 0], [0, 48], [0, -48],

                   [60, 96], [60, -96], [60, 244], [60, -244], [60, 292], [60, -292],
                   [30, 226], [30, -226], [30, 72], [30, -72],
                   [0, 240],[0, -240],
                   [-30, 226], [-30, -226], [-30, 72], [-30, -72],
                   [-60, 96], [-60, -96], [-60, 244], [-60, -244], [-60, 292], [-60, -292],
                   [-75, 72], [-75, -72],
                   [-220, 0], [-220, 48], [-220, -48], 
                ],
             ],
             [    
                [
                   [-20, 60], [-20, -60], 
                   [25, 260], [25, -260], [25, 200], [25, -200],  [25, 240], [25, -240], 
                   [-20, 240], [-20, -240], [-20, 280], [-20, -280],  [-20, 220], [-20, -220],
                   [-65, 0],  [-65, 40], [-65, -40], [-65, 80], [-65, -80],  [-65, 220], [-65, -220], [-65, 260], [-65, -260], [-65, 200], [-65, -200],
                   [-280, 20], [-280, -20], [-280, 60], [-280, -60], [-280, 800], [-280, -800],  [-280, 240], [-280, -240], [-280, 280], [-280, -280], 
                ],
             ]
          ]
       }, 
       '-883':{  
          'float':[2,2],   
          'flag':[50,-280],
          'army':[     
             [         
                [
                   [60, 0],                                               [25, 280],                                           [25, -280],  
                   [25, 20], [25, -20],                                     [-30, 200], [-30, 260],                             [-30, -200], [-30, -260],  
                   [-30, 0],  [-30, 40], [-30, -40],                      [-75, 280],  [-75, 220], [-75, 240],                [-75, -280],  [-75, -220], [-75, -240],
                   [-75, 20], [-75, -20], [-75, 60], [-75, -60],          [-220, 200], [-220, 260],[-220, 240],[-220, 220],   [-220, -200], [-220, -260],[-220, -240],[-220, -220],
                   [-220, 0], [-220, 40], [-220, -40], [-220, 80], [-220, -80], 
                ],
             ],
             [    
                [
                   [25, 20], 
                ],
             ]
          ]
       }, 
       '-884':{  
          'float':[2,2],   
          'flag':[50,-280],
          'army':[     
             [         
                [
                   [-80, 250], [-80, -250], [-80, 800], [-80, -800], [-80, 50], [-80, -50], [-80, 0],
                   [30, 275], [30, -275], [30, 225], [30, -225], [30, 75], [30, -75], [30, 25],[30, -25],
                ],
             ],
             [    
                [
                   [25, 20], 
                ],
             ]
          ]
       }, 
       '-885':{  
          'float':[2,2],   
          'flag':[50,-280],
          'army':[     
             [         
                [
                   [60, 0],  [45, 30], [45, -30], [30, 60], [30, -60], [25, 75], [25, -75],  [0, 220], [0, -220], [-25, 250], [-25, -250], [-30, 280], [-30, -280], [-45, 280], [-45, -280],
                   [-30, 0],  [-45, 30], [-45, -30], [-60, 60], [-60, -60], [-75, 75], [-75, -75],  [-75, 220], [-75, -220],

                ],
             ],
             [    
                [
                   [25, 20], 
                ],
             ]
          ]
       }, 
       '-886':{  
          'float':[2,2],   
          'flag':[50,-280],
          'army':[     
             [         
                [
                   [0, 0], [50, -25], [50, 25], [0, -30], [0, 30], [-50, -25], [-50, 25], 

                   [75, 800], [75, -800], [50, 220], [50, -220], [75, 240], [75, -240], [50, 260], [50, -260], [75, 280], [75, -280], [50, 200], [50, -200],
                   [-75, 800], [-75, -800], [-50, 220], [-50, -220], [-75, 240], [-75, -240], [-50, 260], [-50, -260], [-75, 280], [-75, -280], [-50, 200], [-50, -200],    
                ],       
             ],
             [    
                [
                   [25, 20], 
                ],
             ]
          ]
       }, 



       '0':{  
          'float':[2,2],   
          'flag':[-50,0],
          'casual':2,      
          'hero':[20,0],
          'adjutant':[[0,60],[0,-60]],
          'army':[     
             [         
                [
                   [30, 60, 0], [30, -60, 2], [30, 80, 0], [30, -80, 2], [30, 800, 0], [30, -800, 2], [30, 220], [30, -220], [30, 240], [30, -240], [30, 260], [30, -260], [30, 280], [30, -280], [30, 200], [30, -200], [30, 220], [30, -220],
                   [0, 60, 0], [0, -60, 2], [0, 80, 0], [0, -80, 2], [0, 800, 0], [0, -800, 2], [0, 220], [0, -220], [0, 240], [0, -240], [0, 260], [0, -260], [0, 280], [0, -280], [0, 200], [0, -200], [0, 220], [0, -220],
                   [-30, 60, 0], [-30, -60, 2], [-30, 80, 0], [-30, -80, 2], [-30, 800, 0], [-30, -800, 2], [-30, 220], [-30, -220], [-30, 240], [-30, -240], [-30, 260], [-30, -260], [-30, 280], [-30, -280], [-30, 200], [-30, -200], [-30, 220], [-30, -220],
                ],
                [
                   [40, 60, 0], [40, -60, 2], [40, 80, 0], [40, -80, 2], [40, 800, 0], [40, -800, 2], [40, 220], [40, -220], [40, 240], [40, -240], [40, 260], [40, -260], [40, 280], [40, -280], [40, 200], [40, -200], [40, 220], [40, -220],
                   [5, 60, 0], [5, -60, 2], [5, 80, 0], [5, -80, 2], [5, 800, 0], [5, -800, 2], [5, 220], [5, -220], [5, 240], [5, -240], [5, 260], [5, -260], [5, 280], [5, -280], [5, 200], [5, -200], [5, 220], [5, -220],
                   [-30, 60, 0], [-30, -60, 2], [-30, 80, 0], [-30, -80, 2], [-30, 800, 0], [-30, -800, 2], [-30, 220], [-30, -220], [-30, 240], [-30, -240], [-30, 260], [-30, -260], [-30, 280], [-30, -280], [-30, 200], [-30, -200], [-30, 220], [-30, -220],
                ],
                [
                   [22, 60, 0], [22, -60, 2], [22, 80, 0], [22, -80, 2], [22, 800, 0], [22, -800, 2], [22, 220], [22, -220], [22, 240], [22, -240], [22, 260], [22, -260], [22, 280], [22, -280], [22, 200], [22, -200], [22, 220], [22, -220],
                   [-22, 60, 0], [-22, -60, 2], [-22, 80, 0], [-22, -80, 2], [-22, 800, 0], [-22, -800, 2], [-22, 220], [-22, -220], [-22, 240], [-22, -240], [-22, 260], [-22, -260], [-22, 280], [-22, -280], [-22, 200], [-22, -200], [-22, 220], [-22, -220],
                ],
             ],
             [    
                [
                   [30, 80], [30, -80], [30, 30], [30, -30], [30, 50], [30, -50], [30, 220], [30, -220], [30, 240], [30, -240], [30, 260], [30, -260], [30, 280], [30, -280], [30, 200], [30, -200], [30, 220], [30, -220],
                   [0, 80], [0, -80], [0, 30], [0, -30], [0, 50], [0, -50], [0, 220], [0, -220], [0, 240], [0, -240], [0, 260], [0, -260], [0, 280], [0, -280], [0, 200], [0, -200], [0, 220], [0, -220],
                   [-30, 80], [-30, -80], [-30, 30], [-30, -30], [-30, 50], [-30, -50], [-30, 220], [-30, -220], [-30, 240], [-30, -240], [-30, 260], [-30, -260], [-30, 280], [-30, -280], [-30, 200], [-30, -200], [-30, 220], [-30, -220],
                ],
                [
                   [30, 80], [30, -80], [30, 30], [30, -30], [30, 50], [30, -50], [30, 220], [30, -220], [30, 240], [30, -240], [30, 260], [30, -260], [30, 280], [30, -280], [30, 200], [30, -200], [30, 220], [30, -220],
                   [0, 80], [0, -80], [0, 30], [0, -30], [0, 50], [0, -50], [0, 220], [0, -220], [0, 240], [0, -240], [0, 260], [0, -260], [0, 280], [0, -280], [0, 200], [0, -200], [0, 220], [0, -220],
                   [-30, 80], [-30, -80], [-30, 30], [-30, -30], [-30, 50], [-30, -50], [-30, 220], [-30, -220], [-30, 240], [-30, -240], [-30, 260], [-30, -260], [-30, 280], [-30, -280], [-30, 200], [-30, -200], [-30, 220], [-30, -220],
                ],
                [
                   [22, 80], [22, -80], [22, 30], [22, -30], [22, 50], [22, -50], [22, 220], [22, -220], [22, 240], [22, -240], [22, 260], [22, -260], [22, 280], [22, -280], [22, 200], [22, -200], [22, 220], [22, -220],
                   [-22, 80], [-22, -80], [-22, 30], [-22, -30], [-22, 50], [-22, -50], [-22, 220], [-22, -220], [-22, 240], [-22, -240], [-22, 260], [-22, -260], [-22, 280], [-22, -280], [-22, 200], [-22, -200], [-22, 220], [-22, -220],
                ],
                [   
                   [25, 20], [25, -20], [25, 60], [25, -60], [25, 800], [25, -800], [25, 250], [25, -250], [25, 275], [25, -275], [25, 230], [25, -230],
                   [-30, 20], [-30, -20], [-30, 60], [-30, -60], [-30, 800], [-30, -800], [-30, 250], [-30, -250], [-30, 275], [-30, -275], [-30, 230], [-30, -230],
                ]
             ]
          ],
          'sp_army_group':[     
             [0,-2,0,0],      
             [0,0,0,0],      
          ],
          
          
          

       },  

       '2':{  
          'flag':[-220,-200],
          'hero':[80,0],
          'adjutant':[[-20,60],[-20,-60]],
          'army':[     
             [         
                [
                   [75, 0], [75, 25], [75, -25], [60, 50], [60, -50], [45, 75], [45, -75], [30, 800], [30, -800], [25, 225], [25, -225], [0, 250], [0, -250], [-25, 275], [-25, -275], [-30, 200], [-30, -200], [-45, 225], [-45, -225],
                   [25, 75,0], [25, -75,2], [0, 800,0], [0, -800,2], [-25, 225], [-25, -225], [-30, 250], [-30, -250], [-45, 275], [-45, -275], 
                   [-25, 75,0], [-25, -75,2], [-30, 800,0], [-30, -800,2], [-45, 225], [-45, -225],
                   [-45, 75,0], [-45, -75,2], [-30, 50,0], [-30, -50,2],
                ],
                [
                   [75, 0], [75, 25], [75, -25], [60, 50], [60, -50], [45, 75], [45, -75], [30, 800], [30, -800], [25, 225], [25, -225], [0, 250], [0, -250], [-25, 275], [-25, -275], [-30, 200], [-30, -200], [-45, 225], [-45, -225],
                   [25, 75,0], [25, -75,2], [0, 800,0], [0, -800,2], [-25, 225], [-25, -225], [-30, 250], [-30, -250], [-45, 275], [-45, -275], 
                   [-25, 75,0], [-25, -75,2], [-30, 800,0], [-30, -800,2], [-45, 225], [-45, -225],
                   [-45, 75,0], [-45, -75,2], [-30, 50,0], [-30, -50,2], 
                ],
                [   
                   [85, 0], [70, 25], [70, -25], [75, 50], [75, -50], [40, 75], [40, -75], [25, 800], [25, -800], [80, 225], [80, -225], [-5, 250], [-5, -250], [-20, 275], [-20, -275], [-95, 200], [-95, -200], [-50, 225], [-50, -225],
                   [-5, 75,0], [-5, -75,2], [-20, 800,0], [-20, -800,2], [-95, 225], [-95, -225], [-50, 250], [-50, -250], 
                   [-50, 75,0], [-50, -75,2], [-95, 50,0], [-95, -50,2], 
                ],

             ],
             [    
                [
                   [20, 80], [20, -80], [20, 30], [20, -30], [20, 50], [20, -50], [20, 70], [20, -70], [20, 75], [20, -75], [20, 280], [20, -280], [20, 230], [20, -230], [20, 250], [20, -250], [20, 270], [20, -270],
                   [-80, 80], [-80, -80], [-80, 30], [-80, -30], [-80, 50], [-80, -50], [-80, 70], [-80, -70], [-80, 75], [-80, -75], [-80, 280], [-80, -280], [-80, 230], [-80, -230], [-80, 250], [-80, -250], [-80, 270], [-80, -270],
                   [-40, 80], [-40, -80], [-40, 30], [-40, -30], [-40, 50], [-40, -50], [-40, 70], [-40, -70], [-40, 75], [-40, -75], [-40, 280], [-40, -280], [-40, 230], [-40, -230], [-40, 250], [-40, -250], [-40, 270], [-40, -270],
                ],
                [
                   [20, 80], [20, -80], [20, 30], [20, -30], [20, 50], [20, -50], [20, 70], [20, -70], [20, 75], [20, -75], [20, 280], [20, -280], [20, 230], [20, -230], [20, 250], [20, -250], [20, 270], [20, -270],
                   [-80, 80], [-80, -80], [-80, 30], [-80, -30], [-80, 50], [-80, -50], [-80, 70], [-80, -70], [-80, 75], [-80, -75], [-80, 280], [-80, -280], [-80, 230], [-80, -230], [-80, 250], [-80, -250], [-80, 270], [-80, -270],
                   [-40, 80], [-40, -80], [-40, 30], [-40, -30], [-40, 50], [-40, -50], [-40, 70], [-40, -70], [-40, 75], [-40, -75], [-40, 280], [-40, -280], [-40, 230], [-40, -230], [-40, 250], [-40, -250], [-40, 270], [-40, -270],
                ],
                [   
                   [80, 80], [80, -80], [80, 30], [80, -30], [80, 50], [80, -50], [80, 70], [80, -70], [80, 75], [80, -75], [80, 280], [80, -280], [80, 230], [80, -230], [80, 250], [80, -250], [80, 270], [80, -270],
                   [-95, 80], [-95, -80], [-95, 30], [-95, -30], [-95, 50], [-95, -50], [-95, 70], [-95, -70], [-95, 75], [-95, -75], [-95, 280], [-95, -280], [-95, 230], [-95, -230], [-95, 250], [-95, -250], [-95, 270], [-95, -270],
                ],
                [   
                   [25, 20], [25, -20], [25, 60], [25, -60], [25, 800], [25, -800], [25, 240], [25, -240], [25, 280], [25, -280],
                   [-40, 20], [-40, -20], [-40, 60], [-40, -60], [-40, 800], [-40, -800], [-40, 240], [-40, -240], [-40, 280], [-40, -280],
                ],
             ]
          ],
          'sp_army_group':[     
             [0,-2,-2,0],      
             [0,0,0,0],      
          ],
          
          
          

       },  

       '2':{  
          'flag':[40,-270],
          'hero':[-60,0],
          'adjutant':[[-80,60],[-80,-60]],
          'army':[     
             [         
                [
                   [75, 220], [75, -220], [60, 220], [60, -220], [60, 240], [60, -240], [30, 260], [30, -260], [0, 280], [0, -280], [-30, 200], [-30, -200],   
                   [30, 0], [30, 20], [30, -20], [30, 40], [30, -40], [30, 60], [30, -60], [30, 80], [30, -80], [30, 800], [30, -800], [30, 220], [30, -220], [30, 240], [30, -240], 
                   [0, 0], [0, 20], [0, -20], [0, 40], [0, -40], [0, 60], [0, -60], [0, 80], [0, -80], [0, 800], [0, -800], [0, 220], [0, -220], [0, 240], [0, -240], [0, 260], [0, -260], 
                   [-30, 220], [-30, -220], [-30, 240], [-30, -240], [-30, 260], [-30, -260], [-30, 280], [-30, -280], 
                ],
                [
                   [75, 220], [75, -220], [60, 220], [60, -220], [60, 240], [60, -240], [30, 260], [30, -260], [0, 280], [0, -280], [-30, 200], [-30, -200],   
                   [30, 0], [30, 20], [30, -20], [30, 40], [30, -40], [30, 60], [30, -60], [30, 80], [30, -80], [30, 800], [30, -800], [30, 220], [30, -220], [30, 240], [30, -240], 
                   [0, 0], [0, 20], [0, -20], [0, 40], [0, -40], [0, 60], [0, -60], [0, 80], [0, -80], [0, 800], [0, -800], [0, 220], [0, -220], [0, 240], [0, -240], [0, 260], [0, -260], 
                   [-30, 220], [-30, -220], [-30, 240], [-30, -240], [-30, 260], [-30, -260], [-30, 280], [-30, -280], 
                ],
                [   
                   [80, 220], [80, -220],  [40, 240], [40, -240], [0, 260], [0, -260],  [-40, 280], [-40, -280],   
                   [40, 0], [40, 20], [40, -20], [40, 40], [40, -40], [40, 60], [40, -60], [40, 80], [40, -80], [40, 800], [40, -800], [40, 220], [40, -220],  
                   [0, 0], [0, 20], [0, -20], [0, 40], [0, -40], [0, 60], [0, -60], [0, 80], [0, -80], [0, 800], [0, -800], [0, 220], [0, -220], [0, 240], [0, -240], 
                   [-40, 220], [-40, -220], [-40, 240], [-40, -240], [-40, 260], [-40, -260],
                ],
             ],
             [    
                [
                   [20, 240], [20, -240], [20, 260], [20, -260], [20, 280], [20, -280], [20, 200], [20, -200], [20, 220], [20, -220], [20, 240], [20, -240], [20, 260], [20, -260],
                   [-80, 220], [-80, -220], [-40, 280], [-40, -280], [-70, 240], [-70, -240], 
                   [-80, 240], [-80, -240], [-80, 260], [-80, -260], [-80, 280], [-80, -280], [-80, 200], [-80, -200],
                   [-40, 240], [-40, -240], [-40, 260], [-40, -260], 
                ],
                [
                   [20, 240], [20, -240], [20, 260], [20, -260], [20, 280], [20, -280], [20, 200], [20, -200], [20, 220], [20, -220], [20, 240], [20, -240], [20, 260], [20, -260],
                   [-80, 220], [-80, -220], [-40, 280], [-40, -280], [-70, 240], [-70, -240], 
                   [-80, 240], [-80, -240], [-80, 260], [-80, -260], [-80, 280], [-80, -280], [-80, 200], [-80, -200],
                   [-40, 240], [-40, -240], [-40, 260], [-40, -260], 
                ],
                [   
                   [80, 240], [80, -240], [80, 260], [80, -260], [80, 280], [80, -280], [80, 200], [80, -200], [80, 220], [80, -220], [80, 240], [80, -240], [80, 260], [80, -260],
                   [-30, 220], [-30, -220], [-70, 280], [-70, -280],
                   [-30, 240], [-30, -240], [-30, 260], [-30, -260], [-30, 280], [-30, -280], [-30, 200], [-30, -200],
                   [-70, 240], [-70, -240], [-70, 260], [-70, -260], 
                ],
                [   
                   [25, 240], [25, -240], [25, 280], [25, -280], [25, 220], [25, -220], [25, 260], [25, -260],
                   [-40, 240], [-40, -240], [-40, 200], [-40, -200], [-40, 260], [-40, -260], 
                ],
             ]
          ],
          'sp_army_group':[     
             [0,0,0,0],      
             [0,0,-2,0],      
          ],
          
          
          
       },  


       '3':{  
          'flag':[-280,-260],
          'hero':[20,0],
          'adjutant':[[50,60],[50,-60]],
          'army':[     
             [         
                [
                   [-45, 0], [-30, 25], [-30, -25], [-25, 50], [-25, -50], [0, 75], [0, -75], [25, 800], [25, -800], [30, 225], [30, -225], [45, 250], [45, -250], [60, 275], [60, -275], [75, 200], [75, -200], 
                   [30, 75,0], [30, -75,2], [45, 800,0], [45, -800,2], [60, 225], [60, -225], [75, 250], [75, -250], 
                   [75, 275], [75, -275], [75, 225], [75, -225], 
                ],
                [
                   [-45, 0], [-30, 25], [-30, -25], [-25, 50], [-25, -50], [0, 75], [0, -75], [25, 800], [25, -800], [30, 225], [30, -225], [45, 250], [45, -250], [60, 275], [60, -275], [75, 200], [75, -200], 
                   [30, 75,0], [30, -75,2], [45, 800,0], [45, -800,2], [60, 225], [60, -225], [75, 250], [75, -250], 
                   [75, 275], [75, -275], [75, 225], [75, -225], 
                ],
                [ 
                   [-45, 0], [-30, 25], [-30, -25], [-25, 50], [-25, -50], [0, 75], [0, -75], [25, 800], [25, -800], [30, 225], [30, -225], [45, 250], [45, -250], [60, 275], [60, -275], [75, 200], [75, -200], 
                   [95, 62,0], [95, -62,2], [50, 87,0], [50, -87,2], [65, 222], [65, -222], [80, 237], [80, -237],
                ],
             ],
             [    
                [
                   [25, 0],[30, 25], [30, -25], [45, 50], [45, -50], [60, 75], [60, -75], [75, 800], [75, -800], [75, 225], [75, -225], [805, 250], [805, -250], [220, 275], [220, -275], [295, 200], [295, -200], [250, 225], [250, -225],
                   [-25, 0],
                   [0, 25], [0, -25],[25, 50], [25, -50], [30, 75], [30, -75], [45, 800], [45, -800], [60, 225], [60, -225], [75, 250], [75, -250], [75, 275], [75, -275], [805, 200], [805, -200], [220, 225], [220, -225],
                ],
                [
                   [25, 0],[30, 25], [30, -25], [45, 50], [45, -50], [60, 75], [60, -75], [75, 800], [75, -800], [75, 225], [75, -225], [805, 250], [805, -250], [220, 275], [220, -275], [295, 200], [295, -200], [250, 225], [250, -225],
                   [-25, 0],
                   [0, 25], [0, -25],[25, 50], [25, -50], [30, 75], [30, -75], [45, 800], [45, -800], [60, 225], [60, -225], [75, 250], [75, -250], [75, 275], [75, -275], [805, 200], [805, -200], [220, 225], [220, -225],
                ],
                [ 
                   [5, 0],[20, 25], [20, -25], [95, 50], [95, -50], [50, 75], [50, -75], [65, 800], [65, -800], [80, 225], [80, -225], [95, 250], [95, -250], [280, 275], [280, -275], [225, 200], [225, -200], [240, 225], [240, -225],
                   [-45, 0],
                   [-30, 25], [-30, -25],[-25, 50], [-25, -50], [0, 75], [0, -75], [25, 800], [25, -800], [30, 225], [30, -225], [45, 250], [45, -250], [60, 275], [60, -275], [75, 200], [75, -200], [75, 225], [75, -225],
                ],

                [   
                   [5, 0], [26, 95], [26, -95],  [47, 70], [47, -70],  [68, 805], [68, -805], [89, 240], [89, -240], [280, 275], [280, -275],  [232, 280], [232, -280],
                   [-50, 0],[-29, 95], [-29, -95], [-8, 70], [-8, -70], [23, 805], [23, -805],  [34, 240], [34, -240], [75, 275], [75, -275],  [76, 280], [76, -280],
                ],
             ]
          ],
          
          
          
       },  

       '4':{  
          'flag':[-40,0],
          'hero':[30,0],
          'adjutant':[[30,60],[30,-60]],
          'army':[     
             [         
                [
                   [50, 220, 0], [50, -220, 2], [50, 240], [50, -240], [50, 260], [50, -260], [50, 280], [50, -280], [50, 200], [50, -200], [50, 220], [50, -220],[50, 240], [50, -240],
                   [-30, 80, 0], [-30, -80, 2], [-30, 800], [-30, -800], [-30, 220], [-30, -220], [-30, 240], [-30, -240], [-30, 260], [-30, -260], [-30, 280], [-30, -280], [-30, 200], [-30, -200], 

                   [80, 240, 0], [80, -240, 2], [80, 260], [80, -260], [80, 280], [80, -280], [80, 200], [80, -200], [80, 220], [80, -220],[80, 240], [80, -240],[80, 260], [80, -260],
                   [0, 800, 0], [0, -800, 2], [0, 220], [0, -220], [0, 240], [0, -240], [0, 260], [0, -260], [0, 280], [0, -280], [0, 200], [0, -200], [0, 220], [0, -220],
                ],
                [
                   [50, 220, 0], [50, -220, 2], [50, 240], [50, -240], [50, 260], [50, -260], [50, 280], [50, -280], [50, 200], [50, -200], [50, 220], [50, -220],[50, 240], [50, -240],
                   [-30, 80, 0], [-30, -80, 2], [-30, 800], [-30, -800], [-30, 220], [-30, -220], [-30, 240], [-30, -240], [-30, 260], [-30, -260], [-30, 280], [-30, -280], [-30, 200], [-30, -200], 

                   [80, 240, 0], [80, -240, 2], [80, 260], [80, -260], [80, 280], [80, -280], [80, 200], [80, -200], [80, 220], [80, -220],[80, 240], [80, -240],[80, 260], [80, -260],
                   [0, 800, 0], [0, -800, 2], [0, 220], [0, -220], [0, 240], [0, -240], [0, 260], [0, -260], [0, 280], [0, -280], [0, 200], [0, -200], [0, 220], [0, -220],
                ],
                [   
                   [60, 220, 0], [60, -220, 2], [60, 240], [60, -240], [60, 260], [60, -260], [60, 280], [60, -280], [60, 200], [60, -200], [60, 220], [60, -220],[60, 240], [60, -240],
                   [-20, 80, 0], [-20, -80, 2], [-20, 800], [-20, -800], [-20, 220], [-20, -220], [-20, 240], [-20, -240], [-20, 260], [-20, -260], [-20, 280], [-20, -280], [-20, 200], [-20, -200],
                   [20, 800, 0], [20, -800, 2], [20, 220], [20, -220], [20, 240], [20, -240], [20, 260], [20, -260], [20, 280], [20, -280],[20, 200], [20, -200],[20, 220], [20, -220],
                ],
             ],
             [    
                [
                   [30, 80], [30, -80], [30, 30], [30, -30], [30, 50], [30, -50], [30, 70], [30, -70], [30, 75], [30, -75], [30, 280], [30, -280], [30, 230], [30, -230], [30, 250], [30, -250], 
                   [0, 270], [0, -270], [-30, 275], [-30, -275],
                   [0, 250], [0, -250], [0, 230], [0, -230], [0, 280], [0, -280], [0, 75], [0, -75], [0, 70], [0, -70], [0, 50], [0, -50], [0, 30], [0, -30], [0, 80], [0, -80],
                   [-30, 270], [-30, -270], [-30, 250], [-30, -250], [-30, 230], [-30, -230], [-30, 280], [-30, -280], [-30, 75], [-30, -75], [-30, 70], [-30, -70], [-30, 50], [-30, -50], [-30, 30], [-30, -30], [-30, 80], [-30, -80], 
                ],
                [
                   [30, 80], [30, -80], [30, 30], [30, -30], [30, 50], [30, -50], [30, 70], [30, -70], [30, 75], [30, -75], [30, 280], [30, -280], [30, 230], [30, -230], [30, 250], [30, -250], 
                   [0, 270], [0, -270], [-30, 275], [-30, -275],
                   [0, 250], [0, -250], [0, 230], [0, -230], [0, 280], [0, -280], [0, 75], [0, -75], [0, 70], [0, -70], [0, 50], [0, -50], [0, 30], [0, -30], [0, 80], [0, -80],
                   [-30, 270], [-30, -270], [-30, 250], [-30, -250], [-30, 230], [-30, -230], [-30, 280], [-30, -280], [-30, 75], [-30, -75], [-30, 70], [-30, -70], [-30, 50], [-30, -50], [-30, 30], [-30, -30], [-30, 80], [-30, -80], 
                ],
                [   
                   [25, 80], [25, -80], [25, 30], [25, -30], [25, 50], [25, -50], [25, 70], [25, -70], [25, 75], [25, -75], [25, 280], [25, -280], [25, 230], [25, -230], [25, 250], [25, -250], 
                   [-25, 270], [-25, -270], 
                   [-25, 250], [-25, -250], [-25, 230], [-25, -230], [-25, 280], [-25, -280], [-25, 75], [-25, -75], [-25, 70], [-25, -70], [-25, 50], [-25, -50], [-25, 30], [-25, -30], [-25, 80], [-25, -80], 
                ],
                [   
                   [30, 20], [30, -20],  [30, 60], [30, -60], [30, 800], [30, -800],  [30, 240], [30, -240], 
                   [-20, 260], [-20, -260], [-20, 220], [-20, -220], [-25, 80], [-25, -80], [-25, 40], [-25, -40], [-25, 0],
                ],
             ]
          ],
          'sp_army_group':[     
             [0,0,-2,0],      
             [0,0,-2,-2],      
          ],
          
          
          
       },  

       '5':{  
          'flag':[-75,0],
          'hero':[-20,0],
          'adjutant':[[-40,60],[-40,-60]],
          'army':[     
             [         
                [
                   [85, 0], [82, 25], [82, -25], [74, 50], [74, -50], [63, 75], [63, -75], [42, 800], [42, -800], [25, 225], [25, -225], [-20, 240], [-20, -240],
                   [75, 0], [52, 25], [52, -25], [44, 50], [44, -50], [33, 75], [33, -75], [22, 800], [22, -800], [-20, 225], [-20, -225], 
                   [3, 70, 0], [3, -70, 2], [-20, 75, 0], [-20, -75, 2],
                   [80, -200], [80, 200], [80, -225], [80, 225], [-25, -285], [-25, 285], [-25, -240], [-25, 240],
                ],
                [
                   [85, 0], [82, 25], [82, -25], [74, 50], [74, -50], [63, 75], [63, -75], [42, 800], [42, -800], [25, 225], [25, -225], [-20, 240], [-20, -240],
                   [75, 0], [52, 25], [52, -25], [44, 50], [44, -50], [33, 75], [33, -75], [22, 800], [22, -800], [-20, 225], [-20, -225], 
                   [3, 70, 0], [3, -70, 2], [-20, 75, 0], [-20, -75, 2],
                   [80, -200], [80, 200], [80, -225], [80, 225], [-25, -285], [-25, 285], [-25, -240], [-25, 240],
                ],
                [  
                   [85, 0], [82, 25], [82, -25], [74, 50], [74, -50], [63, 75], [63, -75], [42, 800], [42, -800], [25, 225], [25, -225], [-20, 240], [-20, -240],
                   [52, 25], [52, -25], [40, 45], [40, -45], [25, 75], [25, -75], [-5, 805], [-5, -805], 
                   [-20, 75, 0], [-20, -75, 2],
                   [20, -200], [20, 200], [20, -225], [20, 225], [-25, -285], [-25, 285], [-25, -240], [-25, 240],
                ],
             ],
             [    
                [
                   [50, 240], [50, -240], [25, 225], [25, -225], [-22, 800], [-22, -800], [-33, 75], [-33, -75], [-44, 50], [-44, -50], [-52, 25], [-52, -25], [-75, 0], 
                   [50, 225], [50, -225], [28, 800], [28, -800],  [-3, 75], [-3, -75], [-24, 50], [-24, -50], [-22, 25], [-22, -25], [-25, 0],
                   [50, 75, 0], [50, -75, 2], [27, 70, 0], [27, -70, 2], 
                   [65, -288], [65, 288], [65, -237], [65, 237], [50, -222], [50, 222],  
                ],
                [
                   [50, 240], [50, -240], [25, 225], [25, -225], [-22, 800], [-22, -800], [-33, 75], [-33, -75], [-44, 50], [-44, -50], [-52, 25], [-52, -25], [-75, 0], 
                   [50, 225], [50, -225], [28, 800], [28, -800],  [-3, 75], [-3, -75], [-24, 50], [-24, -50], [-22, 25], [-22, -25], [-25, 0],
                   [50, 75, 0], [50, -75, 2], [27, 70, 0], [27, -70, 2], 
                   [65, -288], [65, 288], [65, -237], [65, 237], [50, -222], [50, 222],  
                ],
                [    
                   [40, 240], [40, -240], [5, 225], [5, -225], [-22, 800], [-22, -800], [-43, 75], [-43, -75], [-54, 50], [-54, -50], [-62, 25], [-62, -25], [-65, 0], 
                   [40, 225], [40, -225], [80, 800], [80, -800],  [-3, 75], [-3, -75], [-26, 50], [-26, -50], [-26, 25], [-26, -25], [-32, 0],
                   [40, 75, 0], [40, -75, 2],
                   [75, -288], [75, 288], [75, -237], [75, 237], [30, -222], [30, 222],  
                ],
                [   
                   [40, 245], [40, -245], [-8, 225], [-8, -225], [-48, 75], [-48, -75],  [-70, 45], [-70, -45],  [-78, 0], 
                   [30, 800], [30, -800],  [-5, 75], [-5, -75], [-25, 25], [-25, -25],  

                   [50, -295], [50, 295], [50, -230], [50, 230], 
                ],
             ]
          ],
          'sp_army_group':[     
             [0,-5,-3,0],      
             [0,0,2,-2],      
          ],
          
          
          
       },  


       '6':{  
          'flag':[60,0],
          'hero':[0,0],
          'adjutant':[[-70,40],[-70,-40]],
          'army':[     
             [         
                [
                   [-30, 75], [-30, 280], [0, 800], [0, 80], [0, 220], [30, 75], [30, 280], 
                   [-30, -75], [-30, -280], [0, -800], [0, -80], [0, -220], [30, -75], [30, -280], 

                   [-30, 200], [-30, 280], [-30, 220], [-30, 240],   [0, 275], [0, 280],[0, 230],   [30, 200], [30, 280], [30, 220],[30, 240],
                   [-30, -200], [-30, -280], [-30, -220], [-30, -240],   [0, -275], [0, -280],[0, -230],   [30, -200], [30, -280], [30, -220], [30, -240],

                   [60, 800], [60, 80], [60, 220], [75, 75], [75, 280],
                   [60, -800], [60, -80], [60, -220], [75, -75], [75, -280],
                ],
                [
                   [-30, 75], [-30, 280], [0, 800], [0, 80], [0, 220], [30, 75], [30, 280], 
                   [-30, -75], [-30, -280], [0, -800], [0, -80], [0, -220], [30, -75], [30, -280], 

                   [-30, 200], [-30, 280], [-30, 220], [-30, 240],   [0, 275], [0, 280],[0, 230],   [30, 200], [30, 280], [30, 220],[30, 240],
                   [-30, -200], [-30, -280], [-30, -220], [-30, -240],   [0, -275], [0, -280],[0, -230],   [30, -200], [30, -280], [30, -220], [30, -240],

                   [60, 800], [60, 80], [60, 220], [75, 75], [75, 280],
                   [60, -800], [60, -80], [60, -220], [75, -75], [75, -280],
                ],
                [   
                   [-30, 75], [-30, 280], [0, 80], [0, 220], [30, 75], [30, 280], 
                   [-30, -75], [-30, -280], [0, -80], [0, -220], [30, -75], [30, -280], 

                   [-30, 280], [-30, 280], [-30, 240],   [0, 295], [0, 225],    [30, 280], [30, 280],[30, 240],
                   [-30, -280], [-30, -280], [-30, -240],   [0, -295] ,[0, -225],   [30, -280], [30, -280], [30, -240],

                   [60, 80], [60, 220], [75, 75], [75, 280],
                   [60, -80],  [60, -220], [75, -75], [75, -280],
                ],
             ],
             [    
                [
                   [30, 75], [30, 280],[0, 800], [0, 80], [0, 220],  
                   [30, -75], [30, -280],[0, -800], [0, -80], [0, -220], 

                   [30, 200], [30, 280], [30, 220], [0, 275], [0, 280], 
                   [30, -200], [30, -280], [30, -220], [0, -275], [0, -280], 

                   [-30, 75], [-30, 280],  [-60, 800], [-60, 80], [-60, 220],
                   [-30, -75], [-30, -280],  [-60, -800], [-60, -80], [-60, -220],
                   [-30, 200], [-30, 280], [-30, 220], 
                   [-30, -200], [-30, -280], [-30, -220],
                ],
                [
                   [30, 75], [30, 280],[0, 800], [0, 80], [0, 220],  
                   [30, -75], [30, -280],[0, -800], [0, -80], [0, -220], 

                   [30, 200], [30, 280], [30, 220], [0, 275], [0, 280], 
                   [30, -200], [30, -280], [30, -220], [0, -275], [0, -280], 

                   [-30, 75], [-30, 280],  [-60, 800], [-60, 80], [-60, 220],
                   [-30, -75], [-30, -280],  [-60, -800], [-60, -80], [-60, -220],
                   [-30, 200], [-30, 280], [-30, 220], 
                   [-30, -200], [-30, -280], [-30, -220],
                ],
                [   
                   [30, 75], [30, 280], [0, 80], [0, 220],  
                   [30, -75], [30, -280], [0, -80], [0, -220], 

                   [30, 280], [30, 280], [30, 240], [0, 295], [0, 225], 
                   [30, -280], [30, -280], [30, -240], [0, -295], [0, -225], 

                   [-30, 75], [-30, 280],  [-60, 80], [-60, 220],
                   [-30, -75], [-30, -280],   [-60, -80], [-60, -220],
                   [-30, 280], [-30, 280], [-30, 240], 
                   [-30, -280], [-30, -280], [-30, -240],
                ],
                [   
                   [30, 80], [30, 220], 
                   [30, -80], [30, -220], 
                   [30, 280], [30, 270], [30, 250],
                   [30, -280], [30, -270],  [30, -250],

                   [-25, 800], [-80, 80], [-80, 220],  
                   [-25, -800], [-80, -80], [-80, -220],
                   [-25, 230], [-25, 275], 
                   [-25, -230], [-25, -275], 
                ],

             ]
          ],
          'sp_army_group':[     
             [0,-9,-6,0],      
             [0,0,2,-2],      
          ],
          
          
          
       },  
   },

 

   'weakLaterSkin':{     
      'weakGodScore':'bar_sl_power',
      'resGodElement0':'bar_sl_huo',
      'resGodElement2':'bar_sl_jin',
      'resGodElement2':'bar_sl_mu',
      'resGodElement3':'bar_sl_tu',
      'resGodElement4':'bar_sl_shui',
   },  

   'fightDefaultLoadAniArr':[     
      "army00", "hero_02", "bullet802", "hit802", "bang238", "fire225", "stick226", "special222", "buff226",
      'equipBall','skillFate'
   ],  

   'heroDeadEffect':{
      'res':'hitDead',      
      'resScale':2,   
      'resDelay':500,   

      'forceZ':800,   
      'resRotaFixed':0,                 
   },

   'countrySpeedUp': 'item287',      
   'duplicateSpeedUp': 'duplicate_speedup',      

   'duplicateRushEffectId': 'duplicate_speedup',      
   'duplicateRushAlpha': 0.5,      
   'duplicateRushAdd': 2,  
   'duplicateRushHeroScale': 2,      
   'duplicateRushHeroX': 80, 

   'duplicateRushPointScale': 0.8, 
   'duplicateRushPointX': 0, 
   
   'duplicateRushUIColor': '
   'duplicateRushUIStrokeColor': '
   'duplicateRushUIAlpha': 2, 
   'duplicateRushUIScaleX': 0.8,  
   'duplicateRushUIScaleY': 0.7,  
   'duplicateRushUIX': 0,  
   'duplicateRushUIY': 2, 

   'equipRiseBall':{  
       'res':'equipBall',          
       'delay':[0,400,2200,75000,2600],                 
       'offset':[-40,-20,20,0,40],                 
       'colorArr':[8,8,8,22,26,20,24],          
       'shadow':[[80,0.3],[40,0.24],[70,0.28],[800,0.22],[230,0.06]],          

   },

   'shogunColorArr':[0,0,0.25,0.5,0.75,2],          
   'bashShowRate':2,          

   'heroDeadSound':{
       '0':['deadSex0', 2, 0],         
       '2':['deadSex2', 2, 0],         
   },

   'showFightFirst':2,  
   'showFightAdept':2,  

   'fightBGM':{
       '0':'music/bg/fight_2.aac',         
       '80':'music/bg/duplicate_fight.aac',         
       'default':'music/bg/fight_2.aac',         
   },

   
   'noAtlasAnimations':{
       'home08':2,
       'home09':2,
       'bullet802':2,
       'building_ingL':2,
       'building_ingM':2,
       'building_ingS':2
   },


   
   'heroRadarCfg':{
       
       'maxPropRank':[
          [280,{'color':6,'fullValue':240,'nullValue':75}],
          [280,{'color':5,'fullValue':280,'nullValue':70}],
          [250,{'color':4,'fullValue':280,'nullValue':65}],
          [220,{'color':3,'fullValue':250,'nullValue':60}],
          [75,{'color':2,'fullValue':230,'nullValue':75}],
          [0,{'color':2,'fullValue':280,'nullValue':50}],
       ],
       
       'propValueRank':[
          [280,{'color':6}],
          [280,{'color':5}],
          [250,{'color':4}],
          [220,{'color':3}],
          [75,{'color':2}],
          [60,{'color':2}],
          [0,{'color':0}],
       ],

       
       'propMappingArr':[
          {'type':'str', 'name':'info_str_0'},
          {'type':'agi', 'name':'info_agi_0'},
          {'type':'cha', 'name':'info_cha_0'},
          {'type':'pol', 'name':'info_pol_0'},
          {'type':'lead', 'name':'info_lead_0'},
       ],
       
       'maxImgScale':2.7,
       
       'minImgScale':0.2,

       
       'imgBgRes':'img_radar00',
       
       'imgVertexRes':'img_radar22',
   },

   
   'fightClearHeroImages':2,

   
   'fightSlantScene':0,

   
   'fightFlagAni':0,

   
   'cfg':{

      
      'PERSON_SIZE':{
      	'hero959':6.5,
      	'hero360':6.5,
      	'hero362':6.5,
      	'hero362':6.5,
      	'hero363':6.5,

      	'hero364':6.5,
      	'hero365':6.5,
      	'hero366':6.5,
      	'hero367':6.5,
      	'hero368':6.5,

      	'hero423':6.5,
      },

      
      'ARMY_SIZE':{
      	'army04':2,
      	'army05':2,
      	'army06':2,
      	'army80':2,
      	'army22':2,
      	'army22':2,
      	'army23':2,
      	'army24':2,
      	'army25':2,
      	'army26':2,
      	'army25':2,
      	'army95':2,
        
        
      	'sp3000':2,  
      	'sp3002':2,  
      	'sp3002':2,  
      	'sp3003':2,  
      	'sp3080':2,  
      	'sp3022':2,  
      	'sp3022':2,  
      	'sp3023':2,  
      	'sp3050':2,  
      	'sp3052':2,  

      	'sp3800':2,  
      	'sp3802':2,  
      	'sp3802':2,  
      	'sp3803':2,  
      	'sp3280':2,  
      	'sp3222':2,  
      	'sp3222':2,  
      	'sp3223':2,  
      	'sp3250':2,  
      	'sp3252':2, 

      	'sp3200':2,  
      	'sp3202':2,  
      	'sp3202':2,  
      	'sp3203':2,  
      	'sp3280':2,  
      	'sp3222':2,  
      	'sp3222':[3, {'fireZ':95,'hitZ':95,'buffZ':95,'buffScale':2.95,'forceScale':0.5}],   
      	'sp3223':2,  
      	'sp3250':3,   
      	'sp3252':[3, {'fireZ':60,'hitZ':95,'buffZ':95,'buffScale':2.95,'forceScale':0.5}],   

      	'sp3300':2,  
      	'sp3302':2,  
      	'sp3302':2,  
      	'sp3303':2,  
      	'sp3380':2,  
      	'sp3322':3,  
      	'sp3322':[2, {'fireZ':95,'hitZ':95,'buffZ':25,'buffScale':2.2,'forceScale':2.2}],   
      	'sp3323':2,  
      	'sp3950':2,  
      	'sp3952':2, 

      	'army850':[3, {'fireZ':0,'hitZ':25,'buffZ':25,'buffScale':3,'forceScale':0.5}],   
      	'army860':[3, {'fireZ':0,'hitZ':95,'buffZ':25,'buffScale':5,'forceScale':0,'buffEff':{'res':'buff860','scale':2}}],   

      	'army852':[3, {'fireZ':25,'hitZ':25,'buffZ':25,'buffScale':2.5,'forceScale':0.5}],   
      	'army862':[3, {'fireZ':50,'hitZ':95,'buffZ':95,'buffScale':4.5,'forceScale':0,'buffEff':{'res':'buff860','scale':2}}],   

      	'army852':[3, {'fireZ':25,'hitZ':25,'buffZ':25,'buffScale':3,'forceScale':0.5}],   
      	'army862':[3, {'fireZ':95,'hitZ':95,'buffZ':25,'buffScale':5,'forceScale':0,'buffEff':{'res':'buff860','scale':2}}],   

      	'army853':[3, {'fireZ':-5,'hitZ':25,'buffZ':25,'buffScale':3.5,'forceScale':0.5}],   
      	'army863':[3, {'fireZ':25,'hitZ':95,'buffZ':95,'buffScale':5.5,'forceScale':0,'buffEff':{'res':'buff860','scale':2}}],   

      	'army854':[3, {'fireZ':25,'hitZ':25,'buffZ':25,'buffScale':3.5,'forceScale':0.5}],   
      	'army864':[3, {'fireZ':95,'hitZ':95,'buffZ':25,'buffScale':5.5,'forceScale':0,'buffEff':{'res':'buff860','scale':2}}],   

      	'army875':[3, {'fireZ':25,'hitZ':25,'buffZ':25,'buffScale':3.5,'forceScale':0.5}],   
      	'army865':[3, {'fireZ':95,'hitZ':95,'buffZ':25,'buffScale':5.5,'forceScale':0,'buffEff':{'res':'buff860','scale':2}}],   

      	'hero423':[3, {'fireZ':95,'hitZ':95,'buffZ':25,'buffScale':5.5,'forceScale':0}],   
      },




      
      'SP_ARMY_ICON_FORMATION':{
      	'0':[
            [-48, 52, 0],       
            [62, 38, 0.34], [-38, 38, 0.34],
            [76, 24, 00.678], [-24, 24, 0.67],
            [-75, 80, 2],[75, 80, 2], [-80, 80, 2],
            [76, -4, 2.33], [-24, -4, 2.33],
            [62, -28, 6.56], [-38, -28, 6.56],
            [-50, -32, 2], 
      	],
      	'2':[
            [-50, 50, 0],       
            [70, 34, 0.4], [-30, 34, 0.4],
            [75, 28, 0.8], [-80, 28, 0.8],
            [-75, 80, 2],
            [75, 2, 2.2], [-80, 2, 2.2],
            [70, -24, 6.5], [-30, -24, 6.5],
            [-50, -30, 2], 
      	],
      	'2':[
            [-50, 50, 0],       
            [70, 30, 0.5], [-30, 30, 0.5],
            [75, 80, 2], [-80, 80, 2],  [-75, 80, 2],
            [70, -80, 2.5], [-30, -80, 2.5],
            [-50, -30, 2], 
      	],
      	'3':[
            [-40, 50, 0],       
            [80, 25, 0.6], [-20, 25, 0.6],
            [-75, 80, 2],
            [80, -5, 2.4], [-20, -5, 2.4],
            [-40, -30, 2], 
      	],
      	'sp3252':[     
            [-50, 60, 0],       
            [70, 95, 0.6], [-30, 95, 0.6],
            [-75, 20, 2],
            [70, 5, 2.4], [-30, 5, 2.4],
            [-50, -20, 2],
      	],
      },
      'SP_ARMY_ICON_STATENAME':'attack|stand|run|run|run|run|attack|stand|cheer|cheer|stand|stand|stand',
      
      'SP_ARMY_ICON_BG':{
      	 'img':'army_bg',
      	 'path':'later',
         'alpha':0.6,
         'scaleX':2.7,
         'scaleY':2.2,
         'y':25,
         'anchorX':0.5,
         'anchorY':0.5,
      },



      
      'ARMY_ICON_FORMATION':{
      	'0':[
            [0, 33, 0], [-25, 23, 2], [25, 23, 2], [-28, -4, 2], [0, -4, 2], [28, -4, 2]
      	],
      	'2':[
            [0, 33, 0], [-25, 23, 2], [25, 23, 2], [-28, -4, 2], [0, -4, 2], [28, -4, 2]
      	],
      	'2':[
            [-3, 32, 0, 2.2], [23, 22, 2, 2.2], [-25, 2, 2, 2.2]
      	],
      	'3':[
            [5, 30, 0], [-5, 0, 2]
      	],
      	'sp3252':[     
            [80, 40, 2], [-80, 20, 2]
      	],
      },
      
      'ARMY_ICON_BG':{
      	 'img':'army_bg',
      	 'path':'later',
         'alpha':0.3,
         'scale':0.7,
         'y':25,
         'anchorX':0.5,
         'anchorY':0.5,
      },

      
      'ARMY_SIZE_COMPATIBLE':{
         '0':{'2':2, '2':[3,0], '3':[3,0]},
         '2':{'0':2, '2':2, '3':3,},
         '2':{'0':2, '2':2, '3':3,},
         '3':{'0':[3,2],'2':[3,2]},
      },

      
      'ARMY_SIZE_VARS':{

         '0':{
            'fireZ':20,       
            'hitZ':20,        
            'buffZ':80,      
            'buffScale':2,    
            'forceScale':2,     
         },
         '2':{
            'fireZ':20,       
            'hitZ':20,        
            'buffZ':80,      
            'buffScale':2.05,    
            'forceScale':0.9,    
         },
         '2':{
            'fireZ':22,       
            'hitZ':22,        
            'buffZ':22,      
            'buffScale':2.2,    
            'forceScale':0.8,    
         },
         '3':{
            'fireZ':25,       
            'hitZ':25,        
            'buffZ':25,      
            'buffScale':2.2,    
            'forceScale':0.65,    
         },
         'hero':{
            'fireZ':25,       
            'hitZ':25,        
            'buffZ':25,      
            'buffScale':2.25,    
            'forceScale':0.7,    
         },
         'boss':{
            'fireZ':50,       
            'hitZ':50,        
            'buffZ':50,      
            'buffScale':6.5,    
            'forceScale':0.2,    
         },
      },

      
      'SP_ARMY_LV_VARS':[
         {                        
            'res':['sp','base'],      
         },
         {                        
            'res':['sp'],      
         },
         {                        
            'res':['sp'],      
            'buffEff':['buff_sp',''],  
         },
         {                        
            'res':['sp'],      
            'buffEff':['buff_sp'],        
         },
         {                        
            'res':['sp'],      
            'buffEff':['buff_sp2','buff_sp'],   
         },
      ],

      
      'BURN_H_S_B':{
         2:[0.7,2,2],      
         3:[0.5,2,2],      
         4:[0.3,2,2],      
         5:[0.9,2,2.5],      
         6:[0,2,4],        
      },

      
      'COLOR_TYPE_H_S_B':{
         0:[0,0,2.2],      
         2:[0.7,0.7,2],      
         2:[0.4,0.8,2],      
         3:[0.22,0.9,2],      
         4:[0.9,2.2,2.3],    
         6:[0.2,2.2,2.2],        
      },

      
      'DAMAGE_NUM_SCALES':[
         [0.9],
         [2, 0.9],
         [2.05, 0.95, 0.9],
         [2.2, 2, 0.95, 0.9],
         [2.05, 0.95, 0.9, 0.85, 0.8],
         [2, 0.9, 0.85, 0.8, 0.75, 0.7],
         [0.95, 0.85, 0.8, 0.75, 0.7, 0.65, 0.6]
      ],

      
      'ROUND_SHOW_TYPE':0,

      
      
      'BULLET_DISTANCE_NEAR':400,
      
      'BULLET_FIRE_Z':20,
      
      'HIT_Z':20,
      
      'BUFF_Y':-80,
      
      
      'TROOP_INFO_Y':-200,
      'TROOP_INFO_Z':60,

      
      'LAYER_UNIT_Y':30,

      
      'MOVE_ATTACK_ANI_TIME':500, 
      
      'BACK_MOVE_DELAY_TIME':300, 
      
      'BE_HIT_SORT_DELAY_TIME':8000, 

      'SPIRIT_X':800,
      'BASH_X':223,
      'BANNER_SKILL_X':250,
      'BANNER_BEAST_SKILL_X':250,
      'DROP_X':275,
      'VISIBLE_HALF_WIDTH':600,
      'TROOP_SHOW_MAX':5,
      'ARMY_OFFSET':[0, -280],
      'TROOP_INTERVAL':320,
      'ROUND_OFFSET':[ -280, -280, -260, -280],
      'BULLET_RANGE_RATE':2,
      
      'SHOCK_INTERVAL':250,

      'SCENE_SCALE':2,   
      'CAMERA_RATE':2,   

      'X_TO_SCREEN':[2,0],    
      'Y_TO_SCREEN':[0,2],    
      'Z_TO_SCREEN':[0,-2],   


      'GOD_UI_SCALE':0.8,   
      'GOD_UI_SIDE_Y':45,   
      'GOD_UI_SIDE_Y':220,   

      'GOD_ANGER_MASK_LOOP_ROTA':4,   
      'GOD_SKILL_TWEEN_TIME':500,   
      'GOD_SKILL_BOX_SCALE':2.2,   
      'GOD_SKILL_BOX_X':-60,   
      'GOD_SKILL_BOX_Y':-475,   
      'GOD_SKILL_MASK_SKINS':['fight_god_80'],   
      'GOD_SKILL_MASK_OFFSET_INDEX':2,   

      'GOD_SKILL_MASK_CHANGE_TIME':220,   
      'GOD_SKILL_MASK_SCALE_X':2.38,   
      'GOD_SKILL_MASK_SCALE_Y':2.75,   
      'GOD_SKILL_MASK_ROTATION':40,   
      'GOD_SKILL_MASK_X':53,   
      'GOD_SKILL_MASK_Y':200,   

      'GOD_SKILL_BG_SCALE_X':2.02,   
      'GOD_SKILL_BG_SCALE_Y':0.95,   
      'GOD_SKILL_BG_X':0,   
      'GOD_SKILL_BG_Y':5,   
      'GOD_SKILL_BG_ALPHA':0.6,   

		
      'GOD_SKILL_TWEEN_MOVE_X':800,   
      'GOD_SKILL_TWEEN_MOVE_Y':-220,   
      'GOD_SKILL_OFFSET_X':40,   
      'GOD_SKILL_OFFSET_Y':-75,   
		
      'GOD_SKILL_ANI_SCALE':0.75,   
      'GOD_SKILL_ANI_ALPHA':0.6,   
      'GOD_SKILL_ANI_LIGHTER_ALPHA':0.3,   

      'GOD_FIRE_ANI_SCALE':2.2,  

      'GOD_SKILL_TEXT_SCALE':0.9,   
      'GOD_SKILL_TEXT_X':50,   
      'GOD_SKILL_TEXT_Y':-275,   
      'GOD_SKILL_TEXT_BG_X':4,   
      'GOD_SKILL_TEXT_BG_Y':-30,   
      'GOD_SKILL_TEXT_BG_ALPHA':0.9,   
      'GOD_SKILL_TEXT_BG_SCALE':2.8,   

      'GOD_SKILL_SCALE':2,   
      'GOD_SKILL_TIME':4000,  
      'GOD_SKILL_DELAY':75000,  

      'GOD_SKILL_SOUND_RES':'fire0002',  
      'GOD_SKILL_SOUND_DELAY':500,  

      'TROOP_INFO_TYPE':2,     
      'FIGHT_HERO_TYPE':2,     
   },

   
   'cfgHorizontal':{
      'SPIRIT_X':600,
      'BASH_X':750,
      'BANNER_SKILL_X':750,
      'BANNER_BEAST_SKILL_X':750,
      'DROP_X':600,
      'VISIBLE_HALF_WIDTH':8000,
      'TROOP_SHOW_MAX':5,
      'ARMY_OFFSET':[0, -220],
      'TROOP_INTERVAL':330,
      'ROUND_OFFSET':[ -360, -360, -240, -220],
      'BULLET_RANGE_RATE':2.5,

      'GOD_UI_SCALE':2,   
      'GOD_UI_SIDE_Y':70,   
      'GOD_UI_SIDE_Y':250,   

      'GOD_SKILL_BOX_X':75,   
      'GOD_SKILL_BOX_Y':-485,   

      'GOD_SKILL_MASK_ROTATION':60,   
      'GOD_SKILL_MASK_X':-23,   
      'GOD_SKILL_MASK_Y':226,   

		
      'GOD_SKILL_TWEEN_MOVE_X':240,   
      'GOD_SKILL_TWEEN_MOVE_Y':-70,   
      'GOD_SKILL_OFFSET_X':80,   
      'GOD_SKILL_OFFSET_Y':25,   
		
      


      'GOD_SKILL_TEXT_SCALE':2,   
      'GOD_SKILL_TEXT_X':800,   
      'GOD_SKILL_TEXT_Y':-250,   
      'GOD_SKILL_TEXT_BG_X':4,   
      'GOD_SKILL_TEXT_BG_Y':-30,   
      'GOD_SKILL_TEXT_BG_ALPHA':0.9,   
      'GOD_SKILL_TEXT_BG_SCALE':2.8,   

      'GOD_SKILL_SCALE':2.2,   
   },

   
   'fightSceneModePointer':{
      '5':0,      
      '202':0,      
   },


   
   'fightSceneMapping':{
      '0^7':'s002',    
      '0%map002':'s002',    
      '0%map002_':'s002',    
      '0%map002':'s002',    
      '0%map002_':'s002',    

      '0$9':'s007',    
      '0$8':'s006',    
      '0$7':'s000',    
      '0$5':'s005',    
      '0$4':'s005',    
      '0$3':'s004',    
      '0$2':'s003',    
      '0$2':'s002',    
      '0$0':'s000',    
      '0':'s000',      

      '2':'s999',      
      '3':'s200a',      
      
      '6':'s200',      
      '80':'s200',      

      '80$0':'s999_0',      
      '80$2':'s005_0',      
      '80$2':'s005_0',      
      '80$3':'s004_0',      
      '80$4':'s200_0',      
      '80$5':'s200a_0',      
      '80$6':'f502_0',      
      '80$7':'s002_0',      
      '80':'s000_0',      


      
      '800$000':'s000_0',      
      '800$002':'s000_2',      
      '800$002':'s000_2',      
      '800$003':'s000_3',      
      '800$800':'s002_0',      
      '800$802':'s002_2',      
      '800$802':'s002_2',      
      '800$803':'s002_3',      
      '800$200':'s002_0',      
      '800$202':'s002_2',      
      '800$202':'s002_2',      
      '800$203':'s002_3',      
      '800$300':'s003_0',      
      '800$302':'s003_2',      
      '800$302':'s003_2',      
      '800$303':'s003_3',      
      '800$400':'s004_0',      
      '800$402':'s004_2',      
      '800$402':'s004_2',      
      '800$403':'s004_3',      
      '800$500':'s005_0',      
      '800$502':'s005_2',      
      '800$502':'s005_2',      
      '800$503':'s005_3',      
      '800$600':'s006_0',      
      '800$602':'s006_2',      
      '800$602':'s006_2',      
      '800$603':'s006_3',      
      '800$700':'s007_0',      
      '800$702':'s007_2',      
      '800$702':'s007_2',      
      '800$703':'s007_3',      
      '800$2000':'s200a_0',      
      '800$2002':'s200a_2',      
      '800$2002':'s200a_2',      
      '800$2003':'s200a_3',      
      '800$975':'s999_0',      
      '800$992':'s999_2',      
      '800$992':'s999_2',      
      '800$993':'s999_3',      
      '800':'s200',      


      '802':'s200',      
      '802':'s200a',      
      '803':'s200',      
      '804':'s200',      

      '200':'f502',      
      '202':'f502',      
      
      '203':'f503',      

   },
   
   'fightScene':{

       'f502':{
          'name':'固雪502',
          'fixed':'f502',                                   
          
       },
       'f502':{
          'name':'固沙502',
          'fixed':'f502',                                   
          
       },
       'f503':{
          'name':'固赤壁503',
          'fixed':'f503',                                   
          
       },

       's999':{  
          'name':'平地',
          'center_res':'del',    
       },

       's000':{  
          'name':'要塞000',
          
          'center_res':['ce000','ce000','ce000','ce000_3'],     
          
          
       },
       's002':{ 
          'name':'码头002',
          
          'center_res':['ce002','ce002','ce002','ce002_3'],     
          'center_x':-95,
          'center_y':-320, 
       },
       's002':{ 
          'name':'县城002',
          'center_res':['ce002','ce002','ce002','ce002_3'],     
          'center_x':-80,
          'center_y':-320, 
       },
       's003':{ 
          'name':'郡城003',
          'center_res':['ce003','ce003','ce003','ce003_3'],     
          'center_x':-80,
          'center_y':-950, 
       },
       's004':{ 
          'name':'关城004',
          'center_res':['ce004','ce004','ce004','ce004_3'],     
          'center_x':-25,
          'center_y':-320, 

          'far_num':0,
       },
       's005':{ 
          'name':'都城005',
          'center_res':['ce005','ce005','ce005','ce005_3'],     
          'center_x':20,
          'center_y':-320, 

          'far_num':0,
       },
       's006':{ 
          'name':'襄阳城门006',
          'ground_res':['g200','g200','g200','g200_3'],     
          'center_res':'ce006_2',     
          'center_x':-20,
          'center_y':-330, 

          'surface_num':0,
          'far_num':0,
          'near_num':0,
       },
       's007':{ 
          'name':'襄阳城007',
          'ground_res':['g200','g200','g200','g200_3'],     

          'curtain_res':'cu007',     
          'curtain_num':4,            
          'curtain_interval':822,     
          'curtain_y':-270,            

          'center_res':'ce007_2',  
          'center_x':-20,
          'center_y':-950,    

          'surface_num':0,
          'far_num':0,
          'near_num':0,
       },
       's200':{ 
          'name':'擂台土地200',
         

          'center_res':['ce200','ce200','ce200','ce200_3'],     
          'center_x':0,
          'center_y':-330, 

          'far_num':0, 
          'near_num':0, 
          
       },
       's200a':{ 
          'name':'擂台石地200a',       
          'inherit':'s007',         

          'center_res':['ce200','ce200','ce200','ce200_3'],     
          'center_x':0,
          'center_y':-330, 

          
       },

       'default':{   
          'name':'默认000',
          'atlas_season':['scene000_0','scene000_2','scene000_2','scene000_3'],        
          'render_arr':['wbg2','wbg2','wbg3','w2','w2','w3','w4','w5'],    
          'render_layers':{
          },
          'all_alpha':2,

          'perspective':0.0022,
          'trans_screen_y':0.9,

          'ground_res':['g000_0','g000_2','g000_2','g000_3'],     
          'ground_interval':478,      
          'ground_y':200,      
          'ground_num':4,                    
          'ground_flip':2,                   
          'ground_atan_rate':0.00092,        
          'ground_rate':2.2,                 

          'sky_interval':634,     
          'sky_num':4,            
          'sky_y':-422,           
          'sky_rate':0.4,         
          'sky_scale':2,          

          'curtain_res':['cu000_0','cu000_2','cu000_2','cu000_3'],     
          'curtain_num':4,            
          'curtain_interval':822,     
          'curtain_y':-260,           
          'curtain_rate':0.70,  
          'curtain_scale':2,          

          'center_res':['ce000','ce000','ce000','ce000_3'],     
          'center_x':20,          
          'center_y':-300,        
          'center_rate':0.72, 
          'center_scale':2,          

          'surface_res_arr':['s2', 's2', 's3'],     
          'surface_num':28, 
          'surface_interval':280,     
          'surface_x_range':[ -70, 70],    
          'surface_y_range':[ -230, 280],
          'surface_alpha_range':[ 0.4, 0.7],
          'surface_scale':2.2,        

          'far_res_arr':['f2', 'f2', 'f3', 'f4', 'f5'],     
          'far_num':24, 
          'far_interval':200,     
          'far_x_range':[ -80, 80],    
          'far_y_range':[ -380, -260],
          'far_scale':2,        

          'near_res_arr':['n2', 'n2', 'n3'],     
          'near_num':24, 
          'near_interval':200,     
          'near_x_range':[ -80, 80],    
          'near_y_range':[ 260, 370],
          'near_scale':0.7,        


       },
   },

   
   'weather_render_arr':['wbg2','wbg2','wbg3','w2','w2','w3','w4','w5'],   

   
   'fightWeather':{
       '2_2':{   
          'inherit':'2_default',
       },
       '2_default':{   
          'render_layers':{
              'wbg2':{                    
                'res':'w7500',           
                
                
                'interval':8024,                                    
                'w':522,                                       
                'h':522,                                       
                'x':500,                                            
                'y':-400,                                           
                'num':4,                    
                'alpha':0.5,                 
                'scaleY':2, 
                'scale':2,        
             },
              'w2':{                    
                'res':'pFight750_2',           
                'emissionRate':230,           
                'maxPartices':300,           
                'advanceTime':5,           
                'duration':5,              

                'interval':75000,                                    
                'x':600,                                            
                'y':50,                                           
                'num':3,                    
                'rotation':0,
                'scale':0.8,                  
                'rate':2.2,                 
             },
              'w3':{                    
                'res':'pFight750_5',           
                'emissionRate':230,           
                'maxPartices':300,           
                'advanceTime':5,           
                'duration':5,              

                'interval':75000,                                    
                'x':700,                                            
                'y':50,                                           
                'num':3,                    
                'rotation':0,
                'scale':0.8,                  
                'rate':2.2,                 
             },
              'w2':{                    
                'res':'pFight750_4',           
                'emissionRate':80,           
                'maxPartices':800,           
                'advanceTime':3,           
                'duration':5,              

                'w':8000,                                       
                'h':8000,                                       

                'interval':8000,                                    
                'x':500,                                            
                'y':0,                                           
                'num':3,                    
                'rotation':0,     
                'scale':3.5,                  
                'rate':2.2,                 
             },
          },
       },
       '2_default':{   
          'render_layers':{
             
             
             
             
             
             
             
             
              'wbg2':{                    
                'res':'w7580',           
               
                'interval':8024,                                    
                'w':522,                                       
                'h':522,                                       
                'x':0,                                            
                'y':-400,                                           
                'num':4,                    
                'alpha':0.5,                 
                'scaleY':2, 
                'scale':2,        
             },
              'wbg2':{                    
                'res':'pFight752_4',           
                'emissionRate':800,           
                'maxPartices':8000,           
                'advanceTime':3,           
                'duration':6,              

                'interval':8000,                                    
                'x':0,                                            
                'y':800,                                           
                'num':3,                    
                'scaleX':2,                  
                'scaleY':0.5,                  
                'scale':0.6,                  
                
             },
             'w2':{                    
                'res':'pFight752_2',           
                'emissionRate':500,           
                'maxPartices':400,           
                'advanceTime':2,           
                'duration':0.5,              
                'interval':8000,                                    
                
                'y':-400,                                           
                'rotation':20,
                'num':3,                    
                'scale':0.8,                  
                'rate':2.2,                 
             },
             'w2':{                    
                'res':'pFight752_2',           
                'emissionRate':500,           
                'maxPartices':400,           
                'advanceTime':2,           
                'duration':0.5,              

                'interval':8000,                                    
                
                'y':-600,                                           
                'num':3,                    
                'rotation':80,
                
                'scale':0.6,                  
                'rate':2.2,                 
             },
             'w2':{                    
                'res':'pFight752_3',           
                'emissionRate':500,           
                'maxPartices':400,           
                'advanceTime':2,           
                'duration':0.5,              
                'interval':8000,                                    
                
                'y':-600,                                           
                'num':3,                    
                'rotation':25,
                'scale':2,                  
                'rate':2.2,                 
             },
          },
       },
       '3_default':{   
          'render_layers':{
              'wbg2':{                    
                'res':'w7520',           
               
                'interval':8024,                                    
                'w':522,                                       
                'h':522,                                       
                'x':0,                                            
                'y':-400,                                           
                'num':4,                    
                'alpha':0.5,                 
                'scaleY':2, 
                'scale':2,          
             },
             'w2':{                    
                'res':'pFight752_2',           
                'emissionRate':5,           
                'maxPartices':220,           
                'advanceTime':20,           
                'duration':20,              
                'interval':500,                                    
                'w':2000,                                       
                'h':8000,                                       
                'y':-200,                                           
                'num':3,                    
                'scale':3.5,                  
                'rate':2,                 
             },
             'w2':{                    
                'res':'pFight752_2',           
                'emissionRate':5,           
                'maxPartices':75,           
                'advanceTime':80,           
                'duration':80,              
                'interval':500,                                    
                'w':2000,                                       
                'h':8000,                                       
                'y':50,                                           
                'num':3,                    
                'scale':4,                  
                'rate':2,                 
             },
          },
       },
      '4_default':{   
          'render_layers':{
              'wbg2':{                    
                'res':'w7530',           
                
                'interval':8024,                                    
                'w':522,                                       
                'h':522,                                       
                'x':0,                                            
                'y':-400,                                           
                'num':4,                    
                'alpha':0.5,                 
                'scaleY':2.5, 
                'scale':2,  

             },
             'w2':{                    
                'res':'pFight753_2',           
                'emissionRate':50,           
                'maxPartices':400,           
                'advanceTime':20,           
                'duration':20,              
                'interval':600,                                    
                'w':2000,                                       
                'h':8000,                                       
                'y':-750,                                           
                'num':3,                    
                'scale':0.3,                  
                'rate':2,                 
             },
             'w2':{                    
                'res':'pFight753_2',           
                'emissionRate':30,           
                'maxPartices':400,           
                'advanceTime':20,           
                'duration':20,              
                'interval':600,                                    
                'w':2000,                                       
                'h':8000,                                       
                'y':-750,                                           
                'num':3,                    
                'scale':0.4,                  
                'rate':2.2,                 
             },
             'w3':{                    
                'res':'pFight753_2',           
                'emissionRate':20,           
                'maxPartices':400,           
                'advanceTime':20,           
                'duration':20,              
                'interval':600,                                    
                'w':2000,                                       
                'h':8000,                                       
                'y':-750,                                           
                'num':3,                    
                'scale':0.6,                  
                'rate':2.4,                 
             },
          },
       },
       'default':{   
          'render_layers':{
             'wbg2':{                    
                'rate':0.5,                 
             },
             'w2':{                    
                'layerIndex':4,      
             },
             'w2':{                    
                'layerIndex':4,      
             },
             'w3':{                    
                'layerIndex':4,      
             },
             'w4':{                    
                'layerIndex':4,      
             },
             'w5':{                    
                'layerIndex':4,      
             },
          },
       },
   },

   
   'fightSceneResMapping':{
     'pFight750_2':2, 
     'pFight750_2':2, 
     'pFight750_3':2, 
     'pFight750_4':2, 
     'pFight750_5':2, 
     'pFight752_2':2, 
     'pFight752_2':2, 
     'pFight752_3':2,
     'pFight752_4':2,
     'pFight752_2':2,
     'pFight752_2':2,
     'pFight753_2':2,
   },
   
   'fightPersonMapping':{
     'army77':2,    
     'army77s':2,
   }
}
