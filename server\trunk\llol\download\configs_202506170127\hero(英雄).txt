{
     'hero704':{      #马云禄     
         'name':'700704',
         'info':'701704',
         'index':10,  
         'state':1,   #开服几天后显示   
         'sex':0,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':3,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':93,   #力量
         'agi':83,   #智力 
         'cha':92,   #魅力 
         'lead':88,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,2,4],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill209':3,'skill208':7,'skill210':3,},
         'fate':{'fate7041':[1,'hero716'],'fate7042':[2,'hero750'],'fate7043':[3,'hero701'],'fate7044':[4,'hero729'],},   #宿命技能
         'adjs':['hero716'],
         'resolve':[[['item209',2],3000,],[['item208',2],2500,],[['item210',2],2500,],],   #问道产生技能的ID，权重
         'res':'hero704',   #模型
         'fast_hero_star':[800,1200,1600,2200,3000],
     },
     'hero715':{      #贾诩     
         'name':'700715',
         'info':'701715',
         'index':11,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':2,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':61,   #力量
         'agi':97,   #智力 
         'cha':80,   #魅力 
         'lead':95,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[3,4,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill201':5,'skill213':5,'skill260':7,},
         'fate':{'fate7151':[1,'hero708'],'fate7152':[2,'hero734'],'fate7153':[3,'hero757'],'fate7154':[4,'hero745'],},   #宿命技能
         'adjs':['hero708'],
         'resolve':[[['item201',2],3000,],[['item213',2],3000,],[['item260',2],2142,],],   #问道产生技能的ID，权重
         'res':'hero715',   #模型
         'fast_hero_star':[800,1200,1600,2200,3000],
     },
     'hero705':{      #黄月英     
         'name':'700705',
         'info':'701705',
         'index':12,  
         'state':1,   #开服几天后显示   
         'sex':0,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':3,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':55,   #力量
         'agi':98,   #智力 
         'cha':84,   #魅力 
         'lead':87,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[3,4,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill220':3,'skill221':3,'skill270':7,},
         'fate':{'fate7051':[1,'hero714'],'fate7052':[2,'hero732'],'fate7053':[3,'hero729'],'fate7054':[4,'hero703'],},   #宿命技能
         'adjs':['hero714'],
         'resolve':[[['item220',2],3000,],[['item221',2],2500,],[['item270',2],2142,],],   #问道产生技能的ID，权重
         'res':'hero705',   #模型
         'fast_hero_star':[800,1200,1600,2200,3000],
     },
     'hero719':{      #甄姬     
         'name':'700719',
         'info':'701719',
         'index':13,  
         'state':1,   #开服几天后显示   
         'sex':0,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':3,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':51,   #力量
         'agi':90,   #智力 
         'cha':95,   #魅力 
         'lead':84,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,4,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill231':7,'skill202':5,'skill221':5,},
         'fate':{'fate7191':[1,'hero717'],'fate7192':[2,'hero722'],'fate7193':[3,'hero703'],'fate7194':[4,'hero704'],},   #宿命技能
         'adjs':['hero717'],
         'resolve':[[['item231',2],1875,],[['item202',2],2500,],[['item221',2],2500,],],   #问道产生技能的ID，权重
         'res':'hero719',   #模型
         'fast_hero_star':[800,1200,1600,2200,3000],
     },
     'hero712':{      #张辽     
         'name':'700712',
         'info':'701712',
         'index':20,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':2,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':95,   #力量
         'agi':84,   #智力 
         'cha':85,   #魅力 
         'lead':93,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,4,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill210':7,'skill219':5,'skill221':5,},
         'fate':{'fate7121':[1,'hero717'],'fate7122':[2,'hero741'],'fate7123':[3,'hero758'],'fate7124':[4,'hero759'],},   #宿命技能
         'adjs':['hero717'],
         'resolve':[[['item210',2],2500,],[['item219',2],3000,],[['item221',2],2500,],],   #问道产生技能的ID，权重
         'res':'hero712',   #模型
         'fast_hero_star':[800,1200,1600,2200,3000],
     },
     'hero706':{      #关羽     
         'name':'700706',
         'info':'701706',
         'index':21,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':2,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':97,   #力量
         'agi':81,   #智力 
         'cha':93,   #魅力 
         'lead':93,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,2,3],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill226':7,'skill209':5,'skill215':3,},
         'fate':{'fate7061':[1,'hero716'],'fate7062':[2,'hero731'],'fate7063':[3,'hero749'],'fate7064':[4,'hero756'],},   #宿命技能
         'adjs':['hero716'],
         'resolve':[[['item226',2],1875,],[['item209',2],3000,],[['item215',2],3000,],],   #问道产生技能的ID，权重
         'res':'hero706',   #模型
         'fast_hero_star':[800,1200,1600,2200,3000],
     },
     'hero722':{      #孙尚香     
         'name':'700722',
         'info':'701722',
         'index':22,  
         'state':1,   #开服几天后显示   
         'sex':0,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':3,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':91,   #力量
         'agi':82,   #智力 
         'cha':94,   #魅力 
         'lead':86,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,3,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill225':5,'skill214':5,'skill216':5,},
         'fate':{'fate7221':[1,'hero710'],'fate7222':[2,'hero716'],'fate7223':[3,'hero751'],'fate7224':[4,'hero704'],},   #宿命技能
         'adjs':['hero710'],
         'resolve':[[['item225',2],1875,],[['item214',2],2500,],[['item216',2],2500,],],   #问道产生技能的ID，权重
         'res':'hero722',   #模型
         'fast_hero_star':[800,1200,1600,2200,3000],
     },
     'hero703':{      #貂蝉     
         'name':'700703',
         'info':'701703',
         'index':23,  
         'state':1,   #开服几天后显示   
         'sex':0,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':3,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':56,   #力量
         'agi':91,   #智力 
         'cha':100,   #魅力 
         'lead':78,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[3,4,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill230':7,'skill220':3,'skill222':3,},
         'fate':{'fate7031':[1,'hero708'],'fate7032':[2,'hero719'],'fate7033':[3,'hero712'],'fate7034':[4,'hero722'],},   #宿命技能
         'adjs':['hero708'],
         'resolve':[[['item230',2],1875,],[['item220',2],3000,],[['item222',2],2500,],],   #问道产生技能的ID，权重
         'res':'hero703',   #模型
         'fast_hero_star':[800,1200,1600,2200,3000],
     },
     'hero717':{      #郭嘉     
         'name':'700717',
         'info':'701717',
         'index':30,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':2,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':56,   #力量
         'agi':98,   #智力 
         'cha':91,   #魅力 
         'lead':92,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,3,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill232':5,'skill202':5,'skill221':7,},
         'fate':{'fate7171':[1,'hero712'],'fate7172':[2,'hero715'],'fate7173':[3,'hero734'],'fate7174':[4,'hero760'],},   #宿命技能
         'adjs':['hero712'],
         'resolve':[[['item232',2],1875,],[['item202',2],2500,],[['item221',2],2500,],],   #问道产生技能的ID，权重
         'res':'hero717',   #模型
         'fast_hero_star':[800,1200,1600,2200,3000],
     },
     'hero716':{      #赵云     
         'name':'700716',
         'info':'701716',
         'index':31,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':2,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':96,   #力量
         'agi':86,   #智力 
         'cha':92,   #魅力 
         'lead':90,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,4,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill227':5,'skill202':7,'skill204':5,},
         'fate':{'fate7161':[1,'hero706'],'fate7162':[2,'hero731'],'fate7163':[3,'hero704'],'fate7164':[4,'hero708'],},   #宿命技能
         'adjs':['hero706'],
         'resolve':[[['item227',2],1875,],[['item202',2],2500,],[['item204',2],2500,],],   #问道产生技能的ID，权重
         'res':'hero716',   #模型
         'fast_hero_star':[800,1200,1600,2200,3000],
     },
     'hero710':{      #孙策     
         'name':'700710',
         'info':'701710',
         'index':32,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':2,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':97,   #力量
         'agi':80,   #智力 
         'cha':90,   #魅力 
         'lead':96,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,5,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill204':7,'skill203':5,'skill222':5,},
         'fate':{'fate7101':[1,'hero722'],'fate7102':[2,'hero709'],'fate7103':[3,'hero728'],'fate7104':[4,'hero733'],},   #宿命技能
         'adjs':['hero722'],
         'resolve':[[['item204',2],2500,],[['item203',2],2142,],[['item222',2],2500,],],   #问道产生技能的ID，权重
         'res':'hero710',   #模型
         'fast_hero_star':[800,1200,1600,2200,3000],
     },
     'hero708':{      #吕布     
         'name':'700708',
         'info':'701708',
         'index':33,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':2,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':100,   #力量
         'agi':63,   #智力 
         'cha':86,   #魅力 
         'lead':89,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,2,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill227':7,'skill207':5,'skill261':5,},
         'fate':{'fate7081':[1,'hero703'],'fate7082':[2,'hero712'],'fate7083':[3,'hero756'],'fate7084':[4,'hero747'],},   #宿命技能
         'adjs':['hero703'],
         'resolve':[[['item227',2],1875,],[['item207',2],3000,],[['item261',2],2142,],],   #问道产生技能的ID，权重
         'res':'hero708',   #模型
         'fast_hero_star':[800,1200,1600,2200,3000],
     },
     'hero799':{      #王元姬     
         'name':'700799',
         'info':'701799',
         'index':34,  
         'state':-1,   #开服几天后显示   
         'sex':0,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':3,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':62,   #力量
         'agi':90,   #智力 
         'cha':96,   #魅力 
         'lead':90,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,2,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill290':5,'skill221':5,'skill260':5,},
         'fate':{'fate7991':[1,'hero760'],'fate7992':[2,'hero722'],'fate7993':[3,'hero719'],'fate7994':[4,'hero707'],},   #宿命技能
         'resolve':[[['item290',],2142,],[['item221',],2500,],[['item260',],2142,],],   #问道产生技能的ID，权重
         'res':'hero799',   #模型
         
     },
     'hero761':{      #庞统     
         'name':'700761',
         'info':'701761',
         'index':40,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':2,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':65,   #力量
         'agi':98,   #智力 
         'cha':77,   #魅力 
         'lead':99,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,4,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill233':7,'skill220':7,'skill223':5,},
         'fate':{'fate7611':[1,'hero761'],'fate7612':[2,'hero731'],'fate7613':[3,'hero709'],'fate7614':[4,'hero714'],},   #宿命技能
         'resolve':[[['item233',],1666,],[['item220',],3000,],[['item223',],2142,],],   #问道产生技能的ID，权重
         'res':'hero761',   #模型
         
     },
     'hero709':{      #周瑜     
         'name':'700709',
         'info':'701709',
         'index':41,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':2,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':74,   #力量
         'agi':98,   #智力 
         'cha':99,   #魅力 
         'lead':95,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,3,4],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill237':7,'skill216':7,'skill217':5,},
         'fate':{'fate7091':[1,'hero709'],'fate7092':[2,'hero752'],'fate7093':[3,'hero751'],'fate7094':[4,'hero723'],},   #宿命技能
         'resolve':[[['item237',],1666,],[['item216',],2500,],[['item217',],2142,],],   #问道产生技能的ID，权重
         'res':'hero709',   #模型
         
     },
     'hero723':{      #小乔     
         'name':'700723',
         'info':'701723',
         'index':42,  
         'state':3,   #开服几天后显示   
         'sex':0,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':3,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':57,   #力量
         'agi':94,   #智力 
         'cha':100,   #魅力 
         'lead':85,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[3,4,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill235':7,'skill214':5,'skill215':7,},
         'fate':{'fate7231':[1,'hero709'],'fate7232':[2,'hero710'],'fate7233':[3,'hero722'],'fate7234':[4,'hero720'],},   #宿命技能
         'adjs':['hero709'],
         'resolve':[[['item235',],1666,],[['item214',],2500,],[['item215',],3000,],],   #问道产生技能的ID，权重
         'res':'hero723',   #模型
         
     },
     'hero714':{      #诸葛亮     
         'name':'700714',
         'info':'701714',
         'index':43,  
         'state':6,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':2,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':59,   #力量
         'agi':100,   #智力 
         'cha':98,   #魅力 
         'lead':97,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,3,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill229':7,'skill238':7,'skill222':7,},
         'fate':{'fate7141':[1,'hero714'],'fate7142':[2,'hero706'],'fate7143':[3,'hero709'],'fate7144':[4,'hero705'],},   #宿命技能
         'resolve':[[['item229',],1875,],[['item238',],1666,],[['item222',],2500,],],   #问道产生技能的ID，权重
         'res':'hero714',   #模型
         
     },
     'hero713':{      #董卓     
         'name':'700713',
         'info':'701713',
         'index':44,  
         'state':9,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':2,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':96,   #力量
         'agi':89,   #智力 
         'cha':85,   #魅力 
         'lead':97,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,5,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill202':5,'skill289':7,'skill234':7,},
         'fate':{'fate7131':[1,'hero703'],'fate7132':[2,'hero712'],'fate7133':[3,'hero756'],'fate7134':[4,'hero715'],},   #宿命技能
         'adjs':['hero703'],
         'resolve':[[['item202',],2500,],[['item289',],3000,],[['item234',],1666,],],   #问道产生技能的ID，权重
         'res':'hero713',   #模型
         
     },
     'hero707':{      #司马懿     
         'name':'700707',
         'info':'701707',
         'index':45,  
         'state':13,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':2,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':64,   #力量
         'agi':99,   #智力 
         'cha':91,   #魅力 
         'lead':100,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,4,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill228':7,'skill212':7,'skill220':7,},
         'fate':{'fate7071':[1,'hero707'],'fate7072':[2,'hero714'],'fate7073':[3,'hero730'],'fate7074':[4,'hero734'],},   #宿命技能
         'resolve':[[['item228',],1875,],[['item212',],2142,],[['item220',],3000,],],   #问道产生技能的ID，权重
         'res':'hero707',   #模型
         
     },
     'hero718':{      #陆逊     
         'name':'700718',
         'info':'701718',
         'index':46,  
         'state':20,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':2,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':78,   #力量
         'agi':97,   #智力 
         'cha':97,   #魅力 
         'lead':98,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[3,5,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill237':7,'skill201':7,'skill271':7,},
         'fate':{'fate7181':[1,'hero718'],'fate7182':[2,'hero706'],'fate7183':[3,'hero725'],'fate7184':[4,'hero709'],},   #宿命技能
         'resolve':[[['item237',],1666,],[['item201',],3000,],[['item271',],1666,],],   #问道产生技能的ID，权重
         'res':'hero718',   #模型
         
     },
     'hero770':{      #汉献帝     
         'name':'700770',
         'info':'701770',
         'index':47,  
         'state':20,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':2,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':70,   #力量
         'agi':70,   #智力 
         'cha':70,   #魅力 
         'lead':70,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,2,3],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill266':5,'skill267':5,'skill287':7,},
         'fate':{},   #宿命技能
         'resolve':[[['item266',],2142,],[['item267',],2142,],[['item287',],3000,],],   #问道产生技能的ID，权重
         'res':'hero770',   #模型
         
     },
     'hero720':{      #祝融     
         'name':'700720',
         'info':'701720',
         'index':48,  
         'state':28,   #开服几天后显示   
         'sex':0,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':3,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':97,   #力量
         'agi':84,   #智力 
         'cha':98,   #魅力 
         'lead':90,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,2,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill236':7,'skill290':7,'skill222':5,},
         'fate':{'fate7201':[1,'hero720'],'fate7202':[2,'hero704'],'fate7203':[3,'hero722'],'fate7204':[4,'hero711'],},   #宿命技能
         'resolve':[[['item236',],1666,],[['item290',],2142,],[['item222',],2500,],],   #问道产生技能的ID，权重
         'res':'hero720',   #模型
         
     },
     'hero711':{      #孟获     
         'name':'700711',
         'info':'701711',
         'index':49,  
         'state':29,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':2,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':98,   #力量
         'agi':71,   #智力 
         'cha':88,   #魅力 
         'lead':97,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,3,4],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill243':7,'skill209':5,'skill208':7,},
         'fate':{'fate7111':[1,'hero720'],'fate7112':[2,'hero729'],'fate7113':[3,'hero750'],'fate7114':[4,'hero714'],},   #宿命技能
         'adjs':['hero720'],
         'resolve':[[['item243',],1666,],[['item209',],3000,],[['item208',],2500,],],   #问道产生技能的ID，权重
         'res':'hero711',   #模型
         
     },
     'hero724':{      #大乔     
         'name':'700724',
         'info':'701724',
         'index':60,  
         'state':4,   #开服几天后显示   
         'sex':0,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':3,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':59,   #力量
         'agi':90,   #智力 
         'cha':100,   #魅力 
         'lead':91,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[3,4,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill240':7,'skill214':7,'skill290':5,},
         'fate':{'fate7241':[1,'hero724'],'fate7242':[2,'hero710'],'fate7243':[3,'hero709'],'fate7244':[4,'hero723'],},   #宿命技能
         'adjs':['hero710','hero709','hero723'],
         'resolve':[[['item240',],1666,],[['item214',],2500,],[['item290',],2142,],],   #问道产生技能的ID，权重
         'res':'hero724',   #模型
         
     },
     'hero721':{      #蔡文姬     
         'name':'700721',
         'info':'701721',
         'index':61,  
         'state':4,   #开服几天后显示   
         'sex':0,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':3,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':56,   #力量
         'agi':97,   #智力 
         'cha':99,   #魅力 
         'lead':75,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,3,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill230':5,'skill217':7,'skill274':7,},
         'fate':{'fate7211':[1,'hero723'],'fate7212':[2,'hero705'],'fate7213':[3,'hero713'],'fate7214':[4,'hero719'],},   #宿命技能
         'adjs':['hero723'],
         'resolve':[[['item230',],1875,],[['item217',],2142,],[['item274',],1666,],],   #问道产生技能的ID，权重
         'res':'hero721',   #模型
         
     },
     'hero771':{      #吕玲绮     
         'name':'700771',
         'info':'701771',
         'index':62,  
         'state':4,   #开服几天后显示   
         'sex':0,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':3,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':94,   #力量
         'agi':78,   #智力 
         'cha':96,   #魅力 
         'lead':96,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,2,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill225':7,'skill231':5,'skill280':7,},
         'fate':{'fate7711':[1,'hero708'],'fate7712':[2,'hero703'],'fate7713':[3,'hero720'],'fate7714':[4,'hero718'],},   #宿命技能
         'adjs':['hero708'],
         'resolve':[[['item225',],1875,],[['item231',],1875,],[['item280',],1666,],],   #问道产生技能的ID，权重
         'res':'hero771',   #模型
         
     },
     'hero772':{      #班育双姝     
         'name':'700772',
         'info':'701772',
         'index':63,  
         'state':4,   #开服几天后显示   
         'sex':0,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':3,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':71,   #力量
         'agi':89,   #智力 
         'cha':94,   #魅力 
         'lead':93,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[3,5,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill276':7,'skill219':7,'skill223':5,},
         'fate':{'fate7721':[1,'hero719'],'fate7722':[2,'hero718'],'fate7723':[3,'hero774'],'fate7724':[4,'hero764'],},   #宿命技能
         'adjs':['hero719'],
         'resolve':[[['item276',],1666,],[['item219',],3000,],[['item223',],2142,],],   #问道产生技能的ID，权重
         'res':'hero772',   #模型
         
     },
     'hero773':{      #关银屏     
         'name':'700773',
         'info':'701773',
         'index':64,  
         'state':4,   #开服几天后显示   
         'sex':0,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':3,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':90,   #力量
         'agi':86,   #智力 
         'cha':95,   #魅力 
         'lead':79,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,4,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill226':5,'skill207':5,'skill210':5,},
         'fate':{'fate7731':[1,'hero706'],'fate7732':[2,'hero705'],'fate7733':[3,'hero711'],'fate7734':[4,'hero776'],},   #宿命技能
         'adjs':['hero706'],
         'resolve':[[['item226',],1875,],[['item207',],3000,],[['item210',],2500,],],   #问道产生技能的ID，权重
         'res':'hero773',   #模型
         
     },
     'hero774':{      #步练师     
         'name':'700774',
         'info':'701774',
         'index':65,  
         'state':4,   #开服几天后显示   
         'sex':0,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':3,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':61,   #力量
         'agi':91,   #智力 
         'cha':99,   #魅力 
         'lead':83,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,4,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill232':5,'skill289':5,'skill203':5,},
         'fate':{'fate7741':[1,'hero710'],'fate7742':[2,'hero709'],'fate7743':[3,'hero772'],'fate7744':[4,'hero764'],},   #宿命技能
         'resolve':[[['item232',],1875,],[['item289',],3000,],[['item203',],2142,],],   #问道产生技能的ID，权重
         'res':'hero774',   #模型
         
     },
     'hero775':{      #吴国太     
         'name':'700775',
         'info':'701775',
         'index':66,  
         'state':4,   #开服几天后显示   
         'sex':0,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':3,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':54,   #力量
         'agi':93,   #智力 
         'cha':90,   #魅力 
         'lead':98,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[4,5,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill213':3,'skill216':5,'skill277':7,},
         'fate':{'fate7751':[1,'hero763'],'fate7752':[2,'hero722'],'fate7753':[3,'hero709'],'fate7754':[4,'hero770'],},   #宿命技能
         'resolve':[[['item213',],3000,],[['item216',],2500,],[['item277',],1666,],],   #问道产生技能的ID，权重
         'res':'hero775',   #模型
         
     },
     'hero776':{      #张星彩     
         'name':'700776',
         'info':'701776',
         'index':67,  
         'state':4,   #开服几天后显示   
         'sex':0,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':3,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':95,   #力量
         'agi':74,   #智力 
         'cha':89,   #魅力 
         'lead':91,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,3,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill204':5,'skill221':5,'skill282':5,},
         'fate':{'fate7761':[1,'hero731'],'fate7762':[2,'hero735'],'fate7763':[3,'hero704'],'fate7764':[4,'hero778'],},   #宿命技能
         'adjs':['hero731'],
         'resolve':[[['item204',],2500,],[['item221',],2500,],[['item282',],3000,],],   #问道产生技能的ID，权重
         'res':'hero776',   #模型
         
     },
     'hero777':{      #张春华     
         'name':'700777',
         'info':'701777',
         'index':68,  
         'state':4,   #开服几天后显示   
         'sex':0,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':3,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':79,   #力量
         'agi':94,   #智力 
         'cha':93,   #魅力 
         'lead':90,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,2,3],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill215':5,'skill270':5,'skill281':5,},
         'fate':{'fate7771':[1,'hero705'],'fate7772':[2,'hero707'],'fate7773':[3,'hero719'],'fate7774':[4,'hero780'],},   #宿命技能
         'adjs':['hero705'],
         'resolve':[[['item215',],3000,],[['item270',],2142,],[['item281',],3000,],],   #问道产生技能的ID，权重
         'res':'hero777',   #模型
         
     },
     'hero7700':{      #诸葛果     
         'name':'7007700',
         'info':'7017700',
         'index':69,  
         'state':4,   #开服几天后显示   
         'open_date':datetime.datetime(2023,1,20,5,0),   #最早可见日期
         'sex':0,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':3,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':81,   #力量
         'agi':99,   #智力 
         'cha':97,   #魅力 
         'lead':78,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,5,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill222':5,'skill260':5,'skill274':5,},
         'fate':{'fate77001':[1,'hero705'],'fate77002':[2,'hero714'],'fate77003':[3,'hero746'],'fate77004':[4,'hero773'],},   #宿命技能
         'resolve':[[['item222',],2500,],[['item260',],2142,],[['item274',],1666,],],   #问道产生技能的ID，权重
         'res':'hero7700',   #模型
         
     },
     'hero7701':{      #董白     
         'name':'7007701',
         'info':'7017701',
         'index':70,  
         'state':1,   #开服几天后显示   
         'merge':1,   #几次合服后可见
         'sex':0,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':3,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':85,   #力量
         'agi':94,   #智力 
         'cha':96,   #魅力 
         'lead':88,   #统帅
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,4,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill211':5,'skill291':5,'skill267':5,},
         'fate':{'fate77011':[1,'hero713'],'fate77012':[2,'hero770'],'fate77013':[3,'hero778'],'fate77014':[4,'hero719'],},   #宿命技能
         'resolve':[[['item211',],1875,],[['item291',],1875,],[['item267',],2142,],],   #问道产生技能的ID，权重
         'res':'hero7701',   #模型
         
     },
     'hero7702':{      #何皇后     
         'name':'7007702',
         'info':'7017702',
         'index':71,  
         'state':4,   #开服几天后显示   
         'sex':0,   #性别1男0女
         'open_date':datetime.datetime(2024,2,9,5,0),   #最早可见日期
         'type':1,    #0武将，1文官，2全才
         'rarity':3,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':74,   #力量
         'agi':96,   #智力 
         'cha':90,   #魅力 
         'lead':91,   #统帅
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,3,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill233':5,'skill260':5,'skill223':5,},
         'fate':{'fate77021':[1,'hero772'],'fate77022':[2,'hero719'],'fate77023':[3,'hero703'],'fate77024':[4,'hero770'],},   #宿命技能
         'resolve':[[['item233',],1666,],[['item260',],2142,],[['item223',],2142,],],   #问道产生技能的ID，权重
         'res':'hero7702',   #模型
         
     },
     'hero778':{      #刘禅     
         'name':'700778',
         'info':'701778',
         'index':80,  
         'state':1,   #开服几天后显示   
         'merge':2,   #几次合服后可见
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':2,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':79,   #力量
         'agi':91,   #智力 
         'cha':97,   #魅力 
         'lead':88,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[3,5,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill240':7,'skill218':7,'skill283':7,},
         'fate':{'fate7781':[1,'hero714'],'fate7782':[2,'hero716'],'fate7783':[3,'hero776'],'fate7784':[4,'hero763'],},   #宿命技能
         'adjs':['hero714'],
         'resolve':[[['item240',],1666,],[['item218',],2142,],[['item283',],3000,],],   #问道产生技能的ID，权重
         'res':'hero778',   #模型
         
     },
     'hero779':{      #文鸯     
         'name':'700779',
         'info':'701779',
         'index':81,  
         'state':1,   #开服几天后显示   
         'merge':2,   #几次合服后可见
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':2,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':100,   #力量
         'agi':87,   #智力 
         'cha':78,   #魅力 
         'lead':77,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,3,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill245':7,'skill290':7,'skill221':7,},
         'fate':{'fate7791':[1,'hero708'],'fate7792':[2,'hero716'],'fate7793':[3,'hero768'],'fate7794':[4,'hero742'],},   #宿命技能
         'adjs':['hero708'],
         'resolve':[[['item245',],1666,],[['item290',],2142,],[['item221',],2500,],],   #问道产生技能的ID，权重
         'res':'hero779',   #模型
         
     },
     'hero780':{      #曹丕     
         'name':'700780',
         'info':'701780',
         'index':82,  
         'state':1,   #开服几天后显示   
         'merge':2,   #几次合服后可见
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':2,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':88,   #力量
         'agi':95,   #智力 
         'cha':86,   #魅力 
         'lead':96,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,3,4],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill239':7,'skill260':7,'skill288':7,},
         'fate':{'fate7801':[1,'hero707'],'fate7802':[2,'hero715'],'fate7803':[3,'hero719'],'fate7804':[4,'hero762'],},   #宿命技能
         'adjs':['hero707'],
         'resolve':[[['item239',],1666,],[['item260',],2142,],[['item288',],3000,],],   #问道产生技能的ID，权重
         'res':'hero780',   #模型
         
     },
     'hero762':{      #曹操     
         'name':'700762',
         'info':'701762',
         'index':100,  
         'state':6,   #开服几天后显示   
         'merge':1,   #几次合服后可见
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':4,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':93,   #力量
         'agi':94,   #智力 
         'cha':81,   #魅力 
         'lead':99,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,4,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill244':10,'skill212':7,'skill283':5,},
         'fate':{'fate7621':[1,'hero717'],'fate7622':[2,'hero707'],'fate7623':[3,'hero726'],'fate7624':[4,'hero757'],},   #宿命技能
         'resolve':[[['item244',],1500,],[['item212',],2142,],[['item283',],3000,],],   #问道产生技能的ID，权重
         'res':'hero762',   #模型
         
     },
     'hero763':{      #刘备     
         'name':'700763',
         'info':'701763',
         'index':101,  
         'state':6,   #开服几天后显示   
         'merge':1,   #几次合服后可见
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':4,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':96,   #力量
         'agi':90,   #智力 
         'cha':99,   #魅力 
         'lead':81,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,4,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill244':10,'skill218':7,'skill286':5,},
         'fate':{'fate7631':[1,'hero706'],'fate7632':[2,'hero714'],'fate7633':[3,'hero716'],'fate7634':[4,'hero731'],},   #宿命技能
         'resolve':[[['item244',],1500,],[['item218',],2142,],[['item286',],3000,],],   #问道产生技能的ID，权重
         'res':'hero763',   #模型
         
     },
     'hero764':{      #孙权     
         'name':'700764',
         'info':'701764',
         'index':102,  
         'state':6,   #开服几天后显示   
         'merge':1,   #几次合服后可见
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':4,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':90,   #力量
         'agi':96,   #智力 
         'cha':92,   #魅力 
         'lead':92,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,3,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill244':10,'skill205':7,'skill285':5,},
         'fate':{'fate7641':[1,'hero709'],'fate7642':[2,'hero718'],'fate7643':[3,'hero710'],'fate7644':[4,'hero751'],},   #宿命技能
         'resolve':[[['item244',],1500,],[['item205',],2142,],[['item285',],3000,],],   #问道产生技能的ID，权重
         'res':'hero764',   #模型
         
     },
     'hero769':{      #张角     
         'name':'700769',
         'info':'701769',
         'index':103,  
         'state':1,   #开服几天后显示   
         'merge':1,   #几次合服后可见
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':4,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':70,   #力量
         'agi':97,   #智力 
         'cha':93,   #魅力 
         'lead':94,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,3,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill229':10,'skill224':7,'skill284':5,},
         'fate':{'fate7691':[1,'hero713'],'fate7692':[2,'hero762'],'fate7693':[3,'hero763'],'fate7694':[4,'hero764'],},   #宿命技能
         'resolve':[[['item229',],1875,],[['item224',],2142,],[['item284',],3000,],],   #问道产生技能的ID，权重
         'res':'hero769',   #模型
         
     },
     'hero765':{      #华佗     
         'name':'700765',
         'info':'701765',
         'index':110,  
         'state':9,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':4,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':88,   #力量
         'agi':92,   #智力 
         'cha':98,   #魅力 
         'lead':60,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,3,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill272':10,'skill235':7,'skill264':2,},
         'fate':{'fate7651':[1,'hero706'],'fate7652':[2,'hero755'],'fate7653':[3,'hero734'],'fate7654':[4,'hero762'],},   #宿命技能
         'resolve':[[['item272',],1500,],[['item235',],1666,],[['item264',],2142,],],   #问道产生技能的ID，权重
         'res':'hero765',   #模型
         
     },
     'hero768':{      #童渊     
         'name':'700768',
         'info':'701768',
         'index':111,  
         'state':16,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':4,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':98,   #力量
         'agi':60,   #智力 
         'cha':88,   #魅力 
         'lead':92,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,4,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill241':10,'skill218':7,'skill262':2,},
         'fate':{'fate7681':[1,'hero716'],'fate7682':[2,'hero708'],'fate7683':[3,'hero717'],'fate7684':[4,'hero769'],},   #宿命技能
         'resolve':[[['item241',],1500,],[['item218',],2142,],[['item262',],2142,],],   #问道产生技能的ID，权重
         'res':'hero768',   #模型
         
     },
     'hero767':{      #司马徽     
         'name':'700767',
         'info':'701767',
         'index':112,  
         'state':23,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':4,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':60,   #力量
         'agi':88,   #智力 
         'cha':92,   #魅力 
         'lead':98,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[4,5,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill273':10,'skill205':7,'skill265':2,},
         'fate':{'fate7671':[1,'hero714'],'fate7672':[2,'hero761'],'fate7673':[3,'hero707'],'fate7674':[4,'hero763'],},   #宿命技能
         'resolve':[[['item273',],1500,],[['item205',],2142,],[['item265',],2142,],],   #问道产生技能的ID，权重
         'res':'hero767',   #模型
         
     },
     'hero766':{      #于吉     
         'name':'700766',
         'info':'701766',
         'index':113,  
         'state':30,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':4,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':92,   #力量
         'agi':98,   #智力 
         'cha':60,   #魅力 
         'lead':88,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,2,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill242':10,'skill224':7,'skill263':2,},
         'fate':{'fate7661':[1,'hero710'],'fate7662':[2,'hero765'],'fate7663':[3,'hero767'],'fate7664':[4,'hero764'],},   #宿命技能
         'resolve':[[['item242',],1500,],[['item224',],2142,],[['item263',],2142,],],   #问道产生技能的ID，权重
         'res':'hero766',   #模型
         
     },
     'hero783':{      #王越     
         'name':'700783',
         'info':'701783',
         'index':120,  
         'state':1,   #开服几天后显示   
         'merge':2,   #几次合服后可见
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':4,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':99,   #力量
         'agi':75,   #智力 
         'cha':96,   #魅力 
         'lead':89,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,5,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill246':10,'skill210':7,'skill287':5,},
         'fate':{'fate7831':[1,'hero770'],'fate7832':[2,'hero780'],'fate7833':[3,'hero708'],'fate7834':[4,'hero779'],},   #宿命技能
         'adjs':['hero770'],
         'resolve':[[['item246',],1666,],[['item210',],2500,],[['item287',],3000,],],   #问道产生技能的ID，权重
         'res':'hero783',   #模型
         
     },
     'hero784':{      #左慈     
         'name':'700784',
         'info':'701784',
         'index':121,  
         'state':22,   #开服几天后显示   
         'merge':2,   #几次合服后可见
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':4,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':77,   #力量
         'agi':100,   #智力 
         'cha':95,   #魅力 
         'lead':64,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,2,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill275':7,'skill224':10,'skill287':5,},
         'fate':{'fate7841':[1,'hero787'],'fate7842':[2,'hero710'],'fate7843':[3,'hero762'],'fate7844':[4,'hero766'],},   #宿命技能
         'adjs':['hero787'],
         'resolve':[[['item275',],1500,],[['item224',],2142,],[['item287',],3000,],],   #问道产生技能的ID，权重
         'res':'hero784',   #模型
         
     },
     'hero787':{      #南华老仙     
         'name':'700787',
         'info':'701787',
         'index':122,  
         'state':1,   #开服几天后显示   
         'merge':2,   #几次合服后可见
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':4,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':63,   #力量
         'agi':99,   #智力 
         'cha':87,   #魅力 
         'lead':93,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,5,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill275':10,'skill223':7,'skill286':5,},
         'fate':{'fate7871':[1,'hero769'],'fate7872':[2,'hero767'],'fate7873':[3,'hero766'],'fate7874':[4,'hero784'],},   #宿命技能
         'adjs':['hero769'],
         'resolve':[[['item275',],1500,],[['item223',],2142,],[['item286',],3000,],],   #问道产生技能的ID，权重
         'res':'hero787',   #模型
         
     },
     'hero789':{      #袁绍     
         'name':'700789',
         'info':'701789',
         'index':123,  
         'state':1,   #开服几天后显示   
         'merge':2,   #几次合服后可见
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':4,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':92,   #力量
         'agi':86,   #智力 
         'cha':88,   #魅力 
         'lead':93,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,3,4],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill225':10,'skill266':7,'skill267':7,},
         'fate':{'fate7891':[1,'hero762'],'fate7892':[2,'hero749'],'fate7893':[3,'hero742'],'fate7894':[4,'hero763'],},   #宿命技能
         'adjs':['hero762'],
         'resolve':[[['item225',],1875,],[['item266',],2142,],[['item267',],2142,],],   #问道产生技能的ID，权重
         'res':'hero789',   #模型
         
     },
     'hero786':{      #紫虚上人     
         'name':'700786',
         'info':'701786',
         'index':130,  
         'state':1,   #开服几天后显示   
         'merge':3,   #几次合服后可见
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':4,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':85,   #力量
         'agi':96,   #智力 
         'cha':76,   #魅力 
         'lead':82,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,2,4],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill238':11,'skill212':8,'skill285':5,},
         'fate':{'fate7861':[1,'hero714'],'fate7862':[2,'hero761'],'fate7863':[3,'hero765'],'fate7864':[4,'hero768'],},   #宿命技能
         'resolve':[[['item238',],1666,],[['item212',],2142,],[['item285',],3000,],],   #问道产生技能的ID，权重
         'res':'hero786',   #模型
         
     },
     'hero785':{      #陈寿     
         'name':'700785',
         'info':'701785',
         'index':131,  
         'state':1,   #开服几天后显示   
         'merge':3,   #几次合服后可见
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':4,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':66,   #力量
         'agi':93,   #智力 
         'cha':83,   #魅力 
         'lead':80,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,3,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill205':11,'skill203':8,'skill214':5,},
         'fate':{},   #宿命技能
         'resolve':[[['item205',],2142,],[['item203',],2142,],[['item214',],2500,],],   #问道产生技能的ID，权重
         'res':'hero785',   #模型
         
     },
     'hero782':{      #马忠     
         'name':'700782',
         'info':'701782',
         'index':140,  
         'state':1,   #开服几天后显示   
         'merge':4,   #几次合服后可见
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':4,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':87,   #力量
         'agi':66,   #智力 
         'cha':72,   #魅力 
         'lead':83,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[3,4,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill204':5,'skill216':8,'skill217':10,},
         'fate':{'fate7821':[1,'hero706'],'fate7822':[2,'hero736'],'fate7823':[3,'hero725'],'fate7824':[4,'hero728'],},   #宿命技能
         'resolve':[[['item204',],2500,],[['item216',],2500,],[['item217',],2142,],],   #问道产生技能的ID，权重
         'res':'hero782',   #模型
         
     },
     'hero788':{      #黄承彦     
         'name':'700788',
         'info':'701788',
         'index':141,  
         'state':1,   #开服几天后显示   
         'merge':4,   #几次合服后可见
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':4,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':68,   #力量
         'agi':92,   #智力 
         'cha':94,   #魅力 
         'lead':100,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,4,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill208':8,'skill270':10,'skill284':5,},
         'fate':{'fate7881':[1,'hero718'],'fate7882':[2,'hero705'],'fate7883':[3,'hero714'],'fate7884':[4,'hero767'],},   #宿命技能
         'resolve':[[['item208',],2500,],[['item270',],2142,],[['item284',],3000,],],   #问道产生技能的ID，权重
         'res':'hero788',   #模型
         
     },
     'hero781':{      #兀突骨     
         'name':'700781',
         'info':'701781',
         'index':142,  
         'state':1,   #开服几天后显示   
         'merge':4,   #几次合服后可见
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':4,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':95,   #力量
         'agi':77,   #智力 
         'cha':79,   #魅力 
         'lead':91,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,3,4],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill243':11,'skill261':8,'skill282':5,},
         'fate':{'fate7811':[1,'hero711'],'fate7812':[2,'hero720'],'fate7813':[3,'hero735'],'fate7814':[4,'hero714'],},   #宿命技能
         'resolve':[[['item243',],1666,],[['item261',],2142,],[['item282',],3000,],],   #问道产生技能的ID，权重
         'res':'hero781',   #模型
         
     },
     'hero792':{      #孙坚     
         'name':'700792',
         'info':'701792',
         'index':150,  
         'state':1,   #开服几天后显示   
         'merge':5,   #几次合服后可见
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':4,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':98,   #力量
         'agi':80,   #智力 
         'cha':90,   #魅力 
         'lead':97,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,4,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill234':10,'skill293':7,'skill282':5,},
         'fate':{'fate7921':[1,'hero764'],'fate7922':[2,'hero710'],'fate7923':[3,'hero722'],'fate7924':[4,'hero775'],},   #宿命技能
         'resolve':[[['item234',],1666,],[['item293',],1875,],[['item282',],3000,],],   #问道产生技能的ID，权重
         'res':'hero792',   #模型
         
     },
     'hero793':{      #刘邦     
         'name':'700793',
         'info':'701793',
         'index':151,  
         'state':1,   #开服几天后显示   
         'merge':5,   #几次合服后可见
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':4,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':77,   #力量
         'agi':95,   #智力 
         'cha':90,   #魅力 
         'lead':95,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,2,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill247':10,'skill291':7,'skill285':5,},
         'fate':{'fate7931':[1,'hero763'],'fate7932':[2,'hero706'],'fate7933':[3,'hero794'],'fate7934':[4,'hero797'],},   #宿命技能
         'resolve':[[['item247',],1500,],[['item291',],1875,],[['item285',],3000,],],   #问道产生技能的ID，权重
         'res':'hero793',   #模型
         
     },
     'hero794':{      #项羽     
         'name':'700794',
         'info':'701794',
         'index':152,  
         'state':1,   #开服几天后显示   
         'merge':5,   #几次合服后可见
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':4,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':100,   #力量
         'agi':76,   #智力 
         'cha':93,   #魅力 
         'lead':98,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,4,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill245':10,'skill294':7,'skill261':5,},
         'fate':{'fate7941':[1,'hero708'],'fate7942':[2,'hero716'],'fate7943':[3,'hero793'],'fate7944':[4,'hero798'],},   #宿命技能
         'resolve':[[['item245',],1666,],[['item294',],1875,],[['item261',],2142,],],   #问道产生技能的ID，权重
         'res':'hero794',   #模型
         
     },
     'hero795':{      #虞姬     
         'name':'700795',
         'info':'701795',
         'index':153,  
         'state':10,   #开服几天后显示   
         'merge':5,   #几次合服后可见
         'sex':0,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':4,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':76,   #力量
         'agi':94,   #智力 
         'cha':100,   #魅力 
         'lead':92,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,3,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill248':10,'skill217':7,'skill279':5,},
         'fate':{'fate7951':[1,'hero794'],'fate7952':[2,'hero703'],'fate7953':[3,'hero708'],'fate7954':[4,'hero793'],},   #宿命技能
         'resolve':[[['item248',],1500,],[['item217',],2142,],[['item279',],1666,],],   #问道产生技能的ID，权重
         'res':'hero795',   #模型
         
     },
     'hero798':{      #韩信     
         'name':'700798',
         'info':'701798',
         'index':154,  
         'state':20,   #开服几天后显示   
         'merge':5,   #几次合服后可见
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':4,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':83,   #力量
         'agi':97,   #智力 
         'cha':89,   #魅力 
         'lead':100,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,4,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill239':10,'skill205':7,'skill267':5,},
         'fate':{'fate7981':[1,'hero762'],'fate7982':[2,'hero718'],'fate7983':[3,'hero794'],'fate7984':[4,'hero797'],},   #宿命技能
         'resolve':[[['item239',],1666,],[['item205',],2142,],[['item267',],2142,],],   #问道产生技能的ID，权重
         'res':'hero798',   #模型
         
     },
     'hero797':{      #张良     
         'name':'700797',
         'info':'701797',
         'index':155,  
         'state':30,   #开服几天后显示   
         'merge':5,   #几次合服后可见
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':4,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':73,   #力量
         'agi':100,   #智力 
         'cha':97,   #魅力 
         'lead':95,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,3,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill233':10,'skill222':7,'skill277':5,},
         'fate':{'fate7971':[1,'hero769'],'fate7972':[2,'hero714'],'fate7973':[3,'hero798'],'fate7974':[4,'hero793'],},   #宿命技能
         'resolve':[[['item233',],1666,],[['item222',],2500,],[['item277',],1666,],],   #问道产生技能的ID，权重
         'res':'hero797',   #模型
         
     },
     'hero7000':{      #公孙瓒     
         'name':'7007000',
         'info':'7017000',
         'index':161,  
         'state':1,   #开服几天后显示   
         'merge':6,   #几次合服后可见
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':4,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':96,   #力量
         'agi':90,   #智力 
         'cha':88,   #魅力 
         'lead':94,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,3,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill211':10,'skill261':7,'skill216':5,},
         'fate':{'fate70001':[1,'hero716'],'fate70002':[2,'hero789'],'fate70003':[3,'hero708'],'fate70004':[4,'hero763'],},   #宿命技能
         'resolve':[[['item211',],1875,],[['item261',],2142,],[['item216',],2500,],],   #问道产生技能的ID，权重
         'res':'hero7000',   #模型
         
     },
     'hero7001':{      #张仲景     
         'name':'7007001',
         'info':'7017001',
         'index':162,  
         'state':1,   #开服几天后显示   
         'merge':6,   #几次合服后可见
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':4,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':80,   #力量
         'agi':96,   #智力 
         'cha':99,   #魅力 
         'lead':74,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,4,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill240':10,'skill223':7,'skill272':5,},
         'fate':{'fate70011':[1,'hero765'],'fate70012':[2,'hero787'],'fate70013':[3,'hero788'],'fate70014':[4,'hero785'],},   #宿命技能
         'resolve':[[['item240',],1666,],[['item223',],2142,],[['item272',],1500,],],   #问道产生技能的ID，权重
         'res':'hero7001',   #模型
         
     },
     'hero7002':{      #王昭君     
         'name':'7007002',
         'info':'7017002',
         'index':163,  
         'state':1,   #开服几天后显示   
         'merge':6,   #几次合服后可见
         'sex':0,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':4,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':94,   #力量
         'agi':80,   #智力 
         'cha':100,   #魅力 
         'lead':90,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,3,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill235':10,'skill206':7,'skill218':5,},
         'fate':{'fate70021':[1,'hero703'],'fate70022':[2,'hero721'],'fate70023':[3,'hero770'],'fate70024':[4,'hero783'],},   #宿命技能
         'resolve':[[['item235',],1666,],[['item206',],1875,],[['item218',],2142,],],   #问道产生技能的ID，权重
         'res':'hero7002',   #模型
         
     },
     'hero7003':{      #钟会     
         'name':'7007003',
         'info':'7017003',
         'index':164,  
         'state':1,   #开服几天后显示   
         'merge':7,   #几次合服后可见
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':4,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':79,   #力量
         'agi':95,   #智力 
         'cha':90,   #魅力 
         'lead':87,   #统帅
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,3,4],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill275':7,'skill247':7,'skill251':7,},
         'fate':{'fate70031':[1,'hero7004'],'fate70032':[2,'hero762'],'fate70033':[3,'hero778'],'fate70034':[4,'hero729'],},   #宿命技能
         'resolve':[[['item275',],1500,],[['item247',],1500,],[['item251',],1666,],],   #问道产生技能的ID，权重
         'res':'hero7003',   #模型
         
     },
     'hero7004':{      #邓艾     
         'name':'7007004',
         'info':'7017004',
         'index':165,  
         'state':1,   #开服几天后显示   
         'merge':7,   #几次合服后可见
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':4,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':71,   #力量
         'agi':93,   #智力 
         'cha':95,   #魅力 
         'lead':91,   #统帅
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,3,4],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill973':10,'skill292':7,'skill269':5,},
         'fate':{'fate70041':[1,'hero729'],'fate70042':[2,'hero7003'],'fate70043':[3,'hero778'],'fate70044':[4,'hero793'],},   #宿命技能
         'resolve':[[['item973',],1500,],[['item292',],1875,],[['item269',],2142,],],   #问道产生技能的ID，权重
         'res':'hero7004',   #模型
         
     },
     'hero7005':{      #潘凤     
         'name':'7007005',
         'info':'7017005',
         'index':166,  
         'state':1,   #开服几天后显示   
         'merge':7,   #几次合服后可见
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':4,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':85,   #力量
         'agi':65,   #智力 
         'cha':92,   #魅力 
         'lead':78,   #统帅
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[3,5,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill241':7,'skill245':7,'skill250':7,},
         'fate':{'fate70051':[1,'hero756'],'fate70052':[2,'hero730'],'fate70053':[3,'hero789'],'fate70054':[4,'hero706'],},   #宿命技能
         'resolve':[[['item241',],1500,],[['item245',],1666,],[['item250',],1666,],],   #问道产生技能的ID，权重
         'res':'hero7005',   #模型
         
     },
     'hero7006':{      #木鹿大王     
         'name':'7007006',
         'info':'7017006',
         'index':167,  
         'state':1,   #开服几天后显示   
         'merge':7,   #几次合服后可见
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':4,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':90,   #力量
         'agi':84,   #智力 
         'cha':76,   #魅力 
         'lead':89,   #统帅
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,3,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill243':10,'skill254':7,'skill290':5,},
         'fate':{'fate70061':[1,'hero711'],'fate70062':[2,'hero720'],'fate70063':[3,'hero714'],'fate70064':[4,'hero781'],},   #宿命技能
         'resolve':[[['item243',],1666,],[['item254',],1500,],[['item290',],2142,],],   #问道产生技能的ID，权重
         'res':'hero7006',   #模型
         
     },
     'hero7007':{      #李儒     
         'name':'7007007',
         'info':'7017007',
         'index':168,  
         'state':1,   #开服几天后显示   
         'merge':7,   #几次合服后可见
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':4,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':69,   #力量
         'agi':93,   #智力 
         'cha':88,   #魅力 
         'lead':92,   #统帅
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,3,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill961':7,'skill222':7,'skill212':7,},
         'fate':{'fate70071':[1,'hero7702'],'fate70072':[2,'hero713'],'fate70073':[3,'hero708'],'fate70074':[4,'hero703'],},   #宿命技能
         'resolve':[[['item961',],1500,],[['item222',],2500,],[['item212',],2142,],],   #问道产生技能的ID，权重
         'res':'hero7007',   #模型
         
     },
     'hero7008':{      #何进     
         'name':'7007008',
         'info':'7017008',
         'index':169,  
         'state':1,   #开服几天后显示   
         'merge':7,   #几次合服后可见
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':4,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':86,   #力量
         'agi':87,   #智力 
         'cha':91,   #魅力 
         'lead':93,   #统帅
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,4,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill970':7,'skill967':7,'skill265':3,},
         'fate':{'fate70081':[1,'hero713'],'fate70082':[2,'hero7702'],'fate70083':[3,'hero789'],'fate70084':[4,'hero770'],},   #宿命技能
         'resolve':[[['item970',],1500,],[['item967',],1875,],[['item265',],2142,],],   #问道产生技能的ID，权重
         'res':'hero7008',   #模型
         
     },
     'hero757':{      #典韦     
         'name':'700757',
         'info':'701757',
         'index':200,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':99,   #力量
         'agi':65,   #智力 
         'cha':61,   #魅力 
         'lead':76,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,4,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill289':5,'skill268':5,},
         'fate':{'fate7571':[1,'hero726'],'fate7572':[2,'hero702'],'fate7573':[3,'hero708'],},   #宿命技能
         'resolve':[[['item289',2],3000,],[['item268',2],2142,],],   #问道产生技能的ID，权重
         'res':'hero757',   #模型
         'fast_hero_star':[300,400,500,600,700],
     },
     'hero755':{      #周泰     
         'name':'700755',
         'info':'701755',
         'index':201,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':95,   #力量
         'agi':68,   #智力 
         'cha':70,   #魅力 
         'lead':81,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,3,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill201':5,'skill269':5,},
         'fate':{'fate7551':[1,'hero733'],'fate7552':[2,'hero709'],'fate7553':[3,'hero765'],},   #宿命技能
         'resolve':[[['item201',2],3000,],[['item269',2],2142,],],   #问道产生技能的ID，权重
         'res':'hero755',   #模型
         'fast_hero_star':[300,400,500,600,700],
     },
     'hero729':{      #姜维     
         'name':'700729',
         'info':'701729',
         'index':250,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':90,   #力量
         'agi':92,   #智力 
         'cha':87,   #魅力 
         'lead':94,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,4,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill228':5,'skill220':5,'skill222':5,},
         'fate':{'fate7291':[1,'hero740'],'fate7292':[2,'hero754'],'fate7293':[3,'hero732'],},   #宿命技能
         'adjs':['hero740'],
         'resolve':[[['item228',2],1875,],[['item220',2],3000,],[['item222',2],2500,],],   #问道产生技能的ID，权重
         'res':'hero729',   #模型
         'fast_hero_star':[600,800,1000,1200,1500],
     },
     'hero702':{      #许褚     
         'name':'700702',
         'info':'701702',
         'index':251,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':96,   #力量
         'agi':61,   #智力 
         'cha':67,   #魅力 
         'lead':80,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,3,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill201':3,'skill289':5,'skill213':3,},
         'fate':{'fate7021':[1,'hero701'],'fate7022':[2,'hero757'],'fate7023':[3,'hero743'],},   #宿命技能
         'adjs':['hero701'],
         'resolve':[[['item201',2],3000,],[['item289',2],3000,],[['item213',2],3000,],],   #问道产生技能的ID，权重
         'res':'hero702',   #模型
         'fast_hero_star':[600,800,1000,1200,1500],
     },
     'hero733':{      #甘宁     
         'name':'700733',
         'info':'701733',
         'index':252,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':95,   #力量
         'agi':73,   #智力 
         'cha':85,   #魅力 
         'lead':86,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,3,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill204':5,'skill289':5,'skill262':1,},
         'fate':{'fate7331':[1,'hero753'],'fate7332':[2,'hero728'],'fate7333':[3,'hero748'],},   #宿命技能
         'adjs':['hero753'],
         'resolve':[[['item204',2],2500,],[['item289',2],3000,],[['item262',2],2142,],],   #问道产生技能的ID，权重
         'res':'hero733',   #模型
         'fast_hero_star':[600,800,1000,1200,1500],
     },
     'hero701':{      #马超     
         'name':'700701',
         'info':'701701',
         'index':253,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':97,   #力量
         'agi':72,   #智力 
         'cha':90,   #魅力 
         'lead':89,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,2,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill207':3,'skill210':5,'skill221':3,},
         'fate':{'fate7011':[1,'hero702'],'fate7012':[2,'hero731'],'fate7013':[3,'hero737'],},   #宿命技能
         'adjs':['hero702'],
         'resolve':[[['item207',2],3000,],[['item210',2],2500,],[['item221',2],2500,],],   #问道产生技能的ID，权重
         'res':'hero701',   #模型
         'fast_hero_star':[600,800,1000,1200,1500],
     },
     'hero726':{      #夏侯惇     
         'name':'700726',
         'info':'701726',
         'index':254,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':93,   #力量
         'agi':75,   #智力 
         'cha':83,   #魅力 
         'lead':90,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,4,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill208':5,'skill210':5,'skill213':3,},
         'fate':{'fate7261':[1,'hero727'],'fate7262':[2,'hero734'],'fate7263':[3,'hero744'],},   #宿命技能
         'adjs':['hero727'],
         'resolve':[[['item208',2],2500,],[['item210',2],2500,],[['item213',2],3000,],],   #问道产生技能的ID，权重
         'res':'hero726',   #模型
         'fast_hero_star':[600,800,1000,1200,1500],
     },
     'hero728':{      #太史慈     
         'name':'700728',
         'info':'701728',
         'index':255,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':94,   #力量
         'agi':75,   #智力 
         'cha':82,   #魅力 
         'lead':91,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,3,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill201':5,'skill216':3,'skill268':3,},
         'fate':{'fate7281':[1,'hero733'],'fate7282':[2,'hero710'],'fate7283':[3,'hero739'],},   #宿命技能
         'adjs':['hero733'],
         'resolve':[[['item201',2],3000,],[['item216',2],2500,],[['item268',2],2142,],],   #问道产生技能的ID，权重
         'res':'hero728',   #模型
         'fast_hero_star':[600,800,1000,1200,1500],
     },
     'hero736':{      #黄忠     
         'name':'700736',
         'info':'701736',
         'index':256,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':93,   #力量
         'agi':80,   #智力 
         'cha':75,   #魅力 
         'lead':92,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[3,5,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill213':7,'skill214':5,'skill216':5,},
         'fate':{'fate7361':[1,'hero716'],'fate7362':[2,'hero732'],'fate7363':[3,'hero727'],},   #宿命技能
         'adjs':['hero716'],
         'resolve':[[['item213',2],3000,],[['item214',2],2500,],[['item216',2],2500,],],   #问道产生技能的ID，权重
         'res':'hero736',   #模型
         'fast_hero_star':[600,800,1000,1200,1500],
     },
     'hero734':{      #荀彧     
         'name':'700734',
         'info':'701734',
         'index':257,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':51,   #力量
         'agi':94,   #智力 
         'cha':91,   #魅力 
         'lead':65,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[3,4,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill201':5,'skill216':5,'skill263':1,},
         'fate':{'fate7341':[1,'hero717'],'fate7342':[2,'hero715'],'fate7343':[3,'hero745'],},   #宿命技能
         'adjs':['hero717'],
         'resolve':[[['item201',2],3000,],[['item216',2],2500,],[['item263',2],2142,],],   #问道产生技能的ID，权重
         'res':'hero734',   #模型
         'fast_hero_star':[600,800,1000,1200,1500],
     },
     'hero725':{      #吕蒙     
         'name':'700725',
         'info':'701725',
         'index':258,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':87,   #力量
         'agi':93,   #智力 
         'cha':83,   #魅力 
         'lead':95,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[3,4,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill219':5,'skill222':5,'skill265':1,},
         'fate':{'fate7251':[1,'hero751'],'fate7252':[2,'hero746'],'fate7253':[3,'hero755'],},   #宿命技能
         'adjs':['hero751'],
         'resolve':[[['item219',2],3000,],[['item222',2],2500,],[['item265',2],2142,],],   #问道产生技能的ID，权重
         'res':'hero725',   #模型
         'fast_hero_star':[600,800,1000,1200,1500],
     },
     'hero731':{      #张飞     
         'name':'700731',
         'info':'701731',
         'index':259,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':99,   #力量
         'agi':71,   #智力 
         'cha':74,   #魅力 
         'lead':90,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,4,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill208':5,'skill220':3,'skill261':7,},
         'fate':{'fate7311':[1,'hero706'],'fate7312':[2,'hero730'],'fate7313':[3,'hero754'],},   #宿命技能
         'adjs':['hero706'],
         'resolve':[[['item208',2],2500,],[['item220',2],3000,],[['item261',2],2142,],],   #问道产生技能的ID，权重
         'res':'hero731',   #模型
         'fast_hero_star':[600,800,1000,1200,1500],
     },
     'hero758':{      #于禁     
         'name':'700758',
         'info':'701758',
         'index':300,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':82,   #力量
         'agi':70,   #智力 
         'cha':64,   #魅力 
         'lead':90,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,4,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill201':5,'skill289':3,},
         'fate':{},   #宿命技能
         'resolve':[[['item201',2],3000,],[['item289',2],3000,],],   #问道产生技能的ID，权重
         'res':'hero_04',   #模型
         'fast_hero_star':[300,400,500,600,700],
     },
     'hero759':{      #乐进     
         'name':'700759',
         'info':'701759',
         'index':301,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':84,   #力量
         'agi':74,   #智力 
         'cha':72,   #魅力 
         'lead':83,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,3,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill207':3,'skill220':3,},
         'fate':{},   #宿命技能
         'resolve':[[['item207',2],3000,],[['item220',2],3000,],],   #问道产生技能的ID，权重
         'res':'hero_01',   #模型
         'fast_hero_star':[300,400,500,600,700],
     },
     'hero760':{      #王司徒     
         'name':'700760',
         'info':'701760',
         'index':302,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':51,   #力量
         'agi':92,   #智力 
         'cha':72,   #魅力 
         'lead':69,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[3,4,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill289':3,'skill213':3,},
         'fate':{},   #宿命技能
         'resolve':[[['item289',2],3000,],[['item213',2],3000,],],   #问道产生技能的ID，权重
         'res':'hero_02',   #模型
         'fast_hero_star':[300,400,500,600,700],
     },
     'hero738':{      #孙乾     
         'name':'700738',
         'info':'701738',
         'index':303,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':55,   #力量
         'agi':84,   #智力 
         'cha':84,   #魅力 
         'lead':66,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,2,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill215':3,'skill281':3,},
         'fate':{},   #宿命技能
         'resolve':[[['item215',2],3000,],[['item281',2],3000,],],   #问道产生技能的ID，权重
         'res':'hero_02',   #模型
         'fast_hero_star':[300,400,500,600,700],
     },
     'hero739':{      #庞德     
         'name':'700739',
         'info':'701739',
         'index':304,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':93,   #力量
         'agi':70,   #智力 
         'cha':74,   #魅力 
         'lead':83,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,2,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill289':3,'skill202':3,},
         'fate':{},   #宿命技能
         'resolve':[[['item289',2],3000,],[['item202',2],2500,],],   #问道产生技能的ID，权重
         'res':'hero_06',   #模型
         'fast_hero_star':[300,400,500,600,700],
     },
     'hero740':{      #廖化     
         'name':'700740',
         'info':'701740',
         'index':305,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':78,   #力量
         'agi':66,   #智力 
         'cha':68,   #魅力 
         'lead':71,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,4,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill201':3,'skill204':3,},
         'fate':{},   #宿命技能
         'resolve':[[['item201',2],3000,],[['item204',2],2500,],],   #问道产生技能的ID，权重
         'res':'hero_01',   #模型
         'fast_hero_star':[300,400,500,600,700],
     },
     'hero741':{      #徐晃     
         'name':'700741',
         'info':'701741',
         'index':306,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':92,   #力量
         'agi':74,   #智力 
         'cha':73,   #魅力 
         'lead':84,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,4,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill209':5,'skill215':5,},
         'fate':{},   #宿命技能
         'resolve':[[['item209',2],3000,],[['item215',2],3000,],],   #问道产生技能的ID，权重
         'res':'hero_03',   #模型
         'fast_hero_star':[300,400,500,600,700],
     },
     'hero742':{      #文丑     
         'name':'700742',
         'info':'701742',
         'index':307,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':93,   #力量
         'agi':63,   #智力 
         'cha':58,   #魅力 
         'lead':78,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,2,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill207':3,'skill208':3,},
         'fate':{},   #宿命技能
         'resolve':[[['item207',2],3000,],[['item208',2],2500,],],   #问道产生技能的ID，权重
         'res':'hero_06',   #模型
         'fast_hero_star':[300,400,500,600,700],
     },
     'hero743':{      #曹仁     
         'name':'700743',
         'info':'701743',
         'index':308,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':86,   #力量
         'agi':81,   #智力 
         'cha':76,   #魅力 
         'lead':89,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,5,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill201':5,'skill215':3,},
         'fate':{},   #宿命技能
         'resolve':[[['item201',2],3000,],[['item215',2],3000,],],   #问道产生技能的ID，权重
         'res':'hero_01',   #模型
         'fast_hero_star':[300,400,500,600,700],
     },
     'hero744':{      #程普     
         'name':'700744',
         'info':'701744',
         'index':309,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':80,   #力量
         'agi':78,   #智力 
         'cha':83,   #魅力 
         'lead':84,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[3,4,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill215':3,'skill282':5,},
         'fate':{},   #宿命技能
         'resolve':[[['item215',2],3000,],[['item282',2],3000,],],   #问道产生技能的ID，权重
         'res':'hero_02',   #模型
         'fast_hero_star':[300,400,500,600,700],
     },
     'hero745':{      #荀攸     
         'name':'700745',
         'info':'701745',
         'index':310,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':52,   #力量
         'agi':93,   #智力 
         'cha':84,   #魅力 
         'lead':73,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,5,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill219':5,'skill220':3,},
         'fate':{},   #宿命技能
         'resolve':[[['item219',2],3000,],[['item220',2],3000,],],   #问道产生技能的ID，权重
         'res':'hero_02',   #模型
         'fast_hero_star':[300,400,500,600,700],
     },
     'hero746':{      #诸葛瑾     
         'name':'700746',
         'info':'701746',
         'index':311,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':57,   #力量
         'agi':85,   #智力 
         'cha':90,   #魅力 
         'lead':75,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,3,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill201':5,'skill219':3,},
         'fate':{},   #宿命技能
         'resolve':[[['item201',2],3000,],[['item219',2],3000,],],   #问道产生技能的ID，权重
         'res':'hero_02',   #模型
         'fast_hero_star':[300,400,500,600,700],
     },
     'hero747':{      #陈宫     
         'name':'700747',
         'info':'701747',
         'index':312,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':65,   #力量
         'agi':91,   #智力 
         'cha':76,   #魅力 
         'lead':90,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[3,4,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill219':3,'skill281':5,},
         'fate':{},   #宿命技能
         'resolve':[[['item219',2],3000,],[['item281',2],3000,],],   #问道产生技能的ID，权重
         'res':'hero_02',   #模型
         'fast_hero_star':[300,400,500,600,700],
     },
     'hero748':{      #韩当     
         'name':'700748',
         'info':'701748',
         'index':313,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':83,   #力量
         'agi':67,   #智力 
         'cha':68,   #魅力 
         'lead':76,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,3,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill207':3,'skill219':3,},
         'fate':{},   #宿命技能
         'resolve':[[['item207',2],3000,],[['item219',2],3000,],],   #问道产生技能的ID，权重
         'res':'hero_04',   #模型
         'fast_hero_star':[300,400,500,600,700],
     },
     'hero749':{      #颜良     
         'name':'700749',
         'info':'701749',
         'index':314,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':92,   #力量
         'agi':64,   #智力 
         'cha':56,   #魅力 
         'lead':79,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,4,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill209':3,'skill213':3,},
         'fate':{},   #宿命技能
         'resolve':[[['item209',2],3000,],[['item213',2],3000,],],   #问道产生技能的ID，权重
         'res':'hero_05',   #模型
         'fast_hero_star':[300,400,500,600,700],
     },
     'hero750':{      #马岱     
         'name':'700750',
         'info':'701750',
         'index':315,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':82,   #力量
         'agi':72,   #智力 
         'cha':74,   #魅力 
         'lead':81,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,3,4],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill207':3,'skill215':3,},
         'fate':{},   #宿命技能
         'resolve':[[['item207',2],3000,],[['item215',2],3000,],],   #问道产生技能的ID，权重
         'res':'hero_03',   #模型
         'fast_hero_star':[300,400,500,600,700],
     },
     'hero751':{      #鲁肃     
         'name':'700751',
         'info':'701751',
         'index':316,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':64,   #力量
         'agi':92,   #智力 
         'cha':89,   #魅力 
         'lead':80,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,3,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill208':5,'skill219':5,},
         'fate':{},   #宿命技能
         'resolve':[[['item208',2],2500,],[['item219',2],3000,],],   #问道产生技能的ID，权重
         'res':'hero_02',   #模型
         'fast_hero_star':[300,400,500,600,700],
     },
     'hero752':{      #黄盖     
         'name':'700752',
         'info':'701752',
         'index':317,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':83,   #力量
         'agi':73,   #智力 
         'cha':81,   #魅力 
         'lead':79,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[4,5,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill213':5,'skill215':5,},
         'fate':{},   #宿命技能
         'resolve':[[['item213',2],3000,],[['item215',2],3000,],],   #问道产生技能的ID，权重
         'res':'hero_05',   #模型
         'fast_hero_star':[300,400,500,600,700],
     },
     'hero753':{      #凌统     
         'name':'700753',
         'info':'701753',
         'index':318,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':93,   #力量
         'agi':63,   #智力 
         'cha':89,   #魅力 
         'lead':78,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[2,3,4],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill207':3,'skill213':3,},
         'fate':{},   #宿命技能
         'resolve':[[['item207',2],3000,],[['item213',2],3000,],],   #问道产生技能的ID，权重
         'res':'hero_06',   #模型
         'fast_hero_star':[300,400,500,600,700],
     },
     'hero754':{      #夏侯霸     
         'name':'700754',
         'info':'701754',
         'index':319,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':88,   #力量
         'agi':82,   #智力 
         'cha':85,   #魅力 
         'lead':86,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,2,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill209':5,'skill219':5,},
         'fate':{},   #宿命技能
         'resolve':[[['item209',2],3000,],[['item219',2],3000,],],   #问道产生技能的ID，权重
         'res':'hero_06',   #模型
         'fast_hero_star':[300,400,500,600,700],
     },
     'hero756':{      #华雄     
         'name':'700756',
         'info':'701756',
         'index':320,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':91,   #力量
         'agi':65,   #智力 
         'cha':60,   #魅力 
         'lead':81,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,2,4],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill207':3,'skill209':5,},
         'fate':{},   #宿命技能
         'resolve':[[['item207',2],3000,],[['item209',2],3000,],],   #问道产生技能的ID，权重
         'res':'hero_01',   #模型
         'fast_hero_star':[300,400,500,600,700],
     },
     'hero727':{      #夏侯渊     
         'name':'700727',
         'info':'701727',
         'index':350,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':92,   #力量
         'agi':77,   #智力 
         'cha':80,   #魅力 
         'lead':88,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,3,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill209':5,'skill208':5,'skill269':3,},
         'fate':{'fate7271':[1,'hero730'],'fate7272':[2,'hero736'],'fate7273':[3,'hero754'],},   #宿命技能
         'adjs':['hero730'],
         'resolve':[[['item209',2],3000,],[['item208',2],2500,],[['item269',2],2142,],],   #问道产生技能的ID，权重
         'res':'hero_03',   #模型
         'fast_hero_star':[600,800,1000,1200,1500],
     },
     'hero730':{      #张郃     
         'name':'700730',
         'info':'701730',
         'index':351,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':92,   #力量
         'agi':87,   #智力 
         'cha':82,   #魅力 
         'lead':92,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,3,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill207':5,'skill209':5,'skill214':5,},
         'fate':{'fate7301':[1,'hero712'],'fate7302':[2,'hero741'],'fate7303':[3,'hero759'],},   #宿命技能
         'adjs':['hero712'],
         'resolve':[[['item207',2],3000,],[['item209',2],3000,],[['item214',2],2500,],],   #问道产生技能的ID，权重
         'res':'hero_04',   #模型
         'fast_hero_star':[600,800,1000,1200,1500],
     },
     'hero732':{      #法正     
         'name':'700732',
         'info':'701732',
         'index':352,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':58,   #力量
         'agi':94,   #智力 
         'cha':79,   #魅力 
         'lead':82,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,4,5],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill219':5,'skill220':5,'skill264':1,},
         'fate':{'fate7321':[1,'hero735'],'fate7322':[2,'hero727'],'fate7323':[3,'hero736'],},   #宿命技能
         'adjs':['hero735'],
         'resolve':[[['item219',2],3000,],[['item220',2],3000,],[['item264',2],2142,],],   #问道产生技能的ID，权重
         'res':'hero_02',   #模型
         'fast_hero_star':[600,800,1000,1200,1500],
     },
     'hero735':{      #魏延     
         'name':'700735',
         'info':'701735',
         'index':353,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':92,   #力量
         'agi':76,   #智力 
         'cha':70,   #魅力 
         'lead':87,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[1,2,4],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill289':5,'skill202':3,'skill204':7,},
         'fate':{'fate7351':[1,'hero736'],'fate7352':[2,'hero729'],'fate7353':[3,'hero750'],},   #宿命技能
         'adjs':['hero736'],
         'resolve':[[['item289',2],3000,],[['item202',2],2500,],[['item204',2],2500,],],   #问道产生技能的ID，权重
         'res':'hero_05',   #模型
         'fast_hero_star':[600,800,1000,1200,1500],
     },
     'hero737':{      #徐庶     
         'name':'700737',
         'info':'701737',
         'index':354,  
         'state':1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':67,   #力量
         'agi':95,   #智力 
         'cha':84,   #魅力 
         'lead':85,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'arr':[3,5,6],   #可用阵法，1锋矢阵，2钩行阵，3雁形阵，4鹤翼阵，5铁桶阵，6八卦阵
         'skill':{'skill202':3,'skill213':3,'skill214':5,},
         'fate':{'fate7371':[1,'hero747'],'fate7372':[2,'hero738'],'fate7373':[3,'hero734'],},   #宿命技能
         'adjs':['hero747'],
         'resolve':[[['item202',2],2500,],[['item213',2],3000,],[['item214',2],2500,],],   #问道产生技能的ID，权重
         'res':'hero_02',   #模型
         'fast_hero_star':[600,800,1000,1200,1500],
     },
     'hero101':{      #黄巾先锋     
         'name':'700101',
         'index':700,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero409',
         'str':82,   #力量
         'agi':70,   #智力 
         'cha':72,   #魅力 
         'lead':50,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_01',   #模型
         
     },
     'hero102':{      #黄巾主力     
         'name':'700102',
         'index':701,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero409',
         'str':71,   #力量
         'agi':77,   #智力 
         'cha':65,   #魅力 
         'lead':60,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_02',   #模型
         
     },
     'hero103':{      #黄巾精英     
         'name':'700103',
         'index':702,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero410',
         'str':81,   #力量
         'agi':72,   #智力 
         'cha':70,   #魅力 
         'lead':60,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_03',   #模型
         
     },
     'hero104':{      #黄巾首领     
         'name':'700104',
         'index':703,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero411',
         'str':85,   #力量
         'agi':70,   #智力 
         'cha':70,   #魅力 
         'lead':70,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_04',   #模型
         
     },
     'hero105':{      #黄巾辎重队     
         'name':'700105',
         'index':704,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero405',
         'str':65,   #力量
         'agi':60,   #智力 
         'cha':59,   #魅力 
         'lead':50,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_05',   #模型
         
     },
     'hero106':{      #黄巾巡逻队     
         'name':'700106',
         'index':705,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero409',
         'str':69,   #力量
         'agi':72,   #智力 
         'cha':55,   #魅力 
         'lead':50,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_06',   #模型
         
     },
     'hero107':{      #程远志     
         'name':'700107',
         'index':706,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero410',
         'str':93,   #力量
         'agi':63,   #智力 
         'cha':58,   #魅力 
         'lead':76,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_01',   #模型
         
     },
     'hero108':{      #邓茂     
         'name':'700108',
         'index':707,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero411',
         'str':65,   #力量
         'agi':88,   #智力 
         'cha':76,   #魅力 
         'lead':85,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_02',   #模型
         
     },
     'hero109':{      #张梁     
         'name':'700109',
         'index':708,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero410',
         'str':81,   #力量
         'agi':79,   #智力 
         'cha':76,   #魅力 
         'lead':79,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_03',   #模型
         
     },
     'hero110':{      #张宝     
         'name':'700110',
         'index':709,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero801',
         'str':75,   #力量
         'agi':88,   #智力 
         'cha':78,   #魅力 
         'lead':83,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_04',   #模型
         
     },
     'hero111':{      #未使用     
         'name':'700111',
         'index':710,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero747',
         'str':84,   #力量
         'agi':98,   #智力 
         'cha':91,   #魅力 
         'lead':92,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_05',   #模型
         
     },
     'hero112':{      #赵弘     
         'name':'700112',
         'index':711,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero403',
         'str':85,   #力量
         'agi':61,   #智力 
         'cha':70,   #魅力 
         'lead':83,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_06',   #模型
         
     },
     'hero113':{      #孙仲     
         'name':'700113',
         'index':712,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero403',
         'str':82,   #力量
         'agi':63,   #智力 
         'cha':65,   #魅力 
         'lead':75,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_01',   #模型
         
     },
     'hero114':{      #韩忠     
         'name':'700114',
         'index':713,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero410',
         'str':85,   #力量
         'agi':71,   #智力 
         'cha':70,   #魅力 
         'lead':77,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_02',   #模型
         
     },
     'hero115':{      #叛军先锋     
         'name':'700115',
         'index':714,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero411',
         'str':84,   #力量
         'agi':72,   #智力 
         'cha':65,   #魅力 
         'lead':81,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_03',   #模型
         
     },
     'hero116':{      #叛军中军     
         'name':'700116',
         'index':715,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero404',
         'str':73,   #力量
         'agi':87,   #智力 
         'cha':63,   #魅力 
         'lead':82,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_04',   #模型
         
     },
     'hero117':{      #张纯     
         'name':'700117',
         'index':716,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero409',
         'str':88,   #力量
         'agi':72,   #智力 
         'cha':65,   #魅力 
         'lead':86,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_05',   #模型
         
     },
     'hero118':{      #张举     
         'name':'700118',
         'index':717,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero410',
         'str':81,   #力量
         'agi':91,   #智力 
         'cha':63,   #魅力 
         'lead':87,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_06',   #模型
         
     },
     'hero119':{      #禁军部队     
         'name':'700119',
         'index':718,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero411',
         'str':88,   #力量
         'agi':72,   #智力 
         'cha':65,   #魅力 
         'lead':86,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_01',   #模型
         
     },
     'hero120':{      #禁军精英     
         'name':'700120',
         'index':719,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero403',
         'str':81,   #力量
         'agi':91,   #智力 
         'cha':63,   #魅力 
         'lead':87,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_02',   #模型
         
     },
     'hero121':{      #蹇硕     
         'name':'700121',
         'index':720,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero403',
         'str':91,   #力量
         'agi':72,   #智力 
         'cha':65,   #魅力 
         'lead':86,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_03',   #模型
         
     },
     'hero122':{      #禁军统领     
         'name':'700122',
         'index':721,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero410',
         'str':82,   #力量
         'agi':84,   #智力 
         'cha':63,   #魅力 
         'lead':87,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_04',   #模型
         
     },
     'hero123':{      #张让     
         'name':'700123',
         'index':722,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero411',
         'str':88,   #力量
         'agi':77,   #智力 
         'cha':69,   #魅力 
         'lead':75,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_05',   #模型
         
     },
     'hero124':{      #赵忠     
         'name':'700124',
         'index':723,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero401',
         'str':71,   #力量
         'agi':88,   #智力 
         'cha':69,   #魅力 
         'lead':79,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_06',   #模型
         
     },
     'hero125':{      #封谞     
         'name':'700125',
         'index':724,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero403',
         'str':85,   #力量
         'agi':72,   #智力 
         'cha':71,   #魅力 
         'lead':74,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_01',   #模型
         
     },
     'hero126':{      #段珪     
         'name':'700126',
         'index':725,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero404',
         'str':72,   #力量
         'agi':87,   #智力 
         'cha':73,   #魅力 
         'lead':76,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_02',   #模型
         
     },
     'hero127':{      #曹节     
         'name':'700127',
         'index':726,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero403',
         'str':82,   #力量
         'agi':72,   #智力 
         'cha':72,   #魅力 
         'lead':72,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_03',   #模型
         
     },
     'hero128':{      #侯览     
         'name':'700128',
         'index':727,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero404',
         'str':76,   #力量
         'agi':87,   #智力 
         'cha':74,   #魅力 
         'lead':71,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_04',   #模型
         
     },
     'hero129':{      #程旷     
         'name':'700129',
         'index':728,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero403',
         'str':84,   #力量
         'agi':72,   #智力 
         'cha':76,   #魅力 
         'lead':73,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_05',   #模型
         
     },
     'hero130':{      #夏恽     
         'name':'700130',
         'index':729,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero403',
         'str':76,   #力量
         'agi':80,   #智力 
         'cha':71,   #魅力 
         'lead':78,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_06',   #模型
         
     },
     'hero131':{      #郭胜     
         'name':'700131',
         'index':730,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero404',
         'str':73,   #力量
         'agi':85,   #智力 
         'cha':68,   #魅力 
         'lead':74,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_01',   #模型
         
     },
     'hero132':{      #何苗     
         'name':'700132',
         'index':731,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero403',
         'str':93,   #力量
         'agi':81,   #智力 
         'cha':67,   #魅力 
         'lead':85,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_02',   #模型
         
     },
     'hero133':{      #刀斧手     
         'name':'700133',
         'index':732,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero404',
         'str':85,   #力量
         'agi':70,   #智力 
         'cha':67,   #魅力 
         'lead':80,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_03',   #模型
         
     },
     'hero134':{      #西凉兵     
         'name':'700134',
         'index':733,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero403',
         'str':71,   #力量
         'agi':83,   #智力 
         'cha':68,   #魅力 
         'lead':74,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_04',   #模型
         
     },
     'hero135':{      #西凉精英     
         'name':'700135',
         'index':734,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero404',
         'str':88,   #力量
         'agi':79,   #智力 
         'cha':67,   #魅力 
         'lead':82,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_05',   #模型
         
     },
     'hero136':{      #董卓部将     
         'name':'700136',
         'index':735,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero805',
         'str':76,   #力量
         'agi':81,   #智力 
         'cha':68,   #魅力 
         'lead':74,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_06',   #模型
         
     },
     'hero137':{      #董卓心腹     
         'name':'700137',
         'index':736,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero806',
         'str':93,   #力量
         'agi':73,   #智力 
         'cha':67,   #魅力 
         'lead':87,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_01',   #模型
         
     },
     'hero138':{      #董卓侍卫     
         'name':'700138',
         'index':737,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero805',
         'str':73,   #力量
         'agi':85,   #智力 
         'cha':68,   #魅力 
         'lead':74,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_02',   #模型
         
     },
     'hero139':{      #丁原     
         'name':'700139',
         'index':738,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero403',
         'str':77,   #力量
         'agi':81,   #智力 
         'cha':67,   #魅力 
         'lead':85,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_03',   #模型
         
     },
     'hero140':{      #丁原部将     
         'name':'700140',
         'index':739,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero805',
         'str':89,   #力量
         'agi':80,   #智力 
         'cha':67,   #魅力 
         'lead':86,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_04',   #模型
         
     },
     'hero141':{      #何进部将     
         'name':'700141',
         'index':740,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero806',
         'str':88,   #力量
         'agi':81,   #智力 
         'cha':67,   #魅力 
         'lead':88,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_05',   #模型
         
     },
     'hero142':{      #西凉将领     
         'name':'700142',
         'index':741,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero805',
         'str':73,   #力量
         'agi':85,   #智力 
         'cha':68,   #魅力 
         'lead':74,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_06',   #模型
         
     },
     'hero143':{      #赵岑     
         'name':'700143',
         'index':742,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero806',
         'str':81,   #力量
         'agi':71,   #智力 
         'cha':70,   #魅力 
         'lead':83,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_01',   #模型
         
     },
     'hero144':{      #华雄     
         'name':'700144',
         'index':743,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero805',
         'str':93,   #力量
         'agi':73,   #智力 
         'cha':73,   #魅力 
         'lead':84,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_02',   #模型
         
     },
     'hero145':{      #张济     
         'name':'700145',
         'index':744,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero806',
         'str':86,   #力量
         'agi':74,   #智力 
         'cha':70,   #魅力 
         'lead':85,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_03',   #模型
         
     },
     'hero146':{      #徐荣     
         'name':'700146',
         'index':745,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero808',
         'str':88,   #力量
         'agi':75,   #智力 
         'cha':73,   #魅力 
         'lead':80,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_04',   #模型
         
     },
     'hero147':{      #胡轸     
         'name':'700147',
         'index':746,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero809',
         'str':82,   #力量
         'agi':76,   #智力 
         'cha':70,   #魅力 
         'lead':81,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_05',   #模型
         
     },
     'hero148':{      #樊稠     
         'name':'700148',
         'index':747,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero805',
         'str':89,   #力量
         'agi':72,   #智力 
         'cha':73,   #魅力 
         'lead':87,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_06',   #模型
         
     },
     'hero149':{      #西凉悍将     
         'name':'700149',
         'index':748,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero806',
         'str':90,   #力量
         'agi':76,   #智力 
         'cha':70,   #魅力 
         'lead':81,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_01',   #模型
         
     },
     'hero150':{      #西凉乱军     
         'name':'700150',
         'index':749,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero808',
         'str':84,   #力量
         'agi':72,   #智力 
         'cha':73,   #魅力 
         'lead':87,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_02',   #模型
         
     },
     'hero151':{      #李儒     
         'name':'700151',
         'index':750,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero403',
         'str':73,   #力量
         'agi':96,   #智力 
         'cha':89,   #魅力 
         'lead':94,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_03',   #模型
         
     },
     'hero152':{      #荥阳守军     
         'name':'700152',
         'index':751,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero805',
         'str':79,   #力量
         'agi':77,   #智力 
         'cha':71,   #魅力 
         'lead':82,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_04',   #模型
         
     },
     'hero153':{      #郭汜     
         'name':'700153',
         'index':752,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero806',
         'str':85,   #力量
         'agi':79,   #智力 
         'cha':73,   #魅力 
         'lead':84,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_05',   #模型
         
     },
     'hero154':{      #李蒙     
         'name':'700154',
         'index':753,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero808',
         'str':83,   #力量
         'agi':79,   #智力 
         'cha':73,   #魅力 
         'lead':80,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_06',   #模型
         
     },
     'hero155':{      #桥瑁     
         'name':'700155',
         'index':754,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero809',
         'str':85,   #力量
         'agi':79,   #智力 
         'cha':73,   #魅力 
         'lead':84,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_01',   #模型
         
     },
     'hero156':{      #桥瑁部将     
         'name':'700156',
         'index':755,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero805',
         'str':83,   #力量
         'agi':79,   #智力 
         'cha':73,   #魅力 
         'lead':80,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_02',   #模型
         
     },
     'hero157':{      #周昂     
         'name':'700157',
         'index':756,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero806',
         'str':72,   #力量
         'agi':87,   #智力 
         'cha':73,   #魅力 
         'lead':76,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_03',   #模型
         
     },
     'hero158':{      #周昂部将     
         'name':'700158',
         'index':757,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero808',
         'str':82,   #力量
         'agi':72,   #智力 
         'cha':72,   #魅力 
         'lead':72,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_04',   #模型
         
     },
     'hero159':{      #黄祖     
         'name':'700159',
         'index':758,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero809',
         'str':76,   #力量
         'agi':87,   #智力 
         'cha':74,   #魅力 
         'lead':71,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_05',   #模型
         
     },
     'hero160':{      #荆州将领     
         'name':'700160',
         'index':759,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero403',
         'str':84,   #力量
         'agi':72,   #智力 
         'cha':76,   #魅力 
         'lead':73,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_06',   #模型
         
     },
     'hero161':{      #蔡瑁     
         'name':'700161',
         'index':760,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero404',
         'str':76,   #力量
         'agi':80,   #智力 
         'cha':71,   #魅力 
         'lead':78,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_01',   #模型
         
     },
     'hero162':{      #蒯越     
         'name':'700162',
         'index':761,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero403',
         'str':73,   #力量
         'agi':85,   #智力 
         'cha':68,   #魅力 
         'lead':74,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_02',   #模型
         
     },
     'hero163':{      #冀州将领     
         'name':'700163',
         'index':762,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero404',
         'str':93,   #力量
         'agi':81,   #智力 
         'cha':67,   #魅力 
         'lead':85,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_03',   #模型
         
     },
     'hero164':{      #韩馥     
         'name':'700164',
         'index':763,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero404',
         'str':85,   #力量
         'agi':70,   #智力 
         'cha':77,   #魅力 
         'lead':80,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_04',   #模型
         
     },
     'hero165':{      #袁绍部将     
         'name':'700165',
         'index':764,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero805',
         'str':82,   #力量
         'agi':81,   #智力 
         'cha':69,   #魅力 
         'lead':85,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_05',   #模型
         
     },
     'hero166':{      #袁绍精锐     
         'name':'700166',
         'index':765,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero806',
         'str':81,   #力量
         'agi':80,   #智力 
         'cha':71,   #魅力 
         'lead':81,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_06',   #模型
         
     },
     'hero167':{      #袁绍亲卫     
         'name':'700167',
         'index':766,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero808',
         'str':78,   #力量
         'agi':84,   #智力 
         'cha':70,   #魅力 
         'lead':86,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_01',   #模型
         
     },
     'hero168':{      #冀州兵     
         'name':'700168',
         'index':767,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero809',
         'str':85,   #力量
         'agi':70,   #智力 
         'cha':67,   #魅力 
         'lead':83,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_02',   #模型
         
     },
     'hero169':{      #袁绍     
         'name':'700169',
         'index':768,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero410',
         'str':89,   #力量
         'agi':84,   #智力 
         'cha':89,   #魅力 
         'lead':89,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_03',   #模型
         
     },
     'hero170':{      #麴义     
         'name':'700170',
         'index':769,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero801',
         'str':89,   #力量
         'agi':84,   #智力 
         'cha':89,   #魅力 
         'lead':89,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_04',   #模型
         
     },
     'hero171':{      #袁绍伏兵     
         'name':'700171',
         'index':770,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero809',
         'str':85,   #力量
         'agi':71,   #智力 
         'cha':69,   #魅力 
         'lead':77,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_05',   #模型
         
     },
     'hero172':{      #黄祖     
         'name':'700172',
         'index':771,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero806',
         'str':85,   #力量
         'agi':71,   #智力 
         'cha':69,   #魅力 
         'lead':77,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_06',   #模型
         
     },
     'hero173':{      #樊城将领     
         'name':'700173',
         'index':772,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero410',
         'str':87,   #力量
         'agi':72,   #智力 
         'cha':72,   #魅力 
         'lead':72,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_01',   #模型
         
     },
     'hero174':{      #黄祖部属     
         'name':'700174',
         'index':773,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero411',
         'str':74,   #力量
         'agi':85,   #智力 
         'cha':74,   #魅力 
         'lead':79,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_02',   #模型
         
     },
     'hero175':{      #蒯良     
         'name':'700175',
         'index':774,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero801',
         'str':73,   #力量
         'agi':87,   #智力 
         'cha':76,   #魅力 
         'lead':76,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_03',   #模型
         
     },
     'hero176':{      #吕公     
         'name':'700176',
         'index':775,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero410',
         'str':80,   #力量
         'agi':83,   #智力 
         'cha':74,   #魅力 
         'lead':80,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_04',   #模型
         
     },
     'hero177':{      #山林伏兵     
         'name':'700177',
         'index':776,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero411',
         'str':73,   #力量
         'agi':80,   #智力 
         'cha':70,   #魅力 
         'lead':74,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_05',   #模型
         
     },
     'hero178':{      #荆州散兵     
         'name':'700178',
         'index':777,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero801',
         'str':87,   #力量
         'agi':81,   #智力 
         'cha':67,   #魅力 
         'lead':84,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_06',   #模型
         
     },
     'hero179':{      #荆州援兵     
         'name':'700179',
         'index':778,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero410',
         'str':88,   #力量
         'agi':70,   #智力 
         'cha':76,   #魅力 
         'lead':75,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_01',   #模型
         
     },
     'hero180':{      #荆州追兵     
         'name':'700180',
         'index':779,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero411',
         'str':80,   #力量
         'agi':81,   #智力 
         'cha':77,   #魅力 
         'lead':80,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_02',   #模型
         
     },
     'hero181':{      #刘表     
         'name':'700181',
         'index':780,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero801',
         'str':71,   #力量
         'agi':94,   #智力 
         'cha':80,   #魅力 
         'lead':91,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_03',   #模型
         
     },
     'hero182':{      #西凉密探     
         'name':'700182',
         'index':781,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero410',
         'str':83,   #力量
         'agi':75,   #智力 
         'cha':76,   #魅力 
         'lead':85,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_04',   #模型
         
     },
     'hero183':{      #管亥     
         'name':'700183',
         'index':782,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero411',
         'str':95,   #力量
         'agi':71,   #智力 
         'cha':72,   #魅力 
         'lead':80,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_05',   #模型
         
     },
     'hero184':{      #沮授     
         'name':'700184',
         'index':783,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero801',
         'str':77,   #力量
         'agi':90,   #智力 
         'cha':75,   #魅力 
         'lead':83,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_06',   #模型
         
     },
     'hero419':{      #马贼     
         'name':'700419',
         'index':784,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero419',
         'str':80,   #力量
         'agi':71,   #智力 
         'cha':72,   #魅力 
         'lead':70,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_02',   #模型
         
     },
     'hero800':{      #土匪     
         'name':'700800',
         'index':800,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero409',
         'str':75,   #力量
         'agi':75,   #智力 
         'cha':75,   #魅力 
         'lead':75,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_01',   #模型
         
     },
     'hero801':{      #月氐来使     
         'name':'700801',
         'index':801,  
         'state':-1,   #开服几天后显示   
         'sex':0,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':80,   #力量
         'agi':100,   #智力 
         'cha':100,   #魅力 
         'lead':80,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_02',   #模型
         
     },
     'hero802':{      #楼兰来使     
         'name':'700802',
         'index':802,  
         'state':-1,   #开服几天后显示   
         'sex':0,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':100,   #力量
         'agi':80,   #智力 
         'cha':100,   #魅力 
         'lead':80,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_03',   #模型
         
     },
     'hero803':{      #高句丽来使     
         'name':'700803',
         'index':803,  
         'state':-1,   #开服几天后显示   
         'sex':0,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':90,   #力量
         'agi':90,   #智力 
         'cha':100,   #魅力 
         'lead':80,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_04',   #模型
         
     },
     'hero804':{      #鲜卑来使     
         'name':'700804',
         'index':804,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':100,   #力量
         'agi':80,   #智力 
         'cha':80,   #魅力 
         'lead':80,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_05',   #模型
         
     },
     'hero805':{      #匈奴来使     
         'name':'700805',
         'index':805,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':80,   #力量
         'agi':100,   #智力 
         'cha':80,   #魅力 
         'lead':80,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_06',   #模型
         
     },
     'hero806':{      #乌桓来使     
         'name':'700806',
         'index':806,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':90,   #力量
         'agi':90,   #智力 
         'cha':80,   #魅力 
         'lead':80,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_01',   #模型
         
     },
     'hero807':{      #羌族来使     
         'name':'700807',
         'index':807,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':100,   #力量
         'agi':80,   #智力 
         'cha':80,   #魅力 
         'lead':80,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_02',   #模型
         
     },
     'hero808':{      #南蛮来使     
         'name':'700808',
         'index':808,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':80,   #力量
         'agi':100,   #智力 
         'cha':80,   #魅力 
         'lead':80,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_03',   #模型
         
     },
     'hero809':{      #山越来使     
         'name':'700809',
         'index':809,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':90,   #力量
         'agi':90,   #智力 
         'cha':80,   #魅力 
         'lead':80,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_04',   #模型
         
     },
     'hero810':{      #邪马台来使     
         'name':'700810',
         'index':810,  
         'state':-1,   #开服几天后显示   
         'sex':0,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':80,   #力量
         'agi':100,   #智力 
         'cha':100,   #魅力 
         'lead':80,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_05',   #模型
         
     },
     'hero820':{      #运钱队     
         'name':'700820',
         'index':820,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero405',
         'str':75,   #力量
         'agi':75,   #智力 
         'cha':60,   #魅力 
         'lead':80,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_06',   #模型
         
     },
     'hero821':{      #步兵辎重队     
         'name':'700821',
         'index':821,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero409',
         'str':90,   #力量
         'agi':60,   #智力 
         'cha':60,   #魅力 
         'lead':80,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_01',   #模型
         
     },
     'hero822':{      #骑兵辎重队     
         'name':'700822',
         'index':822,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero411',
         'str':60,   #力量
         'agi':90,   #智力 
         'cha':60,   #魅力 
         'lead':80,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_02',   #模型
         
     },
     'hero823':{      #弓兵辎重队     
         'name':'700823',
         'index':823,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero410',
         'str':90,   #力量
         'agi':60,   #智力 
         'cha':60,   #魅力 
         'lead':80,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_03',   #模型
         
     },
     'hero824':{      #方士辎重队     
         'name':'700824',
         'index':824,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero416',
         'str':60,   #力量
         'agi':90,   #智力 
         'cha':60,   #魅力 
         'lead':80,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_04',   #模型
         
     },
     'hero825':{      #黄巾军     
         'name':'700825',
         'index':825,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero809',
         'str':100,   #力量
         'agi':80,   #智力 
         'cha':80,   #魅力 
         'lead':80,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_05',   #模型
         
     },
     'hero826':{      #重弩车     
         'name':'700826',
         'index':826,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero411',
         'str':60,   #力量
         'agi':60,   #智力 
         'cha':60,   #魅力 
         'lead':60,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero826',   #模型
         
     },
     'hero827':{      #霹雳车     
         'name':'700827',
         'index':827,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero410',
         'str':80,   #力量
         'agi':80,   #智力 
         'cha':80,   #魅力 
         'lead':80,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero827',   #模型
         
     },
     'hero828':{      #黄巾军     
         'name':'700828',
         'index':828,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero416',
         'str':60,   #力量
         'agi':95,   #智力 
         'cha':95,   #魅力 
         'lead':60,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'skill':{'skill233':17,},
         'res':'hero_02',   #模型
         
     },
     'hero829':{      #黄巾军     
         'name':'700829',
         'index':829,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero417',
         'str':95,   #力量
         'agi':60,   #智力 
         'cha':60,   #魅力 
         'lead':95,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'skill':{'skill234':17,},
         'res':'hero_06',   #模型
         
     },
     'hero830':{      #黄巾军     
         'name':'700830',
         'index':830,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero410',
         'str':85,   #力量
         'agi':85,   #智力 
         'cha':85,   #魅力 
         'lead':85,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         'skill':{'skill271':17,},
         'res':'hero_01',   #模型
         
     },
     'hero831':{      #护国军     
         'name':'700831',
         'index':831,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero415',
         'str':70,   #力量
         'agi':70,   #智力 
         'cha':70,   #魅力 
         'lead':70,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero831',   #模型
         
     },
     'hero850':{      #玄武     
         'name':'700850',
         'info':'701850',
         'index':850,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':120,   #力量
         'agi':80,   #智力 
         'cha':90,   #魅力 
         'lead':100,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'army850',   #模型
         
     },
     'hero851':{      #朱雀     
         'name':'700851',
         'info':'701851',
         'index':851,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':85,   #力量
         'agi':120,   #智力 
         'cha':100,   #魅力 
         'lead':90,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'army851',   #模型
         
     },
     'hero852':{      #白虎     
         'name':'700852',
         'info':'701852',
         'index':852,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':110,   #力量
         'agi':110,   #智力 
         'cha':90,   #魅力 
         'lead':90,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'army852',   #模型
         
     },
     'hero853':{      #青龙     
         'name':'700853',
         'info':'701853',
         'index':853,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':115,   #力量
         'agi':85,   #智力 
         'cha':110,   #魅力 
         'lead':95,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'army853',   #模型
         
     },
     'hero854':{      #梼杌     
         'name':'700854',
         'info':'701854',
         'index':854,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':80,   #力量
         'agi':125,   #智力 
         'cha':95,   #魅力 
         'lead':110,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'army854',   #模型
         
     },
     'hero855':{      #帝江     
         'name':'700855',
         'info':'701855',
         'index':855,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':100,   #力量
         'agi':95,   #智力 
         'cha':110,   #魅力 
         'lead':110,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'army855',   #模型
         
     },
     'hero860':{      #真·玄武     
         'name':'700860',
         'info':'701860',
         'index':860,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':95,   #力量
         'agi':130,   #智力 
         'cha':80,   #魅力 
         'lead':125,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'army860',   #模型
         
     },
     'hero861':{      #真·朱雀     
         'name':'700861',
         'info':'701861',
         'index':861,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':110,   #力量
         'agi':115,   #智力 
         'cha':120,   #魅力 
         'lead':90,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'army861',   #模型
         
     },
     'hero862':{      #真·白虎     
         'name':'700862',
         'info':'701862',
         'index':862,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':140,   #力量
         'agi':95,   #智力 
         'cha':95,   #魅力 
         'lead':110,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'army862',   #模型
         
     },
     'hero863':{      #真·青龙     
         'name':'700863',
         'info':'701863',
         'index':863,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':80,   #力量
         'agi':120,   #智力 
         'cha':110,   #魅力 
         'lead':135,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'army863',   #模型
         
     },
     'hero864':{      #真·梼杌     
         'name':'700864',
         'info':'701864',
         'index':864,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':95,   #力量
         'agi':110,   #智力 
         'cha':140,   #魅力 
         'lead':105,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'army864',   #模型
         
     },
     'hero865':{      #真·帝江     
         'name':'700865',
         'info':'701865',
         'index':865,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':145,   #力量
         'agi':90,   #智力 
         'cha':105,   #魅力 
         'lead':115,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'army865',   #模型
         
     },
     'hero880':{      #兵     
         'name':'700880',
         'index':880,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero419',
         'str':75,   #力量
         'agi':60,   #智力 
         'cha':60,   #魅力 
         'lead':75,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'',   #模型
         
     },
     'hero881':{      #士     
         'name':'700881',
         'index':881,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero415',
         'str':95,   #力量
         'agi':70,   #智力 
         'cha':80,   #魅力 
         'lead':90,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'',   #模型
         
     },
     'hero882':{      #相     
         'name':'700882',
         'index':882,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero416',
         'str':70,   #力量
         'agi':95,   #智力 
         'cha':90,   #魅力 
         'lead':80,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'',   #模型
         
     },
     'hero883':{      #马     
         'name':'700883',
         'index':883,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero409',
         'str':90,   #力量
         'agi':80,   #智力 
         'cha':95,   #魅力 
         'lead':70,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'',   #模型
         
     },
     'hero884':{      #炮     
         'name':'700884',
         'index':884,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':1,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero403',
         'str':80,   #力量
         'agi':90,   #智力 
         'cha':70,   #魅力 
         'lead':95,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,2],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'',   #模型
         
     },
     'hero885':{      #车     
         'name':'700885',
         'index':885,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':0,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero410',
         'str':95,   #力量
         'agi':85,   #智力 
         'cha':95,   #魅力 
         'lead':85,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[1,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'',   #模型
         
     },
     'hero886':{      #将     
         'name':'700886',
         'index':886,  
         'state':-1,   #开服几天后显示   
         'sex':1,   #性别1男0女
         'type':2,    #0武将，1文官，2全才
         'rarity':0,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero411',
         'str':85,   #力量
         'agi':95,   #智力 
         'cha':85,   #魅力 
         'lead':95,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[0,3],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'',   #模型
         
     },
     'hero301':{      #水鬼     
         'name':'700301',
         'index':901,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':85,   #力量
         'agi':45,   #智力 
         'cha':110,   #魅力 
         'lead':80,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero301',   #模型
         
     },
     'hero302':{      #夜叉     
         'name':'700302',
         'index':902,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':110,   #力量
         'agi':70,   #智力 
         'cha':90,   #魅力 
         'lead':50,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero302',   #模型
         
     },
     'hero303':{      #罗刹     
         'name':'700303',
         'index':903,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':50,   #力量
         'agi':110,   #智力 
         'cha':70,   #魅力 
         'lead':90,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero303',   #模型
         
     },
     'hero304':{      #牛头     
         'name':'700304',
         'index':904,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':70,   #力量
         'agi':90,   #智力 
         'cha':50,   #魅力 
         'lead':110,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero304',   #模型
         
     },
     'hero305':{      #马面     
         'name':'700305',
         'index':905,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':100,   #力量
         'agi':70,   #智力 
         'cha':110,   #魅力 
         'lead':40,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero305',   #模型
         
     },
     'hero306':{      #日游神     
         'name':'700306',
         'index':906,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':120,   #力量
         'agi':66,   #智力 
         'cha':100,   #魅力 
         'lead':34,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero306',   #模型
         
     },
     'hero307':{      #夜游神     
         'name':'700307',
         'index':907,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':120,   #力量
         'agi':58,   #智力 
         'cha':42,   #魅力 
         'lead':100,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero307',   #模型
         
     },
     'hero308':{      #聚合怪     
         'name':'700308',
         'index':908,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':98,   #力量
         'agi':31,   #智力 
         'cha':95,   #魅力 
         'lead':96,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero308',   #模型
         
     },
     'hero309':{      #魏征     
         'name':'700309',
         'index':909,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':110,   #力量
         'agi':65,   #智力 
         'cha':90,   #魅力 
         'lead':75,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero309',   #模型
         
     },
     'hero310':{      #钟馗     
         'name':'700310',
         'index':910,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':75,   #力量
         'agi':110,   #智力 
         'cha':65,   #魅力 
         'lead':90,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero310',   #模型
         
     },
     'hero311':{      #陆之道     
         'name':'700311',
         'index':911,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':90,   #力量
         'agi':75,   #智力 
         'cha':110,   #魅力 
         'lead':65,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero311',   #模型
         
     },
     'hero312':{      #崔珏     
         'name':'700312',
         'index':912,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':65,   #力量
         'agi':90,   #智力 
         'cha':75,   #魅力 
         'lead':110,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero312',   #模型
         
     },
     'hero313':{      #孟婆     
         'name':'700313',
         'index':913,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':50,   #力量
         'agi':50,   #智力 
         'cha':50,   #魅力 
         'lead':50,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero313',   #模型
         
     },
     'hero314':{      #捐客     
         'name':'700314',
         'index':914,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':50,   #力量
         'agi':50,   #智力 
         'cha':50,   #魅力 
         'lead':50,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero314',   #模型
         
     },
     'hero315':{      #貔貅     
         'name':'700315',
         'index':915,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':80,   #力量
         'agi':130,   #智力 
         'cha':100,   #魅力 
         'lead':90,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero315',   #模型
         
     },
     'hero316':{      #麒麟     
         'name':'700316',
         'index':916,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':90,   #力量
         'agi':80,   #智力 
         'cha':130,   #魅力 
         'lead':100,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero316',   #模型
         
     },
     'hero317':{      #穷奇     
         'name':'700317',
         'index':917,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':130,   #力量
         'agi':100,   #智力 
         'cha':80,   #魅力 
         'lead':90,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero317',   #模型
         
     },
     'hero318':{      #饕餮     
         'name':'700318',
         'index':918,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':100,   #力量
         'agi':80,   #智力 
         'cha':90,   #魅力 
         'lead':130,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero318',   #模型
         
     },
     'hero319':{      #无睑监视者     
         'name':'700319',
         'index':919,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':65,   #力量
         'agi':120,   #智力 
         'cha':95,   #魅力 
         'lead':70,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero319',   #模型
         
     },
     'hero320':{      #佝偻罪人     
         'name':'700320',
         'index':920,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':55,   #力量
         'agi':95,   #智力 
         'cha':130,   #魅力 
         'lead':70,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero320',   #模型
         
     },
     'hero321':{      #人性之脓     
         'name':'700321',
         'index':921,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':130,   #力量
         'agi':45,   #智力 
         'cha':95,   #魅力 
         'lead':80,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero321',   #模型
         
     },
     'hero322':{      #背叛之灵     
         'name':'700322',
         'index':922,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':70,   #力量
         'agi':84,   #智力 
         'cha':66,   #魅力 
         'lead':130,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero322',   #模型
         
     },
     'hero323':{      #记忆聚合体     
         'name':'700323',
         'index':923,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':83,   #力量
         'agi':88,   #智力 
         'cha':89,   #魅力 
         'lead':90,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero323',   #模型
         
     },
     'hero324':{      #劫数将军     
         'name':'700324',
         'index':924,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':150,   #力量
         'agi':25,   #智力 
         'cha':55,   #魅力 
         'lead':120,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero324',   #模型
         
     },
     'hero325':{      #土狗     
         'name':'700325',
         'index':925,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':100,   #力量
         'agi':50,   #智力 
         'cha':95,   #魅力 
         'lead':75,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero325',   #模型
         
     },
     'hero326':{      #鬼魂武蓝     
         'name':'700326',
         'index':926,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':108,   #力量
         'agi':38,   #智力 
         'cha':99,   #魅力 
         'lead':75,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero326',   #模型
         
     },
     'hero327':{      #鬼魂武绿     
         'name':'700327',
         'index':927,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':110,   #力量
         'agi':72,   #智力 
         'cha':42,   #魅力 
         'lead':96,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero327',   #模型
         
     },
     'hero328':{      #鬼魂武紫     
         'name':'700328',
         'index':928,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':105,   #力量
         'agi':103,   #智力 
         'cha':77,   #魅力 
         'lead':35,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero328',   #模型
         
     },
     'hero329':{      #鬼魂文蓝     
         'name':'700329',
         'index':929,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':82,   #力量
         'agi':112,   #智力 
         'cha':44,   #魅力 
         'lead':82,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero329',   #模型
         
     },
     'hero330':{      #鬼魂文绿     
         'name':'700330',
         'index':930,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':45,   #力量
         'agi':108,   #智力 
         'cha':90,   #魅力 
         'lead':77,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero330',   #模型
         
     },
     'hero331':{      #鬼魂文紫     
         'name':'700331',
         'index':931,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':73,   #力量
         'agi':110,   #智力 
         'cha':49,   #魅力 
         'lead':88,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero331',   #模型
         
     },
     'hero332':{      #鬼魂老蓝     
         'name':'700332',
         'index':932,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':38,   #力量
         'agi':98,   #智力 
         'cha':130,   #魅力 
         'lead':54,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero332',   #模型
         
     },
     'hero333':{      #鬼魂老绿     
         'name':'700333',
         'index':933,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':33,   #力量
         'agi':130,   #智力 
         'cha':65,   #魅力 
         'lead':92,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero333',   #模型
         
     },
     'hero334':{      #鬼魂老紫     
         'name':'700334',
         'index':934,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':40,   #力量
         'agi':55,   #智力 
         'cha':95,   #魅力 
         'lead':130,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero334',   #模型
         
     },
     'hero335':{      #红灵1     
         'name':'700335',
         'index':935,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':81,   #力量
         'agi':118,   #智力 
         'cha':48,   #魅力 
         'lead':93,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero335',   #模型
         
     },
     'hero336':{      #红灵2     
         'name':'700336',
         'index':936,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':57,   #力量
         'agi':95,   #智力 
         'cha':123,   #魅力 
         'lead':65,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero336',   #模型
         
     },
     'hero337':{      #红灵3     
         'name':'700337',
         'index':937,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':134,   #力量
         'agi':72,   #智力 
         'cha':89,   #魅力 
         'lead':45,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero337',   #模型
         
     },
     'hero338':{      #游魂1     
         'name':'700338',
         'index':938,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':120,   #力量
         'agi':87,   #智力 
         'cha':48,   #魅力 
         'lead':85,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero338',   #模型
         
     },
     'hero339':{      #游魂2     
         'name':'700339',
         'index':939,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':93,   #力量
         'agi':54,   #智力 
         'cha':75,   #魅力 
         'lead':118,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero339',   #模型
         
     },
     'hero340':{      #游魂3     
         'name':'700340',
         'index':940,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':44,   #力量
         'agi':96,   #智力 
         'cha':125,   #魅力 
         'lead':75,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero340',   #模型
         
     },
     'hero350':{      #瑶姬     
         'name':'700350',
         'index':950,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':50,   #力量
         'agi':50,   #智力 
         'cha':50,   #魅力 
         'lead':50,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero350',   #模型
         
     },
     'hero351':{      #羿     
         'name':'700351',
         'index':951,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':50,   #力量
         'agi':50,   #智力 
         'cha':50,   #魅力 
         'lead':50,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero351',   #模型
         
     },
     'hero352':{      #帝夋     
         'name':'700352',
         'index':952,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':50,   #力量
         'agi':50,   #智力 
         'cha':50,   #魅力 
         'lead':50,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero352',   #模型
         
     },
     'hero353':{      #毕方     
         'name':'700353',
         'index':953,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':50,   #力量
         'agi':50,   #智力 
         'cha':50,   #魅力 
         'lead':50,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero353',   #模型
         
     },
     'hero354':{      #玉兔     
         'name':'700354',
         'index':954,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':50,   #力量
         'agi':50,   #智力 
         'cha':50,   #魅力 
         'lead':50,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero354',   #模型
         
     },
     'hero355':{      #九凤     
         'name':'700355',
         'index':955,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':50,   #力量
         'agi':50,   #智力 
         'cha':50,   #魅力 
         'lead':50,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero355',   #模型
         
     },
     'hero356':{      #谛听     
         'name':'700356',
         'index':956,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':50,   #力量
         'agi':50,   #智力 
         'cha':50,   #魅力 
         'lead':50,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero356',   #模型
         
     },
     'hero357':{      #西王母     
         'name':'700357',
         'index':957,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':50,   #力量
         'agi':50,   #智力 
         'cha':50,   #魅力 
         'lead':50,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero357',   #模型
         
     },
     'hero358':{      #硬核战地诗人     
         'name':'700358',
         'index':958,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':50,   #力量
         'agi':50,   #智力 
         'cha':50,   #魅力 
         'lead':50,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero358',   #模型
         
     },
     'hero359':{      #斑寅将军     
         'name':'700359',
         'index':959,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':152,   #力量
         'agi':97,   #智力 
         'cha':110,   #魅力 
         'lead':91,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero359',   #模型
         
     },
     'hero360':{      #参天铸木     
         'name':'700360',
         'index':960,  
         'state':-1,   #开服几天后显示   
         'talent': 'hero359',   #借用天赋
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':119,   #力量
         'agi':148,   #智力 
         'cha':89,   #魅力 
         'lead':94,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero360',   #模型
         
     },
     'hero361':{      #巡海夜叉     
         'name':'700361',
         'index':961,  
         'state':-1,   #开服几天后显示   
         'talent': 'hero359',   #借用天赋
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':112,   #力量
         'agi':88,   #智力 
         'cha':155,   #魅力 
         'lead':95,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero361',   #模型
         
     },
     'hero362':{      #癫火钩星     
         'name':'700362',
         'index':962,  
         'state':-1,   #开服几天后显示   
         'talent': 'hero359',   #借用天赋
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':91,   #力量
         'agi':110,   #智力 
         'cha':98,   #魅力 
         'lead':151,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero362',   #模型
         
     },
     'hero363':{      #结晶子路     
         'name':'700363',
         'index':963,  
         'state':-1,   #开服几天后显示   
         'talent': 'hero359',   #借用天赋
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':113,   #力量
         'agi':112,   #智力 
         'cha':111,   #魅力 
         'lead':114,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero363',   #模型
         
     },
     'hero364':{      #上饶金蟾     
         'name':'700364',
         'index':964,  
         'state':-1,   #开服几天后显示   
         'talent': 'hero359',   #借用天赋
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':157,   #力量
         'agi':125,   #智力 
         'cha':78,   #魅力 
         'lead':90,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero364',   #模型
         
     },
     'hero365':{      #杀意盎然     
         'name':'700365',
         'index':965,  
         'state':-1,   #开服几天后显示   
         'talent': 'hero359',   #借用天赋
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero854',
         'str':100,   #力量
         'agi':171,   #智力 
         'cha':82,   #魅力 
         'lead':97,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero365',   #模型
         
     },
     'hero366':{      #浪涌大蟹     
         'name':'700366',
         'index':966,  
         'state':-1,   #开服几天后显示   
         'talent': 'hero359',   #借用天赋
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':108,   #力量
         'agi':88,   #智力 
         'cha':160,   #魅力 
         'lead':94,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero366',   #模型
         
     },
     'hero367':{      #无头善歌     
         'name':'700367',
         'index':967,  
         'state':-1,   #开服几天后显示   
         'talent': 'hero359',   #借用天赋
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'icon':'hero855',
         'str':83,   #力量
         'agi':114,   #智力 
         'cha':98,   #魅力 
         'lead':155,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero367',   #模型
         
     },
     'hero368':{      #撼地魔猿     
         'name':'700368',
         'index':968,  
         'state':-1,   #开服几天后显示   
         'talent': 'hero359',   #借用天赋
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':113,   #力量
         'agi':115,   #智力 
         'cha':112,   #魅力 
         'lead':110,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero368',   #模型
         
     },
     'hero409':{      #四维牌喽啰1     
         'name':'700409',
         'index':1009,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':100,   #力量
         'agi':75,   #智力 
         'cha':60,   #魅力 
         'lead':85,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero409',   #模型
         
     },
     'hero410':{      #四维牌喽啰2     
         'name':'700410',
         'index':1010,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':88,   #力量
         'agi':68,   #智力 
         'cha':84,   #魅力 
         'lead':80,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero410',   #模型
         
     },
     'hero415':{      #四维牌喽啰3     
         'name':'700415',
         'index':1015,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':65,   #力量
         'agi':75,   #智力 
         'cha':85,   #魅力 
         'lead':95,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero415',   #模型
         
     },
     'hero416':{      #四维牌喽啰4     
         'name':'700416',
         'index':1016,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':55,   #力量
         'agi':100,   #智力 
         'cha':70,   #魅力 
         'lead':95,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero416',   #模型
         
     },
     'hero417':{      #四维牌喽啰5     
         'name':'700417',
         'index':1017,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':100,   #力量
         'agi':25,   #智力 
         'cha':85,   #魅力 
         'lead':110,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero417',   #模型
         
     },
     'hero421':{      #孙鲁班     
         'name':'700421',
         'index':1018,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':50,   #力量
         'agi':50,   #智力 
         'cha':50,   #魅力 
         'lead':60,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_04',   #模型
         
     },
     'hero422':{      #孙鲁育     
         'name':'700422',
         'index':1019,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':50,   #力量
         'agi':50,   #智力 
         'cha':50,   #魅力 
         'lead':60,   #统帅
         'pol':90,   #政治，开发测试用，之后移除
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero_05',   #模型
         
     },
     'hero423':{      #巨兽     
         'name':'700423',
         'index':1020,  
         'state':-1,   #开服几天后显示   
         'sex':-1,   #性别1男0女
         'type':-1,    #0武将，1文官，2全才
         'rarity':-1,  #0良才，1名将，2国士，3巾帼，4传奇
         'str':200,   #力量
         'agi':50,   #智力 
         'cha':50,   #魅力 
         'lead':50,   #统帅
         'army':[-1,-1],   #兵种，[前军兵种，后军兵种]0步兵，1骑兵，2弓兵，3方士
         
         'res':'hero423',   #模型
         
     },
         
     
}