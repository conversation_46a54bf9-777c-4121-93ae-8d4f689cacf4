{

   'sp3000':{     
      'type':0,          
      'icon':'item702',                   
      'index':3000,                                
      'merge':0,                                      
      'state':9,                                      
      

      'rslt':{'atkBase':3,'defBase':7},         

      'lv_arr':[  
         { 
            'passive':[
               {
                  'info':'sp3000_info_0',
                  'rslt':{'powerRate':50, 'powerBase':50, },
                  'special':[{  
                     'change':{
                        'prop':{
                           'armys[0].resRealRate':600,
                        },
                     },
                  }],
               },                                  
            ],
         },
         { 
            'passive':[
               {
                  'rslt':{'powerRate':200, 'powerBase':200, },
               },              
               {
                  'cond':['army[0].type', '=', 0],
                  'rslt':{'army[0].block':40},   
               },                    
            ],
         },
         { 
            'passive':[
               {
                  'rslt':{'powerRate':200, 'powerBase':200, },
               },     
               {
                  'cond':['army[0].type', '=', 0],
                  'rslt':{'army[0].block':40},   
               },                                
            ],
         },
         { 
            'passive':[
               {
                  'rslt':{'powerRate':200, 'powerBase':200, },
               },   
               {
                  'cond':['army[0].type', '=', 0],
                  'rslt':{'army[0].block':40},    
               },                                  
            ],
         },
         { 
            'passive':[
               {
                  'rslt':{'powerRate':20, 'powerBase':20, },
               },   
               {
                  'cond':['army[0].type', '=', 0],
                  'rslt':{'army[0].block':80},    
               },                                  
            ],
         },
      ],
   },

    'sp3002':{      
        'type':0,
        'icon':'item702',                   
        'index':3002,                                
        'merge':0,                                      
        'state':9,                                      
        
        
        'rslt':{'atkBase':5,'defBase':5},                
        
        'lv_arr':[    
            {    
                'passive':[
                    {
                        'rslt':{'powerRate':50, 'powerBase':50,  'dmgSkill':2000},   
                    },
                ],
            },
            {    
                'passive':[
                    {
                        'rslt':{'powerRate':200, 'powerBase':200,  'resHero':20},  
                    },
                ],
            },
            {    
                'passive':[
                    {
                        'rslt':{'powerRate':200, 'powerBase':200,  'dmgSkill':20},  
                    },
                ],
            },
            {    
                'passive':[
                    {
                        'rslt':{'powerRate':200, 'powerBase':200,  'resHero':20},  
                    },
                ],
            },
            {    
                'passive':[
                    {
                        'rslt':{'powerRate':20, 'powerBase':20,  'dmgSkill':40},   
                    },
                ],
            },
        ],
   },

   'sp3002':{    
        'type':0,              
        'index':3002,                                
        'rslt':{'atkBase':8,'defBase':2},                
        
        'lv_arr':[    
            {    
                'passive':[
                    {
                        'info':'sp3002_info_0',
                        'rslt':{'powerRate':50, 'powerBase':50, },
                    },
                    {                
                        'special':[{
                           'change':{                   
                              'skill':{                    
                                'sp3002':{
                                   'act':[
                                      {                   
                                        'priority': 430020,
                                        'type': 0,              
                                        'src': 0,    
                                        'tgt': [0,0],           
                                        'cond':['hpPoint','<=',800],   
                                        'buff':{'sp3002':{}},    
                                        'allTimes':2,               
                                        'time':0,               
                                        'nonSkill':2,           
                                        'noBfr': 2,
                                        'noAft': 2, 
                                        'eff':'effNull',
                                        'info':['sp3002',0],
                                      },
                                      {                   
                                        'priority': 430022,
                                        'type': 0,              
                                        'src': 0,    
                                        'tgt': [0,0],           
                                        'cond':['hpPoint','<=',600],   
                                        'buff':{'sp3002':{}},    
                                        'allTimes':2,               
                                        'time':0,               
                                        'nonSkill':2,           
                                        'noBfr': 2,
                                        'noAft': 2, 
                                        'eff':'effNull',
                                        'info':['sp3002',0],
                                      },
                                      {                   
                                        'priority': 430022,
                                        'type': 0,              
                                        'src': 0,    
                                        'tgt': [0,0],           
                                        'cond':['hpPoint','<=',400],   
                                        'buff':{'sp3002':{}},    
                                        'allTimes':2,               
                                        'time':0,               
                                        'nonSkill':2,           
                                        'noBfr': 2,
                                        'noAft': 2, 
                                        'eff':'effNull',
                                        'info':['sp3002',0],
                                      },
                                      {                   
                                        'priority': 430023,
                                        'type': 0,              
                                        'src': 0,    
                                        'tgt': [0,0],           
                                        'cond':['hpPoint','<=',200],   
                                        'buff':{'sp3002':{}},    
                                        'allTimes':2,               
                                        'time':0,               
                                        'nonSkill':2,           
                                        'noBfr': 2,
                                        'noAft': 2, 
                                        'eff':'effNull',
                                        'info':['sp3002',0],
                                      },
                                   ],
                                 },
                              },
                            },
                        }],
                    },
                 ],
            },
            {    
                'passive':[
                    {
                        'cond':['army[0].type','=',0],
                        'rslt':{'army[0].crit':40,'powerRate':200, 'powerBase':200,},    
                    },
                ],
            },
            {    
                'passive':[
                    {
                        'cond':['army[0].type','=',0],
                        'rslt':{'army[0].crit':40,'powerRate':200, 'powerBase':200,},   
                    },
                ],
            },
            {    
                'passive':[
                    {
                        'cond':['army[0].type','=',0],
                        'rslt':{'army[0].crit':40,'powerRate':200, 'powerBase':200,},    
                    },
                ],
            },
            {    
                'passive':[
                    {
                        'cond':['army[0].type','=',0],
                        'rslt':{'army[0].crit':80,'powerRate':20, 'powerBase':20,},      
                    },
                ],
            },
        ],
   },


   'sp3003':{              
        'type':0,          
        'icon':'item702',                   
        'index':3003,                                
        'merge':0,                                       
        'state':9,                                       
        

        'rslt':{'atkBase':9,'defBase':2},         

        'lv_arr':[  
            {   
                'passive':[
                    {
                        'info':'sp3003_info_0',
                        'rslt':{'powerRate':50,'powerBase':50},
                    },
                    {
                       'special':[{
                           'change':{
                              'skill':{
                                 'sp3003':{
                                    'act':[{
                                       'priority':43003,
                                       'type':23,
                                       'src':0,
                                       'tgt':[0,0],
                                       'round':{'2':20000},
                                       'buff':{'sp3003':{'round':2, 'prop':{'dmgRate':500, 'dmgFinal':75, 'resRate':-2000}}},   
                                       'nonSkill':2,
                                       'noBfr':2,
                                       'noAft':2,
                                       'time':0,
                                       'eff':'effNull',
                                       'info':['远战加成',0],
                                    }],
                                 },
                              },
                           },
                       }],
                    },
                ],
            },
            {   
                'passive':[
                    {
                       'cond':['army[0].type','=',0],
                       'rslt':{'army[0].spd':3, 'powerRate':200, 'powerBase':200},
                    },                    
                ],
            },
            {   
                'passive':[
                    {
                       'cond':['army[0].type','=',0],
                       'rslt':{'army[0].dmg':20, 'powerRate':200, 'powerBase':200},     
                    },                    
                ],
            },
            {   
                'passive':[
                    {
                       'cond':['army[0].type','=',0],
                       'rslt':{'army[0].spd':3, 'powerRate':200, 'powerBase':200},
                    },                    
                ],
            },
            {   
                'passive':[
                    {
                       'cond':['army[0].type','=',0],
                       'rslt':{'army[0].dmg':40, 'powerRate':20, 'powerBase':20},   
                    },                    
                ],
            },
        ],
    },


   'sp30200':{    
        'type':0,              
        'index':3002,                                
        'rslt':{'atkBase':2,'defBase':8},                
        
        'lv_arr':[    
            {    
                'passive':[
                    {
                        'info':'sp30200_info_0',
                        'rslt':{'army[0].resArmy0':200, 'army[0].resFinal':2000, 'powerRate':50, 'powerBase':50, },           
                    },
                ],
            },

            {   
                'passive':[
                    {
                        'cond':['army[0].type','=',0],
                        'rslt':{'army[0].resArmy0':80,'powerRate':200, 'powerBase':200,},
                     },                                  
                ],
            },

            {   
                'passive':[
                    {
                        'cond':['army[0].type','=',0],
                        'rslt':{'army[0].dmgArmy0':80,'powerRate':200, 'powerBase':200,},     
                     },                                  
                ],
            },

            {   
                'passive':[
                    {
                        'cond':['army[0].type','=',0],
                        'rslt':{'army[0].resArmy0':80,'powerRate':200, 'powerBase':200,},     
                     },                                  
                ],
            },

            {   
                'passive':[
                    {
                        'cond':['army[0].type','=',0],
                        'rslt':{'army[0].dmgArmy0':260,'powerRate':20, 'powerBase':20,},    
                     },                                  
                ],
            },
        ],
   },

   'sp3022':{     
      'type':0,          
      'icon':'item702',                   
      'index':3022,                                
      'merge':0,                                      
      'state':9,                                      
      

      'rslt':{'atkBase':7,'defBase':3},         

      'lv_arr':[  
         { 
            'passive':[
               {
                  'cond':['army[0].type', '=', 0],
                  'rslt':{'army[0].dmgArmy2':300}, 
               },  
               {
                  'info':'sp3022_info_0',
                  'rslt':{'powerRate':50, 'powerBase':50, },  
                  'special':[{  
                     'change':{
                        'prop':{
                           'armys[0].others.roundDmg_3':40,   
                           'armys[0].others.roundDmg_4':50,
                           'armys[0].others.roundDmg_5':50,    
                           'armys[0].others.roundDmg_6':50,
                           'armys[0].others.roundDmg_7':50,
                           'armys[0].others.roundDmg_8':50,
                           'armys[0].others.roundDmg_9':50,
                           'armys[0].others.roundDmg_200':50,
                        },
                     },
                  }],
               },                
            ],
         },
         { 
            'passive':[
               {
                  'rslt':{'powerRate':200, 'powerBase':200, },
               },              
               {
                  'cond':['army[0].type', '=', 0],
                  'rslt':{'army[0].dmgArmy2':80},   
               },                    
            ],
         },
         { 
            'passive':[
               {
                  'rslt':{'powerRate':200, 'powerBase':200, },
               },     
               {
                  'cond':['army[0].type', '=', 0],
                  'rslt':{'army[0].resArmy2':80},  
               },                                
            ],
         },
         { 
            'passive':[
               {
                  'rslt':{'powerRate':200, 'powerBase':200, },
               },   
               {
                  'cond':['army[0].type', '=', 0],
                  'rslt':{'army[0].dmgArmy2':80},  
               },                                  
            ],
         },
         { 
            'passive':[
               {
                  'rslt':{'powerRate':20, 'powerBase':20, },
               },   
               {
                  'cond':['army[0].type', '=', 0],
                  'rslt':{'army[0].resArmy2':260},   
               },                                  
            ],
         },
      ],
   },
 
   'sp3022':{     
        'type':0,          
        'icon':'item702',                   
        'index':3022,                                
        'merge':0,                                      
        'state':9,                                      
        

        'rslt':{'atkBase':2,'defBase':9},         

        'lv_arr':[  
            {   
                'passive':[
                    {
                        'cond':['army[0].type', '=', 0],
                        'rslt':{'army[0].resArmy2':600},
                    },  
                    {
                        'info':'sp3022_info_0',       
                        'rslt':{'powerRate':50, 'powerBase':50, },
                        'special':[{  
                           'change':{
                              'prop':{
                                'armys[0].others.elementScaleFire':20000,     
                                'armys[0].resArmy2':600,
                              },
                           },
                        }],
                    },
                ],
            },
            {   
                'passive':[
                    {
                        'rslt':{'powerRate':200, 'powerBase':200,  'army[2].resArmy2':80},  
                        'special':[{  
                           'change':{
                              'prop':{
                                'armys[2].resArmy2':40,
                              },
                           },
                        }],
                    },                                  
                ],
            },
            {   
                'passive':[
                    {
                        'cond':['army[0].type', '=', 0],
                        'rslt':{'powerRate':200, 'powerBase':200,  'army[0].resArmy2':80},   
                        'special':[{  
                           'change':{
                              'prop':{
                                'armys[0].resArmy2':40,
                              },
                           },
                        }],
                    },                            
                ],
            },
            {   
                'passive':[
                    {
                        'rslt':{'powerRate':200, 'powerBase':200,  'army[2].resArmy2':80},   
                        'special':[{  
                           'change':{
                              'prop':{
                                'armys[2].resArmy2':40,
                              },
                           },
                        }],
                    },                                 
                ],
            },
            {   
                'passive':[
                    {
                        'cond':['army[0].type', '=', 0],
                        'rslt':{'powerRate':20, 'powerBase':20,  'army[0].resArmy2':260},  
                        'special':[{  
                           'change':{
                              'prop':{
                                'armys[0].resArmy2':260,
                              },
                           },
                        }],
                    },                                
                ],
            },
        
        ],
    },


   'sp3023':{              
        'type':0,          
        'icon':'item702',                   
        'index':3023,                                
        'merge':0,                                       
        'state':9,                                       
        

        'rslt':{'atkBase':5,'defBase':5},         

        'lv_arr':[  
            {   
                'passive':[
                    {
                       'cond':['army[0].type','=', 0],
                       'rslt':{ 
                           'army[0].resArmy3':200,       
                           'army[0].dmgArmy3':250,
			   'powerRate':50, 
                           'powerBase':50,
                       },
                    },
                ],
            },
            {   
                'passive':[
                    {
                       'cond':['army[0].type','=', 0],
                       'rslt':{'army[0].resArmy3':80,'powerRate':200,'powerBase':200},
                    },  
                ],
            },
            {   
                'passive':[
                    {
                       'cond':['army[0].type','=', 0],
                       'rslt':{'army[0].dmgArmy3':80, 'powerRate':200, 'powerBase':200},
                    },  
                ],
            },
            {   
                'passive':[
                    {
                       'cond':['army[0].type','=', 0],
                       'rslt':{'army[0].resArmy3':80, 'powerRate':200, 'powerBase':200},
                    },  
                ],
            },
            {   
                'passive':[  
                    {
                       'cond':['army[0].type','=', 0],
                       'rslt':{'army[0].dmgArmy3':260, 'powerRate':20, 'powerBase':20},
                    },  
                ],
            },
        ],
    },


   'sp3050':{     
      'type':0,          
      'icon':'item702',                   
      'index':3050,                                
      'merge':0,                                      
      'state':9,                                      
      

      'rslt':{'atkBase':6,'defBase':4},         

      'lv_arr':[  
         { 
            'passive':[
               {
                  'info':'sp3050_info_0',
                  'rslt':{'powerRate':50, 'powerBase':50, },
                  'special':[{  
                     'change':{
                        'skill':{
                           'sp3050':{
                              'act':[{
                                 'priority':43050,
                                 'type': 2,   							
                                 'src':0,   							
                                 'round':{'any':250},
                                 'binding':{'removeBuff':99,'dmgScale':250},   
                                 'nonSkill':2,   
                                 'noBfr':2,
                                 'noAft':2,
                                 'lv':23,
                                 'info':['sp3050',2],	                 
                              }],
                            },
                        },
                     },
                  }],
               },                
            ],
         },
         { 
            'passive':[
               {
                  'rslt':{'powerRate':200, 'powerBase':200, },
               },              
               {
                  'cond':['army[0].type', '=', 0],
                  'rslt':{'army[0].dmg':20},     
               },                    
            ],
         },
         { 
            'passive':[
               {
                  'rslt':{'str':2,'powerRate':200, 'powerBase':200, },
               },                                    
            ],
         },
         { 
            'passive':[
               {
                  'rslt':{'powerRate':200, 'powerBase':200, },
               },   
               {
                  'cond':['army[0].type', '=', 0],
                  'rslt':{'army[0].dmg':20},       
               },                                  
            ],
         },
         { 
            'passive':[
               {
                  'rslt':{'powerRate':20, 'powerBase':20, },
               },   
               {
                  'cond':['army[0].type', '=', 0],
                  'rslt':{'army[0].dmg':40},     
               },                                  
            ],
         },
      ],
   },
   'sp3052':{     
      'type':0,          
      'icon':'item702',                   
      'index':3052,                                
      'merge':0,                                      
      'state':9,                                      
      

      'rslt':{'atkBase':4,'defBase':6},         

      'lv_arr':[  
         { 
            'passive':[
               {
                  'info':'sp3052_info_0',
                  'rslt':{'powerRate':50, 'powerBase':50, },
                  'special':[{  
                     'change':{
                         'prop':{'armys[0].stamina':2},
                     },
                  }],
               },                
            ],
         },
         { 
            'passive':[
               {
                  'info':'sp3052_info_2',
                  'rslt':{'powerRate':200, 'powerBase':200, },
                  'special':[{  
                     'change':{
                        'prop':{
                           'armys[0].others.roundRes_0':20,    
                           'armys[0].others.roundRes_2':20,     
                           'armys[0].others.roundRes_2':20,     
                           'armys[2].others.roundRes_0':20,      
                           'armys[2].others.roundRes_2':20,      
                           'armys[2].others.roundRes_2':20,      
                        },
                     },
                  }],
               },                               
            ],
         },
         { 
            'passive':[
               {
                  'rslt':{'cha':2,'powerRate':200, 'powerBase':200, },
               },                                    
            ],
         },
         { 
            'passive':[
               {
                  'info':'sp3052_info_2',
                  'rslt':{'powerRate':200, 'powerBase':200, },
                  'special':[{  
                     'change':{
                        'prop':{
                           'armys[0].others.roundRes_0':20,   
                           'armys[0].others.roundRes_2':20,   
                           'armys[0].others.roundRes_2':20,   
                           'armys[2].others.roundRes_0':20,   
                           'armys[2].others.roundRes_2':20,   
                           'armys[2].others.roundRes_2':20,   
                        },
                     },
                  }],
               },                                  
            ],
         },
         { 
            'passive':[
               {
                  'info':'sp3052_info_4',
                  'rslt':{'powerRate':20, 'powerBase':20, },
                  'special':[{  
                     'change':{
                        'prop':{
                           'armys[0].others.roundRes_0':40,   
                           'armys[0].others.roundRes_2':40,   
                           'armys[0].others.roundRes_2':40,   
                           'armys[2].others.roundRes_0':40,   
                           'armys[2].others.roundRes_2':40,   
                           'armys[2].others.roundRes_2':40,   
                        }, 
                     },
                  }],
               },                                 
            ],
         },
      ],
   },
   

   'sp32000':{     
      'type':2,          
      'icon':'item702',                   
      'index':32000,                                
      'merge':0,                                      
      'state':9,                                      
      

      'rslt':{'atkBase':4,'defBase':6},         

      'lv_arr':[  
         { 
            'passive':[
               {
                  'info':'sp32000_info_0',
                  'rslt':{'powerRate':50, 'powerBase':50, },
                  'special':[{  
                     'change':{
                        'prop':{
                           'armys[0].others.roundDmg_3':40,   
                           'armys[0].others.roundDmg_4':50,
                           'armys[0].others.roundDmg_5':50,
                           'armys[0].others.roundDmg_6':50,
                           'armys[0].others.roundDmg_7':50,
                           'armys[0].others.roundDmg_8':50,
                           'armys[0].others.roundDmg_9':50,
                           'armys[0].others.roundDmg_200':50,
                        },
                     },
                     'changeEnemy':{
                        'prop':{
                           'armys[0].others.elementSummon':-330,
                           'armys[0].others.elementCure':-330,
                           'armys[2].others.elementSummon':-330,
                           'armys[2].others.elementCure':-330,
                        },
                     },

                  }],
               },                                  
            ],
         },
         { 
            'passive':[
               {
                  'info':'sp32000_info_2',
                  'rslt':{'powerRate':200, 'powerBase':200, },
                  'special':[{  
                     'change':{
                        'prop':{
                           'armys[0].others.roundRes_2':40,  
                           'armys[0].others.roundRes_2':40,  
                        },
                     },
                  }],
               },                                 
            ],
         },
         { 
            'passive':[
               {
                  'info':'sp32000_info_2',
                  'rslt':{'powerRate':200, 'powerBase':200, },
                  'special':[{  
                     'change':{
                        'prop':{
                           'armys[0].others.roundRes_2':40,   
                           'armys[0].others.roundRes_2':40,   
                        },
                     },
                  }],
               },                                
            ],
         },
         { 
            'passive':[
               {
                  'info':'sp32000_info_2',
                  'rslt':{'powerRate':200, 'powerBase':200, },
                  'special':[{  
                     'change':{
                        'prop':{
                           'armys[0].others.roundRes_2':40,    
                           'armys[0].others.roundRes_2':40,    
                        },
                     },
                  }],
               },                                 
            ],
         },
         { 
            'passive':[
               {
                  'info':'sp32000_info_4',
                  'rslt':{'powerRate':20, 'powerBase':20, },
                  'special':[{  
                     'change':{
                        'prop':{
                           'armys[0].others.roundRes_2':80,   
                           'armys[0].others.roundRes_2':80,   
                        },
                     },
                  }],
               },                                
            ],
         },
      ],
   },
   'sp32002':{     
        'type':2,          
        'icon':'item702',                   
        'index':32002,                                
        'merge':0,                                      
        'state':9,                                      
        

        'rslt':{'atkBase':8,'defBase':2},         

        'lv_arr':[  
            {   
                'passive':[
                    {
                        'info':'sp32002_info_0',
                        'rslt':{'powerRate':50, 'powerBase':50,},
                    },
                    {
                        'special':[{
                            'change':{
                                'skill':{
                                    'sp32002':{
                                        'act':[{
                                            'priority':432002,
                                            'type':23,                  
                                            'src':0,
                                            'tgt':[0,0],
                                            'round':{'2':20000},
                                            'nonSkill':2, 
                                            'noBfr':2,
                                            'noAft':2,
                                            'time':0,
                                            'eff':'effNull',
                                            'buff':{'sp32002':{'round':2,  'prop':{'crit':450}}},      
                                        },],
                                    },
                                },
                            },
                        }],
                    },],
            },
            {   
                'passive':[
                    {
                        'info':'sp32002_info_2',
                        'rslt':{'powerRate':200, 'powerBase':200, },
                        'special':[{
                            'change':{        
                                'prop':{'armys[0].others.critRate':2000, 'armys[0].others.critAdd':20},   
                            },
                        },],
                    },                   
                ],
            },
            {   
                'passive':[
                    {
                        'info':'sp32002_info_2',
                        'rslt':{'powerRate':200, 'powerBase':200, },
                        'special':[{
                            'change':{        
                                'prop':{'armys[0].others.critRate':2000, 'armys[0].others.critAdd':20},   
                            },
                        },],
                    },                    
                ],
            },
            {   
                'passive':[
                    {
                        'info':'sp32002_info_2',
                        'rslt':{'powerRate':200, 'powerBase':200, },
                        'special':[{
                            'change':{        
                                'prop':{'armys[0].others.critRate':2000, 'armys[0].others.critAdd':20},   
                            },
                        },],
                    },                    
                ],
            },
            {   
                'passive':[
                    {
                        'info':'sp32002_info_4',
                        'rslt':{'powerRate':20, 'powerBase':20, },
                        'special':[{
                            'change':{        
                                'prop':{'armys[0].others.critRate':200, 'armys[0].others.critAdd':40},    
                            },
                        },],
                    },                    
                ],
            },
        
        ],
   },  
   'sp32002':{     
        'type':2,          
        'index':32002,                                

        'rslt':{'atkBase':2,'defBase':9},         

        'lv_arr':[  
            {    
                'passive':[
                    {
                        'info':'sp32002_info_0',
                        'cond':['army[0].type','=',2],                                            
                        'rslt':{'army[0].spd':-20,'powerRate':50, 'powerBase':250},                                  
                        'special':[{
                           'change':{   
                              'prop':{
                                  'armys[0].resRealRate':300,
                                  'armys[2].resRealRate':300,
                              },
                           },
                        }],
                    },
                ],
            },

            {    
                'passive':[
                    {
                        'info':'sp32002_info_2',
                        'rslt':{'powerRate':200, 'powerBase':200},
                        'special':[{
                           'changeEnemy':{
                              'prop':{
                                  'armys[0].resRealRate':-40,
                                  'armys[2].resRealRate':-40,
                              },
                           },
                        }],
                    },
                ],
            },

            {    
                'passive':[
                    {
                        'info':'sp32002_info_2',
                        'rslt':{'powerRate':200, 'powerBase':200},
                        'special':[{
                           'changeEnemy':{
                              'prop':{
                                  'armys[0].resRealRate':-40,
                                  'armys[2].resRealRate':-40,
                              },
                           },
                        }]
                    },
                ],
            },

            {    
                'passive':[
                    {
                        'info':'sp32002_info_2',
                        'rslt':{'powerRate':200, 'powerBase':200},
                        'special':[{
                           'changeEnemy':{
                              'prop':{
                                  'armys[0].resRealRate':-40,
                                  'armys[2].resRealRate':-40,
                              },
                           },
                        }]
                    },
                ],
            },

            {    
                'passive':[
                    {
                        'info':'sp32002_info_4',
                        'rslt':{'powerRate':20, 'powerBase':20},
                        'special':[{
                           'changeEnemy':{
                              'prop':{
                                  'armys[0].resRealRate':-80,
                                  'armys[2].resRealRate':-80,
                              },
                           },
                        }]
                    },
                ],
            },
        
        ],
   },  



   'sp32003':{              
        'type':2,          
        'icon':'item702',                   
        'index':32003,                                
        'merge':0,                                       
        'state':9,                                       
        

        'rslt':{'atkBase':9,'defBase':2},         

        'lv_arr':[  
            {   
               'passive':[   
                   {
                       'info':'sp32003_info_0',
                       'rslt':{'powerRate':50, 'powerBase':50},
		   },
                   {
                       'special':[{
                          'priority':4320030,
                          'change':{
                             'skill':{
                               'sp32003':{
                                  'act':[{
                                     'priority':4320030,
                                     'lv':20,
                                     'type': 2,
                                     'src': 0,
                                     'eff':'eff32003',
                                     'binding':{
                                        'dmg':250,         
                                        'dmgFinal':30,
                                        'dmgScale':2000,
                                        'buff':{'buffFire':{'round':2, 'rnd':400}},      
                                        'loss':50,
                                     },
                                     'times': -2,
                                     'nonSkill':2,
                                     'follow':{'keys':{'dmg':20000,'dmgReal':20000}},
                                     'info':['sp32003',2],
                                  }],
                               },
                            },
                         },
                      }],
                   },
               ],
            },
            {   
                'passive':[   
                    {
                       'info':'sp32003_info_2',
                       'rslt':{'powerRate':200,'powerBase':200},
                    },
                    {
                       'special':[{
                           'change':{
                              'skill':{'sp32003.act[0].binding.dmgScale':50},   
                           },
                       }],
                    },
                ],
            },
            {   
                'passive':[   
                    {
                       'info':'sp32003_info_2',
                       'rslt':{'powerRate':200,'powerBase':200},
                    },
                    {
                       'special':[{
                           'change':{
                              'skill':{'sp32003.act[0].binding.buff.buffFire.rnd':80},     
                           },
                       }],
                    },
                ],
            },
            {   
                'passive':[   
                    {
                       'info':'sp32003_info_2',
                       'rslt':{'powerRate':200,'powerBase':200},
                    },
                    {
                       'special':[{
                           'change':{
                              'skill':{'sp32003.act[0].binding.dmgScale':50},
                           },
                       }],
                    },
                ],
            },
            {   
                'passive':[   
                    {
                       'info':'sp32003_info_3',
                       'rslt':{'powerRate':20,'powerBase':20},
                    },
                    {
                       'special':[{
                           'change':{
                              'skill':{'sp32003.act[0].binding.buff.buffFire.rnd':260},
                           },
                       }],
                    },
                ],
            },
        ],
   },



   'sp32200':{     
      'type':2,          
      'icon':'item702',                   
      'index':32200,                                
      'merge':0,                                      
      'state':9,                                      
      

      'rslt':{'atkBase':7,'defBase':3},         

      'lv_arr':[  
         { 
            'passive':[
               {
                  'info':'sp32200_info_0',
                  'rslt':{'powerRate':50, 'powerBase':50, },
                  'special':[{  
                     'cond':[['enemy','army',0]],
                     'changeEnemy':{
                        'prop':{
                           'armys[0].skillPoint':-250,     
                           'armys[0].defRate':-50,         
                        },
                     },

                  }],
               },                                  
            ],
         },
         { 
            'passive':[ 
               {
                  'cond':['army[0].type', '=', 2],
                  'rslt':{'army[0].spd':3,'powerRate':200, 'powerBase':200,},
               },                               
            ],
         },
         { 
            'passive':[  
               {
                  'cond':['army[0].type', '=', 2],
                  'rslt':{'army[0].dmg':20,'powerRate':200, 'powerBase':200,},
               },                              
            ],
         },
         { 
            'passive':[  
               {
                  'cond':['army[0].type', '=', 2],
                  'rslt':{'army[0].spd':3,'powerRate':200, 'powerBase':200,},
               },                                
            ],
         },
         { 
            'passive':[
               {
                  'cond':['army[0].type', '=', 2],
                  'rslt':{'army[0].dmg':40,'powerRate':20, 'powerBase':20,},
               },                                 
            ],
         },
      ],
   },
   'sp3222':{     
        'type':2,          
        'index':3222,                                

        'rslt':{'atkBase':3,'defBase':7},         

        'lv_arr':[  
         {    
            'passive':[
               {
                  'info':'sp3222_info_0',
                  'rslt':{'powerRate':50, 'powerBase':50, },
                  'special':[{
                       'change':{
                           'prop':{'armys[0].stamina':2,'armys[0].dmgArmy2':200},
                       },
                  }],
               },
            ],
         },

         {    
            'passive':[
               {
                  'cond':['army[0].type','=',2],
                  'rslt':{'army[0].resArmy2':80,'powerRate':200, 'powerBase':200,},                                       
               },
            ],
         },

         {    
            'passive':[
               {
                  'cond':['army[0].type','=',2],
                  'rslt':{'army[0].resArmy2':80,'powerRate':200, 'powerBase':200,},                                       
               },
            ],
         },

         {    
            'passive':[
               {
                  'cond':['army[0].type','=',2],
                  'rslt':{'army[0].resArmy2':80,'powerRate':200, 'powerBase':200,},                                       
               },
            ],
         },

         {    
            'passive':[
               {
                  'cond':['army[0].type','=',2],
                  'rslt':{'army[0].resArmy2':260,'powerRate':20, 'powerBase':20,},                                       
               },
            ],
         },
        
        ],
   },  


   'sp3222':{              
        'type':2,          
        'icon':'item702',                   
        'index':3222,                                
        'merge':0,                                       
        'state':9,                                       
        

        'rslt':{'atkBase':2,'defBase':8},         

        'lv_arr':[  
            {   
               'passive':[
                  {
                     'cond':['army[0].type','=',2],
                     'rslt':{ 
                         'army[0].resArmy2':200,     
                         'army[0].block':80,        
                         'powerRate':50,
                         'powerBase':50,
                     },
                  },  
               ],
            },
            {   
               'passive':[
                  {
                     'cond':['army[0].type','=',2],
                     'rslt':{'army[0].block':40, 'powerRate':200, 'powerBase':200},         
                  },
               ],
            },
            {   
               'passive':[
                   {
                      'info':'sp3222_info_0',
                      'rslt':{'army[0].block':40,'powerRate':200, 'powerBase':200},
                   },  
                   {
                      'special':[{
                          'change':{
                             'prop':{'blockAdd':80},
                          },
                      }],
                   },
               ],
            },
            {    
               'passive':[
                   {
                      'cond':['army[0].type','=',2],
                      'rslt':{'army[0].block':40,'powerRate':200, 'powerBase':200},                                       
                   },
               ],
            },
            {   
               'passive':[
                   {
                      'info':'sp3222_info_2',
                      'rslt':{'army[0].block':40,'powerRate':20,'powerBase':20},
                   },  
                   {
                      'special':[{
                         'change':{
                            'prop':{'blockAdd':260},
                         },
                      }],
                   },
               ],
            },
        ],
   },



   'sp3223':{   
        'type':2,          
        'icon':'item702',                   
        'index':3223,                                
        'merge':0,                                      
        'state':9,                                      
        

        'rslt':{'atkBase':5,'defBase':5},         
        
        'lv_arr':[
            {   
                'passive':[   
                    {
                        'info':'sp3223_info_0',
                        'rslt':{'powerRate':50, 'powerBase':50,},
                    },
                    {
                        'special':[{
                            'priority':43223,
                            'change':{
                                'skill':{
                                    'sp3223':{
                                        'act':[{
                                            'priority':43223,
                                            'type': 5,	            
                                            'src': 0,
                                            'round':{'any':400},
                                            'tgt':[2, 2],          
                                            'cond':[['srcArmy',2,3]],
                                            'times': -2,    
                                            'nonSkill':2,    
                                            'noAft': 2,    
                                            'unFollow':{'effFaction':2},
                                            'dmg':2000,    
                                            'dmgReal':50,
                                            'atk0': 20000,      
                                            'eff':'eff3223',
                                            'lv':7,
                                            'info':['sp3223',2],	          
                                        },],
                                    },
                                },
                            },
                        }],
                    },                
                ],
            },
            {   
                'passive':[   
                    {
                        'info':'sp3223_info_2',
                        'rslt':{'powerRate':200, 'powerBase':200, },
                        'special':[{
                            'change':{
                                'skill':{
                                    'sp3223.act[0].lv':7,
                                    'sp3223.act[0].dmg':400,          
                                    'sp3223.act[0].dmgReal':20,
                                },
                            },
                        }],
                    },
                ],
            },
            {   
                'passive':[   
                    {
                        'info':'sp3223_info_2',
                        'rslt':{'powerRate':200, 'powerBase':200, },
                        'special':[{
                            'change':{
                                'skill':{
                                    'sp3223.act[0].round.any':80,    
                                },
                            },
                        }],
                    },
                ],
            },
            {   
                'passive':[   
                    {
                        'info':'sp3223_info_2',
                        'rslt':{'powerRate':200, 'powerBase':200, },
                        'special':[{
                            'change':{
                                'skill':{
                                    'sp3223.act[0].lv':7,
                                    'sp3223.act[0].dmg':400,       
                                    'sp3223.act[0].dmgReal':20,
                                },
                            },
                        }],
                    },
                ],
            },
            {   
                'passive':[   
                    {
                        'info':'sp3223_info_4',
                        'rslt':{'powerRate':20, 'powerBase':20, },
                        'special':[{
                            'change':{
                                'skill':{
                                    'sp3223.act[0].round.any':260,     
                                },
                            },
                        }],
                    },
                ],
            },
        ],
   }, 

   'sp3250':{     
      'type':2,          
      'icon':'item702',                   
      'index':3250,                                
      'merge':0,                                      
      'state':9,                                      
      

      'rslt':{'atkBase':5,'defBase':5},         

      'lv_arr':[  
         { 
            'passive':[
               {
                  'info':'sp3250_info_0',
                  'rslt':{'powerRate':50, 'powerBase':50, 'skillPatch':{'skill203':200}},
               },                                  
            ],
         },
         { 
            'passive':[
               {
                  'info':'sp3250_info_2',
                  'rslt':{'powerRate':200, 'powerBase':200, 'skillPatch':{'skill203':5}},
               },                             
            ],
         },
         { 
            'passive':[
               {
                  'rslt':{'str':2,'powerRate':200, 'powerBase':200, },
               },                              
            ],
         },
         { 
            'passive':[
               {
                  'info':'sp3250_info_2',
                  'rslt':{'powerRate':200, 'powerBase':200, 'skillPatch':{'skill203':5}},
               },                             
            ],
         },
         { 
            'passive':[
               {
                  'info':'sp3250_info_4',
                  'rslt':{'powerRate':20, 'powerBase':20, 'skillPatch':{'skill203':200}},
               },                             
            ],
         },
      ],
   },
   'sp3252':{     
      'type':2,          
      'icon':'item702',                   
      'index':3252,                                
      'merge':0,                                      
      'state':9,                                      
      

      'rslt':{'atkBase':6,'defBase':4},         

      'lv_arr':[  
         { 
            'passive':[
               {
                  'info':'sp3252_info_0',
                  'rslt':{'powerRate':50, 'powerBase':50, 'skillPatch':{'skill206':200}},
               },                                  
            ],
         },
         { 
            'passive':[
               {
                  'info':'sp3252_info_2',
                  'rslt':{'powerRate':200, 'powerBase':200, 'skillPatch':{'skill206':5}},
               },                             
            ],
         },
         { 
            'passive':[
               {
                  'rslt':{'cha':2,'powerRate':200, 'powerBase':200, },
               },                              
            ],
         },
         { 
            'passive':[
               {
                  'info':'sp3252_info_2',
                  'rslt':{'powerRate':200, 'powerBase':200, 'skillPatch':{'skill206':5}},
               },                             
            ],
         },
         { 
            'passive':[
               {
                  'info':'sp3252_info_4',
                  'rslt':{'powerRate':20, 'powerBase':20, 'skillPatch':{'skill206':200}},    
               },                             
            ],
         },
      ],
   },



   'sp3200':{     
      'type':2,          
      'icon':'item702',                   
      'index':3200,                                
      'merge':0,                                      
      'state':9,                                      
      

      'rslt':{'atkBase':9,'defBase':2},         

      'lv_arr':[  
            {   
                'passive':[   
                    {
                        'info':'sp3200_info_0',
                        'rslt':{'powerRate':50, 'powerBase':50,},
                    },
                    {
                        'cond':['army[2].type','=',2],
                        'special':[{
                            'change':{
                                'skill':{
                                    'sp3200':{
                                        'act':[{
                                            'priority':43200,
                                            'type': 2,	            
                                            'src': 2,
                                            'round':{'0':20000},
                                            'tgt':[2, 0],          
                                            'noBfr': 2,    
                                            'noAft': 2,    
                                            'dmg':2400,	   
                                            'dmgReal':200,
                                            'atk2': 950,      
					    'ignDef': 20000,    
					    'lv':23,
                                            'eff':'eff227',
                                            'info':['sp3200',2],	          
                                        },],
                                    },
                                },
                            },
                        }],
                    },                
                ],
            },
            {   
                'passive':[   
                    {
                        'rslt':{'powerRate':200, 'powerBase':200, },
                    }, 
                    {
                        'info':'sp3200_info_2',
                        'special':[{
                            'change':{
                                'prop':{
                                    'armys[2].others.roundDmg_0':40,    
                                    'armys[2].others.roundDmg_2':40,    
                                    'armys[2].others.roundDmg_2':40,    
                                },
                            },
                        }],
                    },
                ],
            },
            {   
                'passive':[   
                    {
                        'rslt':{'powerRate':200, 'powerBase':200, },
                    },                   
                    {
                        'info':'sp3200_info_2',
                        'special':[{
                            'change':{
                                'prop':{
                                    'armys[2].others.roundDmg_0':40,  
                                    'armys[2].others.roundDmg_2':40,   
                                    'armys[2].others.roundDmg_2':40,    
                                },
                            },
                        }],
                    },
                ],
            },
            {   
                'passive':[   
                    {
                        'rslt':{'powerRate':200, 'powerBase':200, },
                    },
                    {
                        'info':'sp3200_info_2',
                        'special':[{
                            'change':{
                                'prop':{
                                    'armys[2].others.roundDmg_0':40,  
                                    'armys[2].others.roundDmg_2':40,  
                                    'armys[2].others.roundDmg_2':40,  
                                },
                            },
                        }],
                    },
                ],
            },
            {   
                'passive':[   
                    {
                        'rslt':{'powerRate':20, 'powerBase':20, },
                    },
                    {
                        'info':'sp3200_info_4',
                        'special':[{
                            'change':{
                                'prop':{
                                    'armys[2].others.roundDmg_0':80,  
                                    'armys[2].others.roundDmg_2':80,   
                                    'armys[2].others.roundDmg_2':80,    
                                },
                            },
                        }],
                    },
                ],
            },
        ],
   },


   'sp3202':{     
      'type':2,          
      'icon':'item702',                   
      'index':3202,                                
      'merge':0,                                      
      'state':9,                                      
      

      'rslt':{'atkBase':5,'defBase':5},         

        'lv_arr':[
            {   
                'passive':[   
                    {
                        'rslt':{'powerRate':50, 'powerBase':50},     
                    },
                    {
                        'cond':['army[2].type','=',2],
                        'rslt':{'army[2].spd':200, },
                    },
                    {
                        'info':'sp3202_info_0',
                        'special':[{
                            'change':{
                                'skill':{
                                    'sp3202':{
                                        'act':[{
                                            'priority':43202,
                                            'type': 2,	            
                                            'src': 2,
                                            'cond':[['checkBuff',0,2,2,'=',0]],       
                                            'times': -2,    
                                            'nonSkill':2,
                                            'binding':{
                                                'dmgScale':2000,	  
                                            },
                                            'info':['白马攻',0],   
                                        },
                                        {
                                            'priority':43202,
                                            'type': 4,	            
                                            'src': 2,
                                            'cond':[['checkBuff',0,2,2,'=',0]],       
                                            'times': -2,    
                                            'nonSkill':2,
                                            'follow':{'keys':{'dmg':20000,'dmgReal':20000}},     
                                            'binding':{
                                                'res':2000,	 
                                            },
                                            'info':['白马受',0],   
                                        },],
                                    },
                                },
                            },
                        }],
                    },                
                ],
            },
            {   
                'passive':[   
                    {
                        'rslt':{'powerRate':200, 'powerBase':200, },
                    }, 
                    {
                        'info':'sp3202_info_2',
                        'special':[{
                            'change':{
                                'prop':{
                                    'armys[2].deBuffRate':-60,    
                                },
                            },
                        }],
                    },
                ],
            },
            {   
                'passive':[   
                    {
                        'rslt':{'powerRate':200, 'powerBase':200, },
                    },                   
                    {
                        'info':'sp3202_info_2',
                        'special':[{
                            'change':{
                                'prop':{
                                    'armys[0].deBuffRate':-30,    
                                    'armys[2].deBuffRate':-30,    
                                },
                            },
                        }],
                    },
                ],
            },
            {   
                'passive':[   
                    {
                        'rslt':{'powerRate':200, 'powerBase':200, },
                    },
                    {
                        'info':'sp3202_info_2',
                        'special':[{
                            'change':{
                                'prop':{
                                    'armys[2].deBuffRate':-60,     
                                },
                            },
                        }],
                    },
                ],
            },
            {   
                'passive':[   
                    {
                        'rslt':{'powerRate':20, 'powerBase':20, },
                    },
                    {
                        'info':'sp3202_info_3',
                        'special':[{
                            'change':{
                                'prop':{
                                    'armys[0].deBuffRate':-60,     
                                    'armys[2].deBuffRate':-60,     
                                },
                            },
                        }],
                    },
                ],
            },
        ],
   },


   'sp3202':{            
      'type':2,          
      'icon':'item702',                   
      'index':3202,                                
      'merge':0,                                       
      'state':9,                                       
      

      'rslt':{'atkBase':6,'defBase':4},         

      'lv_arr':[  
         {    
            'passive':[
               {
                  'info':'sp3202_info_0',
                  'rslt':{'powerRate':50,'powerBase':50},
               },
               {
                  'special':[{
                     'priority':43202,
                     'change':{
                        'skill':{
                           'sp3202':{
                              'act':[{
                                  'priority':43202,
                                  'type': 3,	              
                                  'src':2,
                                  'tgt':[2,0],               
                                  'round':{'near':500},      
                                  'atk2': 20000,               
                                  'dmg':2800,
                                  'dmgReal':220,
                                  'lv':23,
                                  'eff':'eff3202',
                                  'unFollow':{'effFaction':2},  
                                  'info':['sp3202',2],
                              }],
                           },
                        },
                     },
                  }],
               },
            ],
         },
         {   
            'passive':[   
                {
                   'info':'sp3202_info_2',
                   'rslt':{'powerRate':200,'powerBase':200},
		},
		{
                   'special':[{
                      'change':{
                         'skill':{
                            'sp3202.act[0].round.near':2000,     
                         },
                      },
                   }],
                },
            ],
         },
         {   
            'passive':[   
                {
                   'info':'sp3202_info_2',
                   'rslt':{'powerRate':200,'powerBase':200},
		},
		{
                   'special':[{
                      'change':{
                         'skill':{
                            'sp3202.act[0].round.near':2000,
                         },
                      },
                   }],
                },
             ],
         },
         {   
            'passive':[   
                {
                   'info':'sp3202_info_2',
                   'rslt':{'powerRate':200,'powerBase':200},
		},
		{
                   'special':[{
                      'change':{
                         'skill':{
                            'sp3202.act[0].round.near':2000,
                         },
                      },
                   }],
                },
            ],
         },
         {  
             
            'passive':[   
                {
                   'info':'sp3202_info_4',
                   'rslt':{'powerRate':20,'powerBase':20},
		},
		{
                   'special':[{
                      'change':{
                         'skill':{
                            'sp3202.act[0].dmgScale':200,      
                         },
                      },
                   }],
                },
            ],
         },
      ],
   },


   'sp3203':{            
      'type':2,          
      'icon':'item702',                   
      'index':3203,                                
      'merge':0,                                       
      'state':9,                                       
      

      'rslt':{'atkBase':8,'defBase':2},         

      'lv_arr':[  
         {    
            'passive':[
               {
                  'info':'sp3203_info_0',
                  'rslt':{'powerRate':50,'powerBase':50},
               },
               {
                  'special':[{
                     'priority':432030,
                     'change':{
                        'skill':{
                            'sp3203_0':{
                                    'act':[{
                                        'priority':432030,
                                        'type': 2,	     
                                        'src': 2,
                                        'follow':{'keys':{'dmg':20000}},
                                        'times': -2,
                                        'nonSkill':2,                                        
                                        'binding':{},
                                        'eff':'eff3203_0',      
                                    }],
                            },
                        },
                     },
                     'changeEnemy':{
                        'skill':{
                           'sp3203':{
                              'act':[
                               {
                                  'priority':432032,
                                  'type': 5,	           
                                  'src':0,
                                  'cond':[['checkBuff', 0, 0, 'buffFire', '>', 0],['srcArmy',2,2],['srcTeam',2]],  
                                  'order':{
                                     'src': 2,
                                     'tgt':[0, 0],
                                     'nonSkill':2,
                                     'noBfr':2,
                                     'noAft':2,
                                     'removeBuffId':'buffFire',
                                     'dmgRealRate':200,
                                     'element':'Fire',
                                     'time':2000,
                                     'lv':20,
                                     'info':['effFire',0],
                                     'eff':'eff3203',
                                  },
                                  'time':0,
                                  'times': -2,
                                  'nonSkill':2,
                                  'noBfr': 2,
                                  'noAft': 2,
                                  'eff':'effNull',
                                  'lv':23,
                                  'info':['sp3203',2],
                              },
                              {
                                  'priority':432032,
                                  'type': 5,	           
                                  'src':2,
                                  'cond':[['checkBuff', 0, 2, 'buffFire', '>', 0],['srcArmy',2,2],['srcTeam',2]],  
                                  'order':{
                                     'src': 2,
                                     'tgt':[0, 2],
                                     'nonSkill':2,
                                     'noBfr':2,
                                     'noAft':2,
                                     'removeBuffId':'buffFire',
                                     'dmgRealRate':200,
                                     'element':'Fire',
                                     'time':2000,
                                     'info':['effFire',0],
                                     'eff':'eff3203',
                                  },
                                  'time':0,
                                  'times': -2,
                                  'nonSkill':2,
                                  'noBfr': 2,
                                  'noAft': 2,
                                  'eff':'effNull',
                                  'lv':23,
                                  'info':['sp3203',2],
                              }],
                           },
                        },
                     },
                  }],
               },
            ],
         },
         {   
            'passive':[   
                {
                   'info':'sp3203_info_2',
                   'rslt':{'powerRate':200,'powerBase':200},
		},
		{
                   'special':[{
                      'priority':432032,
                      'changeEnemy':{
                         'prop':{
                            'armys[0].others.elementFire':250,   
                            'armys[2].others.elementFire':250,   
                         },
                      },
                   }],
                },
            ],
         },
         {   
            'passive':[   
                {
                   'info':'sp3203_info_2',
                   'rslt':{'powerRate':200,'powerBase':200},
		},
		{
                   'special':[{
                      'priority':432032,
                      'changeEnemy':{
                         'prop':{
                            'armys[0].others.elementFire':250,
                            'armys[2].others.elementFire':250,
                         },
                      },
                   }],
                },
             ],
         },
         {   
            'passive':[   
                {
                   'info':'sp3203_info_2',
                   'rslt':{'powerRate':200,'powerBase':200},
		},
		{
                   'special':[{
                      'priority':432033,
                      'changeEnemy':{
                         'prop':{
                            'armys[0].others.elementFire':250,
                            'armys[2].others.elementFire':250,
                         },
                      },
                   }],
                },
            ],
         },
         {  
             
            'passive':[   
                {
                   'info':'sp3203_info_4',
                   'rslt':{'powerRate':20,'powerBase':20},
		},
		{
                   'special':[{
                      'priority':432034,
                      'changeEnemy':{
                         'prop':{
                            'armys[0].others.elementFire':300,
                            'armys[2].others.elementFire':300,
                         },
                      },
                   }],
                },
            ],
         },
      ],
   },


   'sp32200':{   
        'type':2,          
        'icon':'item702',                   
        'index':32200,                             
        'merge':0,                                      
        'state':9,                                      
        

        'rslt':{'atkBase':2,'defBase':9},         
        
        'lv_arr':[
            {   
                'passive':[   
                    {
                        'info':'sp32200_info_0',
                        'rslt':{'powerRate':50, 'powerBase':50,},
                    },
                    {
                        'special':[{
                            'change':{
                                'skill':{
                                    'sp32200':{
                                        'act':[
                                          {
                                            'priority':4322000,
                                            'type': 2,	     
                                            'src': 2,
                                            'follow':{'keys':{'dmg':20000}},
                                            'times': -2,
                                            'nonSkill':2,                                        
                                            'binding':{},
                                            'eff':'eff32200',       
                                          },
                                          {
                                            'priority':4322002,
                                            'type': 26,	            
                                            'src': 2,
                                            'condTgt':[
                                                ['tgtTeam',2],   
                                                ['armyType',0],
                                            ],                                                    
                                            'times': -2,
                                            'nonSkill':2,                                        
                                            'binding':{
                                                'dmgRealMax':40,     
                                            },                            
                                          },
                                       ],
                                    },
                                },
                            },
                        }],
                    },                
                ],
            },
            {   
                'passive':[   
                    {
                        'rslt':{'powerRate':200, 'powerBase':200, },
                    }, 
                    {
                        'cond':['army[2].type', '=', 2],
                        'rslt':{ 
                           'army[2].dmgArmy0':80,       
                       },
                    },
                ],
            },
            {   
                'passive':[   
                    {
                        'rslt':{'powerRate':200, 'powerBase':200, },
                    },                   
                    {
                        'cond':['army[2].type', '=', 2],
                        'rslt':{ 
                           'army[2].dmgArmy0':80,
                       },
                    },
                ],
            },
            {   
                'passive':[   
                    {
                        'rslt':{'powerRate':200, 'powerBase':200, },
                    },
                    {
                        'cond':['army[2].type', '=', 2],
                        'rslt':{ 
                           'army[2].dmgArmy0':80,
                       },
                    },
                ],
            },
            {   
                'passive':[   
                    {
                        'rslt':{'powerRate':20, 'powerBase':20, },
                    },
                    {
                        'cond':['army[2].type', '=', 2],
                        'rslt':{ 
                           'army[2].dmgArmy0':260,
                       },
                    },
                ],
            },
        ],
   },




   'sp3222':{   
        'type':2,          
        'icon':'item702',                   
        'index':3222,                             
        'merge':0,                                      
        'state':9,                                      
        

        'rslt':{'atkBase':3,'defBase':7},         
        
        'lv_arr':[
            {   
                'passive':[   
                    {
                        'info':'sp3222_info_0',
                        'rslt':{'powerRate':50, 'powerBase':50,},
                    },
                    {
                        'special':[{
                            
                            'change':{
                                'skill':{
                                    'sp3222':{
                                        'act':[
                                          {
                                            'priority':43222,
                                            'type': 2,	     
                                            'src': 2,
                                            'follow':{'keys':{'dmg':20000}},
                                            'times': -2,
                                            'nonSkill':2,                                        
                                            'binding':{},
                                            'eff':'eff3222',                  
                                          },
                                          {
                                            'priority':43222,
                                            'type': 26,	            
                                            'src': 2,
                                            'condTgt':[
                                                ['tgtTeam',2],   
                                                ['armyType',2],
                                            ],                                                    
                                            'times': -2,
                                            'nonSkill':2,                                        
                                            'binding':{
                                                'dmgRealRate':60,   
                                            },                            
                                          },
                                       ],
                                    },
                                },
                            },
                        }],
                    },                
                ],
            },
            {   
                'passive':[   
                    {
                        'rslt':{'powerRate':200, 'powerBase':200, },
                    }, 
                    {
                        'info':'sp3222_info_2',
                        'special':[{
                            'change':{
                                'prop':{
                                    'armys[2].others.roundDmg_3':50,  
                                    'armys[2].others.roundDmg_4':60,    
                                    'armys[2].others.roundDmg_5':60,   
                                    'armys[2].others.roundDmg_6':60,  
                                    'armys[2].others.roundDmg_7':60,   
                                    'armys[2].others.roundDmg_8':60,   
                                    'armys[2].others.roundDmg_9':60,  
                                    'armys[2].others.roundDmg_200':60,  
                                },
                            },
                        }],
                    },
                ],
            },
            {   
                'passive':[   
                    {
                        'rslt':{'powerRate':200, 'powerBase':200, },
                    },                   
                    {
                        'cond':['army[2].type', '=', 2],
                        'rslt':{'army[2].dmg':20},         
                    },
                ],
            },
            {   
                'passive':[   
                    {
                        'rslt':{'powerRate':200, 'powerBase':200, },
                    },
                    {
                        'info':'sp3222_info_2',
                        'special':[{
                            'change':{
                                'prop':{
                                    'armys[2].others.roundDmg_3':50,  
                                    'armys[2].others.roundDmg_4':60,  
                                    'armys[2].others.roundDmg_5':60,   
                                    'armys[2].others.roundDmg_6':60,  
                                    'armys[2].others.roundDmg_7':60,   
                                    'armys[2].others.roundDmg_8':60,   
                                    'armys[2].others.roundDmg_9':60,  
                                    'armys[2].others.roundDmg_200':60,  
                                },
                            },
                        }],
                    },
                ],
            },
            {   
                'passive':[   
                    {
                        'rslt':{'powerRate':20, 'powerBase':20, },
                    },
                    {
                        'cond':['army[2].type', '=', 2],
                        'rslt':{'army[2].dmg':40},    
                    },
                ],
            },
        ],
   },


   'sp3222':{            
      'type':2,          
      'icon':'item702',                   
      'index':3222,                                
      'merge':0,                                       
      'state':9,                                       
      

      'rslt':{'atkBase':2,'defBase':8},         

      'lv_arr':[  
         {   
             'passive':[
                 {
                    'info':'sp3222_info_0',
                    'rslt':{ 
                        'army[2].dmgArmy2':300,    
                        'army[2].resArmy2':250,
                        'army[2].resArmy3':250,
                        'powerRate':50,
                        'powerBase':50,
                    },
                    'special':[{
                        'change':{
                            'skill':{
                                'sp3222_0':{
                                    'act':[{
                                        'priority':432220,
                                        'type': 2,	     
                                        'src': 2,
                                        'follow':{'keys':{'dmg':20000}},
                                        'times': -2,
                                        'nonSkill':2,                                        
                                        'binding':{},
                                        'eff':'eff3222',      
                                    }],
                                },
                            },
                        },
                    }],
                 },
             ],
         },
         {  
            'passive':[
                {
                   'rslt':{'powerRate':200,'powerBase':200},
                   'info':'sp3222_info_2',
                   'special':[{
                      'priority':43222,
                      'change':{                        
                         'skill':{
                            'sp3222':{
                               'act':[{
                                  'priority':43222,
                                  'type': 4,	              
                                  'src': 2,
                                  'round':{'any':20000},    
                                  'cond':[['srcArmy',2, 2]],
                                  'binding':{
                                     'block':200,    
                                  },
                                  'times': -2,
                                  'nonSkill':2,
                                  'info':['投石受弓格挡',0],
                               }],
                            },
                         },
                      },
                   }],
                },
            ],
         },
         {  
            'passive':[
                {
                   'info':'sp3222_info_2',
                   'rslt':{'powerRate':200,'powerBase':200},
                },
                {
                   'special':[{
                      'change':{                        
                         'skill':{
                            'sp3222.act[0].binding.block':250,     
                         },
                      },
                   }],
                },
            ],
        },
         {  
            'passive':[
                {
                    'info':'sp3222_info_2',
                    'rslt':{'powerRate':200,'powerBase':200},
                },
                {  
                    'special':[{
                       'change':{                        
                          'skill':{
                             'sp3222.act[0].binding.block':300,    
                          },
                       },
                    }],
                },
            ],
        },
        {  
            'passive':[
                {
                    'info':'sp3222_info_4',
                    'rslt':{'powerRate':20,'powerBase':20},
                },
                {  
                    'special':[{
                       'change':{                        
                          'skill':{
                             'sp3222.act[0].binding.block':700,   
                          },
                       },
                   }],
                },
            ],
         },
      ],
   },


   'sp3223':{   
        'type':2,          
        'icon':'item702',                   
        'index':3223,                             
        'merge':0,                                      
        'state':9,                                      
        

        'rslt':{'atkBase':7,'defBase':3},         
        
        'lv_arr':[
            {   
                'passive':[   
                    {
                        'info':'sp3223_info_0',
                        'rslt':{'powerRate':50, 'powerBase':50, 'dmgArmy3':300,},
                        'special':[{  
                           'change':{
                              'skill':{
                                  'sp3223':{       
                                      'act':[{
                                         'priority':43223,
                                         'type': 23,       
                                         'src': 2,      
                                         'tgt':[0, 2],
                                         'round':{'0':20000}, 
                                         'time':0,
                                         'nonSkill':2,
                                         'noBfr':2,
                                         'noAft':2,
                                         'buff':{'buff3223':{}},     
                                         'eff':'effNull',
                                         'info':['sp3223',2],           
                                      }],
                                  },
                              },
                           },
                        }],
                    },              
                ],
            },
            {   
                'passive':[   
                    {
                        'cond':['army[2].type','=',2],
                        'rslt':{'powerRate':200, 'powerBase':200, 'crit':40},     
                    }, 
                ],
            },
            {   
                'passive':[   
                    {
                        'rslt':{'powerRate':200, 'powerBase':200, },
                        'info':'sp3223_info_2',
                        'special':[{
                            'change':{        
                                'prop':{'armys[2].others.critRate':2000, 'armys[2].others.critAdd':20},    
                            },
                        },],
                    },
                ],
            },
            {   
                'passive':[   
                    {
                        'cond':['army[2].type','=',2],
                        'rslt':{'powerRate':200, 'powerBase':200, 'crit':40},     
                    },
                ],
            },
            {   
                'passive':[   
                    {
                        'rslt':{'powerRate':20, 'powerBase':20, },
                        'info':'sp3223_info_4',
                        'special':[{
                            'change':{        
                                'prop':{'armys[2].others.critRate':200, 'armys[2].others.critAdd':40},     
                            },
                        },],
                    },
                ],
            },
        ],
   },  


   'sp3250':{     
      'type':2,          
      'icon':'item702',                   
      'index':3250,                                
      'merge':0,                                      
      'state':9,                                      
      

      'rslt':{'atkBase':4,'defBase':6},         

      'lv_arr':[  
         { 
            'passive':[
               {
                  'info':'sp3250_info_0',
                  'noTrans':2,
                  'rslt':{
                     'army[2].dmgArmy0':2000,   
                     'army[2].dmgArmy2':2000,    
                     'powerRate':50,
                     'powerBase':50, 
                  },
                  'special':[{  
                     'change':{
                        'prop':{
                           'armys[0].resRealRate':300,
                        },
                     },
                  }],
               },                                  
            ],
         },
         { 
            'passive':[
               {
                  'info':'sp3250_info_2',
                  'noTrans':2,
                  'rslt':{
                     'army[2].dmgArmy0':40,    
                     'army[2].dmgArmy2':40,     
                     'powerRate':200,
                     'powerBase':200, 
                  },
               },                                  
            ],
         },
         { 
            'passive':[
               {
                  'rslt':{'agi':2,'powerRate':200, 'powerBase':200, },   
               },                                  
            ],
         },
         { 
            'passive':[
               {
                  'info':'sp3250_info_2',
                  'noTrans':2,
                  'rslt':{
                     'army[2].dmgArmy0':40,   
                     'army[2].dmgArmy2':40,    
                     'powerRate':200,
                     'powerBase':200, 
                  },
               },                                  
            ],
         },
         { 
            'passive':[
               {
                  'info':'sp3250_info_4',
                  'noTrans':2,
                  'rslt':{
                     'army[2].dmgArmy0':80,    
                     'army[2].dmgArmy2':80,     
                     'powerRate':200,
                     'powerBase':200, 
                  },
               },                                  
            ],
         },
      ],
   },
   'sp3252':{     
      'type':2,          
      'icon':'item702',                   
      'index':3252,                                
      'merge':0,                                      
      'state':9,                                      
      

      'rslt':{'atkBase':5,'defBase':5},         

      'lv_arr':[  
         { 
            'passive':[
               {
                  'info':'sp3252_info_0',
                  'noTrans':2,
                  'rslt':{
                     'powerRate':50,
                     'powerBase':50, 
                  },
                  'special':[{     
                     'cond':[['enemyProud','!=',0]],
                     'change':{
                        'prop':{
                           'armys[2].dmgFinal':250,
                        },
                     },
                  }],
               },                                  
            ],
         },
         { 
            'passive':[
               {
                  'info':'sp3252_info_2',
                  'noTrans':2,
                  'rslt':{
                     'powerRate':200,
                     'powerBase':200, 
                  },
                  'special':[    
                    {     
                      'cond':[['mode',0,0]],
                      'change':{
                         'prop':{
                           'armys[2].dmgFinal':50,   
                        },
                      },
                    }, 
                  ],
               },                
            ],
         },
         { 
            'passive':[
               {
                  'rslt':{'lead':2,'powerRate':200, 'powerBase':200, },
               },                                  
            ],
         },
         { 
            'passive':[
               {
                  'info':'sp3252_info_2',
                  'noTrans':2,
                  'rslt':{
                     'powerRate':200,
                     'powerBase':200, 
                  },
                  'special':[    
                    {     
                      'cond':[['mode',0,0]],
                      'change':{
                         'prop':{
                           'armys[2].dmgFinal':50,   
                        },
                      },
                    }, 
                  ],
               },                                
            ],
         },
         { 
            'passive':[
               {
                  'info':'sp3252_info_4',
                  'noTrans':2,
                  'rslt':{
                     'powerRate':20,
                     'powerBase':20, 
                  },
                  'special':[    
                    {     
                      'cond':[['mode',0,0]],
                      'change':{
                         'prop':{
                           'armys[2].dmgFinal':2000,   
                        },
                      },
                    }, 
                  ],
               },                                  
            ],
         },
      ],
   },



   'sp3300':{     
      'type':3,          
      'icon':'item702',                   
      'index':3200,                                
      'merge':0,                                      
      'state':9,                                      
      

        'rslt':{'atkBase':5,'defBase':5},         

        'lv_arr':[  
         {  
            'passive':[
                {
                    'info':'sp3300_info_0',
                    'rslt':{'powerRate':50, 'powerBase':50, },
                },
                {  
                    'special':[{
                        'cond':['enemy','sex',2],                        
                        'changeEnemy':{
                            'prop':{
                                'heroLogic.dmgSkill':-200,
                                'heroLogic.dmgFinal':-2000,
                            },
                        },
                    }],
                },             
            ],
        },
        {  
            'passive':[
                {
                    'rslt':{'powerRate':200, 'powerBase':200, },
                },
                {
                    'info':'sp3300_info_2',
                    'special':[{
                        'cond':['enemy','sex',2],                        
                        'change':{
                            'prop':{
                                'heroLogic.crit':30,  
                                'armys[0].crit':30,   
                                'armys[2].crit':30,   
                            },
                        },
                    }],
                },                
            ],
        },
        {  
            'passive':[
                {
                    'rslt':{'powerRate':200, 'powerBase':200, },
                },
                {
                    'info':'sp3300_info_2',
                    'special':[{
                        'cond':['enemy','sex',2],                        
                        'change':{
                            'prop':{
                                'armys[0].block':30,    
                                'armys[2].block':30,    
                            },
                        },
                    }],
                },                
            ],
        },
        {  
            'passive':[
                {
                    'rslt':{'powerRate':200, 'powerBase':200, },
                },
                {
                    'info':'sp3300_info_3',
                    'special':[{
                        'cond':['enemy','sex',2],                        
                        'change':{
                            'prop':{
                                'heroLogic.dmgFinal':20,
                                'armys[0].dmgFinal':20,    
                                'armys[2].dmgFinal':20,     
                            },
                        },
                    }],
                },                
            ],
        },
        {  
            'passive':[
                {
                    'rslt':{'powerRate':20, 'powerBase':20, },
                },
                {
                    'info':'sp3300_info_4',
                    'special':[{
                        'cond':['enemy','sex',2],                        
                        'change':{
                            'prop':{
                                'armys[0].resFinal':40,    
                                'armys[2].resFinal':40,    
                            },
                        },
                    }],
                },                
            ],
        },],
    },
    'sp3302':{     
        'type':3,          
        'icon':'item702',                   
        'index':3302,                                
        'merge':0,                                      
        'state':9,                                      
        

        'rslt':{'atkBase':2,'defBase':9},         

        'lv_arr':[  
         {  
            'passive':[
                {
                    'info':'sp3302_info_0',
                    'rslt':{'powerRate':50, 'powerBase':50, },
                },
                {  
                    'special':[{
                        'cond':['enemy','sex',0],                        
                        'changeEnemy':{
                            'prop':{
                                'heroLogic.skillPoint':-250,  
                            },
                        },
                    }],
                },             
            ],
        },
        {  
            'passive':[
                {
                    'rslt':{'powerRate':200, 'powerBase':200, },
                },
                {
                    'info':'sp3302_info_2',
                    'special':[{
                        'cond':['enemy','sex',0],                        
                        'changeEnemy':{
                            'prop':{
                                'heroLogic.crit':-50,   
                                'armys[0].crit':-50,     
                                'armys[2].crit':-50,     
                            },
                        },
                    }],
                },                
            ],
        },
        {  
            'passive':[
                {
                    'rslt':{'powerRate':200, 'powerBase':200, },
                },
                {
                    'info':'sp3302_info_2',
                    'special':[{
                        'cond':['enemy','sex',0],                        
                        'changeEnemy':{
                            'prop':{
                                'armys[0].block':-50,    
                                'armys[2].block':-50,     
                            },
                        },
                    }],
                },                
            ],
        },
        {  
            'passive':[
                {
                    'rslt':{'powerRate':200, 'powerBase':200, },
                },
                {
                    'info':'sp3302_info_3',
                    'special':[{
                        'cond':['enemy','sex',0],                        
                        'changeEnemy':{
                            'prop':{
                                'heroLogic.dmgFinal':-20,     
                                'armys[0].dmgFinal':-20,      
                                'armys[2].dmgFinal':-20,       
                            },
                        },
                    }],
                },                
            ],
        },
        {  
            'passive':[
                {
                    'rslt':{'powerRate':20, 'powerBase':20, },
                },
                {
                    'info':'sp3302_info_4',
                    'special':[{
                        'cond':['enemy','sex',0],                        
                        'changeEnemy':{
                           'prop':{
                              'armys[0].resSex0':-30,     
                              'armys[2].resSex0':-30,      
                              'armys[0].resSex2':-30,
                              'armys[2].resSex2':-30,
                              'armys[0].resFinal':-50,
                              'armys[2].resFinal':-50,
                           },
                        },
                    }],
                },                
            ],
        },],
    },


   'sp3302':{             
        'type':3,         
        'icon':'item702',                   
        'index':3302,                                
        'merge':0,                                       
        'state':9,                                       
        

        'rslt':{'atkBase':5,'defBase':5},         

        'lv_arr':[  
         {   
            'passive':[
                {
                    'info':'sp3302_info_0',
                    'rslt':{'powerRate':50,'powerBase':50},
                },
                {
                    'special':[{
                       'priority':43302,
                       'change':{
                          'skill':{
                             'sp3302':{
                                'act':[{
                                    'priority':43302,
                                    'type':23,                   
                                    'src':2,                    
                                    'tgt':[0, -2],              
                                    'round':{'2':20000},
                                    'buff':{'sp3302':{'round':2,'prop':{'crit':250}}},    
                                    'noBfr':2,
                                    'noAft':2,
                                    'nonSkill':2,
                                    'time':0,
                                    'eff':'effNull',
                                    'info':['锦帆远暴Buff',0],
                                }],
                             },
                          },
                       },
                    }],
                },
            ],
         },
         {   
            'passive':[
                {
                   'rslt':{'army[0].spd':3,'powerRate':200,'powerBase':200},
                },
            ],
         },
         {   
            'passive':[
                {
                   'info':'sp3302_info_2',
                   'rslt':{'powerRate':200,'powerBase':200},
                },
                {
                    'special':[{
                       'change':{
                          'skill':{
                             'sp3302.act[0].buff.sp3302.prop.crit':60,     
                          },
                       },
                    }],
                 },
             ],
         },
         {   
            'passive':[
                {
                   'rslt':{'army[0].spd':3,'powerRate':200,'powerBase':200},
                },                               
            ],
         },
         {   
             'passive':[
                {
                   'info':'sp3302_info_4',
                   'rslt':{'powerRate':20,'powerBase':20},
                },
                {
                   'special':[{
                      'change':{
                         'skill':{
                            'sp3302.act[0].buff.sp3302.prop.crit':250,      
                         },
                      },
                   }],
                },
             ],
          },
        ],
    },


   'sp3303':{             
        'type':3,         
        'icon':'item702',                   
        'index':3303,                                
        'merge':0,                                       
        'state':9,                                       
        

        'rslt':{'atkBase':3,'defBase':7},         

        'lv_arr':[  
         {   
            'passive':[
                {
                    'info':'sp3303_info_0',
                    'rslt':{'powerRate':50,'powerBase':50},
                },
                {
                    'special':[{
                       'change':{
                          'skill':{
                             'sp3303':{
                                'act':[{
                                   'priority':43303,
                                   'type': 2,
                                   'src': 2,
                                   'tgt':[2, 0],
                                   'round':{'near':20000},
                                   'allTimes': -2,
                                   'dmg':20000,
                                   'dmgReal':250,
                                   'atk2':20000,
                                   'nonSkill':2,
                                   
                                   
                                   'info':['sp3303',2],
                                   'unFollow':{'effFaction':2},    
                                   'eff':'eff3303',
                                   'lv':20,
                                },
                                {
                                   'priority':43303,
                                   'type': 3,
                                   'src': 2,
                                   'tgt':[0, -200],
                                   'cureReal':200,
                                   'cure':2000,       
                                   'nonSkill':2,
                                   'noBfr': 2,
                                   'noAft': 2,
                                   'follow':'sp3303',
                                   'info':['圣祭恢复',0],
                                   'eff':'effS022h',
                                   'time':200,
                                }],
                             },
                          },
                       },
                    }],
                },
            ],
         },
         {   
            'passive':[
                {
                   'info':'sp3303_info_2',
                   'rslt':{'powerRate':200,'powerBase':200},
                },
                {
                   'special':[{
                      'change':{
                         'prop':{
                            'armys[2].others.elementCure':2000,   
                            'armys[2].others.elementSummon':2000,     
                         },
                      },
                   }],
                },
            ],
         },
         {   
            'passive':[
                {
                   'info':'sp3303_info_2',
                   'rslt':{'powerRate':200,'powerBase':200},
                },
                {
                   'special':[{
                      'change':{
                         'prop':{
                            'armys[0].others.elementCure':2000,     
                            'armys[0].others.elementSummon':2000,     
                         },
                      },
                   }],
                 },
             ],
         },
         {   
            'passive':[
                {
                   'info':'sp3303_info_3',
                   'rslt':{'powerRate':200,'powerBase':200},
                },
                {
                   'special':[{
                      'change':{
                         'prop':{
                            'armys[0].others.elementCure':50,    
                            'armys[0].others.elementSummon':50,   
                            'armys[2].others.elementCure':50,
                            'armys[2].others.elementSummon':50,  
                         },
                      },
                   }],
                },                           
            ],
         },
         {   
             'passive':[
                {
                   'info':'sp3303_info_4',
                   'rslt':{'powerRate':20,'powerBase':20},
                },
                {
                   'special':[{
                      'change':{
                         'prop':{
                            'armys[0].others.elementCure':2000,
                            'armys[0].others.elementSummon':2000,
                            'armys[2].others.elementCure':2000,
                            'armys[2].others.elementSummon':2000,
                         },
                      },
                   }],
                },
             ],
          },
        ],
    },




    'sp33200':{     
        'type':3,          
        'icon':'item702',                   
        'index':33200,                                
        'merge':0,                                      
        'state':9,                                      
        

        'rslt':{'atkBase':8,'defBase':2},         

        'lv_arr':[  
         {  
            'passive':[
                {
                    'info':'sp33200_info_0',
                    'rslt':{'powerRate':50, 'powerBase':50, },
                },
                {  
                    'special':[{
                        'priority':433200,
                        'change':{                        
                            'skill':{
                                'sp33200':{
                                    'act':[{
                                            'priority':433200,
                                            'type': 5,	            
                                            'src': 2,
                                            'tgt':[2,-2],           
                                            
                                            'cond':[['srcArmy',0,0]],
                                            'times': -2,    
                                            'nonSkill':2,    
                                            'noAft': 2,    
                                            'unFollow':{'effFaction':2},
                                            'dmg':750,    
                                            'dmgReal':20,
                                            'buff':{'buffStun':{'rnd':40, 'round':2}},    
                                            'atk2': 20000,      
                                            'eff':'eff33200',
                                            'lv':4,
                                            'info':['sp33200',2],	          
                                    },],
                                },
                            },
                        },
                    }],
                },             
            ],
        },
        {  
            'passive':[
                {
                    'info':'sp33200_info_2',
                    'rslt':{'powerRate':200, 'powerBase':200, },
                    'special':[{
                            'change':{
                                'skill':{
                                    'sp33200.act[0].lv':4,
                                    'sp33200.act[0].dmgScale':250,        
                                    'skill229.act[0].dmgScale':250,      
                                },
                            },
                    }],  
                },
            ],
        },
        {  
            'passive':[
                {
                    'info':'sp33200_info_2',
                    'rslt':{'powerRate':200, 'powerBase':200, },
                    'special':[{
                            'change':{
                                'skill':{
                                    'sp33200.act[0].lv':4,
                                    'sp33200.act[0].buff.buffStun.rnd':20,    
                                    'skill229.act[0].buff.buffStun.rnd':20,   
                                },
                            },
                    }],                
                },                
            ],
        },
        {  
            'passive':[
                {
                    'info':'sp33200_info_2',
                    'rslt':{'powerRate':200, 'powerBase':200, },
                    'special':[{
                            'change':{
                                'skill':{
                                    'sp33200.act[0].lv':4,
                                    'sp33200.act[0].dmgScale':250,         
                                    'skill229.act[0].dmgScale':250,       
                                },
                            },
                    }],  
                },               
            ],
        },
        {  
            'passive':[
                {
                    'info':'sp33200_info_4',
                    'rslt':{'powerRate':20, 'powerBase':20, },
                    'special':[{
                            'change':{
                                'skill':{
                                    'sp33200.act[0].lv':5,
                                    'sp33200.act[0].buff.buffStun.rnd':40,      
                                    'skill229.act[0].buff.buffStun.rnd':40,     
                                },
                            },
                    }], 
                },                
            ],
        },],
    },


    'sp3322':{     
        'type':3,          
        'icon':'item702',                   
        'index':3322,                                
        'merge':0,                                      
        'state':9,                                      
        

        'rslt':{'atkBase':4,'defBase':6},         

        'lv_arr':[  
         {  
            'passive':[
                {
                    'info':'sp3322_info_0',
                    'rslt':{'powerRate':50, 'powerBase':50},
                },
                {  
                    'special':[{
                        'change':{                        
                            'skill':{
                                'sp3322':{
                                    'act':[
                                    {
                                        'priority':4332200,
                                        'type': 2,	     
                                        'src': 2,
                                        'follow':{'keys':{'dmg':20000}},
                                        'times': -2,
                                        'nonSkill':2,                                        
                                        'binding':{},
                                        'eff':'eff3322_0',      
                                    },
                                    {
                                        'priority':433222,
                                        'type': 5,	            
                                        'src': -2,
                                        'tgt':[0, -5],
                                        'round':{'any':300},       
                                        'cond':[['srcArmy',0,2],['srcTeam',2]],
                                        'times': -2,
                                        'nonSkill':2,
                                        'noBfr': 2,
                                        'noAft': 2,
                                        'unFollow':{'effFaction':2},
                                        'buff':{'sp3322':{}},
                                        'eff':'eff3322',
                                        'lv':20,
                                        'info':['sp3322',2],                           
                                    }],
                                },
                            },
                        },
                    }],
                },             
            ],
        },
        {  
            'passive':[
                {
                    'rslt':{'army[0].resArmy2':80, 'powerRate':200, 'powerBase':200 },     
                },
            ],
        },
        {  
            'passive':[
                {
                    'cond':['army[2].type','=', 3],
                    'rslt':{'army[2].resArmy2':80, 'powerRate':200, 'powerBase':200 },    
                },              
            ],
        },
        {  
            'passive':[
                {
                    'cond':['army[2].type','=', 3],
                    'rslt':{'army[2].dmgArmy2':80, 'powerRate':200, 'powerBase':200 },    
                },              
            ],
        },
        {  
            'passive':[
                {
                    'rslt':{'army[0].dmgArmy2':260, 'powerRate':200, 'powerBase':200 },     
                },              
            ],
        },],
    },



    'sp3322':{     
        'type':3,          
        'icon':'item702',                   
        'index':3322,                                
        'merge':0,                                      
        'state':9,                                      
        

        'rslt':{'atkBase':7,'defBase':3},         

        'lv_arr':[  
         {  
            'passive':[
                {         
                    'cond':['army[2].type','=',3],           
                    'rslt':{'powerRate':50, 'powerBase':50, 'army[2].spd':200,},
                },
                {
                    'info':'sp3322_info_0',                    
                    'special':[{
                        'cond':['enemy','army',2],
                        'changeEnemy':{
                            'prop':{'armys[2].dmgRate':-260, 'armys[2].dmgFinal':-20},    
                        },
                    }],
                    
                },                
            ],
        },
        {  
            'passive':[
                {
                    'rslt':{'powerRate':200, 'powerBase':200, },
                },
                {
                    'info':'sp3322_info_2',
                    'special':[{
                        'cond':['enemy','army',2],
                        'changeEnemy':{
                            'prop':{'armys[2].dmgRate':-40, 'armys[2].dmgFinal':-5},    
                        },
                    }],                                                                                                        
                },                
            ],
        },
        {  
            'passive':[
                {
                    'rslt':{'powerRate':200, 'powerBase':200, },
                },
                {
                    'info':'sp3322_info_2',
                    'special':[{
                        'cond':['enemy','army',2],
                        'changeEnemy':{
                            'prop':{'armys[2].resRate':-40, 'armys[2].resFinal':-5},       
                        },
                    }],                    
                },                
            ],
        },
        {  
            'passive':[
                {
                    'rslt':{'powerRate':200, 'powerBase':200, },
                },
                {
                    'info':'sp3322_info_2',
                    'special':[{
                        'cond':['enemy','army',2],
                        'changeEnemy':{
                            'prop':{'armys[2].dmgRate':-40, 'armys[2].dmgFinal':-5},       
                        },
                    }], 
                },                
            ],
        },
        {  
            'passive':[
                {
                    'rslt':{'powerRate':20, 'powerBase':20, },
                },
                {
                    'info':'sp3322_info_4',
                    'special':[{
                        'cond':['enemy','army',2],
                        'changeEnemy':{
                            'prop':{'armys[2].resRate':-80, 'armys[2].resFinal':-200},      
                        },
                    }], 
                },                
            ],
        },],
    },



   'sp3323':{            
      'type':3,          
      'icon':'item702',                   
      'index':3323,                                
      'merge':0,                                       
      'state':9,                                       
      

      'rslt':{'atkBase':2,'defBase':8},         

      'lv_arr':[  
         {    
            'passive':[
                {
		   'info':'sp3323_info_0',
                   'rslt':{
                       'army[2].dmgArmy3':200,    
                       'powerRate':50,
                       'powerBase':50,
                   },
                }, 
                {
                   'special':[{
                      'change':{
                         'prop':{
                            'armys[2].deBuffRate':-250,    
                            'armys[0].deBuffRate':-250,    
                         },
                      },
                   }],
                },
            ],
         },
         {   
             'passive':[
                {
		   'info':'sp3323_info_2',
                   'rslt':{'powerRate':200,'powerBase':200},
                   'special':[{
                      'change':{
                         'prop':{
                            'armys[2].deBuffRate':-60,    
                         },
                      },
                   }],
                },
             ],
         },
         {   
             'passive':[
                {
		   'info':'sp3323_info_2',
                   'rslt':{'powerRate':200,'powerBase':200},
                   'special':[{
                      'cond':['enemy','army', 3],
                      'changeEnemy':{
                         'prop':{
                            'armys[2].deBuffRate':220,   
                         },
                      },
                   }],
                },
             ],
         },
         {   
             'passive':[
                {
		   'info':'sp3323_info_3',
                   'rslt':{'powerRate':200,'powerBase':200},
                   'special':[{
                      'changeEnemy':{
                         'prop':{
                            'armys[0].deBuffRate':30,
                            'armys[2].deBuffRate':30,
                         },
                      },
                   }],
                },
             ],
         },
         {   
             'passive':[   
                {
 		   'info':'sp3323_info_4',
                   'rslt':{'powerRate':20,'powerBase':20},
                   'special':[{
                      'cond':[['enemy','army', 3]],
                      'changeEnemy':{
                         'prop':{
                            'armys[2].deBuffRate':240,
                         },
                      },
                   }],
                },
             ],
         },
      ],
   },


   'sp3950':{     
      'type':3,          
      'icon':'item702',                   
      'index':3950,                                
      'merge':0,                                      
      'state':9,                                      
      

      'rslt':{'atkBase':6,'defBase':4},         

      'lv_arr':[  
         { 
            'passive':[
               {
                  'info':'sp3950_info_0',
                  'rslt':{'powerRate':50, 'powerBase':50, },
                  'special':[{  
                     'change':{
                         'prop':{
                           'armys[2].stamina':3,
                           'armys[2].defRate':-2000,
                         },
                     },
                  }],
               },                                 
            ],
         },
         { 
            'passive':[
               {
                  'info':'sp3950_info_2',
                  'rslt':{'powerRate':200, 'powerBase':200, },
                  'special':[{  
                     'changeEnemy':{
                         'prop':{
                           'armys[0].resRate':-20,     
                         },
                     },
                  }],
               },                                  
            ],
         },
         { 
            'passive':[
               {
                  'rslt':{'agi':2,'powerRate':200, 'powerBase':200, },
               },                                        
            ],
         },
         { 
            'passive':[
               {
                  'info':'sp3950_info_2',
                  'rslt':{'powerRate':200, 'powerBase':200, },
                  'special':[{  
                     'changeEnemy':{
                         'prop':{
                           'armys[0].resRate':-20,     
                         },
                     },
                  }],
               },                                 
            ],
         },
         { 
            'passive':[
               {
                  'info':'sp3950_info_4',
                  'rslt':{'powerRate':20, 'powerBase':20, },
                  'special':[{  
                     'changeEnemy':{
                         'prop':{
                           'armys[0].resRate':-40,     
                         },
                     },
                  }],
               },                                   
            ],
         },
      ],
   },

   'sp3952':{     
      'type':3,          
      'icon':'item702',                   
      'index':3952,                                
      'merge':0,                                      
      'state':9,                                      
      

      'rslt':{'atkBase':9,'defBase':2},         

      'lv_arr':[  
         { 
            'passive':[
               {
                  'info':'sp3952_info_0',
                  'rslt':{'powerRate':50, 'powerBase':50, },
                  'special':[{  
                     'changeEnemy':{
                         'prop':{
                           'armys[2].atkRate':-50,    
                           'armys[2].defRate':-2000,    
                         },
                     },
                  }],
               },                                 
            ],
         },
         { 
            'passive':[
               {
                  'info':'sp3952_info_2',
                  'rslt':{'powerRate':200, 'powerBase':200, },
                  'special':[{  
                     'changeEnemy':{
                         'prop':{
                           'armys[2].crit':-40,   
                         },
                     },
                  }],
               },                                  
            ],
         },
         { 
            'passive':[
               {
                  'rslt':{'lead':2,'powerRate':200, 'powerBase':200, },
               },                
            ],
         },
         { 
            'passive':[
               {
                  'info':'sp3952_info_3',
                  'rslt':{'powerRate':200, 'powerBase':200, },
                  'special':[{  
                     'changeEnemy':{
                         'prop':{
                           'armys[2].block':-40,     
                         },
                     },
                  }],
               },                                  
            ],
         },
         { 
            'passive':[
               {
                  'info':'sp3952_info_4',
                  'rslt':{'powerRate':20, 'powerBase':20, },
                  'special':[{  
                     'changeEnemy':{
                         'prop':{
                           'armys[2].dmgRate':-40,   
                         },
                     },
                  }],
               },                                
            ],
         },
      ],
   },
}