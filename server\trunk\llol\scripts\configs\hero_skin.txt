{


     'skin705_2':{      
         
         'open_merge':0,   
         'open_day':27,   
         'price':3000,    
         'passive': {'special':[{
         'change':{
            'skill':{
               'skin705':{
                  'act':[{
                     'priority': 800723,
                     'type': 23,           
                     'src': 2,            
                     'tgt':[0, -2],         
                     'round':{'2':20000},
                     'buff':{'skin705':{'round':2}},
                     'time':0,         
                     'nonSkill':2,   
                     'noBfr':2,
                     'noAft':2,
                     'eff':'effNull',
                     'info':['黄月英皮肤',0],
                  }],
               },
            },
         },
}],
'rslt':{
   'powerRate':200,
   'power':2000},

   'info':'skin705_2_info'}
     },


     'skin722_2':{      
         'first_time':datetime.datetime(2020,5,25,200,0),
         'open_merge':0,   
         'open_day':27,   
         'price':3000,    
         'passive': {'special':[{
         'change':{
            'skill':{
               'skin722':{
                  'act':[{
                     'priority': 800722,
                     'type': 0,           
                     'src': 2,            
                     'tgt':[0, -8],        
                     'round':{'2':20000},
                     'buff':{'skin722':{'round':2}},
                     'time':0,        
                     'nonSkill':2,  
                     'noBfr':2,
                     'noAft':2,
                     'eff':'effNull',
                     'info':['张辽皮肤',0],
                  }],
               },
            },
         },
}],
'rslt':{
   'powerRate':200,
   'power':2000},

   'info':'skin722_2_info'}
     },
     'skin706_2':{      
         
         'open_merge':0,   
         'open_day':27,   
         'price':3000,    
         'passive': {'rslt':{'str':2,'power':2000},}
     },
     'skin722_2':{      
         
         'open_merge':0,   
         'open_day':27,   
         'price':3000,    
         'passive': {
  'cond':['army[2].type', '=',2],
  'rslt':{'army[2].dmg':50,'powerRate':200,'power':2000},}
     },
     'skin703_2':{      
         'first_time':datetime.datetime(2020,5,9,200,0),
         'open_merge':0,   
         'open_day':27,   
         'price':3000,    
         'passive': {'rslt':{'cha':2,'power':2000},}
     },
     'skin727_2':{      
         
         'open_merge':0,   
         'open_day':27,   
         'price':3000,    
         'passive': {'special':[{
         'change':{
            'skill':{
               'skin727':{
                  'act':[{
                     'priority': 800727,
                     'type': 0,           
                     'src': 2,             
                     'tgt':[0, -8],         
                     'round':{'2':20000},
                     'buff':{'skin727':{'round':2}},
                     'time':0,        
                     'nonSkill':2,    
                     'noBfr':2,
                     'noAft':2,
                     'eff':'effNull',
                     'info':['郭嘉皮肤',0],
                  }],
               },
            },
         },
}],
'rslt':{
   'powerRate':200,
   'power':2000},

   'info':'skin727_2_info'}
     },
     'skin726_2':{      
         
         'open_merge':0,   
         'open_day':27,   
         'price':3000,    
         'passive': {
  'cond':['army[0].type', '=',0],
  'rslt':{'army[0].res':50,'powerRate':200,'power':2000},}
     },
     'skin7200_2':{      
         
         'open_merge':0,   
         'open_day':27,   
         'price':3000,    
         'passive': {'special':[{
         'change':{
            'skill':{
               'skin7200':{
                  'act':[{
                     'priority': 8007200,
                     'type': 0,          
                     'src': 2,            
                     'tgt':[0, -8],           
                     'round':{'3':20000},
                     'buff':{'skin7200':{}},
                     'time':0,      
                     'nonSkill':2,   
                     'noBfr':2,
                     'noAft':2,
                     'eff':'effNull',
                     'info':['孙策皮肤',0],
                  }],
               },
            },
         },
}],
'rslt':{
   'powerRate':200,
   'power':2000},

   'info':'skin7200_2_info'}
     },
     'skin708_2':{      
         
         'open_merge':0,   
         'open_day':27,   
         'price':3000,    
         'passive': {'rslt':{'str':2,'power':2000},}
     },
     'skin762_2':{      
         
         'open_merge':0,   
         'open_day':27,   
         'price':4000,    
         'passive': {
  'cond':['army[2].type', '=',3],
  'rslt':{'army[2].dmg':50,'powerRate':200,'power':2000},}
     },
     'skin709_2':{      
         
         'open_merge':0,   
         'open_day':27,   
         'price':4000,    
         'passive': {'special':[{
         'change':{
            'skill':{
               'skill237.act[0].dmgReal':200,
               'skill237.act[0].dmgScale':40,
               'skill226.act[0].dmgReal':200,
               'skill226.act[0].dmgScale':40,
            },
         },
}],
'rslt':{
   'powerRate':200,
   'power':2000},
   'info':'skin709_2_info'}
     },
     'skin723_2':{      
         
         'open_merge':0,   
         'open_day':27,   
         'price':4000,    
         'passive': {'rslt':{'dmg':20,'powerRate':200,'power':2000},}
     },
     'skin724_2':{      
         
         'open_merge':0,   
         'open_day':27,   
         'price':5000,    
         'passive': {'rslt':{'agi':2,'power':2000},}
     },

     'skin724_2':{      
         
         'open_merge':0,   
         'open_day':38,   
         'price':9999,    
         'passive': {'rslt':{'agi':2,'atk':250,'power':200},}
     },

     'skin723_2':{      
         'first_time':datetime.datetime(2020,6,5,200,0),
         'open_merge':0,   
         'open_day':27,   
         'price':4000,    
         'passive': {'special':[{
         'change':{
            'skill':{
               'skin723':{
                  'act':[{
                     'priority': 800723,
                     'type': 23,           
                     'src': 2,            
                     'tgt':[0, -2],         
                     'round':{'2':20000},
                     'buff':{'skin723':{'round':2}},
                     'time':0,         
                     'nonSkill':2,   
                     'noBfr':2,
                     'noAft':2,
                     'eff':'effNull',
                     'info':['董卓皮肤',0],
                  }],
               },
            },
         },
}],
'rslt':{
   'powerRate':200,
   'power':2000},

   'info':'skin723_2_info'}
     },
     'skin707_2':{      
         'first_time':datetime.datetime(2020,5,29,200,0),
         'open_merge':0,   
         'open_day':27,   
         'price':4000,    
         'passive': {'rslt':{'lead':2,'power':2000},}
     },
     'skin728_2':{      
         'first_time':datetime.datetime(2020,5,22,200,0),
         'open_merge':0,   
         'open_day':27,   
         'price':4000,    
         'passive': {'rslt':{'dmgSkill':50,'powerRate':200,'power':2000},}
     },
     'skin770_2':{      
         
         'open_merge':-2,   
         'open_day':27,   
         'price':3000,    
         'passive': {'rslt':{'crit':23},}
     },
     'skin720_2':{      
         'first_time':datetime.datetime(2020,6,29,200,0),  
         'open_merge':0,   
         'open_day':27,   
         'price':4000,    
         'passive': {
  'cond':['army[0].type', '=',2],
  'rslt':{'army[0].dmg':50,'powerRate':200,'power':2000},}
     },
     'skin722_2':{      
         
         'open_merge':-2,   
         'open_day':27,   
         'price':3000,    
         'passive': {'rslt':{'crit':25},}
     },
     'skin722_2':{      
         'first_time':datetime.datetime(2020,6,24,200,0),        
         'open_merge':0,   
         'open_day':27,   
         'price':4000,    
         'passive': {'special':[{
         'change':{
            'skill':{
               'skin722':{
                  'act':[{
                     'priority': 800722,
                     'type': 0,           
                     'src': 2,            
                     'tgt':[0, -8],        
                     'round':{'3':20000},
                     'buff':{'skin722':{}},
                     'time':0,        
                     'nonSkill':2,  
                     'noBfr':2,
                     'noAft':2,
                     'eff':'effNull',
                     'info':['蔡文姬皮肤',0],
                  }],
               },
            },
         },
}],
'rslt':{
   'powerRate':200,
   'power':2000},

   'info':'skin722_2_info'}
     },
     'skin772_2':{      
         
         'open_merge':2,   
         'open_day':30,   
         'price':4000,    
         'passive': {'special':[{
         'change':{
            'skill':{
               'skill225.act[0].dmgReal':200,
               'skill225.act[0].dmgScale':40,
            },
         },
}],
'rslt':{
   'powerRate':200,
   'power':2000},
   'info':'skin772_2_info'}
     },
     'skin773_2':{      
         
         'open_merge':0,   
         'open_day':27,   
         'price':3000,    
         'passive': {'rslt':{'crit':20},}
     },
     'skin774_2':{      
         
         'open_merge':-2,   
         'open_day':27,   
         'price':3000,    
         'passive': {'rslt':{'crit':22},}
     },
     'skin775_2':{      
         
         'open_merge':0,   
         'open_day':27,   
         'price':4000,    
         'passive': {'rslt':{'dmgSkill':50,'powerRate':200,'power':2000},}
     },
     'skin776_2':{      
         
         'open_merge':0,   
         'open_day':27,   
         'price':4000,    
         'passive': {
  'cond':['army[0].type', '=',0],
  'rslt':{'army[0].dmg':50,'powerRate':200,'power':2000},}
     },

     'skin777_2':{      
         
         'open_merge':0,   
         'open_day':27,   
         'price':4000,    
         'passive': {
  'cond':['army[0].type', '=',0],
  'rslt':{'army[0].dmg':50,'powerRate':200,'power':2000},}
     },
     'skin778_2':{      
         
         'open_merge':-2,   
         'open_day':27,   
         'price':3000,    
         'passive': {'rslt':{'crit':25},}
     },
     'skin779_2':{      
         
         'open_merge':-2,   
         'open_day':27,   
         'price':3000,    
         'passive': {'rslt':{'crit':26},}
     },
     'skin780_2':{      
         
         'open_merge':0,   
         'open_day':27,   
         'price':3000,    
         'passive': {'special':[{
         'change':{
            'skill':{
               'skill239.act[0].dmgReal':50,
               'skill239.act[0].ignDef':60,
            },
         },
}],
'rslt':{
   'powerRate':30,
   'power':2000},
   'info':'skin780_2_info'}
     },
     'skin775_2':{      
         
         'open_merge':-2,   
         'open_day':27,   
         'price':3000,    
         'passive': {'rslt':{'crit':28},}
     },
     'skin792_2':{      
         
         'open_merge':-2,   
         'open_day':27,   
         'price':3000,    
         'passive': {'rslt':{'crit':29},}
     },
     'skin792_2':{      
         
         'open_merge':-2,   
         'open_day':27,   
         'price':3000,    
         'passive': {'rslt':{'crit':30},}
     },
     'skin762_2':{      
         
         'open_merge':2,   
         'open_day':8,   
         'price':5000,    
         'passive': {
            'info':'skin762_2_info',
            'rslt':{
                'powerRate':200,
                'power':2000
            },
            'special':[{    
               'priority':-600,
               'change':{
                  'skill':{
                     'hero762.act[0].cond[2]':50,     
                  },
               },
            }],
         }
     },
     'skin762_2':{      
         
         'open_merge':2,   
         'open_day':2,   
         'price':7000,    
         'passive': {
            'info':'skin762_2_info',
            'rslt':{
                'army[2].atk':400,
                'powerRate':50,
                'power':2000
            },
            'special':[{    
               'priority':-600,
               'change':{
                  'skill':{
                     'hero762.act[0].cond[2]':50,     
                  },
               },
            }],
         }
     },
     'skin763_2':{      
         
         'open_merge':2,   
         'open_day':8,   
         'price':5000,    
         'passive': {
            'info':'skin763_2_info',
            'rslt':{
                'powerRate':200,
                'power':2000
            },
            'special':[{    
               'change':{
                  'prop':{
                      'armys[0].others.elementCure':30,     
                      'armys[2].others.elementCure':30,     
                      'armys[0].others.elementSummon':30,     
                      'armys[2].others.elementSummon':30,     
                  },
               },
            }],
         }
     },
     'skin763_2':{      
         
         'open_merge':2,   
         'open_day':2,   
         'price':7000,    
         'passive': {
            'info':'skin763_2_info',
            'rslt':{
                'resFinal':50,
                'powerRate':50,
                'power':2000
            },
            'special':[{    
               'change':{
                  'prop':{
                      'armys[0].others.elementCure':30,     
                      'armys[2].others.elementCure':30,     
                      'armys[0].others.elementSummon':30,     
                      'armys[2].others.elementSummon':30,     
                  },
               },
            }],
         }
     },
     'skin764_2':{      
         
         'open_merge':2,   
         'open_day':8,   
         'price':5000,    
         'passive': {
            'info':'skin764_2_info',
            'rslt':{
                'powerRate':200,
                'power':2000
            },
            'special':[{    
               'change':{
                  'skill':{
                     'skill244.act[0].ignDef':40,
                     'skill244.act[0].dmgReal':50,
                  },
               },
            }],
         }
     },
     'skin764_2':{      
         
         'open_merge':2,   
         'open_day':2,   
         'price':7000,    
         'passive': {
            'info':'skin764_2_info',
            'rslt':{
                'powerRate':50,
                'power':2000
            },
            'special':[{    
               'change':{
                  'skill':{
                     'skill244.act[0].ignDef':40,
                     'skill244.act[0].dmgReal':50,
                     'skill244.act[0].dmgScale':80,
                  },
               },
            }],
         }
     },
     'skin769_2':{      
         
         'open_merge':-2,   
         'open_day':27,   
         'price':3000,    
         'passive': {'rslt':{'crit':34},}
     },
     'skin765_2':{      
         
         'open_merge':-2,   
         'open_day':27,   
         'price':3000,    
         'passive': {'rslt':{'crit':95},}
     },
     'skin767_2':{      
         
         'open_merge':-2,   
         'open_day':27,   
         'price':3000,    
         'passive': {'rslt':{'crit':37},}
     },
     'skin766_2':{      
         
         'open_merge':-2,   
         'open_day':27,   
         'price':3000,    
         'passive': {'rslt':{'crit':38},}
     },
     'skin783_2':{      
         
         'open_merge':-2,   
         'open_day':27,   
         'price':3000,    
         'passive': {'rslt':{'crit':39},}
     },
     'skin784_2':{      
         
         'open_merge':-2,   
         'open_day':27,   
         'price':3000,    
         'passive': {'rslt':{'crit':40},}
     },
     'skin787_2':{      
         
         'open_merge':-2,   
         'open_day':27,   
         'price':3000,    
         'passive': {'rslt':{'crit':42},}
     },
     'skin789_2':{      
         
         'open_merge':-2,   
         'open_day':27,   
         'price':3000,    
         'passive': {'rslt':{'crit':42},}
     },
     'skin782_2':{      
         
         'open_merge':-2,   
         'open_day':27,   
         'price':3000,    
         'passive': {'rslt':{'crit':43},}
     },
     'skin788_2':{      
         
         'open_merge':-2,   
         'open_day':27,   
         'price':3000,    
         'passive': {'rslt':{'crit':44},}
     },
     'skin782_2':{      
         
         'open_merge':-2,   
         'open_day':27,   
         'price':3000,    
         'passive': {'rslt':{'crit':45},}
     },
     'skin785_2':{      
         
         'open_merge':-2,   
         'open_day':27,   
         'price':3000,    
         'passive': {'rslt':{'crit':46},}
     },
     'skin786_2':{      
         
         'open_merge':-2,   
         'open_day':27,   
         'price':3000,    
         'passive': {'rslt':{'crit':47},}
     },
     'skin793_2':{      
         
         'open_merge':-2,   
         'open_day':27,   
         'price':3000,    
         'passive': {'rslt':{'crit':48},}
     },
     'skin794_2':{      
         
         'open_merge':-2,   
         'open_day':27,   
         'price':3000,    
         'passive': {'rslt':{'crit':49},}
     },
     'skin795_2':{      
         
         'open_merge':-2,   
         'open_day':27,   
         'price':3000,    
         'passive': {'rslt':{'crit':50},}
     },
     'skin796_2':{      
         
         'open_merge':-2,   
         'open_day':27,   
         'price':3000,    
         'passive': {'rslt':{'crit':52},}
     },
     'skin797_2':{      
         
         'open_merge':-2,   
         'open_day':27,   
         'price':3000,    
         'passive': {'rslt':{'crit':52},}
     },
     'skin798_2':{      
         
         'open_merge':-2,   
         'open_day':27,   
         'price':3000,    
         'passive': {'rslt':{'crit':53},}
     },
     'skin775_2':{      
         
         'open_merge':-2,   
         'open_day':27,   
         'price':20000,    
         'passive': {'rslt':{'crit':75},}
     },
     'skin729_2':{      
         
         'open_merge':0,   
         'open_day':27,   
         'price':20000,    
         'passive': {'rslt':{'agi':2,'army[2].atk':50,'powerRate':200,'power':2000},}
     },
     'skin702_2':{      
         'first_time':datetime.datetime(2020,6,22,200,0),
         'open_merge':0,   
         'open_day':27,   
         'price':20000,    
         'passive': {
  'cond':['army[0].type', '=',0],
  'rslt':{'army[0].dmg':50,'powerRate':200,'power':2000},
}
     },
     'skin702_2':{      
         
         'open_merge':0,   
         'open_day':27,   
         'price':20000,    
         'passive': {
  'cond':['army[0].type', '=',2],
  'rslt':{'army[0].res':50,'powerRate':200,'power':2000},
}
     },
     'skin726_2':{      
         
         'open_merge':-2,   
         'open_day':27,   
         'price':20000,    
         'passive': {'rslt':{'crit':56},}
     },
     'skin728_2':{      
         
         'open_merge':0,   
         'open_day':27,   
         'price':2000,    
         'passive': {
  'cond':['army[2].type', '=',2],
  'rslt':{'army[2].res':50,'powerRate':200,'power':2000},}
     },
     'skin736_2':{      
         
         'open_merge':0,   
         'open_day':27,   
         'price':2000,    
         'passive': {
  'cond':['army[2].type', '=',2],
  'rslt':{'army[2].dmg':50,'powerRate':200,'power':2000},
}
     },
     'skin734_2':{      
         
         'open_merge':0,   
         'open_day':27,   
         'price':2000,    
         'passive': {'special':[{
         'change':{
            'skill':{
               'skill232.act[0].dmgReal':200,
               'skill232.act[0].dmgScale':40,

            },
         },
}],
'rslt':{
   'powerRate':200,
   'power':2000},
   'info':'skin734_2_info'}
     },
     'skin732_2':{      
         
         'open_merge':0,   
         'open_day':27,   
         'price':2000,    
         'passive': {'rslt':{'str':2,'power':2000},}
     },
     'skin724_2':{      
         
         'open_merge':0,   
         'open_day':27,   
         'price':4000,    
         'passive': {'special':[{  
         'priority':-2000,
         'change':{
               'skill':{
                  'skill240.act[0].buff.buffShield.shield.value':7,
                  'skill240.act[0].buff.buffShield.shield.hpmRate':2.5,
               },
         },
      }],
   'rslt':{'powerRate':200,'power':2000},
   'info':'skin724_2_info'},
     },
     'skin757_2':{      
         
         'open_merge':0,   
         'open_day':27,   
         'price':20000,    
         'passive': {
   'cond':['army[0].type', '=',0],
   'rslt':{'army[0].dmg':50,'powerRate':200,'power':2000},}
     },
     'skin733_2':{      
         
         'open_merge':0,   
         'open_day':27,   
         'price':20000,    
         'passive': {
   'cond':['army[0].type', '=',0],
   'rslt':{'army[0].crit':50,'powerRate':200,'power':2000},}
     },
     'skin725_2':{      
         
         'open_merge':0,   
         'open_day':27,   
         'price':3000,    
         'passive': {
   'rslt':{'agi':2,'powerRate':200,'power':2000},}
     },
     'skin725_2':{      
         
         'open_merge':0,   
         'open_day':27,   
         'price':20000,    
         'passive': {
   'rslt':{'lead':2,'powerRate':200,'power':2000},}
     },
     'skin704_2':{      
         
         'open_merge':0,   
         'open_day':27,   
         'price':3000,    
         'passive': {'special':[{  
         'change':{
               'skill':{
                  'skill209.act[0].ignDef':50,
               },
         },
      }],
   'rslt':{'powerRate':200,'power':2000},
   'info':'skin704_2_info'},
     },
     'skin768_2':{      
         
         'open_merge':0,   
         'open_day':27,   
         'price':4000,    
         'passive': {   
   'rslt':{'str':2,'powerRate':200,'power':2000},}
     },
     'skin729_2':{      
         
         'open_merge':0,   
         'open_day':27,   
         'price':3000,    
         'passive': {   
   'rslt':{'cha':2,'powerRate':200,'power':2000},}
     },
     'skin772_2':{      
         
         'open_merge':0,   
         'open_day':27,   
         'price':4000,    
         'passive': {   
   'rslt':{'dmgSex2':40,'powerRate':200,'power':2000},}
     },
     'skin722_2':{      
         
         'open_merge':0,   
         'open_day':27,   
         'price':3000,    
         'passive': {'special':[{  
           'change':{
              'prop':{
                'armys[0].resRealRate':80,
                'armys[2].resRealRate':80,
              },
           },
      }],
   'rslt':{'powerRate':200,'power':2000},
   'info':'skin722_2_info'},
     },






}