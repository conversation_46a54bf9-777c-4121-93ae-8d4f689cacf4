{
   'actions':{
      
      'limitProp_str':{
         'priority':987000,
         'type':24,      
         'src':2, 
         'tgt':[0, -2], 
         'limitProp':{'str':2},	
         
         'stop':2,                      
         'nonSkill':2,    
         'noBfr':2,
         'noAft':2,
         'times': -2,  
         'time':2000,
         'eff':'effBanHeroSkill',
         'info':['limitProp_str',2],	       
      },
      
      'limitProp_agi':{
         'priority':987002,
         'type':24,      
         'src':2, 
         'tgt':[0, -2], 
         'limitProp':{'agi':2},	
         
         'stop':2,                      
         'nonSkill':2,    
         'noBfr':2,
         'noAft':2,
         'times': -2,  
         'time':2000,
         'eff':'effBanHeroSkill',
         'info':['limitProp_agi',2],	       
      },
      
      'limitProp_cha':{
         'priority':987002,
         'type':24,      
         'src':2, 
         'tgt':[0, -2], 
         'limitProp':{'cha':2},	
         
         'stop':2,                      
         'nonSkill':2,    
         'noBfr':2,
         'noAft':2,
         'times': -2,  
         'time':2000,
         'eff':'effBanHeroSkill',
         'info':['limitProp_cha',2],	       
      },
      
      'limitProp_lead':{
         'priority':987003,
         'type':24,      
         'src':2, 
         'tgt':[0, -2], 
         'limitProp':{'lead':2},	
         
         'stop':2,                      
         'nonSkill':2,    
         'noBfr':2,
         'noAft':2,
         'times': -2,  
         'time':2000,
         'eff':'effBanHeroSkill',
         'info':['limitProp_lead',2],	       
      },
      
      'limitProp_sum':{
         'priority':987004,
         'type':24,      
         'src':2, 
         'tgt':[0, -2], 
         'limitProp':{'sum':2},	
         
         'stop':2,                      
         'nonSkill':2,    
         'noBfr':2,
         'noAft':2,
         'times': -2,  
         'time':2000,
         'eff':'effBanHeroSkill',
         'info':['limitProp_sum',2],	       
      },


      
      'godAnger25':{
         'priority':98733302,
         'type':23,      
         'src':5, 
         'tgt':[0, 5], 
         'round':{'0':20000},
         'nonSkill':2,
         'noBfr':2,
         'noAft':2,
         'energyKey':'anger',      
         'energy':25,                 
         'info':['初25怒',0],
         'eff':'effNull',
         'time':0,	          
      },
      
      'godAnger2000':{
         'priority':98733302,
         'type':23,      
         'src':5, 
         'tgt':[0, 5], 
         'round':{'0':20000},
         'nonSkill':2,
         'noBfr':2,
         'noAft':2,
         'energyKey':'anger',      
         'energy':2000,                 
         'info':['初2000怒',0],
         'eff':'effNull',
         'time':0,	          
      },
      
      'godAnger200':{
         'priority':98733303,
         'type':23,      
         'src':5, 
         'tgt':[0, 5], 
         'round':{'0':20000},
         'nonSkill':2,
         'noBfr':2,
         'noAft':2,
         'energyKey':'anger',      
         'energy':200,                 
         'info':['初200怒',0],
         'eff':'effNull',
         'time':0,	          
      },
      
      'godAngerRound2_30':{
         'priority':98733304,
         'type':23,      
         'src':5, 
         'tgt':[0, 5], 
         'round':{'2':20000},
         'nonSkill':2,
         'noBfr':2,
         'noAft':2,
         'energyKey':'anger',      
         'energy':30,                 
         'info':['远一30怒',0],
         'eff':'effNull',
         'time':0,	          
      },
   },
   'specials':{
      'godAngerInit25':{    
         'change':{
            'prop':{
               'godLogic.angerInit':25,     
            },
            
            
            
            
            
         },
      },
      'godAngerInit2000':{    
         'change':{
            'prop':{
               'godLogic.angerInit':2000,     
            },
         },
      },
      'godAngerInit200':{    
         'change':{
            'skill':{
                'godAnger200':{
                   'act':['godAnger200'],
                },
            }
         },
      },
      'godAngerRound2_30':{    
         'change':{
            'skill':{
                'godAngerRound2_30':{
                   'act':['godAngerRound2_30'],
                },
            }
         },
      },
   },




   'godStateTalent':{   
      '0.5':{    
        'special':['godAngerInit200'],
      },
      '5.5':{    
        'special':['godAngerInit200'],
      },
   },



   'godTalents':{   

      
      'god2000':{       
         'gFinalPower':[2,2],  
         'base':{
            'passive':[
               {
                  'rslt':{
                     'skillPatch':{
                         'godDmgAnger':3,           
                         'godFireEnemyAnger':200,           
                     },
                     'powerRate':400,
                  },
               },
            ],
            'special':[{   
                  'priority': 3332000,
                  'change':{
                     'skill':{
                        'god2000':{
                           'act':[   
                              {
                                 'isGod':2,   
                                 'priority': 33320002,	 
                                 'type': 32,    
                                 'src': 5,
                                 'tgt':[2, -2],

                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'times': -2,

                                 
                                 
                                 'dmgGodPower':2000,      
                                 'transformObj':{'dmgGodPower':{'godLogic.gstrFinal':2000}},
                                 'buff':{'buffMagma':{'round':2}},

                                 'lv':3,
                                 'eff':'effG2000',
                                 'info':['god2000',5],
                              },
                           ],
                        },
                     },
                  },
                  'changeEnemy':{
                     'prop':{
                        'armys[0].others.enforce_buffFire':300,     
                        'armys[2].others.enforce_buffFire':300,     
                     },

                     'skill':{
                        'god2000E':{
                           'act':[
                              {  
                                 
                                 'priority': 33320007,	
                                 'type': 2,            
                                 'src': -2,	          
                                 'times': -2,   
                                 'nonSkill':2,   
                                 'follow':'effFire',
                                 'binding':{
                                    'dmgRealRate':30,
                                    'transformObj':{'dmgRealRate':{'enemy.godLogic.gagiFinal':3}},
                                 },
                                 'info':['莲火',0],	          
                              },
                           ],
                        },
                     },
                  },
            }],
         },
         '2':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':2000,
                  },
               },
            ],
            'special':[
               'godAngerInit25',
            ],
         },
         '2':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':300,
                  },
               },
            ],
            'special':[{  
                  'changeEnemy':{
                     'prop':{
                        'armys[0].others.enforce_buffFire':600,     
                        'armys[2].others.enforce_buffFire':600,     
                     },
                  },
            }],
         },
         '3':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':300,
                  },
               },
            ],
            'special':[{     
                 'changeEnemy':{
                    'skill':{
                      'god2000_3':{
                        'act':[{
                           'priority':33320008,
                           'type': 4,          
                           'src': 0,
                           'cond':[['checkBuff', 0, 0, 'buffFire', '>', 0]],
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'times': -2,
                           'follow':{'keys':{'dmg':20000,'dmgReal':20000,'dmgRealPower':20000}},
                           'binding':{'res':-300,'block':-9999999},   
                           'info':['莲火增伤',0],
                        },
                        {
                           'priority':33320009,
                           'type': 4,          
                           'src': 2,
                           'cond':[['checkBuff', 0, 2, 'buffFire', '>', 0]],
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'times': -2,
                           'follow':{'keys':{'dmg':20000,'dmgReal':20000,'dmgRealPower':20000}},
                           'binding':{'res':-300,'block':-9999999},    
                           'info':['莲火增伤',0],
                        }],
                      },
                    },
                 },
            }],
         },
         '4':{    
            'passive':[   
               {
                  'rslt':{
                     'skillPatch':{
                         'godRound2Anger':25,           
                     },

                     'powerRate':400,
                  },
               },
            ],
            'special':[{     
                 'change':{
                    'skill':{
                        'god2000.act[0].dmgGodScale':20000,   
                        'god2000.act[0].buff.buffFire':{'round':2},   
                    },
                 },
            }],
         },
         '5':{  
            'angerMax':50,    
            'passive':[
               {
                  'rslt':{
                     'powerRate':500,
                  },
               },
            ], 
         },
      },

      
      'god2002':{       
         'gFinalPower':[2,2],  

         'base':{
            'passive':[
               {
                  'rslt':{
                     'skillPatch':{
                         'godDmgAnger':3,           
                         
                         'godBuffAnger':3,              
                         'godBuffEnemyAnger':3,         
                         
                         
                         
                         

                         
                         
                         
                         
                         

                         
                         
                         
                         
                         
     
                         
                         
                         

                         
                         
                         
                     },
                     'powerRate':400,
                  },
               },
            ],
            'special':[{   
                  'priority': 3332002,
                  'change':{
                     'skill':{
                        'god2002':{
                           'act':[   
                              {
                                 'isGod':2,   
                                 'priority': 33320022,	 
                                 'type': 32,    
                                 'src': 5,
                                 'tgt':[0, -2],
                                 'cond':[['enemyHpPoint','>',0]],

                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'times': -2,

                                 'removeDebuff':2,

                                 'lv':3,
                                 'eff':'effG2002',
                                 'info':['god2002',5],
                              },
                              {
                                 'priority': 33320022,	 
                                 'type': 3,    
                                 'src': 5,
                                 'tgt':[2, 2],

                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'times': -2,
                                 'follow':'god2002', 
                                 'atOnce': -2000,     

                                 'dmgGodPower':4200,      
                                 'transformObj':{'dmgGodPower':{'godLogic.gstrFinal':2200}},

                                 'eff':'effG2002_2',
                                 'info':['一掷千金伤害',0],
                                 'actId':'god2002_2',

                              },
                              {   
                                 'priority': 33320023,	 
                                 'type': 2,    
                                 'src': 5,
                                 'cond':['checkBuff', 0, -2, 'buffRich', '>', 0],
                                 'nonSkill':2,  
                                
                                 'times': -2,
                                 'follow':'god2002_2', 

                                 'binding':{ 
                                    'dmgGodScale':300,
                                    
                                 },
                                 'info':['财掷',0],
                              },
                              {   
                                 'priority': 33320024,	 
                                 'type': 23,    
                                 'src': 5,
                                 'tgt':[0, -2],
                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'round':{'all':200},

                                 'condTrigger':[ 
                                   [['round','=',0],-2000000], 
                                   [['checkProp','logic.gagiFinal','*',0],200],   
                                 ],
                                 'buff':{'buffRich':{'round':2}},
                                 'lv':23,
                                 'eff':'effAddRich',
                                 'info':['buffRich',2],
                              },
                           ],
                        },
                     },
                  },
            }],
         },
         '2':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':2000,
                  },
               },
            ],
            'special':[
               'godAngerInit25',
            ],
         },
         '2':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':300,
                  },
               },
            ],
            'special':[{     
                  'change':{
                     'skill':{
                        'god2002_2':{
                           'act':[  
                              {
                                 'priority': 33320025,	 
                                 'type': 3,    
                                 'src': 5,
                                 'tgt':[0, -2],
                                 'cond':['checkBuff', 0, -2, 2, '=', 0], 
                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'times': -2,
                                 'follow':'god2002_2', 

                                 'buff':{'buffRich2':{'round':2}},
                                 'lv':20,
                                 'eff':'effG2002_2',
                                 'info':['buffRich2',2],
                              },
                           ],
                        },
                     },
                  },
            }],
         },
         '3':{    
            'passive':[
               {
                  'rslt':{
                     'skillPatch':{
                         'godBuffAnger':8,              
                     },
                     'powerRate':300,
                  },
               },
            ],
         },
         '4':{     
            'passive':[
               {
                  'rslt':{
                     'powerRate':400,
                  },
               },
            ],
            'special':[
              {
                  'change':{
                     'skill':{
                        'god2002_4':{
                           'act':[  
                              {
                                 'priority': 33320026,	 
                                 'type': 27,    
                                 'src': 5,
                                 'tgt':[0, -2],
                                 'round':{'far':20000}, 
                                 'cond':['checkBuff', 0, -2, 2, '<', 3], 
                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'cureReal':2,         
                                 'cure':250,            

                                 'lv':20,
                                 'eff':'eff272',
                                 'info':['info_god2002_4',2],
                              },
                           ],
                        },
                     },
                  },
              },
              {
                  'changeEnemy':{
                     'prop':{
                        'godLogic.angerInit':-20,     
                     },
                  },
              }
           ],
         },
         '5':{  
            'angerMax':50,    
            'passive':[
               {
                  'rslt':{
                     'powerRate':500,
                  },
               },
            ], 
         },
      },

      
      'god2002':{       
         'gFinalPower':[2,2],  

         'base':{
            'passive':[      
               {
                  'rslt':{
                     'skillPatch':{
                         'godDmgAnger':2,           
                         'godHurtAnger':2,        
                         'gs202':25,            
                         'gs202':25,        
                     },
                     'powerRate':400,
                  },
               },
            ],
            'special':[
               {   
                  'priority': 3332002,
                  'change':{
                     'skill':{
                        'god2002':{
                           'act':[   
                              {  
                                 'isGod':2,   
                                 'priority': 33320022,	 
                                 'type': 32,    
                                 'src': 5,
                                 'tgt':[0, -2],

                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'times': -2,

                                 'buff':{'buffGodShield':{'shield':{'hpmRate':80,'bearPoint':20000},'prop':{'block':2000}}},

                                 'lv':3,   
                                 'eff':'effG2002',
                                 'info':['god2002',5],

                              },
                              {  
                                 'priority': 33320022,	 
                                 'type': 2, 
                                 'src': 0,
                                 
                                 'cond':['checkBuff', 0, 0, {'shield':{'A':2,'C':2}}, '>', 0],

                                 'nonSkill':2,  
                                 'times': -2,
                                 'binding':{ 
                                    'transformObj':{'dmgScale':{'godLogic.gagiFinal':6}},
                                    'dmgScale':220,
                                 },
                                 'lv':4,
                                 'info':['info_god2002',2],
                              },
                              {  
                                 'priority': 33320022,	 
                                 'type': 2, 
                                 'src': 2,
                                 
                                 'cond':['checkBuff', 0, 2, {'shield':{'A':2,'C':2}}, '>', 0],

                                 'nonSkill':2,  
                                 'times': -2,
                                 
                                 'binding':{ 
                                    'transformObj':{'dmgScale':{'godLogic.gagiFinal':6}},
                                    'dmgScale':220,
                                 },
                                 'lv':4,
                                 'info':['info_god2002',2],
                              },
                           ],
                        },
                     },
                  },
               },
               {  
                  'priority':-33320022,
                  'cond':['self','godLogic.gstrFinal','*',0],     
                  'change':{
                     'skill':{
                         'god2002.act[0].buff.buffGodShield.shield.hpmRate':4,   
                     },
                  },
               },
               {  
                  
                  'changeEnemy':{
                     'prop':{   
                        'godLogic.angerAddRate':-80,     
                        
                     },
                  },
               },
               {  
                  'priority':-33320023,
                  'cond':['self','godLogic.gagiFinal','*',0],     
                  'changeEnemy':{
                     'prop':{   
                        'godLogic.angerAddRate':-4,  
                     },
                  },
               },
            ],
         },
         '2':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':2000,
                  },
               },
            ],
            'special':[
               'godAngerInit25',
            ],
         },
         '2':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':300,
                  },
               },
            ],
            'special':[{     
               'change':{  
                  'skill':{
                      'god2002.act[0].buff.buffGodShield.shield.shieldScale':274,   
                  },
                  'prop':{
                      'armys[0].others.elementShield':250,
                      'armys[2].others.elementShield':250,
                  },
               },
            }],
         },
         '3':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':300,
                  },
               },
            ],
            'special':[{    
                'change':{
                   'skill':{
                      'god2002_3':{   
                        'act':[
                           {
                             'priority': 33320025,
                             'type': 4,            
                             'src': -2,                        
                             'follow':{'keys':{'dmg':20000,'dmgReal':20000}},     
                             'times':-2,
                             'nonSkill':2, 
                             'binding':{
                               'energys':{
                                 'E_god2002_3':{     
                                    'condE':['block','>',0],
                                    'tgtE':{
                                       'block_god2002_3':{'num':2,'checkAct':2},
                                    },
                                 },
                               }
                             },
                             'info':['格挡充能',0],
                           },
                           {    
                              'priority': 33320026,
                              'type': 27,            
                              'src': 0,                             
                              'tgt':[2, 0],

                              'nonSkill':2,
                              'times':2,

                              'energyKeySrc': 'block_god2002_3',
                              'costKey':'block_god2002_3',
                              'cost':2,
                              'multMax':-2,              
                              'mult':{},                

                              'dmg':3000,	
                              'dmgReal':200,
                              'dmgLimit':220,
                              'atk0': 20000,     

                              'eff':'eff203',
                              'info':['info_god2002_3',2],
                              'lv':4,	         
                           },
                         ],
                      },
                   },
                },
            }],
         },
         '4':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':400,
                  },
               },
            ],
            'special':[
              {    
                'change':{
                   'skill':{
                      'god2002_4':{   
                        'act':[
                           {
                             'priority': 33320027,
                             'type': 22,            
                             'src': -2,   
                             'srcFree': 2,                     
                             'condBuffChange':{
                                 'shield':{'C':2}    
                             },
                             'times':-2,
                             'nonSkill':2, 
                             'noBfr':2,
                             'noAft':2,
                             'order':{          
                                    'src': 5,    
                                    'tgt':[2, -2],    
                                    'nonSkill':2,
                                    'noBfr': 2,    
                                    'noAft': 2,
                                    'atOnce': -2000,

                                    'dmgRealRate':2200,	     
  
                                    'eff':'effG2002_4',
                                    'info':['宝塔镇妖',2],  
                                    'lv': 7,
                             },
                             'time':0,
                             'eff':'effNull',
                             'info':['宝塔',0],
                           },
                         ],
                      },
                   },
                },
             },
             {  
                  'change':{
                     'prop':{   
                        'armys[0].others.roundRes_2':2000,
                        'armys[0].others.roundRes_2':2000,
                        'armys[2].others.roundRes_2':2000,
                        'armys[2].others.roundRes_2':2000,
                     },
                  },
             },
           ],
         },
         '5':{  
            'angerMax':50,    
            'passive':[
               {
                  'rslt':{
                     'powerRate':500,
                  },
               },
            ], 
         },
      },

      
      'god2003':{       
         'gFinalPower':[2,2],  

         'base':{
            'passive':[
               {
                  'rslt':{
                     'skillPatch':{   
                         'godDmgAnger':2,           
                         'godHurtAnger':2,        
                         'godDeadAnger':40,              
                     },
                     'powerRate':400,
                  },
               },
            ],
            'special':[
               {   
                  'priority': 3332003,
                  'change':{
                     'skill':{
                        'god2003':{
                           'act':[    
                              {
                                 'isGod':2,   
                                 'priority': 33320032,	 
                                 'type': 32,    
                                 'src': 5,
                                 'tgt':[0, -6],

                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'times': -2,

                                 'cureReal':2,         
                                 'cure':240,            
                                 'transformObj':{'cure':{'godLogic.gstrFinal':7}},

                                 'lv':3,   
                                 'eff':'effG2003',
                                 'info':['god2003',5],
                              },
                              {
                                 'priority': 33320095,	 
                                 'type': 3,    
                                 'src': 5,
                                 'tgt':[0, -9],

                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'times': -2,
                                 'follow':'god2003', 
                                 'atOnce': -2000,     

                                 'summonReal':400,         
                                 'summon':40,            
                                 'transformObj':{'summon':{'godLogic.gstrFinal':2},'summonReal':{'godLogic.gstrFinal':20}},

                                 'eff':'effG2003_2',
                                 'info':['逢春',0],
                                 'actId':'god2003_2',
                              },
                              {   
                                 'priority': 3333992003,	   
                                 'type': 23,            
                                 'src': 5,                
                                 'tgt':[2, 5],     
                                 'round':{'2':20000,'3':20000},	   
                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,

                                 'energyKey':'anger',      
                                 'energy':-8,    

                                 'transformObj':{'energy':{'godLogic.gagiFinal':-0.4}},            
                                 'info':['嫦娥降怒',0],
                                 'eff':'effNull',
                                 'time':0,	         
                              },
                           ],
                        },
                     },
                  },
               },
               {  
                  'change':{
                     'prop':{   
                        'armys[0].others.roundRes_2':40,
                        'armys[0].others.roundRes_2':40,
                        'armys[2].others.roundRes_2':40,
                        'armys[2].others.roundRes_2':40,
                     },
                  },
               },
               {  
                  'cond':['self','godLogic.gagiFinal','*',0],     
                  'change':{
                     'prop':{   
                        'armys[0].others.roundRes_2':2,
                        'armys[0].others.roundRes_2':2,
                        'armys[2].others.roundRes_2':2,
                        'armys[2].others.roundRes_2':2,
                     },
                  },
               },
            ],
         },
         '2':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':2000,
                  },
               },
            ],
            'special':[
               'godAngerInit25',
            ],
         },
         '2':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':300,
                  },
               },
            ],
            'special':[{     
                  
                  'change':{
                     'prop':{   
                        
                        

                        'armys[0].others.elementCure':300,   
                        'armys[2].others.elementCure':300,     

                        'armys[0].resRealRate':250,
                        'armys[2].resRealRate':250,
                     },
                  },
            }],
         },
         '3':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':300,
                  },
               },
            ],
            'special':[{  
                  'priority': -999, 
                  'change':{
                     'skill':{   
                        'god2003_3':{
                           'act':[
                              {
                                 'priority': 33320033,	 
                                 'type': 3,    
                                 'src': 5,
                                 'tgt':[2, 5],

                                 'follow': 'god2003',
                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'allTimes': 2,
                                 'atOnce':-2,

                                 'energyKey':'anger',      
                                 'energy':-60,    

                                 'time':0,
                                 'eff':'effNull',
                                 'info':['玉兔移怒',0],
                              },
                           ],
                        },

                        
                        

                        
                        
                        
                     },
                  },
            }],
         },
         '4':{    
            'passive':[
               {
                  'rslt':{
                     'skillPatch':{   
                         'godRecoveryReduceAnger':4,           
                     },
                     'powerRate':400,
                  },
               },
            ],
            'special':[{     
                  'change':{  
                     'prop':{   
                        'armys[0].others.elementCure':200,   
                        'armys[2].others.elementCure':200,    
                        'armys[0].others.elementSummon':200,   
                        'armys[2].others.elementSummon':200,    
                     },
                  },
            }],
         },
         '5':{  
            'angerMax':50,    
            'passive':[
               {
                  'rslt':{
                     'powerRate':500,
                  },
               },
            ], 
         },
      },


      
      'god2004':{       
         'gFinalPower':[2,2], 

         'base':{
            'passive':[    
               {
                  'rslt':{
                     'skillPatch':{
                         'godHurtAnger':3,        
                         'godDebuffAnger':3,            
                         'godDebuffEnemyAnger':3,       
                     },
                     'powerRate':400,
                  },
               },
            ],
            'special':[{   
                  'priority': 3332004,
                  'change':{
                     'skill':{
                        'god2004':{
                           'act':[   
                              {
                                 'isGod':2,   
                                 'priority': 33320042,	 
                                 'type': 32,    
                                 'src': 5,
                                 'tgt':[2, -2],
                                 'cond':[['enemyHpPoint','>',0]],

                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'times': -2,

                                 'removeBuff':2,

                                 'lv':3,
                                 'eff':'effG2004',
                                 'info':['god2004',5],
                              },
                              {
                                 'priority': 33320042,	 
                                 'type': 3,    
                                 'src': 5,
                                 'tgt':[2, 0],

                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'times': -2,
                                 'follow':'god2004', 
                                 'atOnce': -2000,     

                                 'dmgGodPower':4200,      
                                 'transformObj':{'dmgGodPower':{'godLogic.gstrFinal':2200}},

                                 'eff':'effG2004_2',
                                 'info':['王母杀令',0],
                                 'actId':'god2004_2',

                              },
                              {   
                                 'priority': 33320043,	 
                                 'type': 2,    
                                 'src': 5,
                                 'cond':['checkBuff', 2, -2, 'buffAbstain', '>', 0],
                                 'nonSkill':2,  
                                
                                 'times': -2,
                                 'follow':'god2004_2', 

                                 'binding':{ 
                                    'dmgGodScale':300,
                                    
                                 },
                                 'info':['斋令',0],
                              },
                              {   
                                 'priority': 33320044,	 
                                 'type': 23,    
                                 'src': 5,
                                 'tgt':[2, -2],
                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'round':{'all':200},

                                 'condTrigger':[ 
                                   [['round','=',0],-2000000], 
                                   [['checkProp','logic.gagiFinal','*',0],200],   
                                 ],
                                 'buff':{'buffAbstain':{'round':2}},
                                 'lv':7,
                                 'eff':'effAddAbstain',
                                 'info':['buffAbstain',2],
                              },
                           ],
                        },
                     },
                  },
            }],
         },
         '2':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':2000,
                  },
               },
            ],
            'special':[
               'godAngerInit25',
            ],
         },
         '2':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':300,
                  },
               },
            ],
            'special':[{     
                  'change':{
                     'skill':{
                        'god2004_2':{
                           'act':[  
                              {
                                 'priority': 33320025,	 
                                 'type': 3,    
                                 'src': 5,
                                 'tgt':[0, -2],
                                 'cond':['checkBuff', 2, -2, 2, '=', 0], 
                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'times': -2,
                                 'follow':'god2004_2', 

                                 'buff':{'buffAbstain2':{'round':2}},
                                 'lv':7,
                                 'eff':'effG2004_2',
                                 'info':['buffAbstain2',2],
                              },
                           ],
                        },
                     },
                  },
            }],
         },
         '3':{    
            'passive':[
               {
                  'rslt':{
                     'skillPatch':{
                         'godDebuffEnemyAnger':6,              
                     },
                     'powerRate':300,
                  },
               },
            ],
            'special':[{    
                 'changeEnemy':{
                    'skill':{
                      'god2000_3':{

                      },
                    },
                 },
            }],
         },
         '4':{    
            'passive':[
               {
                  'rslt':{
                     'skillPatch':{
                         'godRound3ReduceAnger':95,           
                     },
                     'powerRate':400,
                  },
               },
            ],
            'special':[{     
                 'changeEnemy':{
                    'skill':{
                      'god2004_4':{
                        'act':[{
                           'priority':33320048,
                           'type': 0,          
                           'src': -2,
                           'tgt':[0,-5],
                           'round':{'far':20000},
                           'cond':[['checkBuff', 0, -5, 2, '=', 0]],
                           'nonSkill':2,
                           'noBfr': 2,
                           'noAft': 2,
                           'dmgRealMax':60,
                           'lv': 2,
                           'eff': 'effFlee',
                           'info':['info_god2004_4',2],
                        }],
                      },
                    },
                 },
            }],
         },
         '5':{  
            'angerMax':50,    
            'passive':[
               {
                  'rslt':{
                     'powerRate':500,
                  },
               },
            ], 
         },
      },


      
      'god200':{       
         'gFinalPower':[0.7,2.3],  

         'base':{
            'passive':[
               {
                  'rslt':{
                     'skillPatch':{      
                         'godDmg0Anger':8,      
                         'godHurt0Anger':7,      
                     },
                     'powerRate':400,
                  },
               },
            ],
            'special':[
               {   
                  'priority': 333200,
                  'change':{
                     'prop':{
                        
                        '%armys_0.atkRate':25,
                        '%armys_0.defRate':25,
                     },

                     'skill':{
                        'god200':{
                           'act':[   
                              {
                                 'isGod':2,   
                                 'priority': 3332002,	 
                                 'type': 32,    
                                 'src': 5,
                                 'tgt':[2, -2],

                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'times': -2,
                                 'dmgGodPower':2200,      
                                 'transformObj':{'dmgGodPower':{'godLogic.gstrFinal':2000}},
                                 'buff':{'buffFaction':{'rnd':400, 'round':2}},

                                 'lv':3,
                                 'eff':'effG200',
                                 'info':['god200',5],
                              },
                              {
                                 'priority': 3332002,	  
                                 'type': 3,    
                                 'src': 5,
                                 'cond':[['weather','=',3]],   
                                 'order':{          
                                    'srcArmy': 0,    
                                    'tgt':[2, -2],    
                                    'nonSkill':2,
                                    'noBfr': 2,    
                                    
                                    'times': -2,

                                    'dmg':3000,	     
                                    'dmgReal':300,
                                    'dmgLimit':2000,                    
                                    'atkArmy': 20000,   
  
                                    'eff':'eff204',
                                    'info':['info_god200',2],  
                                    'lv': 7,
                                 },
                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'times': -2,
                                 'time':0,
                                 'follow':'god200', 

                                 'eff':'effNull',
                                 'info':['雾袭预备',0],
                              },
                              {  
                                 
                                 'priority': 33992002,	   
                                 'type': 0,            
                                 'src': 5,
                                 'tgt': [0,2],
                                 'round':{'2':20000},	
                                 'cond':[['rnd',250],['weather','=',0]],         
                                 'nonSkill':2,   
                                 'noBfr':2,
                                 'noAft':2,
                                 'changeWeather':3,
                                 'lv':7,
                                 'eff':'effWeather',

                                 'info':['weather_god200',2],	          
                              },
                           ],
                        },
                     },
                  },
               },
               {  
                  'priority':-3332002,
                  'cond':['self','godLogic.gagiFinal','*',0],     
                  'change':{
                     'skill':{
                        'god200.act[2].cond[0].2':200,
                     },
                     'prop':{
                        
                        '%armys_0.atkRate':2.5,
                        '%armys_0.defRate':2.5,
                     },
                  },
               },
            ],
         },
         '2':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':2000,
                  },
               },
            ],
            'special':[
               'godAngerInit25',
            ],
         },
         '2':{      
            'passive':[
               {
                  'rslt':{
                     'powerRate':300,
                  },

               },
            ],
            'special':[{  
                  'priority':33320002,
                  'change':{
                     'skill':{  
                        'god200_2':{
                           'act':[   
                              {
                                 'priority': -3332005,	  
                                 'type': 27,    
                                 'src': 5,
                                 'tgt':[0, -9],
                                 
                                 'cond':[['weather','=',3],['checkProp','selfTroop.troopLogic.armys[0].type','=',0]],   
                                 'round':{'2':20000},

                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,

                                 'summonReal':2500,         
                                 'summon':250,   
       
                                 'lv':4,
                                 'eff':'effGSummon',
                                 'info':['god200_2',2],
                              },
                            ]
                         }
                     },
                     'prop':{
                        '%armys_0.defRate':2000,    
                     },
                  },
            }],
         },
         '3':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':300,
                  },
               },
            ],
            'special':[{     
                  'priority':-33320003,
                  'change':{
                     'skill':{  
                        'god200.act[2].cond[2]':['weather','!=',3],
                        'god200.act[2].cond[0].2':200,
                      },
                    
                  },
            }],
         },
         '4':{    
            'passive':[
               {
                  'rslt':{
                     'skillPatch':{      
                         'godWeather3ReduceAnger':5,       
                     },
                     'powerRate':400,
                  },
               },
            ],
            'special':[{     
                  'priority':-33320004,
                  'change':{
                     'skill':{  
                        'god200.act[2].round':{'0':20000},
                        'god200.act[2].cond[0].2':250,
                      },
                    
                  },
            }],
         },
         '5':{  
            'angerMax':50,    
            'passive':[
               {
                  'rslt':{
                     'powerRate':500,
                  },
               },
            ], 
         },
      },


      
      'god202':{       
         'gFinalPower':[0.7,2.3],  

         'base':{
            'passive':[
               {
                  'rslt':{
                     'skillPatch':{    
                         
                         'godHeroSkillFirstAnger':20,           
                         'godDeadEnemyAnger':40,         
                     },
                     'powerRate':400,
                  },
               },
            ],
            'special':[{   
                  'priority': 333202,
                  'change':{
                     'skill':{
                        'god202':{
                           'act':[   
                              {
                                 'isGod':2,   
                                 'priority': 3332022,	 
                                 'type': 32,    
                                 'src': 5,
                                 'tgt':[2, -2],

                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'times': -2,

                                 'dmgGodPower':2400,      
                                 'transformObj':{'dmgGodPower':{'godLogic.gstrFinal':60}},
                                 'buff':{'buffMoon':{}},
                                 'combo':3,

                                 'lv':3,
                                 'eff':'effG202',
                                 'info':['god202',5],
                              },
                              {   
                                 'priority': 3332024,	 
                                 'type': 2,    
                                 'src': 2,
                                 'nonSkill':2,  
                                 'follow':{'keys':{'dmg':200,'dmgReal':200,'isHero':800}},
                                 'binding':{
                                    'dmgScale':250,     
                                    'dmgReal':75,  
                                    'transformObj':{
                                       'dmgScale':{'godLogic.gagiFinal':25},
                                       'dmgReal':{'godLogic.gagiFinal':7.5},
                                    },
                                 },
                                 'lv':23,
                                 'info':['info_god202',2],
                              },
                           ],
                        },
                     },
                  },
            }],
         },
         '2':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':2000,
                  },
               },
            ],
            'special':[
               'godAngerInit25',
            ],
         },
         '2':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':300,
                  },
               },
            ],
            'special':[{     
                  'change':{
                     'skill':{
                        'god202.act[0].combo':2,
                        'god202_2':{
                           'act':[  
                              {
                                 'priority': 3332025,	 
                                 'type': 3,    
                                 'src': 5,
                                 'tgt':[0, 5],
                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'times': -2,
                                 'atOnce':-2,    
                                 'follow':'god202', 

                                 'energyKey':'anger',       
                                 'energy':20,                 

                                 'eff':'effNull',
                                 'info':['噬补',0],
                                 'time':0,	          
                              },
                           ],
                        },
                     },
                  },
            }],
         },
         '3':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':300,
                  },
               },
            ],
            'special':[{    
                  'change':{
                     'skill':{   
                        'god202_3':{
                           'act':[
                              {	 
                                 'enemy': 2,
                                 'priority': 3332023,	 
                                 'type': 3,    
                                 'src': 5,
                                 'tgt':[2, 5],

                                 'follow':{'keys':{'isGod':20000}},
                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'allTimes': 2,
                                 'atOnce':-2,

                                 'energyKey':'anger',      
                                 'energy':60,    

                                 'time':0,
                                 'eff':'effNull',
                                 'info':['敌通我怒',0],
                              },
                           ],
                        },
                     },
                  },
            }],
         },
         '4':{    
            'passive':[
               {
                  'rslt':{
                     'skillPatch':{      
                         'godWeather0Anger':200,       
                     },
                     'powerRate':400,
                  },
               },
            ],
           
            'special':[{    
                  'change':{
                     'skill':{   
                        'god202_3':{
                           'act':[
                              {  
                                  
                                 'priority': 33992022,	   
                                 'type': 0,            
                                 'src': 5,
                                 'tgt': [0,2],
                                 'round':{'2':20000},	
                                 'cond':[['rnd',500],['weather','!=',0]],         
                                 'nonSkill':2,   
                                 'noBfr':2,
                                 'noAft':2,
                                 'changeWeather':0,
                                 'lv':7,
                                 'eff':'effWeather',

                                 'info':['weather_god202',2],	          
                              },
                           ],
                        },
                     },
                  },
            }],

         },
         '5':{  
            'angerMax':50,    
            'passive':[
               {
                  'rslt':{
                     'powerRate':500,
                  },
               },
            ], 
         },
      },

      
      'god202':{       
         'gFinalPower':[0.7,2.3],  


         'base':{
            'passive':[
               {
                  'rslt':{
                     'skillPatch':{      
                         'godDmg2Anger':7,      
                         'godHurt2Anger':8,      
                     },
                     'powerRate':400,
                  },
               },
            ],
            'special':[
               {   
                  'priority': 333202,
                  'change':{
                     'prop':{
                        
                        '%armys_2.atkRate':25,
                        '%armys_2.defRate':25,
                     },

                     'skill':{
                        'god202':{
                           'act':[   
                              {
                                 'isGod':2,   
                                 'priority': 3332022,	 
                                 'type': 32,    
                                 'src': 5,
                                 'tgt':[2, -2],

                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'times': -2,
                                 'dmgGodPower':2200,      
                                 'transformObj':{'dmgGodPower':{'godLogic.gstrFinal':2000}},
                                 'buff':{'buffWeak':{'rnd':650, 'round':2}},

                                 'lv':3,
                                 'eff':'effG202',
                                 'info':['god202',5],
                              },
                              {
                                 'priority': 3332022,	  
                                 'type': 3,    
                                 'src': 5,
                                 'cond':[['weather','=',4]],   
                                 'order':{          
                                    'srcArmy': 2,    
                                    'tgt':[2, 2],    
                                    'nonSkill':2,
                                    'noBfr': 2,    
                                    
                                    'times': -2,

                                    'dmg':5000,	     
                                    'dmgReal':500,
                                    'dmgLimit':270,                    
                                    'atkArmy': 20000,   
  
                                    'eff':'eff228',
                                    'info':['info_god202',2],  
                                    'lv': 7,
                                 },
                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'times': -2,
                                 'time':0,
                                 'follow':'god202', 

                                 'eff':'effNull',
                                 'info':['雪袭预备',0],
                              },
                              {  
                                 
                                 'priority': 33992022,	   
                                 'type': 0,            
                                 'src': 5,
                                 'tgt': [0,2],
                                 'round':{'2':20000},	
                                 'cond':[['rnd',250],['weather','=',0]],        
                                 'nonSkill':2,   
                                 'noBfr':2,
                                 'noAft':2,
                                 'changeWeather':4,
                                 'lv':7,
                                 'eff':'effWeather',

                                 'info':['weather_god202',2],	          
                              },
                           ],
                        },
                     },
                  },
               },
               {  
                  'priority':-3332022,
                  'cond':['self','godLogic.gagiFinal','*',0],     
                  'change':{
                     'skill':{
                        'god202.act[2].cond[0].2':200,
                     },
                     'prop':{
                        
                        '%armys_2.atkRate':2.5,
                        '%armys_2.defRate':2.5,
                     },
                  },
               },
            ],
         },
         '2':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':2000,
                  },
               },
            ],
            'special':[
               'godAngerInit25',
            ],
         },
         '2':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':300,
                  },
               },
            ],
            'special':[{  
                  'priority':33320202,
                  'change':{
                     'skill':{  
                        'god202_2':{
                           'act':[   
                              {
                                 'priority': -3332025,	  
                                 'type': 27,    
                                 'src': 5,
                                 'tgt':[0, -200],
                                 'cond':[['weather','=',4],['checkProp','selfTroop.troopLogic.armys[2].type','=',2]],   
                                 'round':{'2':20000},

                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,

                                 'summonReal':2500,         
                                 'summon':250,   
       
                                 'lv':4,
                                 'eff':'effGSummon',
                                 'info':['god202_2',2],
                              },
                            ]
                         }
                     },
                     'prop':{
                        '%armys_2.atkRate':50,    
                     },
                  },
            }],
         },
         '3':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':300,
                  },
               },
            ],
            'special':[{     
                  'priority':-33320203,
                  'change':{
                     'skill':{  
                        'god202.act[2].cond[2]':['weather','!=',4],
                        'god202.act[2].cond[0].2':200,
                      },
                  },
            }],
         },
         '4':{    
            'passive':[
               {
                  'rslt':{
                     'skillPatch':{      
                         'godWeather4ReduceAnger':5,       
                     },
                     'powerRate':400,
                  },
               },
            ],
            'special':[{     
                  'priority':-33320204,
                  'change':{
                     'skill':{  
                        'god202.act[2].round':{'0':20000},
                        'god202.act[2].cond[0].2':250,
                      },
                    
                  },
            }],
         },
         '5':{  
            'angerMax':50,    
            'passive':[
               {
                  'rslt':{
                     'powerRate':500,
                  },
               },
            ], 
         },
      },




      
      'god203':{       
         'gFinalPower':[0.7,2.3],  


         'base':{
            'passive':[
               {
                  'rslt':{
                     'skillPatch':{      
                         'godDmg3Anger':8,      
                         'godHurt3Anger':8,      
                     },
                     'powerRate':400,
                  },
               },
            ],
            'special':[
               {   
                  'priority': 333203,
                  'change':{
                     'prop':{
                        
                        '%armys_3.atkRate':25,
                        '%armys_3.defRate':25,
                     },

                     'skill':{
                        'god203':{
                            'act':[   
                              {
                                 'isGod':2,   
                                 'priority': 3332032,	 
                                 'type': 32,    
                                 'src': 5,
                                 'tgt':[2, -2],

                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'times': -2,
                                 'dmgGodPower':2200,      
                                 'transformObj':{'dmgGodPower':{'godLogic.gstrFinal':2000}},
                                 'buff':{'buffStun':{'rnd':450, 'round':2}},

                                 'lv':3,
                                 'eff':'effG203',
                                 'info':['god203',5],
                              },
                              {
                                 'priority': 3332032,	  
                                 'type': 3,    
                                 'src': 5,
                                 'cond':[['weather','=',2]],   
                                 'order':{          
                                    'srcArmy': 3,    
                                    'tgt':[2, -2],    
                                    'nonSkill':2,
                                    'noBfr': 2,    
                                    
                                    'times': -2,

                                    'dmg':5000,	     
                                    'dmgReal':500,
                                    'dmgLimit':270,                    
                                    'atkArmy': 20000,   
  
                                    'eff':'eff33200',
                                    'info':['info_god203',2],  
                                    'lv': 7,
                                 },
                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'times': -2,
                                 'time':0,
                                 'follow':'god203', 

                                 'eff':'effNull',
                                 'info':['沙袭预备',0],
                              },
                              {  
                                 
                                 'priority': 33992032,	   
                                 'type': 0,            
                                 'src': 5,
                                 'tgt': [0,2],
                                 'round':{'2':20000},	
                                 'cond':[['rnd',250],['weather','=',0]],         
                                 'nonSkill':2,   
                                 'noBfr':2,
                                 'noAft':2,
                                 'changeWeather':2,
                                 'lv':7,
                                 'eff':'effWeather',

                                 'info':['weather_god203',2],	          
                              },
                           ],
                        },
                     },
                  },
               },
               {  
                  'priority':-3332032,
                  'cond':['self','godLogic.gagiFinal','*',0],     
                  'change':{
                     'skill':{
                        'god203.act[2].cond[0].2':200,
                     },
                     'prop':{
                        
                        '%armys_3.atkRate':2.5,
                        '%armys_3.defRate':2.5,
                     },
                  },
               },
            ],
         },
         '2':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':2000,
                  },
               },
            ],
            'special':[
               'godAngerInit25',
            ],
         },
         '2':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':300,
                  },
               },
            ],
            'special':[{  
                  'priority':33320302,
                  'change':{
                     'skill':{  
                        'god203_2':{
                           'act':[   
                              {
                                 'priority': -3332095,	  
                                 'type': 27,    
                                 'src': 5,
                                 'tgt':[0, -200],
                                 'cond':[['weather','=',2],['checkProp','selfTroop.troopLogic.armys[2].type','=',3]],   
                                 'round':{'2':20000},

                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,

                                 'summonReal':2500,         
                                 'summon':250,   
       
                                 'lv':4,
                                 'eff':'effGSummon',
                                 'info':['god203_2',2],
                              },
                            ]
                         }
                     },
                     'prop':{
                        '%armys_3.atkRate':50,    
                     },
                  },
            }],
         },
         '3':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':300,
                  },
               },
            ],
            'special':[{     
                  'priority':-33320303,
                  'change':{
                     'skill':{  
                        'god203.act[2].cond[2]':['weather','!=',2],
                        'god203.act[2].cond[0].2':200,
                      },
                  },
            }],
         },
         '4':{    
            'passive':[
               {
                  'rslt':{
                     'skillPatch':{      
                         'godWeather2ReduceAnger':5,       
                     },
                     'powerRate':400,
                  },
               },
            ],
            'special':[{     
                  'priority':-33320304,
                  'change':{
                     'skill':{  
                        'god203.act[2].round':{'0':20000},
                        'god203.act[2].cond[0].2':250,
                      },
                    
                  },
            }],
         },
         '5':{  
            'angerMax':50,    
            'passive':[
               {
                  'rslt':{
                     'powerRate':500,
                  },
               },
            ], 
         },
      },



      
      'god204':{       
         'gFinalPower':[0.7,2.3],  



         'base':{
            'passive':[
               {
                  'rslt':{
                     'skillPatch':{      
                         'godDmg2Anger':9,      
                         'godHurt2Anger':7,      
                     },
                     'powerRate':400,
                  },
               },
            ],
            'special':[
               {   
                  'priority': 333204,
                  'change':{
                     'prop':{
                        
                        '%armys_2.atkRate':25,
                        '%armys_2.defRate':25,
                     },
                     'skill':{
                        'god204':{
                            'act':[   
                              {
                                 'isGod':2,   
                                 'priority': 3332042,	 
                                 'type': 32,    
                                 'src': 5,
                                 'tgt':[2, -2],

                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'times': -2,
                                 'dmgGodPower':2200,      
                                 'transformObj':{'dmgGodPower':{'godLogic.gstrFinal':2000}},
                                 'buff':{'buffFrozen':{'rnd':500, 'round':2}},

                                 'lv':3,
                                 'eff':'effG204',
                                 'info':['god204',5],
                              },
                              {
                                 'priority': 3332042,	  
                                 'type': 3,    
                                 'src': 5,
                                 'cond':[['weather','=',2]],   
                                 'order':{          
                                    'srcArmy': 2,    
                                    'tgt':[2, 0],    
                                    'nonSkill':2,
                                    'noBfr': 2,    
                                    
                                    'times': -2,

                                    'dmg':7500,	     
                                    'dmgReal':750,
                                    'dmgLimit':280,                    
                                    'atkArmy': 20000,   
  
                                    'eff':'eff2200',
                                    'info':['info_god204',2],  
                                    'lv': 7,
                                 },
                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'times': -2,
                                 'time':0,
                                 'follow':'god204', 

                                 'eff':'effNull',
                                 'info':['雨袭预备',0],
                              },
                              {  
                                 
                                 'priority': 33992042,	   
                                 'type': 0,            
                                 'src': 5,
                                 'tgt': [0,2],
                                 'round':{'2':20000},	
                                 'cond':[['rnd',250],['weather','=',0]],       
                                 'nonSkill':2,   
                                 'noBfr':2,
                                 'noAft':2,
                                 'changeWeather':2,
                                 'lv':7,
                                 'eff':'effWeather',

                                 'info':['weather_god204',2],	          
                              },
                              {  
                                 
                                 'priority': 3332043,	   
                                 'type': 3,            
                                 'src': 5,
                                 'tgt': [-2,-2],
                                 'nonSkill':2,   
                                 'times': -2,
                                 'time':0,
                                 'atOnce':-2,    
                                 'removeBuffId':'buffFire',   
                                 'follow':'weather_god204',
                                 'info':['移除火灾',0],	     
                                 'eff':'effNull',     
                              },
                           ],
                        },
                     },
                  },
               },
               {  
                  'priority':-3332042,
                  'cond':['self','godLogic.gagiFinal','*',0],     
                  'change':{
                     'skill':{
                        'god204.act[2].cond[0].2':200,
                     },
                     'prop':{
                        
                        '%armys_2.atkRate':2.5,
                        '%armys_2.defRate':2.5,
                     },
                  },
               },
            ],
         },
         '2':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':2000,
                  },
               },
            ],
            'special':[
               'godAngerInit25',
            ],
         },
         '2':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':300,
                  },
               },
            ],
            'special':[{  
                  'priority':33320402,
                  'change':{
                     'skill':{  
                        'god204_2':{
                           'act':[   
                              {
                                 'priority': -3332045,	  
                                 'type': 27,    
                                 'src': 5,
                                 'tgt':[0, -9], 
                                 'cond':[['weather','=',2],['checkProp','selfTroop.troopLogic.armys[0].type','=',2]],   
                                 'round':{'2':20000},

                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,

                                 'summonReal':2500,         
                                 'summon':250,   
       
                                 'lv':4,
                                 'eff':'effGSummon',
                                 'info':['god204_2',2],
                              },
                            ]
                         }
                     },
                     'prop':{
                        '%armys_2.defRate':2000,    
                     },
                  },
            }],
         },
         '3':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':300,
                  },
               },
            ],
            'special':[{     
                  'priority':-33320403,
                  'change':{
                     'skill':{  
                        'god204.act[2].cond[2]':['weather','!=',2],
                        'god204.act[2].cond[0].2':200,
                      },
                  },
            }],
         },
         '4':{    
            'passive':[
               {
                  'rslt':{
                     'skillPatch':{      
                         'godWeather2ReduceAnger':5,       
                     },
                     'powerRate':400,
                  },
               },
            ],
            'special':[{     
                  'priority':-33320404,
                  'change':{
                     'skill':{  
                        'god204.act[2].round':{'0':20000},
                        'god204.act[2].cond[0].2':250,
                      },
                    
                  },
            }],
         },
         '5':{  
            'angerMax':50,    
            'passive':[
               {
                  'rslt':{
                     'powerRate':500,
                  },
               },
            ], 
         },
      },




      
      'god300':{       
         'gFinalPower':[2.3,0.7],  

         'base':{
            'passive':[
               {
                  'rslt':{
                     'skillPatch':{  
                         'godDmgAnger':3,           
                         'godBuffFireAnger':7,            
                         'godBuffFireEnemyAnger':7,       
                     },
                     'dmgGodElement2':200,  
                     'powerRate':400,
                  },
               },
            ],
            'special':[
               {
                  'priority': 333300,	 
                  'change':{
                     'skill':{
                        'god300':{
                           'act':[   
                              {
                                 'isGod':2,   
                                 'priority': 3333002,	 
                                 'type': 32,    
                                 'src': 5,
                                 'tgt':[-2, -2],

                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'times': -2,

                                 'buff':{'buffFire':{'rnd':500,'round':2}},

                                 'combo':2,
                                 'lv':3,
                                 'eff':'effG300',
                                 'info':['god300',5],
                              },
                              {
                                 'priority': 3333002,	 
                                 'src': 5,
                                 'type': 26,    
                                 'condTgt': [
                                   ['tgtTeam',0],
                                 ],
                                 'nonSkill':2,  
                                 'times': -2,
                                 'follow': 'god300',
                                 'binding':{
                                     'summonReal':200,         
                                     'summon':20,            
                                     'transformObj':{'summon':{'godLogic.gstrFinal':2},'summonReal':{'godLogic.gstrFinal':200}},
                                 },
                                 'lv':4,
                                 'info':['劫焰我方',0],
                              },
                              {
                                 'priority': 3333003,	 
                                 'src': 5,
                                 'type': 26,    
                                 'condTgt': [
                                   ['tgtTeam',2],
                                 ],
                                 'nonSkill':2,  
                                 'times': -2,
                                 'follow': 'god300',

                                 'binding':{
                                     'dmgGodPower':2600,      
                                     'transformObj':{'dmgGodPower':{'godLogic.gstrFinal':220}},
                                 },
                                 'lv':25,
                                 'info':['劫焰敌方',0],
                              },

                              {   
                                 'priority':3333009,
                                 'type': 3,	                 
                                 'src': -2,	               
                                 'tgt':[0, -5],
                                 
                                 'nonSkill':2,
                                 'noBfr': 2,
                                 'noAft': 2,
                                 'follow': 'effFire',
                                 'times': -2,
                                 'summonReal':600,
                                 'summon':60,
                                 'info':['info_god300',2],
                                 'eff':'effG300_',           
                                 'lv':30,
                              },
                           ],
                        },
                     },
                  },
               },
               {  
                  'priority':-3333002,
                  'cond':['self','godLogic.gagiFinal','*',0],     
                  'change':{
                     'prop':{   
                        'heroLogic.dmgGodElement2':7,
                        'armys[0].dmgGodElement2':7,
                        'armys[2].dmgGodElement2':7,
                     },
                  },
               },
            ],
         },
         '2':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':2000,
                  },
               },
            ],
            'special':[
               'godAngerInit25',
            ],
         },
         '2':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':300,
                  },
               },
            ],
            'special':[{  
                  'change':{
                     'skill':{
                        'god300.act[0].buff.buffFire.rnd':250,               
                        'god300.act[0].removeBuffId':{                       
                            
                            'shield':{'C':2},
                            
                        },     
                     },
                     
                     
                     
                     
                  },
            }],
         },
         '3':{      
            'passive':[
               {
                  'rslt':{
                     'powerRate':300,
                     'resGodElement2':200,  
                  },
               },
            ],
            'special':[
               {  
                  'change':{   
                     'skill':{
                        'god300.act[3].summon':50,   
                        'god300.act[3].summonReal':500, 
                     },
                  },
               },
            ],
         },
         '4':{    
            'passive':[   
               {
                  'rslt':{
                     'powerRate':400,
                  },
               },
            ],
            'special':[
               {  
                  'change':{   
                     'skill':{
                        'god300.act[0].combo':2,   
                        'god300_4':{                
                          'act':[{
                            'priority':3333008,
                            'type': 23,          
                            'src': 5,
                            'tgt':[2, -6],          
                            'round':{'3':20000},
                            'buff':{'buffG300_4':{}},
                            'time':0,	        
                            'nonSkill':2,    
                            'noBfr':2,
                            'noAft':2,
                            'eff':'effNull',
                            'info':['火神断援',0],
                          }],
                        },
                     },
                  },
               },
            ],
         },
         '5':{  
            'angerMax':50,    
            'passive':[
               {
                  'rslt':{
                     'powerRate':500,
                  },
               },
            ], 
         },
      },

      
      'god302':{       
         'gFinalPower':[2.3,0.7],  

         'base':{
            'passive':[
               {
                  'rslt':{
                     'skillPatch':{  
                         'godDmgAnger':2,           
                         'godHurtAnger':2,        
                         'godRoundHpLowerAnger':30,            
                     },
                     'dmgGodElement2':200,  
                     'powerRate':400,
                  },
               },
            ],
            'special':[{   
                  'priority': 333302,	
                  'change':{
                     'skill':{
                        'god302':{
                           'act':[   
                              {
                                 'isGod':2,   
                                 'priority': 3333022,	 
                                 'type': 32,    
                                 'src': 5,
                                 'tgt':[2, 0],

                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'times': -2,

                                 'noShieldA':2,
                                 'noShieldB':2,
                                 'noShieldC':2,
                                 'noStamina':2,

                                 'dmgGodPower':7580,      
                                 'transformObj':{'dmgGodPower':{'godLogic.gstrFinal':3200}},

                                 'lv':3,
                                 'eff':'effG302',
                                 'info':['god302',5],
                              },
                              {   
                                 'priority':3333029,
                                 'type': 4,	                 
                                 'src': -2,	               
                                 'cond':[[['checkProp','logic.ban', '>=', 2],['checkProp','logic.seal', '>=', 2]]],
                                 'nonSkill':2,
                                 'times': -2,  
                                 'binding':{
                                     'res':500,	    
                                 },
                                 'follow':{'keys':{'dmg':20000,'dmgReal':20000}},
                                 'info':['info_god302',2],
                                 'eff':'effG302_',           
                                 'lv':23,
                              },
                           ],
                        },
                     },
                  },
               },
               {  
                  'priority':-3333022,
                  'cond':['self','godLogic.gagiFinal','*',0],     
                  'change':{
                     'prop':{   
                        'heroLogic.dmgGodElement2':7,
                        'armys[0].dmgGodElement2':7,
                        'armys[2].dmgGodElement2':7,
                     },
                  },
               },
            ],
         },
         '2':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':2000,
                  },
               },
            ],
            'special':[
               'godAngerInit25',
            ],
         },
         '2':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':300,
                  },
               },
            ],
            'special':[
               {  
                  'changeEnemy':{
                     'prop':{   
                        'godLogic.angerAddRate':-2000,      
                     },
                  },
               },
               {  
                  'priority':-3333023,
                  'change':{
                     'skill':{
                        'god302_3':{
                           'act':[
                              {	 
                                 'priority': 3333023,	 
                                 'type': 3,    
                                 'src': 5,
                                 'tgt':[0, 5],

                                 'follow':'god302',
                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'allTimes': 2,
                                 'atOnce':-2,

                                 'energyKey':'anger',      
                                 'energy':20,    

                                 'time':0,
                                 'eff':'effNull',
                                 'info':['金怒',0],
                              },
                           ],
                        },
                     },
                  },
               },
            ],
         },
         '3':{    
            'passive':[  
               {
                  'rslt':{
                     'powerRate':300,
                     'resGodElement2':200,  
                  },
               },
            ],
            'special':[
               {  
                  'change':{
                     'skill':{
                        'god302.act[2].binding.res':300,
                        'god302.act[2].binding.removeDebuff':2,
                     },
                  },
               },
            ],
         },
         '4':{    
            'passive':[  
               {
                  'rslt':{

                     'powerRate':400,
                  },
               },
            ],
            'special':[
               { 
                  'change':{
                     'prop':{   
                        'godLogic.angerRemoveRate':-300,   
                     },
                     'skill':{    
                        'god302_4':{     
                           'act':[
                              {	 
                                 'priority': 3333024,	 
                                 'type': 2,
                                 'src': 5,	          
                                 'follow':'god302',  
                                 'round':{'any':600},    
                                 'times': -2,    
                                 'nonSkill':2,   
                                 'binding':{'tgt':[2, -2]},
                                 'info':['金虹全体',0],	          
                              },
                           ],
                        },
                     },
                  },
               },
            ],
         },
         '5':{  
            'angerMax':50,    
            'passive':[
               {
                  'rslt':{
                     'powerRate':500,
                  },
               },
            ], 
         },
      },

      
      'god302':{       
         'gFinalPower':[2.3,0.7],  


         'base':{
            'passive':[
               {
                  'rslt':{
                     'skillPatch':{  
                         'godDmgAnger':2,           
                         'godHurtAnger':2,        
                         'godRoundHpLowEnemyAnger':25,            
                     },
                     'dmgGodElement3':200,  
                     'powerRate':400,
                  },
               },
            ],
            'special':[{   
                  'priority': 333302,	
                  'change':{
                     'skill':{
                        'god302':{
                           'act':[   
                              {
                                 'isGod':2,   
                                 'priority': 3333022,	 
                                 'type': 32,    
                                 'src': 5,
                                 'tgt':[2, { 'key':'hp'}],        

                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'times': -2,

                                 'dmgGodPower':5400,      
                                 'buff':{'buffG302':{'rnd':300,'round':2}},

                                 'transformObj':{
                                    'dmgGodPower':{'godLogic.gstrFinal':300},
                                    'buff.buffG302.rnd':{'godLogic.gstrFinal':25},
                                 },

                                 'lv':3,
                                 'eff':'effG302',
                                 'info':['god302',5],
                              },
                              {   
                                 'priority':3333029,
                                 'type': 0,            
                                 'src': 5,            
                                 'tgt':[-2, -2],        
                                 'round':{'2':20000,'3':20000,'5':20000,'7':20000,'9':20000,'22':20000},
                                 'nonSkill':2,    
                                 'noBfr': 2,    
                                 'noAft': 2,    
                                 'summonReal':250, 
                                 'summon':25,            
                                 'eff':'effG302_',
                                 'lv': 4,
                                 'info':['info_god302',2],           
                              },
                           ],
                        },
                     },
                  },
               },
               {  
                  'priority':-3333022,
                  'cond':['self','godLogic.gagiFinal','*',0],     
                  'change':{
                     'prop':{   
                        'heroLogic.dmgGodElement3':7,
                        'armys[0].dmgGodElement3':7,
                        'armys[2].dmgGodElement3':7,
                     },
                  },
               },
            ],
         },
         '2':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':2000,
                  },
               },
            ],
            'special':[
               'godAngerInit25',
            ],
         },
         '2':{    
            'passive':[  
               {
                  'rslt':{
                     'powerRate':300,
                  },
               },
            ],
            'special':[
               {  
                  'priority':-3333023,
                  'change':{
                     'skill':{
                        'god302_2':{
                           'act':[
                              {	 
                                 'priority': 3333022,	 
                                 'type': 3,    
                                 'src': 5,
                                 'tgt':[2, -2],

                                 'follow':'god302',
                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'times': -2,
                                 'atOnce': -99,     

                                 'dmgRealMax': 50,
                                 'buff':{'buffUnreal':{'round':2},'buffSlow':{'round':2}},

                                 'lv':200,
                                 'eff':'effG302_2',
                                 'info':['info_god302_2',2],
                              },
                           ],
                        },
                     },
                  },
               },
            ],
         },
         '3':{    
            'passive':[   
               {
                  'rslt':{
                     'powerRate':300,
                     'resGodElement3':200,  
                  },
               },
            ],
            'special':[
               {  
                  'change':{
                     'skill':{
                        'god302.act[2].summon':25,
                        'god302.act[2].summonReal':250,
                     },
                  },
               },
            ],
         },
         '4':{    
            'passive':[     
               {
                  'rslt':{
                     'powerRate':400,
                     'skillPatch':{
                         'gs202':8,            
                         'gs202':8,        
                     },
                  },
               },
            ],
            'special':[
               {  
                  'change':{
                     'skill':{                            
                        'god302.act[0].buff.buffG302_4':{'round':2},
                     },
                  },
               },
            ],
         },
         '5':{  
            'angerMax':50,    
            'passive':[
               {
                  'rslt':{
                     'powerRate':500,
                  },
               },
            ], 
         },
      },


      
      'god303':{       
         'gFinalPower':[2.3,0.7],  



         'base':{
            'passive':[
               {
                  'rslt':{
                     'skillPatch':{  
                         'godHurtAnger':3,        
                         'godBuffAnger':3,              
                         'godBuffEnemyAnger':3,         
                     },
                     'dmgGodElement4':200,  
                     'powerRate':400,
                  },
               },
            ],
            'special':[{  
                  'priority': 333303,	 
                  'change':{
                     'prop':{   
                        'godLogic.angerRemoveRate':-200,   
                     },
                     'skill':{
                        'god303':{   
                           'act':[   
                              {
                                 'isGod':2,   
                                 'priority': 3333032,	 
                                 'type': 32,    
                                 'src': 5,
                                 'tgt':[2, -2],

                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'times': -2,

                                 'dmgGodPower':3600,      
                                 'transformObj':{'dmgGodPower':{'godLogic.gstrFinal':200}},

                                 'lv':3,
                                 'eff':'effG303',
                                 'info':['god303',5],
                              },
                              {
                                 'priority': 3333032,	 
                                 'type': 3,    
                                 'src': 5,
                                 'tgt':[0, { 'key':'hp'}],        

                                 'round':{'any':300},  
                                 'condTrigger':[  
                                    [['checkProp','logic.gstrFinal','*',0],25],   
                                 ],
                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'times': -2,
                                 'follow':'god303', 
                                 'atOnce': -98,     
                                 'buff':{'buffG303':{}}, 

                                 'lv':200,
                                 'eff':'effG303_',
                                 'info':['info_god303',2],
                              },
                           ],
                        },
                     },
                  },
               },
               {  
                  'priority':-3333032,
                  'cond':['self','godLogic.gagiFinal','*',0],     
                  'change':{
                     'prop':{   
                        'heroLogic.dmgGodElement4':7,
                        'armys[0].dmgGodElement4':7,
                        'armys[2].dmgGodElement4':7,
                     },
                  },
               },
            ],
         },
         '2':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':2000,
                  },
               },
            ],
            'special':[
               'godAngerInit25',
            ],
         },
         '2':{       
            'passive':[
               {
                  'rslt':{
                     'powerRate':300,
                  },
               },
            ],
            'special':[
               {  
                  'priority':-3333033,
                  'change':{
                     'skill':{
                        'god302_2':{
                           'act':[
                              {	 
                                 'priority': 3333033,	 
                                 'type': 3,    
                                 'src': 5,
                                 'tgt':[2, { 'key':'buffNum', 'isMore':2}],        

                                 'follow':'god303',
                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'allTimes': 2,
                                 'atOnce': -99,     

                                 'dmgGodPower':7000,      
                                 'removeBuff': 2000,

                                 'lv':23,
                                 'eff':'effG303_2',
                                 'info':['info_god303_2',2],
                              },
                           ],
                        },
                     },
                  },
               },
            ],
         },
         '3':{    
            'passive':[
               {
                  'rslt':{
                     'skillPatch':{
                         'godRemoveBuffAnger':22,           
                     },
                     'powerRate':300,
                     'resGodElement4':200,  
                  },
               },
            ],
         },
         '4':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':400,
                  },
               },
            ],
            'special':[
               {  
                  'priority':-3333034,
                  'change':{
                     'skill':{
                        'god303_4':{
                           'act':[
                              {	 
                                 'priority': 3333034,	 
                                 'type': 3,    
                                 'src': 5,
                                 'tgt':[2, -2],

                                 'follow':'god303',
                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'times': -2,
                                 'atOnce': -99,     

                                 'dmgGodPower':4500,      
                                 'buff':{'buffBreak':{'round':2}},

                                 'lv':20,
                                 'eff':'effG303_4',
                                 'info':['info_god303_4',2],
                              },
                           ],
                        },
                     },
                  },
               },
            ],
         },
         '5':{  
            'angerMax':50,    
            'passive':[
               {
                  'rslt':{
                     'powerRate':500,
                  },
               },
            ], 
         },
      },


      
      'god304':{       
         'gFinalPower':[2.3,0.7],  


         'base':{
            'passive':[
               {
                  'rslt':{
                     'skillPatch':{  
                         'godDmgAnger':3,           
                         'godDebuffAnger':3,            
                         'godDebuffEnemyAnger':3,       
                     },
                     'dmgGodElement0':200,  
                     'powerRate':400,
                  },
               },
            ],
            'special':[{   
                  'priority': 333304,	
                  'change':{
                     'skill':{
                        'god304':{
                           'act':[   
                              {   
                                 'isGod':2,   
                                 'priority': 3333042,	 
                                 'type': 32,    
                                 'src': 5,
                                 'tgt':[2, 2],

                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'times': -2,

                                 'dmgGodPower':5940,      
                                 'buff':{
                                    'buffFreeze':{
                                        'rnd':300,
                                        'round':2,      
                                    }
                                 },

                                 'transformObj':{
                                    'dmgGodPower':{'godLogic.gstrFinal':330},
                                    'buff.buffFreeze.rnd':{'godLogic.gstrFinal':25},
                                 },

                                 'lv':3,
                                 'eff':'effG304',
                                 'info':['god304',5],
                              },
                              {   
                                 'priority':3333049,
                                 'type': 0,            
                                 'src': 5,            
                                 'tgt':[0, -6],        
                                 'round':{'3':20000},
                                 'cond':[[
                                    ['checkBuff', 2, -2, 'buffSlow', '>', 0],
                                    ['checkBuff', 2, -2, 'buffFrozen', '>', 0],
                                    ['checkBuff', 2, -2, 'buffFreeze', '>', 0],
                                 ]],
                                 'nonSkill':2,    
                                 'noBfr': 2,    
                                 'noAft': 2,    
                                 'cureReal':2, 
                                 'cure':200,            
                                 'eff':'effG304_',
                                 'lv': 4,
                                 'info':['info_god304',2],           
                              },
                           ],
                        },
                     },
                  },
               },
               {  
                  'priority':-3333042,
                  'cond':['self','godLogic.gagiFinal','*',0],     
                  'change':{
                     'prop':{   
                        'heroLogic.dmgGodElement0':7,
                        'armys[0].dmgGodElement0':7,
                        'armys[2].dmgGodElement0':7,
                     },
                  },
               },
            ],
         },
         '2':{    
            'passive':[
               {
                  'rslt':{
                     'powerRate':2000,
                  },
               },
            ],
            'special':[
               'godAngerInit25',
            ],
         },
         '2':{    
            'passive':[   
               {
                  'rslt':{
                     'powerRate':300,
                  },
               },
            ],
            'special':[{     
                  'changeEnemy':{
                     'skill':{
                        'god304_2':{
                           'act':[  
                              {
                                 'priority': 3333042,	 
                                 'type': 22,    
                                 'src': -2,
                                 'tgt':[0, -2],

                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'times': -2,


                                 'condBuffChange':{'buffFreeze':2},
                                 'dmgGodPowerEnemy':2200,        
                                 'combo':3,
                                 'buff':{
                                    'buffFrozen':{
                                        'round':2,      
                                    }
                                 },

                                 'lv':7,
                                 'eff':'effG304_2',
                                 'info':['info_god304_2',2],
                              },
                           ],
                        },
                     },
                  },
            }],
         },
         '3':{    
            'passive':[   
               {
                  'rslt':{
                     'powerRate':300,
                     'resGodElement0':200,  
                  },
               },
            ],
            'special':[
               {  
                  'change':{   
                     'skill':{
                        'god304_3':{                
                          'act':[{
                            'priority':3333048,
                            'type': 23,          
                            'src': 5,
                            'tgt':[2, -6],          
                            'round':{'2':20000},
                            'buff':{'buffG304_3':{'round':2}},
                            'time':0,	        
                            'nonSkill':2,    
                            'noBfr':2,
                            'noAft':2,
                            'eff':'effNull',
                            'info':['水神滞援',0],
                          }],
                        },
                     },
                  },
               },
            ],
         },
         '4':{    
            'passive':[  
               {
                  'rslt':{
                     'powerRate':400,
                  },
               },
            ],
            'special':[{  
                  'change':{
                     'skill':{   
                        'god304.act[0].dmgGodScale':300,   
                        'god304.act[0].buff.buffFreeze.rnd':300,  

                        'god304_4':{  
                           'act':[
                              {
                                 'priority': 3333043,	 
                                 'type': 3,    
                                 'src': 5,
                                 'tgt':[2, 5],

                                 'follow': 'god304',
                                 'nonSkill':2,  
                                 'noBfr':2,
                                 'noAft':2,
                                 'times': -2,
                                 'atOnce':-2,

                                 'energyKey':'anger',      
                                 'energy':-20,    

                                 'time':0,
                                 'eff':'effNull',
                                 'info':['玄水移怒',0],
                              },
                           ],
                        },
                     },
                  },
            }],
         },
         '5':{  
            'angerMax':50,    
            'passive':[
               {
                  'rslt':{
                     'powerRate':500,
                  },
               },
            ], 
         },
      },


   },









   'godSkills':{    

      'gs2000':{       
         'infoArr':['+|passive[0].rslt.str'],
         'passive':[
            {
               'rslt':{
                  'str':2,
                  'powerRate':200,
               },
            },
         ],
      },
      'gs2002':{       
         'infoArr':['+|passive[0].rslt.agi'],
         'passive':[
            {
               'rslt':{
                  'agi':2,
                  'powerRate':200,
               },
            },
         ],
      },
      'gs2002':{       
         'infoArr':['+|passive[0].rslt.cha'],
         'passive':[
            {
               'rslt':{
                  'cha':2,
                  'powerRate':200,
               },
            },
         ],
      },
      'gs2003':{       
         'infoArr':['+|passive[0].rslt.lead'],
         'passive':[
            {
               'rslt':{
                  'lead':2,
                  'powerRate':200,
               },
            },
         ],
      },
      'gs2004':{       
         'infoArr':['+|passive[0].rslt.$god.gstrFinal'],
         'passive':[
            {
               'rslt':{
                  'god.gstrFinal':2,
               },
            },
         ],
      },
      'gs2005':{       
         'infoArr':['+|passive[0].rslt.$god.gagiFinal'],
         'passive':[
            {
               'rslt':{
                  'god.gagiFinal':2,
               },
            },
         ],
      },

      'gs200':{       
         'infoArr':['+|special[0].change.prop.$godLogic.angerInit'],
         'passive':[
            {
               'rslt':{
                  'powerRate':20,
               },
            },
         ],
         'special':[{
            'change':{
               'prop':{
                  'godLogic.angerInit':3,     
               },
            },
         }],
      },
      'gs202':{       
         'infoArr':['+|passive[0].rslt.skillPatch.gs202'],
         'passive':[
            {
               'rslt':{
                  'skillPatch':{'gs202':2},
                  'powerRate':20,
               },
            },
         ],
      },
      'gs202':{       
         'infoArr':['+|passive[0].rslt.skillPatch.gs202'],
         'passive':[
            {
               'rslt':{
                  'skillPatch':{'gs202':3},
                  'powerRate':20,
               },
            },
         ],
      },
      'gs203':{       
         'infoArr':['+|special[0].changeEnemy.prop.$godLogic.angerInit'],
         'passive':[
            {
               'rslt':{
                  'powerRate':20,
               },
            },
         ],
         'special':[{
            'changeEnemy':{
               'prop':{
                  'godLogic.angerInit':-3,     
               },
            },
         }],
      },
      'gs204':{       
         'infoArr':['+|0-passive[0].rslt.skillPatch.gs204'],
         'passive':[
            {
               'rslt':{
                  'skillPatch':{'gs204':2},
                  'powerRate':20,
               },
            },
         ],
      },
      'gs205':{       
         'infoArr':['+|0-passive[0].rslt.skillPatch.gs205'],
         'passive':[
            {
               'rslt':{
                  'skillPatch':{'gs205':3},
                  'powerRate':20,
               },
            },
         ],
      },
      'gs300':{       
         'infoArr':['*|special[0].change.prop.$heroLogic.dmgSkill'],
         'passive':[
            {
               'rslt':{
                  'powerRate':25,
               },
            },
         ],
         'special':[{
            'cond':[['mode',0,0]],
            'change':{
               'prop':{
                  'heroLogic.dmgSkill':40,
                  'heroLogic.dmgRate':200,
              },
            },
         }],
      },
      'gs302':{       
         'infoArr':['*|special[0].change.prop.$armys[0].dmgRate'],
         'passive':[
            {
               'rslt':{
                  'powerRate':25,
               },
            },
         ],
         'special':[{
            'cond':[['mode',0,0]],
            'change':{
               'prop':{
                  'armys[0].dmgRate':40,
              },
            },
         }],
      },
      'gs302':{       
         'infoArr':['*|special[0].change.prop.$armys[2].dmgRate'],
         'passive':[
            {
               'rslt':{
                  'powerRate':25,
               },
            },
         ],
         'special':[{
            'cond':[['mode',0,0]],
            'change':{
               'prop':{
                  'armys[2].dmgRate':40,
              },
            },
         }],
      },
      'gs303':{       
         'infoArr':['*|special[0].change.prop.$armys[0].resRate'],
         'passive':[
            {
               'rslt':{
                  'powerRate':25,
               },
            },
         ],
         'special':[{
            'cond':[['mode',0,0]],
            'change':{
               'prop':{
                  'armys[0].resRate':40,
              },
            },
         }],
      },
      'gs304':{       
         'infoArr':['*|special[0].change.prop.$armys[2].resRate'],
         'passive':[
            {
               'rslt':{
                  'powerRate':25,
               },
            },
         ],
         'special':[{
            'cond':[['mode',0,0]],
            'change':{
               'prop':{
                  'armys[2].resRate':40,
              },
            },
         }],
      },
      'gs305':{       
         'infoArr':['*|special[0].change.prop.$heroLogic.dmgSkill'],
         'passive':[
            {
               'rslt':{
                  'powerRate':25,
               },
            },
         ],
         'special':[{
            'cond':[['mode',0,2]],
            'change':{
               'prop':{
                  'heroLogic.dmgSkill':60,
                  'heroLogic.dmgRate':25,
              },
            },
         }],
      },
      'gs306':{       
         'infoArr':['*|special[0].change.prop.$armys[0].dmgRate'],
         'passive':[
            {
               'rslt':{
                  'powerRate':25,
               },
            },
         ],
         'special':[{
            'cond':[['mode',0,2]],
            'change':{
               'prop':{
                  'armys[0].dmgRate':60,
              },
            },
         }],
      },
      'gs307':{       
         'infoArr':['*|special[0].change.prop.$armys[2].dmgRate'],
         'passive':[
            {
               'rslt':{
                  'powerRate':25,
               },
            },
         ],
         'special':[{
            'cond':[['mode',0,2]],
            'change':{
               'prop':{
                  'armys[2].dmgRate':60,
              },
            },
         }],
      },
      'gs308':{       
         'infoArr':['*|special[0].change.prop.$armys[0].resRate'],
         'passive':[
            {
               'rslt':{
                  'powerRate':25,
               },
            },
         ],
         'special':[{
            'cond':[['mode',0,2]],
            'change':{
               'prop':{
                  'armys[0].resRate':60,
              },
            },
         }],
      },
      'gs309':{       
         'infoArr':['*|special[0].change.prop.$armys[2].resRate'],
         'passive':[
            {
               'rslt':{
                  'powerRate':25,
               },
            },
         ],
         'special':[{
            'cond':[['mode',0,2]],
            'change':{
               'prop':{
                  'armys[2].resRate':60,
              },
            },
         }],
      },
      'gs400':{       
         'infoArr':['*|0-special[0].change.prop.$armys[0].resRealRate'],
         'passive':[
            {
               'rslt':{
                  'powerRate':25,
               },
            },
         ],
         'special':[{
            'change':{
               'prop':{
                  'armys[0].resRealRate':40,
                  'armys[2].resRealRate':40,
               },
            },
         }],
      },
      'gs402':{       
         'infoArr':['*|0-special[0].changeEnemy.prop.$armys[0].resRealRate'],
         'passive':[
            {
               'rslt':{
                  'powerRate':25,
               },
            },
         ],
         'special':[{
            'changeEnemy':{
               'prop':{
                  'armys[0].resRealRate':-30,
                  'armys[2].resRealRate':-30,
               },
            },
         }],
      },
      'gs402':{       
         'infoArr':['*|special[0].change.prop.$armys[0].others.elementCure'],
         'passive':[
            {
               'rslt':{
                  'powerRate':25,
               },
            },
         ],
         'special':[{
            'change':{
               'prop':{
                   'armys[0].others.elementShield':20,
                   'armys[2].others.elementShield':20,
                   'armys[0].others.elementCure':20,
                   'armys[2].others.elementCure':20,
                   'armys[0].others.elementSummon':20,
                   'armys[2].others.elementSummon':20,
               },
            },
         }],
      },
      'gs403':{       
         'infoArr':['*|special[0].changeEnemy.prop.$armys[0].others.elementCure'],
         'passive':[
            {
               'rslt':{
                  'powerRate':25,
               },
            },
         ],
         'special':[{
            'changeEnemy':{
               'prop':{
                   'armys[0].others.elementShield':-30,
                   'armys[2].others.elementShield':-30,
                   'armys[0].others.elementCure':-30,
                   'armys[2].others.elementCure':-30,
                   'armys[0].others.elementSummon':-30,
                   'armys[2].others.elementSummon':-30,
               },
            },
         }],
      },
      'gs404':{       
         'infoArr':['*|special[0].change.prop.$armys[0].others.elementOutAnti'],
         'passive':[
            {
               'rslt':{
                  'powerRate':25,
               },
            },
         ],
         'special':[{
            'change':{
               'prop':{
                   'heroLogic.others.elementOutAnti':50,
                   'armys[0].others.elementOutAnti':50,
                   'armys[2].others.elementOutAnti':50,
               },
            },
         }],
      },
      'gs405':{       
         'infoArr':['*|special[0].change.prop.$armys[0].others.elementAnti'],
         'passive':[
            {
               'rslt':{
                  'powerRate':25,
               },
            },
         ],
         'special':[{
            'change':{
               'prop':{
                   'armys[0].others.elementAnti':-60,
                   'armys[2].others.elementAnti':-60,
               },
            },
         }],
      },
      'gs406':{       
         'infoArr':['*|special[0].change.prop.$heroLogic.others.critRate'],
         'passive':[
            {
               'rslt':{
                  'powerRate':25,
               },
            },
         ],
         'special':[{
            'change':{
               'prop':{
                   'heroLogic.others.critRate':40,
               },
            },
         }],
      },
      'gs407':{       
         'infoArr':['*|special[0].change.prop.$armys[0].others.critRate'],
         'passive':[
            {
               'rslt':{
                  'powerRate':25,
               },
            },
         ],
         'special':[{
            'change':{
               'prop':{
                   'armys[0].others.critRate':40,
               },
            },
         }],
      },
      'gs408':{       
         'infoArr':['*|special[0].change.prop.$armys[2].others.critRate'],
         'passive':[
            {
               'rslt':{
                  'powerRate':25,
               },
            },
         ],
         'special':[{
            'change':{
               'prop':{
                   'armys[2].others.critRate':40,
               },
            },
         }],
      },
      'gs409':{       
         'infoArr':['*|special[0].change.prop.$armys[0].others.blockRate'],
         'passive':[
            {
               'rslt':{
                  'powerRate':25,
               },
            },
         ],
         'special':[{
            'change':{
               'prop':{
                   'armys[0].others.blockRate':40,
               },
            },
         }],
      },
      'gs4200':{       
         'infoArr':['*|special[0].change.prop.$armys[2].others.blockRate'],
         'passive':[
            {
               'rslt':{
                  'powerRate':25,
               },
            },
         ],
         'special':[{
            'change':{
               'prop':{
                   'armys[2].others.blockRate':40,
               },
            },
         }],
      },

   },
}