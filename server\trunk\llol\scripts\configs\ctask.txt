{

    'chapter':{                          
      'chapter_2':{
      'chapter_map':'text_painting2',        
      'chapter_hero':'hero407',        
      'chapter_reward':{'item702':200,'item002':200,'item027':5,'coin':50,},        
      
      'talks':[
],        
      'task_id':[ 'cmain_2', 'cmain_2', 'cmain_3', ],        
                            },

      'chapter_2':{
      'chapter_map':'text_painting2',        
      'chapter_hero':'hero727',        
      'chapter_reward':{'item702':200,'food':2000000,'iron':2000000,'coin':50,},        
      'guide':{        
        'in':{'box':2 ,'effect':2 },
        'out':{ 'arrow':2,'box':2 },
     },
      'talks':[
        ['hero727','chapter_2_npc_name2','chapter_2_npc_dialogue2',],  
        ['hero403','chapter_2_npc_name2','chapter_2_npc_dialogue2',],  
        ['hero702','chapter_2_npc_name3','chapter_2_npc_dialogue3',],  
],        
      'task_id':[ 'cmain_4','cmain_5','cmain_6','cmain_7','cmain_8',],        
                            },

      'chapter_3':{
      'chapter_map':'text_painting3',        
      'chapter_hero':'hero727',        
      'chapter_reward':{'item702':200,'iron':2000000,'wood':2000000,'coin':2000,},        
      'guide':{        
        'in':{ 'box':2, 'effect':2 },
        'out':{ 'arrow':2,'box':2 },
     },
      'talks':[
        ['hero702','chapter_3_npc_name2','chapter_3_npc_dialogue2',],  
        ['hero727','chapter_3_npc_name2','chapter_3_npc_dialogue2',],  
        ['hero403','chapter_3_npc_name3','chapter_3_npc_dialogue3',],  
],        
      'task_id':['cmain_9','cmain_200', 'cmain_22','cmain_22','cmain_23', ],        
                            },

      'chapter_4':{
      'chapter_map':'text_painting4',        
      'chapter_hero':'hero727',        
      'chapter_reward':{'item702':200,'wood':2000000,'gold':50000,'coin':2000,},        
      'guide':{        
        'in':{ 'box':2, 'effect':2 },
        'out':{'arrow':2, 'box':2 },
     },
      'talks':[
        ['hero403','chapter_4_npc_name2','chapter_4_npc_dialogue2',],  
        ['hero727','chapter_4_npc_name2','chapter_4_npc_dialogue2',],  
        ['hero702','chapter_4_npc_name3','chapter_4_npc_dialogue3',],  
],        
      'task_id':['cmain_25', 'cmain_24','cmain_26','cmain_27','cmain_28',],        
                            },

      'chapter_5':{
      'chapter_map':'text_painting5',        
      'chapter_hero':'hero727',        
      'chapter_reward':{'item702':200,'food':2000000,'wood':2000000,'coin':250,},        
      'guide':{        
        'in':{ 'box':0.5, 'effect':2 },
        'out':{ 'box':0.5 },
     },
      'talks':[
        ['hero727','chapter_5_npc_name2','chapter_5_npc_dialogue2',],  
        ['hero702','chapter_5_npc_name2','chapter_5_npc_dialogue2',],  
        ['hero403','chapter_5_npc_name3','chapter_5_npc_dialogue3',],  
],        
      'task_id':[ 'cmain_29', 'cmain_20','cmain_22', 'cmain_22', 'cmain_23',],        
                            },

      'chapter_6':{
      'chapter_map':'text_painting6',        
      'chapter_hero':'hero727',        
      'chapter_reward':{'item032':5,'iron':2000000,'gold':50000,'coin':250,},        
      'guide':{        
        'in':{ 'effect':2 },
        'out':{ 'box':0.3},
     },
      'talks':[
        ['hero403','chapter_6_npc_name2','chapter_6_npc_dialogue2',],  
        ['hero702','chapter_6_npc_name2','chapter_6_npc_dialogue2',],  
        ['hero727','chapter_6_npc_name3','chapter_6_npc_dialogue3',],  
],        
      'task_id':['cmain_24','cmain_25', 'cmain_26','cmain_27', 'cmain_28',],        
                            },

      'chapter_7':{
      'chapter_map':'text_painting7',        
      'chapter_hero':'hero762',        
      'chapter_reward':{'item032':5,'iron':2000000,'item030':5,'coin':200,},        
      
      'talks':[
        ['hero403','chapter_7_npc_name2','chapter_7_npc_dialogue2',],  
        ['hero702','chapter_7_npc_name2','chapter_7_npc_dialogue2',],  
        ['hero762','chapter_7_npc_name3','chapter_7_npc_dialogue3',],  
],        
      'task_id':[  'cmain_29', 'cmain_30', 'cmain_32','cmain_32','cmain_33','cmain_34', ],        
                            },

      'chapter_8':{
      'chapter_map':'text_painting8',        
      'chapter_hero':'hero762',        
      'chapter_reward':{'item032':5,'wood':2000000,'item073':3,'coin':200,},        
      
      'talks':[
        ['hero403','chapter_8_npc_name2','chapter_8_npc_dialogue2',],  
        ['hero762','chapter_8_npc_name2','chapter_8_npc_dialogue2',],  
        ['hero702','chapter_8_npc_name3','chapter_8_npc_dialogue3',],  
],        
      'task_id':[ 'cmain_95','cmain_36', 'cmain_37','cmain_38','cmain_39','cmain_40',],        
                            },

      'chapter_9':{
      'chapter_map':'text_painting9',        
      'chapter_hero':'hero762',        
      'chapter_reward':{'item032':5,'food':2000000,'item074':3,'coin':200,},        
      
      'talks':[
        ['hero702','chapter_9_npc_name2','chapter_9_npc_dialogue2',],  
        ['hero762','chapter_9_npc_name2','chapter_9_npc_dialogue2',],  
        ['hero403','chapter_9_npc_name3','chapter_9_npc_dialogue3',],  
],        
      'task_id':['cmain_42',  'cmain_42','cmain_43','cmain_44', 'cmain_45','cmain_46',],        
                            },

      'chapter_200':{
      'chapter_map':'text_painting200',        
      'chapter_hero':'hero762',        
      'chapter_reward':{'item032':5,'gold':50000,'item072':3,'coin':200,},        
      
      'talks':[
        ['hero403','chapter_200_npc_name2','chapter_200_npc_dialogue2',],  
        ['hero702','chapter_200_npc_name2','chapter_200_npc_dialogue2',],  
        ['hero762','chapter_200_npc_name3','chapter_200_npc_dialogue3',],  
],        
      'task_id':['cmain_47','cmain_48', 'cmain_49', 'cmain_50',  'cmain_52','cmain_52',],        
                            },

      'chapter_22':{
      'chapter_map':'text_painting22',        
      'chapter_hero':'hero709',        
      'chapter_reward':{'item032':5,'iron':2000000,'item030':5,'coin':300,},        
      
      'talks':[
        ['hero403','chapter_22_npc_name2','chapter_22_npc_dialogue2',],  
        ['hero709','chapter_22_npc_name2','chapter_22_npc_dialogue2',],  
        ['hero403','chapter_22_npc_name3','chapter_22_npc_dialogue3',],  
],        
      'task_id':['cmain_53', 'cmain_54','cmain_75', 'cmain_56','cmain_57','cmain_58',],        
                            },

      'chapter_22':{
      'chapter_map':'text_painting22',        
      'chapter_hero':'hero709',        
      'chapter_reward':{'item032':5,'wood':2000000,'item287':5,'coin':300,},        
      
      'talks':[
        ['hero709','chapter_22_npc_name2','chapter_22_npc_dialogue2',],  
        ['hero702','chapter_22_npc_name2','chapter_22_npc_dialogue2',],  
        ['hero403','chapter_22_npc_name3','chapter_22_npc_dialogue3',],  
],        
      'task_id':[ 'cmain_59','cmain_60', 'cmain_62', 'cmain_62', 'cmain_63','cmain_64',],        
                            },

      'chapter_23':{
      'chapter_map':'text_painting23',        
      'chapter_hero':'hero709',        
      'chapter_reward':{'item032':5,'food':2000000,'gold':50000,'coin':300,},        
      
      'talks':[
        ['hero709','chapter_23_npc_name2','chapter_23_npc_dialogue2',],  
        ['hero702','chapter_23_npc_name2','chapter_23_npc_dialogue2',],  
        ['hero403','chapter_23_npc_name3','chapter_23_npc_dialogue3',],  
],        
      'task_id':['cmain_65','cmain_66', 'cmain_67', 'cmain_68',  'cmain_69','cmain_70',],        
                            },

      'chapter_24':{
      'chapter_map':'text_painting24',        
      'chapter_hero':'hero709',        
      'chapter_reward':{'item032':5,'food':2000000,'item056':200,'coin':400,},        
      
      'talks':[
        ['hero709','chapter_24_npc_name2','chapter_24_npc_dialogue2',],  
        ['hero702','chapter_24_npc_name2','chapter_24_npc_dialogue2',],  
        ['hero709','chapter_24_npc_name3','chapter_24_npc_dialogue3',],  
],        
      'task_id':['cmain_72', 'cmain_73', 'cmain_74', 'cmain_75','cmain_76','cmain_72',],        
                            },

      'chapter_25':{
      'chapter_map':'text_painting25',        
      'chapter_hero':'hero709',        
      'chapter_reward':{'item032':5,'gold':50000,'item075':5,'coin':400,},        
      
      'talks':[
        ['hero709','chapter_25_npc_name2','chapter_25_npc_dialogue2',],  
        ['hero403','chapter_25_npc_name2','chapter_25_npc_dialogue2',],  
        ['hero702','chapter_25_npc_name3','chapter_25_npc_dialogue3',],  
],        
      'task_id':[ 'cmain_77','cmain_78', 'cmain_79', 'cmain_80', 'cmain_82', 'cmain_82', ],        
                            },

      'chapter_26':{
      'chapter_map':'text_painting26',        
      'chapter_hero':'hero724',        
      'chapter_reward':{'item032':200,'food':2000000,'item058':2000,'coin':500,},        
      
      'talks':[
        ['hero724','chapter_26_npc_name2','chapter_26_npc_dialogue2',],  
        ['hero403','chapter_26_npc_name2','chapter_26_npc_dialogue2',],  
        ['hero724','chapter_26_npc_name3','chapter_26_npc_dialogue3',],  
        ['hero403','chapter_26_npc_name4','chapter_26_npc_dialogue4',],  
        ['hero724','chapter_26_npc_name5','chapter_26_npc_dialogue5',],  
],        
      'task_id':[ 'cmain_83','cmain_84','cmain_85','cmain_86','cmain_87', 'cmain_88',],        
                            },

      'chapter_27':{
      'chapter_map':'text_painting27',        
      'chapter_hero':'hero724',        
      'chapter_reward':{'item032':200,'gold':50000,'item076':5,'coin':500,},        
      
      'talks':[
        ['hero724','chapter_27_npc_name2','chapter_27_npc_dialogue2',],  
        ['hero702','chapter_27_npc_name2','chapter_27_npc_dialogue2',],  
        ['hero724','chapter_27_npc_name3','chapter_27_npc_dialogue3',],  
        ['hero403','chapter_27_npc_name4','chapter_27_npc_dialogue4',],  
        ['hero724','chapter_27_npc_name5','chapter_27_npc_dialogue5',],  
        ['hero403','chapter_27_npc_name6','chapter_27_npc_dialogue6',],  
],        
      'task_id':[  'cmain_89','cmain_75','cmain_92','cmain_92','cmain_93', 'cmain_94', ],        
                            },

      'chapter_28':{
      'chapter_map':'text_painting28',        
      'chapter_hero':'hero724',        
      'chapter_reward':{'item032':200,'food':2000000,'item023':5,'coin':500,},        
      
      'talks':[
        ['hero724','chapter_28_npc_name2','chapter_28_npc_dialogue2',],  
        ['hero403','chapter_28_npc_name2','chapter_28_npc_dialogue2',],  
        ['hero702','chapter_28_npc_name3','chapter_28_npc_dialogue3',],  
        ['hero724','chapter_28_npc_name4','chapter_28_npc_dialogue4',],  
],        
      'task_id':[ 'cmain_95','cmain_96','cmain_97','cmain_98','cmain_99', 'cmain_2000', ],        
                            },


},

    'task':{                          
      'cmain_2':{
      'type':'have_hero',        
      'info':'cmain_2_info',        
      'goto_cfg':{
               'type':3,       
            'panelID':'hero',
                      },
      'need':[2,'all'],        
      'main_reward':{'item037':5,'item002':5,},        
                            },
      'cmain_2':{
      'type':'hero_lv',        
      'info':'cmain_2_info',        
      'goto_cfg':{
               'type':3,       
            'panelID':'hero',
                      },
      'need':[2,3],        
      'main_reward':{'iron':20000,'food':20000,},        
                            },
      'cmain_3':{
      'type':'have_force',        
      'info':'cmain_3_info',        
      'goto_cfg':{
               'type':2,       
                      },
      'need':[2,],        
      'main_reward':{'wood':20000,'food':20000,},        
                            },
      'cmain_4':{
      'type':'com_people',        
      'info':'cmain_4_info',        
      'goto_cfg':{
               'type':2,       
             'secondMenu':'bubble',
                      },
      'need':[2,['244','62','375']],        
      'main_reward':{'item032':3,'item002':5,},        
                            },
      'cmain_5':{
      'type':'build_up',        
      'info':'cmain_5_info',        
      'goto_cfg':{
               'type':2,       
             'buildingID':'building002',
                      },
      'need':[2,2,['building002']],        
      'main_reward':{'item037':5,'gold':25000,},        
                            },
      'cmain_6':{
      'type':'build_up',        
      'info':'cmain_6_info',        
      'goto_cfg':{
               'type':2,       
             'buildingID':'building009',
                      },
      'need':[2,2,['building009'],],        
      'main_reward':{'iron':30000,'food':30000,},        
                            },
      'cmain_7':{
      'type':'train_soldier',        
      'info':'cmain_7_info',        
      'goto_cfg':{
               'type':2,       
             'buildingID':'building009',
                      },
      'need':[500,0],        
      'main_reward':{'food':30000,'iron':30000,},        
                            },
      'cmain_8':{
      'type':'ts_pub',        
      'info':'cmain_8_info',        
      'goto_cfg':{
               'type':2,       
             'state':2,
             'secondMenu':'bubble',
             'buildingID':'building005',
                      },
      'need':[2,'hero_box2'],        
      'main_reward':{'wood':30000,'food':30000,},        
                            },
      'cmain_9':{
      'type':'com_people',        
      'info':'cmain_9_info',        
      'goto_cfg':{
               'type':2,       
             'secondMenu':'bubble',
                      },
      'need':[2,['248','66','950']],        
      'main_reward':{'item032':3,'item002':200,},        
                            },
      'cmain_200':{
      'type':'build_up',        
      'info':'cmain_200_info',        
      'goto_cfg':{
               'type':2,       
             'buildingID':'building002',
                      },
      'need':[2,3,['building002']],        
      'main_reward':{'item037':5,'gold':20000,},        
                            },
      'cmain_22':{
      'type':'build_up',        
      'info':'cmain_22_info',        
      'goto_cfg':{
               'type':2,       
             'buildingID':'building026',
                      },
      'need':[2,3,['building026','building027','building028']],        
      'main_reward':{'iron':40000,'gold':20000,},        
                            },
      'cmain_22':{
      'type':'build_up',        
      'info':'cmain_22_info',        
      'goto_cfg':{
               'type':2,       
             'buildingID':'building022',
                      },
      'need':[2,3,['building022'],],        
      'main_reward':{'food':40000,'item202':200,},        
                            },
      'cmain_23':{
      'type':'train_soldier',        
      'info':'cmain_23_info',        
      'goto_cfg':{
               'type':2,       
             'buildingID':'building022',
                      },
      'need':[20000,3],        
      'main_reward':{'gold':20000,'wood':40000,},        
                            },
      'cmain_24':{
      'type':'build_up',        
      'info':'cmain_24_info',        
      'goto_cfg':{
               'type':2,       
             'buildingID':'building002',
                      },
      'need':[2,6,['building002']],        
      'main_reward':{'item032':3,'item002':5,},        
                            },
      'cmain_25':{
      'type':'ts_catch',        
      'info':'cmain_25_info',        
      'goto_cfg':{
               
             'heroCatch':2,
                      },
      'need':[2],        
      'main_reward':{'item037':5,'gold':20000,},        
                            },
      'cmain_26':{
      'type':'ts_both',        
      'info':'cmain_26_info',        
      'goto_cfg':{
               'type':3,       
            'panelID':'pk',
                      },
      'need':[5],        
      'main_reward':{'iron':40000,'food':40000,},        
                            },
      'cmain_27':{
      'type':'ts_peoplesell',        
      'info':'cmain_27_info',        
      'goto_cfg':{
               
             'secondMenu': 'hero_shop',
            'panelID':'shop',
                      },
      'need':[2],        
      'main_reward':{'wood':40000,'food':40000,},        
                            },
      'cmain_28':{
      'type':'ts_gtask',        
      'info':'cmain_28_info',        
      'goto_cfg':{
               'type':3,       
             'secondMenu':0,
            'panelID':'task',
                      },
      'need':[3],        
      'main_reward':{'gold':20000,'item220':200,},        
                            },
      'cmain_29':{
      'type':'build_up',        
      'info':'cmain_29_info',        
      'goto_cfg':{
               'type':2,       
             'buildingID':'building002',
                      },
      'need':[2,7,['building002']],        
      'main_reward':{'item032':3,'item002':5,},        
                            },
      'cmain_20':{
      'type':'build_up',        
      'info':'cmain_20_info',        
      'goto_cfg':{
               'type':2,       
             'buildingID':'building009',
                      },
      'need':[2,7,['building009','building0200','building022','building022']],        
      'main_reward':{'item037':5,'gold':20000,},        
                            },
      'cmain_22':{
      'type':'cum_train_soldier',        
      'info':'cmain_22_info',        
      'goto_cfg':{
               'type':2,       
             'buildingID':'building009',
                      },
      'need':[5000,'all'],        
      'main_reward':{'iron':40000,'wood':40000,},        
                            },
      'cmain_22':{
      'type':'ts_alien',        
      'info':'cmain_22_info',        
      'goto_cfg':{
               'type':3,       
             'secondMenu':4,
            'panelID':'country',
                      },
      'need':[2,],        
      'main_reward':{'wood':40000,'food':40000,},        
                            },
      'cmain_23':{
      'type':'ts_cutwill',        
      'info':'cmain_23_info',        
      'goto_cfg':{
               
            'panelID':'VIEW_CLIMB_MAIN',
                      },
      'need':[2],        
      'main_reward':{'gold':20000,'item223':200,},        
                            },
      'cmain_24':{
      'type':'com_people',        
      'info':'cmain_24_info',        
      'goto_cfg':{
               'type':2,       
             'secondMenu':'bubble',
                      },
      'need':[2,['260','33','348']],        
      'main_reward':{'item032':3,'item002':5,},        
                            },
      'cmain_25':{
      'type':'open_privilege',        
      'info':'cmain_25_info',        
      'goto_cfg':{
               'type':3,       
            'panelID':'office',
                      },
      'need':[3],        
      'main_reward':{'item037':5,'gold':25000,},        
                            },
      'cmain_26':{
      'type':'ts_asked',        
      'info':'cmain_26_info',        
      'goto_cfg':{
               'type':3,       
            'panelID':'prop_resolve',
                      },
      'need':[2],        
      'main_reward':{'gold':20000,'wood':40000,},        
                            },
      'cmain_27':{
      'type':'ts_pve',        
      'info':'cmain_27_info',        
      'goto_cfg':{
               'type':3,       
            'panelID':'pve',
                      },
      'need':[25],        
      'main_reward':{'iron':40000,'food':40000,},        
                            },
      'cmain_28':{
      'type':'build_up',        
      'info':'cmain_28_info',        
      'goto_cfg':{
               'type':2,       
             'buildingID':'building002',
                      },
      'need':[2,9,['building002']],        
      'main_reward':{'gold':20000,'food':40000,},        
                            },
      'cmain_29':{
      'type':'com_people',        
      'info':'cmain_29_info',        
      'goto_cfg':{
               'type':2,       
             'secondMenu':'bubble',
                      },
      'need':[2,['242','70','340']],        
      'main_reward':{'item037':5,'item002':5,},        
                            },
      'cmain_30':{
      'type':'ts_peoplesell',        
      'info':'cmain_30_info',        
      'goto_cfg':{
               
             'secondMenu': 'hero_shop',
            'panelID':'shop',
                      },
      'need':[2,],        
      'main_reward':{'iron':40000,'item020':5,},        
                            },
      'cmain_32':{
      'type':'ts_build',        
      'info':'cmain_32_info',        
      'goto_cfg':{
               'type':2,       
             'state':2,
             'cityID':['246','32','953'],
             'secondMenu':3,
                      },
      'need':[5,'all','all'],        
      'main_reward':{'wood':40000,'item029':5,},        
                            },
      'cmain_32':{
      'type':'cum_estate',        
      'info':'cmain_32_info',        
      'goto_cfg':{
               'type':2,       
             'estateID':3,
             'cityID':['260','66','348'],
                      },
      'need':[2,2,'3'],        
      'main_reward':{'item073':2,'item047':2000,},        
                            },
      'cmain_33':{
      'type':'estate_active',        
      'info':'cmain_33_info',        
      'goto_cfg':{
               'type':2,       
             'estateID':3,
             'cityID':['260','66','348'],
                      },
      'need':[2,'3'],        
      'main_reward':{'gold':20000,'item079':5,},        
                            },
      'cmain_34':{
      'type':'build_up',        
      'info':'cmain_34_info',        
      'goto_cfg':{
               'type':2,       
             'buildingID':'building002',
                      },
      'need':[2,200,['building002']],        
      'main_reward':{'item402':5,'food':40000,},        
                            },
      'cmain_95':{
      'type':'com_people',        
      'info':'cmain_95_info',        
      'goto_cfg':{
               'type':2,       
             'secondMenu':'bubble',
                      },
      'need':[2,['234','26','364']],        
      'main_reward':{'item037':5,'item002':5,},        
                            },
      'cmain_36':{
      'type':'ts_treasuer',        
      'info':'cmain_36_info',        
      'goto_cfg':{
               
             'secondMenu': 'treasuer_shop',
            'panelID':'shop',
                      },
      'need':[2,],        
      'main_reward':{'iron':40000,'item023':5,},        
                            },
      'cmain_37':{
      'type':'cum_visit',        
      'info':'cmain_37_info',        
      'goto_cfg':{
               
             'visit':2,
                      },
      'need':[2,],        
      'main_reward':{'wood':40000,'item029':5,},        
                            },
      'cmain_38':{
      'type':'ts_thief',        
      'info':'cmain_38_info',        
      'goto_cfg':{
               'type':3,       
            'panelID':'npc_info',
                      },
      'need':[2,],        
      'main_reward':{'food':40000,'item048':2000,},        
                            },
      'cmain_39':{
      'type':'ts_frequents',        
      'info':'cmain_39_info',        
      'goto_cfg':{
               'type':2,       
                      },
      'need':[3,],        
      'main_reward':{'gold':20000,'item080':5,},        
                            },
      'cmain_40':{
      'type':'build_up',        
      'info':'cmain_40_info',        
      'goto_cfg':{
               'type':2,       
             'buildingID':'building008',
                      },
      'need':[2,200,['building008']],        
      'main_reward':{'item402':5,'food':40000,},        
                            },
      'cmain_42':{
      'type':'cum_asked',        
      'info':'cmain_42_info',        
      'goto_cfg':{
               'type':2,       
            'panelID':'prop_resolve',
                      },
      'need':[30],        
      'main_reward':{'item403':5,'food':40000,},        
                            },
      'cmain_42':{
      'type':'get_office',        
      'info':'cmain_42_info',        
      'goto_cfg':{
               'type':3,       
             'secondMenu':'402',
            'panelID':'office',
                      },
      'need':[2,4],        
      'main_reward':{'item037':5,'item002':5,},        
                            },
      'cmain_43':{
      'type':'build_up',        
      'info':'cmain_43_info',        
      'goto_cfg':{
               'type':2,       
             'buildingID':'building005',
                      },
      'need':[2,4,['building005']],        
      'main_reward':{'iron':40000,'item023':5,},        
                            },
      'cmain_44':{
      'type':'com_sand',        
      'info':'cmain_44_info',        
      'goto_cfg':{
               'type':3,       
            'panelID':'pve',
                      },
      'need':[2,'chapter006','battle072',3],        
      'main_reward':{'wood':40000,'item029':5,},        
                            },
      'cmain_45':{
      'type':'com_sand',        
      'info':'cmain_45_info',        
      'goto_cfg':{
               'type':3,       
            'panelID':'pve',
                      },
      'need':[2,'chapter009','battle2008','all'],        
      'main_reward':{'food':40000,'item050':2000
,},        
                            },
      'cmain_46':{
      'type':'degree_build',        
      'info':'cmain_46_info',        
      'goto_cfg':{
               'type':2,       
             'state':2,
             'cityID':['246','32','953'],
             'secondMenu':3,
                      },
      'need':[9999],        
      'main_reward':{'gold':20000,'item082':5,},        
                            },
      'cmain_47':{
      'type':'com_people',        
      'info':'cmain_47_info',        
      'goto_cfg':{
               'type':2,       
             'secondMenu':'bubble',
                      },
      'need':[2,['257','23','338']],        
      'main_reward':{'item037':5,'item002':5,},        
                            },
      'cmain_48':{
      'type':'build_up',        
      'info':'cmain_48_info',        
      'goto_cfg':{
               'type':2,       
             'buildingID':'building002',
                      },
      'need':[2,22,['building002']],        
      'main_reward':{'iron':40000,'item023':5,},        
                            },
      'cmain_49':{
      'type':'equip_num',        
      'info':'cmain_49_info',        
      'goto_cfg':{
               'type':2,       
             'secondMenu':2,
             'buildingID':'building002',
                      },
      'need':[2,'all'],        
      'main_reward':{'wood':40000,'item029':5,},        
                            },
      'cmain_50':{
      'type':'learn_skill',        
      'info':'cmain_50_info',        
      'goto_cfg':{
               'type':3,       
            'panelID':'hero',
                      },
      'need':[2,8,],        
      'main_reward':{'food':40000,'item049':2000,},        
                            },
      'cmain_52':{
      'type':'ts_alien',        
      'info':'cmain_52_info',        
      'goto_cfg':{
               'type':3,       
             'secondMenu':4,
            'panelID':'country',
                      },
      'need':[3,],        
      'main_reward':{'gold':20000,'item082':5,},        
                            },
      'cmain_52':{
      'type':'cum_gtask',        
      'info':'cmain_52_info',        
      'goto_cfg':{
               'type':3,       
             'secondMenu':0,
            'panelID':'task',
                      },
      'need':[20],        
      'main_reward':{'item404':5,'food':40000,},        
                            },
      'cmain_53':{
      'type':'build_up',        
      'info':'cmain_53_info',        
      'goto_cfg':{
               'type':2,       
             'buildingID':'building007',
                      },
      'need':[2,4,['building007']],        
      'main_reward':{'item037':5,'item002':5,},        
                            },
      'cmain_54':{
      'type':'build_up',        
      'info':'cmain_54_info',        
      'goto_cfg':{
               'type':2,       
             'buildingID':'building003',
                      },
      'need':[2,3,['building003']],        
      'main_reward':{'iron':40000,'item020':5,},        
                            },
      'cmain_75':{
      'type':'cum_estate',        
      'info':'cmain_75_info',        
      'goto_cfg':{
               'type':2,       
             'estateID':5,
             'cityID':['246','32','953'],
                      },
      'need':[2,2,'5'],        
      'main_reward':{'item074':2,'item029':5,},        
                            },
      'cmain_56':{
      'type':'estate_active',        
      'info':'cmain_56_info',        
      'goto_cfg':{
               'type':2,       
             'estateID':5,
             'cityID':['246','32','953'],
                      },
      'need':[2,'5'],        
      'main_reward':{'wood':40000,'item034':2000,},        
                            },
      'cmain_57':{
      'type':'ts_cutwill',        
      'info':'cmain_57_info',        
      'goto_cfg':{
               
            'panelID':'VIEW_CLIMB_MAIN',
                      },
      'need':[2],        
      'main_reward':{'gold':20000,'item223':200,},        
                            },
      'cmain_58':{
      'type':'open_privilege',        
      'info':'cmain_58_info',        
      'goto_cfg':{
               'type':3,       
            'panelID':'office',
                      },
      'need':[200],        
      'main_reward':{'item402':5,'food':40000,},        
                            },
      'cmain_59':{
      'type':'com_people',        
      'info':'cmain_59_info',        
      'goto_cfg':{
               'type':2,       
             'secondMenu':'bubble',
                      },
      'need':[2,['264','275','222']],        
      'main_reward':{'item037':5,'item002':5,},        
                            },
      'cmain_60':{
      'type':'hero_quality',        
      'info':'cmain_60_info',        
      'goto_cfg':{
               'type':3,       
            'panelID':'hero',
                      },
      'need':[2,6],        
      'main_reward':{'iron':40000,'item023':5,},        
                            },
      'cmain_62':{
      'type':'cum_estate',        
      'info':'cmain_62_info',        
      'goto_cfg':{
               'type':2,       
             'estateID':6,
             'cityID':['246','32','953'],
                      },
      'need':[2,2,'6'],        
      'main_reward':{'item072':2,'item029':5,},        
                            },
      'cmain_62':{
      'type':'estate_active',        
      'info':'cmain_62_info',        
      'goto_cfg':{
               'type':2,       
             'estateID':6,
             'cityID':['246','32','953'],
                      },
      'need':[2,'6'],        
      'main_reward':{'wood':40000,'item034':2000,},        
                            },
      'cmain_63':{
      'type':'grade_up',        
      'info':'cmain_63_info',        
      'goto_cfg':{
               'type':3,       
            'panelID':'hero',
                      },
      'need':[2,20,[0,2]],        
      'main_reward':{'gold':20000,'item2007':200,},        
                            },
      'cmain_64':{
      'type':'degree_build',        
      'info':'cmain_64_info',        
      'goto_cfg':{
               'type':2,       
             'state':2,
             'cityID':['246','32','953'],
             'secondMenu':3,
                      },
      'need':[20000],        
      'main_reward':{'item402':5,'food':40000,},        
                            },
      'cmain_65':{
      'type':'com_people',        
      'info':'cmain_65_info',        
      'goto_cfg':{
               'type':2,       
             'secondMenu':'bubble',
                      },
      'need':[2,['277','28','326']],        
      'main_reward':{'item037':5,'item002':5,},        
                            },
      'cmain_66':{
      'type':'cum_asked',        
      'info':'cmain_66_info',        
      'goto_cfg':{
               'type':2,       
            'panelID':'prop_resolve',
                      },
      'need':[80],        
      'main_reward':{'iron':40000,'item023':5,},        
                            },
      'cmain_67':{
      'type':'cum_estate',        
      'info':'cmain_67_info',        
      'goto_cfg':{
               'type':2,       
                      },
      'need':[200,'all','all'],        
      'main_reward':{'wood':40000,'item029':5,},        
                            },
      'cmain_68':{
      'type':'cum_train_soldier',        
      'info':'cmain_68_info',        
      'goto_cfg':{
               'type':2,       
             'buildingID':'building009',
                      },
      'need':[2000000,'all'],        
      'main_reward':{'item403':5,'item224':200,},        
                            },
      'cmain_69':{
      'type':'cum_kill',        
      'info':'cmain_69_info',        
      'goto_cfg':{
               'type':2,       
                      },
      'need':[500000,],        
      'main_reward':{'item404':5,'food':40000,},        
                            },
      'cmain_70':{
      'type':'build_up',        
      'info':'cmain_70_info',        
      'goto_cfg':{
               'type':2,       
             'buildingID':'building002',
                      },
      'need':[2,23,['building002']],        
      'main_reward':{'item070':3,'item230':200,},        
                            },
      'cmain_72':{
      'type':'build_up',        
      'info':'cmain_72_info',        
      'goto_cfg':{
               'type':2,       
             'buildingID':'building009',
                      },
      'need':[2,24,['building009','building0200','building022','building022']],        
      'main_reward':{'item037':5,'item002':5,},        
                            },
      'cmain_72':{
      'type':'skill_lv',        
      'info':'cmain_72_info',        
      'goto_cfg':{
               'type':3,       
            'panelID':'hero',
                      },
      'need':[2,200],        
      'main_reward':{'iron':40000,'item020':5,},        
                            },
      'cmain_73':{
      'type':'rank_both',        
      'info':'cmain_73_info',        
      'goto_cfg':{
               'type':3,       
            'panelID':'pk',
                      },
      'need':[2,200],        
      'main_reward':{'food':40000,'item029':5,},        
                            },
      'cmain_74':{
      'type':'ts_catch',        
      'info':'cmain_74_info',        
      'goto_cfg':{
               'type':2,       
             'heroCatch':2,
                      },
      'need':[7],        
      'main_reward':{'wood':40000,'item062':5,},        
                            },
      'cmain_75':{
      'type':'ts_frequents',        
      'info':'cmain_75_info',        
      'goto_cfg':{
               'type':2,       
                      },
      'need':[5,],        
      'main_reward':{'gold':20000,'item063':5,},        
                            },
      'cmain_76':{
      'type':'build_up',        
      'info':'cmain_76_info',        
      'goto_cfg':{
               'type':2,       
             'buildingID':'building002',
                      },
      'need':[2,24,['building002']],        
      'main_reward':{'item2000':2,'gold':20000,},        
                            },
      'cmain_77':{
      'type':'com_people',        
      'info':'cmain_77_info',        
      'goto_cfg':{
               'type':2,       
             'secondMenu':'bubble',
                      },
      'need':[2,['223','75','203']],        
      'main_reward':{'item037':5,'item002':5,},        
                            },
      'cmain_78':{
      'type':'ts_build',        
      'info':'cmain_78_info',        
      'goto_cfg':{
               'type':2,       
             'state':2,
             'secondMenu':3,
                      },
      'need':[2,[['223'],['75'],['203']],['b95','b26','b32']],        
      'main_reward':{'iron':40000,'item020':5,},        
                            },
      'cmain_79':{
      'type':'cum_pk_yard',        
      'info':'cmain_79_info',        
      'goto_cfg':{
               'type':3,       
            'panelID':'pk_yard',
                      },
      'need':[2,],        
      'main_reward':{'food':40000,'item029':5,},        
                            },
      'cmain_80':{
      'type':'equip_num',        
      'info':'cmain_80_info',        
      'goto_cfg':{
               'type':2,       
             'secondMenu':2,
             'buildingID':'building002',
                      },
      'need':[5,'all'],        
      'main_reward':{'wood':40000,'item064':5,},        
                            },
      'cmain_82':{
      'type':'com_sand',        
      'info':'cmain_82_info',        
      'goto_cfg':{
               'type':3,       
            'panelID':'pve',
                      },
      'need':[2,'chapter022','battle244','all'],        
      'main_reward':{'gold':20000,'item065':5,},        
                            },
      'cmain_82':{
      'type':'get_office',        
      'info':'cmain_82_info',        
      'goto_cfg':{
               'type':3,       
             'secondMenu':'502',
            'panelID':'office',
                      },
      'need':[2,5],        
      'main_reward':{'item2002':2,'gold':20000,},        
                            },
      'cmain_83':{
      'type':'com_people',        
      'info':'cmain_83_info',        
      'goto_cfg':{
               'type':2,       
             'secondMenu':'bubble',
                      },
      'need':[2,['96','327','297']],        
      'main_reward':{'item037':5,'item002':5,},        
                            },
      'cmain_84':{
      'type':'have_hero',        
      'info':'cmain_84_info',        
      'goto_cfg':{
               'type':3,       
            'panelID':'hero',
                      },
      'need':[3,[3]],        
      'main_reward':{'wood':40000,'item407':20,},        
                            },
      'cmain_85':{
      'type':'build_up',        
      'info':'cmain_85_info',        
      'goto_cfg':{
               'type':2,       
             'buildingID':'building008',
                      },
      'need':[2,25,['building008']],        
      'main_reward':{'iron':40000,'item030':5,},        
                            },
      'cmain_86':{
      'type':'ts_thief',        
      'info':'cmain_86_info',        
      'goto_cfg':{
               'type':3,       
            'panelID':'npc_info',
                      },
      'need':[3,],        
      'main_reward':{'food':40000,'item406':20,},        
                            },
      'cmain_87':{
      'type':'equip_quality',        
      'info':'cmain_87_info',        
      'goto_cfg':{
               'type':2,       
             'buildingID':'building002',
                      },
      'need':[2,'all',3],        
      'main_reward':{'gold':20000,'item408':20,},        
                            },
      'cmain_88':{
      'type':'ts_watch',        
      'info':'cmain_88_info',        
      'goto_cfg':{
               'type':2,       
             'secondMenu':2,
             'buildingID':'building006',
                      },
      'need':[200],        
      'main_reward':{'item022':5,'item409':20,},        
                            },
      'cmain_89':{
      'type':'com_people',        
      'info':'cmain_89_info',        
      'goto_cfg':{
               'type':2,       
             'secondMenu':'bubble',
                      },
      'need':[2,['75','309','275']],        
      'main_reward':{'item037':5,'item003':5,},        
                            },
      'cmain_75':{
      'type':'build_up',        
      'info':'cmain_75_info',        
      'goto_cfg':{
               'type':2,       
             'buildingID':'building007',
                      },
      'need':[2,5,['building007']],        
      'main_reward':{'iron':40000,'item030':5,},        
                            },
      'cmain_92':{
      'type':'hero_quality',        
      'info':'cmain_92_info',        
      'goto_cfg':{
               'type':3,       
            'panelID':'hero',
                      },
      'need':[2,22],        
      'main_reward':{'food':40000,'item405':5,},        
                            },
      'cmain_92':{
      'type':'ts_frequents',        
      'info':'cmain_92_info',        
      'goto_cfg':{
               'type':2,       
                      },
      'need':[200,],        
      'main_reward':{'wood':40000,'item066':5,},        
                            },
      'cmain_93':{
      'type':'cum_estate',        
      'info':'cmain_93_info',        
      'goto_cfg':{
               'type':2,       
                      },
      'need':[25,'all','all'],        
      'main_reward':{'gold':20000,'item067':5,},        
                            },
      'cmain_94':{
      'type':'ts_mining',        
      'info':'cmain_94_info',        
      'goto_cfg':{
               'type':3,       
            'panelID':'mining',
                      },
      'need':[6,],        
      'main_reward':{'item022':5,'item068':5,},        
                            },
      'cmain_95':{
      'type':'com_people',        
      'info':'cmain_95_info',        
      'goto_cfg':{
               'type':2,       
             'secondMenu':'bubble',
                      },
      'need':[2,['272','24','306']],        
      'main_reward':{'item037':5,'item003':5,},        
                            },
      'cmain_96':{
      'type':'build_up',        
      'info':'cmain_96_info',        
      'goto_cfg':{
               'type':2,       
             'buildingID':'building002',
                      },
      'need':[2,26,['building002']],        
      'main_reward':{'iron':40000,'item030':5,},        
                            },
      'cmain_97':{
      'type':'degree_build',        
      'info':'cmain_97_info',        
      'goto_cfg':{
               'type':2,       
             'state':2,
             'cityID':['246','32','953'],
             'secondMenu':3,
                      },
      'need':[60000],        
      'main_reward':{'food':40000,'item070':3,},        
                            },
      'cmain_98':{
      'type':'equip_num',        
      'info':'cmain_98_info',        
      'goto_cfg':{
               'type':2,       
             'buildingID':'building002',
                      },
      'need':[2,4,],        
      'main_reward':{'wood':40000,'item072':3,},        
                            },
      'cmain_99':{
      'type':'ts_thief',        
      'info':'cmain_99_info',        
      'goto_cfg':{
               'type':3,       
            'panelID':'npc_info',
                      },
      'need':[200,],        
      'main_reward':{'gold':20000,'item2007':20,},        
                            },
      'cmain_2000':{
      'type':'rank_both',        
      'info':'cmain_2000_info',        
      'goto_cfg':{
               'type':3,       
            'panelID':'pk',
                      },
      'need':[2,2000],        
      'main_reward':{'item2004':2,'item076':2,},        
                            },

},

}