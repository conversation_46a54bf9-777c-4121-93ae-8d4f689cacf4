{
############循环充值#####################
	'rool_pay':{
		'show_moneylimit':300,	#累积充值大于6块的玩家才可见
		'rool_mail_name':'rool_mail_name',
		'rool_mail_info':'rool_mail_info',
		'open_days':{'1_370':'goods1'},  #正式配置 
		'open_date':datetime.datetime(2019,1,16,9,0),  #活动上架时间  开服日期的零点-这个时间的零点 向下取整
		'goods1':{
			'hero':'hero998',#'hero765',
			#'show_skill':'skill272',
			'pay_money':1000,			#循环充值金额			
			'first_reward':{

				'item2013':1,		
			},
			'reward':{

				'item2013':1,	
			}
		},
	
	},

	'free_buy':{
		'show_info':{
			'pay_gold':['pay_gold_name','pay_gold_info','hero404'],	
			'pay_food':['pay_food_name','pay_food_info','hero403'],	
			'pay_wood':['pay_wood_name','pay_wood_info','hero401'],	
			'pay_iron':['pay_iron_name','pay_iron_info','hero405'],	
			'buy_gold':['buy_gold_name','buy_gold_info','hero408'],	
			'buy_food':['buy_food_name','buy_food_info','hero404'],	
			'buy_wood':['buy_wood_name','buy_wood_info','hero401'],	
			'buy_iron':['buy_iron_name','buy_iron_info','hero405'],	
			'baggage_gold':['baggage_gold_name','baggage_gold_info','hero403'],	
			'baggage_food':['baggage_food_name','baggage_food_info','hero401'],	
			'baggage_wood':['baggage_wood_name','baggage_wood_info','hero402'],	
			'baggage_iron':['baggage_iron_name','baggage_iron_info','hero405'],	
		},





















 















    '6':{
            'gold':{
                     'limit':[[20000,0.3],[50000,0.1]],
                     'event':[
                             [['pay',120,60,36000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,189000],1500],
                             [['buy',120,50,21000],1000],
                             [['baggage',60,20,2],1000],
                                   ],
                       },
              'food':{
                        'limit':[[20000,0.3],[50000,0.1]],
                        'event':[
                                    [['pay',120,60,72000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,378000],1500],
                                    [['buy',120,50,42000],1000],
                                     [['baggage',60,20,2],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[20000,0.3],[50000,0.1]],
                         'event':[
                                  [['pay',120,60,72000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,378000],1500],
                                  [['buy',120,50,42000],1000],
                                   [['baggage',60,20,2],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[20000,0.3],[50000,0.1]],
                           'event':[
                                    [['pay',120,60,72000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,378000],1500],
                                    [['buy',120,50,42000],1000],
                                   [['baggage',60,20,2],1000],
                                        ],
                        },
                         },
    '7':{
            'gold':{
                     'limit':[[20000,0.3],[50000,0.1]],
                     'event':[
                             [['pay',120,60,36000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,189000],1500],
                             [['buy',120,50,21000],1000],
                             [['baggage',60,40,3],1000],
                                   ],
                       },
              'food':{
                        'limit':[[20000,0.3],[50000,0.1]],
                        'event':[
                                    [['pay',120,60,72000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,378000],1500],
                                    [['buy',120,50,42000],1000],
                                     [['baggage',60,40,3],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[20000,0.3],[50000,0.1]],
                         'event':[
                                  [['pay',120,60,72000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,378000],1500],
                                  [['buy',120,50,42000],1000],
                                   [['baggage',60,40,3],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[20000,0.3],[50000,0.1]],
                           'event':[
                                    [['pay',120,60,72000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,378000],1500],
                                    [['buy',120,50,42000],1000],
                                   [['baggage',60,40,3],1000],
                                        ],
                        },
                         },
    '8':{
            'gold':{
                     'limit':[[20000,0.3],[50000,0.1]],
                     'event':[
                             [['pay',120,60,36000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,189000],1500],
                             [['buy',120,50,21000],1000],
                             [['baggage',60,40,3],1000],
                                   ],
                       },
              'food':{
                        'limit':[[20000,0.3],[50000,0.1]],
                        'event':[
                                    [['pay',120,60,72000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,378000],1500],
                                    [['buy',120,50,42000],1000],
                                     [['baggage',60,40,3],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[20000,0.3],[50000,0.1]],
                         'event':[
                                  [['pay',120,60,72000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,378000],1500],
                                  [['buy',120,50,42000],1000],
                                   [['baggage',60,40,3],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[20000,0.3],[50000,0.1]],
                           'event':[
                                    [['pay',120,60,72000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,378000],1500],
                                    [['buy',120,50,42000],1000],
                                   [['baggage',60,40,3],1000],
                                        ],
                        },
                         },
    '9':{
            'gold':{
                     'limit':[[20000,0.3],[50000,0.1]],
                     'event':[
                             [['pay',120,60,39000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,204750],1500],
                             [['buy',120,60,27300],1000],
                             [['baggage',60,40,3],1000],
                                   ],
                       },
              'food':{
                        'limit':[[20000,0.3],[50000,0.1]],
                        'event':[
                                    [['pay',120,60,78000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,409500],1500],
                                    [['buy',120,60,54600],1000],
                                     [['baggage',60,40,3],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[20000,0.3],[50000,0.1]],
                         'event':[
                                  [['pay',120,60,78000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,409500],1500],
                                  [['buy',120,60,54600],1000],
                                   [['baggage',60,40,3],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[20000,0.3],[50000,0.1]],
                           'event':[
                                    [['pay',120,60,78000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,409500],1500],
                                    [['buy',120,60,54600],1000],
                                   [['baggage',60,40,3],1000],
                                        ],
                        },
                         },
    '10':{
            'gold':{
                     'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                     'event':[
                             [['pay',120,60,39000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,204750],1500],
                             [['buy',120,60,27300],1000],
                             [['baggage',60,40,3],1000],
                                   ],
                       },
              'food':{
                        'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                        'event':[
                                    [['pay',120,60,78000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,409500],1500],
                                    [['buy',120,60,54600],1000],
                                     [['baggage',60,40,3],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                         'event':[
                                  [['pay',120,60,78000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,409500],1500],
                                  [['buy',120,60,54600],1000],
                                   [['baggage',60,40,3],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                           'event':[
                                    [['pay',120,60,78000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,409500],1500],
                                    [['buy',120,60,54600],1000],
                                   [['baggage',60,40,3],1000],
                                        ],
                        },
                         },
    '11':{
            'gold':{
                     'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                     'event':[
                             [['pay',120,60,39000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,204750],1500],
                             [['buy',120,60,27300],1000],
                             [['baggage',60,40,3],1000],
                                   ],
                       },
              'food':{
                        'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                        'event':[
                                    [['pay',120,60,78000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,409500],1500],
                                    [['buy',120,60,54600],1000],
                                     [['baggage',60,40,3],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                         'event':[
                                  [['pay',120,60,78000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,409500],1500],
                                  [['buy',120,60,54600],1000],
                                   [['baggage',60,40,3],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                           'event':[
                                    [['pay',120,60,78000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,409500],1500],
                                    [['buy',120,60,54600],1000],
                                   [['baggage',60,40,3],1000],
                                        ],
                        },
                         },
    '12':{
            'gold':{
                     'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                     'event':[
                             [['pay',120,60,42000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,220500],1500],
                             [['buy',120,70,34300],1000],
                             [['baggage',60,40,3],1000],
                                   ],
                       },
              'food':{
                        'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                        'event':[
                                    [['pay',120,60,84000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,441000],1500],
                                    [['buy',120,70,68600],1000],
                                     [['baggage',60,40,3],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                         'event':[
                                  [['pay',120,60,84000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,441000],1500],
                                  [['buy',120,70,68600],1000],
                                   [['baggage',60,40,3],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                           'event':[
                                    [['pay',120,60,84000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,441000],1500],
                                    [['buy',120,70,68600],1000],
                                   [['baggage',60,40,3],1000],
                                        ],
                        },
                         },
    '13':{
            'gold':{
                     'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                     'event':[
                             [['pay',120,60,42000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,220500],1500],
                             [['buy',120,70,34300],1000],
                             [['baggage',60,60,4],1000],
                                   ],
                       },
              'food':{
                        'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                        'event':[
                                    [['pay',120,60,84000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,441000],1500],
                                    [['buy',120,70,68600],1000],
                                     [['baggage',60,60,4],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                         'event':[
                                  [['pay',120,60,84000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,441000],1500],
                                  [['buy',120,70,68600],1000],
                                   [['baggage',60,60,4],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                           'event':[
                                    [['pay',120,60,84000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,441000],1500],
                                    [['buy',120,70,68600],1000],
                                   [['baggage',60,60,4],1000],
                                        ],
                        },
                         },
    '14':{
            'gold':{
                     'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                     'event':[
                             [['pay',120,60,42000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,220500],1500],
                             [['buy',120,70,34300],1000],
                             [['baggage',60,60,4],1000],
                                   ],
                       },
              'food':{
                        'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                        'event':[
                                    [['pay',120,60,84000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,441000],1500],
                                    [['buy',120,70,68600],1000],
                                     [['baggage',60,60,4],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                         'event':[
                                  [['pay',120,60,84000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,441000],1500],
                                  [['buy',120,70,68600],1000],
                                   [['baggage',60,60,4],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[20000,0.3],[50000,0.2],[100000,0.1]],
                           'event':[
                                    [['pay',120,60,84000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,441000],1500],
                                    [['buy',120,70,68600],1000],
                                   [['baggage',60,60,4],1000],
                                        ],
                        },
                         },
    '15':{
            'gold':{
                     'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                     'event':[
                             [['pay',120,60,45000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,236250],1500],
                             [['buy',120,80,42000],1000],
                             [['baggage',60,60,4],1000],
                                   ],
                       },
              'food':{
                        'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                        'event':[
                                    [['pay',120,60,90000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,472500],1500],
                                    [['buy',120,80,84000],1000],
                                     [['baggage',60,60,4],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                         'event':[
                                  [['pay',120,60,90000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,472500],1500],
                                  [['buy',120,80,84000],1000],
                                   [['baggage',60,60,4],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                           'event':[
                                    [['pay',120,60,90000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,472500],1500],
                                    [['buy',120,80,84000],1000],
                                   [['baggage',60,60,4],1000],
                                        ],
                        },
                         },
    '16':{
            'gold':{
                     'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                     'event':[
                             [['pay',120,60,45000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,236250],1500],
                             [['buy',120,80,42000],1000],
                             [['baggage',60,60,4],1000],
                                   ],
                       },
              'food':{
                        'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                        'event':[
                                    [['pay',120,60,90000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,472500],1500],
                                    [['buy',120,80,84000],1000],
                                     [['baggage',60,60,4],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                         'event':[
                                  [['pay',120,60,90000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,472500],1500],
                                  [['buy',120,80,84000],1000],
                                   [['baggage',60,60,4],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                           'event':[
                                    [['pay',120,60,90000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,472500],1500],
                                    [['buy',120,80,84000],1000],
                                   [['baggage',60,60,4],1000],
                                        ],
                        },
                         },
    '17':{
            'gold':{
                     'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                     'event':[
                             [['pay',120,60,45000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,236250],1500],
                             [['buy',120,80,42000],1000],
                             [['baggage',60,60,4],1000],
                                   ],
                       },
              'food':{
                        'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                        'event':[
                                    [['pay',120,60,90000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,472500],1500],
                                    [['buy',120,80,84000],1000],
                                     [['baggage',60,60,4],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                         'event':[
                                  [['pay',120,60,90000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,472500],1500],
                                  [['buy',120,80,84000],1000],
                                   [['baggage',60,60,4],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[30000,0.3],[100000,0.2],[200000,0.1]],
                           'event':[
                                    [['pay',120,60,90000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,472500],1500],
                                    [['buy',120,80,84000],1000],
                                   [['baggage',60,60,4],1000],
                                        ],
                        },
                         },
    '18':{
            'gold':{
                     'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                     'event':[
                             [['pay',120,60,48000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,252000],1500],
                             [['buy',120,90,50400],500],
                             [['baggage',60,60,4],1000],
                                   ],
                       },
              'food':{
                        'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                        'event':[
                                    [['pay',120,60,96000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,504000],1500],
                                    [['buy',120,90,100800],500],
                                     [['baggage',60,60,4],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                         'event':[
                                  [['pay',120,60,96000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,504000],1500],
                                  [['buy',120,90,100800],500],
                                   [['baggage',60,60,4],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                           'event':[
                                    [['pay',120,60,96000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,504000],1500],
                                    [['buy',120,90,100800],500],
                                   [['baggage',60,60,4],1000],
                                        ],
                        },
                         },
    '19':{
            'gold':{
                     'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                     'event':[
                             [['pay',120,60,48000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,252000],1500],
                             [['buy',120,90,50400],1000],
                             [['baggage',60,100,5],1000],
                                   ],
                       },
              'food':{
                        'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                        'event':[
                                    [['pay',120,60,96000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,504000],1500],
                                    [['buy',120,90,100800],1000],
                                     [['baggage',60,100,5],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                         'event':[
                                  [['pay',120,60,96000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,504000],1500],
                                  [['buy',120,90,100800],1000],
                                   [['baggage',60,100,5],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                           'event':[
                                    [['pay',120,60,96000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,504000],1500],
                                    [['buy',120,90,100800],1000],
                                   [['baggage',60,100,5],1000],
                                        ],
                        },
                         },
    '20':{
            'gold':{
                     'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                     'event':[
                             [['pay',120,60,48000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,252000],1500],
                             [['buy',120,90,50400],1000],
                             [['baggage',60,100,5],1000],
                                   ],
                       },
              'food':{
                        'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                        'event':[
                                    [['pay',120,60,96000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,504000],1500],
                                    [['buy',120,90,100800],1000],
                                     [['baggage',60,100,5],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                         'event':[
                                  [['pay',120,60,96000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,504000],1500],
                                  [['buy',120,90,100800],1000],
                                   [['baggage',60,100,5],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                           'event':[
                                    [['pay',120,60,96000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,504000],1500],
                                    [['buy',120,90,100800],1000],
                                   [['baggage',60,100,5],1000],
                                        ],
                        },
                         },
    '21':{
            'gold':{
                     'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                     'event':[
                             [['pay',120,60,51000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,267750],1500],
                             [['buy',120,100,59500],1000],
                             [['baggage',60,100,5],1000],
                                   ],
                       },
              'food':{
                        'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                        'event':[
                                    [['pay',120,60,102000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,535500],1500],
                                    [['buy',120,100,119000],1000],
                                     [['baggage',60,100,5],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                         'event':[
                                  [['pay',120,60,102000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,535500],1500],
                                  [['buy',120,100,119000],1000],
                                   [['baggage',60,100,5],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                           'event':[
                                    [['pay',120,60,102000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,535500],1500],
                                    [['buy',120,100,119000],1000],
                                   [['baggage',60,100,5],1000],
                                        ],
                        },
                         },
    '22':{
            'gold':{
                     'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                     'event':[
                             [['pay',120,60,51000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,267750],1500],
                             [['buy',120,100,59500],1000],
                             [['baggage',60,100,5],1000],
                                   ],
                       },
              'food':{
                        'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                        'event':[
                                    [['pay',120,60,102000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,535500],1500],
                                    [['buy',120,100,119000],1000],
                                     [['baggage',60,100,5],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                         'event':[
                                  [['pay',120,60,102000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,535500],1500],
                                  [['buy',120,100,119000],1000],
                                   [['baggage',60,100,5],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                           'event':[
                                    [['pay',120,60,102000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,535500],1500],
                                    [['buy',120,100,119000],1000],
                                   [['baggage',60,100,5],1000],
                                        ],
                        },
                         },
    '23':{
            'gold':{
                     'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                     'event':[
                             [['pay',120,60,51000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,267750],1500],
                             [['buy',120,100,59500],1000],
                             [['baggage',60,100,5],1000],
                                   ],
                       },
              'food':{
                        'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                        'event':[
                                    [['pay',120,60,102000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,535500],1500],
                                    [['buy',120,100,119000],1000],
                                     [['baggage',60,100,5],1000],
                                   ],
                            },
                'wood':{
                         'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                         'event':[
                                  [['pay',120,60,102000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,535500],1500],
                                  [['buy',120,100,119000],1000],
                                   [['baggage',60,100,5],1000],
                                      ],
                          },
                 'iron':{
                           'limit':[[50000,0.3],[200000,0.2],[400000,0.1]],
                           'event':[
                                    [['pay',120,60,102000],1500],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,535500],1500],
                                    [['buy',120,100,119000],1000],
                                   [['baggage',60,100,5],1000],
                                        ],
                        },
                         },
    '24':{
            'gold':{
                     'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                     'event':[
                             [['pay',120,60,54000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,283500],1000],
                             [['buy',120,110,69300],1000],
                             [['baggage',60,100,5],1000],
                             [['pay',120,680,765000],1500],
                             [['pay',120,1280,1728000],1500],
                                   ],
                       },
              'food':{
                        'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                        'event':[
                                    [['pay',120,60,108000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,567000],1000],
                                    [['buy',120,110,138600],1000],
                                     [['baggage',60,100,5],1000],
                             [['pay',120,680,1530000],1500],
                             [['pay',120,1280,3456000],1500],
                                   ],
                            },
                'wood':{
                         'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                         'event':[
                                  [['pay',120,60,108000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,567000],1000],
                                  [['buy',120,110,138600],1000],
                                   [['baggage',60,100,5],1000],
                             [['pay',120,680,1530000],1500],
                             [['pay',120,1280,3456000],1500],
                                      ],
                          },
                 'iron':{
                           'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                           'event':[
                                    [['pay',120,60,108000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,567000],1000],
                                    [['buy',120,110,138600],1000],
                                   [['baggage',60,100,5],1000],
                             [['pay',120,680,1530000],1500],
                             [['pay',120,1280,3456000],1500],
                                        ],
                        },
                         },
    '25':{
            'gold':{
                     'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                     'event':[
                             [['pay',120,60,54000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,283500],1000],
                             [['buy',120,110,69300],1000],
                             [['baggage',60,150,6],1000],
                             [['pay',120,680,765000],1500],
                             [['pay',120,1280,1728000],1500],
                                   ],
                       },
              'food':{
                        'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                        'event':[
                                    [['pay',120,60,108000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,567000],1000],
                                    [['buy',120,110,138600],1000],
                                     [['baggage',60,150,6],1000],
                             [['pay',120,680,1530000],1500],
                             [['pay',120,1280,3456000],1500],
                                   ],
                            },
                'wood':{
                         'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                         'event':[
                                  [['pay',120,60,108000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,567000],1000],
                                  [['buy',120,110,138600],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1530000],1500],
                             [['pay',120,1280,3456000],1500],
                                      ],
                          },
                 'iron':{
                           'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                           'event':[
                                    [['pay',120,60,108000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,567000],1000],
                                    [['buy',120,110,138600],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1530000],1500],
                             [['pay',120,1280,3456000],1500],
                                        ],
                        },
                         },
    '26':{
            'gold':{
                     'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                     'event':[
                             [['pay',120,60,54000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,283500],1000],
                             [['buy',120,110,69300],1000],
                             [['baggage',60,150,6],1000],
                             [['pay',120,680,765000],1500],
                             [['pay',120,1280,1728000],1500],
                                   ],
                       },
              'food':{
                        'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                        'event':[
                                    [['pay',120,60,108000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,567000],1000],
                                    [['buy',120,110,138600],1000],
                                     [['baggage',60,150,6],1000],
                             [['pay',120,680,1530000],1500],
                             [['pay',120,1280,3456000],1500],
                                   ],
                            },
                'wood':{
                         'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                         'event':[
                                  [['pay',120,60,108000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,567000],1000],
                                  [['buy',120,110,138600],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1530000],1500],
                             [['pay',120,1280,3456000],1500],
                                      ],
                          },
                 'iron':{
                           'limit':[[100000,0.3],[300000,0.2],[600000,0.1]],
                           'event':[
                                    [['pay',120,60,108000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,567000],1000],
                                    [['buy',120,110,138600],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1530000],1500],
                             [['pay',120,1280,3456000],1500],
                                        ],
                        },
                         },
    '27':{
            'gold':{
                     'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                     'event':[
                             [['pay',120,60,57000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,299250],1000],
                             [['buy',120,120,79800],1000],
                             [['baggage',60,150,6],1000],
                             [['pay',120,680,807500],1500],
                             [['pay',120,1280,1824000],1500],
                                   ],
                       },
              'food':{
                        'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                        'event':[
                                    [['pay',120,60,114000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,598500],1000],
                                    [['buy',120,120,159600],1000],
                                     [['baggage',60,150,6],1000],
                             [['pay',120,680,1615000],1500],
                             [['pay',120,1280,3648000],1500],
                                   ],
                            },
                'wood':{
                         'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                         'event':[
                                  [['pay',120,60,114000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,598500],1000],
                                  [['buy',120,120,159600],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1615000],1500],
                             [['pay',120,1280,3648000],1500],
                                      ],
                          },
                 'iron':{
                           'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                           'event':[
                                    [['pay',120,60,114000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,598500],1000],
                                    [['buy',120,120,159600],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1615000],1500],
                             [['pay',120,1280,3648000],1500],
                                        ],
                        },
                         },
    '28':{
            'gold':{
                     'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                     'event':[
                             [['pay',120,60,57000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,299250],1000],
                             [['buy',120,120,79800],1000],
                             [['baggage',60,150,6],1000],
                             [['pay',120,680,807500],1500],
                             [['pay',120,1280,1824000],1500],
                                   ],
                       },
              'food':{
                        'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                        'event':[
                                    [['pay',120,60,114000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,598500],1000],
                                    [['buy',120,120,159600],1000],
                                     [['baggage',60,150,6],1000],
                             [['pay',120,680,1615000],1500],
                             [['pay',120,1280,3648000],1500],
                                   ],
                            },
                'wood':{
                         'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                         'event':[
                                  [['pay',120,60,114000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,598500],1000],
                                  [['buy',120,120,159600],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1615000],1500],
                             [['pay',120,1280,3648000],1500],
                                      ],
                          },
                 'iron':{
                           'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                           'event':[
                                    [['pay',120,60,114000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,598500],1000],
                                    [['buy',120,120,159600],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1615000],1500],
                             [['pay',120,1280,3648000],1500],
                                        ],
                        },
                         },
    '29':{
            'gold':{
                     'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                     'event':[
                             [['pay',120,60,57000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,299250],1000],
                             [['buy',120,120,79800],1000],
                             [['baggage',60,150,6],1000],
                             [['pay',120,680,807500],1500],
                             [['pay',120,1280,1824000],1500],
                                   ],
                       },
              'food':{
                        'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                        'event':[
                                    [['pay',120,60,114000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,598500],1000],
                                    [['buy',120,120,159600],1000],
                                     [['baggage',60,150,6],1000],
                             [['pay',120,680,1615000],1500],
                             [['pay',120,1280,3648000],1500],
                                   ],
                            },
                'wood':{
                         'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                         'event':[
                                  [['pay',120,60,114000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,598500],1000],
                                  [['buy',120,120,159600],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1615000],1500],
                             [['pay',120,1280,3648000],1500],
                                      ],
                          },
                 'iron':{
                           'limit':[[150000,0.3],[400000,0.2],[800000,0.1]],
                           'event':[
                                    [['pay',120,60,114000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,598500],1000],
                                    [['buy',120,120,159600],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1615000],1500],
                             [['pay',120,1280,3648000],1500],
                                        ],
                        },
                         },
    '30':{
            'gold':{
                     'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                     'event':[
                             [['pay',120,60,60000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,315000],1000],
                             [['buy',120,130,91000],1000],
                             [['baggage',60,150,6],1000],
                             [['pay',120,680,850000],1500],
                             [['pay',120,1280,1920000],1500],
                                   ],
                       },
              'food':{
                        'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                        'event':[
                                    [['pay',120,60,120000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,630000],1000],
                                    [['buy',120,130,182000],1000],
                                     [['baggage',60,150,6],1000],
                             [['pay',120,680,1700000],1500],
                             [['pay',120,1280,3840000],1500],
                                   ],
                            },
                'wood':{
                         'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                         'event':[
                                  [['pay',120,60,120000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,630000],1000],
                                  [['buy',120,130,182000],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1700000],1500],
                             [['pay',120,1280,3840000],1500],
                                      ],
                          },
                 'iron':{
                           'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                           'event':[
                                    [['pay',120,60,120000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,630000],1000],
                                    [['buy',120,130,182000],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1700000],1500],
                             [['pay',120,1280,3840000],1500],
                                        ],
                        },
                         },
    '31':{
            'gold':{
                     'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                     'event':[
                             [['pay',120,60,60000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,315000],1000],
                             [['buy',120,130,91000],1000],
                             [['baggage',60,150,6],1000],
                             [['pay',120,680,850000],1500],
                             [['pay',120,1280,1920000],1500],
                                   ],
                       },
              'food':{
                        'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                        'event':[
                                    [['pay',120,60,120000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,630000],1000],
                                    [['buy',120,130,182000],1000],
                                     [['baggage',60,150,6],1000],
                             [['pay',120,680,1700000],1500],
                             [['pay',120,1280,3840000],1500],
                                   ],
                            },
                'wood':{
                         'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                         'event':[
                                  [['pay',120,60,120000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,630000],1000],
                                  [['buy',120,130,182000],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1700000],1500],
                             [['pay',120,1280,3840000],1500],
                                      ],
                          },
                 'iron':{
                           'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                           'event':[
                                    [['pay',120,60,120000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,630000],1000],
                                    [['buy',120,130,182000],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1700000],1500],
                             [['pay',120,1280,3840000],1500],
                                        ],
                        },
                         },
    '32':{
            'gold':{
                     'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                     'event':[
                             [['pay',120,60,60000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,315000],1000],
                             [['buy',120,130,91000],1000],
                             [['baggage',60,150,6],1000],
                             [['pay',120,680,850000],1500],
                             [['pay',120,1280,1920000],1500],
                                   ],
                       },
              'food':{
                        'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                        'event':[
                                    [['pay',120,60,120000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,630000],1000],
                                    [['buy',120,130,182000],1000],
                                     [['baggage',60,150,6],1000],
                             [['pay',120,680,1700000],1500],
                             [['pay',120,1280,3840000],1500],
                                   ],
                            },
                'wood':{
                         'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                         'event':[
                                  [['pay',120,60,120000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,630000],1000],
                                  [['buy',120,130,182000],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1700000],1500],
                             [['pay',120,1280,3840000],1500],
                                      ],
                          },
                 'iron':{
                           'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                           'event':[
                                    [['pay',120,60,120000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,630000],1000],
                                    [['buy',120,130,182000],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1700000],1500],
                             [['pay',120,1280,3840000],1500],
                                        ],
                        },
                         },
    '33':{
            'gold':{
                     'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                     'event':[
                             [['pay',120,60,63000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,330750],1000],
                             [['buy',120,140,102900],1000],
                             [['baggage',60,150,6],1000],
                             [['pay',120,680,892500],1500],
                             [['pay',120,1280,2016000],1500],
                                   ],
                       },
              'food':{
                        'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                        'event':[
                                    [['pay',120,60,126000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,661500],1000],
                                    [['buy',120,140,205800],1000],
                                     [['baggage',60,150,6],1000],
                             [['pay',120,680,1785000],1500],
                             [['pay',120,1280,4032000],1500],
                                   ],
                            },
                'wood':{
                         'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                         'event':[
                                  [['pay',120,60,126000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,661500],1000],
                                  [['buy',120,140,205800],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1785000],1500],
                             [['pay',120,1280,4032000],1500],
                                      ],
                          },
                 'iron':{
                           'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                           'event':[
                                    [['pay',120,60,126000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,661500],1000],
                                    [['buy',120,140,205800],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1785000],1500],
                             [['pay',120,1280,4032000],1500],
                                        ],
                        },
                         },
    '34':{
            'gold':{
                     'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                     'event':[
                             [['pay',120,60,63000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,330750],1000],
                             [['buy',120,140,102900],1000],
                             [['baggage',60,150,6],1000],
                             [['pay',120,680,892500],1500],
                             [['pay',120,1280,2016000],1500],
                                   ],
                       },
              'food':{
                        'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                        'event':[
                                    [['pay',120,60,126000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,661500],1000],
                                    [['buy',120,140,205800],1000],
                                     [['baggage',60,150,6],1000],
                             [['pay',120,680,1785000],1500],
                             [['pay',120,1280,4032000],1500],
                                   ],
                            },
                'wood':{
                         'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                         'event':[
                                  [['pay',120,60,126000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,661500],1000],
                                  [['buy',120,140,205800],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1785000],1500],
                             [['pay',120,1280,4032000],1500],
                                      ],
                          },
                 'iron':{
                           'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                           'event':[
                                    [['pay',120,60,126000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,661500],1000],
                                    [['buy',120,140,205800],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1785000],1500],
                             [['pay',120,1280,4032000],1500],
                                        ],
                        },
                         },
    '35':{
            'gold':{
                     'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                     'event':[
                             [['pay',120,60,63000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,330750],1000],
                             [['buy',120,140,102900],1000],
                             [['baggage',60,150,6],1000],
                             [['pay',120,680,892500],1500],
                             [['pay',120,1280,2016000],1500],
                                   ],
                       },
              'food':{
                        'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                        'event':[
                                    [['pay',120,60,126000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,661500],1000],
                                    [['buy',120,140,205800],1000],
                                     [['baggage',60,150,6],1000],
                             [['pay',120,680,1785000],1500],
                             [['pay',120,1280,4032000],1500],
                                   ],
                            },
                'wood':{
                         'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                         'event':[
                                  [['pay',120,60,126000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,661500],1000],
                                  [['buy',120,140,205800],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1785000],1500],
                             [['pay',120,1280,4032000],1500],
                                      ],
                          },
                 'iron':{
                           'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                           'event':[
                                    [['pay',120,60,126000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,661500],1000],
                                    [['buy',120,140,205800],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1785000],1500],
                             [['pay',120,1280,4032000],1500],
                                        ],
                        },
                         },
    '36':{
            'gold':{
                     'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                     'event':[
                             [['pay',120,60,66000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                             [['pay',120,300,346500],1000],
                             [['buy',120,150,115500],1000],
                             [['baggage',60,150,6],1000],
                             [['pay',120,680,935000],1500],
                             [['pay',120,1280,2112000],1500],
                                   ],
                       },
              'food':{
                        'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                        'event':[
                                    [['pay',120,60,132000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,693000],1000],
                                    [['buy',120,150,231000],1000],
                                     [['baggage',60,150,6],1000],
                             [['pay',120,680,1870000],1500],
                             [['pay',120,1280,4224000],1500],
                                   ],
                            },
                'wood':{
                         'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                         'event':[
                                  [['pay',120,60,132000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                  [['pay',120,300,693000],1000],
                                  [['buy',120,150,231000],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1870000],1500],
                             [['pay',120,1280,4224000],1500],
                                      ],
                          },
                 'iron':{
                           'limit':[[200000,0.3],[500000,0.2],[1000000,0.1]],
                           'event':[
                                    [['pay',120,60,132000],1000],#'条件'：【【类型，持续时间，元宝数，资源数(辎重免费次数)】，权重】
                                    [['pay',120,300,693000],1000],
                                    [['buy',120,150,231000],1000],
                                   [['baggage',60,150,6],1000],
                             [['pay',120,680,1870000],1500],
                             [['pay',120,1280,4224000],1500],
                                        ],
                        },
                         },




	},

##########################购买政务########################
	'buy_gtask':{
		'name':'buy_gtask_name',
		'info':'buy_gtask_info',
		'hero':'hero761',
		'time':180,	#分钟
		'pay_money':30,	#需要充值的money
		'mulit':[1,3],	#居功至伟(reward_mulit)，3次
		'reward_key':'item021',	#黄金沙漏
		'para':[5,270,10],	#a为玩家完成第5次政务触发，if(玩家已完成政务数 < b && 全服最高政务数-玩家已完成政务数>c && 今日未触发过本事件)

	},

##########################在线奖励########################
'act_online':{

	'pay_money':6,    #充值额度，大于等于此数量后，玩家可以领取充值奖励；		

	'online': [
		{
			'cd':10,    #分钟
			'reward':{'gold':10000,'item002':2},
			'pay_reward':{'item037':10,'item032':2},
		},
		{
			'cd':30,    #分钟
			'reward':{'gold':10000,'food':10000},
			'pay_reward':{'item037':10,'item032':3},
		},
		{
			'cd':60,    #分钟
			'reward':{'gold':10000,'wood':10000},
			'pay_reward':{'item037':10,'item032':5},
		},
		{
			'cd':120,    #分钟
			'reward':{'gold':10000,'iron':10000},
			'pay_reward':{'item037':10,'item032':10},
		},
	],
},

######################加官进爵####################
	'act_officeup':{
		'pay_money':88,			#需要玩家累积充值的金额
		'officelv_reward':{
			1:{'coin':100},     #office等级,奖励
			2:{'coin':600}, 
			3:{'coin':1500}, 
			4:{'coin':2800}, 
			5:{'coin':3888}, 
		},
	},
########################每日许愿############################
	'act_wish':{
		'wish_rewchoo_num':4,   #许愿奖励数量
		'wish_initrew':['coin',100],    #许愿初始奖励
		'wish_multiple':2,    #充值后奖励翻倍
		'hero_icon':'hero724',   #使用英雄图片
		'wish_rewpond':[
			['gold',5000],['gold',5000],['food',10000],['food',10000],['wood',10000],['wood',10000],['iron',10000],['iron',10000],['item032',5],['item037',5],['item021',1],['item024',1],['item027',2],['item057',10],
		],   ####许愿奖池
		'wish_days':7,    #许愿获得额外奖励的累计天数
		'wish_daysrew':[
                        {'item071':4,'item070':4},        #首次7天领取
                        {'item071':3,'item070':3,'item073':3,'item069':3,'item074':3},  
		],#按照数组顺序轮换领取，最后一组循环
	},

##########################精彩活动###############
	#官邸升级，活动时间内累计充值获得的黄金
	#单笔充值，活动时间内记录单笔充值的rmb
	#累计充值，活动时间内累计充值获得的黄金

	'levelup':{
'reward':{








9:[6,{'face':['spec_cattle02'],}],      ###########[6,{'item104':1,'gold':100000,'iron':200000,'wood':200000,'food':200000,}],
10:[30,{'item055':100,'gold':100000,'iron':200000,'wood':200000,'food':200000,}],
11:[30,{'item034':20000,'gold':150000,'iron':300000,'wood':300000,'food':300000,}],
12:[30,{'item059':100,'gold':150000,'iron':300000,'wood':300000,'food':300000,}],
13:[30,{'item057':100,'gold':200000,'iron':400000,'wood':400000,'food':400000,}],
14:[30,{'item036':10,'item079':200,'item080':200,'item081':200,'item082':200,}],
15:[30,{'item075':100,'gold':200000,'iron':400000,'wood':400000,'food':400000,}],
16:[30,{'item036':10,'item107':200,'item113':200,'item124':200,'item130':400,}],
17:[30,{'item070':30,'gold':200000,'iron':400000,'wood':400000,'food':400000,}],
18:[30,{'item071':30,'gold':200000,'iron':400000,'wood':400000,'food':400000,}],
19:[30,{'item059':200,'gold':200000,'iron':400000,'wood':400000,'food':400000,}],
20:[30,{'item057':200,'gold':200000,'iron':400000,'wood':400000,'food':400000,}],
21:[30,{'item061':50,'gold':200000,'iron':400000,'wood':400000,'food':400000,}],
22:[30,{'item036':20,'item406':200,'item407':200,'item408':200,'item409':200,}],
23:[30,{'item036':20,'item107':500,'item113':500,'item124':500,'item130':1000,}],
24:[30,{'item036':20,'item212':100,'item218':100,'item205':100,'item224':100,}],
25:[68,{'item036':20,'item191':10,'item189':50,'item030':20,'item187':50,}],
26:[68,{'item036':30,'item236':100,'item237':100,'item234':100,'item243':100,}],
27:[68,{'item036':30,'item189':100,'item603':5,'item271':100,'item238':100,}],
28:[128,{'item036':60,'item1030':20,'item292':200,'item291':200,'item280':200,}],
29:[128,{'item036':60,'item191':50,'item206':200,'item211':200,'item276':200,}],
30:[328,{'item036':120,'item1031':20,'item246':200,'item245':200,'item016':10,}],




},


		'time':10800,		#持续时间，秒
		'character':'hero704',	#立绘
		'title':'502003',		#标题
		'tips':'502004',		#提示文本
		'tab_name':'502009',		#提示文本
	},



	'levelup_gift':{
		'reward':{





31:[ 'gd0603',{'item016':5,'item1208':40000,'item1189':1,'item295':200,'item296':200,}],
32:[ 'gd0604',{'item016':5,'item1209':40000,'item1190':1,'item293':200,'item294':200,}],
33:[ 'gd0605',{'item016':5,'item1210':40000,'item1191':1,'item279':200,'item278':200,}],
34:[ 'gd0606',{'item1253':1,'item2003':10,'item1171':1,'item962':200,'item963':200,}],
35:[ 'gd0607',{'item1253':1,'item2003':20,'item1171':1,'item964':200,'item965':200,}],
36:[ 'gd0608',{'item1253':1,'item2003':30,'item1171':1,'item960':200,'item961':200,}],




		
		},


		'time':21600,		#持续时间，秒
		'character':'hero704',	#立绘
		'title':'502085',		#标题
		'tips':'502086',		#提示文本
	},



############微信小程序分享#####################
	'share_reward':[[0,{'coin':50}],[30,{'coin':50}],[60,{'coin':50}]], #[[cd,reward],],

'week_card':{		#周卡活动
		'pay':30,		#需要单笔充值的金额，单位：RMB
		#'pay_reward':300,		#立即获得300黄金，单位：黄金
		'cycle':7,		#有奖励的周期，单位：天/偏移时间点
		'cycle_reward':400,		#周期奖励，单位：黄金
		#购买周卡后，立即获得300黄金，购买之后每天可以获得周期奖励，周期奖励是从第二天开始算的，每过一个偏移时间，可领取一次，一共领取7次。
		#第七次的奖励被领取的那天，弹出新一期的周卡奖励
		#玩家没在奖励周期时显示购买入口，玩家在奖励周期内不显示购买入口
		#玩家在奖励周期内时，每天第一次登陆时，发给玩家本日的黄金奖励，登录时推送固定界面
		#若玩家在奖励周期内，没有每天都登陆，未登陆的天数不做计数，即玩家可以完整领取所有7次的奖励
	},




	'member_card':{		#永久卡活动
		'pay':288,		#需要单笔充值的金额，单位：RMB
		'show_pay':6,		#充6元可见
		'day_reward':{'coin':200,'gold':50000,'food':100000,'wood':100000,'iron':100000,'item070':1,'item071':1, 'item021':5},		#每日赠送的奖励
		'limit_count':10,		#奖励最多累积的天数
		'help_info':'member_03', # 帮助
		#购买（或激活）永久卡后，立即获得Min（开服天数,'limit_count'）*'day_reward'奖励
		#之后每一天登录的角色皆可领取一份'day_reward'
		#玩家购买之后立即激活当前服永久卡，在其他区需要手动激活永久卡

	},




####################################紫气东来###############


	'big_shot':{		#紫气东来（通过充值触发）
		'switch':1,			#功能总开关，0表示关闭，1表示开启
		'info':'big_shot_12',			#文字说明
		'show_bgm':'bg_bigshot_ggt5.png',		#上方大图
		'show_switch':0,		#控制是否显示公示概率面板的开关，1显示，0隐藏
		'open_days':{'1_999':'reward'},	#开启日期，包含起始和结束天数(一旦天数产生任何变化，则玩家身上刮奖位置数据都会重置)（账号已经获得该日期的情况下，修改日期会导致所有充值报错）
		'open_ini':3,		#每期活动，默认显示前三天
		'act_condition':[		#单笔充值金额区间 和 该区间的触发几率
			[[68,128],0.01],		#单笔充值rmb在6-68之间，触发几率25%
			[[328,648],0.3],
			[[998,998],0.5],
			[[1998,0],1],		#单笔充值大于998，触发几率100%
		],
		'act_time':60,		#每次触发持续X分钟（后端记结束时间，结束时间内不在重复触发）
		'deduct_bird':1,			#每抽一次扣一分
		'buy_bird':['gd0501',1],			#买分，这个分数是一直记在玩家身上的，永久累计，不会被删除
		'reward':[		#奖励库，抽取时完全随机（去重），每期活动玩家自身记录单独的排序，活动结束后删除排序数据
{'item192':150},
{'item084':10},
{'item1008':200},
{'item970':2000},
{'star5101':1},
{'item516':600},
{'star5301':1},
{'item970':2000},
{'coin':20000},
{'star6101':1},
{'awaken':['hero782']},
{'item794':1000},
{'awaken':['hero788']},
{'star7101':1},
{'coin':20000},
{'item016':40},
{'star6301':1},
{'item514':600},
{'item794':1000},
{'item515':600},
{'star7301':1},
{'item016':40},
{'item970':2000},
{'item1253':20},
{'item1253':20},
{'item1253':20},
{'item970':2000},
{'item1008':200},
{'item084':10},
{'item192':150},




		],
		'reward_show':[	
			[0,0,1,2,3,0,0],
			[0,4,5,6,7,8,0],
			[9,10,11,12,13,14,15],
			[16,17,18,19,20,21,22],
			[0,23,24,25,26,27,0],
			[0,0,28,29,30,0,0],
		],
		#前端显示权重，总奖励数/100的百分比，
	},





################折扣商店####################
'act_sale_shop':{    #开服后第N天开启，当天可见（同一天只会有一个）
  'begin_time':[12,0],  #开启后持续过当天
  'open_days':{4:'goods1',5:'goods1',6:'goods1',10:'goods2',11:'goods2',12:'goods2',16:'goods3',17:'goods3',18:'goods3',22:'goods4',23:'goods4',24:'goods4',28:'goods5',29:'goods5',30:'goods5',34:'goods6',35:'goods6',36:'goods6',40:'goods7',41:'goods7',42:'goods7',46:'goods8',47:'goods8',48:'goods8',52:'goods9',53:'goods9',54:'goods9',58:'goods1',59:'goods1',60:'goods1',64:'goods7',65:'goods7',66:'goods7',70:'goods8',71:'goods8',72:'goods8',76:'goods9',77:'goods9',78:'goods9',82:'goods1',83:'goods1',84:'goods1',88:'goods7',89:'goods7',90:'goods7',94:'goods8',95:'goods8',96:'goods8',100:'goods9',101:'goods9',102:'goods9',106:'goods1',107:'goods1',108:'goods1',112:'goods7',113:'goods7',114:'goods7',118:'goods8',119:'goods8',120:'goods8',124:'goods9',125:'goods9',126:'goods9',130:'goods1',131:'goods1',132:'goods1',136:'goods7',137:'goods7',138:'goods7',142:'goods8',143:'goods8',144:'goods8',148:'goods9',149:'goods9',150:'goods9',154:'goods1',155:'goods1',156:'goods1',160:'goods7',161:'goods7',162:'goods7',166:'goods8',167:'goods8',168:'goods8',172:'goods9',173:'goods9',174:'goods9',178:'goods1',179:'goods1',180:'goods1',184:'goods7',185:'goods7',186:'goods7',190:'goods8',191:'goods8',192:'goods8',196:'goods9',197:'goods9',198:'goods9',202:'goods1',203:'goods1',204:'goods1',208:'goods7',209:'goods7',210:'goods7',214:'goods8',215:'goods8',216:'goods8',220:'goods9',221:'goods9',222:'goods9',226:'goods1',227:'goods1',228:'goods1',232:'goods7',233:'goods7',234:'goods7',238:'goods8',239:'goods8',240:'goods8',244:'goods9',245:'goods9',246:'goods9',250:'goods1',251:'goods1',252:'goods1',256:'goods7',257:'goods7',258:'goods7',262:'goods8',263:'goods8',264:'goods8',268:'goods9',269:'goods9',270:'goods9',274:'goods1',275:'goods1',276:'goods1',280:'goods7',281:'goods7',282:'goods7',286:'goods8',287:'goods8',288:'goods8',292:'goods9',293:'goods9',294:'goods9',298:'goods1',299:'goods1',300:'goods1',304:'goods7',305:'goods7',306:'goods7',310:'goods8',311:'goods8',312:'goods8',316:'goods9',317:'goods9',318:'goods9',322:'goods1',323:'goods1',324:'goods1',328:'goods7',329:'goods7',330:'goods7',334:'goods8',335:'goods8',336:'goods8',340:'goods9',341:'goods9',342:'goods9',346:'goods1',347:'goods1',348:'goods1',352:'goods7',353:'goods7',354:'goods7',358:'goods8',359:'goods8',360:'goods8',364:'goods9',365:'goods9',366:'goods9',},  #开服第n天有效
  'goods1':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[20,80,6],
      'limit':20,
    },
    '2':{
      'reward':['item292',10],
      'price':[400,2000,100],
      'limit':50,
    },
    '3':{
      'reward':['item211',10],
      'price':[400,2000,100],
      'limit':50,
    },
    '4':{
      'reward':['item291',10],
      'price':[400,2000,100],
      'limit':50,
    },
    '5':{
      'reward':['item206',10],
      'price':[400,2000,100],
      'limit':50,
    },
    '6':{
      'reward':['item294',5],
      'price':[400,2000,300],
      'limit':20,
    },
    '7':{
      'reward':['item296',5],
      'price':[400,2000,300],
      'limit':20,
    },
    '8':{
      'reward':['item293',5],
      'price':[400,2000,300],
      'limit':20,
    },
  },
  'goods2':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[20,80,6],
      'limit':20,
    },
    '2':{
      'reward':['item279',5],
      'price':[400,2000,100],
      'limit':20,
    },
    '3':{
      'reward':['item970',5],
      'price':[500,2000,300],
      'limit':20,
    },
    '4':{
      'reward':['star5001',1],
      'price':[2500,10000,500],
      'limit':1,
    },
    '5':{
      'reward':['star5101',1],
      'price':[2500,10000,500],
      'limit':1,
    },
    '6':{
      'reward':['star5201',1],
      'price':[2500,10000,500],
      'limit':1,
    },
    '7':{
      'reward':['star5301',1],
      'price':[2500,10000,500],
      'limit':1,
    },
    '8':{
      'reward':['item517',5],
      'price':[300,1500,1000],
      'limit':120,
    },
  },
  'goods3':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[20,80,6],
      'limit':20,
    },
    '2':{
      'reward':['item278',5],
      'price':[400,2000,100],
      'limit':20,
    },
    '3':{
      'reward':['item046',1],
      'price':[800,1500,300],
      'limit':5,
    },
    '4':{
      'reward':['star6001',1],
      'price':[2500,10000,500],
      'limit':1,
    },
    '5':{
      'reward':['star6101',1],
      'price':[2500,10000,500],
      'limit':1,
    },
    '6':{
      'reward':['star6201',1],
      'price':[2500,10000,500],
      'limit':1,
    },
    '7':{
      'reward':['star6301',1],
      'price':[2500,10000,500],
      'limit':1,
    },
    '8':{
      'reward':['item518',5],
      'price':[300,1500,1000],
      'limit':120,
    },
  },
  'goods4':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[20,80,6],
      'limit':20,
    },
    '2':{
      'reward':['item280',5],
      'price':[400,2000,100],
      'limit':20,
    },
    '3':{
      'reward':['item971',5],
      'price':[500,2000,300],
      'limit':20,
    },
    '4':{
      'reward':['star7001',1],
      'price':[2500,10000,500],
      'limit':1,
    },
    '5':{
      'reward':['star7101',1],
      'price':[2500,10000,500],
      'limit':1,
    },
    '6':{
      'reward':['star7201',1],
      'price':[2500,10000,500],
      'limit':1,
    },
    '7':{
      'reward':['star7301',1],
      'price':[2500,10000,500],
      'limit':1,
    },
    '8':{
      'reward':['item519',5],
      'price':[300,1500,1000],
      'limit':120,
    },
  },
  'goods5':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[20,80,6],
      'limit':20,
    },
    '2':{
      'reward':['item275',5],
      'price':[400,2000,100],
      'limit':20,
    },
    '3':{
      'reward':['item962',5],
      'price':[400,2000,300],
      'limit':20,
    },
    '4':{
      'reward':['item963',5],
      'price':[400,2000,300],
      'limit':20,
    },
    '5':{
      'reward':['item964',5],
      'price':[400,2000,300],
      'limit':20,
    },
    '6':{
      'reward':['item965',5],
      'price':[400,2000,300],
      'limit':20,
    },
    '7':{
      'reward':['item960',5],
      'price':[500,2000,300],
      'limit':20,
    },
    '8':{
      'reward':['item520',5],
      'price':[300,1500,1000],
      'limit':120,
    },
  },
  'goods6':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[20,80,6],
      'limit':20,
    },
    '2':{
      'reward':['item279',5],
      'price':[400,2000,100],
      'limit':20,
    },
    '3':{
      'reward':['item966',5],
      'price':[400,2000,300],
      'limit':20,
    },
    '4':{
      'reward':['item967',5],
      'price':[400,2000,300],
      'limit':20,
    },
    '5':{
      'reward':['item968',5],
      'price':[400,2000,300],
      'limit':20,
    },
    '6':{
      'reward':['item969',5],
      'price':[400,2000,300],
      'limit':20,
    },
    '7':{
      'reward':['item961',5],
      'price':[500,2000,300],
      'limit':20,
    },
    '8':{
      'reward':['item521',5],
      'price':[300,1500,1000],
      'limit':120,
    },
  },
  'goods7':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[20,80,6],
      'limit':20,
    },
    '2':{
      'reward':['item278',5],
      'price':[400,2000,100],
      'limit':20,
    },
    '3':{
      'reward':['item250',5],
      'price':[400,2000,100],
      'limit':20,
    },
    '4':{
      'reward':['item962',5],
      'price':[400,2000,300],
      'limit':20,
    },
    '5':{
      'reward':['item963',5],
      'price':[400,2000,300],
      'limit':20,
    },
    '6':{
      'reward':['item253',5],
      'price':[400,2000,100],
      'limit':20,
    },
    '7':{
      'reward':['item247',5],
      'price':[450,2000,300],
      'limit':20,
    },
    '8':{
      'reward':['item970',5],
      'price':[500,2000,300],
      'limit':20,
    },
  },
  'goods8':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[20,80,6],
      'limit':20,
    },
    '2':{
      'reward':['item280',5],
      'price':[400,2000,100],
      'limit':20,
    },
    '3':{
      'reward':['item251',5],
      'price':[400,2000,100],
      'limit':20,
    },
    '4':{
      'reward':['item964',5],
      'price':[400,2000,300],
      'limit':20,
    },
    '5':{
      'reward':['item965',5],
      'price':[400,2000,300],
      'limit':20,
    },
    '6':{
      'reward':['item254',5],
      'price':[400,2000,100],
      'limit':20,
    },
    '7':{
      'reward':['item248',5],
      'price':[450,2000,300],
      'limit':20,
    },
    '8':{
      'reward':['item971',5],
      'price':[500,2000,300],
      'limit':20,
    },
  },
  'goods9':{  #出售价格，模拟原价，需要的充值金额（rmb）
    '1':{
      'reward':['item036',1],
      'price':[20,80,6],
      'limit':20,
    },
    '2':{
      'reward':['item252',5],
      'price':[400,2000,100],
      'limit':20,
    },
    '3':{
      'reward':['item247',5],
      'price':[450,2000,300],
      'limit':20,
    },
    '4':{
      'reward':['item248',5],
      'price':[450,2000,300],
      'limit':20,
    },
    '5':{
      'reward':['item960',5],
      'price':[500,2000,300],
      'limit':20,
    },
    '6':{
      'reward':['item961',5],
      'price':[500,2000,300],
      'limit':20,
    },
    '7':{
      'reward':['item970',5],
      'price':[500,2000,300],
      'limit':20,
    },
    '8':{
      'reward':['item971',5],
      'price':[500,2000,300],
      'limit':20,
    },
  },
},









##########限时免单######################################

'limit_free':{    #开服后第N天开启，当天可见（同一天只会有一个）
  'begin_time':[[12,0,14,0],[18,0,22,0]],  #开启后持续过当天
  'open_days':{1:'goods1',2:'goods1',3:'goods1',7:'goods2',8:'goods2',9:'goods2',13:'goods3',14:'goods3',15:'goods3',19:'goods4',20:'goods4',21:'goods4',25:'goods5',26:'goods5',27:'goods5',31:'goods6',32:'goods6',33:'goods6',37:'goods7',38:'goods7',39:'goods7',43:'goods8',44:'goods8',45:'goods8',49:'goods9',50:'goods9',51:'goods9',55:'goods10',56:'goods10',57:'goods10',61:'goods2',62:'goods2',63:'goods2',67:'goods3',68:'goods3',69:'goods3',73:'goods4',74:'goods4',75:'goods4',79:'goods5',80:'goods5',81:'goods5',85:'goods6',86:'goods6',87:'goods6',91:'goods7',92:'goods7',93:'goods7',97:'goods8',98:'goods8',99:'goods8',103:'goods9',104:'goods9',105:'goods9',109:'goods10',110:'goods10',111:'goods10',115:'goods2',116:'goods2',117:'goods2',121:'goods3',122:'goods3',123:'goods3',127:'goods4',128:'goods4',129:'goods4',133:'goods5',134:'goods5',135:'goods5',139:'goods6',140:'goods6',141:'goods6',145:'goods7',146:'goods7',147:'goods7',151:'goods8',152:'goods8',153:'goods8',157:'goods9',158:'goods9',159:'goods9',163:'goods10',164:'goods10',165:'goods10',169:'goods2',170:'goods2',171:'goods2',175:'goods3',176:'goods3',177:'goods3',181:'goods4',182:'goods4',183:'goods4',187:'goods5',188:'goods5',189:'goods5',193:'goods6',194:'goods6',195:'goods6',199:'goods7',200:'goods7',201:'goods7',205:'goods8',206:'goods8',207:'goods8',211:'goods9',212:'goods9',213:'goods9',217:'goods10',218:'goods10',219:'goods10',223:'goods2',224:'goods2',225:'goods2',229:'goods3',230:'goods3',231:'goods3',235:'goods4',236:'goods4',237:'goods4',241:'goods5',242:'goods5',243:'goods5',247:'goods6',248:'goods6',249:'goods6',253:'goods7',254:'goods7',255:'goods7',259:'goods8',260:'goods8',261:'goods8',265:'goods9',266:'goods9',267:'goods9',271:'goods10',272:'goods10',273:'goods10',277:'goods2',278:'goods2',279:'goods2',283:'goods3',284:'goods3',285:'goods3',289:'goods4',290:'goods4',291:'goods4',295:'goods5',296:'goods5',297:'goods5',301:'goods6',302:'goods6',303:'goods6',307:'goods7',308:'goods7',309:'goods7',313:'goods8',314:'goods8',315:'goods8',319:'goods9',320:'goods9',321:'goods9',325:'goods10',326:'goods10',327:'goods10',331:'goods2',332:'goods2',333:'goods2',337:'goods3',338:'goods3',339:'goods3',343:'goods4',344:'goods4',345:'goods4',349:'goods5',350:'goods5',351:'goods5',355:'goods6',356:'goods6',357:'goods6',361:'goods7',362:'goods7',363:'goods7',367:'goods8',368:'goods8',369:'goods8',373:'goods9',374:'goods9',375:'goods9',379:'goods10',380:'goods10',381:'goods10',},  #开服第n天有效
  'buy_limit':[100,200,300,300,300],  #数组长度为可够买次数，每次购买all_limit次
  'all_limit':30,   #可购买道具的总次数上限，也是购买一次增加的次数
  'free_show':'free_show_speak',
  'goods1':{ 
    'free_chance':0.4, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item036':3},
        'price':100,
        'limit':10,
      },
      '2':{
        'reward':{'item206':5},
        'price':300,
        'limit':30,
      },
      '3':{
        'reward':{'item211':5},
        'price':300,
       'limit':30,
      },
      '4':{
        'reward':{'item291':5},
        'price':300,
        'limit':30,
      },
      '5':{
        'reward':{'item292':5},
        'price':300,
        'limit':30,
      },
      '6':{
        'reward':{'item297':5},
        'price':300,
        'limit':30,
      },
    },
  },
  'goods2':{ 
    'free_chance':0.4, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item036':3},
        'price':100,
        'limit':10,
      },
      '2':{
        'reward':{'item205':5},
        'price':200,
        'limit':30,
      },
      '3':{
        'reward':{'item218':5},
        'price':200,
       'limit':30,
      },
      '4':{
        'reward':{'item106':1},
        'price':500,
        'limit':5,
      },
      '5':{
        'reward':{'item1245':2},
        'price':600,
        'limit':5,
      },
      '6':{
        'reward':{'item1249':1},
        'price':600,
        'limit':5,
      },
    },
  },
  'goods3':{ 
    'free_chance':0.4, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item036':3},
        'price':100,
        'limit':10,
      },
      '2':{
        'reward':{'item212':5},
        'price':200,
        'limit':30,
      },
      '3':{
        'reward':{'item224':5},
        'price':200,
       'limit':30,
      },
      '4':{
        'reward':{'item106':1},
        'price':500,
        'limit':5,
      },
      '5':{
        'reward':{'item004':2},
        'price':700,
        'limit':5,
      },
      '6':{
        'reward':{'item1182':1},
        'price':750,
        'limit':5,
      },
    },
  },
  'goods4':{ 
    'free_chance':0.4, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item036':3},
        'price':100,
        'limit':10,
      },
      '2':{
        'reward':{'item206':5},
        'price':300,
        'limit':30,
      },
      '3':{
        'reward':{'item211':5},
        'price':300,
       'limit':30,
      },
      '4':{
        'reward':{'item274':10},
        'price':500,
        'limit':10,
      },
      '5':{
        'reward':{'item1245':2},
        'price':600,
        'limit':5,
      },
      '6':{
        'reward':{'item1249':1},
        'price':600,
        'limit':5,
      },
    },
  },
  'goods5':{ 
    'free_chance':0.4, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item036':3},
        'price':100,
        'limit':10,
      },
      '2':{
        'reward':{'item291':5},
        'price':300,
        'limit':30,
      },
      '3':{
        'reward':{'item292':5},
        'price':300,
       'limit':30,
      },
      '4':{
        'reward':{'item275':10},
        'price':500,
        'limit':10,
      },
      '5':{
        'reward':{'item004':2},
        'price':700,
        'limit':5,
      },
      '6':{
        'reward':{'item1182':1},
        'price':750,
        'limit':5,
      },
    },
  },
  'goods6':{ 
    'free_chance':0.3, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item036':3},
        'price':100,
        'limit':20,
      },
      '2':{
        'reward':{'item205':5},
        'price':200,
        'limit':30,
      },
      '3':{
        'reward':{'item206':5},
        'price':300,
       'limit':30,
      },
      '4':{
        'reward':{'item276':10},
        'price':500,
        'limit':10,
      },
      '5':{
        'reward':{'item1245':2},
        'price':600,
        'limit':5,
      },
      '6':{
        'reward':{'item1249':1},
        'price':600,
        'limit':5,
      },
    },
  },
  'goods7':{ 
    'free_chance':0.3, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item036':3},
        'price':100,
        'limit':10,
      },
      '2':{
        'reward':{'item212':5},
        'price':200,
        'limit':30,
      },
      '3':{
        'reward':{'item211':5},
        'price':300,
       'limit':30,
      },
      '4':{
        'reward':{'item277':10},
        'price':500,
        'limit':10,
      },
      '5':{
        'reward':{'item004':2},
        'price':700,
        'limit':5,
      },
      '6':{
        'reward':{'item1182':1},
        'price':750,
        'limit':5,
      },
    },
  },
  'goods8':{ 
    'free_chance':0.3, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800002},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item036':3},
        'price':100,
        'limit':20,
      },
      '2':{
        'reward':{'item218':5},
        'price':200,
        'limit':30,
      },
      '3':{
        'reward':{'item291':5},
        'price':300,
       'limit':30,
      },
      '4':{
        'reward':{'item278':10},
        'price':500,
        'limit':10,
      },
      '5':{
        'reward':{'item1245':2},
        'price':600,
        'limit':5,
      },
      '6':{
        'reward':{'item1249':1},
        'price':600,
        'limit':5,
      },
    },
  },
  'goods9':{ 
    'free_chance':0.3, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item036':3},
        'price':100,
        'limit':20,
      },
      '2':{
        'reward':{'item224':5},
        'price':200,
        'limit':30,
      },
      '3':{
        'reward':{'item292':5},
        'price':300,
       'limit':30,
      },
      '4':{
        'reward':{'item279':10},
        'price':500,
        'limit':10,
      },
      '5':{
        'reward':{'item004':2},
        'price':700,
        'limit':5,
      },
      '6':{
        'reward':{'item1182':1},
        'price':750,
        'limit':5,
      },
    },
  },
  'goods10':{ 
    'free_chance':0.3, #免单几率
    'count_reward':{30:{'gold':200000},80:{'gold':800000},130:{'item036':20},},
    'goods':{
      '1':{
        'reward':{'item036':3},
        'price':100,
        'limit':20,
      },
      '2':{
        'reward':{'item271':10},
        'price':500,
        'limit':10,
      },
      '3':{
        'reward':{'item280':10},
        'price':500,
       'limit':10,
      },
      '4':{
        'reward':{'item106':1},
        'price':500,
        'limit':5,
      },
      '5':{
        'reward':{'item1245':2},
        'price':600,
        'limit':5,
      },
      '6':{
        'reward':{'item1249':1},
        'price':600,
        'limit':5,
      },
    },
  },
},









#############################################精彩活动##########################################
	#单笔充值，活动时间内记录单笔充值的rmb
	#累计充值，活动时间内累计充值获得的黄金

	'pay_ploy':{
		'msg_name': '502001',
		'msg_info': '502002',
		#'XX':{ o开头代表单笔充值，a开头代表累计充值，字母后面的数字向下延续，尽量不重复用10数内的id
			#'date':[[2019,3,27],[2019,10,1]],		#以年月日作为起始/结束日期,（eg：9月1号5点开始，10月1号5点结束）
			#'days':[a,b],		#a&b=开服第几天，a=开启天数，b=结束天数 （eg：偏移值5点，1号6点开服, 'days':[1, 2]   活动入口会在3号5点关闭）
			#'type':'once',		#活动类型：once单笔充值，addup累计充值，today每日累充，choice充值自选
			#'reward':{		#奖励库，根据活动类型写法不同
				#1:{'item003':1,'item004':2,'item005':10,'item020':25,'item073':10},		#单笔充值写法；金额：奖励
				#1000:{'item003':1,'item004':2,'item005':10,'item020':25,'item073':10},	#累计充值写法；金额：奖励
			#},
			#'character':'hero703',	#面板中的立绘
			#'title':'502005',		#标题，入口以及面板内
			#'tips':'502006',		#面板内的提示文本
		#},


       'a1':{         #饕餮
                        
                         'days':[1,10],'type':'addup',
                         'show':[ 'equip129','equip130','equip131','equip132','equip133'],
                         'name':'502090',
                         'info':'502091',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'equip':['equip129'],'item513':60,'item406':200,'item036':20,'item021':20,},
                                      200:{'equip':['equip130'],'item513':60,'item409':400,'item036':30,'item021':30,},
                                      500:{'equip':['equip131'],'item513':120,'item407':600,'item036':40,'item021':40,},
                                      1000:{'equip':['equip132'],'item513':120,'item408':800,'item036':50,'item021':50,},
                                      2000:{'equip':['equip133'],'item513':240,'item410':1000,'item036':60,'item021':60,},
                                      4000:{'item013':100,'item513':360,'item408':1200,'item036':70,'item021':70,},
                                      6000:{'item013':200,'item513':480,'item407':1400,'item036':80,'item021':80,},
                                      8000:{'item013':300,'item513':600,'item409':1600,'item036':100,'item021':100,},
                                      11000:{'item014':50,'item513':840,'item406':1800,'item036':120,'item021':120,},
                                      15000:{'item014':100,'item513':1080,'item084':5,'item036':160,'item015':100,},
                                      20000:{'item014':150,'item513':1440,'item084':10,'item036':200,'item016':100,},
                                      25000:{'item013':320,'item014':160,'item084':15,'item015':120,'item016':100,},
                                      30000:{'item013':320,'item014':160,'item084':15,'item015':120,'item016':100,},
                                     },
                            'character':'hero794',
                            'reward_show':{     '25000':15000, '30000':20000,},
                            'title':'502007',
                            'tips':'502008',
                            },


















     'a2':{         #虞姬
                        
                         'days':[11,20],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item795':50,'item971':100,'item248':60,'item293':60,'item295':60,},
                                      200:{'item795':50,'item971':100,'item279':80,'item206':80,'item291':80,},
                                      500:{'item795':50,'item971':100,'item248':100,'item293':100,'item295':100,},
                                      1000:{'item795':50,'item971':100,'item279':120,'item206':120,'item291':120,},
                                      2000:{'item795':100,'item971':200,'item248':140,'item293':140,'item295':140,},
                                      3000:{'item795':100,'item971':200,'item279':160,'item206':160,'item291':160,},
                                      4500:{'item795':150,'item971':400,'item248':180,'item293':180,'item295':180,},
                                      6000:{'item795':150,'item971':400,'item279':200,'item206':200,'item291':200,},
                                      8000:{'item795':200,'item971':600,'item248':220,'item293':220,'item295':220,},
                                      10000:{'item795':200,'item971':800,'item279':240,'item206':240,'item291':240,},
                                      13000:{'item795':200,'item971':1000,'item248':260,'item293':260,'item295':260,},
                                      16000:{'item795':300,'item971':1200,'item279':280,'item206':280,'item291':280,},
                                      20000:{'item795':400,'item971':1500,'item248':300,'item293':300,'item295':300,},
                                     },
                            'character':'hero795',
                            'reward_show':{},
                            'title':'502007',
                            'tips':'502008',
                            },
















     'a3':{         #韩信
                        
                         'days':[21,30],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item798':50,'item960':100,'item239':60,'item293':60,'item295':60,},
                                      200:{'item798':50,'item960':100,'item253':80,'item206':80,'item291':80,},
                                      500:{'item798':50,'item960':100,'item239':100,'item293':100,'item295':100,},
                                      1000:{'item798':50,'item960':100,'item253':120,'item206':120,'item291':120,},
                                      2000:{'item798':100,'item960':200,'item239':140,'item293':140,'item295':140,},
                                      3000:{'item798':100,'item960':200,'item253':160,'item206':160,'item291':160,},
                                      4500:{'item798':150,'item960':400,'item239':180,'item293':180,'item295':180,},
                                      6000:{'item798':150,'item960':400,'item253':200,'item206':200,'item291':200,},
                                      8000:{'item798':200,'item960':600,'item239':220,'item293':220,'item295':220,},
                                      10000:{'item798':200,'item960':800,'item253':240,'item206':240,'item291':240,},
                                      13000:{'item798':200,'item960':1000,'item239':260,'item293':260,'item295':260,},
                                      16000:{'item798':300,'item960':1200,'item253':280,'item206':280,'item291':280,},
                                      20000:{'item798':400,'item960':1500,'item239':300,'item293':300,'item295':300,},
                                     },
                            'character':'hero798',
                            'title':'502007',
                            'tips':'502008',
                            },



















     'a4':{         #张良
                        
                         'days':[31,40],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item797':50,'item961':100,'item278':60,'item294':60,'item296':60,},
                                      200:{'item797':50,'item961':100,'item247':80,'item211':80,'item292':80,},
                                      500:{'item797':50,'item961':100,'item278':100,'item294':100,'item296':100,},
                                      1000:{'item797':50,'item961':100,'item247':120,'item211':120,'item292':120,},
                                      2000:{'item797':100,'item961':200,'item278':140,'item294':140,'item296':140,},
                                      3000:{'item797':100,'item961':200,'item247':160,'item211':160,'item292':160,},
                                      4500:{'item797':150,'item961':400,'item278':180,'item294':180,'item296':180,},
                                      6000:{'item797':150,'item961':400,'item247':200,'item211':200,'item292':200,},
                                      8000:{'item797':200,'item961':600,'item278':220,'item294':220,'item296':220,},
                                      10000:{'item797':200,'item961':800,'item247':240,'item211':240,'item292':240,},
                                      13000:{'item797':200,'item961':1000,'item278':260,'item294':260,'item296':260,},
                                      16000:{'item797':300,'item961':1200,'item247':280,'item211':280,'item292':280,},
                                      20000:{'item797':400,'item961':1500,'item278':300,'item294':300,'item296':300,},
                                     },
                            'character':'hero797',
                            'title':'502007',
                            'tips':'502008',
                            },






















     'a5 ':{         #转生材料
                        
                         'days':[41,50],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item2003':40,'item407':200,'item406':200,'item036':20,'item021':20,},
                                      200:{'item2003':80,'item408':400,'item409':400,'item036':30,'item021':30,},
                                      500:{'item2003':100,'item2007':10,'item410':1000,'item036':40,'item021':40,},
                                      1000:{'item2003':120,'item2007':20,'item2004':3,'item2005':3,'item2006':3,},
                                      2000:{'item2003':140,'item2007':30,'item2004':6,'item2005':6,'item2006':6,},
                                      3000:{'item2003':160,'item2007':40,'item2004':9,'item2005':9,'item2006':9,},
                                      4500:{'item2003':180,'item2007':50,'item2004':12,'item2005':12,'item2006':12,},
                                      6000:{'item2003':200,'item2007':60,'item2004':15,'item2005':15,'item2006':15,},
                                      8000:{'item2003':220,'item2007':70,'item2004':18,'item2005':18,'item2006':18,},
                                      10000:{'item2003':240,'item2007':80,'item2004':21,'item2005':21,'item2006':21,},
                                      13000:{'item2003':260,'item2007':90,'item2004':24,'item2005':24,'item2006':24,},
                                      16000:{'item2003':280,'item2007':100,'item2004':27,'item2005':27,'item2006':27,},
                                      20000:{'item2003':360,'item2007':120,'item2004':36,'item2005':36,'item2006':36,},
                                     },
                            'character':'hero784',
                            'title':'502007',
                            'tips':'502008',
                            },






















     'a6':{         #仙音霸君
                        
                         'days':[51,60],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item971':100,'item970':100,'item239':100,'item245':100,'item036':5,},
                                      200:{'item971':100,'item970':100,'item239':100,'item245':100,'item036':10,},
                                      500:{'item971':100,'item970':100,'item239':100,'item245':100,'item036':15,},
                                      1000:{'item971':100,'item970':100,'item239':100,'item245':100,'item036':20,},
                                      2000:{'item971':200,'item970':200,'item239':200,'item245':200,'item036':40,},
                                      3000:{'item971':200,'item970':200,'item239':200,'item245':200,'item035':1,},
                                      4500:{'item971':400,'item970':400,'item239':400,'item245':400,'item035':2,},
                                      6000:{'item971':400,'item970':400,'item239':400,'item245':400,'item035':3,},
                                      8000:{'item971':600,'item970':600,'item239':600,'item245':600,'item035':4,},
                                      10000:{'item971':800,'item970':800,'item239':800,'item245':800,'item036':80,},
                                      13000:{'item971':1000,'item970':1000,'item239':1000,'item245':1000,'item036':90,},
                                      16000:{'item971':1200,'item970':1200,'item239':1200,'item245':1200,'item036':100,},
                                      20000:{'item244':1500,'item246':1500,'item247':1500,'item248':1500,'item036':120,},
                                     },
                            'character':'hero784',
                            'title':'502007',
                            'tips':'502008',
                            },






















     'a7 ':{         #转生材料
                        
                         'days':[61,70],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item2003':40,'item407':200,'item406':200,'item036':20,'item021':20,},
                                      200:{'item2003':80,'item408':400,'item409':400,'item036':30,'item021':30,},
                                      500:{'item2003':100,'item2007':10,'item410':1000,'item036':40,'item021':40,},
                                      1000:{'item2003':120,'item2007':20,'item2004':3,'item2005':3,'item2006':3,},
                                      2000:{'item2003':140,'item2007':30,'item2004':6,'item2005':6,'item2006':6,},
                                      3000:{'item2003':160,'item2007':40,'item2004':9,'item2005':9,'item2006':9,},
                                      4500:{'item2003':180,'item2007':50,'item2004':12,'item2005':12,'item2006':12,},
                                      6000:{'item2003':200,'item2007':60,'item2004':15,'item2005':15,'item2006':15,},
                                      8000:{'item2003':220,'item2007':70,'item2004':18,'item2005':18,'item2006':18,},
                                      10000:{'item2003':240,'item2007':80,'item2004':21,'item2005':21,'item2006':21,},
                                      13000:{'item2003':260,'item2007':90,'item2004':24,'item2005':24,'item2006':24,},
                                      16000:{'item2003':280,'item2007':100,'item2004':27,'item2005':27,'item2006':27,},
                                      20000:{'item2003':360,'item2007':120,'item2004':36,'item2005':36,'item2006':36,},
                                     },
                            'character':'hero784',
                            'title':'502007',
                            'tips':'502008',
                            },






















     'a8':{         #辅助技能
                        
                         'days':[71,80],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item278':100,'item279':100,'item036':5,'item275':100,'item277':100,},
                                      200:{'item278':100,'item279':100,'item036':10,'item275':100,'item277':100,},
                                      500:{'item278':100,'item279':100,'item036':15,'item275':100,'item277':100,},
                                      1000:{'item278':100,'item279':100,'item036':20,'item275':100,'item277':100,},
                                      2000:{'item278':200,'item279':200,'item036':40,'item275':200,'item277':200,},
                                      3000:{'item278':200,'item279':200,'item035':1,'item275':200,'item277':200,},
                                      4500:{'item278':400,'item279':400,'item035':2,'item275':400,'item277':400,},
                                      6000:{'item278':400,'item279':400,'item035':3,'item275':400,'item277':400,},
                                      8000:{'item278':600,'item279':600,'item035':4,'item275':600,'item277':600,},
                                      10000:{'item278':800,'item279':800,'item036':80,'item275':800,'item277':800,},
                                      13000:{'item278':1000,'item279':1000,'item036':90,'item275':1000,'item277':1000,},
                                      16000:{'item278':1200,'item279':1200,'item036':100,'item273':1200,'item272':1200,},
                                      20000:{'item278':1500,'item279':1500,'item036':120,'item273':1500,'item272':1500,},
                                     },
                            'character':'hero784',
                            'title':'502007',
                            'tips':'502008',
                            },





















     'a9':{         #仙音霸君
                        
                         'days':[81,90],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item971':100,'item970':100,'item239':100,'item245':100,'item036':5,},
                                      200:{'item971':100,'item970':100,'item239':100,'item245':100,'item036':10,},
                                      500:{'item971':100,'item970':100,'item239':100,'item245':100,'item036':15,},
                                      1000:{'item971':100,'item970':100,'item239':100,'item245':100,'item036':20,},
                                      2000:{'item971':200,'item970':200,'item239':200,'item245':200,'item036':40,},
                                      3000:{'item971':200,'item970':200,'item239':200,'item245':200,'item035':1,},
                                      4500:{'item971':400,'item970':400,'item239':400,'item245':400,'item035':2,},
                                      6000:{'item971':400,'item970':400,'item239':400,'item245':400,'item035':3,},
                                      8000:{'item971':600,'item970':600,'item239':600,'item245':600,'item035':4,},
                                      10000:{'item971':800,'item970':800,'item239':800,'item245':800,'item036':80,},
                                      13000:{'item971':1000,'item970':1000,'item239':1000,'item245':1000,'item036':90,},
                                      16000:{'item971':1200,'item970':1200,'item239':1200,'item245':1200,'item036':100,},
                                      20000:{'item244':1500,'item246':1500,'item247':1500,'item248':1500,'item036':120,},
                                     },
                            'character':'hero784',
                            'title':'502007',
                            'tips':'502008',
                            },






















     'a10':{         #转生材料
                        
                         'days':[91,100],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item2003':40,'item407':200,'item406':200,'item036':20,'item021':20,},
                                      200:{'item2003':80,'item408':400,'item409':400,'item036':30,'item021':30,},
                                      500:{'item2003':100,'item2007':10,'item410':1000,'item036':40,'item021':40,},
                                      1000:{'item2003':120,'item2007':20,'item2004':3,'item2005':3,'item2006':3,},
                                      2000:{'item2003':140,'item2007':30,'item2004':6,'item2005':6,'item2006':6,},
                                      3000:{'item2003':160,'item2007':40,'item2004':9,'item2005':9,'item2006':9,},
                                      4500:{'item2003':180,'item2007':50,'item2004':12,'item2005':12,'item2006':12,},
                                      6000:{'item2003':200,'item2007':60,'item2004':15,'item2005':15,'item2006':15,},
                                      8000:{'item2003':220,'item2007':70,'item2004':18,'item2005':18,'item2006':18,},
                                      10000:{'item2003':240,'item2007':80,'item2004':21,'item2005':21,'item2006':21,},
                                      13000:{'item2003':260,'item2007':90,'item2004':24,'item2005':24,'item2006':24,},
                                      16000:{'item2003':280,'item2007':100,'item2004':27,'item2005':27,'item2006':27,},
                                      20000:{'item2003':360,'item2007':120,'item2004':36,'item2005':36,'item2006':36,},
                                     },
                            'character':'hero784',
                            'title':'502007',
                            'tips':'502008',
                            },




















     'a11 ':{         #兵种技能
                        
                         'days':[101,110],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item1249':1,'item1250':1,'item1251':1,'item1252':1,'item036':5,},
                                      200:{'item1249':2,'item1250':2,'item1251':2,'item1252':2,'item036':10,},
                                      500:{'item1249':4,'item1250':4,'item1251':4,'item1252':4,'item036':15,},
                                      1000:{'item1249':4,'item1250':4,'item1251':4,'item1252':4,'item036':20,},
                                      2000:{'item1253':2,'item1256':2,'item1255':2,'item1254':2,'item036':30,},
                                      3000:{'item1253':2,'item1256':2,'item1255':2,'item1254':2,'item036':40,},
                                      4500:{'item1253':4,'item1256':4,'item1255':4,'item1254':4,'item036':50,},
                                      6000:{'item1253':4,'item1256':4,'item1255':4,'item1254':4,'item036':60,},
                                      8000:{'item1253':6,'item1256':6,'item1255':6,'item1254':6,'item084':3,},
                                      10000:{'item1253':6,'item1256':6,'item1255':6,'item1254':6,'item084':3,},
                                      13000:{'item1253':8,'item1256':8,'item1255':8,'item1254':8,'item084':3,},
                                      16000:{'item1253':8,'item1256':8,'item1255':8,'item1254':8,'item084':3,},
                                      20000:{'item1253':10,'item1256':10,'item1255':10,'item1254':10,'item084':3,},
                                     },
                            'character':'hero784',
                            'title':'502007',
                            'tips':'502008',
                            },





















     'a12':{         #仙音霸君
                        
                         'days':[111,120],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item971':100,'item970':100,'item239':100,'item245':100,'item036':5,},
                                      200:{'item971':100,'item970':100,'item239':100,'item245':100,'item036':10,},
                                      500:{'item971':100,'item970':100,'item239':100,'item245':100,'item036':15,},
                                      1000:{'item971':100,'item970':100,'item239':100,'item245':100,'item036':20,},
                                      2000:{'item971':200,'item970':200,'item239':200,'item245':200,'item036':40,},
                                      3000:{'item971':200,'item970':200,'item239':200,'item245':200,'item035':1,},
                                      4500:{'item971':400,'item970':400,'item239':400,'item245':400,'item035':2,},
                                      6000:{'item971':400,'item970':400,'item239':400,'item245':400,'item035':3,},
                                      8000:{'item971':600,'item970':600,'item239':600,'item245':600,'item035':4,},
                                      10000:{'item971':800,'item970':800,'item239':800,'item245':800,'item036':80,},
                                      13000:{'item971':1000,'item970':1000,'item239':1000,'item245':1000,'item036':90,},
                                      16000:{'item971':1200,'item970':1200,'item239':1200,'item245':1200,'item036':100,},
                                      20000:{'item244':1500,'item246':1500,'item247':1500,'item248':1500,'item036':120,},
                                     },
                            'character':'hero784',
                            'title':'502007',
                            'tips':'502008',
                            },




















     'a13':{         #转生材料
                        
                         'days':[121,130],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item2003':40,'item407':200,'item406':200,'item036':20,'item021':20,},
                                      200:{'item2003':80,'item408':400,'item409':400,'item036':30,'item021':30,},
                                      500:{'item2003':100,'item2007':10,'item410':1000,'item036':40,'item021':40,},
                                      1000:{'item2003':120,'item2007':20,'item2004':3,'item2005':3,'item2006':3,},
                                      2000:{'item2003':140,'item2007':30,'item2004':6,'item2005':6,'item2006':6,},
                                      3000:{'item2003':160,'item2007':40,'item2004':9,'item2005':9,'item2006':9,},
                                      4500:{'item2003':180,'item2007':50,'item2004':12,'item2005':12,'item2006':12,},
                                      6000:{'item2003':200,'item2007':60,'item2004':15,'item2005':15,'item2006':15,},
                                      8000:{'item2003':220,'item2007':70,'item2004':18,'item2005':18,'item2006':18,},
                                      10000:{'item2003':240,'item2007':80,'item2004':21,'item2005':21,'item2006':21,},
                                      13000:{'item2003':260,'item2007':90,'item2004':24,'item2005':24,'item2006':24,},
                                      16000:{'item2003':280,'item2007':100,'item2004':27,'item2005':27,'item2006':27,},
                                      20000:{'item2003':360,'item2007':120,'item2004':36,'item2005':36,'item2006':36,},
                                     },
                            'character':'hero784',
                            'title':'502007',
                            'tips':'502008',
                            },


     'a14':{         #圣甲障毒
                        
                         'days':[131,140],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item278':100,'item279':100,'item036':5,'item960':100,'item961':100,},
                                      200:{'item278':100,'item279':100,'item036':10,'item960':100,'item961':100,},
                                      500:{'item278':100,'item279':100,'item036':15,'item960':100,'item961':100,},
                                      1000:{'item278':100,'item279':100,'item036':20,'item960':100,'item961':100,},
                                      2000:{'item278':200,'item279':200,'item036':40,'item960':200,'item961':200,},
                                      3000:{'item278':200,'item279':200,'item035':1,'item960':200,'item961':200,},
                                      4500:{'item278':400,'item279':400,'item035':2,'item960':400,'item961':400,},
                                      6000:{'item278':400,'item279':400,'item035':3,'item960':400,'item961':400,},
                                      8000:{'item278':600,'item279':600,'item035':4,'item960':600,'item961':600,},
                                      10000:{'item278':800,'item279':800,'item036':80,'item960':800,'item961':800,},
                                      13000:{'item278':1000,'item279':1000,'item036':90,'item960':1000,'item961':1000,},
                                      16000:{'item278':1200,'item279':1200,'item036':100,'item960':1200,'item961':1200,},
                                      20000:{'item278':1500,'item279':1500,'item036':120,'item960':1500,'item961':1500,},
                                     },
                            'character':'hero784',
                            'title':'502007',
                            'tips':'502008',
                            },



















     'a15':{         #仙音霸君
                        
                         'days':[141,150],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item971':100,'item970':100,'item239':100,'item245':100,'item036':5,},
                                      200:{'item971':100,'item970':100,'item239':100,'item245':100,'item036':10,},
                                      500:{'item971':100,'item970':100,'item239':100,'item245':100,'item036':15,},
                                      1000:{'item971':100,'item970':100,'item239':100,'item245':100,'item036':20,},
                                      2000:{'item971':200,'item970':200,'item239':200,'item245':200,'item036':40,},
                                      3000:{'item971':200,'item970':200,'item239':200,'item245':200,'item035':1,},
                                      4500:{'item971':400,'item970':400,'item239':400,'item245':400,'item035':2,},
                                      6000:{'item971':400,'item970':400,'item239':400,'item245':400,'item035':3,},
                                      8000:{'item971':600,'item970':600,'item239':600,'item245':600,'item035':4,},
                                      10000:{'item971':800,'item970':800,'item239':800,'item245':800,'item036':80,},
                                      13000:{'item971':1000,'item970':1000,'item239':1000,'item245':1000,'item036':90,},
                                      16000:{'item971':1200,'item970':1200,'item239':1200,'item245':1200,'item036':100,},
                                      20000:{'item244':1500,'item246':1500,'item247':1500,'item248':1500,'item036':120,},
                                     },
                            'character':'hero784',
                            'title':'502007',
                            'tips':'502008',
                            },





















     'a16':{         #辅助技能
                        
                         'days':[151,160],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item2003':40,'item407':200,'item406':200,'item036':20,'item021':20,},
                                      200:{'item2003':80,'item408':400,'item409':400,'item036':30,'item021':30,},
                                      500:{'item2003':100,'item2007':10,'item410':1000,'item036':40,'item021':40,},
                                      1000:{'item2003':120,'item2007':20,'item2004':3,'item2005':3,'item2006':3,},
                                      2000:{'item2003':140,'item2007':30,'item2004':6,'item2005':6,'item2006':6,},
                                      3000:{'item2003':160,'item2007':40,'item2004':9,'item2005':9,'item2006':9,},
                                      4500:{'item2003':180,'item2007':50,'item2004':12,'item2005':12,'item2006':12,},
                                      6000:{'item2003':200,'item2007':60,'item2004':15,'item2005':15,'item2006':15,},
                                      8000:{'item2003':220,'item2007':70,'item2004':18,'item2005':18,'item2006':18,},
                                      10000:{'item2003':240,'item2007':80,'item2004':21,'item2005':21,'item2006':21,},
                                      13000:{'item2003':260,'item2007':90,'item2004':24,'item2005':24,'item2006':24,},
                                      16000:{'item2003':280,'item2007':100,'item2004':27,'item2005':27,'item2006':27,},
                                      20000:{'item2003':360,'item2007':120,'item2004':36,'item2005':36,'item2006':36,},
                                     },
                            'character':'hero784',
                            'title':'502007',
                            'tips':'502008',
                            },




















     'a17':{         #魂玉
                        
                         'days':[161,170],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item1249':1,'item1250':1,'item1251':1,'item1252':1,'item036':5,},
                                      200:{'item1249':2,'item1250':2,'item1251':2,'item1252':2,'item036':10,},
                                      500:{'item1249':4,'item1250':4,'item1251':4,'item1252':4,'item036':15,},
                                      1000:{'item1249':4,'item1250':4,'item1251':4,'item1252':4,'item036':20,},
                                      2000:{'item1253':2,'item1256':2,'item1255':2,'item1254':2,'item036':30,},
                                      3000:{'item1253':2,'item1256':2,'item1255':2,'item1254':2,'item036':40,},
                                      4500:{'item1253':4,'item1256':4,'item1255':4,'item1254':4,'item036':50,},
                                      6000:{'item1253':4,'item1256':4,'item1255':4,'item1254':4,'item036':60,},
                                      8000:{'item1253':6,'item1256':6,'item1255':6,'item1254':6,'item084':3,},
                                      10000:{'item1253':6,'item1256':6,'item1255':6,'item1254':6,'item084':3,},
                                      13000:{'item1253':8,'item1256':8,'item1255':8,'item1254':8,'item084':3,},
                                      16000:{'item1253':8,'item1256':8,'item1255':8,'item1254':8,'item084':3,},
                                      20000:{'item1253':10,'item1256':10,'item1255':10,'item1254':10,'item084':3,},
                                     },
                            'character':'hero784',
                            'title':'502007',
                            'tips':'502008',
                            },



















     'a18':{         #金色战计
                        
                         'days':[171,180],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item971':100,'item970':100,'item239':100,'item245':100,'item036':5,},
                                      200:{'item971':100,'item970':100,'item239':100,'item245':100,'item036':10,},
                                      500:{'item971':100,'item970':100,'item239':100,'item245':100,'item036':15,},
                                      1000:{'item971':100,'item970':100,'item239':100,'item245':100,'item036':20,},
                                      2000:{'item971':200,'item970':200,'item239':200,'item245':200,'item036':40,},
                                      3000:{'item971':200,'item970':200,'item239':200,'item245':200,'item035':1,},
                                      4500:{'item971':400,'item970':400,'item239':400,'item245':400,'item035':2,},
                                      6000:{'item971':400,'item970':400,'item239':400,'item245':400,'item035':3,},
                                      8000:{'item971':600,'item970':600,'item239':600,'item245':600,'item035':4,},
                                      10000:{'item971':800,'item970':800,'item239':800,'item245':800,'item036':80,},
                                      13000:{'item971':1000,'item970':1000,'item239':1000,'item245':1000,'item036':90,},
                                      16000:{'item971':1200,'item970':1200,'item239':1200,'item245':1200,'item036':100,},
                                      20000:{'item244':1500,'item246':1500,'item247':1500,'item248':1500,'item036':120,},
                                     },
                            'character':'hero784',
                            'title':'502007',
                            'tips':'502008',
                            },



















     'a19':{         #兵种技能
                        
                         'days':[181,190],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item2003':40,'item407':200,'item406':200,'item036':20,'item021':20,},
                                      200:{'item2003':80,'item408':400,'item409':400,'item036':30,'item021':30,},
                                      500:{'item2003':100,'item2007':10,'item410':1000,'item036':40,'item021':40,},
                                      1000:{'item2003':120,'item2007':20,'item2004':3,'item2005':3,'item2006':3,},
                                      2000:{'item2003':140,'item2007':30,'item2004':6,'item2005':6,'item2006':6,},
                                      3000:{'item2003':160,'item2007':40,'item2004':9,'item2005':9,'item2006':9,},
                                      4500:{'item2003':180,'item2007':50,'item2004':12,'item2005':12,'item2006':12,},
                                      6000:{'item2003':200,'item2007':60,'item2004':15,'item2005':15,'item2006':15,},
                                      8000:{'item2003':220,'item2007':70,'item2004':18,'item2005':18,'item2006':18,},
                                      10000:{'item2003':240,'item2007':80,'item2004':21,'item2005':21,'item2006':21,},
                                      13000:{'item2003':260,'item2007':90,'item2004':24,'item2005':24,'item2006':24,},
                                      16000:{'item2003':280,'item2007':100,'item2004':27,'item2005':27,'item2006':27,},
                                      20000:{'item2003':360,'item2007':120,'item2004':36,'item2005':36,'item2006':36,},
                                     },
                            'character':'hero784',
                            'title':'502007',
                            'tips':'502008',
                            },





















     'a20':{         #辅助技能
                        
                         'days':[191,200],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item278':100,'item279':100,'item036':5,'item960':100,'item961':100,},
                                      200:{'item278':100,'item279':100,'item036':10,'item960':100,'item961':100,},
                                      500:{'item278':100,'item279':100,'item036':15,'item960':100,'item961':100,},
                                      1000:{'item278':100,'item279':100,'item036':20,'item960':100,'item961':100,},
                                      2000:{'item278':200,'item279':200,'item036':40,'item960':200,'item961':200,},
                                      3000:{'item278':200,'item279':200,'item035':1,'item960':200,'item961':200,},
                                      4500:{'item278':400,'item279':400,'item035':2,'item960':400,'item961':400,},
                                      6000:{'item278':400,'item279':400,'item035':3,'item960':400,'item961':400,},
                                      8000:{'item278':600,'item279':600,'item035':4,'item960':600,'item961':600,},
                                      10000:{'item278':800,'item279':800,'item036':80,'item960':800,'item961':800,},
                                      13000:{'item278':1000,'item279':1000,'item036':90,'item960':1000,'item961':1000,},
                                      16000:{'item278':1200,'item279':1200,'item036':100,'item960':1200,'item961':1200,},
                                      20000:{'item278':1500,'item279':1500,'item036':120,'item960':1500,'item961':1500,},
                                     },
                            'character':'hero784',
                            'title':'502007',
                            'tips':'502008',
                            },




















     'a21':{         #魂玉
                        
                         'days':[201,210],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item971':100,'item970':100,'item239':100,'item245':100,'item036':5,},
                                      200:{'item971':100,'item970':100,'item239':100,'item245':100,'item036':10,},
                                      500:{'item971':100,'item970':100,'item239':100,'item245':100,'item036':15,},
                                      1000:{'item971':100,'item970':100,'item239':100,'item245':100,'item036':20,},
                                      2000:{'item971':200,'item970':200,'item239':200,'item245':200,'item036':40,},
                                      3000:{'item971':200,'item970':200,'item239':200,'item245':200,'item035':1,},
                                      4500:{'item971':400,'item970':400,'item239':400,'item245':400,'item035':2,},
                                      6000:{'item971':400,'item970':400,'item239':400,'item245':400,'item035':3,},
                                      8000:{'item971':600,'item970':600,'item239':600,'item245':600,'item035':4,},
                                      10000:{'item971':800,'item970':800,'item239':800,'item245':800,'item036':80,},
                                      13000:{'item971':1000,'item970':1000,'item239':1000,'item245':1000,'item036':90,},
                                      16000:{'item971':1200,'item970':1200,'item239':1200,'item245':1200,'item036':100,},
                                      20000:{'item244':1500,'item246':1500,'item247':1500,'item248':1500,'item036':120,},
                                     },
                            'character':'hero784',
                            'title':'502007',
                            'tips':'502008',
                            },


     'a22':{         #转生材料
                        
                         'days':[211,220],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item2003':40,'item407':200,'item406':200,'item036':20,'item021':20,},
                                      200:{'item2003':80,'item408':400,'item409':400,'item036':30,'item021':30,},
                                      500:{'item2003':100,'item2007':10,'item410':1000,'item036':40,'item021':40,},
                                      1000:{'item2003':120,'item2007':20,'item2004':3,'item2005':3,'item2006':3,},
                                      2000:{'item2003':140,'item2007':30,'item2004':6,'item2005':6,'item2006':6,},
                                      3000:{'item2003':160,'item2007':40,'item2004':9,'item2005':9,'item2006':9,},
                                      4500:{'item2003':180,'item2007':50,'item2004':12,'item2005':12,'item2006':12,},
                                      6000:{'item2003':200,'item2007':60,'item2004':15,'item2005':15,'item2006':15,},
                                      8000:{'item2003':220,'item2007':70,'item2004':18,'item2005':18,'item2006':18,},
                                      10000:{'item2003':240,'item2007':80,'item2004':21,'item2005':21,'item2006':21,},
                                      13000:{'item2003':260,'item2007':90,'item2004':24,'item2005':24,'item2006':24,},
                                      16000:{'item2003':280,'item2007':100,'item2004':27,'item2005':27,'item2006':27,},
                                      20000:{'item2003':360,'item2007':120,'item2004':36,'item2005':36,'item2006':36,},
                                     },
                            'character':'hero784',
                            'title':'502007',
                            'tips':'502008',
                            },




















     'a23':{         #金色战计
                        
                         'days':[221,230],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item1249':1,'item1250':1,'item1251':1,'item1252':1,'item036':5,},
                                      200:{'item1249':2,'item1250':2,'item1251':2,'item1252':2,'item036':10,},
                                      500:{'item1249':4,'item1250':4,'item1251':4,'item1252':4,'item036':15,},
                                      1000:{'item1249':4,'item1250':4,'item1251':4,'item1252':4,'item036':20,},
                                      2000:{'item1253':2,'item1256':2,'item1255':2,'item1254':2,'item036':30,},
                                      3000:{'item1253':2,'item1256':2,'item1255':2,'item1254':2,'item036':40,},
                                      4500:{'item1253':4,'item1256':4,'item1255':4,'item1254':4,'item036':50,},
                                      6000:{'item1253':4,'item1256':4,'item1255':4,'item1254':4,'item036':60,},
                                      8000:{'item1253':6,'item1256':6,'item1255':6,'item1254':6,'item084':3,},
                                      10000:{'item1253':6,'item1256':6,'item1255':6,'item1254':6,'item084':3,},
                                      13000:{'item1253':8,'item1256':8,'item1255':8,'item1254':8,'item084':3,},
                                      16000:{'item1253':8,'item1256':8,'item1255':8,'item1254':8,'item084':3,},
                                      20000:{'item1253':10,'item1256':10,'item1255':10,'item1254':10,'item084':3,},
                                     },
                            'character':'hero784',
                            'title':'502007',
                            'tips':'502008',
                            },



















     'a24':{         #仙音霸君
                        
                         'days':[231,240],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item971':100,'item970':100,'item239':100,'item245':100,'item036':5,},
                                      200:{'item971':100,'item970':100,'item239':100,'item245':100,'item036':10,},
                                      500:{'item971':100,'item970':100,'item239':100,'item245':100,'item036':15,},
                                      1000:{'item971':100,'item970':100,'item239':100,'item245':100,'item036':20,},
                                      2000:{'item971':200,'item970':200,'item239':200,'item245':200,'item036':40,},
                                      3000:{'item971':200,'item970':200,'item239':200,'item245':200,'item035':1,},
                                      4500:{'item971':400,'item970':400,'item239':400,'item245':400,'item035':2,},
                                      6000:{'item971':400,'item970':400,'item239':400,'item245':400,'item035':3,},
                                      8000:{'item971':600,'item970':600,'item239':600,'item245':600,'item035':4,},
                                      10000:{'item971':800,'item970':800,'item239':800,'item245':800,'item036':80,},
                                      13000:{'item971':1000,'item970':1000,'item239':1000,'item245':1000,'item036':90,},
                                      16000:{'item971':1200,'item970':1200,'item239':1200,'item245':1200,'item036':100,},
                                      20000:{'item244':1500,'item246':1500,'item247':1500,'item248':1500,'item036':120,},
                                     },
                            'character':'hero784',
                            'title':'502007',
                            'tips':'502008',
                            },



















     'a25':{         #转生材料
                        
                         'days':[241,250],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item2003':40,'item407':200,'item406':200,'item036':20,'item021':20,},
                                      200:{'item2003':80,'item408':400,'item409':400,'item036':30,'item021':30,},
                                      500:{'item2003':100,'item2007':10,'item410':1000,'item036':40,'item021':40,},
                                      1000:{'item2003':120,'item2007':20,'item2004':3,'item2005':3,'item2006':3,},
                                      2000:{'item2003':140,'item2007':30,'item2004':6,'item2005':6,'item2006':6,},
                                      3000:{'item2003':160,'item2007':40,'item2004':9,'item2005':9,'item2006':9,},
                                      4500:{'item2003':180,'item2007':50,'item2004':12,'item2005':12,'item2006':12,},
                                      6000:{'item2003':200,'item2007':60,'item2004':15,'item2005':15,'item2006':15,},
                                      8000:{'item2003':220,'item2007':70,'item2004':18,'item2005':18,'item2006':18,},
                                      10000:{'item2003':240,'item2007':80,'item2004':21,'item2005':21,'item2006':21,},
                                      13000:{'item2003':260,'item2007':90,'item2004':24,'item2005':24,'item2006':24,},
                                      16000:{'item2003':280,'item2007':100,'item2004':27,'item2005':27,'item2006':27,},
                                      20000:{'item2003':360,'item2007':120,'item2004':36,'item2005':36,'item2006':36,},
                                     },
                            'character':'hero784',
                            'title':'502007',
                            'tips':'502008',
                            },





















     'a26':{         #辅助技能
                        
                         'days':[251,260],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item278':100,'item279':100,'item036':5,'item960':100,'item961':100,},
                                      200:{'item278':100,'item279':100,'item036':10,'item960':100,'item961':100,},
                                      500:{'item278':100,'item279':100,'item036':15,'item960':100,'item961':100,},
                                      1000:{'item278':100,'item279':100,'item036':20,'item960':100,'item961':100,},
                                      2000:{'item278':200,'item279':200,'item036':40,'item960':200,'item961':200,},
                                      3000:{'item278':200,'item279':200,'item035':1,'item960':200,'item961':200,},
                                      4500:{'item278':400,'item279':400,'item035':2,'item960':400,'item961':400,},
                                      6000:{'item278':400,'item279':400,'item035':3,'item960':400,'item961':400,},
                                      8000:{'item278':600,'item279':600,'item035':4,'item960':600,'item961':600,},
                                      10000:{'item278':800,'item279':800,'item036':80,'item960':800,'item961':800,},
                                      13000:{'item278':1000,'item279':1000,'item036':90,'item960':1000,'item961':1000,},
                                      16000:{'item278':1200,'item279':1200,'item036':100,'item960':1200,'item961':1200,},
                                      20000:{'item278':1500,'item279':1500,'item036':120,'item960':1500,'item961':1500,},
                                     },
                            'character':'hero784',
                            'title':'502007',
                            'tips':'502008',
                            },




















     'a27':{         #仙音
                        
                         'days':[261,270],'type':'addup',
                         'reward':{
                                      50:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item071':3,},
                                      100:{'item971':100,'item970':100,'item239':100,'item245':100,'item036':5,},
                                      200:{'item971':100,'item970':100,'item239':100,'item245':100,'item036':10,},
                                      500:{'item971':100,'item970':100,'item239':100,'item245':100,'item036':15,},
                                      1000:{'item971':100,'item970':100,'item239':100,'item245':100,'item036':20,},
                                      2000:{'item971':200,'item970':200,'item239':200,'item245':200,'item036':40,},
                                      3000:{'item971':200,'item970':200,'item239':200,'item245':200,'item035':1,},
                                      4500:{'item971':400,'item970':400,'item239':400,'item245':400,'item035':2,},
                                      6000:{'item971':400,'item970':400,'item239':400,'item245':400,'item035':3,},
                                      8000:{'item971':600,'item970':600,'item239':600,'item245':600,'item035':4,},
                                      10000:{'item971':800,'item970':800,'item239':800,'item245':800,'item036':80,},
                                      13000:{'item971':1000,'item970':1000,'item239':1000,'item245':1000,'item036':90,},
                                      16000:{'item971':1200,'item970':1200,'item239':1200,'item245':1200,'item036':100,},
                                      20000:{'item244':1500,'item246':1500,'item247':1500,'item248':1500,'item036':120,},
                                     },
                            'character':'hero784',
                            'title':'502007',
                            'tips':'502008',
                            },










     'o1':{      #逆刃戮尘
                        
                         'days':[1,2],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item962':20,'item963':20,'item294':20,'item293':20,'coin':680,},
                                      128:{'item962':50,'item963':50,'item294':50,'item293':50,'coin':1280,},
                                      328:{'item962':120,'item963':120,'item294':120,'item293':120,'coin':3280,},
                                      648:{'item962':300,'item963':300,'item294':300,'item293':300,'coin':6480,},
                                      998:{'item962':450,'item963':450,'item294':450,'item293':450,'coin':10000,},
                                      1998:{'item962':900,'item963':900,'item294':900,'item293':900,'coin':20000,},
                                     },
                            'character':'hero761',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o2':{      #焱雨星陨
                        
                         'days':[3,4],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item964':20,'item965':20,'item295':20,'item296':20,'coin':680,},
                                      128:{'item964':50,'item965':50,'item295':50,'item296':50,'coin':1280,},
                                      328:{'item964':120,'item965':120,'item295':120,'item296':120,'coin':3280,},
                                      648:{'item964':300,'item965':300,'item295':300,'item296':300,'coin':6480,},
                                      998:{'item964':450,'item965':450,'item295':450,'item296':450,'coin':10000,},
                                      1998:{'item964':900,'item965':900,'item295':900,'item296':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },
























     'o3':{      #圣甲冰河
                        
                         'days':[5,6],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item960':20,'item247':20,'item245':20,'item239':20,'coin':680,},
                                      128:{'item960':50,'item247':50,'item245':50,'item239':50,'coin':1280,},
                                      328:{'item960':120,'item247':120,'item245':120,'item239':120,'coin':3280,},
                                      648:{'item960':300,'item247':300,'item245':300,'item239':300,'coin':6480,},
                                      998:{'item960':450,'item247':450,'item245':450,'item239':450,'coin':10000,},
                                      1998:{'item960':900,'item247':900,'item245':900,'item239':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o4':{      #破空血瞳
                        
                         'days':[7,8],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item961':20,'item248':20,'item246':20,'item244':20,'coin':680,},
                                      128:{'item961':50,'item248':50,'item246':50,'item244':50,'coin':1280,},
                                      328:{'item961':120,'item248':120,'item246':120,'item244':120,'coin':3280,},
                                      648:{'item961':300,'item248':300,'item246':300,'item244':300,'coin':6480,},
                                      998:{'item961':450,'item248':450,'item246':450,'item244':450,'coin':10000,},
                                      1998:{'item961':900,'item248':900,'item246':900,'item244':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o5':{      #博闻推演凶煞
                        
                         'days':[9,10],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item251':20,'item253':20,'item254':20,'item279':20,'coin':680,},
                                      128:{'item251':50,'item253':50,'item254':50,'item279':50,'coin':1280,},
                                      328:{'item251':120,'item253':120,'item254':120,'item279':120,'coin':3280,},
                                      648:{'item251':300,'item253':300,'item254':300,'item279':300,'coin':6480,},
                                      998:{'item251':450,'item253':450,'item254':450,'item279':450,'coin':10000,},
                                      1998:{'item251':900,'item253':900,'item254':900,'item279':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o6':{      #警觉骑胄
                        
                         'days':[11,12],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item966':20,'item967':20,'item294':20,'item293':20,'coin':680,},
                                      128:{'item966':50,'item967':50,'item294':50,'item293':50,'coin':1280,},
                                      328:{'item966':120,'item967':120,'item294':120,'item293':120,'coin':3280,},
                                      648:{'item966':300,'item967':300,'item294':300,'item293':300,'coin':6480,},
                                      998:{'item966':450,'item967':450,'item294':450,'item293':450,'coin':10000,},
                                      1998:{'item966':900,'item967':900,'item294':900,'item293':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o7':{      #掩蔽石肤
                        
                         'days':[13,14],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item968':20,'item969':20,'item295':20,'item296':20,'coin':680,},
                                      128:{'item968':50,'item969':50,'item295':50,'item296':50,'coin':1280,},
                                      328:{'item968':120,'item969':120,'item295':120,'item296':120,'coin':3280,},
                                      648:{'item968':300,'item969':300,'item295':300,'item296':300,'coin':6480,},
                                      998:{'item968':450,'item969':450,'item295':450,'item296':450,'coin':10000,},
                                      1998:{'item968':900,'item969':900,'item295':900,'item296':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o8':{      #狂骨+仁义+凶煞
                        
                         'days':[15,16],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item250':20,'item252':20,'item254':20,'item278':20,'coin':680,},
                                      128:{'item250':50,'item252':50,'item254':50,'item278':50,'coin':1280,},
                                      328:{'item250':120,'item252':120,'item254':120,'item278':120,'coin':3280,},
                                      648:{'item250':300,'item252':300,'item254':300,'item278':300,'coin':6480,},
                                      998:{'item250':450,'item252':450,'item254':450,'item278':450,'coin':10000,},
                                      1998:{'item250':900,'item252':900,'item254':900,'item278':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o9':{      #逆刃+戮尘
                        
                         'days':[17,18],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item962':20,'item963':20,'item206':20,'item211':20,'coin':680,},
                                      128:{'item962':50,'item963':50,'item206':50,'item211':50,'coin':1280,},
                                      328:{'item962':120,'item963':120,'item206':120,'item211':120,'coin':3280,},
                                      648:{'item962':300,'item963':300,'item206':300,'item211':300,'coin':6480,},
                                      998:{'item962':450,'item963':450,'item206':450,'item211':450,'coin':10000,},
                                      1998:{'item962':900,'item963':900,'item206':900,'item211':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o10':{      #焱雨+星陨
                        
                         'days':[19,20],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item964':20,'item965':20,'item292':20,'item291':20,'coin':680,},
                                      128:{'item964':50,'item965':50,'item292':50,'item291':50,'coin':1280,},
                                      328:{'item964':120,'item965':120,'item292':120,'item291':120,'coin':3280,},
                                      648:{'item964':300,'item965':300,'item292':300,'item291':300,'coin':6480,},
                                      998:{'item964':450,'item965':450,'item292':450,'item291':450,'coin':10000,},
                                      1998:{'item964':900,'item965':900,'item292':900,'item291':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },















     'o11':{      #圣甲+障毒+凶煞
                        
                         'days':[21,22],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item254':20,'item961':20,'item970':20,'item277':20,'coin':680,},
                                      128:{'item254':50,'item961':50,'item970':50,'item277':50,'coin':1280,},
                                      328:{'item254':120,'item961':120,'item970':120,'item277':120,'coin':3280,},
                                      648:{'item254':300,'item961':300,'item970':300,'item277':300,'coin':6480,},
                                      998:{'item254':450,'item961':450,'item970':450,'item277':450,'coin':10000,},
                                      1998:{'item254':900,'item961':900,'item970':900,'item277':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o12':{      #警觉+骑胄
                        
                         'days':[23,24],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item966':20,'item967':20,'item294':20,'item293':20,'coin':680,},
                                      128:{'item966':50,'item967':50,'item294':50,'item293':50,'coin':1280,},
                                      328:{'item966':120,'item967':120,'item294':120,'item293':120,'coin':3280,},
                                      648:{'item966':300,'item967':300,'item294':300,'item293':300,'coin':6480,},
                                      998:{'item966':450,'item967':450,'item294':450,'item293':450,'coin':10000,},
                                      1998:{'item966':900,'item967':900,'item294':900,'item293':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o13':{      #掩蔽+石肤
                        
                         'days':[25,26],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item968':20,'item969':20,'item295':20,'item296':20,'coin':680,},
                                      128:{'item968':50,'item969':50,'item295':50,'item296':50,'coin':1280,},
                                      328:{'item968':120,'item969':120,'item295':120,'item296':120,'coin':3280,},
                                      648:{'item968':300,'item969':300,'item295':300,'item296':300,'coin':6480,},
                                      998:{'item968':450,'item969':450,'item295':450,'item296':450,'coin':10000,},
                                      1998:{'item968':900,'item969':900,'item295':900,'item296':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o14':{      #魂玉
                        
                         'days':[27,28],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item1183':1,'item1182':1,'item1170':2,'item1169':1,'coin':680,},
                                      128:{'item1183':2,'item1182':2,'item1170':3,'item1169':2,'coin':1280,},
                                      328:{'item1184':1,'item1183':1,'item1171':2,'item1170':1,'coin':3280,},
                                      648:{'item1198':1,'item1199':1,'item1205':2,'item1172':1,'coin':6480,},
                                      998:{'item1200':1,'item1201':1,'item1205':3,'item1204':2,'coin':10000,},
                                      1998:{'item1204':3,'item1205':3,'item1207':3,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o15':{      #战计
                        
                         'days':[29,30],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item1250':2,'item1251':2,'item1252':2,'item1249':2,'coin':680,},
                                      128:{'item1250':4,'item1251':4,'item1252':4,'item1249':4,'coin':1280,},
                                      328:{'item1254':2,'item1255':2,'item1256':2,'item1253':2,'coin':3280,},
                                      648:{'item1254':4,'item1255':4,'item1256':4,'item1253':4,'coin':6480,},
                                      998:{'item1254':6,'item1255':6,'item1256':6,'item1253':6,'coin':10000,},
                                      1998:{'item1254':10,'item1255':10,'item1256':10,'item1253':10,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o16':{      #圣甲+勾魂
                        
                         'days':[31,32],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item971':20,'item248':20,'item960':20,'item247':20,'coin':680,},
                                      128:{'item971':50,'item248':50,'item960':50,'item247':50,'coin':1280,},
                                      328:{'item971':120,'item248':120,'item960':120,'item247':120,'coin':3280,},
                                      648:{'item971':300,'item248':300,'item960':300,'item247':300,'coin':6480,},
                                      998:{'item971':450,'item248':450,'item960':450,'item247':450,'coin':10000,},
                                      1998:{'item971':900,'item248':900,'item960':900,'item247':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o17':{      #逆刃戮尘
                        
                         'days':[33,34],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item962':20,'item963':20,'item251':20,'item253':20,'coin':680,},
                                      128:{'item962':50,'item963':50,'item251':50,'item253':50,'coin':1280,},
                                      328:{'item962':120,'item963':120,'item251':120,'item253':120,'coin':3280,},
                                      648:{'item962':300,'item963':300,'item251':300,'item253':300,'coin':6480,},
                                      998:{'item962':450,'item963':450,'item251':450,'item253':450,'coin':10000,},
                                      1998:{'item962':900,'item963':900,'item251':900,'item253':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o18':{      #焱雨星陨
                        
                         'days':[35,36],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item964':20,'item965':20,'item250':20,'item252':20,'coin':680,},
                                      128:{'item964':50,'item965':50,'item250':50,'item252':50,'coin':1280,},
                                      328:{'item964':120,'item965':120,'item250':120,'item252':120,'coin':3280,},
                                      648:{'item964':300,'item965':300,'item250':300,'item252':300,'coin':6480,},
                                      998:{'item964':450,'item965':450,'item250':450,'item252':450,'coin':10000,},
                                      1998:{'item964':900,'item965':900,'item250':900,'item252':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o19':{      #警觉骑胄
                        
                         'days':[37,38],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item966':20,'item967':20,'item254':20,'item275':20,'coin':680,},
                                      128:{'item966':50,'item967':50,'item254':50,'item275':50,'coin':1280,},
                                      328:{'item966':120,'item967':120,'item254':120,'item275':120,'coin':3280,},
                                      648:{'item966':300,'item967':300,'item254':300,'item275':300,'coin':6480,},
                                      998:{'item966':450,'item967':450,'item254':450,'item275':450,'coin':10000,},
                                      1998:{'item966':900,'item967':900,'item254':900,'item275':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o20':{      #掩蔽石肤
                        
                         'days':[39,40],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item968':20,'item969':20,'item254':20,'item274':20,'coin':680,},
                                      128:{'item968':50,'item969':50,'item254':50,'item274':50,'coin':1280,},
                                      328:{'item968':120,'item969':120,'item254':120,'item274':120,'coin':3280,},
                                      648:{'item968':300,'item969':300,'item254':300,'item274':300,'coin':6480,},
                                      998:{'item968':450,'item969':450,'item254':450,'item274':450,'coin':10000,},
                                      1998:{'item968':900,'item969':900,'item254':900,'item274':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o21':{      #圣甲障毒
                        
                         'days':[41,42],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item247':20,'item248':20,'item960':20,'item961':20,'coin':680,},
                                      128:{'item247':50,'item248':50,'item960':50,'item961':50,'coin':1280,},
                                      328:{'item247':120,'item248':120,'item960':120,'item961':120,'coin':3280,},
                                      648:{'item247':300,'item248':300,'item960':300,'item961':300,'coin':6480,},
                                      998:{'item247':450,'item248':450,'item960':450,'item961':450,'coin':10000,},
                                      1998:{'item247':900,'item248':900,'item960':900,'item961':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o22':{      #警觉骑胄
                        
                         'days':[43,44],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item966':20,'item967':20,'item294':20,'item293':20,'coin':680,},
                                      128:{'item966':50,'item967':50,'item294':50,'item293':50,'coin':1280,},
                                      328:{'item966':120,'item967':120,'item294':120,'item293':120,'coin':3280,},
                                      648:{'item966':300,'item967':300,'item294':300,'item293':300,'coin':6480,},
                                      998:{'item966':450,'item967':450,'item294':450,'item293':450,'coin':10000,},
                                      1998:{'item966':900,'item967':900,'item294':900,'item293':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o23':{      #掩蔽石肤
                        
                         'days':[45,46],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item968':20,'item969':20,'item295':20,'item296':20,'coin':680,},
                                      128:{'item968':50,'item969':50,'item295':50,'item296':50,'coin':1280,},
                                      328:{'item968':120,'item969':120,'item295':120,'item296':120,'coin':3280,},
                                      648:{'item968':300,'item969':300,'item295':300,'item296':300,'coin':6480,},
                                      998:{'item968':450,'item969':450,'item295':450,'item296':450,'coin':10000,},
                                      1998:{'item968':900,'item969':900,'item295':900,'item296':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },















     'o24':{      #魂玉
                        
                         'days':[47,48],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item1183':1,'item1182':1,'item1170':2,'item1169':1,'coin':680,},
                                      128:{'item1183':2,'item1182':2,'item1170':3,'item1169':2,'coin':1280,},
                                      328:{'item1184':1,'item1183':1,'item1171':2,'item1170':1,'coin':3280,},
                                      648:{'item1198':1,'item1199':1,'item1205':2,'item1172':1,'coin':6480,},
                                      998:{'item1200':1,'item1201':1,'item1205':3,'item1204':2,'coin':10000,},
                                      1998:{'item1204':3,'item1205':3,'item1207':3,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o25':{      #战计
                        
                         'days':[49,50],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item1250':2,'item1251':2,'item1252':2,'item1249':2,'coin':680,},
                                      128:{'item1250':4,'item1251':4,'item1252':4,'item1249':4,'coin':1280,},
                                      328:{'item1254':2,'item1255':2,'item1256':2,'item1253':2,'coin':3280,},
                                      648:{'item1254':4,'item1255':4,'item1256':4,'item1253':4,'coin':6480,},
                                      998:{'item1254':6,'item1255':6,'item1256':6,'item1253':6,'coin':10000,},
                                      1998:{'item1254':10,'item1255':10,'item1256':10,'item1253':10,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o26':{      #冰河圣甲
                        
                         'days':[51,52],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item247':20,'item960':20,'item245':20,'item239':20,'coin':680,},
                                      128:{'item247':50,'item960':50,'item245':50,'item239':50,'coin':1280,},
                                      328:{'item247':120,'item960':120,'item245':120,'item239':120,'coin':3280,},
                                      648:{'item247':300,'item960':300,'item245':300,'item239':300,'coin':6480,},
                                      998:{'item247':450,'item960':450,'item245':450,'item239':450,'coin':10000,},
                                      1998:{'item247':900,'item960':900,'item245':900,'item239':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o27':{      #逆刃+警觉
                        
                         'days':[53,54],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item962':20,'item966':20,'item293':20,'item206':20,'coin':680,},
                                      128:{'item962':50,'item966':50,'item293':50,'item206':50,'coin':1280,},
                                      328:{'item962':120,'item966':120,'item293':120,'item206':120,'coin':3280,},
                                      648:{'item962':300,'item966':300,'item293':300,'item206':300,'coin':6480,},
                                      998:{'item962':450,'item966':450,'item293':450,'item206':450,'coin':10000,},
                                      1998:{'item962':900,'item966':900,'item293':900,'item206':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o28':{      #戮尘+骑胄
                        
                         'days':[55,56],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item963':20,'item967':20,'item294':20,'item211':20,'coin':680,},
                                      128:{'item963':50,'item967':50,'item294':50,'item211':50,'coin':1280,},
                                      328:{'item963':120,'item967':120,'item294':120,'item211':120,'coin':3280,},
                                      648:{'item963':300,'item967':300,'item294':300,'item211':300,'coin':6480,},
                                      998:{'item963':450,'item967':450,'item294':450,'item211':450,'coin':10000,},
                                      1998:{'item963':900,'item967':900,'item294':900,'item211':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o29':{      #英雄技能
                        
                         'days':[57,58],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item964':20,'item968':20,'item295':20,'item291':20,'coin':680,},
                                      128:{'item964':50,'item968':50,'item295':50,'item291':50,'coin':1280,},
                                      328:{'item964':120,'item968':120,'item295':120,'item291':120,'coin':3280,},
                                      648:{'item964':300,'item968':300,'item295':300,'item291':300,'coin':6480,},
                                      998:{'item964':450,'item968':450,'item295':450,'item291':450,'coin':10000,},
                                      1998:{'item964':900,'item968':900,'item295':900,'item291':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },















     'o30':{      #魂玉
                        
                         'days':[59,60],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item965':20,'item969':20,'item296':20,'item292':20,'coin':680,},
                                      128:{'item965':50,'item969':50,'item296':50,'item292':50,'coin':1280,},
                                      328:{'item965':120,'item969':120,'item296':120,'item292':120,'coin':3280,},
                                      648:{'item965':300,'item969':300,'item296':300,'item292':300,'coin':6480,},
                                      998:{'item965':450,'item969':450,'item296':450,'item292':450,'coin':10000,},
                                      1998:{'item965':900,'item969':900,'item296':900,'item292':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o31':{      #金色天
                        
                         'days':[61,62],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item961':20,'item248':20,'item246':20,'item244':20,'coin':680,},
                                      128:{'item961':50,'item248':50,'item246':50,'item244':50,'coin':1280,},
                                      328:{'item961':120,'item248':120,'item246':120,'item244':120,'coin':3280,},
                                      648:{'item961':300,'item248':300,'item246':300,'item244':300,'coin':6480,},
                                      998:{'item961':450,'item248':450,'item246':450,'item244':450,'coin':10000,},
                                      1998:{'item961':900,'item248':900,'item246':900,'item244':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },















     'o32':{      #切割蓄势
                        
                         'days':[63,64],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item250':20,'item252':20,'item254':20,'item275':20,'coin':680,},
                                      128:{'item250':50,'item252':50,'item254':50,'item275':50,'coin':1280,},
                                      328:{'item250':120,'item252':120,'item254':120,'item275':120,'coin':3280,},
                                      648:{'item250':300,'item252':300,'item254':300,'item275':300,'coin':6480,},
                                      998:{'item250':450,'item252':450,'item254':450,'item275':450,'coin':10000,},
                                      1998:{'item250':900,'item252':900,'item254':900,'item275':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o33':{      #金色地
                        
                         'days':[65,66],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item1183':1,'item1182':1,'item1170':2,'item1169':1,'coin':680,},
                                      128:{'item1183':2,'item1182':2,'item1170':3,'item1169':2,'coin':1280,},
                                      328:{'item1184':1,'item1183':1,'item1171':2,'item1170':1,'coin':3280,},
                                      648:{'item1198':1,'item1199':1,'item1205':2,'item1172':1,'coin':6480,},
                                      998:{'item1200':1,'item1201':1,'item1205':3,'item1204':2,'coin':10000,},
                                      1998:{'item1204':3,'item1205':3,'item1207':3,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o34':{      #破空血瞳
                        
                         'days':[67,68],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item1250':2,'item1251':2,'item1252':2,'item1249':2,'coin':680,},
                                      128:{'item1250':4,'item1251':4,'item1252':4,'item1249':4,'coin':1280,},
                                      328:{'item1254':2,'item1255':2,'item1256':2,'item1253':2,'coin':3280,},
                                      648:{'item1254':4,'item1255':4,'item1256':4,'item1253':4,'coin':6480,},
                                      998:{'item1254':6,'item1255':6,'item1256':6,'item1253':6,'coin':10000,},
                                      1998:{'item1254':10,'item1255':10,'item1256':10,'item1253':10,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },















     'o35':{      #金色人
                        
                         'days':[69,70],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item251':20,'item253':20,'item254':20,'item276':20,'coin':680,},
                                      128:{'item251':50,'item253':50,'item254':50,'item276':50,'coin':1280,},
                                      328:{'item251':120,'item253':120,'item254':120,'item276':120,'coin':3280,},
                                      648:{'item251':300,'item253':300,'item254':300,'item276':300,'coin':6480,},
                                      998:{'item251':450,'item253':450,'item254':450,'item276':450,'coin':10000,},
                                      1998:{'item251':900,'item253':900,'item254':900,'item276':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o36':{      #退避洞鉴
                        
                         'days':[71,72],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item247':20,'item960':20,'item245':20,'item239':20,'coin':680,},
                                      128:{'item247':50,'item960':50,'item245':50,'item239':50,'coin':1280,},
                                      328:{'item247':120,'item960':120,'item245':120,'item239':120,'coin':3280,},
                                      648:{'item247':300,'item960':300,'item245':300,'item239':300,'coin':6480,},
                                      998:{'item247':450,'item960':450,'item245':450,'item239':450,'coin':10000,},
                                      1998:{'item247':900,'item960':900,'item245':900,'item239':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o37':{      #英雄技能
                        
                         'days':[73,74],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item962':20,'item966':20,'item293':20,'item206':20,'coin':680,},
                                      128:{'item962':50,'item966':50,'item293':50,'item206':50,'coin':1280,},
                                      328:{'item962':120,'item966':120,'item293':120,'item206':120,'coin':3280,},
                                      648:{'item962':300,'item966':300,'item293':300,'item206':300,'coin':6480,},
                                      998:{'item962':450,'item966':450,'item293':450,'item206':450,'coin':10000,},
                                      1998:{'item962':900,'item966':900,'item293':900,'item206':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },















     'o38':{      #魂玉
                        
                         'days':[75,76],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item963':20,'item967':20,'item294':20,'item211':20,'coin':680,},
                                      128:{'item963':50,'item967':50,'item294':50,'item211':50,'coin':1280,},
                                      328:{'item963':120,'item967':120,'item294':120,'item211':120,'coin':3280,},
                                      648:{'item963':300,'item967':300,'item294':300,'item211':300,'coin':6480,},
                                      998:{'item963':450,'item967':450,'item294':450,'item211':450,'coin':10000,},
                                      1998:{'item963':900,'item967':900,'item294':900,'item211':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o39':{      #金色天
                        
                         'days':[77,78],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item964':20,'item968':20,'item295':20,'item291':20,'coin':680,},
                                      128:{'item964':50,'item968':50,'item295':50,'item291':50,'coin':1280,},
                                      328:{'item964':120,'item968':120,'item295':120,'item291':120,'coin':3280,},
                                      648:{'item964':300,'item968':300,'item295':300,'item291':300,'coin':6480,},
                                      998:{'item964':450,'item968':450,'item295':450,'item291':450,'coin':10000,},
                                      1998:{'item964':900,'item968':900,'item295':900,'item291':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },















     'o40':{      #切割蓄势
                        
                         'days':[79,80],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item965':20,'item969':20,'item296':20,'item292':20,'coin':680,},
                                      128:{'item965':50,'item969':50,'item296':50,'item292':50,'coin':1280,},
                                      328:{'item965':120,'item969':120,'item296':120,'item292':120,'coin':3280,},
                                      648:{'item965':300,'item969':300,'item296':300,'item292':300,'coin':6480,},
                                      998:{'item965':450,'item969':450,'item296':450,'item292':450,'coin':10000,},
                                      1998:{'item965':900,'item969':900,'item296':900,'item292':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o41':{      #金色地
                        
                         'days':[81,82],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item961':20,'item248':20,'item246':20,'item244':20,'coin':680,},
                                      128:{'item961':50,'item248':50,'item246':50,'item244':50,'coin':1280,},
                                      328:{'item961':120,'item248':120,'item246':120,'item244':120,'coin':3280,},
                                      648:{'item961':300,'item248':300,'item246':300,'item244':300,'coin':6480,},
                                      998:{'item961':450,'item248':450,'item246':450,'item244':450,'coin':10000,},
                                      1998:{'item961':900,'item248':900,'item246':900,'item244':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o42':{      #破空血瞳
                        
                         'days':[83,84],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item250':20,'item252':20,'item254':20,'item275':20,'coin':680,},
                                      128:{'item250':50,'item252':50,'item254':50,'item275':50,'coin':1280,},
                                      328:{'item250':120,'item252':120,'item254':120,'item275':120,'coin':3280,},
                                      648:{'item250':300,'item252':300,'item254':300,'item275':300,'coin':6480,},
                                      998:{'item250':450,'item252':450,'item254':450,'item275':450,'coin':10000,},
                                      1998:{'item250':900,'item252':900,'item254':900,'item275':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o43':{      #金色人
                        
                         'days':[85,86],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item1183':1,'item1182':1,'item1170':2,'item1169':1,'coin':680,},
                                      128:{'item1183':2,'item1182':2,'item1170':3,'item1169':2,'coin':1280,},
                                      328:{'item1184':1,'item1183':1,'item1171':2,'item1170':1,'coin':3280,},
                                      648:{'item1198':1,'item1199':1,'item1205':2,'item1172':1,'coin':6480,},
                                      998:{'item1200':1,'item1201':1,'item1205':3,'item1204':2,'coin':10000,},
                                      1998:{'item1204':3,'item1205':3,'item1207':3,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o44':{      #退避洞鉴
                        
                         'days':[87,88],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item1250':2,'item1251':2,'item1252':2,'item1249':2,'coin':680,},
                                      128:{'item1250':4,'item1251':4,'item1252':4,'item1249':4,'coin':1280,},
                                      328:{'item1254':2,'item1255':2,'item1256':2,'item1253':2,'coin':3280,},
                                      648:{'item1254':4,'item1255':4,'item1256':4,'item1253':4,'coin':6480,},
                                      998:{'item1254':6,'item1255':6,'item1256':6,'item1253':6,'coin':10000,},
                                      1998:{'item1254':10,'item1255':10,'item1256':10,'item1253':10,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o45':{      #英雄技能
                        
                         'days':[89,90],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item251':20,'item253':20,'item254':20,'item276':20,'coin':680,},
                                      128:{'item251':50,'item253':50,'item254':50,'item276':50,'coin':1280,},
                                      328:{'item251':120,'item253':120,'item254':120,'item276':120,'coin':3280,},
                                      648:{'item251':300,'item253':300,'item254':300,'item276':300,'coin':6480,},
                                      998:{'item251':450,'item253':450,'item254':450,'item276':450,'coin':10000,},
                                      1998:{'item251':900,'item253':900,'item254':900,'item276':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },















     'o46':{      #魂玉
                        
                         'days':[91,92],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item247':20,'item960':20,'item245':20,'item239':20,'coin':680,},
                                      128:{'item247':50,'item960':50,'item245':50,'item239':50,'coin':1280,},
                                      328:{'item247':120,'item960':120,'item245':120,'item239':120,'coin':3280,},
                                      648:{'item247':300,'item960':300,'item245':300,'item239':300,'coin':6480,},
                                      998:{'item247':450,'item960':450,'item245':450,'item239':450,'coin':10000,},
                                      1998:{'item247':900,'item960':900,'item245':900,'item239':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o47':{      #金色天
                        
                         'days':[93,94],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item962':20,'item966':20,'item293':20,'item206':20,'coin':680,},
                                      128:{'item962':50,'item966':50,'item293':50,'item206':50,'coin':1280,},
                                      328:{'item962':120,'item966':120,'item293':120,'item206':120,'coin':3280,},
                                      648:{'item962':300,'item966':300,'item293':300,'item206':300,'coin':6480,},
                                      998:{'item962':450,'item966':450,'item293':450,'item206':450,'coin':10000,},
                                      1998:{'item962':900,'item966':900,'item293':900,'item206':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },















     'o48':{      #切割蓄势
                        
                         'days':[95,96],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item963':20,'item967':20,'item294':20,'item211':20,'coin':680,},
                                      128:{'item963':50,'item967':50,'item294':50,'item211':50,'coin':1280,},
                                      328:{'item963':120,'item967':120,'item294':120,'item211':120,'coin':3280,},
                                      648:{'item963':300,'item967':300,'item294':300,'item211':300,'coin':6480,},
                                      998:{'item963':450,'item967':450,'item294':450,'item211':450,'coin':10000,},
                                      1998:{'item963':900,'item967':900,'item294':900,'item211':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o49':{      #金色地
                        
                         'days':[97,98],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item964':20,'item968':20,'item295':20,'item291':20,'coin':680,},
                                      128:{'item964':50,'item968':50,'item295':50,'item291':50,'coin':1280,},
                                      328:{'item964':120,'item968':120,'item295':120,'item291':120,'coin':3280,},
                                      648:{'item964':300,'item968':300,'item295':300,'item291':300,'coin':6480,},
                                      998:{'item964':450,'item968':450,'item295':450,'item291':450,'coin':10000,},
                                      1998:{'item964':900,'item968':900,'item295':900,'item291':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o50':{      #破空血瞳
                        
                         'days':[99,100],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item965':20,'item969':20,'item296':20,'item292':20,'coin':680,},
                                      128:{'item965':50,'item969':50,'item296':50,'item292':50,'coin':1280,},
                                      328:{'item965':120,'item969':120,'item296':120,'item292':120,'coin':3280,},
                                      648:{'item965':300,'item969':300,'item296':300,'item292':300,'coin':6480,},
                                      998:{'item965':450,'item969':450,'item296':450,'item292':450,'coin':10000,},
                                      1998:{'item965':900,'item969':900,'item296':900,'item292':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o51':{      #金色人
                        
                         'days':[101,102],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item961':20,'item248':20,'item246':20,'item244':20,'coin':680,},
                                      128:{'item961':50,'item248':50,'item246':50,'item244':50,'coin':1280,},
                                      328:{'item961':120,'item248':120,'item246':120,'item244':120,'coin':3280,},
                                      648:{'item961':300,'item248':300,'item246':300,'item244':300,'coin':6480,},
                                      998:{'item961':450,'item248':450,'item246':450,'item244':450,'coin':10000,},
                                      1998:{'item961':900,'item248':900,'item246':900,'item244':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o52':{      #退避洞鉴
                        
                         'days':[103,104],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item250':20,'item252':20,'item254':20,'item275':20,'coin':680,},
                                      128:{'item250':50,'item252':50,'item254':50,'item275':50,'coin':1280,},
                                      328:{'item250':120,'item252':120,'item254':120,'item275':120,'coin':3280,},
                                      648:{'item250':300,'item252':300,'item254':300,'item275':300,'coin':6480,},
                                      998:{'item250':450,'item252':450,'item254':450,'item275':450,'coin':10000,},
                                      1998:{'item250':900,'item252':900,'item254':900,'item275':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },
















     'o53':{      #英雄技能
                        
                         'days':[105,106],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item1183':1,'item1182':1,'item1170':2,'item1169':1,'coin':680,},
                                      128:{'item1183':2,'item1182':2,'item1170':3,'item1169':2,'coin':1280,},
                                      328:{'item1184':1,'item1183':1,'item1171':2,'item1170':1,'coin':3280,},
                                      648:{'item1198':1,'item1199':1,'item1205':2,'item1172':1,'coin':6480,},
                                      998:{'item1200':1,'item1201':1,'item1205':3,'item1204':2,'coin':10000,},
                                      1998:{'item1204':3,'item1205':3,'item1207':3,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o54':{      #魂玉
                        
                         'days':[107,108],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item1250':2,'item1251':2,'item1252':2,'item1249':2,'coin':680,},
                                      128:{'item1250':4,'item1251':4,'item1252':4,'item1249':4,'coin':1280,},
                                      328:{'item1254':2,'item1255':2,'item1256':2,'item1253':2,'coin':3280,},
                                      648:{'item1254':4,'item1255':4,'item1256':4,'item1253':4,'coin':6480,},
                                      998:{'item1254':6,'item1255':6,'item1256':6,'item1253':6,'coin':10000,},
                                      1998:{'item1254':10,'item1255':10,'item1256':10,'item1253':10,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o55':{      #金色天
                        
                         'days':[109,110],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item251':20,'item253':20,'item254':20,'item276':20,'coin':680,},
                                      128:{'item251':50,'item253':50,'item254':50,'item276':50,'coin':1280,},
                                      328:{'item251':120,'item253':120,'item254':120,'item276':120,'coin':3280,},
                                      648:{'item251':300,'item253':300,'item254':300,'item276':300,'coin':6480,},
                                      998:{'item251':450,'item253':450,'item254':450,'item276':450,'coin':10000,},
                                      1998:{'item251':900,'item253':900,'item254':900,'item276':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },















     'o56':{      #切割蓄势
                        
                         'days':[111,112],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item247':20,'item960':20,'item245':20,'item239':20,'coin':680,},
                                      128:{'item247':50,'item960':50,'item245':50,'item239':50,'coin':1280,},
                                      328:{'item247':120,'item960':120,'item245':120,'item239':120,'coin':3280,},
                                      648:{'item247':300,'item960':300,'item245':300,'item239':300,'coin':6480,},
                                      998:{'item247':450,'item960':450,'item245':450,'item239':450,'coin':10000,},
                                      1998:{'item247':900,'item960':900,'item245':900,'item239':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o57':{      #金色地
                        
                         'days':[113,114],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item962':20,'item966':20,'item293':20,'item206':20,'coin':680,},
                                      128:{'item962':50,'item966':50,'item293':50,'item206':50,'coin':1280,},
                                      328:{'item962':120,'item966':120,'item293':120,'item206':120,'coin':3280,},
                                      648:{'item962':300,'item966':300,'item293':300,'item206':300,'coin':6480,},
                                      998:{'item962':450,'item966':450,'item293':450,'item206':450,'coin':10000,},
                                      1998:{'item962':900,'item966':900,'item293':900,'item206':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o58':{      #破空血瞳
                        
                         'days':[115,116],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item963':20,'item967':20,'item294':20,'item211':20,'coin':680,},
                                      128:{'item963':50,'item967':50,'item294':50,'item211':50,'coin':1280,},
                                      328:{'item963':120,'item967':120,'item294':120,'item211':120,'coin':3280,},
                                      648:{'item963':300,'item967':300,'item294':300,'item211':300,'coin':6480,},
                                      998:{'item963':450,'item967':450,'item294':450,'item211':450,'coin':10000,},
                                      1998:{'item963':900,'item967':900,'item294':900,'item211':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o59':{      #金色人
                        
                         'days':[117,118],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item964':20,'item968':20,'item295':20,'item291':20,'coin':680,},
                                      128:{'item964':50,'item968':50,'item295':50,'item291':50,'coin':1280,},
                                      328:{'item964':120,'item968':120,'item295':120,'item291':120,'coin':3280,},
                                      648:{'item964':300,'item968':300,'item295':300,'item291':300,'coin':6480,},
                                      998:{'item964':450,'item968':450,'item295':450,'item291':450,'coin':10000,},
                                      1998:{'item964':900,'item968':900,'item295':900,'item291':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o60':{      #退避洞鉴
                        
                         'days':[119,120],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item965':20,'item969':20,'item296':20,'item292':20,'coin':680,},
                                      128:{'item965':50,'item969':50,'item296':50,'item292':50,'coin':1280,},
                                      328:{'item965':120,'item969':120,'item296':120,'item292':120,'coin':3280,},
                                      648:{'item965':300,'item969':300,'item296':300,'item292':300,'coin':6480,},
                                      998:{'item965':450,'item969':450,'item296':450,'item292':450,'coin':10000,},
                                      1998:{'item965':900,'item969':900,'item296':900,'item292':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o61':{      #英雄技能
                        
                         'days':[121,122],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item961':20,'item248':20,'item246':20,'item244':20,'coin':680,},
                                      128:{'item961':50,'item248':50,'item246':50,'item244':50,'coin':1280,},
                                      328:{'item961':120,'item248':120,'item246':120,'item244':120,'coin':3280,},
                                      648:{'item961':300,'item248':300,'item246':300,'item244':300,'coin':6480,},
                                      998:{'item961':450,'item248':450,'item246':450,'item244':450,'coin':10000,},
                                      1998:{'item961':900,'item248':900,'item246':900,'item244':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },















     'o62':{      #魂玉
                        
                         'days':[123,124],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item250':20,'item252':20,'item254':20,'item275':20,'coin':680,},
                                      128:{'item250':50,'item252':50,'item254':50,'item275':50,'coin':1280,},
                                      328:{'item250':120,'item252':120,'item254':120,'item275':120,'coin':3280,},
                                      648:{'item250':300,'item252':300,'item254':300,'item275':300,'coin':6480,},
                                      998:{'item250':450,'item252':450,'item254':450,'item275':450,'coin':10000,},
                                      1998:{'item250':900,'item252':900,'item254':900,'item275':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o63':{      #金色天
                        
                         'days':[125,126],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item1183':1,'item1182':1,'item1170':2,'item1169':1,'coin':680,},
                                      128:{'item1183':2,'item1182':2,'item1170':3,'item1169':2,'coin':1280,},
                                      328:{'item1184':1,'item1183':1,'item1171':2,'item1170':1,'coin':3280,},
                                      648:{'item1198':1,'item1199':1,'item1205':2,'item1172':1,'coin':6480,},
                                      998:{'item1200':1,'item1201':1,'item1205':3,'item1204':2,'coin':10000,},
                                      1998:{'item1204':3,'item1205':3,'item1207':3,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },















     'o64':{      #切割蓄势
                        
                         'days':[127,128],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item1250':2,'item1251':2,'item1252':2,'item1249':2,'coin':680,},
                                      128:{'item1250':4,'item1251':4,'item1252':4,'item1249':4,'coin':1280,},
                                      328:{'item1254':2,'item1255':2,'item1256':2,'item1253':2,'coin':3280,},
                                      648:{'item1254':4,'item1255':4,'item1256':4,'item1253':4,'coin':6480,},
                                      998:{'item1254':6,'item1255':6,'item1256':6,'item1253':6,'coin':10000,},
                                      1998:{'item1254':10,'item1255':10,'item1256':10,'item1253':10,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o65':{      #金色地
                        
                         'days':[129,130],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item251':20,'item253':20,'item254':20,'item276':20,'coin':680,},
                                      128:{'item251':50,'item253':50,'item254':50,'item276':50,'coin':1280,},
                                      328:{'item251':120,'item253':120,'item254':120,'item276':120,'coin':3280,},
                                      648:{'item251':300,'item253':300,'item254':300,'item276':300,'coin':6480,},
                                      998:{'item251':450,'item253':450,'item254':450,'item276':450,'coin':10000,},
                                      1998:{'item251':900,'item253':900,'item254':900,'item276':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },




       'o66':{      #切割蓄势
                        
                         'days':[131,132],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item247':20,'item960':20,'item245':20,'item239':20,'coin':680,},
                                      128:{'item247':50,'item960':50,'item245':50,'item239':50,'coin':1280,},
                                      328:{'item247':120,'item960':120,'item245':120,'item239':120,'coin':3280,},
                                      648:{'item247':300,'item960':300,'item245':300,'item239':300,'coin':6480,},
                                      998:{'item247':450,'item960':450,'item245':450,'item239':450,'coin':10000,},
                                      1998:{'item247':900,'item960':900,'item245':900,'item239':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o67':{      #金色地
                        
                         'days':[133,134],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item962':20,'item966':20,'item293':20,'item206':20,'coin':680,},
                                      128:{'item962':50,'item966':50,'item293':50,'item206':50,'coin':1280,},
                                      328:{'item962':120,'item966':120,'item293':120,'item206':120,'coin':3280,},
                                      648:{'item962':300,'item966':300,'item293':300,'item206':300,'coin':6480,},
                                      998:{'item962':450,'item966':450,'item293':450,'item206':450,'coin':10000,},
                                      1998:{'item962':900,'item966':900,'item293':900,'item206':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o68':{      #破空血瞳
                        
                         'days':[135,136],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item963':20,'item967':20,'item294':20,'item211':20,'coin':680,},
                                      128:{'item963':50,'item967':50,'item294':50,'item211':50,'coin':1280,},
                                      328:{'item963':120,'item967':120,'item294':120,'item211':120,'coin':3280,},
                                      648:{'item963':300,'item967':300,'item294':300,'item211':300,'coin':6480,},
                                      998:{'item963':450,'item967':450,'item294':450,'item211':450,'coin':10000,},
                                      1998:{'item963':900,'item967':900,'item294':900,'item211':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o69':{      #金色人
                        
                         'days':[137,138],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item964':20,'item968':20,'item295':20,'item291':20,'coin':680,},
                                      128:{'item964':50,'item968':50,'item295':50,'item291':50,'coin':1280,},
                                      328:{'item964':120,'item968':120,'item295':120,'item291':120,'coin':3280,},
                                      648:{'item964':300,'item968':300,'item295':300,'item291':300,'coin':6480,},
                                      998:{'item964':450,'item968':450,'item295':450,'item291':450,'coin':10000,},
                                      1998:{'item964':900,'item968':900,'item295':900,'item291':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o70':{      #退避洞鉴
                        
                         'days':[139,140],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item965':20,'item969':20,'item296':20,'item292':20,'coin':680,},
                                      128:{'item965':50,'item969':50,'item296':50,'item292':50,'coin':1280,},
                                      328:{'item965':120,'item969':120,'item296':120,'item292':120,'coin':3280,},
                                      648:{'item965':300,'item969':300,'item296':300,'item292':300,'coin':6480,},
                                      998:{'item965':450,'item969':450,'item296':450,'item292':450,'coin':10000,},
                                      1998:{'item965':900,'item969':900,'item296':900,'item292':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o71':{      #英雄技能
                        
                         'days':[141,142],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item961':20,'item248':20,'item246':20,'item244':20,'coin':680,},
                                      128:{'item961':50,'item248':50,'item246':50,'item244':50,'coin':1280,},
                                      328:{'item961':120,'item248':120,'item246':120,'item244':120,'coin':3280,},
                                      648:{'item961':300,'item248':300,'item246':300,'item244':300,'coin':6480,},
                                      998:{'item961':450,'item248':450,'item246':450,'item244':450,'coin':10000,},
                                      1998:{'item961':900,'item248':900,'item246':900,'item244':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o72':{      #魂玉
                        
                         'days':[143,144],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item250':20,'item252':20,'item254':20,'item275':20,'coin':680,},
                                      128:{'item250':50,'item252':50,'item254':50,'item275':50,'coin':1280,},
                                      328:{'item250':120,'item252':120,'item254':120,'item275':120,'coin':3280,},
                                      648:{'item250':300,'item252':300,'item254':300,'item275':300,'coin':6480,},
                                      998:{'item250':450,'item252':450,'item254':450,'item275':450,'coin':10000,},
                                      1998:{'item250':900,'item252':900,'item254':900,'item275':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o73':{      #金色天
                        
                         'days':[145,146],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item1183':1,'item1182':1,'item1170':2,'item1169':1,'coin':680,},
                                      128:{'item1183':2,'item1182':2,'item1170':3,'item1169':2,'coin':1280,},
                                      328:{'item1184':1,'item1183':1,'item1171':2,'item1170':1,'coin':3280,},
                                      648:{'item1198':1,'item1199':1,'item1205':2,'item1172':1,'coin':6480,},
                                      998:{'item1200':1,'item1201':1,'item1205':3,'item1204':2,'coin':10000,},
                                      1998:{'item1204':3,'item1205':3,'item1207':3,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o74':{      #切割蓄势
                        
                         'days':[147,148],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item1250':2,'item1251':2,'item1252':2,'item1249':2,'coin':680,},
                                      128:{'item1250':4,'item1251':4,'item1252':4,'item1249':4,'coin':1280,},
                                      328:{'item1254':2,'item1255':2,'item1256':2,'item1253':2,'coin':3280,},
                                      648:{'item1254':4,'item1255':4,'item1256':4,'item1253':4,'coin':6480,},
                                      998:{'item1254':6,'item1255':6,'item1256':6,'item1253':6,'coin':10000,},
                                      1998:{'item1254':10,'item1255':10,'item1256':10,'item1253':10,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o75':{      #金色地
                        
                         'days':[149,150],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item251':20,'item253':20,'item254':20,'item276':20,'coin':680,},
                                      128:{'item251':50,'item253':50,'item254':50,'item276':50,'coin':1280,},
                                      328:{'item251':120,'item253':120,'item254':120,'item276':120,'coin':3280,},
                                      648:{'item251':300,'item253':300,'item254':300,'item276':300,'coin':6480,},
                                      998:{'item251':450,'item253':450,'item254':450,'item276':450,'coin':10000,},
                                      1998:{'item251':900,'item253':900,'item254':900,'item276':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o76':{      #切割蓄势
                        
                         'days':[151,152],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item247':20,'item960':20,'item245':20,'item239':20,'coin':680,},
                                      128:{'item247':50,'item960':50,'item245':50,'item239':50,'coin':1280,},
                                      328:{'item247':120,'item960':120,'item245':120,'item239':120,'coin':3280,},
                                      648:{'item247':300,'item960':300,'item245':300,'item239':300,'coin':6480,},
                                      998:{'item247':450,'item960':450,'item245':450,'item239':450,'coin':10000,},
                                      1998:{'item247':900,'item960':900,'item245':900,'item239':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o77':{      #金色地
                        
                         'days':[153,154],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item962':20,'item966':20,'item293':20,'item206':20,'coin':680,},
                                      128:{'item962':50,'item966':50,'item293':50,'item206':50,'coin':1280,},
                                      328:{'item962':120,'item966':120,'item293':120,'item206':120,'coin':3280,},
                                      648:{'item962':300,'item966':300,'item293':300,'item206':300,'coin':6480,},
                                      998:{'item962':450,'item966':450,'item293':450,'item206':450,'coin':10000,},
                                      1998:{'item962':900,'item966':900,'item293':900,'item206':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o78':{      #破空血瞳
                        
                         'days':[155,156],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item963':20,'item967':20,'item294':20,'item211':20,'coin':680,},
                                      128:{'item963':50,'item967':50,'item294':50,'item211':50,'coin':1280,},
                                      328:{'item963':120,'item967':120,'item294':120,'item211':120,'coin':3280,},
                                      648:{'item963':300,'item967':300,'item294':300,'item211':300,'coin':6480,},
                                      998:{'item963':450,'item967':450,'item294':450,'item211':450,'coin':10000,},
                                      1998:{'item963':900,'item967':900,'item294':900,'item211':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o79':{      #金色人
                        
                         'days':[157,158],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item964':20,'item968':20,'item295':20,'item291':20,'coin':680,},
                                      128:{'item964':50,'item968':50,'item295':50,'item291':50,'coin':1280,},
                                      328:{'item964':120,'item968':120,'item295':120,'item291':120,'coin':3280,},
                                      648:{'item964':300,'item968':300,'item295':300,'item291':300,'coin':6480,},
                                      998:{'item964':450,'item968':450,'item295':450,'item291':450,'coin':10000,},
                                      1998:{'item964':900,'item968':900,'item295':900,'item291':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o80':{      #退避洞鉴
                        
                         'days':[159,160],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item965':20,'item969':20,'item296':20,'item292':20,'coin':680,},
                                      128:{'item965':50,'item969':50,'item296':50,'item292':50,'coin':1280,},
                                      328:{'item965':120,'item969':120,'item296':120,'item292':120,'coin':3280,},
                                      648:{'item965':300,'item969':300,'item296':300,'item292':300,'coin':6480,},
                                      998:{'item965':450,'item969':450,'item296':450,'item292':450,'coin':10000,},
                                      1998:{'item965':900,'item969':900,'item296':900,'item292':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o81':{      #英雄技能
                        
                         'days':[161,162],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item961':20,'item248':20,'item246':20,'item244':20,'coin':680,},
                                      128:{'item961':50,'item248':50,'item246':50,'item244':50,'coin':1280,},
                                      328:{'item961':120,'item248':120,'item246':120,'item244':120,'coin':3280,},
                                      648:{'item961':300,'item248':300,'item246':300,'item244':300,'coin':6480,},
                                      998:{'item961':450,'item248':450,'item246':450,'item244':450,'coin':10000,},
                                      1998:{'item961':900,'item248':900,'item246':900,'item244':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o82':{      #魂玉
                        
                         'days':[163,164],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item250':20,'item252':20,'item254':20,'item275':20,'coin':680,},
                                      128:{'item250':50,'item252':50,'item254':50,'item275':50,'coin':1280,},
                                      328:{'item250':120,'item252':120,'item254':120,'item275':120,'coin':3280,},
                                      648:{'item250':300,'item252':300,'item254':300,'item275':300,'coin':6480,},
                                      998:{'item250':450,'item252':450,'item254':450,'item275':450,'coin':10000,},
                                      1998:{'item250':900,'item252':900,'item254':900,'item275':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o83':{      #金色天
                        
                         'days':[165,166],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item1183':1,'item1182':1,'item1170':2,'item1169':1,'coin':680,},
                                      128:{'item1183':2,'item1182':2,'item1170':3,'item1169':2,'coin':1280,},
                                      328:{'item1184':1,'item1183':1,'item1171':2,'item1170':1,'coin':3280,},
                                      648:{'item1198':1,'item1199':1,'item1205':2,'item1172':1,'coin':6480,},
                                      998:{'item1200':1,'item1201':1,'item1205':3,'item1204':2,'coin':10000,},
                                      1998:{'item1204':3,'item1205':3,'item1207':3,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o84':{      #切割蓄势
                        
                         'days':[167,168],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item1250':2,'item1251':2,'item1252':2,'item1249':2,'coin':680,},
                                      128:{'item1250':4,'item1251':4,'item1252':4,'item1249':4,'coin':1280,},
                                      328:{'item1254':2,'item1255':2,'item1256':2,'item1253':2,'coin':3280,},
                                      648:{'item1254':4,'item1255':4,'item1256':4,'item1253':4,'coin':6480,},
                                      998:{'item1254':6,'item1255':6,'item1256':6,'item1253':6,'coin':10000,},
                                      1998:{'item1254':10,'item1255':10,'item1256':10,'item1253':10,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o85':{      #金色地
                        
                         'days':[169,170],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item251':20,'item253':20,'item254':20,'item276':20,'coin':680,},
                                      128:{'item251':50,'item253':50,'item254':50,'item276':50,'coin':1280,},
                                      328:{'item251':120,'item253':120,'item254':120,'item276':120,'coin':3280,},
                                      648:{'item251':300,'item253':300,'item254':300,'item276':300,'coin':6480,},
                                      998:{'item251':450,'item253':450,'item254':450,'item276':450,'coin':10000,},
                                      1998:{'item251':900,'item253':900,'item254':900,'item276':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o86':{      #切割蓄势
                        
                         'days':[171,172],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item247':20,'item960':20,'item245':20,'item239':20,'coin':680,},
                                      128:{'item247':50,'item960':50,'item245':50,'item239':50,'coin':1280,},
                                      328:{'item247':120,'item960':120,'item245':120,'item239':120,'coin':3280,},
                                      648:{'item247':300,'item960':300,'item245':300,'item239':300,'coin':6480,},
                                      998:{'item247':450,'item960':450,'item245':450,'item239':450,'coin':10000,},
                                      1998:{'item247':900,'item960':900,'item245':900,'item239':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o87':{      #金色地
                        
                         'days':[173,174],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item962':20,'item966':20,'item293':20,'item206':20,'coin':680,},
                                      128:{'item962':50,'item966':50,'item293':50,'item206':50,'coin':1280,},
                                      328:{'item962':120,'item966':120,'item293':120,'item206':120,'coin':3280,},
                                      648:{'item962':300,'item966':300,'item293':300,'item206':300,'coin':6480,},
                                      998:{'item962':450,'item966':450,'item293':450,'item206':450,'coin':10000,},
                                      1998:{'item962':900,'item966':900,'item293':900,'item206':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o88':{      #破空血瞳
                        
                         'days':[175,176],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item963':20,'item967':20,'item294':20,'item211':20,'coin':680,},
                                      128:{'item963':50,'item967':50,'item294':50,'item211':50,'coin':1280,},
                                      328:{'item963':120,'item967':120,'item294':120,'item211':120,'coin':3280,},
                                      648:{'item963':300,'item967':300,'item294':300,'item211':300,'coin':6480,},
                                      998:{'item963':450,'item967':450,'item294':450,'item211':450,'coin':10000,},
                                      1998:{'item963':900,'item967':900,'item294':900,'item211':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o89':{      #金色人
                        
                         'days':[177,178],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item964':20,'item968':20,'item295':20,'item291':20,'coin':680,},
                                      128:{'item964':50,'item968':50,'item295':50,'item291':50,'coin':1280,},
                                      328:{'item964':120,'item968':120,'item295':120,'item291':120,'coin':3280,},
                                      648:{'item964':300,'item968':300,'item295':300,'item291':300,'coin':6480,},
                                      998:{'item964':450,'item968':450,'item295':450,'item291':450,'coin':10000,},
                                      1998:{'item964':900,'item968':900,'item295':900,'item291':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o90':{      #退避洞鉴
                        
                         'days':[179,180],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item965':20,'item969':20,'item296':20,'item292':20,'coin':680,},
                                      128:{'item965':50,'item969':50,'item296':50,'item292':50,'coin':1280,},
                                      328:{'item965':120,'item969':120,'item296':120,'item292':120,'coin':3280,},
                                      648:{'item965':300,'item969':300,'item296':300,'item292':300,'coin':6480,},
                                      998:{'item965':450,'item969':450,'item296':450,'item292':450,'coin':10000,},
                                      1998:{'item965':900,'item969':900,'item296':900,'item292':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o91':{      #英雄技能
                        
                         'days':[181,182],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item961':20,'item248':20,'item246':20,'item244':20,'coin':680,},
                                      128:{'item961':50,'item248':50,'item246':50,'item244':50,'coin':1280,},
                                      328:{'item961':120,'item248':120,'item246':120,'item244':120,'coin':3280,},
                                      648:{'item961':300,'item248':300,'item246':300,'item244':300,'coin':6480,},
                                      998:{'item961':450,'item248':450,'item246':450,'item244':450,'coin':10000,},
                                      1998:{'item961':900,'item248':900,'item246':900,'item244':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o92':{      #魂玉
                        
                         'days':[183,184],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item250':20,'item252':20,'item254':20,'item275':20,'coin':680,},
                                      128:{'item250':50,'item252':50,'item254':50,'item275':50,'coin':1280,},
                                      328:{'item250':120,'item252':120,'item254':120,'item275':120,'coin':3280,},
                                      648:{'item250':300,'item252':300,'item254':300,'item275':300,'coin':6480,},
                                      998:{'item250':450,'item252':450,'item254':450,'item275':450,'coin':10000,},
                                      1998:{'item250':900,'item252':900,'item254':900,'item275':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o93':{      #金色天
                        
                         'days':[185,186],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item1183':1,'item1182':1,'item1170':2,'item1169':1,'coin':680,},
                                      128:{'item1183':2,'item1182':2,'item1170':3,'item1169':2,'coin':1280,},
                                      328:{'item1184':1,'item1183':1,'item1171':2,'item1170':1,'coin':3280,},
                                      648:{'item1198':1,'item1199':1,'item1205':2,'item1172':1,'coin':6480,},
                                      998:{'item1200':1,'item1201':1,'item1205':3,'item1204':2,'coin':10000,},
                                      1998:{'item1204':3,'item1205':3,'item1207':3,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o94':{      #切割蓄势
                        
                         'days':[187,188],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item1250':2,'item1251':2,'item1252':2,'item1249':2,'coin':680,},
                                      128:{'item1250':4,'item1251':4,'item1252':4,'item1249':4,'coin':1280,},
                                      328:{'item1254':2,'item1255':2,'item1256':2,'item1253':2,'coin':3280,},
                                      648:{'item1254':4,'item1255':4,'item1256':4,'item1253':4,'coin':6480,},
                                      998:{'item1254':6,'item1255':6,'item1256':6,'item1253':6,'coin':10000,},
                                      1998:{'item1254':10,'item1255':10,'item1256':10,'item1253':10,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o95':{      #金色地
                        
                         'days':[189,190],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item251':20,'item253':20,'item254':20,'item276':20,'coin':680,},
                                      128:{'item251':50,'item253':50,'item254':50,'item276':50,'coin':1280,},
                                      328:{'item251':120,'item253':120,'item254':120,'item276':120,'coin':3280,},
                                      648:{'item251':300,'item253':300,'item254':300,'item276':300,'coin':6480,},
                                      998:{'item251':450,'item253':450,'item254':450,'item276':450,'coin':10000,},
                                      1998:{'item251':900,'item253':900,'item254':900,'item276':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o96':{      #切割蓄势
                        
                         'days':[191,192],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item247':20,'item960':20,'item245':20,'item239':20,'coin':680,},
                                      128:{'item247':50,'item960':50,'item245':50,'item239':50,'coin':1280,},
                                      328:{'item247':120,'item960':120,'item245':120,'item239':120,'coin':3280,},
                                      648:{'item247':300,'item960':300,'item245':300,'item239':300,'coin':6480,},
                                      998:{'item247':450,'item960':450,'item245':450,'item239':450,'coin':10000,},
                                      1998:{'item247':900,'item960':900,'item245':900,'item239':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o97':{      #金色地
                        
                         'days':[193,194],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item962':20,'item966':20,'item293':20,'item206':20,'coin':680,},
                                      128:{'item962':50,'item966':50,'item293':50,'item206':50,'coin':1280,},
                                      328:{'item962':120,'item966':120,'item293':120,'item206':120,'coin':3280,},
                                      648:{'item962':300,'item966':300,'item293':300,'item206':300,'coin':6480,},
                                      998:{'item962':450,'item966':450,'item293':450,'item206':450,'coin':10000,},
                                      1998:{'item962':900,'item966':900,'item293':900,'item206':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o98':{      #破空血瞳
                        
                         'days':[195,196],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item963':20,'item967':20,'item294':20,'item211':20,'coin':680,},
                                      128:{'item963':50,'item967':50,'item294':50,'item211':50,'coin':1280,},
                                      328:{'item963':120,'item967':120,'item294':120,'item211':120,'coin':3280,},
                                      648:{'item963':300,'item967':300,'item294':300,'item211':300,'coin':6480,},
                                      998:{'item963':450,'item967':450,'item294':450,'item211':450,'coin':10000,},
                                      1998:{'item963':900,'item967':900,'item294':900,'item211':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o99':{      #金色人
                        
                         'days':[197,198],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item964':20,'item968':20,'item295':20,'item291':20,'coin':680,},
                                      128:{'item964':50,'item968':50,'item295':50,'item291':50,'coin':1280,},
                                      328:{'item964':120,'item968':120,'item295':120,'item291':120,'coin':3280,},
                                      648:{'item964':300,'item968':300,'item295':300,'item291':300,'coin':6480,},
                                      998:{'item964':450,'item968':450,'item295':450,'item291':450,'coin':10000,},
                                      1998:{'item964':900,'item968':900,'item295':900,'item291':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o100':{      #退避洞鉴
                        
                         'days':[199,200],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item965':20,'item969':20,'item296':20,'item292':20,'coin':680,},
                                      128:{'item965':50,'item969':50,'item296':50,'item292':50,'coin':1280,},
                                      328:{'item965':120,'item969':120,'item296':120,'item292':120,'coin':3280,},
                                      648:{'item965':300,'item969':300,'item296':300,'item292':300,'coin':6480,},
                                      998:{'item965':450,'item969':450,'item296':450,'item292':450,'coin':10000,},
                                      1998:{'item965':900,'item969':900,'item296':900,'item292':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o101':{      #英雄技能
                        
                         'days':[201,202],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item961':20,'item248':20,'item246':20,'item244':20,'coin':680,},
                                      128:{'item961':50,'item248':50,'item246':50,'item244':50,'coin':1280,},
                                      328:{'item961':120,'item248':120,'item246':120,'item244':120,'coin':3280,},
                                      648:{'item961':300,'item248':300,'item246':300,'item244':300,'coin':6480,},
                                      998:{'item961':450,'item248':450,'item246':450,'item244':450,'coin':10000,},
                                      1998:{'item961':900,'item248':900,'item246':900,'item244':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o102':{      #魂玉
                        
                         'days':[203,204],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item250':20,'item252':20,'item254':20,'item275':20,'coin':680,},
                                      128:{'item250':50,'item252':50,'item254':50,'item275':50,'coin':1280,},
                                      328:{'item250':120,'item252':120,'item254':120,'item275':120,'coin':3280,},
                                      648:{'item250':300,'item252':300,'item254':300,'item275':300,'coin':6480,},
                                      998:{'item250':450,'item252':450,'item254':450,'item275':450,'coin':10000,},
                                      1998:{'item250':900,'item252':900,'item254':900,'item275':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o103':{      #金色天
                        
                         'days':[205,206],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item1183':1,'item1182':1,'item1170':2,'item1169':1,'coin':680,},
                                      128:{'item1183':2,'item1182':2,'item1170':3,'item1169':2,'coin':1280,},
                                      328:{'item1184':1,'item1183':1,'item1171':2,'item1170':1,'coin':3280,},
                                      648:{'item1198':1,'item1199':1,'item1205':2,'item1172':1,'coin':6480,},
                                      998:{'item1200':1,'item1201':1,'item1205':3,'item1204':2,'coin':10000,},
                                      1998:{'item1204':3,'item1205':3,'item1207':3,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o104':{      #切割蓄势
                        
                         'days':[207,208],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item1250':2,'item1251':2,'item1252':2,'item1249':2,'coin':680,},
                                      128:{'item1250':4,'item1251':4,'item1252':4,'item1249':4,'coin':1280,},
                                      328:{'item1254':2,'item1255':2,'item1256':2,'item1253':2,'coin':3280,},
                                      648:{'item1254':4,'item1255':4,'item1256':4,'item1253':4,'coin':6480,},
                                      998:{'item1254':6,'item1255':6,'item1256':6,'item1253':6,'coin':10000,},
                                      1998:{'item1254':10,'item1255':10,'item1256':10,'item1253':10,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o105':{      #博闻推演
                        
                         'days':[209,210],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item251':20,'item253':20,'item254':20,'item276':20,'coin':680,},
                                      128:{'item251':50,'item253':50,'item254':50,'item276':50,'coin':1280,},
                                      328:{'item251':120,'item253':120,'item254':120,'item276':120,'coin':3280,},
                                      648:{'item251':300,'item253':300,'item254':300,'item276':300,'coin':6480,},
                                      998:{'item251':450,'item253':450,'item254':450,'item276':450,'coin':10000,},
                                      1998:{'item251':900,'item253':900,'item254':900,'item276':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },


     'o106':{      #圣甲
                        
                         'days':[211,212],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item247':20,'item960':20,'item245':20,'item239':20,'coin':680,},
                                      128:{'item247':50,'item960':50,'item245':50,'item239':50,'coin':1280,},
                                      328:{'item247':120,'item960':120,'item245':120,'item239':120,'coin':3280,},
                                      648:{'item247':300,'item960':300,'item245':300,'item239':300,'coin':6480,},
                                      998:{'item247':450,'item960':450,'item245':450,'item239':450,'coin':10000,},
                                      1998:{'item247':900,'item960':900,'item245':900,'item239':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o107':{      #
                        
                         'days':[213,214],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item962':20,'item966':20,'item293':20,'item206':20,'coin':680,},
                                      128:{'item962':50,'item966':50,'item293':50,'item206':50,'coin':1280,},
                                      328:{'item962':120,'item966':120,'item293':120,'item206':120,'coin':3280,},
                                      648:{'item962':300,'item966':300,'item293':300,'item206':300,'coin':6480,},
                                      998:{'item962':450,'item966':450,'item293':450,'item206':450,'coin':10000,},
                                      1998:{'item962':900,'item966':900,'item293':900,'item206':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o108':{      #
                        
                         'days':[215,216],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item963':20,'item967':20,'item294':20,'item211':20,'coin':680,},
                                      128:{'item963':50,'item967':50,'item294':50,'item211':50,'coin':1280,},
                                      328:{'item963':120,'item967':120,'item294':120,'item211':120,'coin':3280,},
                                      648:{'item963':300,'item967':300,'item294':300,'item211':300,'coin':6480,},
                                      998:{'item963':450,'item967':450,'item294':450,'item211':450,'coin':10000,},
                                      1998:{'item963':900,'item967':900,'item294':900,'item211':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o109':{      #
                        
                         'days':[217,218],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item964':20,'item968':20,'item295':20,'item291':20,'coin':680,},
                                      128:{'item964':50,'item968':50,'item295':50,'item291':50,'coin':1280,},
                                      328:{'item964':120,'item968':120,'item295':120,'item291':120,'coin':3280,},
                                      648:{'item964':300,'item968':300,'item295':300,'item291':300,'coin':6480,},
                                      998:{'item964':450,'item968':450,'item295':450,'item291':450,'coin':10000,},
                                      1998:{'item964':900,'item968':900,'item295':900,'item291':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o110':{      #
                        
                         'days':[219,220],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item965':20,'item969':20,'item296':20,'item292':20,'coin':680,},
                                      128:{'item965':50,'item969':50,'item296':50,'item292':50,'coin':1280,},
                                      328:{'item965':120,'item969':120,'item296':120,'item292':120,'coin':3280,},
                                      648:{'item965':300,'item969':300,'item296':300,'item292':300,'coin':6480,},
                                      998:{'item965':450,'item969':450,'item296':450,'item292':450,'coin':10000,},
                                      1998:{'item965':900,'item969':900,'item296':900,'item292':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o111':{      #英雄技能
                        
                         'days':[221,222],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item961':20,'item248':20,'item246':20,'item244':20,'coin':680,},
                                      128:{'item961':50,'item248':50,'item246':50,'item244':50,'coin':1280,},
                                      328:{'item961':120,'item248':120,'item246':120,'item244':120,'coin':3280,},
                                      648:{'item961':300,'item248':300,'item246':300,'item244':300,'coin':6480,},
                                      998:{'item961':450,'item248':450,'item246':450,'item244':450,'coin':10000,},
                                      1998:{'item961':900,'item248':900,'item246':900,'item244':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o112':{      #魂玉
                        
                         'days':[223,224],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item250':20,'item252':20,'item254':20,'item275':20,'coin':680,},
                                      128:{'item250':50,'item252':50,'item254':50,'item275':50,'coin':1280,},
                                      328:{'item250':120,'item252':120,'item254':120,'item275':120,'coin':3280,},
                                      648:{'item250':300,'item252':300,'item254':300,'item275':300,'coin':6480,},
                                      998:{'item250':450,'item252':450,'item254':450,'item275':450,'coin':10000,},
                                      1998:{'item250':900,'item252':900,'item254':900,'item275':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o113':{      #金色天
                        
                         'days':[225,226],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item1183':1,'item1182':1,'item1170':2,'item1169':1,'coin':680,},
                                      128:{'item1183':2,'item1182':2,'item1170':3,'item1169':2,'coin':1280,},
                                      328:{'item1184':1,'item1183':1,'item1171':2,'item1170':1,'coin':3280,},
                                      648:{'item1198':1,'item1199':1,'item1205':2,'item1172':1,'coin':6480,},
                                      998:{'item1200':1,'item1201':1,'item1205':3,'item1204':2,'coin':10000,},
                                      1998:{'item1204':3,'item1205':3,'item1207':3,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o114':{      #切割蓄势
                        
                         'days':[227,228],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item1250':2,'item1251':2,'item1252':2,'item1249':2,'coin':680,},
                                      128:{'item1250':4,'item1251':4,'item1252':4,'item1249':4,'coin':1280,},
                                      328:{'item1254':2,'item1255':2,'item1256':2,'item1253':2,'coin':3280,},
                                      648:{'item1254':4,'item1255':4,'item1256':4,'item1253':4,'coin':6480,},
                                      998:{'item1254':6,'item1255':6,'item1256':6,'item1253':6,'coin':10000,},
                                      1998:{'item1254':10,'item1255':10,'item1256':10,'item1253':10,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o115':{      #博闻推演
                        
                         'days':[229,230],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item251':20,'item253':20,'item254':20,'item276':20,'coin':680,},
                                      128:{'item251':50,'item253':50,'item254':50,'item276':50,'coin':1280,},
                                      328:{'item251':120,'item253':120,'item254':120,'item276':120,'coin':3280,},
                                      648:{'item251':300,'item253':300,'item254':300,'item276':300,'coin':6480,},
                                      998:{'item251':450,'item253':450,'item254':450,'item276':450,'coin':10000,},
                                      1998:{'item251':900,'item253':900,'item254':900,'item276':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o116':{      #切割蓄势
                        
                         'days':[231,232],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item247':20,'item960':20,'item245':20,'item239':20,'coin':680,},
                                      128:{'item247':50,'item960':50,'item245':50,'item239':50,'coin':1280,},
                                      328:{'item247':120,'item960':120,'item245':120,'item239':120,'coin':3280,},
                                      648:{'item247':300,'item960':300,'item245':300,'item239':300,'coin':6480,},
                                      998:{'item247':450,'item960':450,'item245':450,'item239':450,'coin':10000,},
                                      1998:{'item247':900,'item960':900,'item245':900,'item239':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o117':{      #金色地
                        
                         'days':[233,234],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item962':20,'item966':20,'item293':20,'item206':20,'coin':680,},
                                      128:{'item962':50,'item966':50,'item293':50,'item206':50,'coin':1280,},
                                      328:{'item962':120,'item966':120,'item293':120,'item206':120,'coin':3280,},
                                      648:{'item962':300,'item966':300,'item293':300,'item206':300,'coin':6480,},
                                      998:{'item962':450,'item966':450,'item293':450,'item206':450,'coin':10000,},
                                      1998:{'item962':900,'item966':900,'item293':900,'item206':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o118':{      #破空血瞳
                        
                         'days':[235,236],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item963':20,'item967':20,'item294':20,'item211':20,'coin':680,},
                                      128:{'item963':50,'item967':50,'item294':50,'item211':50,'coin':1280,},
                                      328:{'item963':120,'item967':120,'item294':120,'item211':120,'coin':3280,},
                                      648:{'item963':300,'item967':300,'item294':300,'item211':300,'coin':6480,},
                                      998:{'item963':450,'item967':450,'item294':450,'item211':450,'coin':10000,},
                                      1998:{'item963':900,'item967':900,'item294':900,'item211':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o119':{      #金色人
                        
                         'days':[237,238],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item964':20,'item968':20,'item295':20,'item291':20,'coin':680,},
                                      128:{'item964':50,'item968':50,'item295':50,'item291':50,'coin':1280,},
                                      328:{'item964':120,'item968':120,'item295':120,'item291':120,'coin':3280,},
                                      648:{'item964':300,'item968':300,'item295':300,'item291':300,'coin':6480,},
                                      998:{'item964':450,'item968':450,'item295':450,'item291':450,'coin':10000,},
                                      1998:{'item964':900,'item968':900,'item295':900,'item291':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o120':{      #退避洞鉴
                        
                         'days':[239,240],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item965':20,'item969':20,'item296':20,'item292':20,'coin':680,},
                                      128:{'item965':50,'item969':50,'item296':50,'item292':50,'coin':1280,},
                                      328:{'item965':120,'item969':120,'item296':120,'item292':120,'coin':3280,},
                                      648:{'item965':300,'item969':300,'item296':300,'item292':300,'coin':6480,},
                                      998:{'item965':450,'item969':450,'item296':450,'item292':450,'coin':10000,},
                                      1998:{'item965':900,'item969':900,'item296':900,'item292':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o121':{      #英雄技能
                        
                         'days':[241,242],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item961':20,'item248':20,'item246':20,'item244':20,'coin':680,},
                                      128:{'item961':50,'item248':50,'item246':50,'item244':50,'coin':1280,},
                                      328:{'item961':120,'item248':120,'item246':120,'item244':120,'coin':3280,},
                                      648:{'item961':300,'item248':300,'item246':300,'item244':300,'coin':6480,},
                                      998:{'item961':450,'item248':450,'item246':450,'item244':450,'coin':10000,},
                                      1998:{'item961':900,'item248':900,'item246':900,'item244':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o122':{      #魂玉
                        
                         'days':[243,244],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item250':20,'item252':20,'item254':20,'item275':20,'coin':680,},
                                      128:{'item250':50,'item252':50,'item254':50,'item275':50,'coin':1280,},
                                      328:{'item250':120,'item252':120,'item254':120,'item275':120,'coin':3280,},
                                      648:{'item250':300,'item252':300,'item254':300,'item275':300,'coin':6480,},
                                      998:{'item250':450,'item252':450,'item254':450,'item275':450,'coin':10000,},
                                      1998:{'item250':900,'item252':900,'item254':900,'item275':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o123':{      #金色天
                        
                         'days':[245,246],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item1183':1,'item1182':1,'item1170':2,'item1169':1,'coin':680,},
                                      128:{'item1183':2,'item1182':2,'item1170':3,'item1169':2,'coin':1280,},
                                      328:{'item1184':1,'item1183':1,'item1171':2,'item1170':1,'coin':3280,},
                                      648:{'item1198':1,'item1199':1,'item1205':2,'item1172':1,'coin':6480,},
                                      998:{'item1200':1,'item1201':1,'item1205':3,'item1204':2,'coin':10000,},
                                      1998:{'item1204':3,'item1205':3,'item1207':3,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o124':{      #切割蓄势
                        
                         'days':[247,248],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item1250':2,'item1251':2,'item1252':2,'item1249':2,'coin':680,},
                                      128:{'item1250':4,'item1251':4,'item1252':4,'item1249':4,'coin':1280,},
                                      328:{'item1254':2,'item1255':2,'item1256':2,'item1253':2,'coin':3280,},
                                      648:{'item1254':4,'item1255':4,'item1256':4,'item1253':4,'coin':6480,},
                                      998:{'item1254':6,'item1255':6,'item1256':6,'item1253':6,'coin':10000,},
                                      1998:{'item1254':10,'item1255':10,'item1256':10,'item1253':10,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o125':{      #博闻推演
                        
                         'days':[249,250],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item251':20,'item253':20,'item254':20,'item276':20,'coin':680,},
                                      128:{'item251':50,'item253':50,'item254':50,'item276':50,'coin':1280,},
                                      328:{'item251':120,'item253':120,'item254':120,'item276':120,'coin':3280,},
                                      648:{'item251':300,'item253':300,'item254':300,'item276':300,'coin':6480,},
                                      998:{'item251':450,'item253':450,'item254':450,'item276':450,'coin':10000,},
                                      1998:{'item251':900,'item253':900,'item254':900,'item276':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o126':{      #切割蓄势
                        
                         'days':[251,252],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item247':20,'item960':20,'item245':20,'item239':20,'coin':680,},
                                      128:{'item247':50,'item960':50,'item245':50,'item239':50,'coin':1280,},
                                      328:{'item247':120,'item960':120,'item245':120,'item239':120,'coin':3280,},
                                      648:{'item247':300,'item960':300,'item245':300,'item239':300,'coin':6480,},
                                      998:{'item247':450,'item960':450,'item245':450,'item239':450,'coin':10000,},
                                      1998:{'item247':900,'item960':900,'item245':900,'item239':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o127':{      #金色地
                        
                         'days':[253,254],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item962':20,'item966':20,'item293':20,'item206':20,'coin':680,},
                                      128:{'item962':50,'item966':50,'item293':50,'item206':50,'coin':1280,},
                                      328:{'item962':120,'item966':120,'item293':120,'item206':120,'coin':3280,},
                                      648:{'item962':300,'item966':300,'item293':300,'item206':300,'coin':6480,},
                                      998:{'item962':450,'item966':450,'item293':450,'item206':450,'coin':10000,},
                                      1998:{'item962':900,'item966':900,'item293':900,'item206':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o128':{      #破空血瞳
                        
                         'days':[255,256],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item963':20,'item967':20,'item294':20,'item211':20,'coin':680,},
                                      128:{'item963':50,'item967':50,'item294':50,'item211':50,'coin':1280,},
                                      328:{'item963':120,'item967':120,'item294':120,'item211':120,'coin':3280,},
                                      648:{'item963':300,'item967':300,'item294':300,'item211':300,'coin':6480,},
                                      998:{'item963':450,'item967':450,'item294':450,'item211':450,'coin':10000,},
                                      1998:{'item963':900,'item967':900,'item294':900,'item211':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o129':{      #金色人
                        
                         'days':[257,258],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item964':20,'item968':20,'item295':20,'item291':20,'coin':680,},
                                      128:{'item964':50,'item968':50,'item295':50,'item291':50,'coin':1280,},
                                      328:{'item964':120,'item968':120,'item295':120,'item291':120,'coin':3280,},
                                      648:{'item964':300,'item968':300,'item295':300,'item291':300,'coin':6480,},
                                      998:{'item964':450,'item968':450,'item295':450,'item291':450,'coin':10000,},
                                      1998:{'item964':900,'item968':900,'item295':900,'item291':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o130':{      #退避洞鉴
                        
                         'days':[259,260],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item965':20,'item969':20,'item296':20,'item292':20,'coin':680,},
                                      128:{'item965':50,'item969':50,'item296':50,'item292':50,'coin':1280,},
                                      328:{'item965':120,'item969':120,'item296':120,'item292':120,'coin':3280,},
                                      648:{'item965':300,'item969':300,'item296':300,'item292':300,'coin':6480,},
                                      998:{'item965':450,'item969':450,'item296':450,'item292':450,'coin':10000,},
                                      1998:{'item965':900,'item969':900,'item296':900,'item292':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o131':{      #英雄技能
                        
                         'days':[261,262],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item961':20,'item248':20,'item246':20,'item244':20,'coin':680,},
                                      128:{'item961':50,'item248':50,'item246':50,'item244':50,'coin':1280,},
                                      328:{'item961':120,'item248':120,'item246':120,'item244':120,'coin':3280,},
                                      648:{'item961':300,'item248':300,'item246':300,'item244':300,'coin':6480,},
                                      998:{'item961':450,'item248':450,'item246':450,'item244':450,'coin':10000,},
                                      1998:{'item961':900,'item248':900,'item246':900,'item244':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o132':{      #魂玉
                        
                         'days':[263,264],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item250':20,'item252':20,'item254':20,'item275':20,'coin':680,},
                                      128:{'item250':50,'item252':50,'item254':50,'item275':50,'coin':1280,},
                                      328:{'item250':120,'item252':120,'item254':120,'item275':120,'coin':3280,},
                                      648:{'item250':300,'item252':300,'item254':300,'item275':300,'coin':6480,},
                                      998:{'item250':450,'item252':450,'item254':450,'item275':450,'coin':10000,},
                                      1998:{'item250':900,'item252':900,'item254':900,'item275':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o133':{      #金色天
                        
                         'days':[265,266],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item1183':1,'item1182':1,'item1170':2,'item1169':1,'coin':680,},
                                      128:{'item1183':2,'item1182':2,'item1170':3,'item1169':2,'coin':1280,},
                                      328:{'item1184':1,'item1183':1,'item1171':2,'item1170':1,'coin':3280,},
                                      648:{'item1198':1,'item1199':1,'item1205':2,'item1172':1,'coin':6480,},
                                      998:{'item1200':1,'item1201':1,'item1205':3,'item1204':2,'coin':10000,},
                                      1998:{'item1204':3,'item1205':3,'item1207':3,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o134':{      #切割蓄势
                        
                         'days':[267,268],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item1250':2,'item1251':2,'item1252':2,'item1249':2,'coin':680,},
                                      128:{'item1250':4,'item1251':4,'item1252':4,'item1249':4,'coin':1280,},
                                      328:{'item1254':2,'item1255':2,'item1256':2,'item1253':2,'coin':3280,},
                                      648:{'item1254':4,'item1255':4,'item1256':4,'item1253':4,'coin':6480,},
                                      998:{'item1254':6,'item1255':6,'item1256':6,'item1253':6,'coin':10000,},
                                      1998:{'item1254':10,'item1255':10,'item1256':10,'item1253':10,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },














     'o135':{      #博闻推演
                        
                         'days':[269,270],'type':'once',
                         'reward':{
                                      6:{'item030':5,'item021':5,'coin':60,},
                                      30:{'item036':5,'item021':10,'item030':10,'coin':100,},
                                      68:{'item251':20,'item253':20,'item254':20,'item276':20,'coin':680,},
                                      128:{'item251':50,'item253':50,'item254':50,'item276':50,'coin':1280,},
                                      328:{'item251':120,'item253':120,'item254':120,'item276':120,'coin':3280,},
                                      648:{'item251':300,'item253':300,'item254':300,'item276':300,'coin':6480,},
                                      998:{'item251':450,'item253':450,'item254':450,'item276':450,'coin':10000,},
                                      1998:{'item251':900,'item253':900,'item254':900,'item276':900,'coin':20000,},
                                     },
                            'character':'hero708',
                            'title':'502005',
                            'tips':'502006',
                            },
























},


###########################累计消费###########################
           'consume':{       
                   'title':'502054',
                   'msg_name': '502001',
                   'msg_info': '502002',
                   'open_days':{'1_2':'C4','3_4':'C4','5_6':'C4','7_8':'C4','9_10':'C4','11_12':'C4','13_14':'C4','15_16':'C4','17_18':'C4','19_20':'C4','21_22':'C4','23_24':'C4','25_26':'C4','27_28':'C4','29_30':'C4','31_32':'C4','33_34':'C4','35_36':'C4','37_38':'C4','39_40':'C4','41_42':'C4','43_44':'C4','45_46':'C4','47_48':'C4','49_50':'C4','51_52':'C4','53_54':'C4','55_56':'C4','57_58':'C4','59_60':'C4','61_62':'C4','63_64':'C4','65_66':'C4','67_68':'C4','69_70':'C4','71_72':'C4','73_74':'C4','75_76':'C4','77_78':'C4','79_80':'C4','81_82':'C4','83_84':'C4','85_86':'C4','87_88':'C4','89_90':'C4','91_92':'C4','93_94':'C4','95_96':'C4','97_98':'C4','99_100':'C4','101_102':'C4','103_104':'C4','105_106':'C4','107_108':'C4','109_110':'C4','111_112':'C4','113_114':'C4','115_116':'C4','117_118':'C4','119_120':'C4','121_122':'C4','123_124':'C4','125_126':'C4','127_128':'C4','129_130':'C4','131_132':'C4','133_134':'C4','135_136':'C4','137_138':'C4','139_140':'C4','141_142':'C4','143_144':'C4','145_146':'C4','147_148':'C4','149_150':'C4','151_152':'C4','153_154':'C4','155_156':'C4','157_158':'C4','159_160':'C4','161_162':'C4','163_164':'C4','165_166':'C4','167_168':'C4','169_170':'C4','171_172':'C4','173_174':'C4','175_176':'C4','177_178':'C4','179_180':'C4','181_182':'C4','183_184':'C4','185_186':'C4','187_188':'C4','189_190':'C4','191_192':'C4','193_194':'C4','195_196':'C4','197_198':'C4','199_200':'C4','201_202':'C4','203_204':'C4','205_206':'C4','207_208':'C4','209_210':'C4','211_212':'C4','213_214':'C4','215_216':'C4','217_218':'C4','219_220':'C4','221_222':'C4','223_224':'C4','225_226':'C4','227_228':'C4','229_230':'C4','231_232':'C4','233_234':'C4','235_236':'C4','237_238':'C4','239_240':'C4','241_242':'C4','243_244':'C4','245_246':'C4','247_248':'C4','249_250':'C4','251_252':'C4','253_254':'C4','255_256':'C4','257_258':'C4','259_260':'C4','261_262':'C4','263_264':'C4','265_266':'C4','267_268':'C4','269_270':'C4','271_272':'C4','273_274':'C4','275_276':'C4','277_278':'C4','279_280':'C4','281_282':'C4','283_284':'C4','285_286':'C4','287_288':'C4','289_290':'C4','291_292':'C4','293_294':'C4','295_296':'C4','297_298':'C4','299_300':'C4','301_302':'C4','303_304':'C4','305_306':'C4','307_308':'C4','309_310':'C4','311_312':'C4','313_314':'C4','315_316':'C4','317_318':'C4','319_320':'C4','321_322':'C4','323_324':'C4','325_326':'C4','327_328':'C4','329_330':'C4','331_332':'C4','333_334':'C4','335_336':'C4','337_338':'C4','339_340':'C4','341_342':'C4','343_344':'C4','345_346':'C4','347_348':'C4','349_350':'C4','351_352':'C4','353_354':'C4','355_356':'C4','357_358':'C4','359_360':'C4','361_362':'C4','363_364':'C4','365_366':'C4','367_368':'C4','369_370':'C4','371_372':'C4','373_374':'C4','375_376':'C4','377_378':'C4','379_380':'C4','381_382':'C4','383_384':'C4','385_386':'C4','387_388':'C4','389_390':'C4','391_392':'C4','393_394':'C4','395_396':'C4','397_398':'C4','399_400':'C4','401_402':'C4','403_404':'C4','405_406':'C4','407_408':'C4','409_410':'C4','411_412':'C4','413_414':'C4','415_416':'C4','417_418':'C4','419_420':'C4','421_422':'C4','423_424':'C4','425_426':'C4','427_428':'C4','429_430':'C4','431_432':'C4','433_434':'C4','435_436':'C4','437_438':'C4','439_440':'C4','441_442':'C4',},
                        
         'C1':{      #第一期
                        
                         'character':'hero703',
                         'title':'502010',
                         'tips':'502011',
                         'reward':{
                                      300:{'food':30000,'item037':5,},
                                      800:{'food':50000,'item037':10,},
                                      1500:{'food':70000,'item037':15,'item101':1,},
                                      3000:{'food':100000,'item032':10,'item037':20,},
                                      5000:{'gold':75000,'food':150000,'item037':25,'item104':1,},
                                      10000:{'gold':100000,'food':200000,'wood':200000,'iron':200000,'item021':20,},
                                      20000:{'gold':150000,'food':300000,'wood':300000,'iron':300000,'item107':70,},
                                      
                                     },

                            },     
         'C2':{      #第二期
                        
                         'character':'hero703',
                         'title':'502010',
                         'tips':'502011',
                         'reward':{
                                      300:{'food':50000,'item037':10,},
                                      800:{'food':70000,'item037':20,},
                                      1500:{'food':100000,'item037':30,'item101':1,},
                                      3000:{'food':150000,'item032':10,'item037':40,},
                                      5000:{'gold':100000,'food':200000,'item037':25,'item104':1,},
                                      10000:{'gold':150000,'food':300000,'wood':200000,'iron':200000,'item107':50,},
                                      20000:{'gold':200000,'food':400000,'wood':300000,'iron':300000,'item107':70,},
                                      30000:{'gold':250000,'food':500000,'wood':500000,'iron':500000,'item107':100,},
                                     },

                            },     
         'C3':{      #第三期
                        
                         'character':'hero703',
                         'title':'502010',
                         'tips':'502011',
                         'reward':{
                                      300:{'food':100000,'item037':20,},
                                      800:{'food':150000,'item037':30,},
                                      1500:{'food':200000,'item037':40,'item104':1,},
                                      3000:{'food':250000,'item032':20,'item037':50,},
                                      5000:{'gold':150000,'food':300000,'item037':60,'item104':2,},
                                      10000:{'gold':200000,'food':400000,'wood':400000,'iron':400000,'item107':100,},
                                      20000:{'gold':250000,'food':500000,'wood':500000,'iron':500000,'item107':150,},
                                      30000:{'gold':300000,'food':600000,'wood':600000,'iron':600000,'item107':200,},
                                     },

                            },     
         'C4':{      #第四期
                        
                         'character':'hero703',
                         'title':'502010',
                         'tips':'502011',
                         'reward':{
                                      300:{'gold':75000,'food':50000,'item130':50,},
                                      800:{'gold':100000,'food':100000,'item113':50,},
                                      1500:{'gold':125000,'food':150000,'item107':50,},
                                      3000:{'gold':150000,'food':200000,'item130':100,},
                                      5000:{'gold':200000,'food':250000,'item113':100,},
                                      10000:{'gold':300000,'food':300000,'wood':100000,'iron':100000,'item107':100,},
                                      20000:{'gold':400000,'food':350000,'wood':200000,'iron':200000,'item113':200,},
                                      30000:{'gold':500000,'food':400000,'wood':300000,'iron':300000,'item107':300,},
                                     },

                            },},





##############################惊喜礼包#########################################

        'surprise_gift':{
                         'active_time':[[10,00],[22,00]],
                         'openday':{'1_3':'goods1','5_7':'goods2','9_11':'goods2','13_15':'goods3','17_19':'goods4','21_23':'goods5','25_27':'goods5','29_31':'goods5','33_35':'goods6','37_39':'goods6','41_43':'goods6','45_47':'goods7','49_51':'goods7','53_55':'goods7','57_59':'goods7','61_63':'goods7','65_67':'goods7','69_71':'goods7','73_75':'goods7','77_79':'goods7','81_83':'goods7','85_87':'goods7','89_91':'goods7','93_95':'goods7','97_99':'goods7','101_103':'goods7','105_107':'goods7','109_111':'goods7','113_115':'goods7','117_119':'goods7','121_123':'goods7','125_127':'goods7','129_131':'goods7','133_135':'goods7','137_139':'goods7','141_143':'goods7','145_147':'goods7','149_151':'goods7','153_155':'goods7','157_159':'goods7','161_163':'goods7','165_167':'goods7','169_171':'goods7','173_175':'goods7','177_179':'goods7','181_183':'goods7','185_187':'goods7','189_191':'goods7','193_195':'goods7','197_199':'goods7','201_203':'goods7','205_207':'goods7','209_211':'goods7','213_215':'goods7','217_219':'goods7','221_223':'goods7','225_227':'goods7','229_231':'goods7','233_235':'goods7','237_239':'goods7',},

                         'goods1':{
                                    'gd1170':{ # 需要充值的人民币
                                       'name':'surprise_55', # 礼包名字
                                       'num':1, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':5000,
                                       'reward':{'item2003':50,},
                                      },
                                    'gd1171':{ # 需要充值的人民币
                                       'name':'surprise_56', # 礼包名字
                                       'num':5, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':8000,
                                       'reward':{'item2003':80,},
                                      },
                                    'gd1172':{ # 需要充值的人民币
                                       'name':'surprise_57', # 礼包名字
                                       'num':2, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':22000,
                                       'reward':{'item2003':120,'item2007':20,},
                                      },
                                    
                                    
                         },
                         'goods2':{
                                    'gd1170':{ # 需要充值的人民币
                                       'name':'surprise_55', # 礼包名字
                                       'num':1, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':5000,
                                       'reward':{'item2003':50,},
                                      },
                                    'gd1171':{ # 需要充值的人民币
                                       'name':'surprise_56', # 礼包名字
                                       'num':5, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':8000,
                                       'reward':{'item2003':80,},
                                      },
                                    'gd1172':{ # 需要充值的人民币
                                       'name':'surprise_57', # 礼包名字
                                       'num':2, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':22000,
                                       'reward':{'item2003':120,'item2007':20,},
                                      },
                                    'gd1173':{ # 需要充值的人民币
                                       'name':'surprise_58', # 礼包名字
                                       'num':2, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':50000,
                                       'reward':{'item2003':300,'item2007':40,},
                                      },
                                    
                                    
                                    
                         },
                         'goods3':{
                                    'gd1170':{ # 需要充值的人民币
                                       'name':'surprise_55', # 礼包名字
                                       'num':1, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':5000,
                                       'reward':{'item2003':50,},
                                      },
                                    'gd1171':{ # 需要充值的人民币
                                       'name':'surprise_56', # 礼包名字
                                       'num':5, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':8000,
                                       'reward':{'item2003':80,},
                                      },
                                    'gd1172':{ # 需要充值的人民币
                                       'name':'surprise_57', # 礼包名字
                                       'num':5, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':22000,
                                       'reward':{'item2003':120,'item2007':20,},
                                      },
                                    'gd1173':{ # 需要充值的人民币
                                       'name':'surprise_58', # 礼包名字
                                       'num':3, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':50000,
                                       'reward':{'item2003':300,'item2007':40,},
                                      },
                                    
                         },
                         'goods4':{
                                    'gd1170':{ # 需要充值的人民币
                                       'name':'surprise_55', # 礼包名字
                                       'num':1, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':5000,
                                       'reward':{'item2003':50,},
                                      },
                                    'gd1171':{ # 需要充值的人民币
                                       'name':'surprise_56', # 礼包名字
                                       'num':5, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':8000,
                                       'reward':{'item2003':80,},
                                      },
                                    'gd1172':{ # 需要充值的人民币
                                       'name':'surprise_57', # 礼包名字
                                       'num':5, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':22000,
                                       'reward':{'item2003':120,'item2007':20,},
                                      },
                                    'gd1173':{ # 需要充值的人民币
                                       'name':'surprise_58', # 礼包名字
                                       'num':1, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':50000,
                                       'reward':{'item2003':300,'item2007':40,},
                                      },
                                    'gd1174':{ # 需要充值的人民币
                                       'name':'surprise_59', # 礼包名字
                                       'num':1, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':50000,
                                       'reward':{'item2003':200,'item2007':20,'item2004':10,},
                                      },
                                    'gd1175':{ # 需要充值的人民币
                                       'name':'surprise_60', # 礼包名字
                                       'num':1, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':50000,
                                       'reward':{'item2003':200,'item2007':20,'item2005':10,},
                                      },
                                    'gd1176':{ # 需要充值的人民币
                                       'name':'surprise_61', # 礼包名字
                                       'num':1, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':50000,
                                       'reward':{'item2003':200,'item2007':20,'item2006':10,},
                                      },
                         },
                         'goods5':{
                                    'gd1170':{ # 需要充值的人民币
                                       'name':'surprise_55', # 礼包名字
                                       'num':1, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':5000,
                                       'reward':{'item2003':50,},
                                      },
                                    'gd1171':{ # 需要充值的人民币
                                       'name':'surprise_56', # 礼包名字
                                       'num':5, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':8000,
                                       'reward':{'item2003':80,},
                                      },
                                    'gd1172':{ # 需要充值的人民币
                                       'name':'surprise_57', # 礼包名字
                                       'num':5, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':22000,
                                       'reward':{'item2003':120,'item2007':20,},
                                      },
                                    'gd1173':{ # 需要充值的人民币
                                       'name':'surprise_58', # 礼包名字
                                       'num':3, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':50000,
                                       'reward':{'item2003':300,'item2007':40,},
                                      },
                                    'gd1174':{ # 需要充值的人民币
                                       'name':'surprise_59', # 礼包名字
                                       'num':1, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':50000,
                                       'reward':{'item2003':200,'item2007':20,'item2004':10,},
                                      },
                                    'gd1175':{ # 需要充值的人民币
                                       'name':'surprise_60', # 礼包名字
                                       'num':1, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':50000,
                                       'reward':{'item2003':200,'item2007':20,'item2005':10,},
                                      },
                                    'gd1176':{ # 需要充值的人民币
                                       'name':'surprise_61', # 礼包名字
                                       'num':1, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':50000,
                                       'reward':{'item2003':200,'item2007':20,'item2006':10,},
                                      },
                         },
                         'goods6':{
                                    'gd1170':{ # 需要充值的人民币
                                       'name':'surprise_55', # 礼包名字
                                       'num':1, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':5000,
                                       'reward':{'item2003':50,},
                                      },
                                    'gd1171':{ # 需要充值的人民币
                                       'name':'surprise_56', # 礼包名字
                                       'num':5, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':8000,
                                       'reward':{'item2003':80,},
                                      },
                                    'gd1172':{ # 需要充值的人民币
                                       'name':'surprise_57', # 礼包名字
                                       'num':5, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':22000,
                                       'reward':{'item2003':120,'item2007':20,},
                                      },
                                    'gd1173':{ # 需要充值的人民币
                                       'name':'surprise_58', # 礼包名字
                                       'num':3, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':50000,
                                       'reward':{'item2003':300,'item2007':40,},
                                      },
                                    'gd1174':{ # 需要充值的人民币
                                       'name':'surprise_59', # 礼包名字
                                       'num':2, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':50000,
                                       'reward':{'item2003':200,'item2007':20,'item2004':10,},
                                      },
                                    'gd1175':{ # 需要充值的人民币
                                       'name':'surprise_60', # 礼包名字
                                       'num':2, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':50000,
                                       'reward':{'item2003':200,'item2007':20,'item2005':10,},
                                      },
                                    'gd1176':{ # 需要充值的人民币
                                       'name':'surprise_61', # 礼包名字
                                       'num':2, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':50000,
                                       'reward':{'item2003':200,'item2007':20,'item2006':10,},
                                      },
                         },
                         'goods7':{
                                    'gd1170':{ # 需要充值的人民币
                                       'name':'surprise_55', # 礼包名字
                                       'num':1, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':5000,
                                       'reward':{'item2003':50,},
                                      },
                                    'gd1171':{ # 需要充值的人民币
                                       'name':'surprise_56', # 礼包名字
                                       'num':5, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':8000,
                                       'reward':{'item2003':80,},
                                      },
                                    'gd1172':{ # 需要充值的人民币
                                       'name':'surprise_57', # 礼包名字
                                       'num':5, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':22000,
                                       'reward':{'item2003':120,'item2007':20,},
                                      },
                                    'gd1173':{ # 需要充值的人民币
                                       'name':'surprise_58', # 礼包名字
                                       'num':3, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':50000,
                                       'reward':{'item2003':300,'item2007':40,},
                                      },
                                    'gd1174':{ # 需要充值的人民币
                                       'name':'surprise_59', # 礼包名字
                                       'num':2, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':50000,
                                       'reward':{'item2003':200,'item2007':20,'item2004':10,},
                                      },
                                    'gd1175':{ # 需要充值的人民币
                                       'name':'surprise_60', # 礼包名字
                                       'num':2, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':50000,
                                       'reward':{'item2003':200,'item2007':20,'item2005':10,},
                                      },
                                    'gd1176':{ # 需要充值的人民币
                                       'name':'surprise_61', # 礼包名字
                                       'num':2, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':50000,
                                       'reward':{'item2003':200,'item2007':20,'item2006':10,},
                                      },
                                    'gd1177':{ # 需要充值的人民币
                                       'name':'surprise_62', # 礼包名字
                                       'num':1, # 可购买次数
                                       'exclusion':[],
                                       'worth_gold':200000,
                                       'reward':{'item2003':500,'item2004':25,'item2005':25,'item2006':25,},
                                      },

                         },
        },






         'exchange_shop':{    #兑换商店
        'name':100000,
        'sort_by':['item042','item043','item044','item045','item195','item176','item1211','item1270','item2014'],#排序规则，使用货币道具ID
        'consume_items':['item042','item043','item044','item045','item195','item176','item1211','item1270','item2014'],# 每个商店中消耗的货币
           'begin_time':[
 {'time':[1,2],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[3,4],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[5,6],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[7,8],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[9,10],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[11,12],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[13,14],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[15,16],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[17,18],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[19,20],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[21,22],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[23,24],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[25,26],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[27,28],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[29,30],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[31,32],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[33,34],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[35,36],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[37,38],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[39,40],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[41,42],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[43,44],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[45,46],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[47,48],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[49,50],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[51,52],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[53,54],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[55,56],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[57,58],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[59,60],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[61,62],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[63,64],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[65,66],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[67,68],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[69,70],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[71,72],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[73,74],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[75,76],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[77,78],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[79,80],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[81,82],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[83,84],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[85,86],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[87,88],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[89,90],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[91,92],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[93,94],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[95,96],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[97,98],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[99,100],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[101,102],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[103,104],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[105,106],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[107,108],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[109,110],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[111,112],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[113,114],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[115,116],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[117,118],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[119,120],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[121,122],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[123,124],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[125,126],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[127,128],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[129,130],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[131,132],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[133,134],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[135,136],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[137,138],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[139,140],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[141,142],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[143,144],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[145,146],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[147,148],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[149,150],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[151,152],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[153,154],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[155,156],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[157,158],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[159,160],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[161,162],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[163,164],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[165,166],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[167,168],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[169,170],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[171,172],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[173,174],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[175,176],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[177,178],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[179,180],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[181,182],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[183,184],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[185,186],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[187,188],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[189,190],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[191,192],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[193,194],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[195,196],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[197,198],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[199,200],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[201,202],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[203,204],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[205,206],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[207,208],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[209,210],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[211,212],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[213,214],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[215,216],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[217,218],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[219,220],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[221,222],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[223,224],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[225,226],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[227,228],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[229,230],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[231,232],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[233,234],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[235,236],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[237,238],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[239,240],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[241,242],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[243,244],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[245,246],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[247,248],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[249,250],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[251,252],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[253,254],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[255,256],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[257,258],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[259,260],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[261,262],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[263,264],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[265,266],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[267,268],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[269,270],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[271,272],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[273,274],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[275,276],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[277,278],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[279,280],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[281,282],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[283,284],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[285,286],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[287,288],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[289,290],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[291,292],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[293,294],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[295,296],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[297,298],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[299,300],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[301,302],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[303,304],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[305,306],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[307,308],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[309,310],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[311,312],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[313,314],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[315,316],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[317,318],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[319,320],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[321,322],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[323,324],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[325,326],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[327,328],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[329,330],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[331,332],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[333,334],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[335,336],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[337,338],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[339,340],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[341,342],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[343,344],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[345,346],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[347,348],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[349,350],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[351,352],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[353,354],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[355,356],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[357,358],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[359,360],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[361,362],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[363,364],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[365,366],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[367,368],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[369,370],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[371,372],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[373,374],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[375,376],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[377,378],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[379,380],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[381,382],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[383,384],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[385,386],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[387,388],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[389,390],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[391,392],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[393,394],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[395,396],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[397,398],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[399,400],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
 {'time':[401,402],'goods':['goods10','goods2','goods2','goods1','goods1','goods1','goods1','goods1','goods1']},
], #开服第几天，1=开启天数，2=结束天数 （eg：偏移值5点，1号5点开服, 'days':[1, 2] 活动入口会在3号5点关闭,[青龙qinglong_shop，白虎baihu_shop,朱雀zhuque_shop,玄武xuanwu_shop]从这四个商店里取goods，当数组里该位置为null时，商店分页显示为未开启

     'shoplists':[
        {   #五铢商店

         'goods1':[
         {
           'reward':{'item412':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item056':20},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item058':80},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item602':2},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item605':4},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item272':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item241':10},
           'price':10,
           'limit':60,
              },











          ],
         'goods2':[
         {
           'reward':{'item412':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item056':20},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item058':80},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item602':2},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item605':4},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item272':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item241':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item273':10},
           'price':10,
           'limit':60,
              },










          ],
         'goods3':[
         {
           'reward':{'item412':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item419':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item056':20},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item058':80},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item602':2},
           'price':10,
           'limit':60,
              },

         {
           'reward':{'item272':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item241':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item273':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item242':10},
           'price':10,
           'limit':60,
              },








          ],
         'goods4':[
         {
           'reward':{'item412':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item419':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item420':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item056':20},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item058':80},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item602':2},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item605':4},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item272':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item241':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item273':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item242':10},
           'price':10,
           'limit':60,
              },







          ],
         'goods5':[
         {
           'reward':{'item412':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item056':20},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item058':80},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item602':2},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item605':4},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item272':10},
           'price':10,
           'limit':60,
              },












          ],
         'goods6':[
         {
           'reward':{'item412':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item419':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item056':20},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item058':80},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item602':2},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item605':4},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item272':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item241':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item273':10},
           'price':10,
           'limit':60,
              },









          ],
         'goods7':[
         {
           'reward':{'item412':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item419':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item420':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item056':20},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item058':80},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item602':2},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item605':4},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item272':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item241':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item273':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item242':10},
           'price':10,
           'limit':60,
              },







          ],
         'goods8':[
         {
           'reward':{'item412':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item419':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item420':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item426':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item056':20},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item058':80},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item602':2},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item605':4},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item272':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item241':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item273':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item242':10},
           'price':10,
           'limit':60,
              },






          ],
         'goods9':[
         {
           'reward':{'item412':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item419':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item420':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item426':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item056':20},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item058':80},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item602':2},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item605':4},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item272':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item241':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item273':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item242':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item1064':1},
           'price':600,
           'limit':10,
              },





          ],
         'goods10':[
         {
           'reward':{'item1057':1},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item1058':1},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item1059':1},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item1060':1},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item056':20},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item058':80},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item602':2},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item605':4},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item272':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item241':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item273':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item242':10},
           'price':10,
           'limit':60,
              },
         {
           'reward':{'item1064':1},
           'price':600,
           'limit':10,
              },





          ],
},

{   #武帝商店

 'goods1':[
        {
           'reward':{'equip':['equip036']},
           'price':30,
           'limit':1,
              },
        {
           'reward':{'equip':['equip039']},
           'price':200,
           'limit':1,
              },
        {
           'reward':{'equip':['equip050']},
           'price':300,
           'limit':1,
              },
        {
           'reward':{'equip':['equip054']},
           'price':400,
           'limit':1,
              },
        {
           'reward':{'equip':['equip047']},
           'price':500,
           'limit':1,
              },
        {
           'reward':{'item427':10},
           'price':15,
           'limit':50,
              },
        {
           'reward':{'item427':200},
           'price':300,
           'limit':20,
              },
        {
           'reward':{'item602':3},
           'price':10,
           'limit':60,
              },
        {
           'reward':{'item605':6},
           'price':10,
           'limit':60,
              },






          ],
 'goods2':[
        {
           'reward':{'equip':['equip036']},
           'price':50,
           'limit':1,
              },
        {
           'reward':{'equip':['equip039']},
           'price':400,
           'limit':1,
              },
        {
           'reward':{'equip':['equip050']},
           'price':600,
           'limit':1,
              },
        {
           'reward':{'equip':['equip054']},
           'price':800,
           'limit':1,
              },
        {
           'reward':{'equip':['equip047']},
           'price':1000,
           'limit':1,
              },
        {
           'reward':{'item427':10},
           'price':15,
           'limit':50,
              },
        {
           'reward':{'item427':200},
           'price':300,
           'limit':20,
              },
        {
           'reward':{'item042':10},
           'price':10,
           'limit':50,
              },







          ],
 'goods3':[
        {
           'reward':{'equip':['equip036']},
           'price':50,
           'limit':1,
              },
        {
           'reward':{'equip':['equip039']},
           'price':400,
           'limit':1,
              },
        {
           'reward':{'equip':['equip050']},
           'price':600,
           'limit':1,
              },
        {
           'reward':{'equip':['equip054']},
           'price':800,
           'limit':1,
              },
        {
           'reward':{'equip':['equip047']},
           'price':1000,
           'limit':1,
              },
        {
           'reward':{'item427':10},
           'price':15,
           'limit':50,
              },
        {
           'reward':{'item427':200},
           'price':300,
           'limit':20,
              },
        {
           'reward':{'item042':10},
           'price':10,
           'limit':50,
              },







          ],
},

{   #天罚商店

 'goods1':[
        {
           'reward':{'item413':5},
           'price':8,
           'limit':120,
              },
        {
           'reward':{'item414':5},
           'price':10,
           'limit':120,
              },
        {
           'reward':{'item415':5},
           'price':12,
           'limit':120,
              },
        {
           'reward':{'item418':5},
           'price':15,
           'limit':120,
              },
        {
           'reward':{'item421':5},
           'price':20,
           'limit':120,
              },
        {
           'reward':{'item059':200},
           'price':10,
           'limit':60,
              },
        {
           'reward':{'item076':10},
           'price':10,
           'limit':60,
              },



          ],
 'goods2':[
        {
           'reward':{'item413':5},
           'price':8,
           'limit':120,
              },
        {
           'reward':{'item414':5},
           'price':10,
           'limit':120,
              },
        {
           'reward':{'item415':5},
           'price':12,
           'limit':120,
              },
        {
           'reward':{'item418':5},
           'price':15,
           'limit':120,
              },
        {
           'reward':{'item421':5},
           'price':20,
           'limit':120,
              },
        {
           'reward':{'item042':10},
           'price':10,
           'limit':50,
              },




          ],
 'goods3':[
        {
           'reward':{'item413':5},
           'price':8,
           'limit':120,
              },
        {
           'reward':{'item414':5},
           'price':10,
           'limit':120,
              },
        {
           'reward':{'item415':5},
           'price':12,
           'limit':120,
              },
        {
           'reward':{'item418':5},
           'price':15,
           'limit':120,
              },
        {
           'reward':{'item421':5},
           'price':20,
           'limit':120,
              },
        {
           'reward':{'item042':10},
           'price':10,
           'limit':50,
              },




          ],
},

{   #斗神商店

 'goods1':[
        {
           'reward':{'equip':['equip059']},
           'price':50,
           'limit':1,
              },
        {
           'reward':{'equip':['equip060']},
           'price':400,
           'limit':1,
              },
        {
           'reward':{'equip':['equip061']},
           'price':600,
           'limit':1,
              },
        {
           'reward':{'equip':['equip062']},
           'price':800,
           'limit':1,
              },
        {
           'reward':{'equip':['equip063']},
           'price':1000,
           'limit':1,
              },
        {
           'reward':{'item428':10},
           'price':20,
           'limit':50,
              },
        {
           'reward':{'item428':200},
           'price':400,
           'limit':20,
              },
        {
           'reward':{'item042':10},
           'price':10,
           'limit':50,
              },













          ],
 'goods2':[
        {
           'reward':{'equip':['equip059']},
           'price':50,
           'limit':1,
              },
        {
           'reward':{'equip':['equip060']},
           'price':400,
           'limit':1,
              },
        {
           'reward':{'equip':['equip061']},
           'price':600,
           'limit':1,
              },
        {
           'reward':{'equip':['equip062']},
           'price':800,
           'limit':1,
              },
        {
           'reward':{'equip':['equip063']},
           'price':1000,
           'limit':1,
              },
        {
           'reward':{'item428':10},
           'price':20,
           'limit':1,
              },
        {
           'reward':{'item428':200},
           'price':400,
           'limit':1,
              },
        {
           'reward':{'item042':10},
           'price':10,
           'limit':50,
              },













          ],
 'goods3':[
        {
           'reward':{'equip':['equip059']},
           'price':50,
           'limit':1,
              },
        {
           'reward':{'equip':['equip060']},
           'price':400,
           'limit':1,
              },
        {
           'reward':{'equip':['equip061']},
           'price':600,
           'limit':1,
              },
        {
           'reward':{'equip':['equip062']},
           'price':800,
           'limit':1,
              },
        {
           'reward':{'equip':['equip063']},
           'price':1000,
           'limit':1,
              },
        {
           'reward':{'item428':10},
           'price':20,
           'limit':1,
              },
        {
           'reward':{'item428':200},
           'price':400,
           'limit':1,
              },
        {
           'reward':{'item042':10},
           'price':10,
           'limit':50,
              },













          ],
},

{   #羽林商店

 'goods1':[
        {
           'reward':{'equip':['equip070']},
           'price':20,
           'limit':1,
              },
        {
           'reward':{'equip':['equip071']},
           'price':30,
           'limit':1,
              },
        {
           'reward':{'equip':['equip072']},
           'price':40,
           'limit':1,
              },
        {
           'reward':{'equip':['equip073']},
           'price':50,
           'limit':1,
              },
        {
           'reward':{'equip':['equip074']},
           'price':60,
           'limit':1,
              },
        {
           'reward':{'item042':10},
           'price':10,
           'limit':50,
              },




          ],
},

{   #轩辕商店

 'goods1':[
        {
           'reward':{'equip':['equip065']},
           'price':4000,
           'limit':1,
              },
        {
           'reward':{'equip':['equip066']},
           'price':4000,
           'limit':1,
              },
        {
           'reward':{'equip':['equip067']},
           'price':4500,
           'limit':1,
              },
        {
           'reward':{'equip':['equip068']},
           'price':4500,
           'limit':1,
              },
        {
           'reward':{'equip':['equip069']},
           'price':5000,
           'limit':1,
              },
        {
           'reward':{'item434':10},
           'price':200,
           'limit':240,
              },
        {
           'reward':{'item434':200},
           'price':4000,
           'limit':12,
              },
        {
           'reward':{'item435':10},
           'price':300,
           'limit':100,
              },
        {
           'reward':{'item435':200},
           'price':6000,
           'limit':5,
              },







          ],
},

{   #女妖商店

 'goods1':[
        {
           'reward':{'equip':['equip093']},
           'price':500,
           'limit':1,
              },
        {
           'reward':{'equip':['equip094']},
           'price':500,
           'limit':1,
              },
        {
           'reward':{'equip':['equip095']},
           'price':800,
           'limit':1,
              },
        {
           'reward':{'equip':['equip096']},
           'price':800,
           'limit':1,
              },
        {
           'reward':{'equip':['equip097']},
           'price':1000,
           'limit':1,
              },
        {
           'reward':{'item477':10},
           'price':20,
           'limit':200,
              },
        {
           'reward':{'item477':200},
           'price':400,
           'limit':20,
              },
        {
           'reward':{'item042':10},
           'price':10,
           'limit':50,
              },








          ],
},

{   #伏羲商店

 'goods1':[
        {
           'reward':{'equip':['equip108']},
           'price':500,
           'limit':1,
              },
        {
           'reward':{'equip':['equip109']},
           'price':500,
           'limit':1,
              },
        {
           'reward':{'equip':['equip110']},
           'price':800,
           'limit':1,
              },
        {
           'reward':{'equip':['equip111']},
           'price':800,
           'limit':1,
              },
        {
           'reward':{'equip':['equip112']},
           'price':1000,
           'limit':1,
              },
        {
           'reward':{'item490':10},
           'price':20,
           'limit':200,
              },
        {
           'reward':{'item490':200},
           'price':400,
           'limit':20,
              },
        {
           'reward':{'item042':10},
           'price':10,
           'limit':50,
              },








          ],
},

{   #伏羲商店

 'goods1':[
        {
           'reward':{'equip':['equip114']},
           'price':500,
           'limit':1,
              },
        {
           'reward':{'equip':['equip115']},
           'price':500,
           'limit':1,
              },
        {
           'reward':{'equip':['equip116']},
           'price':800,
           'limit':1,
              },
        {
           'reward':{'equip':['equip117']},
           'price':800,
           'limit':1,
              },
        {
           'reward':{'equip':['equip118']},
           'price':1000,
           'limit':1,
              },
        {
           'reward':{'item492':10},
           'price':20,
           'limit':200,
              },
        {
           'reward':{'item492':200},
           'price':400,
           'limit':20,
              },
        {
           'reward':{'item042':10},
           'price':10,
           'limit':50,
              },








          ],
},

],
},
        'pay_choose':{ 
        'name':502062,
        'info':502063,
        'need_pay_coin':500,
        'limit_time':200,
        'miss_reward':['item042',10], #活动结束后，对未领取奖励的玩家进行补偿{补偿物品，数量}，邮件发奖，奖励总数=剩余可领取次数x奖励数量
        'mail_name':'502081',  #邮件标题
        'mail_info':'502082',  #邮件内容
        'begin_time':[ 
 {'time':[1,2],'goods':'goods12'},
 {'time':[3,4],'goods':'goods12'},
 {'time':[5,6],'goods':'goods12'},
 {'time':[7,8],'goods':'goods12'},
 {'time':[9,10],'goods':'goods12'},
 {'time':[11,12],'goods':'goods12'},
 {'time':[13,14],'goods':'goods12'},
 {'time':[15,16],'goods':'goods12'},
 {'time':[17,18],'goods':'goods12'},
 {'time':[19,20],'goods':'goods12'},
 {'time':[21,22],'goods':'goods12'},
 {'time':[23,24],'goods':'goods12'},
 {'time':[25,26],'goods':'goods12'},
 {'time':[27,28],'goods':'goods12'},
 {'time':[29,30],'goods':'goods12'},
 {'time':[31,32],'goods':'goods12'},
 {'time':[33,34],'goods':'goods12'},
 {'time':[35,36],'goods':'goods12'},
 {'time':[37,38],'goods':'goods12'},
 {'time':[39,40],'goods':'goods12'},
 {'time':[41,42],'goods':'goods12'},
 {'time':[43,44],'goods':'goods12'},
 {'time':[45,46],'goods':'goods12'},
 {'time':[47,48],'goods':'goods12'},
 {'time':[49,50],'goods':'goods12'},
 {'time':[51,52],'goods':'goods12'},
 {'time':[53,54],'goods':'goods12'},
 {'time':[55,56],'goods':'goods12'},
 {'time':[57,58],'goods':'goods12'},
 {'time':[59,60],'goods':'goods12'},
 {'time':[61,62],'goods':'goods12'},
 {'time':[63,64],'goods':'goods12'},
 {'time':[65,66],'goods':'goods12'},
 {'time':[67,68],'goods':'goods12'},
 {'time':[69,70],'goods':'goods12'},
 {'time':[71,72],'goods':'goods12'},
 {'time':[73,74],'goods':'goods12'},
 {'time':[75,76],'goods':'goods12'},
 {'time':[77,78],'goods':'goods12'},
 {'time':[79,80],'goods':'goods12'},
 {'time':[81,82],'goods':'goods12'},
 {'time':[83,84],'goods':'goods12'},
 {'time':[85,86],'goods':'goods12'},
 {'time':[87,88],'goods':'goods12'},
 {'time':[89,90],'goods':'goods12'},
 {'time':[91,92],'goods':'goods12'},
 {'time':[93,94],'goods':'goods12'},
 {'time':[95,96],'goods':'goods12'},
 {'time':[97,98],'goods':'goods12'},
 {'time':[99,100],'goods':'goods12'},
 {'time':[101,102],'goods':'goods12'},
 {'time':[103,104],'goods':'goods12'},
 {'time':[105,106],'goods':'goods12'},
 {'time':[107,108],'goods':'goods12'},
 {'time':[109,110],'goods':'goods12'},
 {'time':[111,112],'goods':'goods12'},
 {'time':[113,114],'goods':'goods12'},
 {'time':[115,116],'goods':'goods12'},
 {'time':[117,118],'goods':'goods12'},
 {'time':[119,120],'goods':'goods12'},
 {'time':[121,122],'goods':'goods12'},
 {'time':[123,124],'goods':'goods12'},
 {'time':[125,126],'goods':'goods12'},
 {'time':[127,128],'goods':'goods12'},
 {'time':[129,130],'goods':'goods12'},
 {'time':[131,132],'goods':'goods12'},
 {'time':[133,134],'goods':'goods12'},
 {'time':[135,136],'goods':'goods12'},
 {'time':[137,138],'goods':'goods12'},
 {'time':[139,140],'goods':'goods12'},
 {'time':[141,142],'goods':'goods12'},
 {'time':[143,144],'goods':'goods12'},
 {'time':[145,146],'goods':'goods12'},
 {'time':[147,148],'goods':'goods12'},
 {'time':[149,150],'goods':'goods12'},
 {'time':[151,152],'goods':'goods12'},
 {'time':[153,154],'goods':'goods12'},
 {'time':[155,156],'goods':'goods12'},
 {'time':[157,158],'goods':'goods12'},
 {'time':[159,160],'goods':'goods12'},
 {'time':[161,162],'goods':'goods12'},
 {'time':[163,164],'goods':'goods12'},
 {'time':[165,166],'goods':'goods12'},
 {'time':[167,168],'goods':'goods12'},
 {'time':[169,170],'goods':'goods12'},
 {'time':[171,172],'goods':'goods12'},
 {'time':[173,174],'goods':'goods12'},
 {'time':[175,176],'goods':'goods12'},
 {'time':[177,178],'goods':'goods12'},
 {'time':[179,180],'goods':'goods12'},
 {'time':[181,182],'goods':'goods12'},
 {'time':[183,184],'goods':'goods12'},
 {'time':[185,186],'goods':'goods12'},
 {'time':[187,188],'goods':'goods12'},
 {'time':[189,190],'goods':'goods12'},
 {'time':[191,192],'goods':'goods12'},
 {'time':[193,194],'goods':'goods12'},
 {'time':[195,196],'goods':'goods12'},
 {'time':[197,198],'goods':'goods12'},
 {'time':[199,200],'goods':'goods12'},
 {'time':[201,202],'goods':'goods12'},
 {'time':[203,204],'goods':'goods12'},
 {'time':[205,206],'goods':'goods12'},
 {'time':[207,208],'goods':'goods12'},
 {'time':[209,210],'goods':'goods12'},
 {'time':[211,212],'goods':'goods12'},
 {'time':[213,214],'goods':'goods12'},
 {'time':[215,216],'goods':'goods12'},
 {'time':[217,218],'goods':'goods12'},
 {'time':[219,220],'goods':'goods12'},
 {'time':[221,222],'goods':'goods12'},
 {'time':[223,224],'goods':'goods12'},
 {'time':[225,226],'goods':'goods12'},
 {'time':[227,228],'goods':'goods12'},
 {'time':[229,230],'goods':'goods12'},
 {'time':[231,232],'goods':'goods12'},
 {'time':[233,234],'goods':'goods12'},
 {'time':[235,236],'goods':'goods12'},
 {'time':[237,238],'goods':'goods12'},
 {'time':[239,240],'goods':'goods12'},
 {'time':[241,242],'goods':'goods12'},
 {'time':[243,244],'goods':'goods12'},
 {'time':[245,246],'goods':'goods12'},
 {'time':[247,248],'goods':'goods12'},
 {'time':[249,250],'goods':'goods12'},
 {'time':[251,252],'goods':'goods12'},
 {'time':[253,254],'goods':'goods12'},
 {'time':[255,256],'goods':'goods12'},
 {'time':[257,258],'goods':'goods12'},
 {'time':[259,260],'goods':'goods12'},
 {'time':[261,262],'goods':'goods12'},
 {'time':[263,264],'goods':'goods12'},
 {'time':[265,266],'goods':'goods12'},
 {'time':[267,268],'goods':'goods12'},
 {'time':[269,270],'goods':'goods12'},
 {'time':[271,272],'goods':'goods12'},
 {'time':[273,274],'goods':'goods12'},
 {'time':[275,276],'goods':'goods12'},
 {'time':[277,278],'goods':'goods12'},
 {'time':[279,280],'goods':'goods12'},
 {'time':[281,282],'goods':'goods12'},
 {'time':[283,284],'goods':'goods12'},
 {'time':[285,286],'goods':'goods12'},
 {'time':[287,288],'goods':'goods12'},
 {'time':[289,290],'goods':'goods12'},
 {'time':[291,292],'goods':'goods12'},
 {'time':[293,294],'goods':'goods12'},
 {'time':[295,296],'goods':'goods12'},
 {'time':[297,298],'goods':'goods12'},
 {'time':[299,300],'goods':'goods12'},
 {'time':[301,302],'goods':'goods12'},
 {'time':[303,304],'goods':'goods12'},
 {'time':[305,306],'goods':'goods12'},
 {'time':[307,308],'goods':'goods12'},
 {'time':[309,310],'goods':'goods12'},
 {'time':[311,312],'goods':'goods12'},
 {'time':[313,314],'goods':'goods12'},
 {'time':[315,316],'goods':'goods12'},
 {'time':[317,318],'goods':'goods12'},
 {'time':[319,320],'goods':'goods12'},
 {'time':[321,322],'goods':'goods12'},
 {'time':[323,324],'goods':'goods12'},
 {'time':[325,326],'goods':'goods12'},
 {'time':[327,328],'goods':'goods12'},
 {'time':[329,330],'goods':'goods12'},
 {'time':[331,332],'goods':'goods12'},
 {'time':[333,334],'goods':'goods12'},
 {'time':[335,336],'goods':'goods12'},
 {'time':[337,338],'goods':'goods12'},
 {'time':[339,340],'goods':'goods12'},
 {'time':[341,342],'goods':'goods12'},
 {'time':[343,344],'goods':'goods12'},
 {'time':[345,346],'goods':'goods12'},
 {'time':[347,348],'goods':'goods12'},
 {'time':[349,350],'goods':'goods12'},
 {'time':[351,352],'goods':'goods12'},
 {'time':[353,354],'goods':'goods12'},
 {'time':[355,356],'goods':'goods12'},
 {'time':[357,358],'goods':'goods12'},
 {'time':[359,360],'goods':'goods12'},
 {'time':[361,362],'goods':'goods12'},
 {'time':[363,364],'goods':'goods12'},
 {'time':[365,366],'goods':'goods12'},
 {'time':[367,368],'goods':'goods12'},
 {'time':[369,370],'goods':'goods12'},
 {'time':[371,372],'goods':'goods12'},
 {'time':[373,374],'goods':'goods12'},
 {'time':[375,376],'goods':'goods12'},
 {'time':[377,378],'goods':'goods12'},
 {'time':[379,380],'goods':'goods12'},
 {'time':[381,382],'goods':'goods12'},
 {'time':[383,384],'goods':'goods12'},
 {'time':[385,386],'goods':'goods12'},
 {'time':[387,388],'goods':'goods12'},
 {'time':[389,390],'goods':'goods12'},
 {'time':[391,392],'goods':'goods12'},
 {'time':[393,394],'goods':'goods12'},
 {'time':[395,396],'goods':'goods12'},
 {'time':[397,398],'goods':'goods12'},
 {'time':[399,400],'goods':'goods12'},
        ],  #开服第几天，1=开启天数，2=结束天数 （eg：偏移值5点，1号5点开服, 'days':[1, 2]   活动入口会在3号5点关闭）




 'goods1':[
        {
           'reward':{'item036':2},
              },
        {
           'reward':{'item042':10},
              },
        {
           'reward':{'item091':1},
              },
        {
           'reward':{'item087':1},
              },
        {
           'reward':{'item088':1},
              },
        {
           'reward':{'item004':3},
              },















          ],
 'goods2':[
        {
           'reward':{'item036':2},
              },
        {
           'reward':{'item042':10},
              },
        {
           'reward':{'item043':10},
              },
        {
           'reward':{'item195':10},
              },
        {
           'reward':{'item091':1},
              },
        {
           'reward':{'item008':1},
              },
        {
           'reward':{'item009':1},
              },
        {
           'reward':{'item087':1},
              },
        {
           'reward':{'item088':1},
              },
        {
           'reward':{'item004':3},
              },











          ],
 'goods3':[
        {
           'reward':{'item770':5},
              },
        {
           'reward':{'item017':1},
              },
        {
           'reward':{'item036':2},
              },
        {
           'reward':{'item042':10},
              },
        {
           'reward':{'item043':10},
              },
        {
           'reward':{'item195':10},
              },
        {
           'reward':{'item091':1},
              },
        {
           'reward':{'item008':1},
              },
        {
           'reward':{'item009':1},
              },
        {
           'reward':{'item087':1},
              },
        {
           'reward':{'item088':1},
              },
        {
           'reward':{'item004':3},
              },









          ],
 'goods4':[
        {
           'reward':{'item770':5},
              },
        {
           'reward':{'item017':1},
              },
        {
           'reward':{'item018':1},
              },
        {
           'reward':{'item036':2},
              },
        {
           'reward':{'item042':10},
              },
        {
           'reward':{'item043':10},
              },
        {
           'reward':{'item195':10},
              },
        {
           'reward':{'item091':1},
              },
        {
           'reward':{'item008':1},
              },
        {
           'reward':{'item009':1},
              },
        {
           'reward':{'item087':1},
              },
        {
           'reward':{'item088':1},
              },
        {
           'reward':{'item004':3},
              },








          ],
 'goods5':[
        {
           'reward':{'item017':1},
              },
        {
           'reward':{'item018':1},
              },
        {
           'reward':{'item019':1},
              },
        {
           'reward':{'item770':5},
              },
        {
           'reward':{'item036':2},
              },
        {
           'reward':{'item042':10},
              },
        {
           'reward':{'item043':10},
              },
        {
           'reward':{'item044':10},
              },
        {
           'reward':{'item195':10},
              },
        {
           'reward':{'item091':1},
              },
        {
           'reward':{'item008':1},
              },
        {
           'reward':{'item009':1},
              },
        {
           'reward':{'item087':1},
              },
        {
           'reward':{'item088':1},
              },
        {
           'reward':{'item004':3},
              },






          ],
 'goods6':[
        {
           'reward':{'item017':1},
              },
        {
           'reward':{'item018':1},
              },
        {
           'reward':{'item019':1},
              },
        {
           'reward':{'item770':5},
              },
        {
           'reward':{'item036':2},
              },
        {
           'reward':{'item042':10},
              },
        {
           'reward':{'item043':10},
              },
        {
           'reward':{'item044':10},
              },
        {
           'reward':{'item045':10},
              },
        {
           'reward':{'item195':10},
              },
        {
           'reward':{'item091':1},
              },
        {
           'reward':{'item008':1},
              },
        {
           'reward':{'item009':1},
              },
        {
           'reward':{'item087':1},
              },
        {
           'reward':{'item088':1},
              },
        {
           'reward':{'item004':3},
              },





          ],
 'goods7':[
        {
           'reward':{'item036':2},
              },
        {
           'reward':{'item042':10},
              },
        {
           'reward':{'item043':10},
              },
        {
           'reward':{'item091':1},
              },
        {
           'reward':{'item087':1},
              },
        {
           'reward':{'item088':1},
              },
        {
           'reward':{'item004':3},
              },














          ],
 'goods8':[
        {
           'reward':{'item770':5},
              },
        {
           'reward':{'item017':1},
              },
        {
           'reward':{'item018':1},
              },
        {
           'reward':{'item036':2},
              },
        {
           'reward':{'item042':10},
              },
        {
           'reward':{'item043':10},
              },
        {
           'reward':{'item044':10},
              },
        {
           'reward':{'item195':10},
              },
        {
           'reward':{'item091':1},
              },
        {
           'reward':{'item008':1},
              },
        {
           'reward':{'item009':1},
              },
        {
           'reward':{'item087':1},
              },
        {
           'reward':{'item088':1},
              },
        {
           'reward':{'item004':3},
              },







          ],
 'goods9':[
        {
           'reward':{'item017':1},
              },
        {
           'reward':{'item018':1},
              },
        {
           'reward':{'item019':1},
              },
        {
           'reward':{'item770':5},
              },
        {
           'reward':{'item036':2},
              },
        {
           'reward':{'item042':10},
              },
        {
           'reward':{'item043':10},
              },
        {
           'reward':{'item044':10},
              },
        {
           'reward':{'item045':10},
              },
        {
           'reward':{'item195':10},
              },
        {
           'reward':{'item176':75},
              },
        {
           'reward':{'item091':1},
              },
        {
           'reward':{'item008':1},
              },
        {
           'reward':{'item009':1},
              },
        {
           'reward':{'item087':1},
              },
        {
           'reward':{'item088':1},
              },
        {
           'reward':{'item004':3},
              },




          ],
 'goods10':[
        {
           'reward':{'item017':1},
              },
        {
           'reward':{'item018':1},
              },
        {
           'reward':{'item019':1},
              },
        {
           'reward':{'item770':5},
              },
        {
           'reward':{'item036':2},
              },
        {
           'reward':{'item042':10},
              },
        {
           'reward':{'item043':10},
              },
        {
           'reward':{'item044':10},
              },
        {
           'reward':{'item045':10},
              },
        {
           'reward':{'item195':10},
              },
        {
           'reward':{'item176':75},
              },
        {
           'reward':{'item1211':10},
              },
        {
           'reward':{'item091':1},
              },
        {
           'reward':{'item008':1},
              },
        {
           'reward':{'item009':1},
              },
        {
           'reward':{'item087':1},
              },
        {
           'reward':{'item088':1},
              },
        {
           'reward':{'item004':3},
              },



          ],
 'goods11':[
        {
           'reward':{'item017':1},
              },
        {
           'reward':{'item018':1},
              },
        {
           'reward':{'item019':1},
              },
        {
           'reward':{'item770':5},
              },
        {
           'reward':{'item036':2},
              },
        {
           'reward':{'item042':10},
              },
        {
           'reward':{'item043':10},
              },
        {
           'reward':{'item044':10},
              },
        {
           'reward':{'item045':10},
              },
        {
           'reward':{'item195':10},
              },
        {
           'reward':{'item176':75},
              },
        {
           'reward':{'item1211':10},
              },
        {
           'reward':{'item1270':10},
              },
        {
           'reward':{'item091':1},
              },
        {
           'reward':{'item008':1},
              },
        {
           'reward':{'item009':1},
              },
        {
           'reward':{'item087':1},
              },
        {
           'reward':{'item088':1},
              },
        {
           'reward':{'item004':3},
              },


          ],
 'goods12':[
        {
           'reward':{'item017':1},
              },
        {
           'reward':{'item018':1},
              },
        {
           'reward':{'item019':1},
              },
        {
           'reward':{'item770':5},
              },
        {
           'reward':{'item036':2},
              },
        {
           'reward':{'item042':10},
              },
        {
           'reward':{'item043':10},
              },
        {
           'reward':{'item044':10},
              },
        {
           'reward':{'item045':10},
              },
        {
           'reward':{'item195':10},
              },
        {
           'reward':{'item176':75},
              },
        {
           'reward':{'item1211':10},
              },
        {
           'reward':{'item1270':10},
              },
        {
           'reward':{'item2014':10},
              },
        {
           'reward':{'item091':1},
              },
        {
           'reward':{'item008':1},
              },
        {
           'reward':{'item009':1},
              },
        {
           'reward':{'item087':1},
              },
        {
           'reward':{'item088':1},
              },
        {
           'reward':{'item004':3},
              },

          ],


},






############兑换皮肤币#####################
        'pay_skincoin':{
                         'active_time':[[9,00],[18,00]],
                         'hero_show':'skin721_1',
                         '1_999':{
                   #                 'pay301':{ # 需要充值的档位ID
                   #                    'name':'skincoin_01', # 礼包名字
                   #                    'icon':'icon_skinsd1.png', # 可缺省，缺省则罗列reward图标
                   #                    'num':-1, # 可购买次数，-1为不限制
                   #                    'exclusion':['mi'],
                   #                    'reward':{'item1030':5,'item1007':5,},
                   #                   },
                   #                 'pay302':{ # 需要充值的档位ID
                   #                    'name':'skincoin_02', # 礼包名字
                   #                    'icon':'icon_skinsd2.png', # 可缺省，缺省则罗列reward图标
                   #                    'num':5, # 可购买次数
                   #                    'exclusion':['mi'],
                   #                    'reward':{'item1030':10,'item1007':10,},
                   #                   },
                   #                 'pay303':{ # 需要充值的档位ID
                   #                    'name':'skincoin_03', # 礼包名字
                   #                    'icon':'icon_skinsd3.png', # 可缺省，缺省则罗列reward图标
                   #                    'num':5, # 可购买次数
                   #                    'exclusion':['mi'],
                   #                    'reward':{'item1031':10,'item1032':1,'item1007':15,},
                   #                   },
                                    'gd0004':{ # 需要充值的档位ID
                                       'name':'skincoin_04', # 礼包名字
                                       'icon':'icon_skinsd4.png', # 可缺省，缺省则罗列reward图标
                                       'num':-1, # 可购买次数
                                       'exclusion':['mi'],
                                       'reward':{'item1091':1280,},
                                      },
                                    'gd0005':{ # 需要充值的档位ID
                                       'name':'skincoin_05', # 礼包名字
                                       'icon':'icon_skinsd5.png', # 可缺省，缺省则罗列reward图标
                                       'num':-1, # 可购买次数
                                       'exclusion':['mi'],
                                       'reward':{'item1091':3500,},
                                      },
                                    'gd0006':{ # 需要充值的档位ID
                                       'name':'skincoin_06', # 礼包名字
                                       'icon':'icon_skinsd6.png', # 可缺省，缺省则罗列reward图标
                                       'num':-1, # 可购买次数
                                       'exclusion':['mi'],
                                       'reward':{'item1091':7000,},
                                      },
                                      
                         },
             },




##########################玲珑奇士#######################################



'sp_army_box':{
	#对应货币item1103
	'tips':'sp_army_tips',
        'sp_army_date':datetime.datetime(2020,8,6),#版本上线日期
        'mail_name':'sp_army_mail_name',
        'mail_info':'sp_army_mail_info',
	'begin_time':[ 
      {'time':[[4,4],[10,0],[23,0],[23,59]],'goods':'s2'},  #开服天数，开启时间，结束充值时间，结束活动的时间
      {'time':[[20,20],[10,0],[23,0],[23,59]],'goods':'s1'},  #开服天数，开启时间，结束充值时间，结束活动的时间
      {'time':[[48,48],[10,0],[23,0],[23,59]],'goods':'s2'},  #开服天数，开启时间，结束充值时间，结束活动的时间
      {'time':[[76,76],[10,0],[23,0],[23,59]],'goods':'s1'},  #开服天数，开启时间，结束充值时间，结束活动的时间
      {'time':[[104,104],[10,0],[23,0],[23,59]],'goods':'s2'},  #开服天数，开启时间，结束充值时间，结束活动的时间
      {'time':[[132,132],[10,0],[23,0],[23,59]],'goods':'s1'},  #开服天数，开启时间，结束充值时间，结束活动的时间
      {'time':[[160,160],[10,0],[23,0],[23,59]],'goods':'s2'},  #开服天数，开启时间，结束充值时间，结束活动的时间
      {'time':[[188,188],[10,0],[23,0],[23,59]],'goods':'s1'},  #开服天数，开启时间，结束充值时间，结束活动的时间
      {'time':[[216,216],[10,0],[23,0],[23,59]],'goods':'s2'},  #开服天数，开启时间，结束充值时间，结束活动的时间
      {'time':[[244,244],[10,0],[23,0],[23,59]],'goods':'s1'},  #开服天数，开启时间，结束充值时间，结束活动的时间
      {'time':[[272,272],[10,0],[23,0],[23,59]],'goods':'s2'},  #开服天数，开启时间，结束充值时间，结束活动的时间
      {'time':[[300,300],[10,0],[23,0],[23,59]],'goods':'s1'},  #开服天数，开启时间，结束充值时间，结束活动的时间
      {'time':[[328,328],[10,0],[23,0],[23,59]],'goods':'s2'},  #开服天数，开启时间，结束充值时间，结束活动的时间
      {'time':[[356,356],[10,0],[23,0],[23,59]],'goods':'s1'},  #开服天数，开启时间，结束充值时间，结束活动的时间





	   ],
	 'pay_sp_army':{
                                    'gd0108':{ # 需要充值的档位ID
                                       'name':'sp_army_coin_01', # 礼包名字
                                       'icon':'icon_qishi_bxbf.png', # 可缺省，缺省则罗列reward图标
                                       'exclusion':['local'],
                                       'reward_num':320,
                                      },
				    'gd0109':{ # 需要充值的档位ID
                                       'name':'sp_army_coin_02', # 礼包名字
                                       'icon':'icon_qishi_bxbf2.png', # 可缺省，缺省则罗列reward图标
                                       'exclusion':['local'],

                                       'reward_num':1100,
                                      },
									  },

	's1':{ 
		'ani': 'show_s1',
		'image': 'bg_qishi_map2.jpg',
		'name': '502001',
		'info': '502002',
		'buy_one':[1,100,10000],		#免费次数，每次购买消耗的道具数量，每次购买获得的银币数量gold
		'buy_ten':[0,1000,100000],		#免费次数，每次购买消耗的道具数量，每次购买获得的银币数量gold
	        'limit':9999,                 #抽奖限制次数每天偏移值恢复
		'lucky':0,                   #初始幸运值
                  
		'goods':[   #物品，幸运值，权重

#幸运值小于等于99时的奖池 #每个道具ID，幸运值，数量，权重
[99,[['item3050',0,500,19],['item3051',0,500,19],['item3150',0,500,19],['item3151',0,500,19],['item3050',0,200,150],['item3051',0,200,150],['item3150',0,200,150],['item3151',0,200,150],['item3050',0,100,580],['item3051',0,100,580],['item3150',0,100,580],['item3151',0,100,580],['item3050',0,50,4000],['item3051',0,50,4000],['item3150',0,50,4000],['item3151',0,50,4000],['item3050',0,20,10000],['item3051',0,20,10000],['item3150',0,20,10000],['item3151',0,20,10000],['item3050',1,10,35000],['item3051',1,10,35000],['item3150',1,10,35000],['item3151',1,10,35000],['item3050',1,5,49440],['item3051',1,5,49440],['item3150',1,5,49440],['item3151',1,5,49440],]],






#幸运值等于100时的奖池 #每个道具ID，幸运值，权重
[100,[['item3050',-100,500,40],['item3051',-100,500,40],['item3150',-100,500,40],['item3151',-100,500,40],['item3050',-100,200,320],['item3051',-100,200,320],['item3150',-100,200,320],['item3151',-100,200,320],['item3050',-100,100,1200],['item3051',-100,100,1200],['item3150',-100,100,1200],['item3151',-100,100,1200],]],




            
		],


		'show_chance':[
['item3050',0.0001,500],['item3051',0.0001,500],['item3150',0.0001,500],['item3151',0.0001,500],['item3050',0.0008,200],['item3051',0.0008,200],['item3150',0.0008,200],['item3151',0.0008,200],['item3050',0.003,100],['item3051',0.003,100],['item3150',0.003,100],['item3151',0.003,100],['item3050',0.01,50],['item3051',0.01,50],['item3150',0.01,50],['item3151',0.01,50],['item3050',0.025,20],['item3051',0.025,20],['item3150',0.025,20],['item3151',0.025,20],['item3050',0.0875,10],['item3051',0.0875,10],['item3150',0.0875,10],['item3151',0.0875,10],['item3050',0.1236,5],['item3051',0.1236,5],['item3150',0.1236,5],['item3151',0.1236,5],





		], 
 
 'num_reward':{25:{'item1104':1},50:{'item1104':1},75:{'item1104':1},100:{'item1104':1}}, #额外奖励抽取次数，奖励内容，奖励数量


  
   
 'sp_army_info':["sp3050","sp3051","sp3150","sp3151"],
   
},
	's2':{ 
		'ani': 'show_s2',
		'image': 'bg_qishi_map2.jpg',
		'name': '502001',
		'info': '502002',
		'buy_one':[1,100,10000],		#每期活动免费次数，每次购买消耗的道具数量，每次购买获得的银币数量
		'buy_ten':[0,1000,100000],		#每期活动免费次数，每次购买消耗的道具数量，每次购买获得的银币数量
	    'limit':9999,                 #抽奖限制次数每天偏移值恢复
		'lucky':0,                   #初始幸运值
                  
		'goods':[   #物品，幸运值，权重

#幸运值小于等于99时的奖池 #每个道具ID，幸运值，数量，权重
[99,[['item3250',0,500,19],['item3251',0,500,19],['item3350',0,500,19],['item3351',0,500,19],['item3250',0,200,150],['item3251',0,200,150],['item3350',0,200,150],['item3351',0,200,150],['item3250',0,100,580],['item3251',0,100,580],['item3350',0,100,580],['item3351',0,100,580],['item3250',0,50,4000],['item3251',0,50,4000],['item3350',0,50,4000],['item3351',0,50,4000],['item3250',0,20,10000],['item3251',0,20,10000],['item3350',0,20,10000],['item3351',0,20,10000],['item3250',1,10,35000],['item3251',1,10,35000],['item3350',1,10,35000],['item3351',1,10,35000],['item3250',1,5,49440],['item3251',1,5,49440],['item3350',1,5,49440],['item3351',1,5,49440],]],




#幸运值等于100时的奖池 #每个道具ID，幸运值，权重
[100,[['item3250',-100,500,40],['item3251',-100,500,40],['item3350',-100,500,40],['item3351',-100,500,40],['item3250',-100,200,320],['item3251',-100,200,320],['item3350',-100,200,320],['item3351',-100,200,320],['item3250',-100,100,1200],['item3251',-100,100,1200],['item3350',-100,100,1200],['item3351',-100,100,1200],]],




            
		],


		'show_chance':[
['item3250',0.0001,500],['item3251',0.0001,500],['item3350',0.0001,500],['item3351',0.0001,500],['item3250',0.0008,200],['item3251',0.0008,200],['item3350',0.0008,200],['item3351',0.0008,200],['item3250',0.003,100],['item3251',0.003,100],['item3350',0.003,100],['item3351',0.003,100],['item3250',0.01,50],['item3251',0.01,50],['item3350',0.01,50],['item3351',0.01,50],['item3250',0.025,20],['item3251',0.025,20],['item3350',0.025,20],['item3351',0.025,20],['item3250',0.0875,10],['item3251',0.0875,10],['item3350',0.0875,10],['item3351',0.0875,10],['item3250',0.1236,5],['item3251',0.1236,5],['item3350',0.1236,5],['item3351',0.1236,5],




		], 
 

 'num_reward':{25:{'item1105':1},50:{'item1105':1},75:{'item1105':1},100:{'item1105':1}}, #额外奖励抽取次数，奖励内容，奖励数量


  
   
 'sp_army_info':["sp3250","sp3251","sp3350","sp3351"],
   
},
    },  



}