{
	'merge':1,	 	#合服开关，0表示不合服就可以开启，1表示合服之后才会开启 
	'merge_ini':7,	 	#合服后，第一次襄阳战开启的天数
	'merge_cycle':8,	 	#合服后，第一次襄阳战之后，每隔X天开启一次
	'gap_ini':999,		#未合服，服务器开启后，第一次襄阳战开启的天数
	'gap':999,		#未合服，第一次襄阳战之后，每隔X天开启一次
	'player_show':[1,['500060','500061']],	#襄阳战隐藏部队功能【功能开关，【屏蔽敌国，屏蔽本国】】
	'overdue':1800,		#归属有效时间（分钟），在襄阳战产生战胜国时，在世界信息中生成一个过期时间点，到达这个时间点将重置襄阳战的城池
				#现在每次襄阳战开战当天都会重置-1到-5的城池归属和驻军数量
				#副本活动中的'reset_city'会清掉这个时间
	'sunrise':[20,0],		#襄阳战20点开启
	'sunset':[22,0],		#襄阳战22点之后不能操作
	'latest_time':[23,0],		#强制结束时间
	'push_count':30,		#推送倒计时，距离开始/结束/还有30分钟时激活推送
	'push_gap':10,		#推送间隔，每10分钟推送 一次
	'push_text':['500000','500001','500020','500030'],		#开启推送文本，结束推送文本，开启时的对话，文本中{0}代表剩余时间，襄阳战结束时的对话
	'push_show':['hero751','guide_npc002'],		#推送时使用的立绘形象
	'entrance2':24,		#主页按键出现倒计时，小于24小时后出现
	'kill_show':10,		#杀敌榜显示
	'kill_list':100,		#杀敌榜
	'effect_door':{		#东西南北门效果,每个城门增加25%攻击和免伤，襄阳城叠加，城门无效果
		'-2':0.25,
		'-3':0.25,
		'-4':0.25,
		'-5':0.25,
	},
	'xyz_cityid':['-1',['-2','-3','-4','-5'],['-10','-11','-12']],	#襄阳城，东南西北门，魏蜀吴阵
	'special_time':{		#这些时间规则只在襄阳范围内生效
		'wait':5,		#战前等待时间
		'fight':[		#战斗时间加速率，襄阳战开始就开始生效【时间区间（分钟），加速率】
			[30,1],
			[60,2],
			[90,3],
			[60000,9999999],
		],		
		'move':[[22,0],7]		#当日22点之后，移动速度翻倍
	},
        #当襄阳战距离强制结束时间latest_time前的[0]分钟起，襄阳城每秒结算战斗的场次 = ceil(当前襄阳城双方总troop数*[1])。未到此配置时仍每秒结算1场
        #有此配置时，每秒最多结算10场战斗（服务器写死）
	'quick_fight':[		
           [50,1],
           [55,0.1],
           [60,0.01],
	],

	'entrance':{		#襄阳城玉玺气泡的显示内容
		'right':[
			{'name':'500034','info':'500035','icon':'bg_167.png'},{'name':'500036','info':'500037','icon':'bg_15.png'},
			{'name':'500038','info':'500039','icon':'bg_16.png'},{'name':'500040','info':'500041','icon':'bg_17.png'}
		],
		'info':'500042',
	},
	'winner':{		#战胜国
		'victory':'500012',		#襄阳战结束后，推送给全世界的胜利信息中的文本
		'reward':[['coin',500],['merit',10000],['item036',10],],	#奖励内容（全国玩家都发，襄阳战完全结束时发邮件）
		'reward_name':'500010',		#邮件标题
		'reward_info':'500011',		#邮件内容，没有需要带入的内容
	},
	'active_add':{		#黄金产业（产业主动行为时额外获得黄金）
		'estate_lv':9,		#只能在本国9级产业上生成（生成的时候是本国的就行了，之后的变更不用管）
		'estate_na':'2',		#类型为2的产业不会生成黄金矿  1村落，2港口，3农田，4林场，5矿场，6牧场
		'estate_num':3,		#最多生成3个（最少0个）
		'coin_add':20,		#一次主动行为额外获得20黄金
		'coin_add_limit':10,		#每个玩家每天只能从黄金矿获得10次黄金
	},

	'country':{		#国家比拼
		'cycle':20,		#每20分钟结算一次（仅仅结算积分，把阶段积分累加到总积分中）
		'score_gate':30,		#结算时，城门属于自己国家加30分
		'score_city':100,		#结算时，襄阳城属于自己国家加100分
		'reward_1':[		#国家比拼奖励，奖励通过邮件发放，若国家没有积分，则没有排名，没有奖励，襄阳战完全结束时发奖
[['coin',500],['item090',5],['item141',10],['item071',5],],           #第一名国家奖励
[['coin',400],['item090',4],['item141',8],['item071',4],],           #第二名国家奖励
[['coin',300],['item090',3],['item141',6],['item071',3],],           #第三名国家奖励
		],

		'reward_2':[		#二次合服的国家比拼奖励
[['coin',500],['item090',5],['item1045',5],],           #第一名国家奖励
[['coin',400],['item090',4],['item1045',4],],           #第二名国家奖励
[['coin',300],['item090',3],['item1045',3],],           #第三名国家奖励
                ],
		'reward_3':[		#3次合服的国家比拼奖励
[['coin',500],['item090',5],['item1045',5],],           #第一名国家奖励
[['coin',400],['item090',4],['item1045',4],],           #第二名国家奖励
[['coin',300],['item090',3],['item1045',3],],           #第三名国家奖励
                ],
		'reward_4':[		#3次合服的国家比拼奖励
[['coin',500],['item090',5],['item1045',5],],           #第一名国家奖励
[['coin',400],['item090',4],['item1045',4],],           #第二名国家奖励
[['coin',300],['item090',3],['item1045',3],],           #第三名国家奖励
                ],
		'reward_5':[		                #3次合服的国家比拼奖励
[['coin',500],['item090',5],['item1045',5],],           #第一名国家奖励
[['coin',400],['item090',4],['item1045',4],],           #第二名国家奖励
[['coin',300],['item090',3],['item1045',3],],           #第三名国家奖励
                ],
		'reward_6':[		                #3次合服的国家比拼奖励
[['coin',500],['item090',5],['item1045',5],],           #第一名国家奖励
[['coin',400],['item090',4],['item1045',4],],           #第二名国家奖励
[['coin',300],['item090',3],['item1045',3],],           #第三名国家奖励
                ],
		'reward_7':[		                #3次合服的国家比拼奖励
[['coin',500],['item2267',5],['item1045',5],],           #第一名国家奖励
[['coin',400],['item2267',4],['item1045',4],],           #第二名国家奖励
[['coin',300],['item2267',3],['item1045',3],],           #第三名国家奖励
        #第三名国家奖励
                ],
		'reward_name':'500002',		#邮件标题
		'reward_info':'500003',		#邮件内容：{0}带入国家，{1}带入排名数，没有排名的国家就没有奖励，不用发邮件
	},
	'personal':{		#个人奖励
		'info':'500025',		#tips说明
		'stage':{		#个人阶段奖励
			'score_1':[		#个人战功阶段奖励，计数达成之后立即发送邮件
[20000,[['item090',5],['merit',3000],['gold',50000],['item056',20],['item058',50],],],           #个人战功奖励
[50000,[['item090',10],['merit',5000],['gold',100000],['item056',40],['item058',100],],],           #个人战功奖励
[100000,[['item090',20],['merit',10000],['gold',150000],['item056',60],['item058',150],],],           #个人战功奖励
[150000,[['item090',30],['merit',15000],['gold',200000],['item056',80],['item058',200],],],           #个人战功奖励
[300000,[['item035',1],['merit',20000],['gold',250000],['item056',100],['item058',300],],],           #个人战功奖励
			],
			'score_2':[		#二次合服的个人战功阶段奖励
[20000,[['item090',5],['merit',5000],['gold',50000],['item1045',5],['item058',50],],],           #个人战功奖励
[50000,[['item090',10],['merit',10000],['gold',100000],['item1045',10],['item058',100],],],           #个人战功奖励
[100000,[['item090',20],['merit',20000],['gold',150000],['item1045',20],['item058',150],],],           #个人战功奖励
[150000,[['item090',30],['merit',30000],['gold',200000],['item1046',3],['item058',200],],],           #个人战功奖励
[300000,[['item090',50],['merit',50000],['gold',250000],['item1046',5],['item058',300],],],           #个人战功奖励
[500000,[['item035',1],['merit',80000],['gold',300000],['item1046',10],['item058',500],],],           #个人战功奖励
			],
			'score_3':[		#3次合服的个人战功阶段奖励
[50000,[['item090',5],['merit',5000],['gold',50000],['item1045',5],['item058',50],],],           #个人战功奖励
[100000,[['item090',10],['merit',10000],['gold',100000],['item1045',10],['item058',100],],],           #个人战功奖励
[150000,[['item090',20],['merit',20000],['gold',150000],['item1045',20],['item058',150],],],           #个人战功奖励
[200000,[['item090',30],['merit',30000],['gold',200000],['item1046',3],['item058',200],],],           #个人战功奖励
[400000,[['item090',40],['merit',40000],['gold',250000],['item1046',5],['item058',300],],],           #个人战功奖励
[600000,[['item090',50],['merit',60000],['gold',300000],['item1046',10],['item058',400],],],           #个人战功奖励
[800000,[['item035',1],['merit',80000],['gold',400000],['item1046',15],['item058',500],],],           #个人战功奖励

			],
			'score_4':[		#3次合服的个人战功阶段奖励
[50000,[['item090',5],['merit',5000],['gold',50000],['item1045',5],['item046',1],],],           #个人战功奖励
[100000,[['item090',10],['merit',10000],['gold',100000],['item1045',10],['item046',1],],],           #个人战功奖励
[150000,[['item090',20],['merit',20000],['gold',150000],['item1045',20],['item046',1],],],           #个人战功奖励
[200000,[['item090',30],['merit',30000],['gold',200000],['item1046',3],['item046',1],],],           #个人战功奖励
[400000,[['item090',40],['merit',40000],['gold',250000],['item1046',5],['item046',1],],],           #个人战功奖励
[600000,[['item090',50],['merit',60000],['gold',300000],['item1046',10],['item046',1],],],           #个人战功奖励
[800000,[['item035',1],['merit',80000],['gold',400000],['item1046',15],['item046',2],],],           #个人战功奖励

			],
			'score_5':[		#5次合服的个人战功阶段奖励
[50000,[['item090',5],['merit',5000],['gold',50000],['item1045',5],['item046',1],],],           #个人战功奖励
[100000,[['item090',10],['merit',10000],['gold',100000],['item1045',10],['item046',1],],],           #个人战功奖励
[150000,[['item090',20],['merit',20000],['gold',150000],['item1045',20],['item046',1],],],           #个人战功奖励
[200000,[['item090',30],['merit',30000],['gold',200000],['item1046',3],['item046',1],],],           #个人战功奖励
[400000,[['item090',40],['merit',40000],['gold',250000],['item1046',5],['item046',1],],],           #个人战功奖励
[600000,[['item090',50],['merit',60000],['gold',300000],['item1046',10],['item046',1],],],           #个人战功奖励
[800000,[['item035',1],['merit',80000],['gold',400000],['item1046',15],['item046',2],],],           #个人战功奖励
			],
			'score_6':[		#6次合服的个人战功阶段奖励
[50000,[['item090',5],['merit',5000],['gold',50000],['item1045',5],['item046',1],],],           #个人战功奖励
[100000,[['item090',10],['merit',10000],['gold',100000],['item1045',10],['item046',1],],],           #个人战功奖励
[150000,[['item090',20],['merit',20000],['gold',150000],['item1045',20],['item046',1],],],           #个人战功奖励
[200000,[['item090',30],['merit',30000],['gold',200000],['item1046',3],['item046',1],],],           #个人战功奖励
[400000,[['item090',40],['merit',40000],['gold',250000],['item1046',5],['item046',1],],],           #个人战功奖励
[600000,[['item090',50],['merit',60000],['gold',300000],['item1046',10],['item046',1],],],           #个人战功奖励
[800000,[['item035',1],['merit',80000],['gold',400000],['item1046',15],['item046',2],],],           #个人战功奖励

			],
			'score_7':[		#6次合服的个人战功阶段奖励
[50000,[['item090',5],['item2267',5],['gold',200000],['item1045',5],['item046',1],],],           #个人战功奖励
[100000,[['item090',10],['item2267',10],['merit',50000],['item1045',10],['item046',1],],],           #个人战功奖励
[150000,[['item090',20],['item2267',20],['gold',300000],['item1045',20],['item046',1],],],           #个人战功奖励
[200000,[['item090',30],['item2267',30],['merit',100000],['item1046',3],['item046',1],],],           #个人战功奖励
[400000,[['item090',40],['item2267',40],['gold',400000],['item1046',5],['item046',1],],],           #个人战功奖励
[600000,[['item090',50],['item2267',50],['merit',150000],['item1046',10],['item046',1],],],           #个人战功奖励
[800000,[['item035',1],['item2267',100],['gold',600000],['item1046',15],['item046',2],],],           #个人战功奖励


			],
			'stage_name':'500004',		#邮件标题
			'stage_info':'500005',		#邮件内容，没有需要带入的内容
		},
		'ranking':{		#个人排名奖励
			'cycle':20,		#每20分钟结算一次（发送奖励，并重置计数）@
			'max':100,		#榜单总共100人
			'reward_1':[		#排名奖励，
[1,[['coin',300],['item027',5],['item030',5],['item036',5],],],
[2,[['coin',270],['item027',5],['item030',5],['item036',5],],],
[3,[['coin',240],['item027',5],['item030',5],['item036',5],],],
[4,[['coin',220],['item027',4],['item030',4],['item036',4],],],
[5,[['coin',200],['item027',4],['item030',4],['item036',4],],],
[6,[['coin',190],['item027',4],['item030',4],['item036',4],],],
[7,[['coin',180],['item027',4],['item030',4],['item036',4],],],
[8,[['coin',170],['item027',4],['item030',4],['item036',4],],],
[9,[['coin',160],['item027',4],['item030',4],['item036',4],],],
[10,[['coin',150],['item027',4],['item030',4],['item036',4],],],
[20,[['coin',140],['item027',3],['item030',3],['item036',3],],],
[30,[['coin',130],['item027',3],['item030',3],['item036',3],],],
[50,[['coin',120],['item027',3],['item030',3],['item036',3],],],
[100,[['coin',100],['item027',2],['item030',2],['item036',2],],],
			],
			'reward_2':[		#二次合服的排名奖励
[1,[['coin',300],['item187',5],['merit',10000],['item036',10],],],
[2,[['coin',270],['item187',5],['merit',10000],['item036',10],],],
[3,[['coin',240],['item187',5],['merit',10000],['item036',10],],],
[4,[['coin',220],['item187',4],['merit',8000],['item036',8],],],
[5,[['coin',200],['item187',4],['merit',8000],['item036',8],],],
[6,[['coin',190],['item187',4],['merit',8000],['item036',8],],],
[7,[['coin',180],['item187',4],['merit',8000],['item036',8],],],
[8,[['coin',170],['item187',4],['merit',8000],['item036',8],],],
[9,[['coin',160],['item187',4],['merit',8000],['item036',8],],],
[10,[['coin',150],['item187',4],['merit',8000],['item036',8],],],
[20,[['coin',140],['item187',3],['merit',6000],['item036',6],],],
[30,[['coin',130],['item187',3],['merit',6000],['item036',6],],],
[50,[['coin',120],['item187',3],['merit',6000],['item036',6],],],
[100,[['coin',100],['item187',2],['merit',4000],['item036',4],],],

			],
			'reward_3':[		#3次合服的排名奖励
[1,[['coin',300],['item187',5],['merit',10000],['item036',10],],],
[2,[['coin',270],['item187',5],['merit',10000],['item036',10],],],
[3,[['coin',240],['item187',5],['merit',10000],['item036',10],],],
[4,[['coin',220],['item187',4],['merit',8000],['item036',8],],],
[5,[['coin',200],['item187',4],['merit',8000],['item036',8],],],
[6,[['coin',190],['item187',4],['merit',8000],['item036',8],],],
[7,[['coin',180],['item187',4],['merit',8000],['item036',8],],],
[8,[['coin',170],['item187',4],['merit',8000],['item036',8],],],
[9,[['coin',160],['item187',4],['merit',8000],['item036',8],],],
[10,[['coin',150],['item187',4],['merit',8000],['item036',8],],],
[20,[['coin',140],['item187',3],['merit',6000],['item036',6],],],
[30,[['coin',130],['item187',3],['merit',6000],['item036',6],],],
[50,[['coin',120],['item187',3],['merit',6000],['item036',6],],],
[100,[['coin',100],['item187',2],['merit',4000],['item036',4],],],

			],

			'reward_4':[		#4次合服的排名奖励
[1,[['coin',300],['item187',5],['merit',10000],['item036',10],],],
[2,[['coin',270],['item187',5],['merit',10000],['item036',10],],],
[3,[['coin',240],['item187',5],['merit',10000],['item036',10],],],
[4,[['coin',220],['item187',4],['merit',8000],['item036',8],],],
[5,[['coin',200],['item187',4],['merit',8000],['item036',8],],],
[6,[['coin',190],['item187',4],['merit',8000],['item036',8],],],
[7,[['coin',180],['item187',4],['merit',8000],['item036',8],],],
[8,[['coin',170],['item187',4],['merit',8000],['item036',8],],],
[9,[['coin',160],['item187',4],['merit',8000],['item036',8],],],
[10,[['coin',150],['item187',4],['merit',8000],['item036',8],],],
[20,[['coin',140],['item187',3],['merit',6000],['item036',6],],],
[30,[['coin',130],['item187',3],['merit',6000],['item036',6],],],
[50,[['coin',120],['item187',3],['merit',6000],['item036',6],],],
[100,[['coin',100],['item187',2],['merit',4000],['item036',4],],],

			],
			'reward_5':[		#5次合服的排名奖励
[1,[['coin',300],['item187',5],['merit',10000],['item036',10],],],
[2,[['coin',270],['item187',5],['merit',10000],['item036',10],],],
[3,[['coin',240],['item187',5],['merit',10000],['item036',10],],],
[4,[['coin',220],['item187',4],['merit',8000],['item036',8],],],
[5,[['coin',200],['item187',4],['merit',8000],['item036',8],],],
[6,[['coin',190],['item187',4],['merit',8000],['item036',8],],],
[7,[['coin',180],['item187',4],['merit',8000],['item036',8],],],
[8,[['coin',170],['item187',4],['merit',8000],['item036',8],],],
[9,[['coin',160],['item187',4],['merit',8000],['item036',8],],],
[10,[['coin',150],['item187',4],['merit',8000],['item036',8],],],
[20,[['coin',140],['item187',3],['merit',6000],['item036',6],],],
[30,[['coin',130],['item187',3],['merit',6000],['item036',6],],],
[50,[['coin',120],['item187',3],['merit',6000],['item036',6],],],
[100,[['coin',100],['item187',2],['merit',4000],['item036',4],],],

			],
			'reward_6':[		#6次合服的排名奖励
[1,[['coin',300],['item187',5],['merit',10000],['item036',10],],],
[2,[['coin',270],['item187',5],['merit',10000],['item036',10],],],
[3,[['coin',240],['item187',5],['merit',10000],['item036',10],],],
[4,[['coin',220],['item187',4],['merit',8000],['item036',8],],],
[5,[['coin',200],['item187',4],['merit',8000],['item036',8],],],
[6,[['coin',190],['item187',4],['merit',8000],['item036',8],],],
[7,[['coin',180],['item187',4],['merit',8000],['item036',8],],],
[8,[['coin',170],['item187',4],['merit',8000],['item036',8],],],
[9,[['coin',160],['item187',4],['merit',8000],['item036',8],],],
[10,[['coin',150],['item187',4],['merit',8000],['item036',8],],],
[20,[['coin',140],['item187',3],['merit',6000],['item036',6],],],
[30,[['coin',130],['item187',3],['merit',6000],['item036',6],],],
[50,[['coin',120],['item187',3],['merit',6000],['item036',6],],],
[100,[['coin',100],['item187',2],['merit',4000],['item036',4],],],
			],
			'reward_7':[		#6次合服的排名奖励
[1,[['coin',300],['item187',5],['merit',10000],['item036',10],],],
[2,[['coin',270],['item187',5],['merit',10000],['item036',10],],],
[3,[['coin',240],['item187',5],['merit',10000],['item036',10],],],
[4,[['coin',220],['item187',4],['merit',8000],['item036',8],],],
[5,[['coin',200],['item187',4],['merit',8000],['item036',8],],],
[6,[['coin',190],['item187',4],['merit',8000],['item036',8],],],
[7,[['coin',180],['item187',4],['merit',8000],['item036',8],],],
[8,[['coin',170],['item187',4],['merit',8000],['item036',8],],],
[9,[['coin',160],['item187',4],['merit',8000],['item036',8],],],
[10,[['coin',150],['item187',4],['merit',8000],['item036',8],],],
[20,[['coin',140],['item187',3],['merit',6000],['item036',6],],],
[30,[['coin',130],['item187',3],['merit',6000],['item036',6],],],
[50,[['coin',120],['item187',3],['merit',6000],['item036',6],],],
[100,[['coin',100],['item187',2],['merit',4000],['item036',4],],],

			],
			'ranking_name':'500006',		#邮件标题
			'ranking_info':'500007',		#邮件内容，{0}带入上一阶段的排名
		}
	},
	'total_ranking':{		#战功排行榜
		'max':100,		#排行榜只统计前100名玩家
		'info':'500021',		#tips说明
		'reward_1':[		#战功排奖励
[1,[['item090',30],['item021',10],['gold',300000],['item084',3],],],
[2,[['item090',27],['item021',9],['gold',270000],['item084',2],],],
[3,[['item090',24],['item021',8],['gold',240000],['item084',2],],],
[4,[['item090',22],['item021',7],['gold',220000],['item084',1],],],
[5,[['item090',20],['item021',7],['gold',200000],['item084',1],],],
[6,[['item090',19],['item021',7],['gold',190000],['item084',1],],],
[7,[['item090',18],['item021',7],['gold',180000],['item084',1],],],
[8,[['item090',17],['item021',7],['gold',170000],['item084',1],],],
[9,[['item090',16],['item021',7],['gold',160000],['item084',1],],],
[10,[['item090',15],['item021',7],['gold',150000],['item084',1],],],
[20,[['item090',14],['item021',6],['gold',140000],['item058',500],],],
[30,[['item090',13],['item021',5],['gold',130000],['item058',400],],],
[50,[['item090',12],['item021',5],['gold',120000],['item058',300],],],
[100,[['item090',10],['item021',4],['gold',100000],['item058',200],],],
		],

		'reward_2':[		#二次合服的战功排奖励
[1,[['item090',30],['item1046',20],['item036',30],['item084',3],],],
[2,[['item090',27],['item1046',18],['item036',27],['item084',2],],],
[3,[['item090',24],['item1046',16],['item036',24],['item084',2],],],
[4,[['item090',22],['item1046',14],['item036',22],['item084',1],],],
[5,[['item090',20],['item1046',12],['item036',20],['item084',1],],],
[6,[['item090',19],['item1046',10],['item036',19],['item084',1],],],
[7,[['item090',18],['item1046',10],['item036',18],['item084',1],],],
[8,[['item090',17],['item1046',10],['item036',17],['item084',1],],],
[9,[['item090',16],['item1046',10],['item036',16],['item084',1],],],
[10,[['item090',15],['item1046',10],['item036',15],['item084',1],],],
[20,[['item090',10],['item1045',10],['item036',10],['item058',500],],],
[100,[['item090',5],['item1045',5],['item036',5],['item058',200],],],
		],

		'reward_3':[		#3次合服的战功排奖励
[1,[['item090',30],['item1046',20],['item036',30],['item084',3],],],
[2,[['item090',27],['item1046',18],['item036',27],['item084',2],],],
[3,[['item090',24],['item1046',16],['item036',24],['item084',2],],],
[4,[['item090',22],['item1046',14],['item036',22],['item084',1],],],
[5,[['item090',20],['item1046',12],['item036',20],['item084',1],],],
[6,[['item090',19],['item1046',10],['item036',19],['item084',1],],],
[7,[['item090',18],['item1046',10],['item036',18],['item084',1],],],
[8,[['item090',17],['item1046',10],['item036',17],['item084',1],],],
[9,[['item090',16],['item1046',10],['item036',16],['item084',1],],],
[10,[['item090',15],['item1046',10],['item036',15],['item084',1],],],
[20,[['item090',10],['item1045',10],['item036',10],['item058',500],],],
[100,[['item090',5],['item1045',5],['item036',5],['item058',200],],],
		],
		'reward_4':[		#4次合服的战功排奖励
[1,[['item090',30],['item1046',20],['item036',30],['item084',3],],],
[2,[['item090',27],['item1046',18],['item036',27],['item084',2],],],
[3,[['item090',24],['item1046',16],['item036',24],['item084',2],],],
[4,[['item090',22],['item1046',14],['item036',22],['item084',1],],],
[5,[['item090',20],['item1046',12],['item036',20],['item084',1],],],
[6,[['item090',19],['item1046',10],['item036',19],['item084',1],],],
[7,[['item090',18],['item1046',10],['item036',18],['item084',1],],],
[8,[['item090',17],['item1046',10],['item036',17],['item084',1],],],
[9,[['item090',16],['item1046',10],['item036',16],['item084',1],],],
[10,[['item090',15],['item1046',10],['item036',15],['item084',1],],],
[20,[['item090',10],['item1045',10],['item036',10],['item058',500],],],
[100,[['item090',5],['item1045',5],['item036',5],['item058',200],],],
		],
		'reward_5':[		#5次合服的战功排奖励
[1,[['item090',30],['item1046',20],['item036',30],['item084',3],],],
[2,[['item090',27],['item1046',18],['item036',27],['item084',2],],],
[3,[['item090',24],['item1046',16],['item036',24],['item084',2],],],
[4,[['item090',22],['item1046',14],['item036',22],['item084',1],],],
[5,[['item090',20],['item1046',12],['item036',20],['item084',1],],],
[6,[['item090',19],['item1046',10],['item036',19],['item084',1],],],
[7,[['item090',18],['item1046',10],['item036',18],['item084',1],],],
[8,[['item090',17],['item1046',10],['item036',17],['item084',1],],],
[9,[['item090',16],['item1046',10],['item036',16],['item084',1],],],
[10,[['item090',15],['item1046',10],['item036',15],['item084',1],],],
[20,[['item090',10],['item1045',10],['item036',10],['item058',500],],],
[100,[['item090',5],['item1045',5],['item036',5],['item058',200],],],
		],
		'reward_6':[		#6次合服的战功排奖励
[1,[['item090',30],['item1046',20],['item036',30],['item084',3],],],
[2,[['item090',27],['item1046',18],['item036',27],['item084',2],],],
[3,[['item090',24],['item1046',16],['item036',24],['item084',2],],],
[4,[['item090',22],['item1046',14],['item036',22],['item084',1],],],
[5,[['item090',20],['item1046',12],['item036',20],['item084',1],],],
[6,[['item090',19],['item1046',10],['item036',19],['item084',1],],],
[7,[['item090',18],['item1046',10],['item036',18],['item084',1],],],
[8,[['item090',17],['item1046',10],['item036',17],['item084',1],],],
[9,[['item090',16],['item1046',10],['item036',16],['item084',1],],],
[10,[['item090',15],['item1046',10],['item036',15],['item084',1],],],
[20,[['item090',10],['item1045',10],['item036',10],['item058',500],],],
[100,[['item090',5],['item1045',5],['item036',5],['item058',200],],],
		],
		'reward_7':[		#6次合服的战功排奖励
[1,[['item2267',30],['item1046',20],['item036',30],['item084',3],],],
[2,[['item2267',27],['item1046',18],['item036',27],['item084',2],],],
[3,[['item2267',24],['item1046',16],['item036',24],['item084',2],],],
[4,[['item2267',22],['item1046',14],['item036',22],['item084',1],],],
[5,[['item2267',20],['item1046',12],['item036',20],['item084',1],],],
[6,[['item2267',19],['item1046',10],['item036',19],['item084',1],],],
[7,[['item2267',18],['item1046',10],['item036',18],['item084',1],],],
[8,[['item2267',17],['item1046',10],['item036',17],['item084',1],],],
[9,[['item2267',16],['item1046',10],['item036',16],['item084',1],],],
[10,[['item2267',15],['item1046',10],['item036',15],['item084',1],],],

[20,[['item2267',10],['item1045',10],['item036',10],['item058',500],],],

[100,[['item2267',5],['item1045',5],['item036',5],['item058',200],],],

		],

		'reward_name':'500008',		#邮件标题
		'reward_info':'500009',		#邮件内容：{0}带入排名数，未上榜没有排名的没有奖励，不用发邮件
	},
	'ballista':{		#攻城器械
		'info':'500017',		#器械说明
		'path': [['-10', '-2', '-1'], ['-11', '-5', '-1'], ['-12', '-3', '-1']],	#攻城车攻城顺序
		'order':['car','car','car','car','big_car'],		#建造顺序
		'robot':'country_pvp',		#敌军机器人
		'max_car_num':1000,		#每个国家最多建造X个攻城器械（建造出的时候加1，阵亡减1）
		'lv':{		#攻城器械等级
			'range':3,		#服务器单将战力前x名的平均值
		},
		'car':{
			'name':'500018',		#弩车
			'info':'500026',		#一句话说明
			'chart':'bg_171.jpg',		#图
			'hero':'hero826',		#英雄
			'lv_add':0,		#额外等级加成
			'install':1000,		#需要的建设度
		},
		'big_car':{
			'name':'500019',		#投石车
			'info':'500027',		#一句话说明
			'chart':'bg_170.jpg',		#图
			'hero':'hero827',		#英雄
			'lv_add':24,		#额外等级加成
			'install':10000,		#需要的建设度
		},
		'air_hammer':{		#免费的气儿锤子
			'consume':['gold',10000],		#一锤子1W银币
			'num_ini':3,		#初始给3锤,为-1时，可以无限使用，max和reply都不生效
			'num_max':10,		#最大存10锤
			'num_reply':360,		#每10分钟增加1锤【分，秒】
			'reward':{'merit':200},		#一锤子给200功勋
			'progress':100		#建设度加100
		},
		'gold_hammer':{		#做完跨服赛之后 感觉也就一般贵的大金锤
			'consume':['coin',1000],		#一锤子1Q黄金
			'num_ini':-1,		#初始给3锤,为-1时，可以无限使用，max和reply都不生效
			'num_max':10,		#最大存10锤
			'num_reply':600,		#每10分钟增加1锤【分，秒】
			'reward':{'merit':5000},		#一锤子给5000功勋
			'progress':1000		#建设度加1000
		}
	},
	'thief_three':{		#黄巾三军（只出现在襄阳战中）
		'name':'500022',		#敌军总称
		'country':17,		#国家
		'arise_time':[[20,40],[21,20]],		#X点出现敌军
		'enemy_hero':['hero828','hero829','hero830'],		#敌军英雄，完全随机
		'enemy_robot':'country',		#敌军机器人
		'enemy_troop':75,		#黄巾军加入战斗的保底部队数（一个部队=一个英雄+若干小兵）
		'enemy_troop_add':0,		#黄巾军加入战斗时，该城每个玩家驻军会额外增加黄巾军部队数
		'enemy_troop_max':75,		#黄巾军加入战斗的上限部队数，最终计算公式：min(enemy_troop_max,enemy_troop+该城玩家驻军数*enemy_troop_add)
	

		'lower_lv_0':65,		#test未合黄巾军不会低于这个等级
		'lower_lv_1':70,		#一合黄巾军不会低于这个等级
		'lower_lv_2':75,		#二合黄巾军不会低于这个等级
		'lower_lv_3':80,		#三合黄巾军不会低于这个等级
		'lower_lv_4':90,		#四合黄巾军不会低于这个等级
		'lower_lv_5':100,		#四合黄巾军不会低于这个等级
		'lower_lv_6':105,		#四合黄巾军不会低于这个等级
		'lower_lv_7':110,		#四合黄巾军不会低于这个等级
		'upper_lv':1.2,		#系统杂项中public_lv最终结果的120%，向上取整
	        'enemy_power':1.3,	        #推荐战力系数，敌人实际战力*enemy_power=推荐战力
		'elites_odds':0.2,		#每一波黄巾三军有X%的几率成为精锐黄巾三军
		'elites_lv':3,		#精锐黄巾三军等级+3
		'gen_odds':0.1,		#每一波黄巾三军有X%的几率成为黄巾三军大将
		'gen_lv':5,		#黄巾三军大将等级+5
		'interval':3,		#生成的黄巾军队列中，突变为大将或者精英的敌人，中间至少有X波普通黄巾军
		'speed':60,		#黄巾三军出现后，移动到目标城池需要60秒
		'target_city':[-5,-4,-3,-2,-1],	#黄巾三军只会入侵以下城池，当目标城池当前阵营属于黄巾军时，也会刷黄巾军（增加目标城池守军）
		'model':['hero701', 'army00s', [3, 3]],		#在世界地图中的模型,是否有模型
		'dir':[2],		#进攻方向
	},
}