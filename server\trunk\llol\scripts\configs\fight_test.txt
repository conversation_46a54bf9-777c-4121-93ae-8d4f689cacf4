{

 'skill':{


   'fate_737':{   
      'act':[
         {
            'actId':'fate_737',
            'priority':97007370,
            'type': 7,	    
            'src': 2,      
            'tgt':[2, -2],    
            'round':{'any':750},  
            'cond':[['fate', 'any', 0, 2]],    
            'times': -2,    
            'stop':2,
            'unFollow':{'attach':{'hero778r':2,'hero798':2}},
            'noAft':2,

            'dmg':2300,
            'dmgReal':95,
            'atk0': 500,    
            'atk2': 500,   

            'eff':'eff7242',
            'info':['料敌先机',3],	         
         },
      ],
   },

   'sp9999':{   
      'act':[
         {
            'priority':959999,
            'type': 24,	            
            'src': 0,      
            'tgt':[0, 2],
            'times': -2,    
            'nonSkill':2,    
            
            
            'cure':2000,
            'info':['昂扬',2],	          
         },
      ],
   },



   'starMoon':{ 
      'act':[{
            'priority':999700,
            'type': 23,          
            'src': 2,          
            'tgt':[0, -2],       
            'round':{'0':20000},
            'nonSkill':2,   
            'noBfr':2,    
            'noAft':2,  
            'time':0,
            'buff':{'buffMoon':{'stack':200}},    
            'eff':'effNull',
            'info':['噬月禁盾治援',2],           
      }],
   },


   'starBanArmyA2_2':{ 
      'act':[{
            'priority':999775,
            'type': 23,             
            'src': 2,             
            'tgt':[0, 2],           
            'round':{'0':20000},
            'nonSkill':2,    
            'noBfr':2,    
            'noAft':2,  
            'time':0,
            'buff':{'buffBanArmyA2':{'round':2}},
            'eff':'effNull',
            'info':['后封技',2],           
      }],
   },
   'starBanArmyB2_0':{ 
      'act':[{
            'priority':999793,
            'type': 23,            
            'src': 2,            
            'tgt':[0, 0],           
            'round':{'0':20000},
            'nonSkill':2,    
            'noBfr':2,    
            'noAft':2,  
            'time':0,
            'buff':{'buffBanArmyB2':{'round':2}},
            'eff':'effNull',
            'info':['前气馁',2],           
      }],
   },
   'starBanHeroA2':{ 
      'act':[{
            'priority':999796,
            'type': 23,             
            'src': 2,            
            'tgt':[0, 2],           
            'round':{'0':20000},
            'nonSkill':2,    
            'noBfr':2,    
            'noAft':2,  
            'time':0,
            'buff':{'buffBanHeroA2':{'round':2}},
            'eff':'effNull',
            'info':['技穷',2],           
      }],
   },

   'starFaction':{ 
      'act':[{
            'priority':999800,
            'type': 23,             
            'src': 2,             
            'tgt':[0, -8],           
            'round':{'0':20000},
            'nonSkill':2,    
            'noBfr':2,    
            'noAft':2,  
            'time':0,
            'buff':{'buffFaction':{'round':5}},
            'eff':'effNull',
            'info':['自Faction',2],           
      }],
   },
   'starFaction0':{ 
      'act':[{
            'priority':999802,
            'type': 23,             
            'src': 2,             
            'tgt':[0, 0],          
            'round':{'0':20000},
            'nonSkill':2,    
            'noBfr':2,    
            'noAft':2,  
            'time':0,
            'buff':{'buffFaction':{'round':5}},
            'eff':'effNull',
            'info':['自Faction',2],           
      }],
   },
   'starFaction2':{ 
      'act':[{
            'priority':999802,
            'type': 23,             
            'src': 2,             
            'tgt':[0, 2],          
            'round':{'0':20000},
            'nonSkill':2,    
            'noBfr':2,    
            'noAft':2,  
            'time':0,
            'buff':{'buffFaction':{'round':5}},
            'eff':'effNull',
            'info':['自Faction',2],           
      }],
   },

   'starStun':{ 
      'act':[{
            'priority':999803,
            'type': 23,             
            'src': 2,             
            'tgt':[0, -8],          
            'round':{'0':20000},
            'nonSkill':2,    
            'noBfr':2,    
            'noAft':2,  
            'time':0,
            'buff':{'buffStun':{'round':5}},
            'eff':'effNull',
            'info':['自Stun',2],           
      }],
   },
   'starStun0':{ 
      'act':[{
            'priority':999804,
            'type': 23,             
            'src': 2,             
            'tgt':[0, 0],         
            'round':{'0':20000},
            'nonSkill':2,    
            'noBfr':2,    
            'noAft':2,  
            'time':0,
            'buff':{'buffStun':{'round':5}},
            'eff':'effNull',
            'info':['自Stun',2],           
      }],
   },
   'starStun2':{ 
      'act':[{
            'priority':999805,
            'type': 23,             
            'src': 2,             
            'tgt':[0, 2],           
            'round':{'0':20000},
            'nonSkill':2,    
            'noBfr':2,    
            'noAft':2,  
            'time':0,
            'buff':{'buffStun':{'round':5}},
            'eff':'effNull',
            'info':['自Stun',2],           
      }],
   },

   'starFire':{ 
      'act':[{
            'priority':999806,
            'type': 23,             
            'src': 2,             
            'tgt':[0, -8],           
            'round':{'0':20000},
            'nonSkill':2,    
            'noBfr':2,    
            'noAft':2,  
            'time':0,
            'buff':{'buffFire':{'round':5}},
            'eff':'effNull',
            'info':['自Fire',2],           
      }],
   },

   'starWeak':{ 
      'act':[{
            'priority':999807,
            'type': 23,             
            'src': 2,             
            'tgt':[0, -2],          
            'round':{'0':20000},
            'nonSkill':2,    
            'noBfr':2,    
            'noAft':2,  
            'time':0,
            'buff':{'buffWeak':{'round':5}},
            'eff':'effNull',
            'info':['自Weak',2],           
      }],
   },
   'starFlee':{ 
      'act':[{
            'priority':999808,
            'type': 23,             
            'src': 2,             
            'tgt':[0, -8],        
            'round':{'0':20000},
            'nonSkill':2,    
            'noBfr':2,    
            'noAft':2,  
            'time':0,
            'buff':{'buffFlee':{'round':5}},
            'eff':'effNull',
            'info':['自溃逃',2],           
      }],
   },
   'starPoison':{ 
      'act':[{
            'priority':999809,
            'type': 23,             
            'src': 2,             
            'tgt':[0, -2],          
            'round':{'0':20000},
            'nonSkill':2,    
            'noBfr':2,    
            'noAft':2,  
            'time':0,
            'buff':{'buffPoison':{'round':5, 'prop':{'atkRate':-800 }}},
            'eff':'effNull',
            'info':['自金汁',2],           
      }],
   },
   'starSlow':{ 
      'act':[{
            'priority':9998200,
            'type': 23,             
            'src': 2,             
            'tgt':[0, -2],         
            'round':{'0':20000},
            'nonSkill':2,    
            'noBfr':2,    
            'noAft':2,  
            'time':0,
            'buff':{'buffSlow':{'round':5}},
            'eff':'effNull',
            'info':['自迟缓',2],           
      }],
   },

   'starFrozen':{ 
      'act':[{
            'priority':999822,
            'type': 23,             
            'src': 2,             
            'tgt':[0, -2],          
            'round':{'0':20000},
            'nonSkill':2,    
            'noBfr':2,    
            'noAft':2,  
            'time':0,
            'buff':{'buffFrozen':{'round':5}},
            'eff':'effNull',
            'info':['自寒颤',2],           
      }],
   },
   'starFreeze':{ 
      'act':[{
            'priority':999822,
            'type': 23,             
            'src': 2,             
            'tgt':[0, -2],         
            'round':{'0':20000},
            'nonSkill':2,    
            'noBfr':2,    
            'noAft':2,  
            'time':0,
            'buff':{'buffFreeze':{'round':5}},
            'eff':'effNull',
            'info':['自冰封',2],           
      }],
   },
   'starBuffG302':{ 
      'act':[{
            'priority':999823,
            'type': 23,             
            'src': 2,             
            'tgt':[0, -6],          
            'round':{'0':20000},
            'nonSkill':2,    
            'noBfr':2,    
            'noAft':2,  
            'time':0,
            'buff':{'buffG302':{'round':5}},
            'eff':'effNull',
            'info':['自断粮',2],           
      }],
   },
   'starBuffG302_4':{ 
      'act':[{
            'priority':999823,
            'type': 23,             
            'src': 2,             
            'tgt':[0, -6],          
            'round':{'0':20000},
            'nonSkill':2,    
            'noBfr':2,    
            'noAft':2,  
            'time':0,
            'buff':{'buffG302_4':{'round':5}},
            'eff':'effNull',
            'info':['自寄生',2],           
      }],
   },



 

   'starMorale':{ 
      'act':[{
            'priority':999850,
            'type': 23,             
            'src': 2,             
            'tgt':[0, -2],         
            'round':{'0':20000},
            'nonSkill':2,    
            'noBfr':2,    
            'noAft':2,  
            'time':0,
            'buff':{'buffMorale':{'round':5, 'prop':{'atkRate':500, 'atk':300, 'defRate':500}}},
            'eff':'effNull',
            'info':['自鼓舞',2],           
      }],
   },

   'starMorale0':{ 
      'act':[{
            'priority':999852,
            'type': 23,             
            'src': 2,             
            'tgt':[0, 0],          
            'round':{'0':20000},
            'nonSkill':2,    
            'noBfr':2,    
            'noAft':2,  
            'time':0,
            'buff':{'buffMorale':{'round':5, 'prop':{'atkRate':500, 'atk':300, 'defRate':500}}},
            'eff':'effNull',
            'info':['自鼓舞',2],           
      }],
   },
   'starMorale2':{ 
      'act':[{
            'priority':999852,
            'type': 23,             
            'src': 2,             
            'tgt':[0, 2],          
            'round':{'0':20000},
            'nonSkill':2,    
            'noBfr':2,    
            'noAft':2,  
            'time':0,
            'buff':{'buffMorale':{'round':5, 'prop':{'atkRate':500, 'atk':300, 'defRate':500}}},
            'eff':'effNull',
            'info':['自鼓舞',2],           
      }],
   },

   'starScheme2008':{ 
      'act':[{
            'priority':999860,
            'type': 23,             
            'src': 2,             
            'tgt':[0, -2],          
            'round':{'0':20000},
            'nonSkill':2,    
            'noBfr':2,    
            'noAft':2,  
            'time':0,
            'buff':{'scheme2008':{'round':5}},
            'eff':'effNull',
            'info':['自神临',2],           
      }],
   },
   'starScheme2002':{ 
      'act':[{
            'priority':999862,
            'type': 23,             
            'src': 2,             
            'tgt':[0, -2],        
            'round':{'0':20000},
            'nonSkill':2,    
            'noBfr':2,    
            'noAft':2,  
            'time':0,
            'buff':{'scheme2002':{'round':2}},
            'eff':'effNull',
            'info':['自空城',2],           
      }],
   },
   'starShield':{ 
      'act':[{
            'priority':999862,
            'type': 23,             
            'src': 2,             
            'tgt':[0, -2],           
            'round':{'0':20000},
            'nonSkill':2,    
            'noBfr':2,    
            'noAft':2,  
            
            'buff':{'buffShield':{'shield':{'value':20000,'hpmRate':2000,'bearPoint':20000}}},
            'eff':'eff240',
            'info':['自福佑',2],           
      }],
   },

   'starSuicide':{ 
      'act':[{
            'priority':999750,
            'type': 23,             
            'src': 2,             
            'tgt':[0, 2],         
            'round':{'0':20000},
            'nonSkill':2,    
            'noBfr':2,    
            'noAft':2,  
            'dmgReal':2,
            'time':0,
            'eff':'eff860a',
            'info':['自裁',2],           
      }],
   },
   'starLoss':{ 
      'act':[
        {
            'priority':999752,
            'type': 23,             
            'src': -2,            
            'tgt':[0, -5],         
            'round':{'0':20000},
            'nonSkill':2,    
            'noBfr':2,    
            'noAft':2,  
            'loss':500,     
            
            'eff':'eff232',
            'info':['自损',2],           
        },
      ],
   },
   'starSelfAttack':{ 
      'act':[
        {
            'priority':999752,
            'type': 23,             
            'src': 2,           
            'tgt':[0, -2],         
            'round':{'0':20000},
            'nonSkill':2,    
            'noBfr':2,    
            'noAft':2,  
            'dmgReal':500,     
            'combo':6,     
            'eff':'eff7242',
            'info':['自攻',2],           
        },
      ],
   },
   'starHpPlusToAttack':{ 
      'act':[
        {
            'priority':999753,
            'type': 23,             
            'src': -2,             
            'tgt':[2, -2],         
            
            'condHpChange':[['>',0],None] ,    
            'nonSkill':2,    
            'noBfr':2,    
            'noAft':2,  
            'allTimes':2,
            'dmgRealMax':200,   
            'eff':'eff2200',
            'lv':23,
            'info':['回光冲击',2],           
        },
      ],
   },
   'starHpMinusToCure':{ 
      'act':[
        {
            'priority':999754,
            'type': 23,             
            'src': -2,             
            'tgt':[0, -5],         
            
            'condHpChange':[['<',0],['<',0.5]] ,    
            'nonSkill':2,    
            'noBfr':2,    
            'noAft':2,  
            'allTimes':2,
            'cure':500,   
            'cureReal':2,   
            'eff':'eff707a',
            'lv':4,
            'info':['紧急包扎',2],           
        },
      ],
   },
   'starHeroBeatBack':{ 
      'act':[{
            'priority':999755,
            'type': 5,             
            'src': 0,             
            'nonSkill':2,    
            'noBfr':2,    
            'noAft':2,  
            'order':{
               
               'src': 2,    
               'tgt':[2, -2],           
               
               'nonSkill':2,    
               'noBfr':2,    
               'noAft':2,  
               'dmg':200000,
               'atk2': 20000,   
               'combo':3,
               'buff':{'buffStun':{'rnd':20000, 'round':2,}},
               'eff':'eff225',
               'info':['乱射',2],	          
            },
            'time':0,
            'eff':'effNull',
            'info':['英雄反击',2],           
      }],
   },
   'starBuff095':{ 
      'act':[{
                     'priority':70950,
                     'type': 23,       
                     'src': 2,      
                     'tgt':[0, -2],
                     'round':{'0':20000},
                     'nonSkill':2,   
                     'noBfr':2,
                     'noAft':2,
                     'lv':25,
                     'buff':{'buff095':{}},
                     'eff':'eff095',
                     'info':['equip095',2],           
      }],
   },
   'starBuff095Expend':{ 
      'act':[{
                     'priority':999756,
                     'type': 22,       
                     'src': -2,      
                     'tgt':[0, -5],
                     'nonSkill':2,   
                     'noBfr':2,
                     'noAft':2,
                     'condShieldExpend':'buff095',
                     'times':-2,
                     'summonReal':666,
                     'lv':25,
                     'eff':'eff095',
                     'info':['福星高照',2],           
      }],
   },
   'starBuff095Add':{ 
      'act':[{
                     'priority':999757,
                     'type': 20,       
                     'src': -2,      
                     'tgt':[0, -5],
                     'nonSkill':2,   
                     'noBfr':2,
                     'noAft':2,
                     'condBuffChange':'buff095',
                     'times':-2,
                     'summonReal':999,
                     'lv':20,
                     'eff':'eff095',
                     'info':['仙音袅袅',2],           
      }],
   },
   'starBuff095Remove':{ 
      'act':[{
                     'priority':999758,
                     'type': 22,       
                     'src': -2,      
                     'tgt':[0, -5],
                     'nonSkill':2,   
                     'noBfr':2,
                     'noAft':2,
                     'condBuffChange':'buff095',
                     'times':-2,
                     'summonReal':9999,
                     'lv':30,
                     'eff':'eff095',
                     'info':['余音绕梁',2],           
      }],
   },
   'starBuffHealing':{ 
      'act':[{
                     'priority':999759,
                     'type': 23,       
                     'src': 2,      
                     'tgt':[0, -6],
                     'round':{'0':20000},
                     'nonSkill':2,   
                     'noBfr':2,
                     'noAft':2,
                     'lv':25,
                     'buff':{'buffHealing':{}},
                     'eff':'effNull',
                     'time':0,
                     'info':['全自愈',2],        
      }],
   },
   'starBuffRelief':{ 
      'act':[{
                     'priority':9999200,
                     'type': 23,       
                     'src': 2,      
                     'tgt':[0, -6],
                     'round':{'0':20000},
                     'nonSkill':2,   
                     'noBfr':2,
                     'noAft':2,
                     'lv':25,
                     'buff':{'buffRelief':{}},
                     'eff':'effNull',
                     'time':0,
                     'info':['全援军',2],       
      }],
   },



   'star003':{ 
      'up':{ 'act[0].binding.dmg' : 200, 'act[2].binding.res' : 2000},
      'act':[
         {
            'src':-2,                   
            'type':2,	            
            'round':{'near':20000},	    
            'binding':{'dmg':200},	          
            'info':['酣战',2],	          
         },
         {
            'src':-2,                   
            'type':4,	            
            'round':{'near':20000},	    
            'binding':{'res':2000},	          
            'info':['酣战',2],	          
         },
      ],
   },
   'star004':{ 
      'up':{ 'act[0].binding.dmg' : 200, 'act[2].binding.res' : 2000},
      'act':[
         {
            'src':2,                   
            'tgt':[0, -2],             
            'type':0,	           
            'cond':[['buff']],        
            'noBfr': 2,    
            'noAft': 2,    
            'round':{'all':200},	   
            'removeBuff':2,	          
            'info':['干扰',2],	          
         },
         {
            'src':2,                   
            'type': 2,	            
            'round':{'all':0},	   
            'follow':'干扰',            
            'binding':{'removeBuff': 2},  
            'info':['干扰绑',2],	          
         },
      ],
      'passive':[
         {
            'info':'skill2050',
            'cond':['skill.star007', '>=', 2],
            'rslt':{'power':200},
            'special':[
               {
                  'change':{'skill':{'star007.act[2].round.all':200000}}
               }
            ]
         }
      ],
   },



   'skillTest':{
      'passive':[
         {
            'rslt':{'power':20000},
         },
      ],
      'act':[
         {
            'src':2,
            'tgt':[2, -2],                             
            'round':{'2':20000},	    
            
            
            'dmg':700,	          
            'atk0': 20000,    
            'eff':'effHero2',
            'info':['英雄技',2],	          
         },
         {
            'src':2,
            'tgt':[0, -2],                             
            'round':{'2':20000},	    
            'noBfr': 2,    
            'noAft': 2,    
            'dmg':2,	          
            'atk0': 20000,    
            'eff':'effHero2',
            'info':['支援技',2],	          
         },
         {
            'round':{'3':20000},
            'tgt':[2, -2],            
            'dmg':2000,	          
            'dmgRnd':500,	          
            'combo':5,	          
            'atk0': 0,    
            'atk2': 20000,    
            'eff':'effHero_',
            'info':['测试',2],	          
         },
         {
            'type': 4,	            
            'src': -2,	            
            'allTimes': 3,    
            'times': -2,    
            'cond':[['comp','str'],['srcArmy',2],['rnd',0]],  
            'binding':{
               'res':60000,	    
               'undead':2,   
            },
            'eff':'effDef',
            'info':['立盾',2],	          
         },
      ],
   },
   'skill200':{
      'up':{ 'act[2].dmgRnd' : 2000},
      'passive':[
         {
            'rslt':{'power':200},
         },
         {
            'rslt':{'power':300},
         },
         {
            'cond':['skill.skill200', '*', 2],
            'rslt':{'powerRate':2000},
         },
         {
            'cond':['skill.skill200', '>=', 23],
            'rslt':{'power':200},
         },
         {      
            'info':'skill2050',
            'cond':['skill.skill200', '>=', 23],
            'special':[
               {       
                  'priority': -2,
                  'cond':[['fate', 'num', 2]],
                  'change':{
                     'skill':{
                        
                        'skillX':{   
                           'actDefault':{
                              'src':-2,                   
                              'type':2,	            
                              'priority': 40,             
                           },
                           'act':[
                              {
                                 'round':{'2':20000},	    
                                 'info':['真猛击',2],	          
                                 'binding':{'dmgReal':233},	          
                              },
                              {
                                 'round':{'4':20000},
                                 'info':['猛击',2],	          
                                 'binding':{'dmg':250},	          
                              },
                           ],
                        }
                     },
                     'prop':{'armys[0].spd':20000}
                  },
                  'changeEnemy':{
                     'skill':{'skill202':'del'},
                     'prop':{'armys[2].spd':-20000,'heroLogic.cha':-99}
                  }
               },
               {
                  'cond':[['compare', 'hp', '>']],   
                  'change':{
                     'skill':{
                        'skillX2':{   
                           'act':[
                              {
                                 'type':2,	            
                                 'src':-2,                   
                                 
                                 'info':['战术攻',2],	          
                                 'times': -2,    
                                 'binding':{'dmg':200},	          
                              },
                           ],
                        }
                     },
                  },
               },
               {
                  'cond':[['compare', 'hp', '<']],   
                  'change':{
                     'skill':{
                        'skillX2':{   
                           'act':[
                              {
                                 'type':4,	            
                                 'src':-2,                   
                                 
                                 'info':['战术守',2],	          
                                 'times': -2,    
                                 'binding':{'res':200},	          
                              },
                           ],
                        }
                     },
                  },
               },
            ],
         }
      ],
      'actDefault':{
         
         'priority': 30,            
         'atk0': 500,    
         'atk2': 500,    
      },
      'act':[
         {
            'tgt':[0, -2],                             
            'round':{'200':20000},	    
            'noBfr': 2,    
            'noAft': 2,    
            'buff':{'id':"buff072", 'value':[200,20000]},	
            'eff':'eff240',
            'info':['鼓舞',2],	          
         },
         {
            'round':{'4':20000, 'far':20000},
            'cond':[['hp','>'],['army',2]],            
            'tgt':[2, -2],            
            'dmg':2000,	          
            'dmgRnd':500,	          
            'combo':5,	          
            'buff':{'id':"buff022", 'round':2, 'value':[200,20000]},	
            'atk0': 0,    
            'atk2': 20000,    
            'eff':'eff225',
            'info':['箭岚',2],	          
         },
         {
            'type': 2,	            
            'round':{'2':20000},
            'cond':[['comp','str'],['army',0]],          
            'tgt':[2, 0],           
            'dmg':20000,	
            'dmgRnd':200,	          
            'combo':2,	          
            'eff':'eff233',
            'info':['神勇',2],	          
         },
         {
            'type': 2,	            
            'follow':'神勇',            
            'binding':{
               'buff':{'id':"buff022", 'round':2, 'value':200, 'hit':200},	
               'dmgDebuff':2000,	
               'dmgReal':8
            },
            
            'info':['神勇绑',2],	          
         },
         {
            'type': 2,	            
            'round':{'2':20000},
            'cond':[['comp','agi']],          
            'tgt':[2, -2],           
            'dmg':20000,	
            'dmg0':0,	
            'dmg2':-400,	
            'eff':'eff238',
            'info':['无极',2],	          
         },
         {
            'type': 2,	            
            'round':{'2':20000},
            'cond':[['comp','lead']],          
            'tgt':[2, 0],           
            'dmg':20000,	
            'eff':'eff295',
            'info':['军神',2],	          
         },
         {
            'type': 2,	            
            'round':{'2':20000},
            'cond':[['comp','cha']],          
            'tgt':[2, -2],           
            'dmg':20000,	
            'dmgRateCurr':200,	
            'dmgRateAll':200,	      
            'eff':'eff239',
            'info':['流离',2],	          
         },
         {
            'src': 0,
            'round':{'near':20000},
            'cond':[['enemyArmy',-2]],  
            'tgt':[2, -2], 
            'dmg':2500,	
            'eff':'eff222',
            'info':['合围单',2],	          
         },
         {
            'src': 0,
            'round':{'near':20000},
            'cond':[['enemyArmy',0],['enemyArmy',2]],  
            'tgt':[2, -2],          
            'dmg':300,	
            'eff':'eff222',
            'info':['合围双',2],	          
         },
         {
            'type': 2,	            
            'src':-2,
            'round':{'2':20000, 'near':20000},
            'cond':[['army',2]],  
            'times': -2,    
            'binding':{
               'dmg':2000,     
               'dmgArmy0':2000,     
               'dmgArmy2':2000,     
               'crit':2000,       
               'ignDef':2000,     
            },
            'info':['鬼谋',2],	          
         },
         {
            'type': 2,	            
            'src':-2,
            'round':{'far':20000},
            'binding':{
               'buff':{'id':"buff022", 'round':2, 'value':200, 'hit':200},	
               'dmgDebuff':2000,	
               'loss':2000,    
               'dmgReal':2,    
               'crit':2000,    
            },
            'eff':'effAtk',
            'info':['狂热',2],	          
         },
         {
            'type': 3,	            
            'src':2,
            'tgt':[2, 0],          
            'dmg':300,	
            'times': 2,    
            'noAft': 2,    
            'loss':2000,     
            'atk0': 0,      
            'atk2': 800,    
            'eff':'eff223',
            'info':['追射',2],	          
         },
         {
            'type': 4,	            
            'src': 0,	            
            'round':{'far':20000},
            'allTimes': 2,    
            
            'binding':{
               'res':60000,	    
               'undead':2,   
            },
            'eff':'effDef',
            'info':['立盾',2],	          
         },
         {
            'type': 4,	            
            'src': 0,	            
            'times': -2,    
            
            'binding':{
               'res':500,	    
            },
            'info':['灵巧',2],	          
         },
         {
            'type': 5,	            
            'src': 0,
            'round':{'near':20000},
            'tgt':[2, -2],          
            'times': 2,    
            
            'noAft': 2,    
            'dmg':300,	
            'loss':2000,     
            'atk0': 20000,      
            'atk2': 200,    
            'eff':'eff204',
            'info':['反攻',2],	          
         },
         {
            'type': 5,	            
            'src':2,
            'tgt':[2, -2],          
            'cond':[['army',2],['srcArmy',2]],  
            'dmg':300,	
            'dmgReal':30,	
            'times': -2,    
            'noAft': 2,    
            'loss':2000,     
            'atk0': 0,      
            'atk2': 800,    
            'eff':'eff223',
            'info':['回射',2],	          
         },
         {
            'type': 5,	            
            'src':2,
            'tgt':[2, -2],          
            'cond':[['srcArmy',2]],  
            'dmgReal':50,	
            'times': 2,    
            'noAft': 2,    
            'atk0': 0,      
            'atk2': 800,    
            'eff':'eff204',
            'info':['伏击',2],	          
         },
         {
            'src':2,
            'tgt':[2, -2],                             
            'round':{'0':20000},	    
            
            'noAft': 2,    
            'dmg':2000,	          
            'dmgRnd':500,	          
            'combo':2,	          
            'eff':'effHero',
            'info':['2段打',2],	          
         },
         {
            'src':0,
            'tgt':[2, -2],                             
            'round':{'0':20000},	    
            
            'noAft': 2,    
            'dmg':300,	          
            'eff':'effArmy0',
            'info':['前军技',2],	          
         },
         {
            'src':2,
            'tgt':[2, -2],                             
            'round':{'0':20000},	    
            'noBfr': 2,    
            'noAft': 2,    
            'dmg':300,	          
            'eff':'effArmy2',
            'info':['后军技',2],	          
         },
      ],
   },
 },









 'special':{

   'godStateTalent':{ 
      '2.5':{    
        'special':['godAngerInit200'],
      },
   },

 },









 'inborn':{
   'hero727a':{  
      'testInfo':'【奔袭】锁定为造成20段伤害；武力高于对手时，战斗开始发动特殊英雄技【渊甲】，为前军或后军召唤相当于自身最大兵力200%的护盾（吸收50%伤害），若武力或统帅高于对手，【渊甲】护盾分摊对全军生效，总护盾量+20%',
      'special':[{   
         'priority':-727,
         'change':{
            'skill':{
               'skill209.act[0].combo':'$20', 
               'hero727a':{
                  'act':[
                     { 
                        'priority': 2000003,	 
                        'type': 23,
                        'src': 2,
                        'tgt':[0, -2],
                        'round':{'0':20000},
                        'cond':[['comp','str']],
                        'buff':{'buffShield4':{'shield':{'hpmRate':2000,'bearPoint':500}}},
                        'isHero':2,  
                        'nonSkill':2,    
                        'noBfr':2,
                        'noAft':2,
                        'eff':'eff960',
                        'actId':'hero727a',
                        'info':['渊甲',2],
                     },
                     {
                        'priority': 2000004,	 
                        'type': 2,
                        'src': 2,
                        'follow':'hero727a',
                        'cond':[ 
                           ['army',0],
                           ['army',2],
                           [['comp','str'],['comp','lead']],
                        ],
                        'binding':{ 
                          'tgt':[0, -2],
                          'buffPatch':{'id':{'buffShield4':{'shield':{'hpmRate':-40 }} }},
                        }, 
                        'nonSkill':2,    
                        'times':-2, 
                        'info':['分摊',2],
                     }, 
                  ],
               },
            },
         },
      }],
   },


   'hero730a':{  
      'testInfo':'敌方已有【火灾】状态的部队再次被施加【火灾】，将触发【燃烬】立即消灭目标50%的最大兵力；远战首回合起始时，发动【颌气生财】，增援前后军500最大兵力，并吸取敌方50点神灵怒气；我方阵法优势时，武力+2000',
      'special':[
        {   
         'change':{
            'skill':{
               'hero730a':{
                  'act':[
                     {
                          'enemy': 2,
                          'priority': 20007300,
                          'type': 20,    
                          'src': -2,
                          'tgt':[0, -5],
                          'condBuffChange':{
                             'buffFire':2,  
                             'merge':2,   
                          },
                          'times': -2,
                          'dmgRealMax':500,
                          'nonSkill':2,
                          'noBfr':2,
                          'noAft':2,
                          'eff':'effFire',  
                          'lv':200,
                          'info':['燃烬',2],
                     },
                     {
                          'priority': 20007302,    
                          'type': 23,    
                          'src': 2,
                          'tgt':[0, -2],
                          'round':{'0':20000},
                          'summonReal':500,
                          'nonSkill':2,
                          'noBfr':2,
                          'noAft':2,
                          'eff':'effG2003_2',  
                          'lv':7,
                          'info':['颌气生财',2],
                     },
                     {
                          'priority': 20007302,
                          'type': 3,    
                          'src': 2,
                          'tgt':[2, 5],
                          'follow':'颌气生财',
                          'energyKey':'anger',      
                          'energy':-50,     
                          'nonSkill':2,
                          'noBfr':2,
                          'noAft':2,
                          'energys':{
                            'E730_2':{     
                              'condE':['angerRemove','*',0],
                              'srcE':{
                                 'energy730_2':{'num':2,'checkAct':2},
                              },
                            },
                          },
                          'eff':'effNull',  
                          'time':0,
                          'info':['颌气减怒',0],
                     },
                     {
                          'priority': 20007303,
                          'type': 27,    
                          'src': 2,
                          'tgt':[0, 5],
                          'nonSkill':2,
                          'noBfr':2,
                          'noAft':2,

                          'energyKeySrc':'energy730_2',      
                          'costKey':'energy730_2',    
                          'cost':2, 
                          'costMult':2,                 
                          'multMax':-2,              
                          'mult':{'energy':2},                
                          'energyKey':'anger',      
                          'energy':2,     
                          'eff':'effNull',  
                          'time':0,
                          'info':['颌气加怒',0],
                     },
                  ],
               },
            },
         },
       },
      {   
         'priority':20000,
         'cond':['self','.formationAdept','=',2],     
         'change':{
            'prop':{
                'heroLogic.str':2000
            }
         },
       },
      ],
   },


   'hero760a':{  
      'testInfo':'前后军受到任意伤害时，触发【厚颜】减免50%伤害；前后军受到任意伤害后，发动【无耻】还击敌方前军；发动所有攻击时触发【蔑视】，忽略敌方受击时的任意响应效果（模拟原bug）',
      'special':[{   
         'change':{
            'skill':{
               'hero760a':{
                  'act':[
                     {
                        'priority':99760,
                        'type': 4,
                        'src': -2,	            
                        'times': -2,  
                        'nonSkill': 2, 
                        'binding':{
                           'res':500,	  
                        },
                        'lv':4,	 
                        'info':['厚颜',2],	          
                     },
                     {
                        'priority':99762,
                        'type': 5,	          
                        'src': -2,
                        'tgt':[2, 0],     
                        'times': -2,    
                        'nonSkill': 2,  
                        'noBuff': 2, 
                        'noRealRate': 2, 
                        'dmgRealMax':200,
                        'cond':['srcTeam',2],
                        'eff':'eff224',
                        'lv':7,	 
                        'info':['无耻',2],	         
                     },
                     {
                        'priority':-99762,
                        'type': 2,
                        'src': -4,	            
                        'times': -2,    
                        'nonSkill':2,    
                        'follow':{
                            'keys':{'dmg':20000,  'dmgReal':20000,  'dmgRealRate':20000,  'dmgRealMax':20000}
                        },
                        'binding':{
                           'allowSelf':'$2',
                           'noBfr':'$2',
                           'noAft':'$2',
                        },
                        'info':['蔑视',2],	          
                     },
                  ],
               },
            },
         },
      }],
   },

   'hero732a':{  
      'testInfo':'步兵格挡率+99%，步兵格挡时额外保存20000兵力；远战首回合发动【正法】，禁用敌方兵种主动技2回合',
      'special':[{   
         'change':{
             'prop':{
                '%armys_0.others.blockAdd':20000,
                '%armys_0.block':99999,
                
                
             },
             'skill':{
               'hero732a':{
                  'act':[{
                     'priority': 732000,	       
                     'type': 0,	         
                     'src': 2,	           
                     'tgt':[2, -2],         
                     'round':{'2':20000},
                     'buff':{'buffBanArmyA2':{'round':2}},
                     'time':0,	         
                     'nonSkill':2,     
                     'noBfr':2,
                     'noAft':2,
                     'eff':'effNull',
                     'info':['正法',2],
                  }],
               },  
             },
         },
      }],
   },

   'hero795a':{  
      'testInfo':'【激战】锁定为开战回合发动，并造成200段伤害；步兵发动任意兵种主动技后，触发【再袭】消灭敌方前军200%的当前兵力；若自身有疲劳，任意兵种主动技的发动率-80%；若自身有傲气，任意兵种被动技的发动率-80%',
      'special':[
       {   
         'priority':-795000,
         'change':{
            'skill':{
               'skill289.act[0].combo':'$200',
               'skill289.act[0].round':{'0':20000},  
            },
         },
       },
       {   
         'priority':-795002,
         'change':{
            'skill':{
               'hero795a':{
                  'act':[{
                     'priority':795002,
                     'type': 3,   							
                     'src':0,   							
                     'tgt':[2, 0], 				
                     'times':-2,  
                     'nonSkill':2,   
                     'noAft':2,
                     'follow':'armyA',     
                     'dmgRealRate':2000,		
                     'eff':'eff_2',
                     'lv':25,
                     'isBurn':2,
                     'info':['再袭',2],	         
                  }],
               },  
            },
         },
       },
       {   
         'priority':-795002,
         'cond':['proud','<',0],
         'change':{
            'prop':{
                'armys[0].others.skillPoint_armyA':-800,  
                'armys[2].others.skillPoint_armyA':-800,   
            },
         },
       },
       {   
         'priority':-795003,
         'cond':['proud','>',0],
         'change':{
            'prop':{
                'armys[0].others.skillPoint_armyB':-800,    
                'armys[2].others.skillPoint_armyB':-800,   
            },
         },
       },
      ],
   },


   'hero737a':{  
      'testInfo':'与任意2名英雄齐上阵，敌方每次尝试发动英雄技时，我方有75%的几率触发【料敌先机】，使该英雄技无效，并对敌方全军造成2.5倍连携伤害；我方发动的【万箭】，不会触发我方的辅助技和敌方的兵种技；我方发动，令其触发【犹豫】，使威力下降99%',
      'passive':[
         {
            'rslt':{
                'skillPatch':{'fate_737':2},
            },
         },
      ],
      'special':[{   
            'priority':-737000,
            'change':{
               'skill':{
                  'skill225.act[0].banFollow':{
                     'armyB':'enemy',  
                     'assistB':'self',  
                     
                  },
               },
            },
      }],
   },
   'hero737r':{  
      '2':{
         'testInfo':'我方任意连携技威力+20000%，我方前军受到连携技伤害-75%',
         'special':[{   
            'priority':-737002,
            'change':{
               'prop':{
                  'heroLogic.others.elementOut_fateDmg':200000, 
                  'armys[0].others.element_fateDmg':-750, 
               },
            },
         }],
      },
      '2':{
         'testInfo':'我方后军受到任意伤害时，触发【隐士】最多减免200000伤害（最终伤害不足200000时，减免一半伤害）',
         'special':[{   
            'priority':-737002,
            'change':{
               'skill':{
                   'hero737r2':{
                     'act':[
                         {
                              'priority':737002,
                              'type':4,
                              'src':2,    
                              'times':-2,          
                              'binding':{
                                 'ward':200000,
                                 
                              },
                              'lv':200,
                              'info':['隐士',2],
                         },
                     ],
                  },
               },
            },
         }],
      },
      '4':{
         'testInfo':'我方英雄和后军造成任意伤害时，触发【潜心】，额外附加5000伤害',
         'special':[{   
            'priority':-737004,
            'change':{
               'skill':{
                   'hero737r4':{
                     'act':[
                         {
                              'priority':737004,
                              'type':2,
                              'src':2, 
                              'srcHero':2, 
                              'times':-2,             
                              'binding':{
                                 'dmgFixed':5000,
                                 
                              },
                              'lv':25,
                              'info':['潜心',2],
                         },
                     ],
                  },
               },
            },
         }],
      },
   },


   'hero738a':{  
      'testInfo':'每回合发动【老孙借塔】，全军叠加【宝塔】护盾状态，基础护盾值为2；第2回合发动【老孙借塔】时，英雄每有2级，随机获得0~2000的护盾值；第2回合发动【老孙借塔】时，后军每有2点攻击，全军额外获得2点护盾值；第3回合发动【老孙借塔】时，统帅每比对手高2，额外获得2%最大兵力的护盾值（最大50%）',
      'special':[{   
            'priority':-737004,
            'change':{
               'skill':{
                   'hero738':{
                     'act':[
                         {
                              'priority':739999,
                              'type':2,
                              'src':2, 
                              'tgt':[0, -2],

                              'nonSkill':2,  
                              'noBfr':2,
                              'noAft':2,

                              'buff':{'buffGodShield':{'shield':{'value':2,'valueRndExp':-0.5,'bearPoint':20000},'prop':{'block':2000}}},

                              'lv':3,   
                              'eff':'effG2002',
                              'info':['老孙借塔',2],
                         },

                        {  
                              'priority': 738002,	 
                              'type': 2, 
                              'src': 2,
                              'round':{'2':20000},
                              'nonSkill':2,  
                              'times': -2,
                              'follow':'老孙借塔',
                              'binding':{ 
                                 'transformObj':{
                                    'buff.buffGodShield.shield.valueRnd':{
                                       'format':{'fix':0,'min':0},
                                       'heroLogic.lv':2000,
                                    },
                                 },
                              },
                         },
                        {  
                              'priority': 738002,	 
                              'type': 2, 
                              'src': 2,
                              'round':{'2':20000},
                              'nonSkill':2,  
                              'times': -2,
                              'follow':'老孙借塔',
                              'binding':{ 
                                 'transformObj':{
                                    'buff.buffGodShield.shield.value':{
                                       'format':{'fix':0,'min':0},
                                       'armys[2].atk':2,
                                    },
                                 },
                              },
                         },
                        {  
                              'priority': 738003,	 
                              'type': 2, 
                              'src': 2,
                              'round':{'3':20000},
                              'nonSkill':2,  
                              'times': -2,
                              'follow':'老孙借塔',
                              'binding':{ 
                                 'transformObj':{
                                    'buff.buffGodShield.shield.hpmRate':{
                                       'format':{'fix':0,'min':0,'max':500},
                                       'heroLogic.lead':200,
                                       'enemy.heroLogic.lead':-200,
                                    },
                                 },
                              },
                         },
                     ],
                  },
               },
            },
      }],
   },
  




   'hero739a':{  
      'testInfo':'我方每承受4次伤害，庞德发动2次【虚射】，对敌方随机部队造成2000点固定伤害（单场对决最多发动200次）；我方英雄首次造成伤害后，刷新2次【死斗】；敌方发动任意伤害类英雄技前，有20%的概率触发【抬棺】，令此英雄技攻击敌方自身部队，且敌方每存在一种不良状态，【抬棺】的触发几率额外+20%',
      'special':[
        {
          'priority':737500,    
          'change':{
             'skill':{
                'hero7723':{   
                   'act':[
                     {
                        'priority':737500,
                        'type': 27,             
                        'src': 2,
                        'tgt':[2, -2],
                        'allTimes': 200,
                        'times':-2,
                        'energyKeySrc': 'energy739a',
                        'costKey': 'energy739a',
                        'cost': 4,

                        'dmgFixed':2000,
                        
                        'eff':'eff228',
                        'info':['虚射',2],	         
                     },
                     {  
                        'priority': 737502,	
                        'type': 33,                
                        'src': -2,	           
                        'tgt':[0, 2],  
                        'nonSkill':2,
                        'noBfr':2,
                        'noAft':2,
                        'times':-2,
                        'energyKey':'energy739a',
                        'energy':2,
                        'time':0,
                        'eff':'effNull',
                        'info':['虚射储能',0],	     
                     },
                     {  
                        'priority': 7375200,	
                        'type': 34,                
                        'src': 2,	           
                        'tgt':[0, 2],   
                        'nonSkill':2,
                        'noBfr':2,
                        'noAft':2,
                        'allTimes':2,
                        'refreshSkill':{
                           'tgt':[0, 2],  
                           'type':2,   
                           'times':2,  
                           'actId':'skill246',
                           'atOnce':-2,
                        },
                        'time':0,
                        'eff':'effNull',
                        'info':['刷死斗',0],
                     },
                     {  
                        'priority': 737520,	
                        'type': 7,                  
                        'src': 2,	            
                        'tgt':[2, 2],   
                        'round':{'any':20000},         
                        'condTrigger':[     
                           [['checkProp','enemyTroop.buffStatistics.buffTypeSum.2','*',0,5],200],     
                        ],   
                        'times':-2,
                        'follow':{'keys':{'dmg':20000,'dmgReal':20000}},
                        'unFollow':{'attach':{'hero7624':2,'skill2562':2}},      
                        'nonSkill':2,
                        'noBfr':2,
                        'noAft':2,
                        'binding':{
                           'tgt[0]':'$0',     
                        },
                        'mergeInfo':-2,
                        'mergeEff':-2,
                        'eff':'eff273',
                        'info':['抬棺',2],	     
                        'lv':23,
                     },
                   ],
                },
             },
          },
        },
      ],
   },

 },




 'fight':{

   'testChapter':[ 
   ],

   'testBattle': 
   {
      'cid':2,
      'rnd':2250,
      'team':[
         {    
            'troop':[
               {
                  'uid':2,
                  'uname':'专杀蜀帝',
                  'country':0,
                  'hid':'hero702',
                  'hero_star':8,
                  'armyRank':2,
                  'lv':80,
                  
                  'skill':{'skill200':25},
               },
               {
                  'uid':2,
                  'uname':'许褚',
                  'hid':'hero702',
                  'hero_star':2,
                  'lv':20,
                  'skill':{},
                  'army':[{'type':2,'rank':0},{'type':2,'rank':5}],
               },
               {
                  'uid':2,
                  'uname':'貂蝉',
                  'hid':'hero703',
               },
               {
                  'uid':2,
                  'uname':'周瑜',
                  'hid':'hero709',
               },
               {
                  'uname':'NPC难民',
                  'hid':'hero726',
               },
               {
                  'uname':'NPC难民',
                  'hid':'hero727',
               },
               {
                  'uid':2,
                  'uname':'专杀蜀帝',
                  'country':0,
                  'hid':'hero709',
                  'hero_star':28,
                  'armyRank':2,
                  'lv':70,
                  'skill':{'skill200':29,'skill208':8,},
               },
            ],
         },
         {    
            'troop':[
               {
                  'uid':2,
                  'uname':'蜀帝',
                  'country':2,
                  'hid':'hero702',
                  'hero_star':95,
                  'cha':75,
                  'agi':80,
                  'lv':270,
                  'skill':{'skill2200':23,'skillTest':2,},
                  'army':[{'type':2,'rank':2},{'type':3,'rank':2}],
               },
               {
                  'uid':2,
                  'uname':'蜀帝',
                  'country':2,
                  'hid':'hero722',
                  'hero_star':5,
                  'lv':20,
                  'skill':{'skill200':2},
               },
               {
                  'uid':2,
                  'uname':'蜀帝',
                  'country':2,
                  'hid':'hero706',
                  'hero_star':200,
                  'lv':50,
                  'skill':{'skill200':6},
                  'army':[{'type':2,'rank':0,'hp':230},{'type':2,'rank':5,'hp':230}],
               },
               {
                  'uid':2,
                  'uname':'蜀帝',
                  'country':2,
                  'hid':'hero727',
                  'hero_star':200,
                  'lv':200,
               },
               {
                  'uid':2,
                  'uname':'蜀帝',
                  'country':2,
                  'hid':'hero728',
                  'hero_star':200,
                  'lv':20,
               },
               {
                  'uname':'普通城防军',
                  'hid':'hero708',
                  'hero_star':200,
                  'lv':30,
                  'skill':{'skill200':22},
                  'army':[{'type':0,'rank':0},{'type':3,'rank':0}],
               },
               {
                  'uname':'城防军大将',
                  'hid':'hero724',
                  'hero_star':25,
                  'lv':45,
                  'skill':{'skill200':26},
                  'army':[{'type':2,'rank':2},{'type':2,'rank':0}],
               },
               {
                  'uid':2,
                  'uname':'蜀帝',
                  'country':2,
                  'hid':'hero725',
                  'hero_star':20,
                  'lv':50,
                  'skill':{'skill200':23},
                  'army':[{'type':0,'rank':2},{'type':3,'rank':2}],
               },
            ],
         },
      ]
   },

   'testComHSlidersConfig': 
   {
      'comRevived':['英雄轮回', 0, 5, 2],
      'comLv':['英雄等级', 2, 300, 2],
      'comStar':['英雄星级', -200, 23, 2],
      'comSkillLv':['技能等级', 2, 30, 2],
      'comBuild':['官邸等级', 0, 33, 2],
      'comArmyRank':['兵段', 2, 5, 2],
      'comArmyLv':['兵种阶级', 0, 75, 2],
      'comArmyAdd':['兵营科技', 2, 75, 2],
      'comShogun':['幕府加成', 0, 2.34, 0.02],
   },

   
   'testBlessPart':[
      ['33斗神套','group02'],
      ['32于吉','hero766'],
      ['30孟获','hero722'],
      ['29祝融','hero720'],
      ['26神庞统神周瑜','hero762a'],
      ['25司马徽','hero767'],
      ['24神赵云神吕布','hero726a'],
      ['22神郭嘉神孙策','hero727a'],
      ['22陆逊','hero728'],
      ['20神貂蝉神关羽','hero703a'],
      ['29天罚套','group02'],
      ['28神张辽神孙尚香','hero722a'],
      ['27童渊','hero768'],
      ['26神黄月英神甄姬','hero705a'],
      ['24司马懿神马云禄神贾诩','hero707'],
      ['22神吕蒙神张飞','hero725a'],
      ['22华佗','hero765'],
      ['200董卓神黄忠神荀彧','hero723'],
      ['8神夏侯惇神太史慈','hero726a'],
      ['7诸葛亮','hero724'],
      ['6神甘宁神马超','hero733a'],
      ['5武帝套','group00'],
      ['4小乔神姜维神许褚','hero723'],
      ['3周瑜','hero709'],
      ['2神典韦神周泰','hero757a'],
      ['2庞统','hero762'],

      ['-----',''  ],
      ['活动：蔡文姬','hero722', 9500000],
      ['活动：蔡文姬','hero722', 3000000],
      ['活动：蔡文姬','hero722', 2500000],
      ['活动：蔡文姬','hero722', 2000000],
      ['活动：蔡文姬','hero722', 2500000],
      ['活动：蔡文姬','hero722', 2250000],
      ['活动：蔡文姬','hero722', 20000000],
      ['活动：蔡文姬','hero722', 750000],
      ['活动：蔡文姬','hero722', 999750],
      ['活动：蔡文姬','hero722', 700000],
      ['活动：蔡文姬','hero722', 600000],
      ['活动：蔡文姬','hero722', 500000],
      ['活动：蔡文姬','hero722', 400000],
      ['活动：蔡文姬','hero722', 300000],
      ['活动：蔡文姬','hero722', 200000],
      ['活动：蔡文姬','hero722', 2000000],
      ['活动：蔡文姬','hero722', 50000],


      ['活动：大乔','hero724', 20000000],
      ['活动：吕玲绮','hero772', 20000000],
      ['-----',''  ],
      ['测试：五虎上将',''  ],
   ],




    
   'testStatisticsNum':2000,
    
   'testStatisticsMultiKill':20,
    
   'testStatisticsMultiKillNum':3,
    
   'testSaveFileNum':300,


   
   'testModes':[
      ['普通模式',-2  ],
      ['国战模式',0  ],
      ['擂台模式',200  ],
      ['跨服【青铜】',80 ,0 ],
      ['跨服【白银】',80 ,2 ],
      ['跨服【黄金】',80 ,2 ],
      ['跨服【闪耀】',80 ,3 ],
      ['跨服【最强】',80 ,4 ],
   ],

   
   'testClimb':[ 
      ['0合标准-02',[0, 40, 0, 2, 98765, 0, 0] ],
      ['0合标准-03',[0, 40, 0, 3, 98765, 0, 0] ],
      ['0合标准-22',[0, 40, 2, 2, 98765, 0, 0] ],
      ['0合标准-23',[0, 40, 2, 3, 98765, 0, 0] ],

      ['0合随机-02',[0, 40, 0, 2, -2, 0, 0] ],
      ['0合扫荡-02',[0, 40, 0, 2, 98765, 2000, 0] ],
      ['0合跳过-02',[0, 40, 0, 2, 98765, 0, 20000] ],

      ['2合标准-02',[2, 70, 0, 2, 98765, 0, 0] ],
      ['2合扫荡-02',[2, 70, 0, 2, 98765, 2000, 0] ],
      ['2合跳过-02',[2, 70, 0, 2, 98765, 0, 20000] ],

      ['2合标准-02',[2, 80, 0, 2, 98765, 0, 0] ],
      ['2合标准-03',[2, 80, 0, 3, 98765, 0, 0] ],
      ['2合标准-22',[2, 80, 2, 2, 98765, 0, 0] ],
      ['2合标准-23',[2, 80, 2, 3, 98765, 0, 0] ],
      ['2合扫荡-02',[2, 80, 0, 2, 98765, 2000, 0] ],
      ['2合跳过-02',[2, 80, 0, 2, 98765, 0, 20000] ],

      ['3合标准-02',[3, 75, 0, 2, 98765, 0, 0] ],
      ['3合扫荡-02',[3, 75, 0, 2, 98765, 2000, 0] ],
      ['3合跳过-02',[3, 75, 0, 2, 98765, 0, 20000] ],

      ['4合标准-02',[4, 2000, 0, 2, 98765, 0, 0] ],
      ['4合扫荡-02',[4, 2000, 0, 2, 98765, 2000, 0] ],
      ['4合跳过-02',[4, 2000, 0, 2, 98765, 0, 20000] ],

      ['5合标准-02',[5, 2200, 0, 2, 98765, 0, 0] ],
      ['5合扫荡-02',[5, 2200, 0, 2, 98765, 2000, 0] ],
      ['5合跳过-02',[5, 2200, 0, 2, 98765, 0, 20000] ],
   ],


   
   'testGodBoss':[
      ['满血斑寅将军',{ 
         'hid':'hero959',
         'lv':2000,
         'boss': {
            'life':2000,
            'undead':2,
            'lifeHpm0':[     
               [80, [4000000,0]],
               [60, [4000000,0]],
               [40, [4000000,0]],
               [20, [4000000,0]],
               [0, [4000000,0]],
            ]
         },
         'bossLifeDead': [0, 0, 0],
         'weak': [['weakGodScore',0,-2],['resGodElement0',0,-750],['resGodElement2',0,-750]]
      }],
      ['test铸木',{ 
         'hid':'hero360',
         'lv':2000,
         'boss': {
            'life':20000,
            'undead':2,
            'lifeHpm0':[     
               [2000, [20000000,0]],
               [80, [2000000,20000]],
               [60, [200000,2000]],
               [40, [20000,200]],
               [20, [2000,20]],
               [0, [200,2]],
            ]
         },
         'bossLifeDead': [3, 2, 0],
         'weak': [['weakGodScore',0,500],['resGodElement2',0,-2500],['resGodElement4',0,-2500]]
      }],
      ['短命夜叉',{ 
         'hid':'hero362',
         'lv':2000,
         'boss': {
            'life':5,
            'undead':2,
            'lifeHpm0':[     
               [5, [20000000,2]],
               [0, [2000,2]],
            ]
         },
         'bossLifeDead': [3, 2, 0],
         'weak': [['weakGodScore',0,500],['resGodElement2',0,-2500],['resGodElement4',0,-2500]]
      }],
      ['短命钩星',{ 
         'hid':'hero362',
         'lv':2000,
         'boss': {
            'life':5,
            'undead':2,
            'lifeHpm0':[     
               [5, [20000000,2]],
               [0, [2000,2]],
            ]
         },
         'bossLifeDead': [3, 2, 0],
         'weak': [['weakGodScore',0,500],['resGodElement2',0,-2500],['resGodElement4',0,-2500]]
      }],
      ['200%血结晶子路',{ 
         'hid':'hero363',
         'lv':2000,
         'boss': {
            'life':2000,
            'undead':2,
            'lifeHpm0':[     
               [80, [4000000,0]],
               [60, [4000000,0]],
               [40, [4000000,0]],
               [20, [4000000,0]],
               [0, [4000000,0]],
            ]
         },
         'bossLifeDead': [5, 3600000, 0],
         'weak': [['weakGodScore',0,-2],['resGodElement0',0,-750],['resGodElement2',0,-750]]
      }],
   ],

   
   'testHids':[
               'hero423','hero850','hero852','hero852','hero853','hero854','hero875','hero860','hero862','hero862','hero863','hero864','hero865','-----',
               

               'hero7000','hero7002','hero7002','hero7700','hero799','hero792','hero793','hero794','hero795','hero798','hero797','-----',

               'hero959','hero360','hero362','hero362','hero363','hero364','hero365','hero366','hero367','hero368','-----',

               'hero782','hero782','hero788','hero777','hero775','hero773','hero772','hero774','hero776','hero772','hero724','hero722','-----',
               'hero786','hero785','hero789','hero783','hero784','hero787','hero762','hero763','hero764','hero769','hero765','hero768','hero767','hero766','-----',
               'hero778', 'hero779', 'hero780', 'hero724', 'hero722', 'hero720', 'hero728', 'hero707', 'hero723', 'hero723',     'hero709', 'hero762',
               'hero727', 'hero726', 'hero7200', 'hero708', 'hero722', 'hero706', 'hero722', 'hero703',
               'hero704', 'hero725', 'hero705', 'hero729', 
'-----',
               'hero770', 'hero757', 'hero775', 'hero729', 'hero702','hero733', 'hero702','hero726','hero728','hero736', 'hero734','hero725', 'hero732', '-----',

               'hero880','hero882','hero882','hero883','hero884','hero885','hero886','-----',
               'hero832','hero826','hero827','-----',

               'hero727', 'hero730', 'hero732', 'hero795', 'hero737', 'hero738', 'hero739', 
               'hero740', 'hero742', 'hero742', 'hero743', 'hero744', 'hero745', 'hero746', 'hero747', 'hero748', 'hero749', 'hero750', 'hero752', 'hero752', 'hero753', 'hero754', 'hero756',  'hero758', 'hero759','hero760','hero802','hero800'],

   
   'testEquips':[
      ['无宝物',[]  ],

      ['金春后笛',[['equip262',4,[]]  ]],
      ['金湛卢剑',[['equip268',4,[]]  ]],

      ['金曲项琵琶',[['equip250',4,[]]  ]],
      ['金镀金博山炉',[['equip252',4,[]]  ]],
      ['金纷争残卷',[['equip252',4,[]]  ]],
      ['金圣灵鹿',[['equip253',4,[]]  ]],
      ['金赤尾雕翎箭',[['equip254',4,[]]  ]],
      ['金月宫仙兔',[['equip256',4,[]]  ]],

      ['------------',[]  ],
      ['金太平清领书',[['equip242',4,[]]  ]],
      ['金飞凤玲珑冠',[['equip243',4,[]]  ]],
      ['------------',[]  ],
      ['金祝融飞刀',[['equip008',4,[]]  ]],
      ['金青龙偃月刀',[['equip022',4,[]]  ]],
      ['金爪黄飞电',[['equip027',4,[]]  ]],
      ['金玉带',[['equip023',4,[]]  ]],
      ['------------',[]  ],
      ['金蚩尤虎魄',[['equip223',4,[]]  ]],
      ['金风后八阵图',[['equip222',4,[]]  ]],
      ['金藤甲',[['equip222',4,[]]  ]],
      ['金木牛流马',[['equip220',4,[]]  ]],
      ['金养由基弓',[['equip229',4,[]]  ]],
      ['------------',[]  ],
      ['金神农套',[['equip224',4,[]],['equip225',4,[]],['equip226',4,[]],['equip227',4,[]],['equip228',4,[]]  ]],
      ['紫神农套',[['equip224',3,[]],['equip225',3,[]],['equip226',3,[]],['equip227',3,[]],['equip228',3,[]]  ]],
      ['蓝神农套',[['equip224',2,[]],['equip225',2,[]],['equip226',2,[]],['equip227',2,[]],['equip228',2,[]]  ]],
      ['绿神农套',[['equip224',2,[]],['equip225',2,[]],['equip226',2,[]],['equip227',2,[]],['equip228',2,[]]  ]],
      ['白神农套',[['equip224',0,[]],['equip225',0,[]],['equip226',0,[]],['equip227',0,[]],['equip228',0,[]]  ]],
      ['------------',[]  ],
      ['金神农药王鞭',[['equip224',4,[]]  ]],  
      ['金神农金翅鸟',[['equip225',4,[]]  ]],
      ['金神农本草经',[['equip226',4,[]]  ]],
      ['金神农造世鼎',[['equip227',4,[]]  ]],
      ['金神农五弦令',[['equip228',4,[]]  ]],
      ['------------',[]  ],
      ['紫神农药王鞭',[['equip224',3,[]]  ]],  
      ['紫神农金翅鸟',[['equip225',3,[]]  ]],
      ['紫神农本草经',[['equip226',3,[]]  ]],
      ['紫神农造世鼎',[['equip227',3,[]]  ]],
      ['紫神农五弦令',[['equip228',3,[]]  ]],
      ['------------',[]  ],
      ['蓝神农药王鞭',[['equip224',2,[]]  ]],  
      ['蓝神农金翅鸟',[['equip225',2,[]]  ]],
      ['蓝神农本草经',[['equip226',2,[]]  ]],
      ['蓝神农造世鼎',[['equip227',2,[]]  ]],
      ['蓝神农五弦令',[['equip228',2,[]]  ]],
      ['------------',[]  ],
      ['绿神农药王鞭',[['equip224',2,[]]  ]],  
      ['绿神农金翅鸟',[['equip225',2,[]]  ]],
      ['绿神农本草经',[['equip226',2,[]]  ]],
      ['绿神农造世鼎',[['equip227',2,[]]  ]],
      ['绿神农五弦令',[['equip228',2,[]]  ]],
      ['------------',[]  ],
      ['白神农药王鞭',[['equip224',0,[]]  ]],  
      ['白神农金翅鸟',[['equip225',0,[]]  ]],
      ['白神农本草经',[['equip226',0,[]]  ]],
      ['白神农造世鼎',[['equip227',0,[]]  ]],
      ['白神农五弦令',[['equip228',0,[]]  ]],
      ['------------',[]  ],
      ['金金牛印',[['equip223',4,[]]  ]],
      ['金伞/胭',[['equip082',4,[]]  ,['equip052',4,[]]  ]],
      ['金胭/石',[['equip082',4,[]]  ,['equip097',4,[]]  ]],
      ['金伞/石',[['equip052',4,[]]  ,['equip097',4,[]]  ]],
      ['金伞/胭/石',[['equip082',4,[]],['equip052',4,[]]  ,['equip097',4,[]]  ]],

      ['金胭脂',[['equip082',4,[]]  ]],
      ['白胡笳十八拍',[['equip2002',0,[]]  ]],
      ['金胡笳十八拍',[['equip2002',4,[]]  ]],
      ['金苍龙鬼月刀',[['equip099',4,[]]  ]],
      ['金十字战戟',[['equip092',4,[]]  ]],
      ['金年兽',[['equip064',4,[]]  ]],

      ['紫GM武力棍',[['equip997',3,[]]  ]],
      ['紫GM智力棍',[['equip998',3,[]]  ]],
      ['紫GM魅力棍',[['equip999',3,[]]  ]],
      ['紫GM统帅棍',[['equip20000',3,[]]  ]],

      ['金雌雄双股剑',[['equip003',4,[]]  ]],
      ['紫雌雄双股剑',[['equip003',3,[]]  ]],
      ['金孟德新书',[['equip026',4,[]]  ]],
      ['紫孟德新书',[['equip026',3,[]]  ]],
      ['金松纹古锭刀',[['equip2002',4,[]]  ]],
      ['金骸骨灵驹',[['equip040',4,[]]  ]],
      ['金便宜十六策',[['equip2004',4,[]]  ]],
      ['金长信宫灯',[['equip2005',4,[]]  ]],
      ['金太极鱼符',[['equip2007',4,[]]  ]],

      ['金太学笔',[['equip2003',4,[]]  ]],
      ['金夜光杯',[['equip2006',4,[]]  ]],

      ['金伏羲套+24洗四维',[['equip2008',4,['wash005', 'wash0200', 'wash025', 'wash020'],24],['equip2009',4,['wash005', 'wash0200', 'wash025', 'wash020'],24],['equip2200',4,['wash005', 'wash0200', 'wash025', 'wash020'],24],['equip222',4,['wash005', 'wash0200', 'wash025', 'wash020'],24],['equip222',4,['wash005', 'wash0200', 'wash025', 'wash020'],24] ]],
      ['金伏羲套+20洗四维',[['equip2008',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip2009',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip2200',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip222',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip222',4,['wash005', 'wash0200', 'wash025', 'wash020'],20] ]],
      ['金伏羲套洗四维',[['equip2008',4,['wash005', 'wash0200', 'wash025', 'wash020']],['equip2009',4,['wash005', 'wash0200', 'wash025', 'wash020']],['equip2200',4,['wash005', 'wash0200', 'wash025', 'wash020']],['equip222',4,['wash005', 'wash0200', 'wash025', 'wash020']],['equip222',4,['wash005', 'wash0200', 'wash025', 'wash020']] ]],
      ['金伏羲套',[['equip2008',4,[]],['equip2009',4,[]],['equip2200',4,[]],['equip222',4,[]],['equip222',4,[]]  ]],
      ['紫伏羲套',[['equip2008',3,[]],['equip2009',3,[]],['equip2200',3,[]],['equip222',3,[]],['equip222',3,[]]  ]],
      ['蓝伏羲套',[['equip2008',2,[]],['equip2009',2,[]],['equip2200',2,[]],['equip222',2,[]],['equip222',2,[]]  ]],
      ['绿伏羲套',[['equip2008',2,[]],['equip2009',2,[]],['equip2200',2,[]],['equip222',2,[]],['equip222',2,[]]  ]],
      ['白伏羲套',[['equip2008',0,[]],['equip2009',0,[]],['equip2200',0,[]],['equip222',0,[]],['equip222',0,[]]  ]],
      ['---',[]  ],



 ['夏侯惇专属',[['equip099',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip075',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip087',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip083',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip045',4,['wash005', 'wash0200', 'wash025', 'wash020'],20]  ]],
      ['金麒麟弓套',[['equip079',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip042',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip044',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip083',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip045',4,['wash005', 'wash0200', 'wash025', 'wash020'],20]  ]],

      ['金蓬莱五件+20洗四维',[['equip095',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip098',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip056',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip057',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip058',4,['wash005', 'wash0200', 'wash025', 'wash020'],20] ]],
      ['金蓬莱五件洗四维',[['equip095',4,['wash005', 'wash0200', 'wash025', 'wash020']],['equip098',4,['wash005', 'wash0200', 'wash025', 'wash020']],['equip056',4,['wash005', 'wash0200', 'wash025', 'wash020']],['equip057',4,['wash005', 'wash0200', 'wash025', 'wash020']],['equip058',4,['wash005', 'wash0200', 'wash025', 'wash020']] ]],
      ['金蓬莱五件',[['equip095',4,[]],['equip098',4,[]],['equip056',4,[]],['equip057',4,[]],['equip058',4,[]] ]],
      ['金蓬莱三件+七星大宛',[['equip006',4,[]],['equip025',4,[]],['equip056',4,[]],['equip057',4,[]],['equip058',4,[]] ]],
      ['金蓬莱藜杖',[['equip095',4,[]]  ]],
      ['金云顶仙鹤',[['equip098',4,[]]  ]],

      ['金照夜玉狮子',[['equip042',4,[]]  ]],
      ['金五禽戏',[['equip049',4,[]]  ]],
      ['金荀令香',[['equip028',4,[]]  ]],
      ['金生死符',[['equip052',4,[]]  ]],
      ['白生死符',[['equip052',0,[]]  ]],
  ['马超专属+20洗四维',[['equip078',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip024',3,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip083',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip086',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip092',4,['wash005', 'wash0200', 'wash025', 'wash020'],20] ]],
      ['金女娲套+24洗四维',[['equip093',4,['wash005', 'wash0200', 'wash025', 'wash020'],24],['equip094',4,['wash005', 'wash0200', 'wash025', 'wash020'],24],['equip095',4,['wash005', 'wash0200', 'wash025', 'wash020'],24],['equip096',4,['wash005', 'wash0200', 'wash025', 'wash020'],24],['equip097',4,['wash005', 'wash0200', 'wash025', 'wash020'],24] ]],
      ['金女娲套+20洗四维',[['equip093',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip094',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip095',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip096',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip097',4,['wash005', 'wash0200', 'wash025', 'wash020'],20] ]],
      ['金女娲套洗四维',[['equip093',4,['wash005', 'wash0200', 'wash025', 'wash020']],['equip094',4,['wash005', 'wash0200', 'wash025', 'wash020']],['equip095',4,['wash005', 'wash0200', 'wash025', 'wash020']],['equip096',4,['wash005', 'wash0200', 'wash025', 'wash020']],['equip097',4,['wash005', 'wash0200', 'wash025', 'wash020']] ]],
      ['金女娲套',[['equip093',4,[]],['equip094',4,[]],['equip095',4,[]],['equip096',4,[]],['equip097',4,[]]  ]],

      ['金倚天剑',[['equip004',4,[]]  ]],
      ['紫大宛马',[['equip025',3,[]]  ]],
      ['金洛神赋',[['equip030',4,[]]  ]],
      ['金玉玺',[['equip024',4,[]]  ]],
      ['金将印',[['equip033',4,[]]  ]],

      ['金卧龙扇',[['equip075',4,[]]  ]],
      ['金方天画戟',[['equip076',4,[]]  ]],
      ['金落香伞',[['equip052',4,[]]  ]],
      ['金赤兔',[['equip075',4,[]]  ]],
      ['金真龙扳指',[['equip044',4,[]]  ]],
      ['金铜制虎符',[['equip046',4,[]]  ]],
      ['金三界帅旗',[['equip045',4,[]]  ]],



      ['吕玲绮专属2',[['equip092',4,['wash005', 'wash0200', 'wash025', 'wash020'], 20],['equip025',4,['wash005', 'wash0200', 'wash025', 'wash020'], 20],['equip049',4,['wash005', 'wash0200', 'wash025', 'wash020'], 20], ['equip044',4,['wash005', 'wash0200', 'wash025', 'wash020'], 20],['equip045',4,['wash005', 'wash0200', 'wash025', 'wash020'], 20]]],
      ['吕玲绮专属2',[['equip004',4,['wash005', 'wash0200', 'wash025', 'wash020'], 20],['equip025',4,['wash005', 'wash0200', 'wash025', 'wash020'], 20],['equip049',4,['wash005', 'wash0200', 'wash025', 'wash020'], 20], ['equip044',4,['wash005', 'wash0200', 'wash025', 'wash020'], 20],['equip075',4,['wash005', 'wash0200', 'wash025', 'wash020'], 20]]],
      ['吕玲绮专属3',[['equip099',4,['wash005', 'wash0200', 'wash025', 'wash020'], 20],['equip025',4,['wash005', 'wash0200', 'wash025', 'wash020'], 20],['equip049',4,['wash005', 'wash0200', 'wash025', 'wash020'], 20], ['equip044',4,['wash005', 'wash0200', 'wash025', 'wash020'], 20],['equip075',4,['wash005', 'wash0200', 'wash025', 'wash020'], 20]]],
      ['吕玲绮专属4',[['equip099',4,['wash005', 'wash0200', 'wash025', 'wash020'], 20],['equip025',4,['wash005', 'wash0200', 'wash025', 'wash020'], 20],['equip049',4,['wash005', 'wash0200', 'wash025', 'wash020'], 20], ['equip044',4,['wash005', 'wash0200', 'wash025', 'wash020'], 20],['equip045',4,['wash005', 'wash0200', 'wash025', 'wash020'], 20]]],
['吕玲绮专属5',[['equip092',4,['wash005', 'wash0200', 'wash025', 'wash020'], 20],['equip025',4,['wash005', 'wash0200', 'wash025', 'wash020'], 20],['equip049',4,['wash005', 'wash0200', 'wash025', 'wash020'], 20], ['equip044',4,['wash005', 'wash0200', 'wash025', 'wash020'], 20],['equip075',4,['wash005', 'wash0200', 'wash025', 'wash020'], 20]]],
      ['金战戟+扳指',[['equip092',4,[]],['equip044',4,[]]  ]],

      ['金5散件+22',[['equip002',4,[],22],['equip023',4,[],22],['equip028',4,[],22], ['equip023',4,[],22],['equip075',4,[],22]  ]],
      ['金5散件+8',[['equip002',4,[],8],['equip023',4,[],8],['equip028',4,[],8], ['equip023',4,[],8],['equip075',4,[],8]  ]],
      ['金5散件+4~20',[['equip002',4,[],4],['equip023',4,[],8],['equip028',4,[],22], ['equip023',4,[],26],['equip075',4,[],20]  ]],
      ['金青釭剑',[['equip077',4,[]]  ]],
      ['金龙胆枪',[['equip078',4,[]]  ]],
      ['金麒麟弓',[['equip079',4,[]]  ]],
      ['金的卢+20',[['equip080',4,[],20]  ]],
      ['金的卢',[['equip080',4,[]]  ]],
      ['金惊帆',[['equip082',4,[]]  ]],
      ['金孙子兵法+20',[['equip083',4,[],20]  ]],
      ['金孙子兵法',[['equip083',4,[]]  ]],
      ['金青囊书',[['equip084',4,[]]  ]],
      ['金遁甲天书',[['equip085',4,[]]  ]],
      ['金狐裘披风+20',[['equip086',4,[],20]  ]],
      ['金狐裘披风',[['equip086',4,[]]  ]],
      ['金匈奴烈酒',[['equip087',4,[]]  ]],
      ['蓝匈奴烈酒',[['equip087',2,[]]  ]],
      ['金神兽砚',[['equip088',4,[]]  ]],
      ['金孔明灯',[['equip089',4,[]]  ]],
      ['金太公兵符+20',[['equip075',4,[],20]  ]],
      ['金太公兵符',[['equip075',4,[]]  ]],
      ['金黄天令旗',[['equip092',4,[]]  ]],
      ['---',[]  ],

      ['金轩辕套+24洗四维',[['equip065',4,['wash005', 'wash0200', 'wash025', 'wash020'],24],['equip066',4,['wash005', 'wash0200', 'wash025', 'wash020'],24],['equip067',4,['wash005', 'wash0200', 'wash025', 'wash020'],24],['equip068',4,['wash005', 'wash0200', 'wash025', 'wash020'],24],['equip069',4,['wash005', 'wash0200', 'wash025', 'wash020'],24] ]],
      ['金轩辕套+20洗四维',[['equip065',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip066',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip067',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip068',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip069',4,['wash005', 'wash0200', 'wash025', 'wash020'],20] ]],
      ['金轩辕套洗四维',[['equip065',4,['wash005', 'wash0200', 'wash025', 'wash020']],['equip066',4,['wash005', 'wash0200', 'wash025', 'wash020']],['equip067',4,['wash005', 'wash0200', 'wash025', 'wash020']],['equip068',4,['wash005', 'wash0200', 'wash025', 'wash020']],['equip069',4,['wash005', 'wash0200', 'wash025', 'wash020']] ]],
      ['金轩辕套',[['equip065',4,[]],['equip066',4,[]],['equip067',4,[]],['equip068',4,[]],['equip069',4,[]]  ]],
      ['金轩辕套仅书紫',[['equip065',4,[]],['equip066',4,[]],['equip067',3,[]],['equip068',4,[]],['equip069',4,[]]  ]],
      ['金轩辕幡+紫套',[['equip065',4,[]],['equip066',3,[]],['equip067',3,[]],['equip068',3,[]],['equip069',3,[]]  ]],
      ['紫轩辕套',[['equip065',3,[]],['equip066',3,[]],['equip067',3,[]],['equip068',3,[]],['equip069',3,[]]  ]],
      ['蓝轩辕套',[['equip065',2,[]],['equip066',2,[]],['equip067',2,[]],['equip068',2,[]],['equip069',2,[]]  ]],
      ['绿轩辕套',[['equip065',2,[]],['equip066',2,[]],['equip067',2,[]],['equip068',2,[]],['equip069',2,[]]  ]],
      ['白轩辕套',[['equip065',0,[]],['equip066',0,[]],['equip067',0,[]],['equip068',0,[]],['equip069',0,[]]  ]],
      ['---',[]  ],

      ['金斗神套+20洗四维',[['equip059',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip060',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip062',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip062',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip063',4,['wash005', 'wash0200', 'wash025', 'wash020'],20] ]],
      ['金斗神套洗四维',[['equip059',4,['wash005', 'wash0200', 'wash025', 'wash020']],['equip060',4,['wash005', 'wash0200', 'wash025', 'wash020']],['equip062',4,['wash005', 'wash0200', 'wash025', 'wash020']],['equip062',4,['wash005', 'wash0200', 'wash025', 'wash020']],['equip063',4,['wash005', 'wash0200', 'wash025', 'wash020']] ]],
      ['金斗神套',[['equip059',4,[]],['equip060',4,[]],['equip062',4,[]],['equip062',4,[]],['equip063',4,[]]  ]],
      ['金斗神套2',[['equip065',4,[],['wash005', 'wash0200', 'wash025', 'wash020']],['equip066',2,[]],['equip048',2,[],['wash005', 'wash0200', 'wash025', 'wash020']],['equip087',4,[],['wash005', 'wash0200', 'wash025', 'wash020']],['equip063',4,[],['wash005', 'wash0200', 'wash025', 'wash020']]  ]],
      ['金斗神斧+4紫斗神套',[['equip059',3,[]],['equip060',3,[]],['equip062',3,[]],['equip062',3,[]],['equip063',3,[]]  ]],   
      ['金斗神斧+3蓝斗神套',[['equip059',4,[]],['equip060',2,[]],['equip062',2,[]],['equip062',2,[]]  ]],    
      ['金斗神斧+2绿斗神套',[['equip059',4,[]],['equip060',2,[]],['equip062',2,[]]  ]],  
      ['金斗神斧+2白斗神套',[['equip059',4,[]],['equip060',0,[]]  ]],
      ['金斗神斧',[['equip059',4,[]]  ]],
      ['---',[]  ],

      ['金天罚套+20洗四维',[['equip034',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip038',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip042',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip043',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip048',4,['wash005', 'wash0200', 'wash025', 'wash020'],20] ]],
      ['金天罚套洗四维',[['equip034',4,['wash005', 'wash0200', 'wash025', 'wash020']],['equip038',4,['wash005', 'wash0200', 'wash025', 'wash020']],['equip042',4,['wash005', 'wash0200', 'wash025', 'wash020']],['equip043',4,['wash005', 'wash0200', 'wash025', 'wash020']],['equip048',4,['wash005', 'wash0200', 'wash025', 'wash020']] ]],
      ['金天罚套',[['equip034',4,[]],['equip038',4,[]],['equip042',4,[]],['equip043',4,[]],['equip048',4,[]]  ]],
      ['金天罚锤+4紫天罚套',[['equip034',4,[]],['equip038',3,[]],['equip042',3,[]],['equip043',3,[]],['equip048',3,[]]  ]],   
      ['金天罚锤+3蓝天罚套',[['equip034',4,[]],['equip038',2,[]],['equip042',2,[]],['equip043',2,[]]  ]],    
      ['金天罚锤+2绿天罚套',[['equip034',4,[]],['equip038',2,[]],['equip042',2,[]]  ]],  
      ['金天罚锤+2白天罚套',[['equip034',4,[]],['equip038',0,[]]  ]],
      ['金天罚锤',[['equip034',4,[]]  ]],
      ['---',[]  ],

      ['金武帝套+20洗四维',[['equip036',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip039',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip047',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip050',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip054',4,['wash005', 'wash0200', 'wash025', 'wash020'],20]  ]],
      ['金武帝套洗四维',[['equip036',4,['wash005', 'wash0200', 'wash025', 'wash020']],['equip039',4,['wash005', 'wash0200', 'wash025', 'wash020']],['equip047',4,['wash005', 'wash0200', 'wash025', 'wash020']],['equip050',4,['wash005', 'wash0200', 'wash025', 'wash020']],['equip054',4,['wash005', 'wash0200', 'wash025', 'wash020']]  ]],
      ['金武帝套',[['equip036',4,[]],['equip039',4,[]],['equip047',4,[]],['equip050',4,[]],['equip054',4,[]]  ]],
      ['4金2白武帝套',[['equip036',4,[]],['equip039',4,[]],['equip050',4,[]],['equip054',4,[]],['equip047',2,[]]  ]],  
      ['4金武帝套',[['equip036',4,[]],['equip039',4,[]],['equip050',4,[]],['equip054',4,[]]  ]],    
      ['3金武帝套',[['equip036',4,[]],['equip039',4,[]],['equip050',4,[]]  ]], 
      ['2金武帝套',[['equip036',4,[]],['equip039',4,[]]  ]],
      ['蓝武帝套',[['equip036',2,[]],['equip039',2,[]],['equip047',2,[]],['equip050',2,[]],['equip054',2,[]]  ]],   
      ['金武帝佩剑',[['equip036',2,[]], ]],
      ['---',[]  ],

      ['金羽林套+20洗四维',[['equip070',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip072',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip072',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip073',4,['wash005', 'wash0200', 'wash025', 'wash020'],20],['equip074',4,['wash005', 'wash0200', 'wash025', 'wash020'],20] ]],
      ['金羽林套洗四维',[['equip070',4,['wash005', 'wash0200', 'wash025', 'wash020']],['equip072',4,['wash005', 'wash0200', 'wash025', 'wash020']],['equip072',4,['wash005', 'wash0200', 'wash025', 'wash020']],['equip073',4,['wash005', 'wash0200', 'wash025', 'wash020']],['equip074',4,['wash005', 'wash0200', 'wash025', 'wash020']] ]],
      ['金羽林套',[['equip070',4,[]],['equip072',4,[]],['equip072',4,[]],['equip073',4,[]],['equip074',4,[]]  ]],
      ['紫羽林套',[['equip070',3,[]],['equip072',3,[]],['equip072',3,[]],['equip073',3,[]],['equip074',3,[]]  ]],
      ['蓝羽林套',[['equip070',2,[]],['equip072',2,[]],['equip072',2,[]],['equip073',2,[]],['equip074',2,[]]  ]],
      ['绿羽林套',[['equip070',2,[]],['equip072',2,[]],['equip072',2,[]],['equip073',2,[]],['equip074',2,[]]  ]],
      ['白羽林套',[['equip070',0,[]],['equip072',0,[]],['equip072',0,[]],['equip073',0,[]],['equip074',0,[]]  ]],
      ['---',[]  ],

      ['金蓬莱三件+倚天赤兔',[['equip056',4,[]],['equip057',4,[]],['equip058',4,[]],['equip004',4,[]],['equip075',4,[]] ]],
      ['2金蓬莱三件+七星大宛',[['equip077',4,[]],['equip025',4,[]],['equip056',4,[]],['equip057',4,[]],['equip058',4,[]] ]],
      ['金蓬莱2件+帅旗七星大宛', [['equip006', 4, []], ['equip025', 4, []], ['equip056', 4, []], ['equip057', 4, []], ['equip045', 4, []] ]],
      ['倚武太灵帅', [['equip004', 4, []], ['equip039', 4, []], ['equip027', 4, []], ['equip057', 4, []], ['equip045', 4, []] ]],
      ['七星大碗帅旗', [['equip006', 4, []], ['equip025', 4, []], ['equip062', 4, []], ['equip044', 4, []], ['equip045', 4, []] ]],
	  ['倚天大碗蓬莱2帅旗落雷',[['equip004',4,[]],['equip025',4,[]],['equip027',4,[]],['equip057',4,[]],['equip045',4,[]] ]],
      ['金蓬莱三件',[['equip056',4,[]],['equip057',4,[]],['equip058',4,[]]  ]],
      ['金元始天书',[['equip056',4,[]]  ]],
      ['金灵宝葫芦',[['equip057',4,[]]  ]],
      ['金太上勒令',[['equip058',4,[]]  ]],

      ['---',[]  ],

      ['金斧绝影玉带七步洗四维',[['equip002',4,[ 'wash005', 'wash0200', 'wash025', 'wash020']],['equip023',4,[ 'wash005', 'wash0200', 'wash025', 'wash020']],['equip023',4,[ 'wash005', 'wash0200', 'wash025', 'wash020']],['equip028',4,[ 'wash005', 'wash0200', 'wash025', 'wash020']]  ]],
      ['金斧绝影玉带七步',[['equip002',4,[]],['equip023',4,[]],['equip023',4,[]],['equip028',4,[]]  ]],
      ['紫斧绝影玉带七步',[['equip002',3,[]],['equip023',3,[]],['equip023',3,[]],['equip028',3,[]]  ]],
      ['蓝斧绝影玉带七步',[['equip002',2,[]],['equip023',2,[]],['equip023',2,[]],['equip028',2,[]]  ]],
      ['绿斧绝影玉带七步',[['equip002',2,[]],['equip023',2,[]],['equip023',2,[]],['equip028',2,[]]  ]],
      ['白斧绝影玉带七步',[['equip002',0,[]],['equip023',0,[]],['equip023',0,[]],['equip028',0,[]]  ]],
      ['金玉玺洗四维',[['equip024',4,[ 'wash005', 'wash0200', 'wash025', 'wash020']]  ]],
      ['白七星刀',[['equip006',0,[]]  ]],
      ['紫青龙偃月刀',[['equip022',3,[]]  ]],
   ],
   
   'testStars':[
      ['无星辰',{}  ],
      ['满四维合2暴格',{'star05':260,'star06':260,'star07':260,'star02':40,'star02':40,'star03':40,'star04':40,     'star08':22,'star200':22,'star22':22,'star24':22}  ],
      ['满非四合2暴格',{'star05':220,'star06':220,'star07':220,'star02':30,'star02':30,'star03':30,'star04':30,     'star09':200,'star22':200,'star23':200,'star25':200}  ],
      ['满四维合2暴格',{'star05':220,'star06':220,'star07':220,'star02':30,'star02':30,'star03':30,'star04':30,     'star08':6,'star200':6,'star22':6,'star24':6}  ],
      ['满四维未合',{'star05':220,'star06':220,'star07':220,'star02':30,'star02':30,     'star08':6,'star200':6,'star22':6,'star24':6}  ],
      ['6级四维星辰',{'star08':6,'star200':6,'star22':6,'star24':6} ],

      ['满青龙武力',{'star08':22} ],
      ['满青龙兵力',{'star09':25} ],
      ['满白虎智力',{'star200':22} ],
      ['满白虎攻击',{'star22':25} ],
      ['满朱雀魅力',{'star22':22} ],
      ['满朱雀速度',{'star23':25} ],
      ['满玄武统帅',{'star24':22} ],
      ['满玄武防御',{'star25':25} ],
      ['---',{}  ],
      ['虚拟3000级防星辰',{'star07':3000} ],
      ['虚拟20000级攻星辰',{'star05':20000} ],
      ['虚拟20000级防星辰',{'star07':20000} ],
      ['虚拟20000级兵星辰',{'star06':20000} ],
      ['虚拟+50全四维',{'star08':50,'star200':50,'star22':50,'star24':50} ],
      ['虚拟+25全四维',{'star08':25,'star200':25,'star22':25,'star24':25} ],
      ['虚拟7500级暴击星辰',{'star02':7500}  ],
      ['虚拟7500级格挡星辰',{'star02':7500}  ],
      ['---',{}  ],
      ['Test全噬月禁盾治援',{'starMoon':2}  ],
      ['Test后封技2回合',{'starBanArmyA2_2':2}  ],
      ['Test前气馁2回合',{'starBanArmyB2_0':2}  ],
      ['Test技穷2回合',{'starBanHeroA2':2}  ],

      ['Test全内讧',{'starFaction':2}  ],
      ['Test前内讧',{'starFaction0':2}  ],
      ['Test后内讧',{'starFaction2':2}  ],
      ['Test全混乱',{'starStun':2}  ],
      ['Test前混乱',{'starStun0':2}  ],
      ['Test后混乱',{'starStun2':2}  ],
      ['Test全冰封',{'starFreeze':2}  ],
      ['Test全断粮',{'starBuffG302':2}  ],
      ['Test全寄生',{'starBuffG302_4':2}  ],

      ['Test全火灾',{'starFire':2}  ],
      ['Test全虚弱',{'starWeak':2}  ],
      ['Test全火虚混汁迟寒',{'starFire':2,'starWeak':2,'starStun':2,'starPoison':2,'starSlow':2,'starFrozen':2}  ],
      ['Test全溃逃',{'starFlee':2}  ],
      ['Test全金汁',{'starPoison':2}  ],
      ['Test全迟缓',{'starSlow':2}  ],
      ['Test全寒颤',{'starFrozen':2}  ],
      ['Test自裁',{'starSuicide':2}  ],
      ['Test全自损',{'starLoss':2}  ],
      ['Test全自攻6段',{'starSelfAttack':2}  ],
      ['Test自损+回冲+包扎',{'starLoss':2,'starHpPlusToAttack':2,'starHpMinusToCure':2}  ],
      ['---',{}  ],
      ['Test全鼓舞',{'starMorale':2}  ],
      ['Test前鼓舞',{'starMorale0':2}  ],
      ['Test后鼓舞',{'starMorale2':2}  ],
      ['Test全福佑',{'starShield':2}  ],
      ['Test全神临',{'starScheme2008':2}  ],
      ['Test全神临+福佑',{'starScheme2008':2,'starShield':2}  ],
      ['Test全神临+空城',{'starScheme2008':2, 'starScheme2002':2}  ],
      ['Test全自愈+援军',{'starBuffHealing':2, 'starBuffRelief':2}  ],
      ['Test英雄反击',{'starHeroBeatBack':2}  ],
      ['Test全仙隐',{'starBuff095':2}  ],
      ['Test仙隐+福星',{'starBuff095':2, 'starBuff095Expend':2}  ],
      ['Test仙隐+仙音+余音',{'starBuff095':2, 'starBuff095Add':2, 'starBuff095Remove':2}  ],
      ['---',{}  ],
      ['30级双暴击',{'star02':30,'star04':30}  ],
      ['30级双格档',{'star02':30,'star03':30}  ],
      ['30级暴击星辰',{'star02':30}  ],
      ['30级格挡星辰',{'star02':30}  ],
      ['30级英魂星辰',{'star26':30}  ],
      ['30级坚定星辰',{'star27':30}  ],
      ['30级步兵星辰',{'star28':30}  ],
      ['30级骑兵星辰',{'star29':30}  ],
      ['30级弓兵星辰',{'star20':30}  ],
      ['30级方士星辰',{'star22':30}  ],
      
      ['30级步兵弓兵星辰',{'star28':30,'star20':30}  ],
      ['30级步兵方士星辰',{'star28':30,'star22':30}  ],
      ['30级骑兵弓兵星辰',{'star29':30,'star20':30}  ],
      ['30级骑兵方士星辰',{'star29':30,'star22':30}  ],

      ['25级青龙号令神纹',{'star09':25}  ],
      ['25级白虎利刃神纹',{'star22':25}  ],
      ['25级朱雀迅捷神纹',{'star23':25}  ],
      ['25级玄武固守神纹',{'star25':25}  ],
      ['200级青龙号令神纹',{'star09':200}  ],
      ['200级白虎利刃神纹',{'star22':200}  ],
      ['200级朱雀迅捷神纹',{'star23':200}  ],
      ['200级玄武固守神纹',{'star25':200}  ],

      ['22级青龙武力神纹',{'star08':22}  ],
      ['22级白虎智力神纹',{'star200':22}  ],
      ['22级朱雀魅力神纹',{'star22':22}  ],
      ['22级玄武统帅神纹',{'star24':22}  ],
      ['6级青龙武力神纹',{'star08':6}  ],
      ['6级白虎智力神纹',{'star200':6}  ],
      ['6级朱雀魅力神纹',{'star22':6}  ],
      ['6级玄武统帅神纹',{'star24':6}  ],

      ['---',{}  ],
      ['50级攻防兵25级格暴',{'star05':50,'star06':50,'star07':50,'star02':25,'star02':25} ],
      ['30级攻防兵星辰',{'star05':30,'star06':30,'star07':30}  ],
      ['25级攻防兵星辰',{'star05':25,'star06':25,'star07':25}  ],
      ['5级攻防兵星辰',{'star05':5,'star06':5,'star07':5}  ],
   ],

   
   'testSciences':[
      ['无科技',{}  ],
      ['8页全满科技',{'2':40,'2':40,'5':40,'6':40,    '58':3,'28':40,'29':40,'30':40,'32':40,'2002':2,'59':3,'60':3,    '2002':5,'37':40,'38':40,'39':40,'40':40   ,'98':3,'99':3,'2000':3,'69':5,'70':5,'72':5,'72':5,'2005':5    ,'33':5,'34':5,'95':5,'48':5,'49':5,'50':5,'52':5,'52':5,'53':5,'54':5,'75':5,'56':5} ],
      ['7页满科技',{'2':40,'2':40,'5':40,'6':40,    '58':3,'28':40,'29':40,'30':40,'32':40,'2002':2,'59':3,'60':3,    '2002':5,'37':40,'38':40,'39':40,'40':40   ,'98':3,'99':3,'2000':3,'69':5,'70':5,'72':5,'72':5,'2005':5} ],
      ['6页满科技',{'2':40,'2':40,'5':40,'6':40,    '58':3,'28':40,'29':40,'30':40,'32':40,'2002':2,'59':3,'60':3,    '2002':5,'37':40,'38':40,'39':40,'40':40} ],
      ['5页满科技',{'2':40,'2':40,'5':40,'6':40,    '58':3,'28':40,'29':40,'30':40,'32':40,'2002':2,'59':3,'60':3} ],
      ['4页满科技',{'2':40,'2':40,'5':40,'6':40,    '58':3,'28':40,'29':40,'30':40,'32':40,'2002':2,'59':3,} ],
      ['3页满科技',{'2':40,'2':40,'5':40,'6':40,    '58':3,'28':40,'29':40,'30':40,'32':40,'2002':2,}  ],
      ['2页满科技',{'2':40,'2':40,'5':40,'6':40,    '58':3,}  ],
      ['2页满科技',{'2':40,'2':40,'5':40,'6':40}  ],
      ['2页半科技',{'2':20,'2':20,'5':20,'6':20}  ],
   ],
   
   'testAdjutants':[
      ['无副将',None  ],

      ['张仲景/- 400/4w',[['hero7002',250,400,28,40000,0],None] ],
      ['神张仲景/- 400/4w',[['hero7002',250,400,28,40000,2],None] ],
      ['刘邦/- 400/4w',[['hero793',250,400,28,40000,0],None] ],
      ['神刘邦/- 400/4w',[['hero793',250,400,28,40000,2],None] ],
      ['神黄承彦/- 250/2w',[['hero788',225,250,28,200000,2],None]],
      ['-/神黄承彦 250/2w',[None,['hero788',225,250,28,200000,2]]],

      ['神汉献帝/- 2/0',[['hero770',2,2,28,0,2],None] ],
      ['神汉献帝/- 400/4w',[['hero770',250,400,28,40000,2],None] ],
      ['汉献帝/- 400/4w',[['hero770',250,400,28,40000,0],None] ],

      ['吕/貂 400/4w',[['hero708',250,400,28,40000],['hero703',250,400,28,40000]] ],
      ['肤神吕/肤神貂 400/4w',[['hero708',250,400,28,40000,2,'skin708_2'],['hero703',250,400,28,40000,2,'skin703_2']] ],
      ['肤神吕/肤神貂 300/3w',[['hero708',250,300,28,30000,2,'skin708_2'],['hero703',250,300,28,30000,2,'skin703_2']] ],
      ['肤神吕/- 300/3w',[['hero708',250,300,28,30000,2,'skin708_2']] ],

      ['红马忠/- 250/2w',[['hero782',225,250,28,200000],None]],
      ['-/红马忠 250/2w',[None,['hero782',225,250,28,200000]]],
      ['红黄承彦/- 250/2w',[['hero788',225,250,28,200000],None]],
      ['-/红黄承彦 250/2w',[None,['hero788',225,250,28,200000]]],

      ['红紫虚/- 250/2w',[['hero786',225,250,28,200000]] ],
      ['-/红紫虚 250/2w',[None,['hero786',225,250,28,200000]] ],
      ['红陈寿/- 250/2w',[['hero785',225,250,28,200000]] ],
      ['-/红陈寿 250/2w',[None,['hero785',225,250,28,200000]] ],

      ['红刘禅/- 250/2w',[['hero778',225,250,28,200000]] ],
      ['红王越/- 250/2w',[['hero783',225,250,28,200000]] ],
      ['红左慈/- 250/2w',[['hero784',225,250,28,200000]] ],
      ['红南华/- 250/2w',[['hero787',225,250,28,200000]] ],

      ['红吴国太/- 250/2w',[['hero775',225,250,28,200000]] ],
      ['红关银屏/- 250/2w',[['hero773',225,250,28,200000]] ],
      ['红张星彩/- 250/2w',[['hero776',225,250,28,200000]] ],
      ['红步练师/- 250/2w',[['hero774',225,250,28,200000]] ],
      ['红汉献帝/- 250/2w',[['hero770',225,250,28,200000]] ],

      ['月英/诸葛 250/2w',[['hero705',225,250,28,200000],['hero724',225,250,28,200000]] ],

      ['吕/关 250/2w',[['hero708',225,250,0,200000],['hero706',225,250,0,200000]] ],
      ['吕/关 250/2k',[['hero708',225,250,0,20000],['hero706',225,250,0,20000]] ],
      ['吕/关 250/2000',[['hero708',225,250,0,2000],['hero706',225,250,0,2000]] ],
      ['吕/关 关250/2w',[['hero708',225,250,0,0],['hero706',225,250,0,200000]] ],
      ['红吴国太/关羽 250',[['hero775',225,250,28],['hero706',225,250]] ],
      ['吕布/关羽 250',[['hero708',225,250],['hero706',225,250]] ],
      ['吕布/貂蝉 250',[['hero708',225,250],['hero703',225,250]] ],
      ['吕布/赵云 250',[['hero708',225,250],['hero726',225,250]] ],
      ['貂蝉/吕布 250',[['hero703',225,250],['hero708',225,250]] ],
      ['貂蝉/郭嘉 250',[['hero703',225,250],['hero727',225,250]] ],
      ['貂蝉/赵云 250',[['hero703',225,250],['hero726',225,250]] ],
      ['赵云/吕布 250',[['hero726',225,250],['hero708',225,250]] ],
      ['赵云/貂蝉 250',[['hero726',225,250],['hero703',225,250]] ],
      ['赵云/小乔 250',[['hero726',225,250],['hero723',225,250]] ],
      ['---',None  ],
      ['吕布/关羽 50',[['hero708',25,50],['hero706',25,50]] ],
      ['吕布/貂蝉 50',[['hero708',25,50],['hero703',25,50]] ],
      ['吕布/赵云 50',[['hero708',25,50],['hero726',25,50]] ],
      ['貂蝉/吕布 50',[['hero703',25,50],['hero708',25,50]] ],
      ['貂蝉/郭嘉 50',[['hero703',25,50],['hero727',25,50]] ],
      ['貂蝉/赵云 50',[['hero703',25,50],['hero726',25,50]] ],
      ['赵云/吕布 50',[['hero726',25,50],['hero708',25,50]] ],
      ['赵云/貂蝉 50',[['hero726',25,50],['hero703',25,50]] ],
      ['赵云/小乔 50',[['hero726',25,50],['hero723',25,50]] ],
      ['---',[None,None]],
      ['吕布/- 250',[['hero708',225,250],None] ],
      ['貂蝉/- 250',[['hero703',225,250],None] ],
      ['赵云/- 250',[['hero726',225,250],None] ],
      ['-/吕布 250',[None,['hero708',225,250]] ],
      ['-/貂蝉 250',[None,['hero703',225,250]] ],
      ['-/赵云 250',[None,['hero726',225,250]] ],

      ['红汉献帝/- 250',[['hero770',225,250,28],None] ],
      ['-/金汉献帝 250',[None,['hero770',225,250,22]] ],
      ['紫汉献帝/- 250',[['hero770',225,250,6],None] ],
      ['-/蓝汉献帝 250',[None,['hero770',225,250,0]] ],
   ],

   
   'testOfficials':['无官职',0,2,-2,-2],
   
   'testTitles':['无称号','title002','title222','title222','title2009','title2200','title2007','title2008','title2005','title2006','title2003','title2004','title2002','title2002','title2002','title002','title003','title004','title005','title006','title007','title008','title009','title0200',
'title022','title022','title023','title024','title025','title026','title027','title028','title029','title020',
'title022','title022','title023','title024','title025','title026','title027','title028','title029','title030','title032','title032',
],

   
   'testLegends':[
      ['无传奇',None  ],
      ['红29',{
         'hero792':28,'hero793':28,'hero794':28,'hero795':28,'hero798':28,'hero797':28,
         'hero782':28,'hero788':28,'hero782':28,
         'hero786':28,'hero785':28,'hero789':28,
         'hero783':28,'hero784':28,'hero787':28,
         'hero765':28,'hero768':28,'hero767':28,'hero766':28,
      }],
      ['红孙坚',{'hero792':28} ],
      ['红刘邦',{'hero793':28} ],
      ['红项羽',{'hero794':28} ],
      ['红虞姬',{'hero795':28} ],
      ['红韩信',{'hero798':28} ],
      ['红张良',{'hero797':28} ],
      ['红23',{
         'hero782':28,'hero788':28,'hero782':28,
         'hero786':28,'hero785':28,'hero789':28,
         'hero783':28,'hero784':28,'hero787':28,
         'hero765':28,'hero768':28,'hero767':28,'hero766':28,
      }],
      ['红马忠',{'hero782':28} ],
      ['红黄承彦',{'hero788':28} ],
      ['红兀突骨',{'hero782':28} ],
      ['红200',{'hero786':28,'hero785':28,'hero765':28,'hero768':28,'hero767':28,'hero766':28,'hero783':28,'hero784':28,'hero787':28,'hero789':28} ],
      ['红紫虚上人',{'hero786':28} ],
      ['红陈寿',{'hero785':28} ],
      ['红8',{'hero765':28,'hero768':28,'hero767':28,'hero766':28,'hero783':28,'hero784':28,'hero787':28,'hero789':28} ],
      ['红袁绍',{'hero789':28} ],
      ['红4+3',{'hero765':28,'hero768':28,'hero767':28,'hero766':28,'hero783':28,'hero784':28,'hero787':28} ],
      ['红王/左/南',{'hero783':28,'hero784':28,'hero787':28} ],
      ['红王越',{'hero783':28} ],
      ['红左慈',{'hero784':28} ],
      ['红南华老仙',{'hero787':28} ],
      ['红华/童/徽/于',{'hero765':28,'hero768':28,'hero767':28,'hero766':28} ],
      ['金华/童/徽/于',{'hero765':22,'hero768':22,'hero767':22,'hero766':22} ],
      ['紫华/童/徽/于',{'hero765':6,'hero768':6,'hero767':6,'hero766':6} ],
      ['蓝华/童/徽/于',{'hero765':0,'hero768':0,'hero767':0,'hero766':0} ],
      ['红华佗',{'hero765':28} ],
      ['红童渊',{'hero768':28} ],
      ['红司马徽',{'hero767':28} ],
      ['红于吉',{'hero766':28} ],
   ],
   
   'testFormations':[
      ['普通方阵',[0,{}] ],
      ['红60锋钩雁',[2,{'2':[5,60],'2':[5,60],'3':[5,60]}] ],['红60钩雁鹤',[2,{'2':[5,60],'3':[5,60],'4':[5,60]}] ],['红60雁鹤铁',[3,{'3':[5,60],'4':[5,60],'5':[5,60]}] ],['红60鹤铁八',[4,{'4':[5,60],'5':[5,60],'6':[5,60]}] ],['红60铁八锋',[5,{'5':[5,60],'6':[5,60],'2':[5,60]}] ],['红60八锋钩',[6,{'6':[5,60],'2':[5,60],'2':[5,60]}] ],
      ['红60锋矢',[2,{'2':[5,60]}] ],['红60钩行',[2,{'2':[5,60]}] ],['红60雁形',[3,{'3':[5,60]}] ],['红60鹤翼',[4,{'4':[5,60]}] ],['红60铁桶',[5,{'5':[5,60]}] ],['红60八卦',[6,{'6':[5,60]}] ],
      ['金50锋矢',[2,{'2':[4,50]}] ],['金50钩行',[2,{'2':[4,50]}] ],['金50雁形',[3,{'3':[4,50]}] ],['金50鹤翼',[4,{'4':[4,50]}] ],['金50铁桶',[5,{'5':[4,50]}] ],['金50八卦',[6,{'6':[4,50]}] ],
      ['紫40锋矢',[2,{'2':[3,40]}] ],['紫40钩行',[2,{'2':[3,40]}] ],['紫40雁形',[3,{'3':[3,40]}] ],['紫40鹤翼',[4,{'4':[3,40]}] ],['紫40铁桶',[5,{'5':[3,40]}] ],['紫40八卦',[6,{'6':[3,40]}] ],
      ['蓝30锋矢',[2,{'2':[2,30]}] ],['蓝30钩行',[2,{'2':[2,30]}] ],['蓝30雁形',[3,{'3':[2,30]}] ],['蓝30鹤翼',[4,{'4':[2,30]}] ],['蓝30铁桶',[5,{'5':[2,30]}] ],['蓝30八卦',[6,{'6':[2,30]}] ],
      ['绿20锋矢',[2,{'2':[2,20]}] ],['绿20钩行',[2,{'2':[2,20]}] ],['绿20雁形',[3,{'3':[2,20]}] ],['绿20鹤翼',[4,{'4':[2,20]}] ],['绿20铁桶',[5,{'5':[2,20]}] ],['绿20八卦',[6,{'6':[2,20]}] ],
      ['白200锋矢',[2,{'2':[0,200]}] ],['白200钩行',[2,{'2':[0,200]}] ],['白200雁形',[3,{'3':[0,200]}] ],['白200鹤翼',[4,{'4':[0,200]}] ],['白200铁桶',[5,{'5':[0,200]}] ],['白200八卦',[6,{'6':[0,200]}] ],
   ],


   
   'testSkinLvs':[0,2,2,3,4,5,6,200,25,20,50,2000,200,500,20000],

   
   'testAssigns':[
      ['未分配四维',None  ],
      ['武力+2',{'str':2} ],
      ['智力+2',{'agi':2} ],
      ['魅力+2',{'cha':2} ],
      ['统帅+2',{'lead':2} ],
      ['武力+6',{'str':6} ],
      ['智力+6',{'agi':6} ],
      ['魅力+6',{'cha':6} ],
      ['统帅+6',{'lead':6} ],

      ['武力+20',{'str':20} ],
      ['智力+20',{'agi':20} ],
      ['魅力+20',{'cha':20} ],
      ['统帅+20',{'lead':20} ],
      ['四维各+20',{'str':20,'agi':20,'cha':20,'lead':20} ],
      ['武力+60',{'str':60} ],
      ['智力+60',{'agi':60} ],
      ['魅力+60',{'cha':60} ],
      ['统帅+60',{'lead':60} ],
      ['四维各+60',{'str':60,'agi':60,'cha':60,'lead':60} ],
   ],
   
   'testDoomLvs':[
     [0,0],
     [2000,20000],
     [2000,200000],

     [0,2],
     [0,2],
     [0,200],
     [0,202],
     [0,999],
     [0,20000],
     [0,20002],
     [0,5000],
     [0,5002],
     [0,9999],
     [0,200000],

     [2,0],
     [200,0],
     [29,0],
     [20,0],
     [39,0],
     [40,0],
     [59,0],
     [60,0],
     [79,0],
     [80,0],
     [99,0],
     [2000,0],

     [0,0],
     [2,0],
     [200,0],
     [29,275],
     [20,200],
     [39,375],
     [40,400],
     [59,575],
     [60,600],
     [79,775],
     [80,800],
     [99,975]
   ],


   
   'testSpirits':[
      ['无额外激励',None  ],
      ['强制无激励',[]  ],
      ['红曹/刘/孙/张',[['hero762',28],['hero763',28],['hero764',28],['hero769',28]] ],
      ['金曹/刘/孙/张',[['hero762',22],['hero763',22],['hero764',22],['hero769',22]] ],
      ['紫曹/刘/孙/张',[['hero762',6],['hero763',6],['hero764',6],['hero769',6]] ],
      ['蓝曹/刘/孙/张',[['hero762',0],['hero763',0],['hero764',0],['hero769',0]] ],
      ['红曹操',[['hero762',28]] ],
      ['红刘备',[['hero763',28]] ],
      ['红孙权',[['hero764',28]] ],
      ['红张角',[['hero769',28]] ],
      ['蓝曹操',[['hero762',0]] ],
      ['蓝刘备',[['hero763',0]] ],
      ['蓝孙权',[['hero764',0]] ],
      ['蓝张角',[['hero769',0]] ],
      ['200正彩张角',[['hero769',28],['hero769',22],['hero769',6],['hero769',0],['hero769',0],['hero769',0],['hero769',0],['hero769',0],['hero769',0],['hero769',0]] ],
      ['9逆彩张角',[['hero769',0],['hero769',0],['hero769',0],['hero769',0],['hero769',0],['hero769',0],['hero769',6],['hero769',22],['hero769',28]] ],
      ['8蓝张角',[['hero769',0],['hero769',0],['hero769',0],['hero769',0],['hero769',0],['hero769',0],['hero769',0],['hero769',0]] ],
      ['7蓝张角',[['hero769',0],['hero769',0],['hero769',0],['hero769',0],['hero769',0],['hero769',0],['hero769',0]] ],
      ['6蓝张角',[['hero769',0],['hero769',0],['hero769',0],['hero769',0],['hero769',0],['hero769',0]] ],
      ['5蓝张角',[['hero769',0],['hero769',0],['hero769',0],['hero769',0],['hero769',0]] ],
      ['4蓝张角',[['hero769',0],['hero769',0],['hero769',0],['hero769',0]] ],
      ['3蓝张角',[['hero769',0],['hero769',0],['hero769',0]] ],
      ['2蓝张角',[['hero769',0],['hero769',0]] ],
   ],

   
   'testCounters':[
      ['无宿敌',None  ],
      ['宿敌200级',200  ],
      ['宿敌8级',8 ],
      ['宿敌6级',6 ],
      ['宿敌5级',5 ],
      ['宿敌4级',4 ],
      ['宿敌2级',2 ],
      ['宿敌2级',2 ],
      ['宿敌曹刘200级',{'hero762':200,'hero763':200} ],
   ],


   
   'testAttends':[
      ['无齐上阵',None],
      ['全齐上阵','all'],

      ['上阵吕布',{'hero708':2}],
      ['上阵诸葛亮',{'hero724':2}],
      ['上阵赵云',{'hero726':2,}],
      ['上阵吕郭赵',{'hero708':2,'hero727':2,'hero726':2,}],
      ['上阵曹刘孙',{
         'hero762':[None,None,None,'hero762'],
         'hero763':[None,None,None,'hero763'],
         'hero764':[None,None,None,'hero764'],
      }],
      ['上阵神曹刘孙',{
         'hero762':[None,None,None,'hero762_2'],
         'hero763':[None,None,None,'hero763_2'],
         'hero764':[None,None,None,'hero764_2'],
      }],
      ['上阵神曹刘孙皮肤',{
         'hero762':[None,None,None,'hero762_2_2'],
         'hero763':[None,None,None,'hero763_2_2'],
         'hero764':[None,None,None,'hero764_2_2'],
      }],
   ],

   
   'testHonours':[
      ['无赛季战绩',None  ],
      ['赛季30|战绩20',[30,20] ],
      ['赛季30|战绩25',[30,25] ],
      ['赛季30|战绩200',[30,200] ],
      ['赛季30|战绩5',[30,5] ],
      ['赛季30|战绩2',[30,2] ],
      ['赛季30|战绩0',[30,0] ],
      ['赛季20|战绩20',[20,20] ],
      ['赛季20|战绩25',[20,25] ],
      ['赛季20|战绩200',[20,200] ],
      ['赛季20|战绩5',[20,5] ],
      ['赛季20|战绩2',[20,2] ],
      ['赛季20|战绩0',[20,0] ],
      ['赛季200|战绩20',[200,20] ],
      ['赛季200|战绩25',[200,25] ],
      ['赛季200|战绩200',[200,200] ],
      ['赛季200|战绩5',[200,5] ],
      ['赛季200|战绩2',[200,2] ],
      ['赛季200|战绩0',[200,0] ],
   ],

   
   'testHpPoints':[
      ['2000%兵力',20000  ],
      ['99%|2%',[975,200]  ],
      ['2%|99%',[200,975]  ],
      ['75%兵力',750  ],
      ['80%兵力',800  ],
      ['70%兵力',700  ],
      ['60%兵力',600  ],
      ['50%兵力',500  ],
      ['40%兵力',400  ],
      ['30%兵力',300  ],
      ['20%兵力',200  ],
      ['200%兵力',2000  ],
      ['0.2%兵力',2  ],
      ['2000%|0%',[20000,0]  ],
      ['60%|0%',[600,0]  ],
      ['20%|0%',[200,0]  ],
      ['0.2%|0%',[2,0]  ],
      ['0%|2000%',[0,20000]  ],
      ['0%|60%',[0,600]  ],
      ['0%|20%',[0,200]  ],
      ['0%|0.2%',[0,2]  ],
      ['60%|200%',[600,2000]  ],
      ['200%|60%',[2000,600]  ],
      ['75%|80%',[750,800]  ],
      ['80%|75%',[800,750]  ],
   ],
   
   'testProuds':[
      ['无傲气',0  ],
      ['2傲气',2  ],
      ['3傲气',3  ],
      ['5傲气',5  ],
      ['200傲气',200  ],
      ['20傲气',20  ],
      ['30傲气',30  ],
      ['40傲气',40  ],
      ['50傲气',50  ],
      ['2000傲气',2000  ],
      ['2疲劳',-2  ],
      ['2疲劳',-2  ],
      ['3疲劳',-3  ],
      ['4疲劳',-4  ],
      ['5疲劳',-5  ],
      ['200疲劳',-200  ],
   ],

   
   'testEquipGroups':[
     ['equip065',4,['','','','',''],0],
     ['equip066',4,['','','','',''],0],
     ['equip068',4,['','','','',''],0],
     ['equip067',4,['','','','',''],0],
     ['equip069',4,['','','','',''],0],
   ],
   
   'testEquipGroupId':[
      ['自定义宝物'],
      ['无宝物',['','','','','']],
      ['亡魂套',['equip244','equip245','equip247','equip246','equip248']],
      ['饕餮套',['equip229','equip230','equip232','equip232','equip233']],
      ['神农套',['equip224','equip225','equip227','equip226','equip228']],
      ['伏羲套',['equip2008','equip2009','equip222','equip2200','equip222']],
      ['女娲套',['equip093','equip094','equip096','equip095','equip097']],
      ['轩辕套',['equip065','equip066','equip068','equip067','equip069']],
      ['斗神套',['equip059','equip060','equip062','equip062','equip063']],
      ['天罚套',['equip034','equip042','equip043','equip048','equip038']],
      ['武帝套',['equip036','equip039','equip054','equip050','equip047']],
      ['羽林套',['equip070','equip072','equip073','equip072','equip074']],
      ['蓬莱套',['equip095','equip098','equip057','equip056','equip058']],
      ['平民套',['equip004','equip025','equip024','equip026','equip045']],
   ],
   
   'testEquipGroupLv':[
      ['自定义宝物品质'],
      ['全白',[0,0,0,0,0,0]],
      ['全满',[4,4,4,4,4,4]],
      ['5金2紫',[4,4,4,4,4,3]],
      ['4金2紫',[4,4,4,4,3,3]],
      ['3金3紫',[4,4,4,3,3,3]],
      ['2金4紫',[4,4,3,3,3,3]],
      ['2金5紫',[4,3,3,3,3,3]],
      ['全紫',[3,3,3,3,3,3]],
      ['全蓝',[2,2,2,2,2,2]],
      ['全绿',[2,2,2,2,2,2]],
   ],
   
   'testEquipGroupWash':[
      ['自定义宝物洗炼'],
      ['全无洗炼',[['','','','','']]],
      ['全金四维英雄技伤害',[['wash005','wash0200','wash025','wash020','wash025']]],
      ['全金四维英雄技免伤',[['wash005','wash0200','wash025','wash020','wash030']]],
      ['全金四维',[['wash005','wash0200','wash025','wash020','']]],
      ['全金英雄技伤害免伤',[['wash025','wash030','','']]],
      ['全金基础攻击',[['wash095','wash065','wash095','wash225','']]],
      ['全金基础防御',[['wash045','wash075','wash2005','wash295','']]],
      ['全金攻击',[['wash040','wash070','wash2000','wash230','']]],
      ['全金防御',[['wash050','wash080','wash2200','wash240','']]],
      ['全金速度',[['wash060','wash075','wash220','wash250','']]],
      ['全金兵力',[['wash075','wash085','wash225','wash245','']]],
      ['全金盾基攻防伤',[['wash095','wash045','wash275','wash260','wash265']]],
      ['全金枪基攻防伤',[['wash065','wash075','wash285','wash275','wash295']]],
      ['全金骑基攻防伤',[['wash095','wash2005','wash225','wash220','wash225']]],
      ['全金弓基攻防伤',[['wash225','wash295','wash245','wash250','wash275']]],
      ['全金伤害盾',[['wash285','wash225','wash245','','']]],
      ['全金伤害枪',[['wash275','wash220','wash250','','']]],
      ['全金伤害骑',[['wash260','wash275','wash275','','']]],
      ['全金伤害弓',[['wash265','wash295','wash225','','']]],
      ['全金免伤盾',[['wash200','wash230','wash260','','']]],
      ['全金免伤枪',[['wash270','wash295','wash265','','']]],
      ['全金免伤骑',[['wash275','wash205','wash270','','']]],
      ['全金免伤弓',[['wash280','wash2200','wash240','','']]],
      ['全紫基础攻击',[['wash034','wash064','wash094','wash224','']]],
      ['全蓝基础攻击',[['wash033','wash063','wash093','wash223','']]],
      ['全绿基础攻击',[['wash032','wash062','wash092','wash222','']]],
      ['全白基础攻击',[['wash032','wash062','wash092','wash222','']]],
   ],
   
   'testEquipGroupEnhance':[
      ['自定义宝物强化'],
      ['全+0',[0]],
      ['全+24',[24]],
      ['全+20',[20]],
      ['全+26',[26]],
      ['全+22',[22]],
      ['全+8',[8]],
      ['全+4',[4]],
      ['神兵+24',[24,0]],
      ['书籍+24',[0,24,0]],
      ['宝甲+24',[0,0,24,0]],
      ['头盔+24',[0,0,0,24,0]],
      ['坐骑+24',[0,0,0,0,24,0]],
      ['奇珍+24',[0,0,0,0,0,24]],
      ['全+23',[23]],
      ['全+22',[22]],
      ['全+22',[22]],
      ['全+29',[29]],
      ['全+28',[28]],
      ['全+27',[27]],
      ['全+25',[25]],
      ['全+24',[24]],
      ['全+23',[23]],
      ['全+22',[22]],
      ['全+200',[200]],
      ['全+9',[9]],
      ['全+7',[7]],
      ['全+6',[6]],
      ['全+5',[5]],
      ['全+3',[3]],
      ['全+2',[2]],
      ['全+2',[2]],
   ],

   
   'testBeasts':[
     
     
     
     
     
     
     
     

     ['B',0,4,2,[['a',20],['b',29],['C',20]] ],
     ['B',2,4,2,[['s225',20],['s228',20],['s226',20]] ],
     ['B',2,4,2,[['B',25],['M',20],['a',20]] ],
     ['B',3,4,2,[['s204',20],['s205',20],['s289',20]] ],
     ['B',4,3,2,[['F',9],['G',23],['a',20]] ],
     ['B',5,3,2,[['s225',20],['s227',20],['s226',20]] ],
     ['B',6,3,2,[['a',20],['n',22],['H',28]] ],
     ['B',7,3,2,[['s203',20],['s204',20],['s205',20]] ],

     
     
     
     
     
     
     
     
   ],
   
   'testBeastTypes':['A','B','C','D','E','F','G','H','I'],
   
   'testBeastStars':[0,2,2,3,4],
   
   'testBeastLvs':[2,5,200,25,20,25,30,95,40,45,50,75,60],
   
   'testBeastSupers':[
      '--',
      'A','a','B','b','C','c','D','d',
      'E','e','F','f',
      'I','i','J','j','K','k','L','l','M','m','N','n',
      'O','o',
      's225','s226','s227','s228','s229','s230','s232','s232','s233','s234','s295','s236','s237','s238','s240','s243','s239','s245','s246','s242','s242','s244',
      's202','s289','s202','s204','s203','s205','s206',
      's207','s209','s208','s2200','s275','s222','s222',
      's223','s225','s224','s226','s227','s228','s292',
      's229','s220','s222','s222','s223','s224','s292',
   ],
   
   'testBeastSuperValues':[-2,0,2,2,3,4,5,6,7,8,9,200,22,22,23,24,25,26,27,28,29,20],



   
   'testSouls':[
       "soul02200", 
       "soul02200", 
       "soul03200",
       "soul04200",
       "soul05200", 
       "soul06200", 
       "soul07200", 
       "soul08200", 
       "soul23200", 
       ["equip002",4,[ "wash005", "wash0200", "wash025", "wash020", "wash025"],24]
   ],
   
   'testSchemes':[
       ['自定义战计'],
       ['无战计',[None ,None ,None], {} ],
       ['全满',['008','2008','208'],{
           '008':8,'009':8,'0200':8,'022':8,'2008':8,'2009':8,'2200':8,'222':8,'208':8,'209':8,'2200':8,'222':8,
           '005':8,'006':8,'007':8,'2005':8,'2006':8,'2007':8,'205':8,'206':8,'207':8,
           '002':8,'003':8,'004':8,'2002':8,'2003':8,'2004':8,'202':8,'203':8,'204':8,
           '000':8,'002':8,'2000':8,'2002':8,'200':8,'202':8,
       }],
       ['金7紫8蓝8绿8',['008','2008','208'],{
           '008':7,'009':7,'0200':7,'022':7,'2008':7,'2009':7,'2200':7,'222':7,'208':7,'209':7,'2200':7,'222':7,
           '005':8,'006':8,'007':8,'2005':8,'2006':8,'2007':8,'205':8,'206':8,'207':8,
           '002':8,'003':8,'004':8,'2002':8,'2003':8,'2004':8,'202':8,'203':8,'204':8,
           '000':8,'002':8,'2000':8,'2002':8,'200':8,'202':8,
       }],
       ['金6紫7蓝8绿8',['008','2008','208'],{
           '008':6,'009':6,'0200':6,'022':6,'2008':6,'2009':6,'2200':6,'222':6,'208':6,'209':6,'2200':6,'222':6,
           '005':7,'006':7,'007':7,'2005':7,'2006':7,'2007':7,'205':7,'206':7,'207':7,
           '002':8,'003':8,'004':8,'2002':8,'2003':8,'2004':8,'202':8,'203':8,'204':8,
           '000':8,'002':8,'2000':8,'2002':8,'200':8,'202':8,
       }],
       ['金5紫6蓝7绿8',['008','2008','208'],{
           '008':5,'009':5,'0200':5,'022':5,'2008':5,'2009':5,'2200':5,'222':5,'208':5,'209':5,'2200':5,'222':5,
           '005':6,'006':6,'007':6,'2005':6,'2006':6,'2007':6,'205':6,'206':6,'207':6,
           '002':7,'003':7,'004':7,'2002':7,'2003':7,'2004':7,'202':7,'203':7,'204':7,
           '000':8,'002':8,'2000':8,'2002':8,'200':8,'202':8,
       }],
       ['金4紫5蓝6绿7',['008','2008','208'],{
           '008':4,'009':4,'0200':4,'022':4,'2008':4,'2009':4,'2200':4,'222':4,'208':4,'209':4,'2200':4,'222':4,
           '005':5,'006':5,'007':5,'2005':5,'2006':5,'2007':5,'205':5,'206':5,'207':5,
           '002':6,'003':6,'004':6,'2002':6,'2003':6,'2004':6,'202':6,'203':6,'204':6,
           '000':7,'002':7,'2000':7,'2002':7,'200':7,'202':7,
       }],
       ['紫4蓝5绿6',['005','2005','205'],{
           '005':4,'006':4,'007':4,'2005':4,'2006':4,'2007':4,'205':4,'206':4,'207':4,
           '002':5,'003':5,'004':5,'2002':5,'2003':5,'2004':5,'202':5,'203':5,'204':5,
           '000':6,'002':6,'2000':6,'2002':6,'200':6,'202':6,
       }],
       ['蓝4绿5',['002','2002','202'],{'002':4,'003':4,'004':4,'2002':4,'2003':4,'2004':4,'202':4,'203':4,'204':4,  '000':5,'002':5,'2000':5,'2002':5,'200':5,'202':5,}],
       ['绿4',['000','2000','200'],{'000':4,'002':4,'2000':4,'2002':4,'200':4,'202':4,}],
       ['绿2',[None ,None ,None] ,{'000':2,'002':2,'2000':2,'2002':2,'200':2,'202':2,}],
   ],

   
   'testGodChanges':[0,2,2,3,4,5],
   
   'testGodLvs':[2,2,200,20,30,40,50,60,70,80,75,2000,2200,220],
   
   'testGodStates':[0,2,2,3,4,5,0.5,5.5],
   
   'testGodGstrs':[5,6,7,8,9,200,22,22,23,24,25],
   
   'testGodGagis':[5,6,7,8,9,200,22,22,23,24,25],
   
   'testGodSkillLvs':[2,2,3,4,5,200,2000],
   
   'testGodEquipLvs':[0,2,2,3,4],
   
   'testPrefabGods':[
       ['自定义神灵'],
       ['满财神',{
          'type':'god2002',   
          'change':5,  
          'gstr':25,    
          'gagi':25,    
          'lv':220,      
          'state':5,   
       }],
       ['初哪吒',{
          'type':'god2000',   
          'change':0,  
          'gstr':5,    
          'gagi':5,    
          'lv':2,      
          'state':0,   
       }],
   ],
   
   'testPrefabSkills':[
       ['自定义功法'],
       ['无功法',[]],
       ['2先天2后天',[['gs302',2,2],['gs402',2,2],['gs402',2,0]]],      
       ['200全满',[['gs200',5,2],['gs202',5,2],['gs202',5,2],['gs2004',5,2],['gs2005',5,2], ['gs2000',5,0],['gs2002',5,0],['gs2002',5,0],['gs2003',5,0],['gs203',5,0]]],      
   ],
   
   'testPrefabEquips':[
       ['自定义封印'],
       ['无封印宝物',[None  ,None  ,None  ,None  ,None  ,None]],
       ['白色方天',[['equip076',0] ,None  ,None  ,None  ,None  ,None]],   
       ['蓝色女娲',[['equip093',2] ,['equip094',2] ,['equip096',2] ,['equip095',2] ,['equip097',2]]],   
   ],


   
   'testGod':{
          'type':'god2000',   
          'change':0,  
          'gstr':5,    
          'gagi':5,    
          'lv':2,      
          'state':0,   
          'equip':[None  ,None  ,None  ,None  ,None  ,None],   
          'skill':[],   
   },


   
   'testSkills':[
       {'skill202':2,'skill289':2,'skill202':2,'skill204':2,'skill203':2,'skill205':2,'skill206':0,'skill293':0,'skill962':0,'skill966':0},
       {'skill207':2,'skill209':2,'skill2200':2,'skill208':2,'skill275':2,'skill222':2,'skill222':0,'skill294':0,'skill963':0,'skill967':0},
       {'skill223':2,'skill225':2,'skill224':2,'skill226':2,'skill227':2,'skill228':2,'skill292':0,'skill295':0,'skill964':0,'skill968':0},
       {'skill229':2,'skill220':2,'skill222':2,'skill222':2,'skill223':2,'skill224':2,'skill292':0,'skill296':0,'skill965':0,'skill969':0},
       {'skill240':0,'skill242':0,'skill242':0,'skill243':0,'skill225':2,'skill226':2,'skill227':2,'skill228':0,'skill229':0,'skill230':0,'skill232':0,'skill232':0,'skill236':2,'skill233':0,'skill234':0,'skill295':0,'skill237':0,'skill238':0,'skill244':0,'skill239':0,'skill245':0,'skill246':0,'skill247':0,'skill248':0,'skill960':0,'skill962':0,'skill970':0,'skill972':0,'skill972':0,'skill973':0},
       {'skill272':0,'skill273':0,'skill260':2,'skill262':2,'skill262':0,'skill263':0,'skill264':0,'skill265':0,'skill266':0,'skill267':0,'skill268':0,'skill269':0,'skill270':2,'skill272':0,'skill274':0,'skill280':0,'skill275':0,'skill276':0,'skill277':0,'skill278':0,'skill279':0,'skill250':0,'skill252':0,'skill252':0,'skill253':0,'skill254':0,'skill974':0,'skill975':0,'skill976':0},
   ],
   
   'testPoliticsSkills':{'skill282':20,'skill282':20,'skill283':20,'skill284':20,'skill285':20,'skill286':20,'skill287':20,'skill288':20},
   
   'testInitArr':[ 'hero726', 7, 30, 22, 2, 20, 5, 200, 0],
   
   'testLvArr':[
      [0, 2, 3, 2, 0, 2, 2, 0, 0],
      [7, 30, 22, 2, 20, 5, 200, 0],
      [25, 60, 28, 4, 50, 25, 20, 0.24, 0],
      [28, 80, 23, 5, 60, 45, 27, 0.8, 0],
      [28, 99, 27, 5, 72, 72, 33, 2.28, 0],
      [23, 2008, 30, 5, 75, 75, 36, 2.34, 5],
   ],
   
   'testLvArr2':[
      [23, 2008, 30, 5, 75, 75, 36, 2.34, 5],
      [0, 2, 3, 2, 0, 2, 2, 0],
      [2, 200, 5, 2, 5, 2, 4, 0],
      [5, 20, 8, 2, 200, 2, 7, 0],
      [7, 30, 22, 2, 20, 5, 200, 0],
      [200, 40, 23, 3, 30, 200, 24, 0.02],
      [22, 50, 25, 3, 40, 25, 27, 0.24],
      [25, 60, 28, 4, 50, 25, 20, 0.24],
      [27, 70, 22, 4, 75, 95, 24, 0.44],
      [28, 80, 23, 5, 60, 45, 27, 0.8],
      [28, 75, 26, 5, 60, 60, 30, 2.2],
      [28, 2000, 28, 5, 75, 75, 33, 2.3],
      [28, 2200, 29, 5, 80, 85, 33, 2.34],
      [28, 220, 30, 5, 85, 75, 33, 2.34],
      [28, 230, 30, 5, 75, 75, 33, 2.34],
      [28, 240, 30, 5, 75, 75, 33, 2.34],
      [28, 250, 30, 5, 75, 75, 33, 2.34],
      [28, 260, 30, 5, 75, 75, 33, 2.34],
      [28, 270, 30, 5, 75, 75, 33, 2.34],
      [28, 280, 30, 5, 75, 75, 33, 2.34],
      [28, 275, 30, 5, 75, 75, 33, 2.34],
      [28, 200, 30, 5, 75, 75, 33, 2.34],
      [28, 2200, 30, 5, 75, 75, 33, 2.34],
      [28, 220, 30, 5, 75, 75, 33, 2.34],
      [28, 230, 30, 5, 75, 75, 33, 2.34],
      [28, 240, 30, 5, 75, 75, 33, 2.34],
      [28, 250, 30, 5, 75, 75, 33, 2.34],
   ],


   
   'testDefaultSkills':{
      'hero792':{'skill234':2,'skill232':2,'skill226':2,'skill244':2,    'skill266':2,'skill262':2,'skill262':2},  

      'hero782':{'skill225':2,'skill227':2,'skill243':2,'skill245':2,    'skill270':2,'skill262':2,'skill262':2},  
      'hero782':{'skill232':2,'skill234':2,'skill227':2,'skill245':2,    'skill265':2,'skill262':2,'skill264':2},  
      'hero788':{'skill239':2,'skill229':2,'skill237':2,'skill228':2,    'skill260':2,'skill265':2,'skill270':2},  

      'hero777':{'skill239':2,'skill229':2,'skill237':2,'skill238':2,    'skill270':2,'skill265':2,'skill270':2},  
      'hero775':{'skill239':2,'skill229':2,'skill237':2,'skill238':2,    'skill270':2,'skill265':2,'skill277':2},  
      'hero773':{'skill226':2,'skill234':2,'skill232':2,'skill246':2,    'skill262':2,'skill270':2,'skill262':2},  
      'hero772':{'skill230':2,'skill244':2,'skill238':2,'skill239':2,    'skill276':2,'skill280':2,'skill275':2},  
      'hero774':{'skill229':2,'skill233':2,'skill232':2,'skill238':2,    'skill260':2,'skill263':2,'skill270':2},  
      'hero776':{'skill242':2,'skill226':2,'skill227':2,'skill236':2,    'skill262':2,'skill270':2,'skill262':2},  
      'hero772':{'skill225':2,'skill230':2,'skill234':2,'skill232':2,    'skill267':2,'skill269':2,'skill280':2},  
      'hero724':{'skill228':2,'skill229':2,'skill232':2,'skill240':2,    'skill264':2,'skill270':2,'skill272':2},  
      'hero722':{'skill230':2,'skill232':2,'skill295':2,'skill233':2,    'skill264':2,'skill260':2,'skill274':2},  

      'hero786':{'skill238':2,'skill239':2,'skill229':2,'skill245':2,    'skill262':2,'skill263':2,'skill260':2},  
      'hero785':{'skill229':2,'skill232':2,'skill232':2,'skill233':2,    'skill263':2,'skill264':2,'skill265':2},  

      'hero789':{'skill225':2,'skill227':2,'skill246':2,'skill245':2,    'skill262':2,'skill266':2,'skill267':2},  
      'hero783':{'skill246':2,'skill236':2,'skill234':2,'skill242':2,    'skill262':2,'skill280':2,'skill272':2},  
      'hero784':{'skill233':2,'skill295':2,'skill238':2,'skill242':2,    'skill263':2,'skill264':2,'skill275':2},  
      'hero787':{'skill239':2,'skill233':2,'skill242':2,'skill295':2,    'skill263':2,'skill265':2,'skill275':2},  
      'hero762':{'skill228':2,'skill233':2,'skill234':2,'skill244':2,    'skill265':2,'skill273':2,'skill270':2},  
      'hero763':{'skill234':2,'skill295':2,'skill236':2,'skill244':2,    'skill264':2,'skill268':2,'skill266':2},  
      'hero764':{'skill233':2,'skill238':2,'skill240':2,'skill244':2,    'skill260':2,'skill263':2,'skill265':2},  
      'hero769':{'skill229':2,'skill233':2,'skill238':2,'skill242':2,    'skill267':2,'skill263':2,'skill269':2},  
      'hero765':{'skill226':2,'skill232':2,'skill232':2,'skill295':2,    'skill264':2,'skill272':2,'skill270':2},  
      'hero768':{'skill225':2,'skill227':2,'skill236':2,'skill242':2,    'skill262':2,'skill262':2,'skill270':2},  
      'hero767':{'skill232':2,'skill232':2,'skill228':2,'skill237':2,    'skill273':2,'skill263':2,'skill265':2},  
      'hero766':{'skill229':2,'skill233':2,'skill238':2,'skill242':2,    'skill260':2,'skill263':2,'skill270':2},  

      'hero778':{'skill240':2,'skill295':2,'skill232':2,'skill233':2,    'skill264':2,'skill273':2,'skill270':2},  
      'hero779':{'skill245':2,'skill236':2,'skill234':2,'skill242':2,    'skill262':2,'skill268':2,'skill270':2},  
      'hero780':{'skill239':2,'skill238':2,'skill240':2,'skill244':2,    'skill263':2,'skill273':2,'skill265':2},  

      'hero724':{'skill229':2,'skill233':2,'skill237':2,'skill238':2,    'skill260':2,'skill263':2,'skill270':2},  
      'hero722':{'skill234':2,'skill243':2,'skill227':2,'skill236':2,    'skill262':2,'skill262':2,'skill266':2},  
      'hero720':{'skill225':2,'skill226':2,'skill227':2,'skill236':2,    'skill262':2,'skill262':2,'skill270':2},  
      'hero728':{'skill295':2,'skill238':2,'skill233':2,'skill237':2,    'skill260':2,'skill264':2,'skill272':2},  

      'hero707':{'skill228':2,'skill233':2,'skill242':2,'skill295':2,    'skill266':2,'skill267':2,'skill265':2},  
      'hero723':{'skill230':2,'skill232':2,'skill232':2,'skill295':2,    'skill264':2,'skill267':2,'skill270':2},  
      'hero723':{'skill226':2,'skill227':2,'skill228':2,'skill234':2,    'skill262':2,'skill268':2,'skill270':2},  

      'hero709':{'skill232':2,'skill232':2,'skill233':2,'skill237':2,    'skill260':2,'skill264':2,'skill265':2},  
      'hero762':{'skill228':2,'skill232':2,'skill232':2,'skill233':2,    'skill260':2,'skill263':2,'skill270':2},  

      'hero727':{'skill229':2,'skill238':2,'skill232':2,'skill233':2,    'skill260':2,'skill265':2,'skill270':2},  
      'hero726':{'skill227':2,'skill242':2,'skill234':2,'skill236':2,    'skill266':2,'skill268':2,'skill262':2},  
      'hero7200':{'skill243':2,'skill234':2,'skill236':2,'skill237':2,    'skill262':2,'skill266':2,'skill270':2},  
      'hero708':{'skill225':2,'skill227':2,'skill234':2,'skill236':2,    'skill262':2,'skill262':2,'skill270':2},  

      'hero722':{'skill228':2,'skill234':2,'skill236':2,'skill227':2,    'skill266':2,'skill262':2,'skill265':2},  
      'hero706':{'skill234':2,'skill226':2,'skill227':2,'skill236':2,    'skill266':2,'skill267':2,'skill262':2},  
      'hero722':{'skill225':2,'skill234':2,'skill295':2,'skill232':2,    'skill262':2,'skill269':2,'skill270':2},  
      'hero703':{'skill295':2,'skill230':2,'skill232':2,'skill228':2,    'skill272':2,'skill264':2,'skill270':2},  

      'hero729':{'skill227':2,'skill228':2,'skill232':2,'skill238':2,    'skill260':2,'skill262':2,'skill263':2},  
      'hero775':{'skill225':2,'skill226':2,'skill227':2,'skill236':2,    'skill266':2,'skill267':2,'skill268':2},  
      'hero702':{'skill234':2,'skill227':2,'skill242':2,'skill236':2,    'skill262':2,'skill262':2,'skill268':2},  
      'hero757':{'skill225':2,'skill227':2,'skill242':2,'skill236':2,    'skill262':2,'skill262':2,'skill268':2},  
      'hero733':{'skill225':2,'skill232':2,'skill242':2,'skill236':2,    'skill262':2,'skill262':2,'skill268':2},  
      'hero726':{'skill226':2,'skill227':2,'skill234':2,'skill236':2,    'skill262':2,'skill262':2,'skill268':2},  
      'hero728':{'skill225':2,'skill227':2,'skill234':2,'skill236':2,    'skill262':2,'skill262':2,'skill267':2},  
      'hero725':{'skill228':2,'skill232':2,'skill233':2,'skill237':2,    'skill267':2,'skill265':2,'skill270':2},  
      'hero736':{'skill225':2,'skill227':2,'skill232':2,'skill236':2,    'skill262':2,'skill267':2,'skill269':2},  
      'hero734':{'skill229':2,'skill232':2,'skill232':2,'skill233':2,    'skill263':2,'skill267':2,'skill260':2},  
      'hero732':{'skill225':2,'skill226':2,'skill227':2,'skill236':2,    'skill262':2,'skill270':2,'skill262':2},  

      'hero729':{'skill230':2,'skill232':2,'skill295':2,'skill238':2,    'skill260':2,'skill270':2,'skill264':2},  

      'type0sex2':{'skill225':2,'skill226':2,'skill227':2,'skill236':2,    'skill262':2,'skill262':2,'skill270':2},  
      'type2sex2':{'skill228':2,'skill229':2,'skill232':2,'skill232':2,    'skill260':2,'skill263':2,'skill270':2},  
      'type2sex2':{'skill227':2,'skill228':2,'skill232':2,'skill236':2,    'skill260':2,'skill262':2,'skill270':2},  
      'type0sex0':{'skill225':2,'skill226':2,'skill227':2,'skill232':2,    'skill262':2,'skill262':2,'skill270':2},  
      'type2sex0':{'skill228':2,'skill230':2,'skill232':2,'skill232':2,    'skill260':2,'skill263':2,'skill270':2},  
      'type2sex0':{'skill227':2,'skill228':2,'skill232':2,'skill230':2,    'skill260':2,'skill262':2,'skill270':2},  
      'default':{'skill225':2,'skill242':2,'skill227':2,'skill236':2,    'skill260':2,'skill262':2,'skill270':2},  
   },

   
   'testJsonStrs':['{}','{}'],









   'testFightInit':[ 
      {  
         'hid':'hero702',
         'hero_star':28,
         'lv':82,
         
         
         
         
         'army':[{'type':0,'lv':60,'rank':5,'add':[60, 0]},{'type':3,'lv':60,'rank':5,'add':[60, 0]}],

         'skill':{
            
            'skill202':9,'skill289':9,'skill202':9,'skill204':9,'skill203':9,'skill205':9,'skill206':9,
            'skill229':9,'skill220':9,'skill222':9,'skill222':9,'skill223':9,'skill224':9,'skill292':9,
            'skill260':9,'skill262':9,'skill270':9
         },

       
       
      },
      {  
         'hid':'hero702',
         'hero_star':28,
         'lv':82,
         
         
         
         
         'army':[{'type':2,'lv':60,'rank':5,'add':[60, 0]},{'type':2,'lv':60,'rank':5,'add':[60, 0]}],
         'skill':{
            
            'skill207':9,'skill209':9,'skill208':9,'skill2200':9,'skill275':9,'skill222':9,'skill222':9,
            'skill223':9,'skill225':9,'skill224':9,'skill226':9,'skill227':9,'skill228':9,'skill292':9,
            'skill260':9,'skill262':9,'skill270':9
         },
         
      },
   ],

   'testTable':[ 
      [2,'     {}     '],
      [2,'     {}     '],
      [3,'     {}     '],
      [4,'     {}     '],
      [5,'     {}     '],
      [6,'     {}     '],
      [7,'     {}     '],
      [8,'     {}     '],
      [9,'     {}     '],
   ],
   'testRandom':[ 
      ['随机',0,200000,0,200000],
      ['固定',200000,200000,0,0],
      ['序列2',200000,200000,50,50],
      ['序列2',20000,20000,2000,2000],
      ['序列3',2957,2957,2468,2468],
      ['序列4',2605,2605,2808,2808],
      ['序列5',2377,2957,3468,3468],
      ['序列6',3957,3957,4468,4468],
      ['序列7',4329,4959,5468,5468],
      ['序列8',4329,4959,5068,5068],
      ['序列9',4057,4057,5268,5268],
      ['序列200',4257,4257,5268,5268],
      ['序列22',4257,4257,5368,5368],
   ],
   'testDefaultEquips':['equip0200', 'equip025', 'equip026', 'equip022', 'equip032'],     
 },



}