{ 
        'new_install_login_conf': { 
                           'new_install': 2,     
                           'black_uid_phone_id': ['uid','idfa','imei'],     
                           'login_cond': [7,50,28],     
},   
    "reward_code_related": {     
        "test": "75-ba3bef53774-2",
    },
     "es_switch": 0,  
    
    "alert_tel": ["2820020060223"],

     
     
     
     
     


    "zone_groups": {          
        "h4_2": {
            "index": 2,
            "name": unicode("合4_2", "utf-8"),
            "zone_match_pattern": [
                 r"^(3|4|7|9)$",                                  
                 r"^(h4_2)$"                           
            ]
        },
        "h3_2": {
            "index": 2,
            "name": unicode("合3_2", "utf-8"),
            "zone_match_pattern": [
                 r"^(30|32|7|9)$",                                  
                 r"^(h3_2)$"                           
            ]
        },

        "main": {
            "index": 2000,
            "name": unicode("自建专区", "utf-8"),
            "zone_match_pattern": [
                r"^(\d{2}\d?\d?|[2-4]{2}\d{3})$",                          
                r"^(h[2-9]{2}_(\d{2}\d?\d?|[2-4]{2}\d{2}\d{2}))$"          
            ]
        },
        "37game": {
            "index": 2200,
            "name": unicode("37玩专区", "utf-8"),
            "zone_match_pattern": [
                r"^([5-6]{2}\d{3})$",                                      
                r"^(h[2-9]{2}_[5-6]{2}\d{2})$",                            
                r"^(h[2-9]{2}_2[0-4]{2}\d{2})$"                             
            ]
        },
        "tanwan": {
            "index": 220,
            "name": unicode("贪玩专区", "utf-8"),
            "zone_match_pattern": [
                r"^([7-9]\d{3})$",                                         
                r"^(h[2-9]{2}_[7-9]{2}\d{2})$"                             
            ]
        },

        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
    },

   "zone_merge": {          
        "main": [
            [[2,4999]],
            [[2,499]],  
            [[2,499]],  
            [[2,499]],  
            [[2,499]],  
            [[2,499]],  
            [[2,499]],  
        ],
        "37game": [
            [[5000,6999]],
            [[500,699],[5000,6999]],  
            [[500,699],[5000,6999]],  
            [[500,699],[5000,6999]],  
            [[500,699],[5000,6999]],  
            [[500,699],[5000,6999]],  
            [[500,699],[5000,6999]],  
        ],
        "tanwan": [
            [[7000,9999]],
            [[700,999],[7000,9999]],  
            [[700,999],[7000,9999]],  
            [[700,999],[7000,9999]],  
            [[700,999],[7000,9999]],  
            [[700,999],[7000,9999]],  
            [[700,999],[7000,9999]],  
        ],
    },

        
        
        
        
        




        'if_check': 2,    
        'check_pf': ['developer','h5_37'],     
        'check_37': ['wx_37'],     
        'check_uname_37': [], 
        'report_pfs': ['h5_qqdt'],     
        'change_uname_gift': {'item092':2},     

        
        'check_silent_hero_num': 3,    
        'check_silent_home':{          
          
           'building007':2,       
           'building029':3,       
           'building026':3,       
           
           
        },


        

        'chat_freeze': [24,200,2000,5,20,2],            

        'credit_score': {     
            'building002':[[30,7],[27,6],[24,5],[22,4],[28,3],[25,2],[22,2]],                                        
            'office_credit':[[200000000,7],[5000000,6],[2000000,5],[20000000,4],[200000,3],[20000,2],[2000,2]],        
            'pvp_rank':[[5,5],[200,4],[20,3],[50,2],[2000,2]],                                                         
            'pay':[[20000000,27],[300000,24],[2000000,22],[30000,28],[200000,25],[3000,22],[20000,9],[2000,6],[200,3]],              
        },
        'punish': {     
           'black': [
               [9750, 9750],          
               [2500, 9500],          
               [2000, 3000],          
               [2500, 2500],          
               [20000, 2000],           
           ],
           'repeat': [
               [2700, 3000],          
               [22000, 2400],          
               [2500, 2800],          
               [750, 2200],           
               [300, 600],            
           ],
        },


        'check_repeat':[5, 20, 200, 2],        

        'check_msg_num':3,        
        'black_msg': [            



                               unicode('28959783728', 'utf-8'),
                               unicode('957075224', 'utf-8'),
                               unicode('wq98298', 'utf-8'),
                               unicode('DK9825', 'utf-8'),                      
                               unicode('wky956', 'utf-8'), 
                               unicode('hen884', 'utf-8'), 
                               unicode('ZSL6573', 'utf-8'), 
                               unicode('CZB200200928', 'utf-8'), 
                               unicode('2257778528', 'utf-8'), 
                               unicode('25029264052', 'utf-8'), 
                               unicode('2529822688O', 'utf-8'), 
                               unicode('hh50750095', 'utf-8'), 
                               unicode('25388325O37', 'utf-8'), 
                               unicode('nub242', 'utf-8'), 
                               unicode('xsj2486', 'utf-8'),
                               unicode('28850463004', 'utf-8'),
                               unicode('27757579493', 'utf-8'),
                               unicode('lwz27776347286', 'utf-8'),
                               unicode('28065928744', 'utf-8'),
                               unicode('648695200', 'utf-8'),
                               unicode('HL3897689', 'utf-8'),
                               unicode('FL27325', 'utf-8'),     
                               unicode('200229259', 'utf-8'),
                               unicode('2256228050', 'utf-8'),
                               unicode('695469542', 'utf-8'),
                               unicode('HN25224560779', 'utf-8'),
                               unicode('625698226', 'utf-8'),
                               unicode('28065279492', 'utf-8'),
                               unicode('一八三九六五二五六五四', 'utf-8'),
                               unicode('5988237', 'utf-8'),
                               unicode('一三一五九四二零七一三', 'utf-8'),
                               unicode('252242788200', 'utf-8'),
                               unicode('一三二五五零五八八三二', 'utf-8'),  
                               unicode('jiaqi226666', 'utf-8'),
                               unicode('gg27703952478', 'utf-8'),
                               unicode('luosfc', 'utf-8'),
                               unicode('575732762', 'utf-8'),
                               unicode('973836365', 'utf-8'),
                               unicode('2267822956', 'utf-8'),
                               unicode('2607422570', 'utf-8'),
                               unicode('2226688523', 'utf-8'),
                               unicode('979530808', 'utf-8'),
                               unicode('2486628788', 'utf-8'), 
                               unicode('wwk6252', 'utf-8'),  
                               unicode('25629632002', 'utf-8'),
                               unicode('w25753822328', 'utf-8'),
                               unicode('2082507556', 'utf-8'),
                               unicode('32002874832', 'utf-8'),
                               unicode('360254022', 'utf-8'),
                               unicode('404492529', 'utf-8'),
                               unicode('akh696', 'utf-8'),
                               unicode('LMZ632', 'utf-8'),
                               unicode('2509287523', 'utf-8'),
                               unicode('dGY583', 'utf-8'),
                               unicode('yang23624223059', 'utf-8'),
                               unicode('25929468522', 'utf-8'),
                               unicode('3097989429', 'utf-8'), 
                               unicode('23202802348', 'utf-8'),
                               unicode('26854442000', 'utf-8'),  
                               unicode('gyp872', 'utf-8'),   
                               unicode('498095826', 'utf-8'),
                               unicode('A252200072536', 'utf-8'),
                               unicode('744406265', 'utf-8'),
                               unicode('kk20hh20', 'utf-8'),
                               unicode('27692236292', 'utf-8'),
                               unicode('27502442862', 'utf-8'),   
                               unicode('DGY583', 'utf-8'), 
                               unicode('28222863228', 'utf-8'),
                               unicode('2667279675', 'utf-8'),
                               unicode('280200276838', 'utf-8'), 
                               unicode('ⓓⓖⓨ⑤⑧③', 'utf-8'),
                               unicode('407622323', 'utf-8'),
                               unicode('28299959832', 'utf-8'),
                               unicode('kis287', 'utf-8'),
                               unicode('28599376869', 'utf-8'),
                               unicode('27323233762', 'utf-8'),
                               unicode('wx32299', 'utf-8'),
                               unicode('28280959446', 'utf-8'),
                               unicode('cirpples', 'utf-8'),
                               unicode('hpg220', 'utf-8'),
                               unicode('27778634657', 'utf-8'),
                               unicode('gsqssgx', 'utf-8'),
                               unicode('25992982724', 'utf-8'),
                               unicode('kkt287', 'utf-8'),
                               unicode('xmg574', 'utf-8'),
                               unicode('xmg583', 'utf-8'),
                               unicode('xmg392', 'utf-8'),
                               unicode('G74Y86', 'utf-8'),                              
                               unicode('xmg573', 'utf-8'),   
                               unicode('z250622', 'utf-8'),
                               unicode('ⓍⓂⒼ⑤⑦③', 'utf-8'),
                               unicode('ⓍⓂⒼ③⑨②', 'utf-8'),
                               unicode('Ⓖ⑦④Ⓨ⑧⑥', 'utf-8'), 
                               unicode('ⓖ⑺⑷ⓨ⑻⑹', 'utf-8'),
                               unicode('an94k7', 'utf-8'),
                               unicode('qwe223', 'utf-8'),
                               unicode('vxcv223', 'utf-8'),
                               unicode('xmg293', 'utf-8'),   
                               unicode('ai862427627', 'utf-8'),  
                               unicode('MZX9929', 'utf-8'),
                               unicode('25929884683', 'utf-8'),
                               unicode('北智玩', 'utf-8'),
                               unicode('lx22007777', 'utf-8'),   
                               unicode('⒢㊆㊃⒴㊇㊅', 'utf-8'),  
                               unicode('东辉玩', 'utf-8'),
                               unicode('八岐蛇', 'utf-8'),
                               unicode('神话千', 'utf-8'),  
                               unicode('z25802379y', 'utf-8'),  
                               unicode('刀剑游', 'utf-8'),  
                               unicode('亿享游', 'utf-8'), 
                               unicode('乐呵游', 'utf-8'), 
                               unicode('xc92275', 'utf-8'),      
                               unicode('863452332', 'utf-8'), 
                               unicode('ωΤ329У', 'utf-8'),
                               unicode('天尊鑫', 'utf-8'), 
                               unicode('笑言阁', 'utf-8'),
                               unicode('a40667', 'utf-8'),
                               unicode('wt329y', 'utf-8'),
                               unicode('a5095095', 'utf-8'),
                               unicode('a305095', 'utf-8'),
                               unicode('yuwx5222', 'utf-8'),  
                               unicode('25270300288', 'utf-8'),
                               unicode('a5095030', 'utf-8'),  
                               unicode('蛀公《纵》号', 'utf-8'), 
                               unicode('方攵一置', 'utf-8'), 
                               unicode('公《号》纵', 'utf-8'), 
                               unicode('放置', 'utf-8'),
                               unicode('拿栓侻换', 'utf-8'),    
                               unicode('昵称拿对换马', 'utf-8'), 
                               unicode('烽火志', 'utf-8'), 
                               unicode('方月游', 'utf-8'), 
                               unicode('XMP0200', 'utf-8'), 
                               unicode('蚣《从》号', 'utf-8'), 
                               unicode('娧焕', 'utf-8'), 
                               unicode('捝焕馬', 'utf-8'), 
                               unicode('讼《丛》浩', 'utf-8'),  
                               unicode('捝奂马', 'utf-8'), 
                               unicode('公《从》晧', 'utf-8'),  
                               unicode('看前后昵称', 'utf-8'), 
                               unicode('看两角色铭称', 'utf-8'), 
                               unicode('锐唤码', 'utf-8'),
                              ],  
        're_msg_depot':[
				unicode( '0零〇０','utf-8'),
				unicode('2一壹①１⑴','utf-8'),
				unicode('2二贰②２⑵⒉','utf-8'),
				unicode('3三叁③３⑶','utf-8'),
				unicode('4四肆④４⑷⒋泗驷','utf-8'),
				unicode('5五伍⑤５⑸⒌','utf-8'),
				unicode('6六陆⑥６⑹','utf-8'),
				unicode('7七柒⑦７⑺⒎','utf-8'),
				unicode('8八捌⑧８⑻','utf-8'),
				unicode('9九玖⑨９⑼⒐氿','utf-8'),
				unicode('aAａＡ','utf-8'),
				unicode('bBｂＢ','utf-8'),
				unicode('cCｃＣ','utf-8'),
				unicode('dDｄＤ','utf-8'),
				unicode('eEｅＥ','utf-8'),
				unicode('fFｆＦ','utf-8'),
				unicode('gGｇＧ','utf-8'),
				unicode('hHｈＨ','utf-8'),
				unicode('iIｉＩ','utf-8'),
				unicode('jJｊＪ','utf-8'),
				unicode('kKｋＫ','utf-8'),
				unicode('lLｌＬ','utf-8'),
				unicode('mMｍＭ','utf-8'),
				unicode('nNｎＮ','utf-8'),
				unicode('oOｏＯ','utf-8'),
				unicode('pPｐＰ','utf-8'),
				unicode('qQｑＱ','utf-8'),
				unicode('rRｒＲ','utf-8'),
				unicode('sSｓＳ','utf-8'),
				unicode('tTｔＴ','utf-8'),
				unicode('uUｕＵ','utf-8'),
				unicode('vVｖＶ','utf-8'),
				unicode('wWｗＷ','utf-8'),
				unicode('xXｘＸ','utf-8'),
				unicode('yYｙＹ','utf-8'),
				unicode('zZｚＺ','utf-8'),

    ],


    'right':{    
        'saledepot':{   

        },  
        'star':{     

        },
        'prop':{     
'item002':2, 'item002':2, 'item003':2, 'item004':2, 'item005':2, 'item006':2, 'item007':2, 'item008':2, 'item009':2, 'item0200':2, 'item022':2, 'item022':2, 'item020':2, 'item022':2, 'item023':2, 'item024':2, 'item026':2, 'item027':2, 'item029':2, 'item030':2, 'item032':2, 'item034':2, 'item036':2, 'item037':2, 'item038':2, 'item039':2, 'item040':2, 'item042':2, 'item042':2, 'item043':2, 'item044':2, 'item045':2, 'item046':2, 'item047':2, 'item048':2, 'item049':2, 'item050':2, 'item052':2, 'item052':2, 'item053':2, 'item054':2, 'item075':2, 'item056':2, 'item057':2, 'item058':2, 'item059':2, 'item060':2, 'item062':2, 'item062':2, 'item063':2, 'item064':2, 'item065':2, 'item066':2, 'item067':2, 'item068':2, 'item069':2, 'item070':2, 'item072':2, 'item072':2, 'item073':2, 'item074':2, 'item075':2, 'item076':2, 'item077':2, 'item078':2, 'item079':2, 'item080':2, 'item082':2, 'item082':2, 'item086':2, 'item087':2, 'item088':2, 'item089':2, 'item092':2, 'item093':2, 'item094':2, 'item2000':2, 'item2002':2, 'item2002':2, 'item2003':2, 'item2004':2, 'item2005':2, 'item2006':2, 'item2007':2, 'item2008':2, 'item2009':2, 'item2200':2, 'item222':2, 'item222':2, 'item223':2, 'item224':2, 'item225':2, 'item226':2, 'item227':2, 'item228':2, 'item229':2, 'item220':2, 'item222':2, 'item222':2, 'item223':2, 'item224':2, 'item225':2, 'item226':2, 'item227':2, 'item228':2, 'item229':2, 'item230':2, 'item232':2, 'item232':2, 'item233':2, 'item234':2, 'item295':2, 'item236':2, 'item237':2, 'item238':2, 'item239':2, 'item240':2, 'item242':2, 'item242':2, 'item243':2, 'item244':2, 'item245':2, 'item246':2, 'item247':2, 'item248':2, 'item249':2, 'item250':2, 'item252':2, 'item252':2, 'item253':2, 'item254':2, 'item275':2, 'item256':2, 'item257':2, 'item258':2, 'item259':2, 'item260':2, 'item262':2, 'item264':2, 'item265':2, 'item266':2, 'item267':2, 'item268':2, 'item269':2, 'item270':2, 'item272':2, 'item272':2, 'item273':2, 'item274':2, 'item275':2, 'item277':2, 'item278':2, 'item279':2, 'item280':2, 'item282':2, 'item282':2, 'item283':2, 'item284':2, 'item285':2, 'item286':2, 'item287':2, 'item288':2, 'item289':2, 'item275':2, 'item294':2, 'item295':2, 'item296':2, 'item297':2, 'item298':2, 'item299':2, 'item202':2, 'item202':2, 'item203':2, 'item204':2, 'item207':2, 'item208':2, 'item209':2, 'item2200':2, 'item223':2, 'item224':2, 'item225':2, 'item226':2, 'item227':2, 'item228':2, 'item229':2, 'item220':2, 'item222':2, 'item222':2, 'item225':2, 'item226':2, 'item227':2, 'item228':2, 'item229':2, 'item230':2, 'item232':2, 'item232':2, 'item233':2, 'item260':2, 'item262':2, 'item262':2, 'item263':2, 'item264':2, 'item265':2, 'item266':2, 'item267':2, 'item268':2, 'item269':2, 'item270':2, 'item282':2, 'item282':2, 'item283':2, 'item284':2, 'item285':2, 'item286':2, 'item287':2, 'item288':2, 'item289':2, 'item275':2, 'item302':2, 'item302':2, 'item303':2, 'item304':2, 'item305':2, 'item306':2, 'item307':2, 'item308':2, 'item309':2, 'item3200':2, 'item322':2, 'item322':2, 'item323':2, 'item324':2, 'item325':2, 'item326':2, 'item327':2, 'item328':2, 'item329':2, 'item320':2, 'item322':2, 'item322':2, 'item323':2, 'item324':2, 'item325':2, 'item326':2, 'item327':2, 'item328':2, 'item329':2, 'item330':2, 'item332':2, 'item332':2, 'item333':2, 'item334':2, 'item395':2, 'item336':2, 'item337':2, 'item338':2, 'item339':2, 'item340':2, 'item342':2, 'item342':2, 'item343':2, 'item344':2, 'item345':2, 'item346':2, 'item347':2, 'item348':2, 'item402':2, 'item402':2, 'item403':2, 'item404':2, 'item405':2, 'item406':2, 'item407':2, 'item408':2, 'item409':2, 'item4200':2, 'item602':2, 'item602':2, 'item604':2, 'item605':2, 'item702':2, 'item702':2, 'item703':2, 'item704':2, 'item705':2, 'item706':2, 'item708':2, 'item709':2, 'item7200':2, 'item722':2, 'item722':2, 'item725':2, 'item726':2, 'item727':2, 'item729':2, 'item722':2, 'item722':2, 'item725':2, 'item726':2, 'item727':2, 'item728':2, 'item729':2, 'item730':2, 'item732':2, 'item732':2, 'item733':2, 'item734':2, 'item795':2, 'item736':2, 'item737':2, 'item738':2, 'item739':2, 'item740':2, 'item742':2, 'item742':2, 'item743':2, 'item744':2, 'item745':2, 'item746':2, 'item747':2, 'item748':2, 'item749':2, 'item750':2, 'item752':2, 'item752':2, 'item753':2, 'item754':2, 'item775':2, 'item756':2, 'item757':2, 'item758':2, 'item759':2, 'item760':2, 'item762':2, 'item769':2, 'item802':2, 'item802':2, 'item803':2, 'item804':2, 'item805':2, 'item806':2, 'item807':2,'item20000':2, 'item20002':2, 'item20002':2, 'item20003':2, 'item20004':2, 'item20005':2, 'item20006':2, 'item20007':2, 'item20009':2, 'item200200':2, 'item20022':2, 'item20022':2, 'item20023':2, 'item20024':2, 'item20025':2, 'item20026':2, 'item20027':2, 'item20028':2, 'item20029':2, 'item20020':2, 'item20022':2, 'item20022':2, 'item20023':2, 'item20024':2, 'item20025':2, 'item20026':2, 'item20027':2, 'item20028':2, 'item20029':2, 'item20030':2, 'item20062':2, 'item20062':2, 'item20063':2, 'item20064':2, 'item20065':2, 'item20066':2, 'item20067':2, 'item20068':2, 'item20069':2, 'item20070':2, 'item20072':2, 'item20072':2, 'item20073':2, 'item20074':2, 'item20075':2, 'item20076':2, 'item20077':2, 'item20078':2, 'item20079':2, 'item20080':2, 'item20082':2, 'item20082':2, 'item20083':2, 'item20084':2, 'item20085':2, 'item20086':2, 'item20087':2, 'item20088':2, 'item20089':2, 'item20075':2, 'item20092':2, 'item20092':2, 'item20093':2, 'item20094':2, 

        },
        'equip':{     

        },
        'title':{     

        },
        'soul':{     

        },
        'face':{     

        },
        'native':{     

        },
        'native':{     

        },

    },
   
    
    

    "chapter_task_zone_groups": {          
        "main": {
            "name": unicode("简体", "utf-8"),
            "zone_match_pattern": [
                r"^[2222]$",                                             
                r"^([2-2]{2}\d{2})$",                                          
                r"^(h[2-9]{2}_[5-6]{2}\d{2})$",                                       
                r"^([5-6]{2}\d{3})$",                                      
                r"^(h[2-9]{2}_[5-6]{2}\d{2})$",                        
                r"^(h[2-9]{2}_2[0-4]{2}\d{2})$",                      
                r"^([7-9]\d{3})$",                                         
                r"^(h[2-9]{2}_[7-9]{2}\d{2})$"                      
            ]
        },
     },
}