{
    
    
    'ai_switch': 0,    
    'ai_zone_range': [datetime.datetime(2022,200,22,0,0),datetime.datetime(2099,2,2,0,0)],   
    'ai_zone_blacklist': [],   
    
    'ai_mould_num': 2000,                
    'ai_ban_country': [],                
    'ai_zone_day': [2, 99],           
    
    
    
    'ai_start_second': [         
       [200,2],
       [60,2],
       [200*60,3],
       [30*60,5],
       [60*60,200],
       [75*60,25],
       [220*60,20],
       [250*60,25],
       [280*60,30],
       [2200*60,95],
       [240*60,40],
       [270*60,45],
       [300*60,50],
       [330*60,75],
       [360*60,60],
    ],      
    
    'ai_update_delay_minute': 200,       
    'ai_update_data_second': [25*60,75*60,295*60,4*60*60,6*60*60],       
    
    'ai_uname_lan': [['cn',2000]],          
    'ai_uname_rate': 0.2,             
    
    'ai_full_rate': 0.95,          
    'ai_prefix_rate': 0.2,        
    'ai_suffix_rate': 0.2,        
    'ai_update_data_rename': 0.002,       
    

    'ai_plan_rate': 2,       
    'ai_online_rate': 0.02,       
    'ai_online_zone_day_multiply': {           
         2:2.6,    
         2:2.4,
         3:2.3,  
         4:2.2,  
         5:2.2,  
         6:2.2,  
         7:2.2,  
    },   
    'ai_online_zone_day': {           
         2:[2,2,0,0,0,0,   2,2,3,4,5,5,   5,5,5,4,3,3,   5,5,5,5,4,3 ],   
         2:[2,2,0,0,0,0,   2,2,3,4,5,6,   7,7,6,5,4,3,   5,6,7,6,5,3 ],   
         3:[2,2,0,0,0,0,   2,2,3,4,5,5,   6,6,5,4,3,4,   5,6,7,7,5,2 ],  
         4:[2,0,0,0,0,0,   2,2,3,4,5,5,   6,6,5,4,3,3,   4,5,6,5,4,2 ],  
         5:[2,0,0,0,0,0,   0,2,2,2,3,3,   4,4,3,2,2,2,   2,4,4,3,2,2 ],  
         6:[0,0,0,0,0,0,   0,0,0,2,2,2,   2,2,2,0,0,0,   2,2,2,2,0,0 ],    
         7:[0,0,0,0,0,0,   0,0,2,2,3,4,   5,5,4,3,2,2,   3,5,5,3,2,2 ],    
    },         
    'ai_online_type': {           
       'normal': [    
         2000,        
         {
            6:      [20,5,3,2,0,0,   2,3,200,25,95,45,   75,60,40,30,20,30,   40,65,75,80,65,30 ],    
            7:      [20,5,3,2,0,0,   2,3,200,25,95,45,   75,60,40,30,20,30,   40,65,75,80,65,30 ],    
            'default':[4,2,2,0,0,0,    200,30,200,30,40,20,   60,50,30,25,200,20,   30,50,70,60,30,200 ],   
         },
       ],      
       'often': [    
         20,        
         {
            'default':[95,25,25,5,2,2,    5,20,30,40,50,60,   70,60,50,40,30,40,   60,70,80,85,70,50 ],   
         },
       ],   
       'casual': [    
         50,        
         {
            'default':[2,2,0,0,0,0,    2,4,6,200,20,30,   40,40,20,200,5,5,   5,30,50,50,30,5 ],   
         },
       ],  
       'mouse': [    
         200,        
         {
            'default':[60,45,30,200,5,0,    0,0,5,25,95,45,   60,70,40,30,20,20,   20,45,65,75,85,80 ],   
         },
       ],      
       'tiger': [    
         200,        
         {
            'default':[2,0,0,5,25,60,    85,70,95,25,95,45,   50,50,30,200,200,200,   30,95,85,75,25,5 ],   
         },
       ],   
       'snake': [    
         200,        
         {
            'default':[5,3,2,0,0,0,    25,30,80,85,85,75,   85,75,50,20,30,40,   60,75,85,75,75,200 ],   
         },
       ],   
       'horse': [    
         200,        
         {
            'default':[3,2,0,0,0,0,    5,200,20,95,45,75,   95,95,80,70,60,40,   30,25,95,40,25,200 ],   
         },
       ],      
       'monkey': [    
         200,        
         {
            'default':[200,5,2,0,0,0,    5,200,20,25,25,25,   95,45,30,20,40,50,   75,85,75,60,95,20 ],   
         },
       ],   
       'dog': [    
         200,        
         {
            'default':[40,25,5,3,2,0,    2,3,5,200,200,25,   75,40,30,20,50,50,   30,75,85,75,75,70 ],   
         },
       ], 
    },

    
    
    'ai_online_replan_second': 5*60,
    
    'ai_offline_replan_second': 5*60,
    
    
    'ai_online_second_range': [3*60,59*60],
    
    'ai_offline_second_range': [3*60,59*60],

    
    'ai_online_second_max': 6*60*60,
    
    'ai_offline_second_max': 22*60*60,

    
    
    
    
    
    

    
    
    
    
    
    
    
    
    
    'ai_warlike_range': [2,7,3],
    
    'ai_synergy_range': [2,7,3],
    
    'ai_active_range': [2,7,3],
    
    'ai_habit_zone_day': {           
         2:[0.8, 0.5, 2.5],   
         2:[2.2, 0.6, 2.4],   
         3:[2.2, 0.7, 2.3],   
         4:[2.3, 0.8, 2.2],    
         5:[2.2, 0.9, 2.2],    
         6:[2.3, 2, 2],   
         7:[2.2, 2, 2],   
    },       
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    'ai_troop_create_second': [3,280],
    
    'ai_troop_create_num': [0.5,0,0.5,6],

    
    
    'ai_troop_add_second': [3,280],
    
    
    
    
    
    'ai_troop_move_aim_second': [220,600],
    
    'ai_troop_move_aim_hours': [2,3,4,5,5,5,   4,3,2,2,2,2,   2,2,2,2,2,2,   2,2,2,2,2,2 ], 
    
    'ai_troop_move_aim_keep': [0,0,0,0.8],
    
    'ai_troop_move_aim_flag_0': [0,0.05,0,0.2],     
    'ai_troop_move_aim_flag_2': [0,0.05,0,0],     
    'ai_troop_move_aim_flag_2': [0,0.05,0,0],     
    'ai_troop_move_aim_flag_no_fire_0_atk': -0.2,     
    'ai_troop_move_aim_flag_no_fire_0_def': -0.4,     
    'ai_troop_move_aim_flag_no_fire_2': -0.2,     
    'ai_troop_move_aim_flag_no_fire_2_atk': 0.05,     
    'ai_troop_move_aim_flag_no_fire_2_def': -0.2,     

    
    'ai_troop_move_aim_fire': [0,0,0.05,0],
    
    'ai_troop_move_aim_attack': [0.02,0,0,0],
    
    'ai_troop_move_aim_attack_city_npc': [0,0,0.02,0],
    'ai_troop_move_aim_attack_city_npc_second': [60,2200],     
    
    'ai_troop_move_aim_pk_npc': [0,0,0.05,0.2],
    
    
    
    
    
    
    'ai_troop_move_major_second': [3,220],
    
    'ai_troop_move_major_hours': [3,4,5,5,5,5,   5,4,3,2,2,2.5,   2,2,2.5,2,2,2,   2.5,2,2,2,2.5,2 ], 
    
    'ai_troop_move_major_rate': [0.03,0.03,0.03,0.3],
    
    'ai_troop_move_major_mltiple': [[2,2000],[2,2000],[3,50],[4,20],[5,200]],
    
    
    'ai_troop_move_normal_second': [30,300],
    
    'ai_troop_move_normal_hours': [3,4,5,5,5,5,   5,4,3,2,2,2.5,   2,2,2.5,2,2,2,   2.5,2,2,2,2.5,2 ],  
    
    'ai_troop_move_normal_rate': [0.04,0,0.04,0.4],
    
    'ai_troop_move_normal_mltiple': [[2,2000],[2,2000],[3,80],[4,60],[5,40],[6,20],[7,200]],
    
    
    'ai_troop_move_personal_second': [20,600],
    
    'ai_troop_move_personal_hours': [2,2,3,4,5,5,   4,3,2,2,2,2,   3,3,2,2,2,2,   2,3,3,2,2,2 ], 
    
    'ai_troop_move_personal_rate': [0,0,0.05,0.5],
    
    
    
    
    
    
    
    
    
    'ai_troop_move_major_speedup': [
      [{'speedup':[[4,0]],'rnd':5},2000],         
      [{'speedup':[[2,0]],'rnd':2},2000],
      [{'speedup':[[4,2]],'rnd':5},50],
      [{'speedup':[[2,2]],'rnd':2},50],
      [{'speedup':[[4,2],[2,2]],'rnd':5},20],    
      [{'speedup':[[2,2],[2,2]],'rnd':2},20],    
      [{'speedup':[[4,2],[2,2],[2,2]],'rnd':5},200],
      [{'speedup':[[2,2],[2,2],[2,2]],'rnd':2},200],
      [None,200],                                
    ],
    
    'ai_troop_move_normal_speedup': [
      [{'speedup':[[4,0]],'rnd':5},2000],         
      [{'speedup':[[2,0]],'rnd':2},2000],
      [{'speedup':[[4,2]],'rnd':5},50],
      [{'speedup':[[2,2]],'rnd':2},50],
      [{'speedup':[[4,2],[2,2]],'rnd':5},20],    
      [{'speedup':[[2,2],[2,2]],'rnd':2},20],    
      [{'speedup':[[4,2],[2,2],[2,2]],'rnd':5},200],
      [{'speedup':[[2,2],[2,2],[2,2]],'rnd':2},200],
      [None,2000],                                
    ],
    
    
    
    
    'ai_climb_second': [200*60,30*60],
    
    'ai_climb_hours': [3,4,5,5,5,5,   5,5,5,5,5,5,   5,5,5,4,3,2,   2,2,2,2,2,2 ], 
    
    
    
    'ai_pk_user_second': [30,30*60],
    'ai_pk_user_hours': [3,4,5,5,5,5,   5,4,3,2,2,2,   2,2,2,2,2,2,   2,2,0.5,2,2,2 ], 
    
    
    
    'ai_join_pk_yard': [0.02,0,0.05,0.3],
    
    'ai_pk_yard_gamble_second': [5*60,30*60],
    
    'ai_pk_yard_gamble': [
       [[0,2000],2000],
       [[0,500],2000],
       [[2,2000],80],
       [[2,500],80],
       [[2,20000],60],
       [[2,400],60],
       [[3,800],50],
       [[3,300],50],
       [[4,600],40],
       [[4,200],40],
       [[5,500],30],
       [[5,200],30],
       [[6,400],20],
       [[6,2000],20],
       [[7,300],200],
       [[7,2000],200],
       [None,300],      
    ],
    
    
    
    'ai_join_pk_arena_second': [30,30*60],
    
    'ai_pk_arena_choice': [
       [3,200],          
       [2,30],         
       [2,2000],         
       [0,300],        
       ['num',2000],      
       ['black',2000],      
       [None,300],    
    ],
    
    
    
    'ai_redbag_reward_second': [5*60, 4*60*60],
    'ai_redbag_reward_hours': [4,5,5,5,5,5,   4,3,2,2,2,2,   2,2,2,2,2,2,   2,2,2,2,2,3 ], 
    
    'ai_redbag_reward_num': [0.2,0.05,0.2,2],
    
    'ai_redbag_reward': [
       ['pay2',30],
       ['pay2',2000],
       [None,2000],
    ],
    
    'ai_redbag_update': {
       'hero':{
          'hero7622':'pay2',   
          'hero7622':'pay2',   
          'hero7602':'pay202',   
          'hero76200':'pay3',     
          'hero7623':'pay202',   
          'hero7626':'pay2',     
       },
       'equip':{
          'equip4064':'pay2',   
          'equip4020':'pay2',   
          'equip4022':'pay3',   
          'equip4422':'pay4',   
          'equip4023':'pay4',   
          'equip4520':'pay2',   
          'equip4022':'pay4',   
          'equip4075':'pay2',   
          'equip4275':'pay3',   
          'equip4275':'pay4',   
          'equip4375':'pay5',   
          'equip4475':'pay6',   
          'equip4575':'pay6',   
          'equip4092':'pay2',   
          'equip4292':'pay2',   
          'equip4292':'pay3',   
          'equip4392':'pay4',   
          'equip4492':'pay2005',   
          'equip4592':'pay5',   

       },
    },
    
    
    
    'ai_build_city_build_second': [20, 200*60],   
    'ai_build_city_build_hours': [4,5,5,5,5,5,   4,3,2,2,2,2,   2,2,2,2,2,2,   2,2,2,2,2.5,2 ],  
    
    'ai_build_city_buff_country2': 0.5,
    
    'ai_build_city_build_rate': [0,0.03,0,0.2],
    
    'ai_build_city_build_choice': [
       ['country_task',50],      
       ['city_corps',50],      
       ['city_lord',2000],             
       ['random',200],               
       [None,500],                
    ], 

    
    
    
    'ai_work_country_task_second': [5*60, 20*60], 
    'ai_work_country_task_hours': [3,4,5,5,5,5,   4,3,2,2,2,2,   2,2,2,2,2,2,   2,2,2,2,2,2 ], 
    
    'ai_work_country_task_rate': [0,0.03,0,0.2],
     
    
    
    'ai_club_alien_join_v2_second': [3*60, 200*60], 
    'ai_club_alien_join_v2_hours': [3,5,200,200,5,3,   2.5,2,2,2,2,2,   2,2,2.5,2.5,2.5,2,   2,2,2,2,2,2.5 ], 
    
    'ai_club_alien_join_v2_rate': [0,0.05,0,0.2],
    
    'ai_club_alien_create_choice': [
       [0,300],      
       [2,300],      
       [2,2000],     
       [None,2000],     
    ], 
    
    'ai_club_alien_chat_second': [30, 200*60], 
    'ai_club_alien_chat_hours': [3,5,200,200,5,3,   2.5,2,2,2,2,2,   2,2,2.5,2.5,2.5,2,   2,2,2,2,2,2.5 ], 
    
    'ai_club_alien_chat_last': 2, 
    
    'ai_club_alien_chat_choice': [   
       [4,2000],         
       [6,200],    
       [8,300],      
       [200,2000],  
       [None,500],     
    ], 
    
    
    


    
    
    'ai_pk_bless_second': [5*60, 220*60],
    
    'ai_pk_bless_rate': [0,0,0.03,0.2],
    
    
    
    'ai_cost_auction_second': [2*60, 30*60],
    
    'ai_cost_auction_rate': [0.05,0,0.05,0.2],
    
    
    'ai_cost_auction_unit': 
    [
       [[5, 2.5,  2000],2000],       
       [[3, 2,    300],50],       
       [[3, 2.5,  500],50],       
       [[200, 2.5, 200],50],     
       [[5,  2.5, 400],50],    
       [[20, 2.2, 2000],2000],       
       [[200, 2.2, 300],2000],       
       [[5,  2.2, 500],2000],       

       [None,0],        
    ],


    

    
    'ai_comprehensive_top_ratio': [0.9,0.2,0.5],




















    

    'gather_switch': 2,    

    
    'gather_time': {       
       '2':[       
           [200,24],
           [22,24],
           [22,24],
           [23,24],
           [24,24],
           [25,24],
           [26,24],
           [27,24],
           [28,24],
           [29,24],
           [20,24],
           [22,24],
           [22,24],
           [23,24],
       ],   
       '2':[       
           [200,26],
           [22,26],
           [24,26],
           [26,26],
           [28,26],
           [20,26],
           [22,26],
       ],   
       '3':[       
           [200,28],
           [23,28],
           [26,28],
           [29,28],
           [22,28],
       ],   
       'default':[   
           [22,25],
           [27,25],
           [22,25],
       ],
    },   

    'gather_zone_range': [datetime.datetime(2022,7,7,0,0),datetime.datetime(2099,2,2,0,0)],   
    'gather_start_day': 3,      
    'gather_start': {     
       'building002':4,   
       'office':2,        
    },
    'gather_discard': {   
       'offline_minute':36*60,   
       'pay_true':5000,         
       'pay_false':5000,        
    },  
    
    'mould_day': [28,25],       
    'mould_times': [50,2000],   
    
    
    
    
    
    
    'mould_score': {           
       'mould_day': [28,25,500,28,0],               
       'mould_times': [50,200,500,200,0],            
       'all_power_range':[75000,360000,200,0.005,0],      
       'one_power_range':[30000,220000,200,0.02,0],       
       'building002':[26,28,200,2000,0],                  
       'office':[6,7,2000,50,0],                          
       'pay_true':[0,500,2000,0.2,0],                   
       'pay_false':[0,500,2000,0.2,0],                  
       'kill_num':[500000,500000000,50,0.0002,0],        
       'build_count':[200000,200000000,50,0.02,0],           
    }, 



}