{
	
	'switch':1,	#总开关1开启 0关闭	
	'sign_threshold':8 ,#报名等级8级以上
	'time':[			#
		[0,1], 		        #报名开始时间，报名结束时间，季节
		[2,[11,05]],		#循环赛开始时间，[季节，[时间]]
		[3,[11,26]],		#10强赛开始时间，[季节，[时间]]
                
	],
             
	'timenext':1*60, #每一战时间间隔，10分钟
        'circle':10,#循环赛轮次数
        'circle_number':100,#循环赛排行榜显示人数
  

	'percent':[0.1,256], #报名人数的10%的机器人，报名人数下限
	'robotrange':'base',#机器人库	
	
	'hero_number1':[[1,3],[2,4],[3,5],[4,6],[5,7],[15,8]],#届数，人数，达到对应届数人数增加,未合服
        'hero_numberx':[[1,8],[2,8],[3,8],[4,8],[5,8],[6,8]],#合服过
	

	'msg_stake':['biwudahui_msg001','biwudahui_msg002'], #押注邮件的标题和内容
	'msg_every':['biwudahui_msg003','biwudahui_msg004'], #循环赛发奖邮件的标题和内容
	'msg_reward':['biwudahui_msg005','biwudahui_msg006'], #整个比赛发奖邮件的标题和内容
	'msg_respect':['biwudahui_msg007','biwudahui_msg008'],#被膜拜奖励的标题和内容
        'pk_info':'pk_info01',

	
        'stake_max':['item078',2000,10000], #押注道具ID，单次最高押注，总押注最大值
	'floating_rate':[[1000,0.01],[2000,0.02],[4000,0.03],[6000,0.04],[8000,0.05],[10000,0.06],[15000,0.08],[20000,0.1],[25000,0.12],[30000,0.14],[35000,0.16],[40000,0.18],[45000,0.2],[50000,0.25],],
 #【差值单位，浮动数值】
	'river_rate':0.05,#抽水赔率，计算赔率用的参数
	'increase_rate':0.3,#未知场次增加的浮动赔率，未知场次在对应赔率上的增量
				#'rate': [(1-0.05)*(A+B)]/A，rate赔率，A、B：玩家战力[(1-0.05)*(A+B)]/A
				#'average'：A*（A/（A+B））+B*（B/（A+B）） average平均战力
        'rate_threshold':10,   #赔率最大值
        'rate_min':1.1,        #赔率最小值
				
        'pk_introduce':'biwudahui_text2',     #比赛页面“？”处提示
        'stake_introduce':'biwudahui_text1',  #押注页面“？”处提示
        

        

				
	'reward_join':{'item078':1000}, #报名参赛的奖励
	'reward_respect1':{'item078':1000},#膜拜他人的奖励
	'reward_respect2':['item078', 100, 5000],#被他人膜拜的奖励道具，单次膜拜奖励，上限
	'reward_every':{'item078':1000}, #每场循环赛胜利的奖励
 
        'area':10,  #排名10名以前的玩家奖励为单独奖励
	
        'reward': [ 
        [1,['title001'],{'item078':30000,'gold':1000000,'wood':2000000,'iron':2000000},],
        [2,['title002'],{'item078':25000,'gold':880000,'wood':1880000,'iron':1880000},],
        [3,['title003'],{'item078':20000,'gold':660000,'wood':1660000,'iron':1660000},],
	[4,['title004'],{'item078':18000,'gold':500000,'wood':1000000,'iron':1000000},],
        [5,['title005'],{'item078':16000,'gold':400000,'wood':800000,'iron':800000},],
        [6,['title006'],{'item078':14000,'gold':300000,'wood':600000,'iron':600000},],
        [7,['title007'],{'item078':13000,'gold':250000,'wood':500000,'iron':500000},],
        [8,['title008'],{'item078':12000,'gold':200000,'wood':400000,'iron':400000},],
        [9,['title120'],{'item078':11000,'gold':150000,'wood':300000,'iron':300000},],
        [10,['title121'],{'item078':10000,'gold':100000,'wood':200000,'iron':200000},],#第10名-第1名   新增设天下第9和天下第10称号
        [16,['title009'],{'item078':9000,'gold':90000,'wood':180000,'iron':180000},],#11-16
	[32,['title010'],{'item078':8000,'gold':80000,'wood':160000,'iron':160000},],#17-32
        [64,['title011'],{'item078':7000,'gold':70000,'wood':140000,'iron':140000},],#33-64
        [128,['title012','title013','title014','title015'],{'item078':6000,'gold':60000,'wood':120000,'iron':120000},],#65-128
        [256,['title016','title017','title018','title019'],{'item078':5000,'gold':50000,'wood':100000,'iron':100000},],#129-256
        [1024,['title020','title021','title022','title023'],{'item078':4000,'gold':40000,'wood':80000,'iron':80000},], #257及以上
	
	

]

}