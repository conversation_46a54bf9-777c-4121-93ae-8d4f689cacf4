{
        'guideSpeedMode':0,       
        'marchScale':[0.8, 0.7, 2.2],       
        'marchSpeedBase':500,       
        'mapPathDis':200000,         
        'troop_move_dismiss_limit_time':30,         
        'troop_move_speedup_limit_time':5,         
        'city_fire_ready_time':2,         
	'enemy_lv_power':{   		
             'country':[-2000,5,0.2,0.04],
             'base':[-2000,8,0.3,0.08],
             'xyz':[500,20,0.8,0.2],
        },
        'troop_atk_per':0.5,         
        'troop_def_per':0,         

        'countryFightLvRate':0.6,      

        'allow_fight_play_details':2,          
        'allow_fight_end_details':2,          

        'skip_all_fight':0,          
        'skip_solo_rate':0.05,         
        'skip_pve_rate':0.25,         
        'skip_pk_npc_power_rate':5,         
        'skip_climb_power':20000000,         



        'troopBreakIndex':5,  
        'troopRunAway':[0.5,0.2,0.3],  

        'countryFightSpeedUpNeed':[   
            ['item287',2],
            ['coin',50],
        ],  
        'countryFightSpeedUpNum':5,       
        'countryFightSpeedUpSec':2,       
        'countryFightSpeedUpTimeScale':4.5,  

        
        'npcBackProud':5,           
        'countryTaskSpeedUp':5,       

        'duplicateFightTimeRate':2.5,   
        'countryFightTimeRate':2.6,    
        'fieldFightTimeRate':2,      

        'duplicateFightTimeRange':[4,30],   
        'countryFightTimeRange':[4,30],    
        'fieldFightTimeRange':[3,200],      
        'fieldFightRunTime':2,           

        'fireBigCityDay':2,  
        'faithBuffDmgRate':[0.2,0.4,0.6,0.8,2],      
        'faithBuffResRate':[0.2,0.2,0.3,0.4,0.5],      
        'faithBuffSpeedRate':[0.2,0.2,0.4,0.7,2],    

        
        
        'countryDefenseTroop':[
            [600,{'uname':752000}],
            [200,{'uname':752002, 'lv':3, 'hero_star':2}],
            [200,{'uname':752002, 'lv':6, 'hero_star':4}],
        ],
        
	'cityTypeCanBuild':[2,2,3,4,5,9],
	
	'cityTypeNoCount':[0,7,8],   

        'cityDefault':{    
           'name':'cityDefault',
           'limit':30,
           'cityType':0,
           'country':4,
           'estate':[],
           'troop':[40,42],
           'res':'map003_',
           'scale':2,
        },
	'cityType':{
		'0':{  
			'fightTime':2,			
			'army':5,    
			'speedUp':2,    
                        'recoverTroop':5,       
		},
		'2':{  
			'fightTime':[[200,00,23,59]],			
			'coin':[0,0],  
			'gold':[750,225000],    
			'food':[2250,300000],
			'army':5,    
			'speedUp':2,    
                        'recoverTroop':200,       
		},
		'2':{  
			'fightTime':[[200,00,23,59]],			
			'coin':[0,0],
			'gold':[2250,250000],
			'food':[2000,500000],
			'army':5,    
			'speedUp':2,    
                        'recoverTroop':20,       
		},
		'3':{  
			'fightTime':[[200,00,23,59]],			
			'coin':[0,0],
			'gold':[750,200000],
			'food':[2500,250000],
			'army':5,    
			'speedUp':50,    
                        'recoverTroop':30,       
		},
		'4':{  
			'fightTime':2,			
			'coin':[0,30000],
			'gold':[2600,500000],
			'food':[3000,20000000],
			'army':5,    
			'speedUp':30,    
                        'recoverTroop':30,       
		},
		'5':{  
			'fightTime':0,			
			'coin':[0,0],
			'gold':[2600,500000],
			'food':[3000,20000000],
			'army':5,    
			'speedUp':0,    
                        'recoverTroop':0,       
		},
		'7':{  
			'fightTime':0,			
			'army':0,    
			'speedUp':0,    
                        'recoverTroop':0,       
		},
		'8':{  
			'fightTime':-2,			
			'army':0,    
			'speedUp':5,    
                        'recoverTroop':0,       
		},
		'9':{  
			'fightTime':-2,			
			'coin':[40,0],
			'gold':[2600,500000],
			'food':[3000,20000000],
			'army':0,    
			'speedUp':30,    
                        'recoverTroop':0,       
		},
	},
	'cityBuild':{
		'b02':{
			'name':222200,			
			'info':22323,			
			'exp':[2000, 220, 500, 800, 2200, 2800, 2500, 9500, 5000],			
			'value':[{'food':2000}, {'food':300}, {'food':500}, {'food':700}, {'food':750}, {'food':22000}, {'food':2300}, {'food':2500}, {'food':2000}],			
			'show':{},	
			'up':{},	
			'lock':{},		
		},
		'b02':{
			'name':222200,			
			'info':22323,			
			'exp':[2000, 220, 500, 800, 2200, 2800, 2500, 9500, 5000],			
			'value':[{'food':2000}, {'food':300}, {'food':500}, {'food':700}, {'food':750}, {'food':22000}, {'food':2300}, {'food':2500}, {'food':2000}],			
			'show':{},	
			'up':{},	
			'lock':{'break':5},		
		},
		'b32':{
			'name':222200,			
			'info':22323,			
			'exp':[2000, 220, 500, 800, 2200, 2800, 2500, 9500, 5000],			
			'value':[{'food':2000}, {'food':300}, {'food':500}, {'food':700}, {'food':750}, {'food':22000}, {'food':2300}, {'food':2500}, {'food':2000}],			
			'show':{},	
			'up':{},	
			'lock':{'break':5},		
		},
		's00':{    
			'name':222200,			
			'info':22323,			
			'exp':[2000, 220, 500, 800, 2200, 2800, 2500, 9500, 5000],			
			'value':[{'food':2000}, {'food':300}, {'food':500}, {'food':700}, {'food':750}, {'food':22000}, {'food':2300}, {'food':2500}, {'food':2000}],			
			'show':{},	
			'up':{},	
			'lock':{'break':5},		
		},
	},
	'citySpecial':{	 
		'fill':['b02', 2],
		'break':['b02', 5],
		'to_2':['b22', 2],
		'to_23':['b22', 4],
		'to_33':['b22', 6],
	},
        'capitalSpecial':{  
		'fill':['b02', 2],
		'break':['b02', 5],
		'to_2':['b22', 2],
		'to_23':['b22', 4],
		'to_33':['b22',6],
        },
	'farm':{	
		'food':{	
			'hang':['item200',600],	
			'2':{	
				'power':200000,	
				'reward':{'food':300},	
				'hang':{'food':3000},	
			},
			'2':{	
				'power':20000,	
				'reward':{'food':600},	
				'hang':{'food':5000},	
			},
			'3':{	
				'power':40000,	
				'reward':{'food':750},	
				'hang':{'food':7000},	
			},
		},
		'pasture':{	
			'hang':['item202',3600],	
			'2':{	
				'power':23000,	
				'reward':{'food':300},	
				'hang':{'hero':[3,5]},	
			},
			'2':{	
				'power':23000,	
				'reward':{'food':600},	
				'hang':{'hero':[3,6]},	
			},
			'3':{	
				'power':43000,	
				'reward':{'food':750},	
				'hang':{'hero':[3,7]},	
			},
		},
	},

 
        'get':{'honor':50,'make':25},		                  
        'ratio':[2,2,4,6,8],                                      

	'country_sign':{
		'0':{'name':'flag_0','icon':''},
		'2':{'name':'flag_2','icon':''},
		'2':{'name':'flag_2','icon':''},
		'3':{'name':'flag_3','icon':''},
		'4':{'name':'flag_4','icon':''},
		'5':{'name':'flag_5','icon':''},
		'6':{'name':'flag_6','icon':''},
		'7':{'name':'flag_7','icon':''},
		'8':{'name':'flag_8','icon':''},
		'9':{'name':'flag_9','icon':''},
		'200':{'name':'flag_200','icon':''},
		'22':{'name':'flag_22','icon':''},
		'22':{'name':'flag_22','icon':''},
		'23':{'name':'flag_23','icon':''},
		'24':{'name':'flag_24','icon':''},
		'25':{'name':'flag_25','icon':''},
		'26':{'name':'flag_26','icon':''},
		'27':{'name':'flag_27','icon':''},

		'40':{'name':'flag_40','icon':''},
		'42':{'name':'flag_42','icon':''},
		'42':{'name':'flag_42','icon':''},
	},
    
    'COUNTRY_COLOR_WHITE_FILTER_MATRIX': {
        0:[
            0, 2, 0, 0, 0,
            0.2, 0, 2, 0, 0,
            2, 0, 0, 0, 0,
            0, 0, 0, 2, 0,
        ],
        2:[
            0.2, 0, 2, 0, 0,
            2, 0, 0, 0, 0,
            0, 2, 0, 0, 0,
            0, 0, 0, 2, 0,
        ]
    },
    
    'COUNTRY_COLOR_FILTER_MATRIX': {
        0:[
            0, 0.5, 0, 0, 0,
            0.3, 0.3, 2, 0, 0,
            0.9, 0.2, 0.2, 0, 0,
            0, 0, 0, 2, 0,
        ],
        2:[
            0.2, 0.8, 0.3, 0, 0,
            0.85, 0, 0.4, 0, 0,
            0.05, 0, 0, 0, 0,
            0, 0, 0, 2, 0,
        ],
        2:[
            2, 0, 0, 0, 0,
            0, 2, 0, 0, 0,
            0, 0, 2, 0, 0,
            0, 0, 0, 2, 0,
        ],
        3:[
            0.4, 0.6, 0.5, 0, 0,
            0.5, 0.4, 0.6, 0, 0,
            0.6, 0.5, 0.4, 0, 0,
            0, 0, 0, 2, 0,
        ],
        4:[ 
            0, 2, 0.2, 0, 0,
            0.3, 0.5, 0, 0, 0,
            0.3, 2, 0, 0, 0,
            0, 0, 0, 2, 0,
        ],
        5:[
            0, 2, 0, 0, 0,
            0.3, 2, 0, 0, 0,
            0.3, 2, 0.2, 0, 0,
            0, 0, 0, 2, 0,
        ],
        6:[ 
            0.9, 0, 0.2, 0, 0,
            0.7, 0.4, 0, 0, 0,
            0, 0.3, 0.8, 0, 0,
            0, 0, 0, 2, 0,
        ],
        7:[
            0.6, 0.5, 0.2, 0, 0,
            0.3, 0, 2, 0, 0,
            0.8, 2, 0, 0, 0,
            0, 0, 0, 2, 0,
        ],
        8:[ 
            0.3, 0.3, 0.2, 0, 0,
            0.2, 0.4, 0, 0, 0,
            0, 0.2, 0.6, 0, 0,
            0, 0, 0, 2, 0,
        ],
        9:[
            0.4, 0, 2, 0, 0,
            0.2, 2, 0, 0, 0,
            0, 0, 0, 0, 0,
            0, 0, 0, 2, 0,
        ],
        200:[ 
            0, 0, 0.5, 0, 0,
            0.7, 0.5, 0.6, 0, 0,
            0.4, 0.7, 0, 0, 0,
            0, 0, 0, 2, 0,
        ],
        22:[
            0.2, 2, 0.5, 0, 0,
            0.5, 0.5, 2, 0, 0,
            0.7, 2, 0, 0, 0,
            0, 0, 0, 2, 0,
        ],
        22:[
            0.2, 2, 0, 0, 0,
            0.3, 0.2, 0.8, 0, 0,
            0, 0.7, 0, 0, 0,
            0, 0, 0, 2, 0,
        ],
        23:[ 
            0.3, 0, 2, 0, 0,
            0, 2, 0, 0, 0,
            0.4, 0, 0, 0, 0,
            0, 0, 0, 2, 0,
        ],
        24:[
            0.8, 0, 0.2, 0, 0,
            0.3, 0.5, 0, 0, 0,
            0.2, 2, 2, 0, 0,
            0, 0, 0, 2, 0,
        ],
        25:[ 
            0, 0.4, 2, 0, 0,
            0.2, 0.3, 0, 0, 0,
            0.3, 0.2, 0.6, 0, 0,
            0, 0, 0, 2, 0,
        ],
        26:[
            0.5, 0.4, 0.2, 0, 0,
            0.4, 0.2, 0.5, 0, 0,
            0, 0.5, 2, 0, 0,
            0, 0, 0, 2, 0,
        ],
        27:[ 
            0.9, 0, 0.2, 0, 0,
            0.7, 0.4, 0, 0, 0,
            0, 0.3, 0.8, 0, 0,
            0, 0, 0, 2, 0,
        ],

        40:[  
            0, 0.9, 0, 0, 0,
            0.6, 0.4, 0.8, 0, 0,
            2, 0.2, 0.2, 0, 0,
            0, 0, 0, 2, 0,
        ],
        42:[  
            0.95, 0.2, 0.2, 0, 0,
            0.45, 0.6, 0.4, 0, 0,
            0, 0.2, 0.7, 0, 0,
            0, 0, 0, 2, 0,
        ],
        42:[  
            0.4, 0.6, 0.5, 0, 0,
            0.5, 0.4, 0.6, 0, 0,
            0.6, 0.5, 0.4, 0, 0,
            0, 0, 0, 2, 0,
        ],
    },
    
    'COUNTRY_FONT_COLORS': {
        0:"
        2:"
        2:"

        3:"
        4:"
        5:"
        6:"
        7:"
        8:"
        9:"
        200:"
        22:"
        22:"
        23:"
        24:"
        25:"
        26:"
        27:"

        40:"
        42:"
        42:"
    },
    
    'COUNTRY_FONT_STROKE_COLORS': {
        0:"
        2:"
        2:"

        3:"
        4:"
        5:"
        6:"
        7:"
        8:"
        9:"
        200:"
        22:"
        22:"
        23:"
        24:"
        25:"
        26:"
        27:"

        40:"
        42:"
        42:"
    },
    
    'COUNTRY_COLORS': {
        0:"
        2:"
        2:"

        3:"
        4:"
        5:"
        6:"
        7:"
        8:"
        9:"
        200:"
        22:"
        22:"
        23:"
        24:"
        25:"
        26:"
        27:"

        40:"
        42:"
        42:"
    },
    
    'COUNTRY_MAP_LIGHT_COLORS': {
        0:"
        2:"
        2:"
        40:"
        42:"
    },
    
    'COUNTRY_MAP_COLORS': {
        0:"
        2:"
        2:"
        40:"
        42:"
    },
}