# -*- coding: utf-8 -*-
import datetime
import sys, os, os.path

os.environ['ttws'] = 'true'
root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../src'))
sys.path.insert(0, root)
import settings
from django.core.management import setup_environ

setup_environ(settings)
import time
from game_lib.logics import game_config
import datetime
from game_lib.models.config import Configs, ConfigLog, TestConfigs, TestConfigLog
import re
from copy import deepcopy as copy
import json


def export_config():
    """
    导出配置
    :return:
    """
    for item in game_config.all_config_list:
        for tip in item:
            print ">>>", tip[0]
            config = Configs.get(tip[0])
            config_value = config.config_value
            f = open(settings.BASEROOT + '/scripts/configs/%s.txt' % tip[0], 'w+')
            f.write(config_value)
            f.close()


def import_config():
    """
    导入配置
    :return:
    """
    now = datetime.datetime.now()
    admin_user = 'auto_import'
    for item in game_config.all_config_list:
        for tip in item:
            config_name = tip[0]
            if config_name in game_config.ignore_export_config_list:
                continue
            try:
                print config_name
                f = open(settings.BASEROOT + '/scripts/configs/%s.txt' % config_name, 'r')
                config_value = f.read()
                config = Configs.get(config_name)
                config.config_value = config_value
                config.sub_time = now
                config.admin_user = admin_user
                config.save()

                config_log = ConfigLog()
                config_log.cname = config_name
                config_log.cval = config_value
                config_log.sub_time = now
                config_log.admin_user = admin_user
                config_log.save()
            except:
                continue

def import_test_config():
    """
    导入测试配置
    :return:
    """
    now = datetime.datetime.now()
    admin_user = 'auto_import'
    for item in game_config.all_config_list:
        for tip in item:
            config_name = tip[0]
            #if config_name in game_config.ignore_export_config_list:
            #    continue
            print config_name
            f = open(settings.BASEROOT + '/scripts/configs/%s.txt' % config_name, 'r')
            config_value = f.read()
            config = TestConfigs.get(config_name)
            config.config_value = config_value
            config.sub_time = now
            config.admin_user = admin_user
            config.save()

            config_log = TestConfigLog()
            config_log.cname = config_name
            config_log.cval = config_value
            config_log.sub_time = now
            config_log.admin_user = admin_user
            config_log.save()


if __name__ == '__main__':
    export_config()
    #import_config()
